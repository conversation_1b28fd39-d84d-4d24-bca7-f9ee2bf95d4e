/* Item Design System - Dark Mode Theme */

:root {
  /* Background Colors */
  --bg-primary: #0A0A0B;
  --bg-secondary: #141416;
  --bg-card: #1C1C1F;
  --bg-hover: #242427;
  
  /* Brand Colors */
  --color-purple: #8B5CF6;
  --color-purple-dark: #7C3AED;
  --color-purple-light: #A78BFA;
  --color-orange: #F97316;
  --color-orange-dark: #EA580C;
  --color-orange-light: #FB923C;
  
  /* Text Colors */
  --text-primary: #FFFFFF;
  --text-secondary: #A1A1AA;
  --text-tertiary: #71717A;
  --text-placeholder: #52525B;
  
  /* Border Colors */
  --border-primary: #27272A;
  --border-secondary: #3F3F46;
  --border-focus: var(--color-purple);
  
  /* Status Colors */
  --color-success: #10B981;
  --color-warning: #F59E0B;
  --color-error: #EF4444;
  --color-info: #3B82F6;
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.5);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.5);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.5);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.5);
  
  /* Solid Colors (No Gradients) */
  --color-solid-purple: var(--color-purple);
  --color-solid-orange: var(--color-orange);
  
  /* Typography Scale (based on item's system) */
  --text-xs: 0.75rem;    /* 12px */
  --text-sm: 0.875rem;   /* 14px */
  --text-base: 1rem;     /* 16px */
  --text-lg: 1.125rem;   /* 18px */
  --text-xl: 1.25rem;    /* 20px */
  --text-2xl: 1.5rem;    /* 24px */
  --text-3xl: 1.75rem;   /* 28px */
  --text-4xl: 2.25rem;   /* 36px */
  --text-5xl: 3rem;      /* 48px */
  
  /* Font Weights */
  --font-light: 300;
  --font-regular: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;
  --font-black: 900;
  
  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-full: 9999px;
  
  /* Transitions */
  --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-base: 200ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 300ms cubic-bezier(0.4, 0, 0.2, 1);
  
  /* Component Specific Variables */
  --item-bg-input: #141416;
  --item-bg-input-alpha-50: rgba(20, 20, 22, 0.5);
  --item-purple: #8B5CF6;
  --item-purple-dark: #7C3AED;
  --item-purple-light: #A78BFA;
  --item-purple-lighter: #C4B5FD;
  --item-orange: #F97316;
  --item-orange-dark: #EA580C;
  --item-orange-light: #FB923C;
  --item-gray-300: #D1D5DB;
  --item-gray-400: #9CA3AF;
  --item-gray-500: #6B7280;
  --item-gray-700: #374151;
  --item-gray-800: #1F2937;
  --item-bg-card: #1C1C1F;
}

/* Background Utility Classes */
.bg-item-bg-primary {
  background-color: var(--bg-primary);
}

.bg-item-bg-secondary {
  background-color: var(--bg-secondary);
}

.bg-item-bg-card {
  background-color: var(--bg-card);
}

.bg-item-bg-hover {
  background-color: var(--bg-hover);
}

.bg-item-gray-500 {
  background-color: var(--item-gray-500);
}

.bg-item-bg-secondary {
  background-color: var(--bg-secondary);
}

/* Text Color Utility Classes */
.text-item-purple {
  color: var(--item-purple);
}

.text-item-purple-dark {
  color: var(--item-purple-dark);
}

.text-item-purple-light {
  color: var(--item-purple-light);
}

.text-item-gray-300 {
  color: var(--item-gray-300);
}

.text-item-gray-400 {
  color: var(--item-gray-400);
}

.text-item-gray-500 {
  color: var(--item-gray-500);
}

.text-item-gray-700 {
  color: var(--item-gray-700);
}

.text-item-gray-300 {
  color: var(--item-gray-300);
}

.text-item-gray-500 {
  color: var(--item-gray-500);
}

/* Border Color Utility Classes */
.border-item-gray-800 {
  border-color: var(--item-gray-800);
}

.border-item-gray-700 {
  border-color: var(--item-gray-700);
}

.border-item-purple {
  border-color: var(--item-purple);
}

/* Hover Utility Classes */
.hover\:border-item-gray-700:hover {
  border-color: var(--item-gray-700);
}

.hover\:text-item-gray-300:hover {
  color: var(--item-gray-300);
}

.hover\:bg-item-purple:hover {
  background-color: var(--item-purple);
}

.hover\:bg-item-purple-dark:hover {
  background-color: var(--item-purple-dark);
}

/* Utility Classes */
.item-card {
  background: var(--bg-card);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  transition: all var(--transition-base);
}

.item-card:hover {
  background: var(--bg-hover);
  border-color: var(--border-secondary);
  box-shadow: var(--shadow-lg);
}

.item-button-primary {
  background: var(--color-solid-purple);
  color: var(--text-primary);
  font-weight: var(--font-medium);
  padding: 0.625rem 1.25rem;
  border-radius: var(--radius-md);
  transition: all var(--transition-base);
  border: none;
  box-shadow: var(--shadow-sm);
}

.item-button-primary:hover {
  background: var(--color-purple-dark);
  box-shadow: var(--shadow-md);
}

.item-button-secondary {
  background: transparent;
  color: var(--color-purple);
  font-weight: var(--font-medium);
  padding: 0.625rem 1.25rem;
  border-radius: var(--radius-md);
  border: 1px solid var(--color-purple);
  transition: all var(--transition-base);
}

.item-button-secondary:hover {
  background: var(--color-purple);
  color: var(--text-primary);
  transform: translateY(-1px);
}

.item-input {
  background: var(--bg-secondary);
  color: var(--text-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  padding: 0.75rem 1rem;
  transition: all var(--transition-base);
  width: 100%;
}

.item-input:focus {
  outline: none;
  border-color: var(--color-purple);
  box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
}

.item-input::placeholder {
  color: var(--text-placeholder);
}

/* Minimal Animation Classes */
@keyframes item-fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.item-animate-in {
  animation: item-fade-in 0.2s ease-out;
}

/* Scrollbar Styling */
.item-scrollbar::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.item-scrollbar::-webkit-scrollbar-track {
  background: var(--bg-secondary);
  border-radius: var(--radius-full);
}

.item-scrollbar::-webkit-scrollbar-thumb {
  background: var(--border-secondary);
  border-radius: var(--radius-full);
  transition: background var(--transition-base);
}

.item-scrollbar::-webkit-scrollbar-thumb:hover {
  background: var(--color-purple);
}

/* Glass Morphism Effect */
.item-glass {
  background: rgba(28, 28, 31, 0.7);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Subtle Focus Effects (Minimal Approach) */
.item-focus-purple {
  box-shadow: 0 0 0 2px rgba(139, 92, 246, 0.2);
}

.item-focus-orange {
  box-shadow: 0 0 0 2px rgba(249, 115, 22, 0.2);
}

/* Minimalist Component Styles */
.item-button-minimal {
  background: transparent;
  color: var(--color-purple);
  font-weight: var(--font-medium);
  padding: 0.5rem 1rem;
  border-radius: var(--radius-md);
  border: 1px solid transparent;
  transition: all var(--transition-base);
}

.item-button-minimal:hover {
  background: var(--color-purple);
  color: var(--text-primary);
}

.item-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
}

.item-badge-purple {
  background: var(--color-purple);
  color: var(--text-primary);
}

.item-badge-orange {
  background: var(--color-orange);
  color: var(--text-primary);
}

.item-badge-outline {
  background: transparent;
  border: 1px solid var(--color-purple);
  color: var(--color-purple);
}