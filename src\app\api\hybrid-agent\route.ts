import { NextRequest, NextResponse } from 'next/server';
import { streamText } from 'ai';
import { openai } from '@ai-sdk/openai';
import { getUserIdFromRequest } from '@/utils/authUtils';
import { withRequestContext } from '@/utils/apiWrapper';
import { clearRequestContext } from '@/utils/requestContext';

// 复用 route.ts 的所有工具导入
import { memoryTools } from '@/tools/memoryTools';
import { weatherTool } from '@/tools/weatherTool';
import { clockTool } from '@/tools/clockTool';
import { gisTool } from '@/tools/gisTool';
import { finishTaskTool } from '@/tools/commonTools';
import { kbTool } from '@/tools/kbTool';
import { jiraTools } from '@/tools/jira-tools';
import { iotTools } from '@/tools/iotTool';
import { getSpecificServersMcpTools, getSpecificServersMcpPrompts } from '@/tools/mcpTools';

// Import system prompt - 添加系统提示词导入
import { defaultSystemPrompt } from '@/prompts/systemPrompt';

// Create system prompt - 复用 chat/route.ts 的系统提示词创建逻辑
const createSystemPrompt = async () => {
  let systemPrompt = defaultSystemPrompt;

  try {
    // 只获取指定的MCP服务器：wms
    const targetServerIds = ['wms'];
    console.log(`[HybridAgent] Target MCP servers: ${targetServerIds.join(', ')}`);

    // Get prompts from specified MCP servers
    const specificServersPrompts = await getSpecificServersMcpPrompts(targetServerIds);

    if (specificServersPrompts) {
      console.log('[HybridAgent] Successfully got prompts from specified MCP servers');
      systemPrompt += `\n\n${specificServersPrompts}`;
    }
  } catch (error) {
    console.error('[HybridAgent] Error getting MCP prompts:', error);
  }

  return systemPrompt;
};

export const POST = withRequestContext(async (req: NextRequest, requestId: string) => {
  // 身份验证
  const userId = await getUserIdFromRequest(req);
  if (!userId) {
    return NextResponse.json(
      { error: 'User not authenticated' },
      { status: 401 }
    );
  }

  console.log(`[HybridAgent:${requestId}] Authenticated user ${userId} accessing hybrid agent API`);

  const body = await req.json();
  const { messages, tools: requestedTools, model } = body;

  console.log(`[HybridAgent:${requestId}] Received request with ${messages?.length || 0} messages`);

  // 获取系统提示 - 添加系统提示词获取
  const systemPrompt = await createSystemPrompt();

  // 获取所有可用工具 - 完全复用 route.ts 逻辑
  const allMcpTools = await getSpecificServersMcpTools(['wms']) || {};
  const isMemoryEnabled = process.env.MEMORY_CHAT_ENABLED === undefined || 
                          process.env.MEMORY_CHAT_ENABLED.toLowerCase() === 'true';

  const tools = {
    weatherTool,
    clockTool,
    gisTool,
    finishTaskTool,
    kbTool,
    // Jira tools
    getJiraCreateMeta: jiraTools.getJiraCreateMeta,
    createJiraIssue: jiraTools.createJiraIssue,
    getJiraProjects: jiraTools.getJiraProjects,
    searchJiraIssues: jiraTools.searchJiraIssues,
    getJiraIssueTransitions: jiraTools.getJiraIssueTransitions,
    doJiraIssueTransition: jiraTools.doJiraIssueTransition,
    // IoT tools
    robotDogPatrolTool: iotTools.robotDogPatrolTool,
    ...(isMemoryEnabled ? {
      searchMemoriesTool: memoryTools.searchMemoriesTool,
      saveMemoryTool: memoryTools.saveMemoryTool,
    } : {}),
    ...allMcpTools
  };

  // 处理工具调用ID，添加请求ID前缀 - 复用 chat/route.ts 的逻辑
  const { formatToolCallId } = await import('@/utils/requestContext');
  const prefixedTools = Object.entries(tools).reduce((acc, [name, tool]) => {
    // 为MCP工具和记忆工具添加请求ID前缀
    if (name !== 'weatherTool' && name !== 'clockTool' && name !== 'gisTool' &&
        name !== 'finishTaskTool' && name !== 'kbTool' &&
        name !== 'getJiraCreateMeta' && name !== 'createJiraIssue' && name !== 'getJiraProjects' && 
        name !== 'searchJiraIssues' && name !== 'getJiraIssueTransitions' && name !== 'doJiraIssueTransition' &&
        name !== 'robotDogPatrolTool') {
      // 创建一个包装器，为toolCallId添加请求ID前缀
      acc[name] = {
        ...tool,
        execute: async (args: any, options: { toolCallId: string; messages: any[]; abortSignal?: AbortSignal }) => {
          // 替换原始toolCallId为带前缀的版本
          const prefixedOptions = {
            ...options,
            toolCallId: formatToolCallId(requestId, options.toolCallId)
          };

          // 使用带前缀的toolCallId调用原始execute函数
          return (tool as any).execute(args, prefixedOptions);
        }
      };
    } else {
      // 标准工具保持不变
      acc[name] = tool;
    }
    return acc;
  }, {} as Record<string, any>);

  console.log(`[HybridAgent:${requestId}] Available tools: ${Object.keys(prefixedTools).length}`);

  // 将系统提示作为最后一条消息，确保在长对话中不会失效 - 复用 chat/route.ts 的消息处理逻辑
  const messagesWithSystemPrompt = [
    ...messages, 
    { 
      role: 'system', 
      content: systemPrompt 
    },
  ];

  const result = await streamText({
    model: openai(model || 'gpt-4.1-2025-04-14'),
    // 不使用 system 参数，改为在 messages 中作为最后一条消息
    messages: messagesWithSystemPrompt,
    temperature: 0.1,
    tools: prefixedTools,
    maxSteps: 30,
    onFinish: (result) => {
      console.log(`[HybridAgent:${requestId}] Token usage - Prompt: ${result.usage.promptTokens}, Completion: ${result.usage.completionTokens}`);
      clearRequestContext(requestId);
    },
    onError: (error) => {
      console.error(`[HybridAgent:${requestId}] Error in streamText:`, error);
      clearRequestContext(requestId);
    }
  });

  return result.toDataStreamResponse();
}); 