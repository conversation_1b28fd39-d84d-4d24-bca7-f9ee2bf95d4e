'use client';

import React, { useState } from 'react';
import { ChatHistory } from '@/utils/chatHistoryUtils';
import ConfirmDialog from './ConfirmDialog';
import { useAuth } from '../contexts/AuthContext';
import { useChat } from '../contexts/ChatContext';
import UserMenu from './UserMenu';
import '@/styles/item-design-system.css';

interface SidebarProps {
  onNewChat: () => void;
  onSelectChat: (id: string) => void;
  onDeleteChat: (id: string) => void;
  onEditChatTitle: (id: string, newTitle: string) => void;
}

export default function Sidebar({
  onNewChat,
  onSelectChat,
  onDeleteChat,
  onEditChatTitle
}: SidebarProps) {
  const { isAuthenticated, user } = useAuth();

  // 使用聊天历史索引数据
  // 从聊天上下文获取状态
  const { chatIndexes, selectedChatId, isLoading, loadingChatId, deleteAllChats } = useChat();

  // 格式化日期函数
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // 确认对话框状态
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [chatToDelete, setChatToDelete] = useState<string | null>(null);
  const [isDeleteAll, setIsDeleteAll] = useState(false);

  // 添加编辑标题的状态
  const [editingChatId, setEditingChatId] = useState<string | null>(null);
  const [editedTitle, setEditedTitle] = useState<string>('');
  
  // 添加侧边栏收缩状态
  const [isCollapsed, setIsCollapsed] = useState(false);

  // 处理删除按钮点击
  const handleDeleteClick = (e: React.MouseEvent, chatId: string) => {
    e.stopPropagation();
    e.preventDefault();
    
    setChatToDelete(chatId);
    setIsDeleteAll(false);
    setConfirmDialogOpen(true);
  };

  // 处理删除所有按钮点击
  const handleDeleteAllClick = () => {
    setChatToDelete(null);
    setIsDeleteAll(true);
    setConfirmDialogOpen(true);
  };

  // 确认删除
  const handleConfirmDelete = async () => {
    if (isDeleteAll) {
      // 批量删除所有聊天
      await deleteAllChats();
    } else if (chatToDelete) {
      // 删除单个聊天
      onDeleteChat(chatToDelete);
    }

    // 重置状态
    setChatToDelete(null);
    setIsDeleteAll(false);
    setConfirmDialogOpen(false);
  };

  // 取消删除
  const handleCancelDelete = () => {
    setChatToDelete(null);
    setIsDeleteAll(false);
    setConfirmDialogOpen(false);
  };

  // 处理编辑按钮点击
  const handleEditClick = (e: React.MouseEvent, chatId: string, currentTitle: string) => {
    e.stopPropagation();
    setEditingChatId(chatId);
    setEditedTitle(currentTitle);
  };

  // 处理保存标题
  const handleSaveTitle = (e: React.FormEvent, chatId: string) => {
    e.preventDefault();
    e.stopPropagation();
    if (editedTitle.trim() !== '') {
      onEditChatTitle(chatId, editedTitle.trim());
    }
    setEditingChatId(null);
  };

  // 取消编辑
  const handleCancelEdit = (e: React.MouseEvent) => {
    e.stopPropagation();
    setEditingChatId(null);
  };

  return (
    <div className={`relative ${isCollapsed ? 'w-16' : 'w-64'} border-r border-item-gray-800 flex flex-col bg-item-bg-primary h-screen sticky top-0 overflow-hidden transition-all duration-300 ease-in-out`}>
      {/* Header with logo and collapse button - item 设计风格 */}
      <div className="p-4 flex-shrink-0 h-16 flex items-center justify-between">
        {!isCollapsed ? (
          <div className="flex items-center space-x-3">
            <h1 className="text-xl font-bold text-item-purple tracking-tight">
              Atlas
            </h1>
            <div className="px-2 py-0.5 text-xs bg-item-purple/20 text-item-purple-light border border-item-purple/40 rounded-md font-medium shadow-sm">
              BETA
            </div>
          </div>
        ) : (
          <div className="w-8 h-8 bg-item-purple/20 rounded-lg flex items-center justify-center">
            <div className="w-3 h-3 bg-item-purple rounded-full"></div>
          </div>
        )}
        
        {/* 收缩/展开按钮 */}
        <button
          onClick={() => setIsCollapsed(!isCollapsed)}
          className="p-1.5 rounded-lg text-item-gray-400 hover:text-item-purple hover:bg-item-purple/10 transition-all duration-200"
          title={isCollapsed ? "展开侧边栏" : "收缩侧边栏"}
        >
          <svg 
            xmlns="http://www.w3.org/2000/svg" 
            width="16" 
            height="16" 
            viewBox="0 0 24 24" 
            fill="none" 
            stroke="currentColor" 
            strokeWidth="2" 
            strokeLinecap="round" 
            strokeLinejoin="round"
            className={`transition-transform duration-200 ${isCollapsed ? 'rotate-180' : ''}`}
          >
            <path d="M15 18l-6-6 6-6"/>
          </svg>
        </button>
      </div>

      {/* New chat button - item 设计风格 */}
      {isAuthenticated && (
        <div className="m-3">
          <button
            className={`item-button-primary w-full rounded-lg flex items-center ${isCollapsed ? 'justify-center' : 'justify-between'} hover:scale-105 transition-all duration-200`}
            style={{
              padding: isCollapsed ? '0.75rem' : '0.75rem 1rem',
              minHeight: '48px'
            }}
            onClick={onNewChat}
            title={isCollapsed ? "New Connection" : undefined}
          >
            {!isCollapsed && <span className="font-medium text-sm text-white">New Connection</span>}
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-white">
              <line x1="12" y1="5" x2="12" y2="19"></line>
              <line x1="5" y1="12" x2="19" y2="12"></line>
            </svg>
          </button>
        </div>
      )}

      {/* Chat history list - 仅在已登录时显示 */}
      {isAuthenticated ? (
        <div className="flex-1 overflow-y-auto min-h-0 flex flex-col">
          {/* 标题固定在顶部 - item 设计风格 */}
          <div className="sticky top-0 z-30 bg-item-bg-primary/95 backdrop-blur-md flex-shrink-0">
            <div className="p-3 flex justify-between items-center">
              {!isCollapsed && <h2 className="text-sm text-item-gray-400 font-medium">Active Connections</h2>}

              {/* Delete All button - item 设计风格 */}
              {chatIndexes.length > 0 && (
                <button
                  className={`p-1.5 rounded-lg text-item-gray-400 hover:text-red-400 hover:bg-red-900/20 transition-all duration-200 ${isCollapsed ? 'mx-auto' : ''}`}
                  onClick={handleDeleteAllClick}
                  disabled={isLoading}
                  aria-label="Delete all chats"
                  title={isCollapsed ? "Delete all chats" : undefined}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M3 6h18"></path>
                    <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
                  </svg>
                </button>
              )}
            </div>
          </div>

          {/* 聊天历史列表 - item 设计风格 */}
          <div className="p-3 pr-1 flex-1 overflow-y-auto overflow-x-hidden item-scrollbar">
            {isLoading ? (
              <div className="flex justify-center items-center h-20 text-item-gray-400 text-sm">
                <div className="flex items-center space-x-2">
                  <div className="w-4 h-4 bg-item-purple rounded-full animate-pulse"></div>
                  <span>Loading...</span>
                </div>
              </div>
            ) : chatIndexes.length === 0 ? (
              <div className="text-center text-item-gray-500 text-sm py-6">
                No active connections
              </div>
            ) : (
              <ul className="space-y-1 pr-[2px]">
                {chatIndexes.map(chat => {
                  // 检查当前聊天是否被选中
                  const isSelected = selectedChatId === chat.id;

                  // 检查当前聊天是否正在加载
                  const isLoading = loadingChatId === chat.id;

                  // 是否正在编辑此聊天
                  const isEditing = editingChatId === chat.id;

                  return (
                    <li
                      key={chat.id}
                      className={`relative group ${isCollapsed ? 'p-2' : 'p-3'} rounded-lg cursor-pointer transition-all duration-200 item-animate-in ${
                        isSelected
                          ? 'bg-item-bg-card border border-item-purple/30'
                          : 'hover:bg-item-bg-hover border border-transparent'
                      }`}
                      onClick={() => onSelectChat(chat.id)}
                      title={isCollapsed ? chat.title : undefined}
                    >
                      <div className={`flex items-center relative z-20 ${isCollapsed ? 'justify-center' : ''}`}>
                        {isCollapsed ? (
                          // 收缩状态：只显示一个小圆点
                          <div className={`w-3 h-3 rounded-full transition-all duration-200 ${isSelected ? 'bg-item-purple animate-pulse shadow-sm' : 'bg-item-gray-700'}`}></div>
                        ) : (
                          <>
                            <div className={`w-2 h-2 rounded-full mr-3 transition-all duration-200 ${isSelected ? 'bg-item-purple animate-pulse shadow-sm' : 'bg-item-gray-700'}`}></div>
                            <div className="flex-1 min-w-0">
                              {isEditing ? (
                                <form onSubmit={(e) => handleSaveTitle(e, chat.id)} onClick={e => e.stopPropagation()}>
                                  <div className="flex items-center">
                                    <input
                                      type="text"
                                      value={editedTitle}
                                      onChange={(e) => setEditedTitle(e.target.value)}
                                      className="item-input text-sm"
                                      autoFocus
                                    />
                                    <div className="flex ml-2 space-x-1">
                                      <button
                                        type="submit"
                                        className="p-1.5 text-green-400 hover:bg-green-900/20 rounded-lg transition-all duration-200"
                                      >
                                        <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                          <polyline points="20 6 9 17 4 12"></polyline>
                                        </svg>
                                      </button>
                                      <button
                                        type="button"
                                        onClick={handleCancelEdit}
                                        className="p-1.5 text-red-400 hover:bg-red-900/20 rounded-lg transition-all duration-200"
                                      >
                                        <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                          <line x1="18" y1="6" x2="6" y2="18"></line>
                                          <line x1="6" y1="6" x2="18" y2="18"></line>
                                        </svg>
                                      </button>
                                    </div>
                                  </div>
                                </form>
                              ) : (
                                <>
                                  <div className="flex items-center">
                                    <div className="truncate text-sm font-medium text-gray-300">
                                      {chat.title}
                                    </div>
                                    <div className="w-5 h-3 ml-2 flex-shrink-0 flex items-center justify-center">
                                      {isLoading && (
                                        <svg className="animate-spin h-3 w-3 text-item-purple" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                        </svg>
                                      )}
                                    </div>
                                  </div>
                                  <div className="text-xs text-item-gray-500 mt-1">{formatDate(chat.updatedAt)}</div>
                                </>
                              )}
                            </div>

                            {/* Action buttons - item 设计风格 - 收缩状态下隐藏 */}
                            {!isEditing && (
                              <div className="flex space-x-1">
                                {/* Edit button */}
                                <button
                                  className={`p-1.5 rounded-lg text-item-gray-400 hover:text-item-purple hover:bg-item-purple/10 transition-all duration-200 ${isSelected ? 'opacity-70' : 'opacity-0 group-hover:opacity-70'}`}
                                  onClick={(e) => handleEditClick(e, chat.id, chat.title)}
                                  aria-label="Edit chat title"
                                >
                                  <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                    <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                                    <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                                  </svg>
                                </button>

                                {/* Delete button */}
                                <button
                                  className={`p-1.5 rounded-lg text-item-gray-400 hover:text-red-400 hover:bg-red-900/20 transition-all duration-200 ${isSelected ? 'opacity-70' : 'opacity-0 group-hover:opacity-70'}`}
                                  onClick={(e) => handleDeleteClick(e, chat.id)}
                                  aria-label="Delete chat"
                                >
                                  <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                    <path d="M3 6h18"></path>
                                    <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
                                  </svg>
                                </button>
                              </div>
                            )}
                          </>
                        )}
                      </div>
                    </li>
                  );
                })}
              </ul>
            )}
          </div>
        </div>
      ) : (
        <div className={`flex-1 flex flex-col items-center justify-center ${isCollapsed ? 'p-2' : 'p-4'}`}>
          {!isCollapsed && (
            <>
              <div className="text-item-gray-400 text-center mb-4 font-medium">
                Please login to start chatting
              </div>
              <div className="text-sm text-item-gray-500 text-center">
                Login to access your chat history
              </div>
            </>
          )}
        </div>
      )}

      {/* 用户状态区域 - item 设计风格 */}
      {isAuthenticated && user && (
        <div className="mt-auto flex-shrink-0">
          <div className={`p-3 flex items-center ${isCollapsed ? 'justify-center' : 'justify-between'}`}>
            {isCollapsed ? (
              // 收缩状态：只显示用户头像
              <div className="w-10 h-10 rounded-full bg-item-purple flex items-center justify-center text-white font-semibold shadow-lg" title={user.firstName || user.userName || 'User'}>
                {user.firstName?.charAt(0).toUpperCase() || user.userName?.charAt(0).toUpperCase() || 'U'}
              </div>
            ) : (
              // 展开状态：显示完整用户信息
              <>
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 rounded-full bg-item-purple flex items-center justify-center text-white font-semibold shadow-lg">
                    {user.firstName?.charAt(0).toUpperCase() || user.userName?.charAt(0).toUpperCase() || 'U'}
                  </div>
                  <div className="overflow-hidden">
                    <div className="text-sm text-white font-medium truncate">
                      {user.firstName || user.userName || 'User'}
                    </div>
                    {user.email && (
                      <div className="text-xs text-item-gray-400 truncate">
                        {user.email}
                      </div>
                    )}
                  </div>
                </div>
                <UserMenu />
              </>
            )}
          </div>
        </div>
      )}

      {/* 确认删除对话框 */}
      <ConfirmDialog
        isOpen={confirmDialogOpen}
        title={isDeleteAll ? "DELETE ALL CONFIRMATION" : "DELETE CONFIRMATION"}
        message={isDeleteAll
          ? "Are you sure you want to delete ALL conversations? This action cannot be undone."
          : "Are you sure you want to delete this conversation?"}
        onConfirm={handleConfirmDelete}
        onCancel={handleCancelDelete}
        containerScoped={true}
      />
    </div>
  );
}