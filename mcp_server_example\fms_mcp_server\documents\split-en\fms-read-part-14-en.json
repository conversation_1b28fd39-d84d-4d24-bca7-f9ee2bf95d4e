{"openapi": "3.0.1", "info": {"title": "FMS", "description": "", "version": "1.0.0"}, "paths": {"/fms-platform-user/Home/GetToekn": {"get": {"summary": "/fms-platform-user/Home/GetToekn", "deprecated": false, "description": "", "tags": ["Home"], "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "string"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-user/Home/GetUserTest": {"post": {"summary": "AI: Obtain user test data", "deprecated": false, "description": "", "tags": ["Home"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserTestDto"}, "example": ""}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserOrderStatisticsListSuccessResponse"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-user/Home/Ding": {"get": {"summary": "AI: Simple echo endpoint that allows anonymous access", "deprecated": false, "description": "", "tags": ["Home"], "parameters": [{"name": "dong", "in": "query", "description": "Enter string", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "string"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-user/Home/EnableAutoAuth": {"get": {"summary": "Whether to enable the automatic authorization switch", "deprecated": false, "description": "", "tags": ["Home"], "parameters": [{"name": "isDenine", "in": "query", "description": "", "required": false, "schema": {"type": "integer", "format": "int32"}}, {"name": "access", "in": "query", "description": "", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BooleanBooleanValueTuple"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-user/Menu/List": {"get": {"summary": "AI: Get all menu lists", "deprecated": false, "description": "", "tags": ["<PERSON><PERSON>"], "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/MenuDto"}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-user/Menu/UserMenuList": {"get": {"summary": "AI: Get a menu list of user-specific modules", "deprecated": false, "description": "", "tags": ["<PERSON><PERSON>"], "parameters": [{"name": "moduleId", "in": "query", "description": "Module ID", "required": false, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/MenuDto"}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-user/users/mutex-strategy": {"get": {"summary": "List of mutually exclusive policies", "deprecated": false, "description": "", "tags": ["MutexStrategy"], "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/MutexStrategyDto"}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-user/users/mutex-strategy/getRoleRelation": {"get": {"summary": "Through the policy id, query the corresponding role", "deprecated": false, "description": "", "tags": ["MutexStrategy"], "parameters": [{"name": "mutexStrategyId", "in": "query", "description": "", "required": false, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/MutexStrategyRoleDto"}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-user/Role/RoleDetail": {"get": {"summary": "AI: Get character details", "deprecated": false, "description": "", "tags": ["Role"], "parameters": [{"name": "roleId", "in": "query", "description": "Role ID", "required": false, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RoleDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-user/roles/all": {"get": {"summary": "AI: Query Roles-all", "deprecated": false, "description": "", "tags": ["Role"], "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RoleSelectListItemDtoListResultDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/samples/test-mapper": {"get": {"summary": "/samples/test-mapper", "deprecated": false, "description": "", "tags": ["<PERSON><PERSON>"], "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TestDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/samples/test-new": {"get": {"summary": "/samples/test-new", "deprecated": false, "description": "", "tags": ["<PERSON><PERSON>"], "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-user/users/state": {"get": {"summary": "AI: Get status list", "deprecated": false, "description": "", "tags": ["State"], "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StateDto"}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-user/terminals/polygons": {"get": {"summary": "Press TerminalCode to query the Polygons drop-down box\r\nforeign", "deprecated": false, "description": "", "tags": ["Terminals"], "parameters": [{"name": "terminalCode", "in": "query", "description": "", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StringFmsSelectListItemListResultDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-user/users/my-profile": {"get": {"summary": "Get information about My Profile", "deprecated": false, "description": "", "tags": ["Users"], "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MyProfileDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-user/users/{id}": {"get": {"summary": "Query - Details", "deprecated": false, "description": "", "tags": ["Users"], "parameters": [{"name": "id", "in": "path", "description": "", "required": true, "example": 0, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-user/users/login-history": {"get": {"summary": "Get login history", "deprecated": false, "description": "", "tags": ["Users"], "parameters": [{"name": "UserId", "in": "query", "description": "", "required": false, "schema": {"type": "integer", "format": "int64"}}, {"name": "OperateSystem", "in": "query", "description": "", "required": false, "schema": {"type": "string"}}, {"name": "OperateType", "in": "query", "description": "", "required": false, "schema": {"type": "integer", "format": "int32"}}, {"name": "OperateTimeStart", "in": "query", "description": "", "required": false, "schema": {"type": "string", "format": "date-time"}}, {"name": "OperateTimeStartEnd", "in": "query", "description": "", "required": false, "schema": {"type": "string", "format": "date-time"}}, {"name": "Sorting", "in": "query", "description": "", "required": false, "schema": {"type": "string"}}, {"name": "PageNumber", "in": "query", "description": "", "required": false, "schema": {"maximum": **********, "minimum": 0, "type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "description": "", "required": false, "schema": {"maximum": **********, "minimum": 1, "type": "integer", "format": "int32"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserLoginDtoPagedResultDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-user/users/try-pull-employee-info/{account}": {"get": {"summary": "Try to pull information about the employee type user from another system", "deprecated": false, "description": "", "tags": ["Users"], "parameters": [{"name": "account", "in": "path", "description": "", "required": true, "example": "", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ThirdPartyEmployeeDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-user/users/try-pull-dirvers-info/{vendorCode}": {"get": {"summary": "Try to pull information about the employee type user from another system", "deprecated": false, "description": "", "tags": ["Users"], "parameters": [{"name": "vendorCode", "in": "path", "description": "", "required": true, "example": "", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/VendorEmployeeDto"}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-user/Users/<USER>": {"get": {"summary": "Push BNP based on UserCode query user data", "deprecated": false, "description": "", "tags": ["Users"], "parameters": [{"name": "UserCode", "in": "query", "description": "", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "boolean"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}}, "components": {"schemas": {"BooleanBooleanValueTuple": {"type": "object", "properties": {"item1": {"type": "boolean"}, "item2": {"type": "boolean"}}, "additionalProperties": false}, "CompanySelectListItemDto": {"type": "object", "properties": {"company_code": {"type": "string", "nullable": true}, "company_name": {"type": "string", "nullable": true}}, "additionalProperties": false}, "FavouriteDto": {"type": "object", "properties": {"speedlink": {"type": "string", "nullable": true}}, "additionalProperties": false}, "MenuDto": {"type": "object", "properties": {"menu_name": {"type": "string", "nullable": true}, "parent_menu_id": {"type": "integer", "format": "int64"}, "url": {"type": "string", "nullable": true}, "level": {"type": "integer", "format": "int32"}, "menu_type": {"type": "integer", "format": "int32"}, "system_id": {"type": "integer", "format": "int32"}, "module_id": {"type": "integer", "format": "int32"}, "icon": {"type": "string", "nullable": true}, "sequence": {"type": "integer", "format": "int32"}, "child_menus": {"type": "array", "items": {"$ref": "#/components/schemas/MenuDto"}, "nullable": true}, "id": {"type": "integer", "format": "int64"}}, "additionalProperties": false}, "MutexStrategyDto": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "role_ids": {"type": "array", "items": {"$ref": "#/components/schemas/MutexStrategyRoleDto"}, "nullable": true}, "id": {"type": "integer", "format": "int64"}}, "additionalProperties": false}, "MutexStrategyRoleDto": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "id": {"type": "integer", "format": "int64"}}, "additionalProperties": false}, "MyProfileDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "user_account": {"type": "string", "nullable": true}, "first_name": {"type": "string", "nullable": true}, "middle_name": {"type": "string", "nullable": true}, "last_name": {"type": "string", "nullable": true}, "user_type": {"type": "integer", "format": "int32"}, "phone": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "vrm_vendor_code": {"type": "string", "nullable": true}, "vrm_user_id": {"type": "integer", "format": "int32"}, "user_time_zone": {"type": "string", "nullable": true}, "last_login_time": {"type": "string", "format": "date-time"}, "vrm_vendor_name": {"type": "string", "nullable": true}, "avatar": {"type": "string", "nullable": true}, "favourite": {"type": "array", "items": {"$ref": "#/components/schemas/FavouriteDto"}, "nullable": true}, "terminal_codes": {"type": "array", "items": {"type": "string"}, "nullable": true}, "company_codes": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "RoleDto": {"type": "object", "properties": {"role_name": {"type": "string", "nullable": true}, "role_desc": {"type": "string", "nullable": true}, "role_code": {"type": "string", "nullable": true}, "is_active": {"type": "boolean"}, "update_time": {"type": "string", "format": "date-time", "nullable": true}, "update_by": {"type": "integer", "format": "int64", "nullable": true}, "created_time": {"type": "string", "format": "date-time", "nullable": true}, "created_by": {"type": "integer", "format": "int64", "nullable": true}, "menu_id_list": {"type": "array", "items": {"type": "integer", "format": "int64"}, "nullable": true}, "create_user": {"type": "string", "nullable": true}, "update_user": {"type": "string", "nullable": true}, "id": {"type": "integer", "format": "int64"}}, "additionalProperties": false}, "FMSOrderListItemDto": {"type": "object", "properties": {"order_number": {"type": "string", "nullable": true}, "total_price": {"type": "number", "format": "double"}, "user_id": {"type": "integer", "format": "int64"}}, "additionalProperties": false}, "RoleSelectListItemDto": {"type": "object", "properties": {"role_id": {"type": "integer", "format": "int64"}, "role_name": {"type": "string", "nullable": true}}, "additionalProperties": false}, "RoleSelectListItemDtoListResultDto": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/RoleSelectListItemDto"}, "nullable": true}}, "additionalProperties": false}, "StateDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "state_name": {"type": "string", "nullable": true}, "state_code": {"type": "string", "nullable": true}, "country": {"type": "string", "nullable": true}}, "additionalProperties": false}, "TerminalSelectListItemDto": {"type": "object", "properties": {"company_code": {"type": "string", "nullable": true}, "company_name": {"type": "string", "nullable": true}, "terminal_name": {"type": "string", "nullable": true}, "terminal_code": {"type": "string", "nullable": true}}, "additionalProperties": false}, "TestDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ThirdPartyEmployeeDto": {"type": "object", "properties": {"user_account": {"type": "string", "nullable": true}, "employee_code": {"type": "string", "nullable": true}, "first_name": {"type": "string", "nullable": true}, "last_name": {"type": "string", "nullable": true}, "middle_name": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "phone": {"type": "string", "nullable": true}, "user_type": {"$ref": "#/components/schemas/UserTypeEnum"}}, "additionalProperties": false}, "UserDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "user_type": {"$ref": "#/components/schemas/UserTypeEnum"}, "vrm_vendor_code": {"type": "string", "nullable": true}, "user_account": {"type": "string", "nullable": true}, "first_name": {"type": "string", "nullable": true}, "middle_name": {"type": "string", "nullable": true}, "last_name": {"type": "string", "nullable": true}, "phone": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "sso_employee_id": {"type": "integer", "format": "int64", "nullable": true}, "employee_code": {"type": "integer", "format": "int64", "readOnly": true, "nullable": true}, "user_time_zone": {"type": "string", "nullable": true}, "avatar": {"type": "string", "nullable": true}, "terminals": {"type": "array", "items": {"$ref": "#/components/schemas/TerminalSelectListItemDto"}, "nullable": true}, "companies": {"type": "array", "items": {"$ref": "#/components/schemas/CompanySelectListItemDto"}, "nullable": true}, "is_active": {"type": "boolean"}, "status": {"type": "integer", "format": "int32", "readOnly": true}, "roles": {"type": "array", "items": {"$ref": "#/components/schemas/RoleSelectListItemDto"}, "nullable": true}}, "additionalProperties": false}, "UserLoginDto": {"type": "object", "properties": {"user_id": {"type": "integer", "format": "int64"}, "operate_system": {"type": "string", "nullable": true}, "operate_type": {"type": "integer", "format": "int32"}, "operate_time": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "UserLoginDtoPagedResultDto": {"type": "object", "properties": {"total_count": {"type": "integer", "format": "int64"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/UserLoginDto"}, "nullable": true}}, "additionalProperties": false}, "UserOrderStatistics": {"type": "object", "properties": {"user_id": {"type": "integer", "format": "int32"}, "user_name": {"type": "string", "nullable": true}, "orders": {"type": "array", "items": {"$ref": "#/components/schemas/FMSOrderListItemDto"}, "nullable": true}}, "additionalProperties": false}, "UserOrderStatisticsListSuccessResponse": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/UserOrderStatistics"}, "nullable": true}, "is_success": {"type": "boolean", "readOnly": true}, "status": {"type": "string", "readOnly": true, "nullable": true}, "message": {"type": "string", "readOnly": true, "nullable": true}, "code": {"type": "integer", "format": "int32", "readOnly": true}}, "additionalProperties": false}, "UserTestDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UserTypeEnum": {"enum": [0, 1, 2, 3, 4, 5, 6, 7, 8], "type": "integer", "format": "int32"}, "VendorEmployeeDto": {"type": "object", "properties": {"vendor_code": {"type": "string", "nullable": true}, "vendor_name": {"type": "string", "nullable": true}, "first_name": {"type": "string", "nullable": true}, "middle_name": {"type": "string", "nullable": true}, "last_name": {"type": "string", "nullable": true}, "phone": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StringFmsSelectListItem": {"type": "object", "properties": {"text": {"type": "string", "nullable": true}, "value": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StringFmsSelectListItemListResultDto": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/StringFmsSelectListItem"}, "nullable": true}}, "additionalProperties": false}}}}