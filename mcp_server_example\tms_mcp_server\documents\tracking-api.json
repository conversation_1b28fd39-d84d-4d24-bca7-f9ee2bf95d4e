{"openapi": "3.0.0", "info": {"title": "订单轨迹位置查询 API", "description": "专门用于查询货物实时位置和运输轨迹的API，不提供订单详情信息", "version": "1.0.0"}, "servers": [{"url": "https://ship.unisco.com/apinew", "description": "轨迹服务API服务器"}], "security": [{"apiKey": []}], "paths": {"/apinew/cpv2/tracking/order/location": {"post": {"security": [{"apiKey": []}], "summary": "查询订单实时位置和轨迹", "description": "根据订单号获取货物运输过程中的实时位置和历史轨迹信息，不返回订单详情", "tags": ["轨迹位置服务"], "parameters": [{"name": "X-API-KEY", "in": "header", "description": "轨迹服务API访问密钥", "required": true, "schema": {"type": "string"}}, {"name": "company_id", "in": "query", "description": "公司ID", "required": true, "schema": {"type": "integer"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"pro": {"type": "string", "description": "订单号/运单号", "example": "12311112265"}}, "required": ["pro"]}}}}, "responses": {"200": {"description": "成功返回轨迹位置信息", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "example": 1, "description": "状态码，1 表示成功"}, "message": {"type": "string", "example": "success", "description": "状态描述"}, "data": {"type": "object", "properties": {"location": {"type": "array", "items": {"$ref": "#/components/schemas/LocationPoint"}}, "origin": {"$ref": "#/components/schemas/Point"}, "destination": {"$ref": "#/components/schemas/Point"}}}, "trace_request_id": {"type": "integer", "description": "追踪请求ID", "example": 4301785}}}}}}, "400": {"description": "请求参数错误"}, "401": {"description": "API Key 无效"}, "500": {"description": "服务器内部错误"}}}}}, "components": {"securitySchemes": {"apiKey": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "X-API-KEY", "in": "header", "description": "请在请求头中添加 X-API-KEY"}}, "schemas": {"LocationPoint": {"type": "object", "properties": {"lat": {"type": "string", "description": "纬度", "example": "25.1038743"}, "lng": {"type": "string", "description": "经度", "example": "117.0101035"}, "time": {"type": "string", "description": "时间", "example": "2024-11-22 09:49:16"}}, "required": ["lat", "lng", "time"]}, "Point": {"type": "object", "properties": {"lat": {"type": "number", "format": "double", "description": "纬度", "example": 33.9771}, "lng": {"type": "number", "format": "double", "description": "经度", "example": -118.241562}}, "required": ["lat", "lng"]}}}}