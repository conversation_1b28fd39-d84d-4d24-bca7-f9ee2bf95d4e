import React, { useState } from 'react';

export default function WeatherCardEmbed({ toolInvocation }: { toolInvocation: any }) {
  const { result } = toolInvocation;
  const [isExpanded, setIsExpanded] = useState(false);

  if (!result) return null;

  // 折叠视图
  if (!isExpanded) {
    return (
      <div
        className="bg-blue-900/40 rounded-lg border border-cyan-800/50 p-3 text-cyan-100 shadow-sm cursor-pointer hover:bg-blue-900/50 transition-all"
        onClick={() => setIsExpanded(true)}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div className="text-2xl mr-2">🌤️</div>
            <div>
              <div className="text-lg font-medium">{result.temperature}°C</div>
              <div className="text-xs text-cyan-300/80">{result.location || ''}</div>
            </div>
          </div>
          <div className="text-xs text-cyan-200/70">{result.condition || result.weather || 'sunny'}</div>
        </div>
      </div>
    );
  }

  // 展开视图
  return (
    <div className="bg-blue-900/40 rounded-lg border border-cyan-800/50 p-3 text-cyan-100 shadow-sm">
      <div className="flex items-center justify-between mb-2 cursor-pointer" onClick={() => setIsExpanded(false)}>
        <div className="flex items-center">
          <div className="text-2xl mr-2">🌤️</div>
          <div>
            <div className="text-lg font-medium">{result.temperature}°C</div>
            <div className="text-xs text-cyan-300/80">{result.location || ''}</div>
          </div>
        </div>
        <button className="text-cyan-300 hover:text-white">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-4 h-4">
            <path strokeLinecap="round" strokeLinejoin="round" d="M4.5 15.75l7.5-7.5 7.5 7.5" />
          </svg>
        </button>
      </div>

      <div className="bg-blue-900/30 rounded-lg p-3 grid grid-cols-2 gap-y-2 text-sm mt-2">
        <div>
          <span className="text-cyan-300">Condition:</span> {result.condition || result.weather || 'sunny'}
        </div>
        <div>
          <span className="text-cyan-300">Humidity:</span> {result.humidity ? `${result.humidity}%` : 'N/A'}
        </div>
        <div>
          <span className="text-cyan-300">Wind:</span> {result.windSpeed ? `${result.windSpeed} km/h` : 'N/A'}
        </div>
        <div>
          <span className="text-cyan-300">Updated:</span> {new Date().toLocaleTimeString()}
        </div>
      </div>
    </div>
  );
}