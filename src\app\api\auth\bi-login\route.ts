import { NextRequest, NextResponse } from 'next/server';
import axios from 'axios';

// BI登录接口配置
const BI_LOGIN_ENDPOINT = 'https://6qu0lucwab.execute-api.us-west-2.amazonaws.com/login/cognito/cube/IDtoken';

// BI登录信息接口
export interface BiLoginResponse {
  idToken?: string;
  accessToken?: string;
  refreshToken?: string;
  expiresIn?: number;
  tokenType?: string;
  [key: string]: any; // 允许其他字段
}

/**
 * BI系统登录API
 * POST /api/auth/bi-login
 */
export async function POST(request: NextRequest) {
  try {
    // 从环境变量读取用户名和密码
    const username = process.env.BI_USERNAME || '<EMAIL>';
    const password = process.env.BI_PASSWORD || 'ChatItem@123';

    console.log('BI登录信息:', {
      endpoint: BI_LOGIN_ENDPOINT,
      username: encodeURIComponent(username),
      password: encodeURIComponent(password)
    });

    // 调用BI登录接口
    const response = await axios.get(BI_LOGIN_ENDPOINT, {
      params: {
        username: username,
        password: password
      },
      timeout: 15000 // 15秒超时
    });

    // 兼容新老格式，优先解包data字段
    let result: any = response.data;
    if (result.code !== 1000) {
      throw new Error(`BI登录失败: ${result.message}`);
    }
    
    const data = result.data;
    console.log('BI登录成功:', data);
    
    // 字段兼容处理（大写转小写）
    const biLoginResponse: BiLoginResponse = {
      idToken: data.IdToken,
      accessToken: data.AccessToken,
      refreshToken: data.RefreshToken
    };

    return NextResponse.json({
      success: true,
      data: biLoginResponse
    });

  } catch (error) {
    console.error('BI登录失败:', error);
    
    if (axios.isAxiosError(error)) {
      console.error('BI登录错误详情:', {
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        message: error.message
      });
    }
    
    return NextResponse.json(
      {
        success: false,
        error: `BI登录失败: ${error instanceof Error ? error.message : String(error)}`
      },
      { status: 500 }
    );
  }
} 