import os
from dotenv import load_dotenv

# Try to load environment variables from .env file if it exists
load_dotenv()

# Browser-Use Configuration
LLM_MODEL = os.getenv("LLM_MODEL", "gpt-4.1-2025-04-14")  # 使用GPT-4.1模型
ANTHROPIC_API_KEY = os.getenv("ANTHROPIC_API_KEY", "")
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY", "")
BROWSER_HEADLESS = os.getenv("BROWSER_HEADLESS", "true").lower() == "true"
BEARER_TOKEN = os.getenv("BEARER_TOKEN", "")  # For search API if needed 