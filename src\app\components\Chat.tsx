'use client';

import React, { useEffect, useRef, useState, useCallback } from 'react';
import { Message } from '@ai-sdk/react';
import { nanoid } from 'nanoid';
import ChatInput from './ChatInput';
import ChatMessage from './ChatMessage';
import { useChat as useChatContext } from '@/app/contexts/ChatContext';
import { useChat as useAiChat } from '@ai-sdk/react';
import type { AudioRecording } from '@/utils/chatHistoryUtils';
import { clientUserContextManager } from '@/utils/clientUserContext';
import { useAuth } from '@/app/contexts/AuthContext';
import { useError } from '@/app/contexts/ErrorContext';
import api from '@/utils/apiClient';
import FacilitySelector from './FacilitySelector';
import { appendClientMessage } from 'ai';
import { log } from '@/utils/logger';
import { useChatAudioRecording } from '@/hooks/useChatAudioRecording';
import { ArtifactsPanel } from './artifacts/ArtifactsPanel';
import { useArtifactsStore } from '@/stores/useArtifactsStore';
import '@/styles/item-design-system.css';
import { getTimezoneString } from '@/utils/timezoneUtils';

// 定义附件类型
interface Attachment {
  name?: string;
  contentType?: string;
  url: string;
}

// 传入的props类型
interface ChatProps {
  // 不再需要onChatSelect回调
}

// Item design system dropdown styles
const dropdownStyles = {
  menu: "absolute mt-1 z-10 bg-item-bg-card border border-item-purple/30 rounded-md shadow-xl shadow-item-purple/20 w-60 overflow-hidden transition-all duration-200 ease-in-out",
  item: "flex items-center w-full px-4 py-2.5 text-left hover:bg-item-bg-hover transition-colors text-white",
  selectedItem: "bg-item-purple/20",
  button: "flex items-center space-x-2 bg-item-bg-card hover:bg-item-bg-hover rounded-md px-3 py-1.5 transition-colors border border-item-purple/30 shadow-sm"
};

export default function Chat({}: ChatProps) {
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const [isFirstMessage, setIsFirstMessage] = useState(true);
  const [model, setModel] = useState<string>('gpt-4.1'); // Default to GPT-4.1
  const [uploadedImage, setUploadedImage] = useState<File | null>(null);
  const [isThinking, setIsThinking] = useState(false); // Add custom thinking state
  const [isVoiceProcessing, setIsVoiceProcessing] = useState(false); // Voice processing state
  const [voiceAudioElement, setVoiceAudioElement] = useState<HTMLAudioElement | null>(null); // 语音音频元素
  const [isVoiceActive, setIsVoiceActive] = useState(false); // 语音是否激活
  const [currentChatAudioRecordings, setCurrentChatAudioRecordings] = useState<AudioRecording[]>([]); // 当前聊天的音频录音
  const { activeArtifacts: artifacts, clearAll } = useArtifactsStore(); // 获取 artifacts 状态和清理函数
  // 下拉菜单状态
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [isAudioDropdownOpen, setIsAudioDropdownOpen] = useState(false); // 音频下拉菜单状态
  const dropdownRef = useRef<HTMLDivElement>(null);
  const audioDropdownRef = useRef<HTMLDivElement>(null); // 音频下拉菜单引用
  // 下拉菜单关闭定时器
  const dropdownTimerRef = useRef<NodeJS.Timeout | null>(null);
  // Add model categorization
  const [modelProvider, setModelProvider] = useState<'openai' | 'anthropic' | 'google' | 'deepseek'>('openai');



  // 使用全局错误处理
  const { setError } = useError();

  // 添加模型可用性状态
  const [availableModelProviders, setAvailableModelProviders] = useState<{
    openai: boolean;
    anthropic: boolean;
    google: boolean;
    deepseek: boolean;
  }>({
    openai: true,     // 默认情况下假设所有提供商都可用
    anthropic: true,  // 我们将稍后检查实际可用性
    google: true,
    deepseek: true
  });

  // 聊天上下文
  const {
    selectedChatId,
    saveCurrentChat,
    selectChat
  } = useChatContext();

  // 使用音频录制 hook
  const {
    startRecording,
    stopRecording,
    saveRecording,
    isRecording: isAudioRecording,
  } = useChatAudioRecording({
    chatId: selectedChatId || undefined,
    onSaveComplete: (audioUrl, originalFileName, audioInfo) => {
      log.info('Audio recording saved:', { audioUrl, originalFileName, audioInfo }, 'Chat');
      
      // 立即更新UI状态，使用服务器返回的完整录音信息
      if (audioInfo) {
        // 为了显示友好的文件名，我们可以添加一个displayName字段
        const recordingWithDisplayName = {
          ...audioInfo,
          displayName: originalFileName // 保存原始文件名用于显示
        };
        setCurrentChatAudioRecordings(prev => [...prev, recordingWithDisplayName]);
      } else {
        // 如果没有audioInfo，使用默认值
        const newRecording: AudioRecording = {
          fileName: originalFileName, // 这种情况下使用原始文件名
          path: `audio/${originalFileName}`,
          size: 0,
          duration: 0,
          createdAt: new Date().toISOString()
        };
        setCurrentChatAudioRecordings(prev => [...prev, newRecording]);
      }
      
      // 保存更新后的聊天历史，包括新的音频录音
      if (selectedChatId && messages.length > 0) {
        // 创建包含新音频录音的数组
        const updatedAudioRecordings = audioInfo 
          ? [...currentChatAudioRecordings, { ...audioInfo, displayName: originalFileName }]
          : [...currentChatAudioRecordings, {
              fileName: originalFileName,
              path: `audio/${originalFileName}`,
              size: 0,
              duration: 0,
              createdAt: new Date().toISOString()
            }];
        
        // 保存聊天历史，包括更新后的音频录音
        saveCurrentChat(messages, model, selectedChatId, updatedAudioRecordings)
          .then(() => {
            log.info('Chat history saved with audio recordings', undefined, 'Chat');
          })
          .catch(err => {
            log.error('Failed to save chat history with audio recordings:', err, 'Chat');
          });
      }
    },
    onError: (error) => {
      log.error('Audio recording error:', error, 'Chat');
      setError(`音频录制错误: ${error.message}`);
    }
  });

  // 使用认证上下文
  const { refreshAccessToken, isAuthenticated, login } = useAuth();

  // Define available models with ability to check availability
  const availableModels = [
    { id: 'gpt-4.1', name: 'GPT-4.1 (2025-04-14)', provider: 'OpenAI', color: 'text-white', requiresKey: 'openai' },
    { id: 'claude-sonnet-4-0', name: 'Claude Sonnet 4', provider: 'Anthropic', color: 'text-white', requiresKey: 'anthropic' },
    { id: 'gemini-2.5-flash-preview-05-20', name: 'Gemini 2.5 Flash Preview', provider: 'Google', color: 'text-white', requiresKey: 'google' },
    { id: 'deepseek-chat', name: 'DeepSeek Chat V3', provider: 'DeepSeek', color: 'text-white', requiresKey: 'deepseek' },
    { id: 'deepseek-reasoner', name: 'DeepSeek Reasoner', provider: 'DeepSeek', color: 'text-white', requiresKey: 'deepseek' }
  ];

  // 检查API密钥可用性
  useEffect(() => {
    const checkApiKeyAvailability = async () => {
      try {
        // 使用apiClient代替fetch发起请求
        const { data, error, status } = await api.get<{
          openai: boolean;
          anthropic: boolean;
          google: boolean;
          deepseek: boolean;
        }>('/api/check-api-keys');

        if (!error && data) {
          setAvailableModelProviders({
            openai: data.openai || false,
            anthropic: data.anthropic || false,
            google: data.google || false,
            deepseek: data.deepseek || false
          });

          // 如果当前选择的模型不可用，尝试选择一个可用的模型
          const currentModelInfo = availableModels.find(m => m.id === model);
          if (currentModelInfo) {
            const currentProvider = currentModelInfo.requiresKey.toLowerCase();
            if (currentProvider && !data[currentProvider as keyof typeof data]) {
              // 当前模型不可用，寻找第一个可用模型
              const firstAvailableModel = availableModels.find(m =>
                data[m.requiresKey.toLowerCase() as keyof typeof data]
              );
              if (firstAvailableModel) {
                setModel(firstAvailableModel.id);
                if (firstAvailableModel.provider === 'OpenAI') {
                  setModelProvider('openai');
                } else if (firstAvailableModel.provider === 'Anthropic') {
                  setModelProvider('anthropic');
                } else if (firstAvailableModel.provider === 'Google') {
                  setModelProvider('google');
                } else if (firstAvailableModel.provider === 'DeepSeek') {
                  setModelProvider('deepseek');
                }
              }
            }
          }
        } else {
          log.error('API key check failed:', error, 'Chat');
          // 如果检查失败，所有模型都设为不可用状态
          setAvailableModelProviders({
            openai: false,
            anthropic: false,
            google: false,
            deepseek: false
          });
        }
      } catch (error) {
        log.error('Error checking API key availability:', error, 'Chat');
        // 处理错误，设置所有模型为不可用
        setAvailableModelProviders({
          openai: false,
          anthropic: false,
          google: false,
          deepseek: false
        });
      }
    };

    checkApiKeyAvailability();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // 空依赖数组，确保只在组件挂载时执行一次



  // 使用新的AI SDK的useChat钩子，采用受控输入模式
  const {
    messages,
    input,
    setInput, // 使用setInput代替handleInputChange
    append, // 使用append代替handleSubmit
    status, // 替换 isLoading
    error: chatError, // 捕获useChat的错误
    setMessages,
    reload,
    stop,
    addToolResult
  } = useAiChat({
    id: selectedChatId || 'new-chat-' + nanoid(), // 添加ID标识，如果有初始ID则使用，否则生成唯一ID
    api: '/api/chat', // API端点路径
    body: {
      model: model, // 传递所选模型
      tools: true // 启用工具
    },
    headers: {
      'Authorization': `Bearer ${clientUserContextManager.getAuthToken()}`,
      'X-Tenant-Id': clientUserContextManager.getCurrentTenantId() || '',
      'X-Facility-Id': clientUserContextManager.getCurrentFacilityId() || '',
      'BI-Token': clientUserContextManager.getBiToken() || '',
      'X-Timezone': getTimezoneString()
    },
    // 添加 throttling 来限制 UI 更新频率，减少输入时的重渲染
    experimental_throttle: 50, // 每50毫秒更新一次UI
    onError: async (error) => {
      log.error('Chat error:', error, 'Chat');
      // 立即重置思考状态
      setIsThinking(false);

      // 检查是否是授权错误 (401)
      if (error.message?.includes('401') || error.message?.toLowerCase().includes('unauthorized')) {
        // 尝试刷新令牌
        const refreshed = await refreshAccessToken();

        if (refreshed) {
          // 令牌已刷新，可以重试
          setError('会话已刷新，请重试发送消息');
          // 移除setTimeout，让用户自行决定何时继续
        } else {
          // 令牌刷新失败，可能需要重新登录
          setError('会话已过期，请重新登录');

          // 如果用户未登录，可以提示登录
          if (!isAuthenticated) {
            // 显示登录提示
            setError('请登录以继续对话');
          }
        }
      } else {
        // 其他错误
        setError(`发生错误: ${error.message}`);
      }
    },
    onFinish: (message) => {
      // 结束时关闭思考状态
      setIsThinking(false);

      if (isFirstMessage) {
        setIsFirstMessage(false);
      }

      // 直接触发保存，确保响应完成后立即保存
      // 使用setTimeout确保状态更新后再保存
      // setTimeout(() => {
      //   if (messages.length > 0 && messages.length !== lastSavedMessageCount.current) {
      //     saveChat();
      //   }
      // }, 100);
    },
    onResponse: (response) => {
      if (!response.ok) {
        response.text().then(text => {
          log.error('Chat response error text:', text, 'Chat');
        }).catch(err => {
          log.error('Failed to read response text:', err, 'Chat');
        });
      }
    },
    maxSteps: 25
  });

  // 语音转录处理
  const handleVoiceError = useCallback((error: Error) => {
    log.error('Voice error:', error, 'Chat');
    setError(`语音功能错误: ${error.message}`);
  }, [setError]);

  // 文件转base64工具函数
  const fileToBase64 = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = (error) => reject(error);
    });
  };

  // 文件转附件对象
  const fileToAttachment = async (file: File): Promise<Attachment> => {
    const url = await fileToBase64(file);
    return {
      name: file.name,
      contentType: file.type,
      url: url
    };
  };

  // 自定义处理提交，使用完全受控模式
  const handleSubmit = async (content: string, file: File | null = null) => {
    // 如果没有输入文本且没有图像，则不提交
    if (!content.trim() && !file) {
      return;
    }

    try {
      // 开始发送消息，立即显示思考状态
      setIsThinking(true);

      // 如果有图像，转换为附件对象
      let attachments: Attachment[] | undefined = undefined;
      if (file) {
        const attachment = await fileToAttachment(file);
        attachments = [attachment];
      }

      // 使用append方法添加用户消息
      // 这样可以更精确地控制何时触发消息发送
      append({
        role: 'user',
        content: content,
      }, {
        experimental_attachments: attachments,
        body: {
          model: model,
          tools: true
        }
      });

      // 清除输入
      setInput('');

      // 清除上传的图像
      setUploadedImage(null);
    } catch (error) {
      log.error('Error sending message with image:', error, 'Chat');
      setError('发送消息失败，请重试');
      // 移除定时器，让错误一直显示直到用户操作
      setIsThinking(false); // 错误时关闭思考状态
    }
  };

  // 当chatError变化时更新错误状态
  useEffect(() => {
    if (chatError) {
      // 立即重置状态
      setIsThinking(false);
      setError(chatError.message || '发生未知错误');
      log.error('Chat error from hook:', chatError, 'Chat');
    }
  }, [chatError, setError]);

  // 滚动到最新消息 - 优化版本，减少抖动
  useEffect(() => {
    if (messagesEndRef.current && messages.length > 0) {
      // 只在最后一条消息是AI回复时才滚动
      const lastMessage = messages[messages.length - 1];
      if (lastMessage.role === 'assistant') {
        // 使用更智能的滚动逻辑
        const container = messagesEndRef.current.parentElement;
        if (container) {
          // 计算是否已经在底部附近
          const isNearBottom = container.scrollHeight - container.scrollTop - container.clientHeight < 100;

          // 只有在底部附近时才自动滚动
          if (isNearBottom) {
            // 使用requestAnimationFrame确保滚动在渲染后执行
            requestAnimationFrame(() => {
              messagesEndRef.current?.scrollIntoView({
                behavior: 'smooth',
                block: 'end'  // 确保滚动到底部
              });
            });
          }
        }
      }
    }
  }, [messages]);

  // 处理模型切换，添加延迟关闭
  const handleModelChange = (newModel: string) => {
    // 如果模型没有变化，不执行任何操作
    if (model === newModel) {
      return;
    }

    // 更新模型
    setModel(newModel);

    // 延迟关闭下拉菜单，避免用户误操作
    if (dropdownTimerRef.current) {
      clearTimeout(dropdownTimerRef.current);
    }

    dropdownTimerRef.current = setTimeout(() => {
      setIsDropdownOpen(false);
      dropdownTimerRef.current = null;
    }, 150); // 150毫秒延迟关闭，提供更好的用户体验

    // 更新模型提供商
    if (newModel.includes('gpt')) {
      setModelProvider('openai');
    } else if (newModel.includes('claude')) {
      setModelProvider('anthropic');
    } else if (newModel.includes('gemini')) {
      setModelProvider('google');
    } else if (newModel.includes('deepseek')) {
      setModelProvider('deepseek');
    }

    // 检查是否有现有对话
    if (messages.length === 0) {
      // 如果没有消息，可以安全地重置会话配置
      reload({
        body: {
          model: newModel,
          tools: true
        }
      });
    }
  };

  // 在useEffect中监听status变化
  useEffect(() => {
    // 当状态变为streaming时，确保isThinking为true
    if (status === 'streaming') {
      setIsThinking(true);
    }
    // 当状态变为ready且不是因为onFinish回调（可能是因为stop()被调用）
    else if (status === 'ready') {
      // 如果是因为stop()被调用，需要手动重置isThinking状态
      setIsThinking(false);
    }
    // 当状态不是streaming或loading时，不自动关闭isThinking
    // 这样在提交后，isThinking会保持为true直到onFinish回调
  }, [status]);

  // 监听聊天切换，清空 artifacts
  useEffect(() => {
    // 当切换到新聊天时，清空 artifacts
    clearAll();
  }, [selectedChatId, clearAll]);

  // 点击外部关闭下拉菜单，添加延迟以提高用户体验
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        // 添加延迟关闭，避免意外关闭
        if (dropdownTimerRef.current) {
          clearTimeout(dropdownTimerRef.current);
        }

        dropdownTimerRef.current = setTimeout(() => {
          setIsDropdownOpen(false);
          dropdownTimerRef.current = null;
        }, 150); // 调整为150毫秒，与模型选择保持一致
      }
    }

    document.addEventListener('mousedown', handleClickOutside);

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      // 清理定时器
      if (dropdownTimerRef.current) {
        clearTimeout(dropdownTimerRef.current);
      }
    };
  }, [dropdownRef]);

  // 清理定时器
  useEffect(() => {
    return () => {
      if (dropdownTimerRef.current) {
        clearTimeout(dropdownTimerRef.current);
      }
    };
  }, []);

  // 点击外部关闭音频下拉菜单
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (audioDropdownRef.current && !audioDropdownRef.current.contains(event.target as Node)) {
        setIsAudioDropdownOpen(false);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // 使用单一状态变量来跟踪是否是新对话 - 根据selectedChatId是否是由前端生成的来判断
  const [isNewConversation, setIsNewConversation] = useState(true);

  // 使用useRef来跟踪上一次保存的消息数量，避免不必要的保存
  const lastSavedMessageCount = useRef(0);

  // 使用useRef来跟踪是否正在保存，防止重复保存
  const isSavingRef = useRef(false);

  // 使用useRef来跟踪是否正在加载聊天记录，防止重复加载
  const isLoadingChatRef = useRef(false);

  // 使用useRef来跟踪setMessages的引用
  const setMessagesRef = useRef(setMessages);

  // 确保setMessagesRef始终是最新的
  useEffect(() => {
    setMessagesRef.current = setMessages;
  }, [setMessages]);

  // 使用useRef和useCallback包装setMessages，确保引用稳定
  const setMessagesStable = useCallback((newMessages: any[]) => {
    // 使用最新的setMessages引用
    setMessagesRef.current(newMessages);
  }, []);

  // 添加一个ref来跟踪上次保存的消息内容状态
  const lastSavedMessagesState = useRef('');

  // 保存聊天的函数，集中处理保存逻辑
  const saveChat = useCallback(async () => {
    // 如果正在保存或没有消息，则不执行保存
    if (isSavingRef.current || messages.length === 0) {
      return;
    }

    // 获取当前消息状态的序列化字符串，用于比较变化
    const currentMessagesState = JSON.stringify(messages);

    // 如果消息数量没有变化且内容没有变化，也不执行保存
    if (messages.length === lastSavedMessageCount.current &&
        currentMessagesState === lastSavedMessagesState.current &&
        !isNewConversation) {
      return;
    }

    try {
      // 标记正在保存
      isSavingRef.current = true;

      // 先更新本地状态，避免重复保存
      lastSavedMessageCount.current = messages.length;
      lastSavedMessagesState.current = currentMessagesState;

      // 如果是新对话，提前标记为非新对话，避免重复保存
      if (isNewConversation) {
        setIsNewConversation(false);
      }

      // 如果是新对话但selectedChatId为null，先创建一个新的聊天ID
      let chatId = selectedChatId;
      if (isNewConversation && !chatId) {
        chatId = nanoid();
      }

      // 使用创建的chatId或现有的selectedChatId
      const savedChat = await saveCurrentChat(messages, model, chatId, currentChatAudioRecordings);

      if (savedChat) {
        // 如果selectedChatId为null或与savedChat.id不一致，则更新selectedChatId
        if (selectedChatId !== savedChat.id) {
          selectChat(savedChat.id);
        }
      }
    } catch (err) {
      log.error('Failed to save chat history:', err, 'Chat');
    } finally {
      // 完成后重置保存状态
      isSavingRef.current = false;
    }
  }, [messages, model, saveCurrentChat, isNewConversation, selectedChatId, selectChat]);

  // 使用ref来跟踪上一次的selectedChatId
  const prevSelectedChatIdRef = useRef(selectedChatId);

  // 加载当前选择的聊天记录
  useEffect(() => {
    // 检查selectedChatId是否真的变化了
    const isChatIdChanged = prevSelectedChatIdRef.current !== selectedChatId;

    // 更新引用
    prevSelectedChatIdRef.current = selectedChatId;

    // 检查setMessages引用是否变化
    const isSetMessagesChanged = setMessagesRef.current !== setMessages;

    // 更新引用
    if (isSetMessagesChanged) {
      setMessagesRef.current = setMessages;
    }

    // 使用已定义的ref，避免重复加载
    if (isLoadingChatRef.current) {
      return;
    }

    // 如果selectedChatId没有真正变化，并且不是新对话，则跳过加载
    if (!isChatIdChanged && !isNewConversation && messages.length > 0) {
      return;
    }

    if (selectedChatId) {
      // 立即重置音频录音状态，避免显示上一个对话的录音
      setCurrentChatAudioRecordings([]);
      
      setIsThinking(true);
      isLoadingChatRef.current = true;

      // 如果是新对话，传递isNewlyCreated=true参数，避免尝试加载不存在的聊天历史
      selectChat(selectedChatId, isNewConversation)
        .then(chatHistory => {
          if (chatHistory && chatHistory.messages) {
            // 检查是否需要更新消息
            const currentMessagesStr = JSON.stringify(messages);
            const newMessagesStr = JSON.stringify(chatHistory.messages);
            const messagesChanged = currentMessagesStr !== newMessagesStr;

            if (messagesChanged) {
              // 使用稳定版本的setMessages
              setMessagesStable(chatHistory.messages);
              log.debug(`Loaded complete conversation, message count: ${chatHistory.messages.length}`, undefined, 'Chat');
            }

            // 更新最后保存的消息数量和状态
            lastSavedMessageCount.current = chatHistory.messages.length;
            lastSavedMessagesState.current = JSON.stringify(chatHistory.messages);

            // 如果有不同的模型，更新模型选择
            if (chatHistory.model && chatHistory.model !== model) {
              // 直接更新模型，不需要存储旧值
              setModel(chatHistory.model);
            }

            // 加载音频录音 - 在这里设置正确的音频录音状态
            if (chatHistory.audioRecordings && chatHistory.audioRecordings.length > 0) {
              setCurrentChatAudioRecordings(chatHistory.audioRecordings);
              log.debug(`Loaded ${chatHistory.audioRecordings.length} audio recordings for chat ${selectedChatId}`, undefined, 'Chat');
            } else {
              // 确保没有音频录音时状态保持为空（已在上面重置）
              log.debug(`No audio recordings found for chat ${selectedChatId}`, undefined, 'Chat');
            }

            // 标记不是新对话
            if (isNewConversation) {
              setIsNewConversation(false);
            }
          }
        })
        .catch(err => {
          log.error('Failed to load chat history:', err, 'Chat');
          setError('加载聊天历史记录失败');
        })
        .finally(() => {
          setIsThinking(false);
          isLoadingChatRef.current = false;
        });
    } else {
      // 新对话，清空所有状态
      setMessagesStable([]);
      setIsThinking(false);
      setCurrentChatAudioRecordings([]);
      if (!isNewConversation) {
        setIsNewConversation(true);
      }
      lastSavedMessageCount.current = 0;
      lastSavedMessagesState.current = '';
      log.debug('Starting new conversation, all states cleared including audio recordings', undefined, 'Chat');
    }
  }, [selectedChatId, selectChat, setError]);

  // 在AI响应完成时保存聊天
  useEffect(() => {
    // 获取当前消息状态的序列化字符串，用于比较变化
    const currentMessagesState = JSON.stringify(messages);

    // 当AI完成响应或有内容变化时保存
    if (status === 'ready' && !isThinking && messages.length > 0 &&
        (messages.length !== lastSavedMessageCount.current ||
         currentMessagesState !== lastSavedMessagesState.current)) {

      // 使用setTimeout添加轻微延迟，确保所有状态更新已完成
      const saveTimeout = setTimeout(() => {
        saveChat();
      }, 300);

      return () => clearTimeout(saveTimeout);
    }
  }, [status, isThinking, messages, saveChat, selectedChatId]);

  // 移除最新AI消息最后一条text类型part的方法
  const removeLastTextPartFromLatestAIMessage = useCallback(() => {
    setMessages(prevMessages => {
      if (prevMessages.length === 0) return prevMessages;
      
      const lastMessage = prevMessages[prevMessages.length - 1];
      
      // 检查是否是AI消息且有requireUserInput工具调用
      if (lastMessage.role === 'assistant' && lastMessage.parts) {
        const hasRequireUserInput = lastMessage.parts.some(part => 
          part.type === 'tool-invocation' && 
          part.toolInvocation?.toolName === 'requireUserInputTool' &&
          part.toolInvocation?.state === 'call'
        );
        
        if (hasRequireUserInput && lastMessage.parts.length > 0) {
          const lastPart = lastMessage.parts[lastMessage.parts.length - 1];
          
          // 如果最后一个part是text类型，则移除它
          if (lastPart.type === 'text') {
            const updatedParts = lastMessage.parts.slice(0, -1);
            const updatedMessage = {
              ...lastMessage,
              parts: updatedParts
            };
            
            return [
              ...prevMessages.slice(0, -1),
              updatedMessage
            ];
          }
        }
      }
      
      return prevMessages;
    });
  }, []);

  // 添加处理用户输入提交的方法
  const handleUserInputSubmit = useCallback((toolCallId: string, data: Record<string, any>) => {
    try {
      // 在addToolResult之前移除最新AI消息的最后一条text part
      removeLastTextPartFromLatestAIMessage();
      
      // 设置思考状态
      setIsThinking(true);

      // 使用addToolResult添加工具结果
      addToolResult({
        toolCallId,
        result: data
      });

    } catch (error) {
      log.error('Error processing user input submission:', error, 'Chat');
      setError('处理用户输入时出错，请重试');
      setIsThinking(false);
    }
  }, [addToolResult, setError, removeLastTextPartFromLatestAIMessage]);

  // 添加处理用户输入取消的方法
  const handleUserInputCancel = useCallback((toolCallId: string) => {
    try {
      // 设置思考状态
      setIsThinking(true);

      // 用户取消的结果格式
      addToolResult({
        toolCallId,
        result: {
          canceled: true,
          message: 'User canceled input'
        }
      });
    } catch (error) {
      log.error('Error processing user input cancellation:', error, 'Chat');
      setError('处理用户输入取消时出错');
      setIsThinking(false);
    }
  }, [addToolResult, setError]);

  // 监听聊天消息以检测requireUserInputTool工具调用，确保即使用户未提交也能保存
  useEffect(() => {
    // 如果没有消息或正在保存，跳过
    if (messages.length === 0 || isSavingRef.current) return;
    
    // 检查最后一条消息
    const lastMessage = messages[messages.length - 1];
    
    // 检查是否有未处理的工具调用，特别是requireUserInputTool类型
    if (lastMessage.role === 'assistant' && lastMessage.parts) {
      // 查找工具调用
      const hasToolInvocation = lastMessage.parts.some(part => {
        if (part.type === 'tool-invocation') {
          const toolInvocation = part.toolInvocation;
          // 检查是否是requireUserInputTool并且状态是"call"(等待用户输入)
          return toolInvocation.toolName === 'requireUserInputTool' && 
                 toolInvocation.state === 'call';
        }
        return false;
      });
      
      // 如果找到等待用户输入的requireUserInputTool，触发保存
      if (hasToolInvocation) {
        // 使用当前消息状态序列化进行比较
        const currentMessagesState = JSON.stringify(messages);
        
        // 如果消息状态不同，才保存
        if (currentMessagesState !== lastSavedMessagesState.current) {
          log.debug('Detected requireUserInputTool call, triggering save', undefined, 'Chat');
          saveChat();
        }
      }
    }
  }, [messages, saveChat]);

  // 使用 ref 来跟踪当前正在进行的语音转录消息 ID
  const currentVoiceTranscriptIdRef = useRef<string | null>(null);
  // 使用 ref 来获取最新的 messages 状态，避免依赖数组问题
  const messagesRef = useRef(messages);
  
  // 更新 messagesRef
  useEffect(() => {
    messagesRef.current = messages;
  }, [messages]);

  // 🎯 简单直接的方案：将最新消息存储到全局状态
  useEffect(() => {
    if (typeof window !== 'undefined') {
      window.__CHAT_MESSAGES__ = messages;
      log.debug('Updated global messages store:', messages.length, 'Chat');
    }
  }, [messages]);

  // 处理语音转录增量更新（AI 助手回复）
  const handleVoiceTranscriptDelta = useCallback((delta: string, isFromBuffer: boolean = false) => {
    if (!delta) return;
    
    // 使用函数式更新确保获取最新状态
    setMessages(prevMessages => {
      const lastMessage = prevMessages[prevMessages.length - 1];
      
      // 检查是否有匹配的语音转录消息：
      // 1. 优先匹配当前正在进行的转录ID
      // 2. 如果没有当前ID，则检查最后一条消息是否是语音转录消息
      const hasMatchingTranscript = lastMessage && 
          lastMessage.role === 'assistant' && 
          (lastMessage.id === currentVoiceTranscriptIdRef.current || 
           (currentVoiceTranscriptIdRef.current === null && lastMessage.id?.startsWith('voice-transcript-')));
      
      if (hasMatchingTranscript) {
        // 如果没有当前ID但找到了语音转录消息，更新ref指向它
        if (currentVoiceTranscriptIdRef.current === null) {
          currentVoiceTranscriptIdRef.current = lastMessage.id;
          log.debug(`Resuming transcript for existing message: ${lastMessage.id}`, undefined, 'Chat');
        }
        
        const updatedContent = lastMessage.content + delta;
        log.debug(`Appending delta to message ${lastMessage.id}, old length: ${lastMessage.content.length}, new length: ${updatedContent.length}, isFromBuffer: ${isFromBuffer}`, undefined, 'Chat');
        const updatedMessage = {
          ...lastMessage,
          content: updatedContent,
          // 同时更新 parts 数组，确保渲染内容同步
          parts: [{
            type: 'text' as const,
            text: updatedContent
          }],
        };
        
        return [
          ...prevMessages.slice(0, -1),
          updatedMessage
        ];
      } else {
        // 创建新的 AI 语音转录消息
        const messageId = 'voice-transcript-' + nanoid();
        currentVoiceTranscriptIdRef.current = messageId;
        log.debug(`Creating new AI message with id: ${messageId}, initial content: ${delta}`, undefined, 'Chat');
        
        const newMessage: Message = {
          id: messageId,
          role: 'assistant',
          content: delta,
          createdAt: new Date(),
        };
        
        return appendClientMessage({
          messages: prevMessages,
          message: newMessage
        });
      }
    });
  }, []); // 移除setMessages依赖，因为setMessages是稳定的

  // 完成语音转录时调用，清除当前转录 ID
  const handleVoiceTranscriptComplete = useCallback(() => {
    currentVoiceTranscriptIdRef.current = null;
    // 总是清除语音处理状态，无论是直接响应还是hybrid agent响应
    log.debug('Voice transcript completed, clearing voice processing state', undefined, 'Chat');
    setIsVoiceProcessing(false);
  }, []); // 移除依赖，setIsVoiceProcessing是稳定的



  // 处理用户语音转录
  const handleUserVoiceTranscript = useCallback((transcript: string) => {
    if (!transcript.trim()) return;
    
    // 用户语音结束，开始显示处理状态
    log.debug('User voice transcript received, starting voice processing...', undefined, 'Chat');
    setIsVoiceProcessing(true);
    
    // 创建符合 Message 类型的用户消息
    const userMessage: Message = {
      id: 'voice-user-' + nanoid(),
      role: 'user',
      content: transcript,
      createdAt: new Date(),
    };
    
    // 使用 appendClientMessage 正确添加用户消息到聊天历史
    setMessages(prevMessages => appendClientMessage({
      messages: prevMessages,
      message: userMessage
    }));
  }, []); // 移除setMessages依赖，因为setMessages是稳定的

  // 当前助手消息的引用 - 每个工具调用对应一个完整的助手响应
  const currentHybridMessageRef = useRef<string | null>(null);
  
  // 处理语音连接变化
  const handleVoiceConnectionChange = useCallback((connected: boolean, audioElement?: HTMLAudioElement) => {
    log.debug('Voice connection changed:', { connected, hasAudioElement: !!audioElement }, 'Chat');
    
    setIsVoiceActive(connected);
    setVoiceAudioElement(audioElement || null);
    
    if (connected && audioElement) {
      // 开始录音 - 添加状态检查避免重复启动
      if (!isAudioRecording()) {
        try {
          // 获取音频元素的媒体流
          const stream = (audioElement as any).srcObject as MediaStream;
          if (stream) {
            startRecording(stream);
          } else {
            log.warn('No media stream found on audio element', undefined, 'Chat');
          }
        } catch (error) {
          log.error('Failed to start recording:', error, 'Chat');
        }
      } else {
        log.debug('Recording already active, skipping start', undefined, 'Chat');
      }
    } else if (!connected && isAudioRecording()) {
      // 停止录音并保存
      stopRecording();
      // 延迟保存以确保最后的数据被记录
      setTimeout(async () => {
        const result = await saveRecording();
        if (result) {
          log.info('Voice recording saved:', result, 'Chat');
        }
      }, 1000);
    }
  }, [startRecording, stopRecording, saveRecording, isAudioRecording]);

  // 处理混合代理流式数据
  const handleHybridAgentStream = useCallback((streamData: any) => {
    const { toolCallId, type, data, error } = streamData;
    
    log.debug('Hybrid agent stream event received:', { toolCallId, type, data: data?.substring(0, 100), error }, 'Chat');
    
    // 注意：语音处理状态已在 handleUserVoiceTranscript 中设置
    
    // 确保有一个助手消息来承载所有工具调用和文本
    const ensureCurrentMessage = () => {
      if (!currentHybridMessageRef.current) {
        // 创建一个新的助手消息 - 使用工具调用ID作为基础
        const assistantMessageId = `voice-assistant-${toolCallId}`;
        currentHybridMessageRef.current = assistantMessageId;
        
        setMessages(prevMessages => {
          // 检查是否已经有这个消息
          const existingMessage = prevMessages.find(msg => msg.id === assistantMessageId);
          if (existingMessage) {
            return prevMessages;
          }
          
          // 创建新的助手消息
          const assistantMessage: Message = {
            id: assistantMessageId,
            role: 'assistant',
            content: '',
            parts: [],
            createdAt: new Date(),
          };
          
          log.debug('Creating new hybrid assistant message:', assistantMessage, 'Chat');
          return appendClientMessage({
            messages: prevMessages,
            message: assistantMessage
          });
        });
      }
      
      return currentHybridMessageRef.current;
    };
    
    if (type === 'chunk' && data) {
      log.debug('Processing chunk data, length:', data.length, 'Chat');
      // 解析流式数据块
      const lines = data.split('\n');
      log.debug('Chunk has lines:', lines.length, 'Chat');
      
      for (let i = 0; i < lines.length; i++) {
        const line = lines[i].trim();
        if (!line) continue;
        
        // 解析 Vercel AI SDK 流式格式
        if (line.includes(':')) {
          const colonIndex = line.indexOf(':');
          if (colonIndex > 0 && colonIndex < 3) { // 前缀应该很短
            const prefix = line.substring(0, colonIndex);
            const rawContent = line.substring(colonIndex + 1);
            log.debug(`Processing Vercel AI SDK format, prefix: "${prefix}", data:`, rawContent.substring(0, 100), 'Chat');
            
            // f: 开始新消息
            if (prefix === 'f') {
              try {
                const parsed = JSON.parse(rawContent);
                log.debug('Starting new message:', parsed, 'Chat');
                ensureCurrentMessage(); // 确保有助手消息
              } catch (e) {
                log.error('Failed to parse message start:', e, 'Chat');
              }
              continue;
            }
            
            // 0: 文本增量
            if (prefix === '0' && rawContent.startsWith('"') && rawContent.endsWith('"')) {
              // 使用 JSON.parse 正确解析字符串，处理转义字符
              let textContent: string;
              try {
                textContent = JSON.parse(rawContent);
              } catch (e) {
                // 如果 JSON.parse 失败，回退到简单的 slice 方法
                textContent = rawContent.slice(1, -1);
              }
              log.debug('Processing text delta:', textContent, 'Chat');
              
              // 确保有当前消息并添加文本内容
              const currentMessageId = ensureCurrentMessage();
              if (currentMessageId) {
                setMessages(prevMessages => {
                  const messageIndex = prevMessages.findIndex(msg => msg.id === currentMessageId);
                  if (messageIndex !== -1) {
                    const updatedMessages = [...prevMessages];
                    const message = updatedMessages[messageIndex];
                    
                    // 更新内容和添加到 parts
                    const newContent = (message.content || '') + textContent;
                    const updatedParts = [...(message.parts || [])];
                    
                    // 检查最后一个part是否是文本类型，如果是则更新，否则添加新的文本part
                    const lastPart = updatedParts[updatedParts.length - 1];
                    if (lastPart && lastPart.type === 'text') {
                      // 更新最后一个文本part
                      updatedParts[updatedParts.length - 1] = {
                        type: 'text' as const,
                        text: (lastPart.text || '') + textContent
                      };
                    } else {
                      // 添加新的文本part
                      updatedParts.push({
                        type: 'text' as const,
                        text: textContent
                      });
                    }
                    
                    updatedMessages[messageIndex] = {
                      ...message,
                      content: newContent,
                      parts: updatedParts
                    };
                    
                    return updatedMessages;
                  }
                  return prevMessages;
                });
              }
              continue;
            }
            
            // 9: 工具调用
            if (prefix === '9') {
              try {
                const parsed = JSON.parse(rawContent);
                log.debug('Processing tool call:', parsed, 'Chat');
                
                const currentMessageId = ensureCurrentMessage();
                if (currentMessageId && parsed.toolCallId && parsed.toolName && parsed.args) {
                  setMessages(prevMessages => {
                    const messageIndex = prevMessages.findIndex(msg => msg.id === currentMessageId);
                    if (messageIndex !== -1) {
                      const updatedMessages = [...prevMessages];
                      const message = updatedMessages[messageIndex];
                      
                      const toolInvocation = {
                        toolCallId: parsed.toolCallId,
                        toolName: parsed.toolName,
                        args: parsed.args,
                        state: 'call'
                      };
                      
                      // 只添加到 parts 数组
                      updatedMessages[messageIndex] = {
                        ...message,
                        parts: [
                          ...(message.parts || []),
                          {
                            type: 'tool-invocation' as const,
                            toolInvocation
                          } as any
                        ]
                      };
                      
                      log.debug('Added tool call to message:', updatedMessages[messageIndex], 'Chat');
                      return updatedMessages;
                    }
                    return prevMessages;
                  });
                }
              } catch (e) {
                log.error('Failed to parse tool call:', e, 'Chat');
              }
              continue;
            }
            
            // a: 工具结果
            if (prefix === 'a') {
              try {
                const parsed = JSON.parse(rawContent);
                log.debug('Processing tool result:', parsed, 'Chat');
                
                const currentMessageId = ensureCurrentMessage();
                if (currentMessageId && parsed.toolCallId && parsed.result) {
                  setMessages(prevMessages => {
                    const messageIndex = prevMessages.findIndex(msg => msg.id === currentMessageId);
                    if (messageIndex !== -1) {
                      const updatedMessages = [...prevMessages];
                      const message = updatedMessages[messageIndex];
                      
                      if (message.parts) {
                        // 找到对应的工具调用并更新结果
                                                 const updatedParts = message.parts.map(part => {
                           if (part.type === 'tool-invocation' && 
                               (part as any).toolInvocation.toolCallId === parsed.toolCallId) {
                             return {
                               type: 'tool-invocation' as const,
                               toolInvocation: {
                                 ...(part as any).toolInvocation,
                                 result: parsed.result,
                                 state: 'result'
                               }
                             };
                           }
                           return part;
                         });
                        
                        updatedMessages[messageIndex] = {
                          ...message,
                          parts: updatedParts
                        };
                        
                        log.debug('Updated tool result in message:', updatedMessages[messageIndex], 'Chat');
                      }
                      
                      return updatedMessages;
                    }
                    return prevMessages;
                  });
                }
              } catch (e) {
                log.error('Failed to parse tool result:', e, 'Chat');
              }
              continue;
            }
            
            // e: 步骤结束 或 d: 消息结束
            if (prefix === 'e' || prefix === 'd') {
              try {
                const parsed = JSON.parse(rawContent);
                log.debug(`${prefix === 'e' ? 'Step' : 'Message'} finished:`, parsed, 'Chat');
                // 可以在这里处理完成状态，比如更新使用统计等
              } catch (e) {
                log.error(`Failed to parse ${prefix === 'e' ? 'step' : 'message'} finish:`, e, 'Chat');
              }
              continue;
            }
          }
        }
      }
    }

    
    if (type === 'completed') {
      log.debug('Hybrid agent stream completed for tool call:', toolCallId, 'Chat');
      // 流式处理完成，重置当前消息引用以便下次调用创建新消息
      currentHybridMessageRef.current = null;
      // 使用 handleVoiceTranscriptComplete 统一处理完成逻辑
      handleVoiceTranscriptComplete();
    }
    
    if (type === 'error') {
      log.error('Hybrid agent stream error:', error, 'Chat');
      // 处理错误，直接清除状态
      log.debug('Voice processing error, clearing state', undefined, 'Chat');
      setIsVoiceProcessing(false);
    }
  }, [handleVoiceTranscriptDelta, handleVoiceTranscriptComplete, setMessages]);

  // 处理音频文件下载
  const handleAudioDownload = useCallback(async (recording: AudioRecording) => {
    try {
      // 调试日志
      console.log('准备下载音频文件:', recording);
      console.log('selectedChatId:', selectedChatId);
      
      // 获取认证头
      const authHeaders = clientUserContextManager.getAuthHeaders();
      
      // 构建下载URL
      const downloadUrl = `/api/chat-history/${selectedChatId}/audio/${recording.fileName}`;
      console.log('下载URL:', downloadUrl);
      
      // 使用fetch下载文件，携带认证token
      const response = await fetch(downloadUrl, {
        method: 'GET',
        headers: {
          ...authHeaders,
        }
      });

      if (!response.ok) {
        throw new Error(`下载失败: ${response.statusText}`);
      }

      // 获取文件blob
      const blob = await response.blob();
      
      // 创建下载链接
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = recording.displayName || recording.fileName;
      
      // 触发下载
      document.body.appendChild(link);
      link.click();
      
      // 清理
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      
      log.info('Audio file downloaded successfully:', recording.fileName, 'Chat');
    } catch (error) {
      log.error('Failed to download audio file:', error, 'Chat');
      setError(`下载音频文件失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }, [selectedChatId, setError]);

  return (
    <div className="flex h-full bg-item-bg-primary text-white">
      {/* 聊天主区域 */}
      <div className={`flex flex-col ${artifacts.length > 0 ? 'flex-1' : 'w-full'}`}>
      {/* 顶部导航栏 - 采用 item 设计系统 */}
      <div className="p-4 flex justify-between items-center flex-shrink-0 h-16">
        <div className="flex items-center w-1/3">
          <div className="relative" ref={dropdownRef}>
            <button
              onClick={() => setIsDropdownOpen(!isDropdownOpen)}
              className="flex items-center space-x-2 bg-item-bg-card hover:bg-item-bg-hover rounded-lg px-4 py-2 transition-all duration-200 border border-item-gray-800 hover:border-item-purple-light item-animate-in"
            >
              <span className="font-medium text-sm text-gray-300">
                {availableModels.find(m => m.id === model)?.name || model}
              </span>
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-4 h-4 text-item-purple">
                <path strokeLinecap="round" strokeLinejoin="round" d={isDropdownOpen ? "M4.5 15.75l7.5-7.5 7.5 7.5" : "M19.5 8.25l-7.5 7.5-7.5-7.5"} />
              </svg>
            </button>

            {isDropdownOpen && (
              <div className="absolute mt-2 z-10 bg-item-bg-card border border-item-gray-800 rounded-lg shadow-xl w-64 overflow-hidden transition-all duration-200 ease-in-out item-animate-in">
                {availableModels
                  .filter(modelOption => {
                    // 隐藏 Gemini 模型
                    if (modelOption.provider === 'Google') {
                      return false;
                    }
                    // 根据API密钥可用性过滤模型
                    const provider = modelOption.requiresKey.toLowerCase();
                    return availableModelProviders[provider as keyof typeof availableModelProviders];
                  })
                  .map(modelOption => (
                    <button
                      key={modelOption.id}
                      onClick={() => handleModelChange(modelOption.id)}
                      className={`flex items-center w-full px-4 py-3 text-left hover:bg-item-purple/20 transition-all duration-200 ${model === modelOption.id ? 'border-l-2 border-item-purple' : ''}`}
                    >
                      <div className="flex-1">
                        <div className={`text-sm font-medium ${modelOption.color}`}>{modelOption.name}</div>
                        <div className="text-xs text-item-gray-400">{modelOption.provider}</div>
                      </div>
                      {model === modelOption.id && (
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-4 h-4 text-item-purple">
                          <path strokeLinecap="round" strokeLinejoin="round" d="M4.5 12.75l6 6 9-13.5" />
                        </svg>
                      )}
                    </button>
                  ))}
              </div>
            )}
          </div>
        </div>

        <div className="flex-1 flex justify-center items-center space-x-4">
        </div>

        <div className="flex items-center space-x-2 w-1/3 justify-end">
          {/* 音频录音下载按钮 - item 设计风格 */}
          {currentChatAudioRecordings.length > 0 && (
            <div className="relative" ref={audioDropdownRef}>
              <button
                onClick={() => setIsAudioDropdownOpen(!isAudioDropdownOpen)}
                className="flex items-center space-x-2 bg-item-bg-card hover:bg-item-bg-hover rounded-lg px-4 py-2 transition-all duration-200 border border-item-gray-800 hover:border-item-purple-light"
              >
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-4 h-4">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M19.114 5.636a9 9 0 010 12.728M16.463 8.288a5.25 5.25 0 010 7.424M6.75 8.25l4.72-4.72a.75.75 0 011.28.53v15.88a.75.75 0 01-1.28.53l-4.72-4.72H4.51c-.88 0-1.704-.507-1.938-1.354A9.01 9.01 0 012.25 12c0-.83.112-1.633.322-2.396C2.806 8.756 3.63 8.25 4.51 8.25H6.75z" />
                </svg>
                <span className="text-sm">Audio ({currentChatAudioRecordings.length})</span>
              </button>
              
              {isAudioDropdownOpen && (
                <div className="absolute right-0 mt-2 w-80 item-card bg-item-bg-card border border-item-gray-800 rounded-lg shadow-xl z-50 item-animate-in">
                  <div className="p-4 border-b border-item-gray-800">
                    <p className="text-sm font-medium text-item-gray-400">Voice Recordings</p>
                  </div>
                  <div className="max-h-60 overflow-y-auto item-scrollbar">
                    {currentChatAudioRecordings.map((recording, index) => (
                      <div key={index} className="p-3 hover:bg-item-bg-hover transition-all duration-200 border-b border-item-gray-900 last:border-0">
                        <div className="flex items-center justify-between">
                          <div className="flex-1 min-w-0">
                            <p className="text-sm text-white truncate">{recording.displayName || recording.fileName}</p>
                            <p className="text-xs text-item-gray-400">
                              {new Date(recording.createdAt).toLocaleString()}
                              {recording.duration && !isNaN(recording.duration) && (
                                <> · {Math.floor(recording.duration / 60)}:{(recording.duration % 60).toString().padStart(2, '0')}</>
                              )}
                            </p>
                          </div>
                          <button
                            onClick={() => handleAudioDownload(recording)}
                            className="ml-3 p-2 bg-item-purple hover:bg-item-purple-dark rounded-lg transition-all duration-200 transform hover:scale-105"
                            title="下载音频文件"
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-4 h-4">
                              <path strokeLinecap="round" strokeLinejoin="round" d="M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5M16.5 12L12 16.5m0 0L7.5 12m4.5 4.5V3" />
                            </svg>
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
          
          {/* 设施选择器 */}
          <FacilitySelector />
        </div>
      </div>

      {/* 消息区域 - 使用 item 设计系统 */}
      <div className="flex-grow overflow-y-auto item-scrollbar messages-scroll-container" style={{ overscrollBehavior: 'contain', position: 'relative' }}>
        {messages.length === 0 && !isThinking && (
          <div className="flex h-full items-center justify-center p-4">
            <div className="text-center max-w-2xl item-animate-in">
              <h2 className="text-3xl font-medium text-item-purple mb-4">Welcome to AI Chatbot</h2>
              <div className="item-card bg-item-bg-card rounded-xl p-6 mx-auto">
                <p className="font-medium text-item-gray-400 mb-3">You can try asking me:</p>
                <ul className="list-none space-y-2 text-left">
                  <li className="flex items-start">
                    <span className="text-item-purple mr-2">•</span>
                    <span>How many orders shipped to COSTCO in the last 5 days?</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-item-purple mr-2">•</span>
                    <span>Why DN-123 commit failed?</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-item-purple mr-2">•</span>
                    <span>Upload an image and ask about its content</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        )}

        {messages.length > 0 && (
          <div className="space-y-6 p-4 message-container" style={{ contain: 'layout', willChange: 'contents' }}>
            {messages.map((message) => (
              <div key={message.id} className={`message-wrapper ${message.role === 'assistant' ? 'ai-message-wrapper' : ''}`}>
                <ChatMessage
                  message={message}
                  modelName={model}
                  onUserInputSubmit={handleUserInputSubmit}
                  onUserInputCancel={handleUserInputCancel}
                />
              </div>
            ))}
            {/* 添加底部空白提供滚动空间 */}
            <div className="h-4" ref={messagesEndRef}></div>
          </div>
        )}
      </div>

      {/* 输入区域 - 使用 item 设计系统 */}
      <div className="flex-shrink-0">
        <div className="p-3">
          <ChatInput
          input={input}
          handleSubmit={handleSubmit}
          isLoading={isThinking || status === 'streaming' || isVoiceProcessing}
          model={model}
          uploadedImage={uploadedImage}
          onStopGeneration={() => {
            // 调用AI SDK的stop函数中断流式响应或取消请求
            stop();
            // 立即重置思考状态，使输入框可用
            setIsThinking(false);
            // 清除语音处理状态
            setIsVoiceProcessing(false);
          }}
          onVoiceTranscriptDelta={handleVoiceTranscriptDelta}
          onVoiceTranscriptComplete={handleVoiceTranscriptComplete}
          onUserVoiceTranscript={handleUserVoiceTranscript}
          onHybridAgentStream={handleHybridAgentStream}
          onVoiceConnectionChange={handleVoiceConnectionChange}
          isAudioRecording={isVoiceActive && isAudioRecording()}
        />
        </div>
      </div>
      </div>
      
      {/* Artifacts 面板 */}
      {artifacts.length > 0 && (
        <div className="w-1/2 h-full">
          <ArtifactsPanel />
        </div>
      )}
    </div>
  );
}