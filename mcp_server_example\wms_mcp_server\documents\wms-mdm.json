{"openapi": "3.0.1", "info": {"title": "WMS3.0", "description": "", "version": "1.0.0"}, "tags": [], "paths": {"/mdm/carrier/search-by-paging": {"post": {"summary": "Search Carrier by Paging", "deprecated": false, "description": "", "tags": [], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CarrierSearch", "description": ""}, "example": {"currentPage": 0, "pageSize": 0, "sortingFields": [{"field": "string", "orderBy": "NONE"}], "id": "string", "ids": ["string"], "eqName": "string", "regexName": "string", "scac": "string", "scacs": ["string"], "scacEq": "string", "mcDot": "string", "mcDotEq": "string", "shippingMethods": ["TL"], "status": "EXCEPTION", "defaultShippingMethod": "TL", "serviceTypes": ["EXCEPTION"]}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RPageResultCarrierDto", "description": ""}, "example": {"code": 0, "msg": "", "success": false, "data": {"list": [{"id": "", "name": "", "scac": "", "mcDot": "", "shippingMethods": [""], "deliveryServices": [""], "createdBy": "", "createdTime": "", "updatedBy": "", "updatedTime": "", "status": "", "defaultShippingMethod": "", "mcNum": "", "partnerSCAC": "", "coverageZipcodes": [""], "contactName": "", "contactEmail": "", "contactPhone": ""}], "totalCount": 0, "currentPage": 0, "pageSize": 0, "totalPage": 0}}}}, "headers": {}}}, "security": [{"oauth21": ["profile", "email", "phone", "openid"]}]}}}, "components": {"schemas": {"SortingField": {"type": "object", "properties": {"field": {"type": "string", "description": ""}, "orderBy": {"type": "string", "description": "", "enum": ["NONE", "ASC", "DESC"]}}}, "CarrierDto": {"type": "object", "properties": {"id": {"type": "string", "description": ""}, "name": {"type": "string", "description": ""}, "scac": {"type": "string", "description": ""}, "mcDot": {"type": "string", "description": ""}, "shippingMethods": {"type": "array", "items": {"type": "string", "enum": ["TL", "LTL", "SMALL_PARCEL", "WILL_CALL"]}, "description": ""}, "deliveryServices": {"type": "array", "items": {"type": "string"}, "description": ""}, "createdBy": {"type": "string", "description": ""}, "createdTime": {"type": "string", "description": ""}, "updatedBy": {"type": "string", "description": ""}, "updatedTime": {"type": "string", "description": ""}, "status": {"type": "string", "description": "", "enum": ["EXCEPTION", "ACTIVE", "INACTIVE"]}, "defaultShippingMethod": {"type": "string", "description": ""}, "mcNum": {"type": "string", "description": ""}, "partnerSCAC": {"type": "string", "description": ""}, "coverageZipcodes": {"type": "array", "items": {"type": "string"}, "description": ""}, "contactName": {"type": "string", "description": ""}, "contactEmail": {"type": "string", "description": ""}, "contactPhone": {"type": "string", "description": ""}}}, "CarrierSearch": {"type": "object", "properties": {"currentPage": {"type": "integer", "description": ""}, "pageSize": {"type": "integer", "description": ""}, "sortingFields": {"type": "array", "items": {"$ref": "#/components/schemas/SortingField", "description": "com.item.xms.persistence.query.SortingField"}, "description": ""}, "id": {"type": "string", "description": ""}, "ids": {"type": "array", "items": {"type": "string"}, "description": ""}, "eqName": {"type": "string", "description": ""}, "names": {"type": "array", "items": {"type": "string"}, "description": ""}, "regexName": {"type": "string", "description": ""}, "scac": {"type": "string", "description": ""}, "scacs": {"type": "array", "items": {"type": "string"}, "description": ""}, "scacEq": {"type": "string", "description": ""}, "mcDot": {"type": "string", "description": ""}, "mcDotEq": {"type": "string", "description": ""}, "shippingMethods": {"type": "array", "items": {"type": "string", "enum": ["TL", "LTL", "SMALL_PARCEL", "WILL_CALL"]}, "description": ""}, "status": {"type": "string", "description": "", "enum": ["EXCEPTION", "ACTIVE", "INACTIVE"]}, "defaultShippingMethod": {"type": "string", "description": "", "enum": ["TL", "LTL", "SMALL_PARCEL", "WILL_CALL"]}, "deliveryServices": {"type": "array", "items": {"type": "string"}, "description": ""}, "mcNumEq": {"type": "string", "description": ""}}}, "RPageResultCarrierDto": {"type": "object", "properties": {"code": {"type": "integer", "description": ""}, "msg": {"type": "string", "description": ""}, "success": {"type": "boolean", "description": ""}, "data": {"$ref": "#/components/schemas/PageResultCarrierDto", "description": ""}}}, "PageResultCarrierDto": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/components/schemas/CarrierDto", "description": "<p></p>"}, "description": ""}, "totalCount": {"type": "integer", "description": ""}, "currentPage": {"type": "integer", "description": ""}, "pageSize": {"type": "integer", "description": ""}, "totalPage": {"type": "integer", "description": ""}}}}, "securitySchemes": {"oauth21": {"type": "oauth2", "flows": {"password": {"authorizationUrl": "{{authUrl}}", "tokenUrl": "{{accessTokenUrl}}", "refreshUrl": "", "scopes": {"profile": "", "email": "", "phone": "", "openid": ""}}}}}}, "servers": [], "security": [{"oauth21": ["profile", "email", "phone", "openid"]}]}