import { clientUserContextManager } from './clientUserContext';

const isDevelopment = process.env.NODE_ENV === 'development';
const PORTAL_ENDPOINT = process.env.NEXT_PUBLIC_PORTAL_ENDPOINT || 'https://portal-staging.item.com';

type RequestMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';

interface RequestOptions {
  method?: RequestMethod;
  headers?: Record<string, string>;
  body?: any;
  skipAuth?: boolean;
}

interface PortalApiResponse<T> {
  data: T | null;
  error: string | null;
  status: number;
  success?: boolean;
  msg?: string;
}

export async function portalApiRequest<T = any>(
  path: string,
  options: RequestOptions = {}
): Promise<PortalApiResponse<T>> {
  const apiPath = path.startsWith('/') ? path.substring(1) : path;
  
  let url;
  if (isDevelopment) {
    url = `/api/portal/${apiPath}`;
  } else {
    url = `${PORTAL_ENDPOINT}/${apiPath}`;
  }
  
  const {
    method = 'GET',
    headers = {},
    body,
    skipAuth = false,
  } = options;
  
  const requestHeaders: Record<string, string> = {
    'Content-Type': 'application/json',
    ...headers
  };
  
  if (!skipAuth) {
    const token = clientUserContextManager.getAuthToken();
    if (token) {
      requestHeaders['Authorization'] = token.startsWith('Bearer ') ? token : `Bearer ${token}`;
    }
  }
  
  const requestConfig: RequestInit = {
    method,
    headers: requestHeaders,
    credentials: 'same-origin'
  };
  
  if (body && method !== 'GET') {
    requestConfig.body = typeof body === 'string' ? body : JSON.stringify(body);
  }
  
  try {
    console.log(`[Portal API] ${method} ${url}`, { headers: requestHeaders, body });
    const response = await fetch(url, requestConfig);
    
    if (response.ok) {
      try {
        const data = await response.json();
        return { 
          data: data.data, 
          error: null, 
          status: response.status,
          success: true,
          msg: data.msg
        };
      } catch (e) {
        const text = await response.text();
        return { data: text as any, error: null, status: response.status };
      }
    } else {
      let errorMessage: string;
      try {
        const errorData = await response.json();
        errorMessage = errorData.msg || errorData.message || `HTTP 错误: ${response.status}`;
      } catch (e) {
        errorMessage = await response.text() || `HTTP 错误: ${response.status}`;
      }
      
      return {
        data: null,
        error: errorMessage,
        status: response.status,
        success: false
      };
    }
  } catch (error) {
    console.error('[Portal API] 请求失败:', error);
    return {
      data: null,
      error: error instanceof Error ? error.message : '请求失败',
      status: 0,
      success: false
    };
  }
}

export const portalApi = {
  get: <T>(path: string, options: Omit<RequestOptions, 'method' | 'body'> = {}) => 
    portalApiRequest<T>(path, { ...options, method: 'GET' }),
  
  post: <T>(path: string, body: any, options: Omit<RequestOptions, 'method'> = {}) => 
    portalApiRequest<T>(path, { ...options, method: 'POST', body }),
  
  put: <T>(path: string, body: any, options: Omit<RequestOptions, 'method'> = {}) => 
    portalApiRequest<T>(path, { ...options, method: 'PUT', body }),
  
  patch: <T>(path: string, body: any, options: Omit<RequestOptions, 'method'> = {}) => 
    portalApiRequest<T>(path, { ...options, method: 'PATCH', body }),
  
  delete: <T>(path: string, options: Omit<RequestOptions, 'method'> = {}) => 
    portalApiRequest<T>(path, { ...options, method: 'DELETE' })
};

export default portalApi; 