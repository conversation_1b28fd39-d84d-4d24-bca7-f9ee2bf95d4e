{"openapi": "3.0.1", "info": {"title": "WMS3.0", "description": "", "version": "1.0.0"}, "tags": [], "paths": {"/wms/outbound/order-plan/create": {"post": {"summary": "Create New Order Plan", "deprecated": false, "description": "", "tags": [], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderPlanCreateCmd", "description": ""}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RIdResponse", "description": ""}, "example": {"code": 0, "msg": "", "success": false, "data": {"id": ""}}}}, "headers": {}}}, "security": [{"oauth21": ["profile", "email", "phone", "openid"]}]}}, "/wms/outbound/order-plan/update": {"put": {"summary": "Update Order Plan", "deprecated": false, "description": "", "tags": [], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderPlanUpdateCmd", "description": ""}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RVoid", "description": ""}, "example": {"code": 0, "msg": "", "success": false, "data": null}}}, "headers": {}}}, "security": [{"oauth21": ["profile", "email", "phone", "openid"]}]}}}, "components": {"schemas": {"RVoid": {"type": "object", "properties": {"code": {"type": "integer", "description": ""}, "msg": {"type": "string", "description": ""}, "success": {"type": "boolean", "description": ""}, "data": {"description": "", "type": "null"}}}, "IdResponse": {"type": "object", "properties": {"id": {"type": "string", "description": ""}}}, "RIdResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": ""}, "msg": {"type": "string", "description": ""}, "success": {"type": "boolean", "description": ""}, "data": {"$ref": "#/components/schemas/IdResponse", "description": ""}}}, "OrderPlanCreateCmd": {"type": "object", "properties": {"pickType": {"type": "string", "description": "", "enum": ["PALLET_PICK", "CASE_PICK", "PIECE_PICK", "NONE"]}, "pickMethod": {"type": "string", "description": "", "enum": ["ORDER_PICK", "WAVE_PICK_BY_ITEM", "WAVE_PICK_BY_ORDER", "BATCH_ORDER_PICK"]}, "pickMode": {"type": "string", "description": "", "enum": ["PICK_TO_WALL", "OPPORTUNITY_PICK", "ROBOTIC_SORTING", "PICK_BY_TOTE_CART"]}, "enableAutoGroup": {"type": "boolean", "description": ""}, "orderPlanItemLineGroupBys": {"type": "array", "items": {"type": "string", "enum": ["VLG", "CARRIER", "SHIP_TO", "ORDER", "ITEM", "PICK_TYPE"]}, "description": ""}, "defaultAssigneeUserId": {"type": "string", "description": ""}, "defaultReplenishmentTaskAssigneeUserId": {"type": "string", "description": ""}, "orderIds": {"type": "array", "items": {"type": "string"}, "description": ""}, "replenishmentTaskIds": {"type": "array", "items": {"type": "string"}, "description": ""}, "customerId": {"type": "string", "description": ""}, "orderDispatchSettingId": {"type": "string", "description": ""}, "taskTags": {"type": "array", "items": {"type": "string"}, "description": ""}, "taskPriority": {"type": "string", "description": "", "enum": ["LOW", "MIDDLE", "HIGH", "TOP"]}, "maxOrderQtyPerPickTask": {"type": "integer", "description": ""}, "maxItemLineQtyPerPickTask": {"type": "integer", "description": ""}, "shippingRule": {"type": "string", "description": "", "enum": ["FIFO", "LIFO", "FEFO", "LSFO", "LEFO"]}, "removePlanOrdersWithErrorAndMarkException": {"type": "boolean", "description": ""}, "noNeedAutoRelease": {"type": "boolean", "description": ""}, "skipPackingScanForItem": {"type": "boolean", "description": ""}, "splitToActions": {"type": "boolean", "description": ""}}}, "OrderPlanUpdateCmd": {"type": "object", "properties": {"requestJson": {"type": "string", "description": ""}, "nullifyFields": {"type": "array", "items": {"type": "string"}, "description": ""}, "id": {"type": "string", "description": ""}, "pickType": {"type": "string", "description": "", "enum": ["PALLET_PICK", "CASE_PICK", "PIECE_PICK", "NONE"]}, "pickMethod": {"type": "string", "description": "", "enum": ["ORDER_PICK", "WAVE_PICK_BY_ITEM", "WAVE_PICK_BY_ORDER", "BATCH_ORDER_PICK"]}, "pickMode": {"type": "string", "description": "", "enum": ["PICK_TO_WALL", "OPPORTUNITY_PICK", "ROBOTIC_SORTING", "PICK_BY_TOTE_CART"]}, "enableAutoGroup": {"type": "boolean", "description": ""}, "orderPlanItemLineGroupBys": {"type": "array", "items": {"type": "string", "enum": ["VLG", "CARRIER", "SHIP_TO", "ORDER", "ITEM", "PICK_TYPE"]}, "description": ""}, "defaultAssigneeUserId": {"type": "string", "description": ""}, "defaultReplenishmentTaskAssigneeUserId": {"type": "string", "description": ""}, "orderIds": {"type": "array", "items": {"type": "string"}, "description": ""}, "pickTaskIds": {"type": "array", "items": {"type": "string"}, "description": ""}, "replenishmentTaskIds": {"type": "array", "items": {"type": "string"}, "description": ""}, "status": {"type": "string", "description": "", "enum": ["BUILDING", "PICK_SUGGESTED", "TASK_CREATED", "SCHEDULED", "RELEASED", "COMPLETED", "CANCELLED"]}, "createdBy": {"type": "string", "description": ""}, "createdTime": {"type": "string", "description": ""}, "updatedBy": {"type": "string", "description": ""}, "updatedTime": {"type": "string", "description": ""}, "customerId": {"type": "string", "description": ""}, "orderDispatchSettingId": {"type": "string", "description": ""}, "taskTags": {"type": "array", "items": {"type": "string"}, "description": ""}, "taskPriority": {"type": "string", "description": "", "enum": ["LOW", "MIDDLE", "HIGH", "TOP"]}, "maxOrderQtyPerPickTask": {"type": "integer", "description": ""}, "maxItemLineQtyPerPickTask": {"type": "integer", "description": ""}, "shippingRule": {"type": "string", "description": "", "enum": ["FIFO", "LIFO", "FEFO", "LSFO", "LEFO"]}, "removePlanOrdersWithErrorAndMarkException": {"type": "boolean", "description": ""}, "noNeedAutoRelease": {"type": "boolean", "description": ""}, "skipPackingScanForItem": {"type": "boolean", "description": ""}, "splitToActions": {"type": "boolean", "description": ""}}, "required": ["id"]}}, "securitySchemes": {"oauth21": {"type": "oauth2", "flows": {"password": {"authorizationUrl": "{{authUrl}}", "tokenUrl": "{{accessTokenUrl}}", "refreshUrl": "", "scopes": {"profile": "", "email": "", "phone": "", "openid": ""}}}}}}, "servers": [], "security": [{"oauth21": ["profile", "email", "phone", "openid"]}]}