import { EmbedTokenManager } from '../EmbedTokenManager';

export class PortalEmbedTokenManager implements EmbedTokenManager {
  getRefreshToken(): string | null {
    const raw = localStorage.getItem('item-client-web/iam_token');
    if (!raw) return null;
    try {
      const parsed = JSON.parse(raw);
      if (parsed && parsed.value) {
        const valueObj = typeof parsed.value === 'string' ? JSON.parse(parsed.value) : parsed.value;
        return valueObj.refresh_token || null;
      }
    } catch (e) {
      // 兼容直接存储对象的情况
      try {
        const valueObj = JSON.parse(raw);
        return valueObj.refresh_token || null;
      } catch {
        return null;
      }
    }
    return null;
  }

  getCurrentToken(): string | null {
    const raw = localStorage.getItem('item-client-web/iam_token');
    if (!raw) return null;
    try {
      const parsed = JSON.parse(raw);
      if (parsed && parsed.value) {
        const valueObj = typeof parsed.value === 'string' ? JSON.parse(parsed.value) : parsed.value;
        return valueObj.access_token || null;
      }
    } catch (e) {
      // 兼容直接存储对象的情况
      try {
        const valueObj = JSON.parse(raw);
        return valueObj.access_token || null;
      } catch {
        return null;
      }
    }
    return null;
  }

  removeToken(): void {
  }

  async refreshToken(): Promise<string | null> {
    return null;
  }

  updateClientToken(token: string) {
  }
} 