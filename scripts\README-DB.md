# 数据库配置说明

## 配置方法

本项目中的数据库连接可以通过以下方式配置：

1. 环境变量（推荐）：在项目根目录创建 `.env.local` 文件，添加以下配置：

```
DB_HOST=your_database_host
DB_PORT=your_database_port
DB_USER=your_database_user
DB_PASSWORD=your_database_password
DB_NAME=your_database_name
```

2. 直接修改配置文件：如果无法使用环境变量，可以直接修改 `scripts/db-config.js` 文件中的默认值。

## 数据库脚本

本目录包含与数据库相关的脚本：

- `initOrganizationMemories.js`：初始化组织记忆数据
- `db-config.js`：数据库连接配置

## 执行脚本

执行初始化组织记忆脚本：

```bash
node scripts/initOrganizationMemories.js
```

## 安装依赖

确保安装了必要的依赖：

```bash
npm install
```

数据库连接使用 `mysql2` 包，已添加到项目的 devDependencies 中。 