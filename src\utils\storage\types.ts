import { ChatHistory } from '../chatHistoryUtils';

// 存储类型
export type StorageType = 'local' | 's3';

// 聊天历史索引项
export interface ChatHistoryIndexItem {
  id: string;
  title: string;
  createdAt: string;
  updatedAt: string;
}

// 用户聊天历史索引
export interface UserChatHistoryIndex {
  userId: string;
  chats: ChatHistoryIndexItem[];
}

// 全局聊天历史索引
export interface ChatHistoryIndexFile {
  version: number;
  lastUpdated: string;
  users: Record<string, UserChatHistoryIndex>;
}

// 存储配置接口
export interface StorageConfig {
  type: StorageType;
  s3?: {
    bucketName: string;
    region: string;
    prefix: string;
    accessKeyId?: string;
    secretAccessKey?: string;
  };
  local?: {
    dir: string;
  };
  // 报告存储配置
  reports?: {
    dir?: string;  // 本地存储目录，默认为 'data/reports'
    prefix?: string; // S3存储前缀，默认为 'reports/'
  };
}

// 聊天历史存储接口
export interface ChatHistoryStorage {
  saveChat(chatHistory: ChatHistory): Promise<void>;
  getChatHistoryList(userId?: string): Promise<ChatHistory[]>;
  getChatHistory(id: string, userId?: string): Promise<ChatHistory | null>;
  deleteChat(id: string, userId?: string): Promise<boolean>;

  // 索引相关方法
  updateChatHistoryIndex(chatHistory: ChatHistory, isDelete?: boolean): Promise<void>;
  getUserChatHistoryIndex(userId: string): Promise<ChatHistoryIndexItem[]>;
  getAllChatHistoryIndex(): Promise<Record<string, ChatHistoryIndexItem[]>>;
  getAllUserIds(): Promise<string[]>;
}

// 从环境变量获取存储配置
export const getStorageConfig = (): StorageConfig => {
  console.log('process.env.STORAGE_TYPE', process.env.STORAGE_TYPE);
  const storageType = (process.env.STORAGE_TYPE || 'local') as StorageType;

  const config: StorageConfig = {
    type: storageType,
  };

  if (storageType === 's3') {
    config.s3 = {
      bucketName: process.env.S3_BUCKET_NAME || 'cyberbot-chat-history',
      region: process.env.AWS_REGION || 'us-east-1',
      prefix: process.env.S3_PREFIX || 'chat-history/',
      accessKeyId: process.env.AWS_ACCESS_KEY_ID,
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
    };

    // 添加报告存储配置
    config.reports = {
      prefix: process.env.S3_REPORTS_PREFIX || 'reports/'
    };

    console.log('已配置S3存储，区域:', config.s3.region, '桶名:', config.s3.bucketName);
    console.log('聊天历史前缀:', config.s3.prefix, '报告前缀:', config.reports.prefix);
    console.log('AWS凭证是否存在:', config.s3.accessKeyId ? '是' : '否');
  } else {
    config.local = {
      dir: process.env.LOCAL_STORAGE_DIR || 'src/chat-history',
    };

    // 添加报告存储配置
    config.reports = {
      dir: process.env.LOCAL_REPORTS_DIR || 'data/reports'
    };

    console.log('已配置本地文件存储，聊天历史目录:', config.local.dir, '报告目录:', config.reports.dir);
  }

  return config;
};