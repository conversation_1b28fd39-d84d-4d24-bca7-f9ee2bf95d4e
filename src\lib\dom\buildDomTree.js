// 导出一个函数，该函数接受一个参数对象
export default function buildDomTree(args) {
  // 设置默认参数
  args = args || {
    doHighlightElements: true,
    focusHighlightIndex: -1,
    viewportExpansion: 0,
    debugMode: false,
  };
  
  const { doHighlightElements, focusHighlightIndex, viewportExpansion, debugMode } = args;
  let highlightIndex = 0;
  
  // 性能监控
  const PERF_METRICS = debugMode ? {
    buildDomTreeCalls: 0,
    timings: {
      buildDomTree: 0,
      highlightElement: 0,
      isInteractiveElement: 0,
      isElementVisible: 0,
      isTopElement: 0,
      isInExpandedViewport: 0,
      isTextNodeVisible: 0,
    },
    cacheMetrics: {
      boundingRectCacheHits: 0,
      boundingRectCacheMisses: 0,
      computedStyleCacheHits: 0,
      computedStyleCacheMisses: 0,
      getBoundingClientRectTime: 0,
      getComputedStyleTime: 0,
      boundingRectHitRate: 0,
      computedStyleHitRate: 0,
    }
  } : null;
  
  // 缓存机制
  const boundingRectCache = new Map();
  const computedStyleCache = new Map();
  
  // 获取元素样式，带缓存
  function getComputedStyleCached(element) {
    if (!element) return null;
    
    const cacheKey = element.uniqueId || element;
    if (computedStyleCache.has(cacheKey)) {
      if (debugMode) PERF_METRICS.cacheMetrics.computedStyleCacheHits++;
      return computedStyleCache.get(cacheKey);
    }
    
    if (debugMode) PERF_METRICS.cacheMetrics.computedStyleCacheMisses++;
    const start = performance.now();
    const style = window.getComputedStyle(element);
    if (debugMode) PERF_METRICS.cacheMetrics.getComputedStyleTime += performance.now() - start;
    computedStyleCache.set(cacheKey, style);
    return style;
  }
  
  // 获取元素边界矩形，带缓存
  function getBoundingClientRectCached(element) {
    if (!element) return null;
    
    const cacheKey = element.uniqueId || element;
    if (boundingRectCache.has(cacheKey)) {
      if (debugMode) PERF_METRICS.cacheMetrics.boundingRectCacheHits++;
      return boundingRectCache.get(cacheKey);
    }
    
    if (debugMode) PERF_METRICS.cacheMetrics.boundingRectCacheMisses++;
    const start = performance.now();
    const rect = element.getBoundingClientRect();
    if (debugMode) PERF_METRICS.cacheMetrics.getBoundingClientRectTime += performance.now() - start;
    boundingRectCache.set(cacheKey, rect);
    return rect;
  }
  
  // 检查元素是否在扩展视口内
  function isInExpandedViewport(element, expansion = 0) {
    const rect = getBoundingClientRectCached(element);
    if (!rect) return false;
    
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;
    
    return !(
      rect.bottom < -expansion ||
      rect.right < -expansion ||
      rect.top > viewportHeight + expansion ||
      rect.left > viewportWidth + expansion
    );
  }
  
  // 检查元素是否被其他元素遮挡
  function isElementObscured(element) {
    const rect = getBoundingClientRectCached(element);
    if (!rect) return true;
    
    // 如果元素面积太小，认为它不可见
    if (rect.width < 1 || rect.height < 1) return true;
    
    // 检查元素中心点
    const centerX = rect.left + rect.width / 2;
    const centerY = rect.top + rect.height / 2;
    
    // 获取该点上最顶层的元素
    const topElement = document.elementFromPoint(centerX, centerY);
    
    // 如果没有元素或者是当前元素的子元素，则认为没有被遮挡
    if (!topElement || element === topElement || element.contains(topElement)) {
      return false;
    }
    
    // 检查是否是当前元素的父元素
    let parent = element.parentElement;
    while (parent) {
      if (parent === topElement) return false;
      parent = parent.parentElement;
    }
    
    // 被其他元素遮挡
    return true;
  }
  
  // 检查元素是否可见
  function isElementVisible(element) {
    if (!element) return false;
    
    const style = getComputedStyleCached(element);
    if (!style) return false;
    
    // 检查基本可见性
    if (style.display === 'none' || style.visibility === 'hidden' || style.opacity === '0') {
      return false;
    }
    
    // 检查尺寸
    const rect = getBoundingClientRectCached(element);
    if (!rect || rect.width === 0 || rect.height === 0) {
      return false;
    }
    
    // 检查是否在视口内
    if (!isInExpandedViewport(element, viewportExpansion)) {
      return false;
    }
    
    // 检查是否被遮挡（可选，可能会影响性能）
    if (debugMode && PERF_METRICS) {
      const start = performance.now();
      const obscured = isElementObscured(element);
      PERF_METRICS.timings.isElementVisible += performance.now() - start;
      if (obscured) return false;
    }
    
    return true;
  }
  
  // 检查文本节点是否可见
  function isTextNodeVisible(textNode) {
    if (!textNode || !textNode.nodeValue || !textNode.nodeValue.trim()) {
      return false;
    }
    
    const parentElement = textNode.parentElement;
    if (!parentElement) return false;
    
    return isElementVisible(parentElement);
  }
  
  // 检查元素是否是顶层元素
  function isTopElement(element) {
    if (!element) return false;
    
    // 检查是否有子元素
    if (element.children.length > 0) {
      // 如果有子元素，但都不可见，仍然可以是顶层元素
      let hasVisibleChildren = false;
      for (let i = 0; i < element.children.length; i++) {
        if (isElementVisible(element.children[i])) {
          hasVisibleChildren = true;
          break;
        }
      }
      if (hasVisibleChildren) return false;
    }
    
    return true;
  }
  
  // 检查元素是否可交互
  function isInteractiveElement(element) {
    if (!element) return false;
    
    if (debugMode && PERF_METRICS) {
      PERF_METRICS.buildDomTreeCalls++;
      const start = performance.now();
      const result = _isInteractiveElement(element);
      PERF_METRICS.timings.isInteractiveElement += performance.now() - start;
      return result;
    }
    
    return _isInteractiveElement(element);
  }
  
  function _isInteractiveElement(element) {
    // 标签名检查
    const interactiveTags = ['a', 'button', 'input', 'select', 'textarea', 'summary', 'option', 'label'];
    if (interactiveTags.includes(element.tagName.toLowerCase())) {
      return true;
    }
    
    // 角色检查
    const interactiveRoles = ['button', 'link', 'checkbox', 'menuitem', 'tab', 'radio', 'switch', 'option'];
    const role = element.getAttribute('role');
    if (role && interactiveRoles.includes(role.toLowerCase())) {
      return true;
    }
    
    // 事件监听器检查
    if (element.onclick || element.addEventListener) {
      // 检查是否有点击相关的事件处理程序
      const elementClone = element.cloneNode(true);
      if (elementClone.onclick !== null) {
        return true;
      }
    }
    
    // 样式检查
    const style = getComputedStyleCached(element);
    if (style && style.cursor === 'pointer') {
      return true;
    }
    
    // 检查常见的交互类名
    const classNames = element.className.split(' ');
    const interactiveClassPatterns = ['btn', 'button', 'clickable', 'link', 'selectable', 'nav-item'];
    for (const pattern of interactiveClassPatterns) {
      if (classNames.some(cls => cls.toLowerCase().includes(pattern))) {
        return true;
      }
    }
    
    // 检查ARIA属性
    if (element.hasAttribute('aria-haspopup') || 
        element.hasAttribute('aria-expanded') || 
        element.hasAttribute('aria-pressed')) {
      return true;
    }
    
    return false;
  }
  
  // 计算XPath
  function getXPath(element) {
    if (!element) return '';
    if (element === document.documentElement) return '/html';
    
    let path = '';
    let current = element;
    while (current && current.nodeType === Node.ELEMENT_NODE) {
      let index = 1;
      let sibling = current.previousElementSibling;
      while (sibling) {
        if (sibling.tagName === current.tagName) {
          index++;
        }
        sibling = sibling.previousElementSibling;
      }
      
      const tagName = current.tagName.toLowerCase();
      path = `/${tagName}[${index}]${path}`;
      current = current.parentElement;
    }
    
    return path;
  }
  
  // 构建DOM树
  function buildDomTreeInternal(rootElement) {
    if (debugMode && PERF_METRICS) {
      const start = performance.now();
      const result = _buildDomTree(rootElement);
      PERF_METRICS.timings.buildDomTree = performance.now() - start;
      
      // 计算缓存命中率
      if (PERF_METRICS.cacheMetrics) {
        const boundingRectTotal = PERF_METRICS.cacheMetrics.boundingRectCacheHits + PERF_METRICS.cacheMetrics.boundingRectCacheMisses;
        const computedStyleTotal = PERF_METRICS.cacheMetrics.computedStyleCacheHits + PERF_METRICS.cacheMetrics.computedStyleCacheMisses;
        
        PERF_METRICS.cacheMetrics.boundingRectHitRate = boundingRectTotal > 0 ? 
          PERF_METRICS.cacheMetrics.boundingRectCacheHits / boundingRectTotal : 0;
          
        PERF_METRICS.cacheMetrics.computedStyleHitRate = computedStyleTotal > 0 ? 
          PERF_METRICS.cacheMetrics.computedStyleCacheHits / computedStyleTotal : 0;
          
        PERF_METRICS.cacheMetrics.overallHitRate = (boundingRectTotal + computedStyleTotal) > 0 ? 
          (PERF_METRICS.cacheMetrics.boundingRectCacheHits + PERF_METRICS.cacheMetrics.computedStyleCacheHits) / 
          (boundingRectTotal + computedStyleTotal) : 0;
      }
      
      return result;
    }
    
    return _buildDomTree(rootElement);
  }
  
  function _buildDomTree(rootElement) {
    const nodeMap = {};
    let rootId = null;
    
    // 跳过的元素类型
    const skipTags = new Set(['script', 'style', 'noscript', 'svg', 'path', 'defs', 'clipPath', 'metadata']);
    
    function processNode(node, parentId = null) {
      if (!node) return null;
      
      // 生成唯一ID
      const nodeId = Math.random().toString(36).substr(2, 9);
      
      if (rootId === null) {
        rootId = nodeId;
      }
      
      // 处理文本节点
      if (node.nodeType === Node.TEXT_NODE) {
        const text = node.textContent.trim();
        if (!text) return null;
        
        const isVisible = isTextNodeVisible(node);
        
        nodeMap[nodeId] = {
          type: 'TEXT_NODE',
          text,
          isVisible,
          parentId
        };
        
        return nodeId;
      }
      
      // 处理元素节点
      if (node.nodeType === Node.ELEMENT_NODE) {
        // 跳过特定标签
        if (skipTags.has(node.tagName.toLowerCase())) {
          return null;
        }
        
        const isVisible = isElementVisible(node);
        
        // 如果元素不可见且不在扩展视口内，可以跳过（除非是根元素）
        if (parentId !== null && !isVisible && !isInExpandedViewport(node, viewportExpansion)) {
          return null;
        }
        
        const isInteractive = isInteractiveElement(node);
        
        // 获取元素属性
        const attributes = {};
        for (const attr of node.attributes) {
          attributes[attr.name] = attr.value;
        }
        
        const xpath = getXPath(node);
        
        // 获取边界矩形
        const rect = getBoundingClientRectCached(node);
        const coordinates = rect ? {
          x: rect.left,
          y: rect.top,
          width: rect.width,
          height: rect.height
        } : null;
        
        // 检查是否在视口内
        const isInViewport = isInExpandedViewport(node, 0); // 不使用扩展
        
        // 检查是否是顶层元素
        const isTopElementValue = isTopElement(node);
        
        // 高亮可交互元素
        let highlightIdx = undefined;
        if (doHighlightElements && isInteractive && isVisible) {
          highlightIdx = highlightIndex++;
          
          // 如果有指定的焦点高亮索引，并且当前元素是该索引，添加焦点类
          if (focusHighlightIndex !== -1 && highlightIdx === focusHighlightIndex) {
            // 这里可以添加焦点样式，但在这个实现中我们只返回数据
          }
        }
        
        nodeMap[nodeId] = {
          type: 'ELEMENT_NODE',
          tagName: node.tagName.toLowerCase(),
          xpath,
          attributes,
          isVisible,
          isInteractive,
          isTopElement: isTopElementValue,
          isInViewport,
          highlightIndex: highlightIdx,
          shadowRoot: !!node.shadowRoot,
          parentId,
          children: [],
          coordinates,
          viewport: {
            width: window.innerWidth,
            height: window.innerHeight
          }
        };
        
        // 处理子节点
        for (const childNode of node.childNodes) {
          const childId = processNode(childNode, nodeId);
          if (childId) {
            nodeMap[nodeId].children.push(childId);
          }
        }
        
        return nodeId;
      }
      
      return null;
    }
    
    processNode(rootElement);
    
    return {
      map: nodeMap,
      rootId,
      perfMetrics: debugMode ? PERF_METRICS : undefined
    };
  }
  
  // 执行DOM树构建
  return buildDomTreeInternal(document.documentElement);
} 