import { tool } from 'ai';
import { z } from 'zod';


export const getPageSchema = tool({
  description: 'Get the HTML content and optionally a screenshot of the current webpage',
  parameters: z.object({
    // request_id is required as specified in dom_tools.py
    request_id: z.string().describe('A unique identifier for this request'),
    // options is optional as specified in dom_tools.py
    options: z.object({
      screenshot: z.object({
        // enabled is required within screenshot object as specified in dom_tools.py
        enabled: z.boolean().default(true).describe('Whether to capture a screenshot'),
        // type and quality are optional
        type: z.enum(['window', 'screen', 'application']).describe('Type of screenshot to capture'),
        quality: z.number().min(0).max(1).describe('Quality of the screenshot')
      }).describe('Screenshot capture options')
    }).describe('Optional parameters for the request')
  })
}); 

export const domOperator = tool({
  description: 'Execute a series of DOM operations on the webpage. After execution, it automatically returns the updated page state.',
  parameters: z.object({
    // current_state is required as specified in dom_tools.py
    current_state: z.object({
      // All fields in current_state are required
      evaluation_previous_goal: z.string().describe('Evaluation of previous actions (Success|Failed|Unknown) with explanation'),
      memory: z.string().describe('Description of what has been done and needs to be remembered'),
      next_goal: z.string().describe('Description of what needs to be done next')
    }).describe('Current state information and evaluation'),
    // actions is required as specified in dom_tools.py
    actions: z.array(
      z.object({
        // Each action type is optional, but if specified, it must have the required fields
        click_element: z.object({
          // index is required for click_element
          index: z.number().describe('The index of the element to click')
        }).optional(),
        input_text: z.object({
          // Both index and text are required for input_text
          index: z.number().describe('The index of the input element'),
          text: z.string().describe('The text to enter')
        }).optional(),
        send_keys: z.object({
          // Both index and keys are required for send_keys
          index: z.number().describe('The index of the target element'),
          keys: z.string().describe('The keys to send (e.g., "Enter", "Control+A")')
        }).optional(),
        scroll_to_text: z.object({
          // text is required for scroll_to_text
          text: z.string().describe('The text to scroll to')
        }).optional(),
        select_dropdown_option: z.object({
          // index and option_index are required for select_dropdown_option
          index: z.number().describe('The index of the select element'),
          option_index: z.number().describe('The index of the option').or(z.string().describe('The value or text of the option'))
        }).optional(),
        get_dropdown_options: z.object({
          // index is required for get_dropdown_options
          index: z.number().describe('The index of the select element')
        }).optional(),
        scroll: z.object({
          // amount is optional for scroll
          amount: z.number().optional().describe('Optional: pixels to scroll, if not provided scrolls one page')
        }).optional()
      })
    ).describe('List of DOM operations to execute'),
    // auto_get_document is optional with a default value of true
    auto_get_document: z.boolean().default(true).describe('Whether to automatically get the document after operations')
  })
}); 