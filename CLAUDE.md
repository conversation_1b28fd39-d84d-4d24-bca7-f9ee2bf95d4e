# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

CyberBot is a modern AI chat system built with Next.js that supports multiple LLMs (GPT-4<PERSON>, <PERSON>, <PERSON>), MCP tool integration, voice streaming, and embeddable chat widgets. It includes enterprise features like multi-tenant support, OAuth authentication, and industry-specific solutions (WMS, FMS, TMS).

## Essential Commands

### Development
```bash
npm run dev                # Start development server (port 3000)
npm run start-all          # Start MCP server and dev server together
npm run build              # Build for production
npm run lint               # Run ESLint
```

### Testing MCP Tools
```bash
npm run test-mcp           # Test single MCP server
npm run test-multi-mcp     # Test multiple MCP servers
```

### Building Embeds
```bash
npm run build:embed                    # Build generic embed
npm run build:embed:unis:production    # Build UNIS production embed
npm run build:embed:wms:staging       # Build WMS staging embed
```

### Database & Reports
```bash
npm run verify-lancedb     # Verify vector database setup
npm run generate-report    # Generate chat reports
```

## Architecture Overview

### Core Systems

1. **MCP Tool System**
   - Tools are loaded from external MCP servers defined in `mcp.json`
   - Base tool class: `src/tools/baseTool.ts`
   - Tool registry: `src/tools/toolRegistry.ts`
   - MCP integration: `src/tools/mcpTools.ts`
   - Tools are cached for 28 days and preloaded on startup

2. **Authentication Flow**
   - OAuth2 with enterprise IAM system
   - Token management in `src/services/authService.ts`
   - Protected routes using `AuthContext`
   - Automatic token refresh on expiry

3. **Storage Architecture**
   - Interface-based storage in `src/services/storage/`
   - Supports local file system and S3
   - Chat history stored as JSON files per user
   - SQLite databases auto-created: `userPrefs.db`, `vector_store.db`, `memory.db`

4. **Real-time Voice**
   - OpenAI Realtime API integration
   - Hook: `src/hooks/useRealtimeVoice.ts`
   - Hybrid agent tool for voice-enabled AI

### API Route Pattern

All API routes follow this pattern:
```typescript
import { withRequestContext } from '@/utils/apiWrapper';
import { getUserIdFromRequest } from '@/utils/getUserFromRequest';

export const POST = withRequestContext(async (req: NextRequest, requestId: string) => {
  const userId = await getUserIdFromRequest(req);
  const body = await req.json();
  // Process request
  return NextResponse.json(data);
});
```

### Environment Variables

Required environment variables (see `.env.example`):
- AI API keys: `OPENAI_API_KEY`, `ANTHROPIC_API_KEY`, `GOOGLE_GENERATIVE_AI_API_KEY`
- IAM: `NEXT_PUBLIC_IAM_CLIENT_ID`, `IAM_CLIENT_SECRET`
- Storage: `STORAGE_TYPE` (local or s3)
- MCP: `siteToolConfig` JSON configuration

### Code Conventions

1. **TypeScript**: Strict mode enabled, explicit type annotations required
2. **Components**: Functional components with hooks, use `'use client'` directive
3. **Error Handling**: Use `withRequestContext` wrapper for API routes
4. **State Management**: Context API for global state, no Redux
5. **Styling**: Tailwind CSS only, no CSS modules

### Key File Locations

- API routes: `/src/app/api/*/route.ts`
- Components: `/src/app/components/`
- Tools: `/src/tools/`
- MCP servers: `/mcp_server_example/`
- Embed configs: `/config/embed/`

### Development Tips

1. When adding new tools, register them in `toolRegistry.ts`
2. For new API routes, always use `withRequestContext` wrapper
3. Check authentication with `getUserIdFromRequest` in protected routes
4. Use `apiClient` from `utils/apiClient.ts` for frontend API calls
5. Test MCP tools with `npm run test-mcp` before integration
6. For embed changes, test with site-specific build commands