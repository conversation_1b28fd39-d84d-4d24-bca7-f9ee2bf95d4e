'use client';

import * as React from 'react';
import { format, parse } from 'date-fns';
import { Calendar as CalendarIcon, X, ChevronLeft, ChevronRight } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { DayPicker } from 'react-day-picker';
import "react-day-picker/dist/style.css";
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';


interface DatePickerProps {
  date: string | undefined;
  setDate: (date: string | undefined) => void;
  className?: string;
  placeholder?: string;
  label?: string;
  name?: string;
}

export function DatePicker({
  date,
  setDate,
  className,
  placeholder = 'Select date...',
  label,
  name,
}: DatePickerProps) {
  // 将字符串日期转换为 Date 对象用于显示
  // 支持两种格式：YYYY-MM-DD 和 YYYY-MM-DD HH:MM:SS
  const parseDate = (dateStr: string) => {
    if (dateStr.includes(' ')) {
      // 如果包含时间部分，只取日期部分
      return parse(dateStr.split(' ')[0], 'yyyy-MM-dd', new Date());
    } else {
      return parse(dateStr, 'yyyy-MM-dd', new Date());
    }
  };
  
  const dateObj = date ? parseDate(date) : undefined;
  
  return (
    <div className={className}>
      {label && (
        <label className="block text-sm font-medium mb-2 text-item-gray-300 font-mono">{label}</label>
      )}
      <Popover>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            className={cn(
              'w-full justify-between bg-item-bg-card border-item-purple/30 text-white hover:bg-item-bg-hover hover:text-white font-mono',
              !date && 'text-item-gray-500',
              'focus:ring-2 focus:ring-item-purple focus:border-item-purple transition-all duration-200',
              'min-w-0'
            )}
          >
            <span className="truncate flex-1 text-left">
              {date ? format(dateObj!, 'MMM dd, yyyy') : <span>{placeholder}</span>}
            </span>
            <div className="flex items-center flex-shrink-0 ml-2">
              {date && (
                <div
                  onClick={(e) => {
                    e.stopPropagation();
                    e.preventDefault();
                    setDate(undefined);
                  }}
                  className="mr-2 cursor-pointer"
                >
                  <X className="h-4 w-4 text-item-gray-400 hover:text-item-orange transition-colors duration-200" />
                </div>
              )}
              <CalendarIcon className="h-5 w-5 text-item-purple" />
            </div>
          </Button>
        </PopoverTrigger>
        <PopoverContent
          className="w-auto p-0 bg-item-bg-card/95 backdrop-blur-sm border border-item-purple/50 text-white shadow-xl shadow-item-purple/30 z-[9999]"
          align="center"
          sideOffset={8}
        >
          <div className="p-3">
            <DayPicker
              mode="single"
              defaultMonth={dateObj}
              selected={dateObj}
              onSelect={(day) => {
                console.log('DayPicker onSelect:', day);
                if (day instanceof Date) {
                  // 将 Date 对象转换为 yyyy-MM-dd 00:00:00 格式的字符串
                  const dateString = format(day, 'yyyy-MM-dd 00:00:00');
                  console.log('Setting date to:', dateString);
                  setDate(dateString);
                } else {
                  setDate(undefined); // 处理 undefined 情况
                }
              }}
              classNames={{
                root: 'rdp-root',
                caption: 'rdp-caption flex items-center justify-center py-2 mb-4 relative',
                caption_label: 'rdp-caption_label text-item-purple font-medium text-lg mx-auto',
                nav: 'rdp-nav flex items-center justify-between absolute top-2 w-full px-2 pointer-events-auto',
                nav_button: 'rdp-nav_button inline-flex justify-center items-center w-9 h-9 p-0 rounded-full text-item-purple hover:bg-item-purple/20 hover:text-item-orange focus:outline-none focus-visible:ring focus-visible:ring-item-purple transition-all duration-200',
                nav_button_previous: 'rdp-nav_button_previous absolute left-1 hover:shadow-md',
                nav_button_next: 'rdp-nav_button_next absolute right-1 hover:shadow-md',
                table: 'rdp-table w-full border-collapse',
                head: 'rdp-head',
                head_row: 'rdp-head_row',
                head_cell: 'rdp-head_cell text-item-orange font-medium text-center py-2',
                tbody: 'rdp-tbody',
                row: 'rdp-row',
                cell: 'rdp-cell p-0 relative',
                day: 'rdp-day w-10 h-10 text-center text-white hover:bg-item-purple/20 rounded-full focus:outline-none focus-visible:ring focus-visible:ring-item-purple transition-all duration-200',
                day_selected: 'rdp-day_selected bg-item-purple text-white hover:bg-item-purple/80 font-medium shadow-md',
                day_today: 'rdp-day_today border border-item-purple font-medium text-item-purple',
                day_outside: 'rdp-day_outside text-item-gray-500 opacity-50',
                day_disabled: 'rdp-day_disabled text-item-gray-400 opacity-40',
                day_hidden: 'rdp-day_hidden invisible',
                button_reset: 'rdp-button_reset'
              }}
              styles={{
                caption_label: { textAlign: 'center', position: 'relative', zIndex: 1 },
                nav: { position: 'absolute', top: '8px', left: 0, right: 0, zIndex: 2 },
                months: { display: 'flex', justifyContent: 'center' }
              }}
              components={{
                Chevron: ({ orientation }) =>
                  orientation === 'left'
                    ? <ChevronLeft className="h-5 w-5 text-item-purple stroke-[2.5]" />
                    : <ChevronRight className="h-5 w-5 text-item-purple stroke-[2.5]" />
              }}
            />
            <div className="pt-3 border-t border-item-gray-700/50 mt-2">
              <Button
                variant="ghost"
                className="w-full text-sm text-item-purple hover:bg-item-purple/20 hover:text-white font-medium transition-all duration-200"
                onClick={() => {
                  // 获取今天的日期并转换为 yyyy-MM-dd 00:00:00 格式
                  const today = new Date();
                  const todayString = format(today, 'yyyy-MM-dd 00:00:00');
                  console.log('Setting date to today:', todayString);
                  setDate(todayString);
                }}
              >
                Today
              </Button>
            </div>
          </div>
        </PopoverContent>
      </Popover>
      {name && date && (
        <input
          type="hidden"
          name={name}
          value={date}
        />
      )}
    </div>
  );
}
