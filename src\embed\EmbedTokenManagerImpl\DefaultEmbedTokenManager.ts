import { EmbedTokenManager } from '../EmbedTokenManager';

// ================= TokenProvider 抽象与实现 =================
interface TokenProvider {
  getToken(): string | null;
}

class LocalStorageTokenProvider implements TokenProvider {
  private keys = [
    'auth_token', 'iam_access_token', 'third_party_token', 'token', 'accessToken', 'access_token', 'jwt'
  ];
  getToken() {
    for (const key of this.keys) {
      const token = localStorage.getItem(key);
      if (token) return token;
    }
    return null;
  }
}

class CookieTokenProvider implements TokenProvider {
  private keys = ['access_token', 'token', 'jwt'];
  getToken() {
    for (const key of this.keys) {
      const match = document.cookie.match(new RegExp('(?:^|; )' + key + '=([^;]*)'));
      if (match) return match[1];
    }
    return null;
  }
}

// 未来可扩展更多 TokenProvider

const tokenProviders: TokenProvider[] = [
  new LocalStorageTokenProvider(),
  new CookieTokenProvider(),
  // 未来可插入更多
];

function getTokenFromProviders(): string | null {
  for (const provider of tokenProviders) {
    const token = provider.getToken();
    if (token) return token;
  }
  return null;
}

export class DefaultEmbedTokenManager implements EmbedTokenManager {
  getRefreshToken(): string | null {
    return localStorage.getItem('iam_refresh_token');
  }

  getCurrentToken(): string | null {
    return getTokenFromProviders();
  }

  removeToken(): void {
    localStorage.removeItem('iam_access_token');
    localStorage.removeItem('access_token');
    localStorage.removeItem('token');
    localStorage.removeItem('iam_refresh_token');
    localStorage.removeItem('refresh_token');
    localStorage.removeItem('iam_id_token');
  }

  async refreshToken(): Promise<string | null> {
    const refreshToken = this.getRefreshToken();
    if (!refreshToken) return null;
    try {
      const response = await fetch('/api/auth/refresh', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ refreshToken })
      });
      if (!response.ok) return null;
      const tokens = await response.json();
      if (tokens.access_token) {
        this.updateClientToken(tokens.access_token);
        if (tokens.refresh_token) {
          localStorage.setItem('iam_refresh_token', tokens.refresh_token);
        }
        if (tokens.id_token) {
          localStorage.setItem('iam_id_token', tokens.id_token);
        }
        return tokens.access_token;
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  updateClientToken(token: string) {
    localStorage.setItem('iam_access_token', token);
    localStorage.setItem('access_token', token);
    localStorage.setItem('token', token);
  }
} 