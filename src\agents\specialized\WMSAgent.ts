import { BaseAgent, AgentConfig, AgentExecutionContext } from '../base/BaseAgent';
import { CoreTool } from 'ai';
import { getSpecificServersMcpTools, getSpecificServersMcpPrompts } from '@/tools/mcpTools';
import { wmsSystemPrompt } from '@/prompts/agents/wmsPrompt';

export class WMSAgent extends BaseAgent {
  constructor() {
    const config: AgentConfig = {
      id: 'wms-agent',
      name: 'WMS Specialist',
      description: 'Specialized agent for Warehouse Management System operations. Handles tasks like creating shipments, managing inventory, and warehouse operations.',
      tools: ['find_wms_api', 'call_wms_api'],
      systemPrompt: async () => {
        // 组合静态提示词和MCP服务器的动态提示词
        let prompt = wmsSystemPrompt;
        
        try {
          // 获取WMS MCP服务器的提示词
          const mcpPrompts = await getSpecificServersMcpPrompts(['wms']);
          if (mcpPrompts) {
            prompt += `\n\n## Additional WMS Context from MCP Server\n${mcpPrompts}`;
          }
        } catch (error) {
          console.error('[WMSAgent] Error loading MCP prompts:', error);
        }
        
        return prompt;
      }
    };
    
    super(config);
  }
  
  // 加载WMS专业工具
  protected async loadSpecializedTools(): Promise<Record<string, CoreTool>> {
    console.log('[WMSAgent] Loading WMS tools from MCP server...');
    
    try {
      // 从WMS MCP服务器加载工具
      const mcpTools = await getSpecificServersMcpTools(['wms']);
      
      // 记录加载的工具
      const toolNames = Object.keys(mcpTools);
      console.log(`[WMSAgent] Available MCP tools: ${toolNames.join(', ')}`);
      
      return mcpTools;
    } catch (error) {
      console.error('[WMSAgent] Error loading MCP tools:', error);
      return {};
    }
  }
  
}