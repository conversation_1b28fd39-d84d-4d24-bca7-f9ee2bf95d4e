import aiohttp
import json
import logging
import base64
import os
from typing import Dict, Any, List, Union, Optional
from fastapi import WebSocket
from pathlib import Path
from config import WISE_BASE_URL, WISE_AUTH
from tools.portal_api_finder import PortalApiFinder
from .base import BaseTool

logger = logging.getLogger(__name__)

# 添加一个辅助函数读取提示词
def get_portal_prompt():
    """读取Portal提示词"""
    parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    portal_prompt_path = os.path.join(parent_dir, "prompts", "portal_prompt.py")
    
    if os.path.exists(portal_prompt_path):
        with open(portal_prompt_path, 'r', encoding='utf-8') as f:
            content = f.read()
            # 提取PORTAL_TOOLS_PROMPT变量的值
            if "PORTAL_TOOLS_PROMPT = '''" in content:
                start_index = content.find("PORTAL_TOOLS_PROMPT = '''") + len("PORTAL_TOOLS_PROMPT = '''")
                end_index = content.find("'''", start_index)
                return content[start_index:end_index]
    logger.error(f"Error reading Portal prompt: {e}")
    return """## Guide for Portal Tools

Portal Tools provides functionality for managing portal operations.

Available tools:
- find_portal_api - Search for Portal API endpoints
- call_portal_api - Call Portal API endpoints
"""

async def on_request_start(session, trace_config_ctx, params):
    logger.info(f"Starting request:")
    logger.info(f"Sending request: {params.method} {params.url}")
    logger.info(f"Headers: {params.headers}")
    logger.info(f"Params: {params}")
    if hasattr(params, 'data'):
        logger.info(f"Data: {params.data}")
    if hasattr(params, 'json'):
        logger.info(f"JSON: {params.json}")

async def on_request_end(session, trace_config_ctx, params):
    logger.info(f"Ending request: {params.method} {params.url}")
    logger.info(f"Response status: {params.response.status}")


class PortalTools(BaseTool):
    def __init__(self, websocket: Optional[WebSocket] = None):
        self.websocket = websocket
        self.base_url = WISE_BASE_URL
        # 生成Basic认证头
        auth_str = f"{WISE_AUTH['username']}:{WISE_AUTH['password']}"
        self.auth_header = {
            'Authorization': f'Basic {base64.b64encode(auth_str.encode()).decode()}',
            'Content-Type': 'application/json'
        }
        try:
            self.api_finder = PortalApiFinder.get_instance()
            logger.info("Successfully initialized Portal API Finder")
        except Exception as e:
            logger.error(f"Failed to initialize Portal API Finder: {str(e)}")
            
    def update_context(self, context: Dict[str, Any]):
        """Update Portal tool context"""
        self.context = context
        logger.info(f"Updated Portal context")
        

    async def _get_facility_url(self) -> str:
        """从客户端获取最新的 facility URL"""
        try:
            if not self.websocket:
                return self.base_url

            # 发送获取 localStorage 的请求
            request_id = str(uuid.uuid4())
            message = {
                "type": "get_local_storage",
                "data": {
                    "request_id": request_id
                }
            }
            
            await self.websocket.send_json(message)
            response = await self.websocket.receive_json()
            
            if response.get("type") == "local_storage_response":
                if "error" in response.get("data", {}):
                    logger.error(f"Error getting localStorage: {response['data']['error']}")
                    return self.base_url
                    
                # 从 localStorage 数据中获取 facility 信息
                storage_data = response.get("data", {}).get("data", {})
                facility_info = storage_data.get("cp-selectedFacility")
                if facility_info:
                    try:
                        facility_data = json.loads(facility_info)
                        access_url = facility_data.get("accessUrl")
                        if access_url:
                            logger.info(f"Got facility URL from localStorage: {access_url}")
                            return access_url
                    except json.JSONDecodeError as e:
                        logger.error(f"Failed to parse facility info: {str(e)}")
            
            return self.base_url
            
        except Exception as e:
            logger.error(f"Error getting facility URL: {str(e)}")
            return self.base_url
        
    async def call_portal_api(self, path: str, method: str, params: Union[Dict[str, Any], List[Any]] = None, headers: Dict[str, Any] = None) -> Dict[str, Any]:
        """Call Wise API with authentication

        Args:
            path: API path
            method: HTTP method (GET, POST, PUT, DELETE)
            params: Request parameters (for POST/PUT methods)

        Returns:
            Dict: API response
        """
        try:
            # 获取最新的 facility URL
            base_url = await self._get_facility_url()
            url = f"{base_url}{path}"
            
            # Log request details
            logger.info(f"Calling Wise API:")
            logger.info(f"URL: {url}")
            logger.info(f"Method: {method}")
            logger.info(f"Headers: {json.dumps(self.auth_header, indent=2)}")
            if params:
                logger.info(f"Request Body: {json.dumps(params, indent=2)}")
            
            async with aiohttp.ClientSession() as session:
                async with session.request(
                    method,
                    url,
                    headers=self.auth_header,
                    json=params
                ) as response:
                    response_json = await response.json()
                    logger.info(f"Wise API Response Status: {response.status}")
                    logger.info(f"Wise API Response: {response_json}")
                    
                    return {
                        "content": [{
                            "content": json.dumps({
                                "success": True,
                                "status_code": response.status,
                                "data": response_json
                            })
                        }]
                    }
                    
        except Exception as e:
            logger.error(f"Wise API call failed: {str(e)}")
            return {
                "content": [{
                    "content": json.dumps({
                        "success": False,
                        "error": str(e),
                        "message": "Wise API call failed"
                    })
                }]
            }
            
    def _clean_response(self, data: Any) -> Any:
        """清理响应数据，移除敏感信息"""
        if isinstance(data, dict):
            return {k: self._clean_response(v) for k, v in data.items() 
                   if k not in ['password', 'token', 'secret']}
        elif isinstance(data, list):
            return [self._clean_response(item) for item in data]
        return data

    async def find_portal_api(self, query: str, top_k: int = 5) -> Dict[str, Any]:
        """
        Find the most relevant Portal APIs based on the query

        Args:
            query: User's search query
            top_k: Number of top relevant APIs to return

        Returns:
            Dict: Response in the format expected by Claude AI
        """
        try:
            if not self.api_finder:
                logger.error("Portal API Finder is not initialized")
                raise ValueError("Portal API Finder is not initialized")

            logger.info(f"Searching for Portal APIs with query: '{query}', top_k: {top_k}")
            
            # 检查API文件是否存在
            if hasattr(self.api_finder, 'api_file_paths'):
                for file_path in self.api_finder.api_file_paths:
                    logger.info(f"Checking API file: {file_path}, exists: {os.path.exists(file_path)}")
            
            results = self.api_finder.search_apis(query, top_k=top_k)
            logger.info(f"Search complete, found {len(results)} results")
            
            # Format results for better presentation
            formatted_results = []
            for result in results:
                api_info = {
                    'path': result['path'],
                    'method': result['method'],
                    'similarity': result['similarity'],
                    'summary': result['details']['summary'],
                    'description': result['details']['description'],
                    'parameters': {
                        'headers': result['details']['parameters']['headers'],
                        'path': result['details']['parameters']['path'],
                        'query': result['details']['parameters']['query'],
                        'body': result['details']['parameters']['body']
                    }
                }
                formatted_results.append(api_info)
                logger.info(f"API found: {result['method']} {result['path']} (similarity: {result['similarity']:.4f})")

            # 返回简化的格式，移除额外的嵌套层级
            return {
                "success": True,
                "apis": formatted_results,
                "message": f"Found {len(formatted_results)} relevant APIs"
            }

        except Exception as e:
            logger.error(f"Error finding Portal APIs: {str(e)}")
            logger.exception("Exception details:")
            return {
                "success": False,
                "error": str(e),
                "message": "Failed to find relevant APIs",
                "apis": []
            }

    def get_tools(self) -> list:
        """Return the available Portal tools and their schemas"""
        return [
            {
                "name": "call_portal_api",
                "description": "Call Portal API endpoints. You must first use find_portal_api to discover the correct API path before calling this.",
                "input_schema": {
                    "type": "object",
                    "properties": {
                        "path": {
                            "type": "string",
                            "description": "API path, must be one defined in the documentation"
                        },
                        "method": {
                            "type": "string",
                            "description": "HTTP method (GET, POST, PUT, DELETE)",
                            "enum": ["GET", "POST", "PUT", "DELETE"]
                        },
                        "params": {
                            "oneOf": [
                                {
                                    "type": "object",
                                    "description": "Request parameters as object, for APIs that expect object parameters"
                                },
                                {
                                    "type": "array",
                                    "description": "Request parameters as array, for APIs that expect array parameters"
                                }
                            ],
                            "description": "Request parameters, must match the API documentation format. Can be either an object or array depending on the API requirements."
                        },
                        "headers": {
                            "type": "object",
                            "description": "Required request headers, must for example: {'Authorization': 'Bearer YOUR_TOKEN'}"
                        }
                    },
                    "required": ["path", "method"]
                }
            },
            {
                "name": "find_portal_api",
                "description": "Search for relevant Portal APIs based on the query. Returns API information including path, method, parameters, etc.",
                "input_schema": {
                    "type": "object",
                    "properties": {
                        "query": {
                            "type": "string",
                            "description": "Search query describing the API functionality you're looking for"
                        },
                        "top_k": {
                            "type": "integer",
                            "description": "Number of most relevant APIs to return",
                            "default": 5,
                            "minimum": 1,
                            "maximum": 10
                        }
                    },
                    "required": ["query"]
                }
            }
        ]

    @classmethod
    def get_system_prompt(cls) -> str:
        return get_portal_prompt()