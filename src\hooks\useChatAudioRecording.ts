import { useRef, useCallback } from 'react';
import { nanoid } from 'nanoid';
import { log } from '@/utils/logger';
import { clientUserContextManager } from '@/utils/clientUserContext';

interface ChatAudioRecordingOptions {
  chatId?: string;
  onSaveComplete?: (audioUrl: string, originalFileName: string, audioInfo?: any) => void;
  onError?: (error: Error) => void;
}

export function useChatAudioRecording({
  chatId,
  onSaveComplete,
  onError
}: ChatAudioRecordingOptions = {}) {
  // Refs for MediaRecorder and audio chunks
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const recordedChunksRef = useRef<Blob[]>([]);
  const audioContextRef = useRef<AudioContext | null>(null);
  const isRecordingRef = useRef(false);
  const recordingStartTimeRef = useRef<number>(0);
  const currentStreamRef = useRef<MediaStream | null>(null);
  
  /**
   * Starts recording by combining remote stream with microphone audio
   */
  const startRecording = useCallback(async (remoteStream?: MediaStream) => {
    try {
      if (isRecordingRef.current) {
        log.warn('Recording already in progress, skipping start', undefined, 'useChatAudioRecording');
        return;
      }

      if (currentStreamRef.current === remoteStream) {
        log.warn('Same stream already being recorded, skipping start', undefined, 'useChatAudioRecording');
        return;
      }

      if (mediaRecorderRef.current && mediaRecorderRef.current.state !== 'inactive') {
        mediaRecorderRef.current.stop();
      }
      if (audioContextRef.current) {
        audioContextRef.current.close();
      }
      
      recordedChunksRef.current = [];
      currentStreamRef.current = remoteStream || null;
      
      let micStream: MediaStream;
      try {
        micStream = await navigator.mediaDevices.getUserMedia({ audio: true });
      } catch (err) {
        log.error('Error getting microphone stream:', err, 'useChatAudioRecording');
        micStream = new MediaStream();
      }

      const audioContext = new AudioContext();
      audioContextRef.current = audioContext;
      const destination = audioContext.createMediaStreamDestination();

      if (remoteStream) {
        try {
          const remoteSource = audioContext.createMediaStreamSource(remoteStream);
          remoteSource.connect(destination);
        } catch (err) {
          log.error('Error connecting remote stream:', err, 'useChatAudioRecording');
        }
      }

      if (micStream.getAudioTracks().length > 0) {
        try {
          const micSource = audioContext.createMediaStreamSource(micStream);
          micSource.connect(destination);
        } catch (err) {
          log.error('Error connecting microphone stream:', err, 'useChatAudioRecording');
        }
      }

      const options = { mimeType: 'audio/webm' };
      const mediaRecorder = new MediaRecorder(destination.stream, options);
      
      mediaRecorder.ondataavailable = (event: BlobEvent) => {
        if (event.data && event.data.size > 0) {
          recordedChunksRef.current.push(event.data);
        }
      };

      mediaRecorder.onerror = (event) => {
        log.error('MediaRecorder error:', event, 'useChatAudioRecording');
        onError?.(new Error('Recording failed'));
      };

      mediaRecorder.start(1000);
      mediaRecorderRef.current = mediaRecorder;
      isRecordingRef.current = true;
      recordingStartTimeRef.current = Date.now();
      
      log.info('Audio recording started', undefined, 'useChatAudioRecording');
    } catch (err) {
      log.error('Error starting recording:', err, 'useChatAudioRecording');
      onError?.(err as Error);
    }
  }, [onError]);

  /**
   * Stops the recording
   */
  const stopRecording = useCallback(() => {
    if (mediaRecorderRef.current && mediaRecorderRef.current.state !== 'inactive') {
      mediaRecorderRef.current.stop();
      isRecordingRef.current = false;
      currentStreamRef.current = null;
      log.info('Audio recording stopped', undefined, 'useChatAudioRecording');
    }

    if (audioContextRef.current) {
      audioContextRef.current.close();
      audioContextRef.current = null;
    }
  }, []);

  /**
   * Saves the recording to the server
   */
  const saveRecording = useCallback(async (): Promise<{ audioUrl: string; fileName: string } | null> => {
    try {
      if (isRecordingRef.current) {
        stopRecording();
        await new Promise(resolve => setTimeout(resolve, 500));
      }

      if (recordedChunksRef.current.length === 0) {
        log.warn('No recorded chunks to save', undefined, 'useChatAudioRecording');
        return null;
      }

      const duration = Date.now() - recordingStartTimeRef.current;
      
      const webmBlob = new Blob(recordedChunksRef.current, { type: 'audio/webm' });
      
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const fileName = `voice_recording_${timestamp}.webm`;
      
      const formData = new FormData();
      formData.append('audio', webmBlob, fileName);
      formData.append('chatId', chatId || 'unknown');
      formData.append('duration', duration.toString());
      
      const authHeaders = clientUserContextManager.getAuthHeaders();
      
      const { 'Content-Type': _, ...headersWithoutContentType } = authHeaders;
      
      const response = await fetch('/api/chat-history/audio', {
        method: 'POST',
        body: formData,
        headers: {
          ...headersWithoutContentType,
        }
      });

      if (!response.ok) {
        throw new Error(`Upload failed: ${response.statusText}`);
      }

      const result = await response.json();
      const { audioUrl, audioInfo } = result;
      
      log.info('Audio recording saved successfully', { audioUrl, fileName, audioInfo }, 'useChatAudioRecording');
      
      recordedChunksRef.current = [];
      
      onSaveComplete?.(audioUrl, fileName, audioInfo);
      
      return { audioUrl, fileName };
    } catch (err) {
      log.error('Error saving recording:', err, 'useChatAudioRecording');
      onError?.(err as Error);
      return null;
    }
  }, [chatId, stopRecording, onSaveComplete, onError]);

  /**
   * Gets the current recording duration in seconds
   */
  const getRecordingDuration = useCallback(() => {
    if (!isRecordingRef.current || recordingStartTimeRef.current === 0) {
      return 0;
    }
    return Math.floor((Date.now() - recordingStartTimeRef.current) / 1000);
  }, []);

  /**
   * Checks if currently recording
   */
  const isRecording = useCallback(() => {
    return isRecordingRef.current;
  }, []);

  return {
    startRecording,
    stopRecording,
    saveRecording,
    getRecordingDuration,
    isRecording,
  };
}