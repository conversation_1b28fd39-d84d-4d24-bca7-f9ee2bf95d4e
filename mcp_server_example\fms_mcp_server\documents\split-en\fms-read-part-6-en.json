{"openapi": "3.0.1", "info": {"title": "FMS", "description": "", "version": "1.0.0"}, "paths": {"/fms-platform-order/ShipmentOrderActualFreight/PushPalletToDockApp": {"get": {"summary": "/fms-platform-order/ShipmentOrderActualFreight/PushPalletToDockApp", "deprecated": false, "description": "", "tags": ["ShipmentOrderActualFreight"], "parameters": [{"name": "orderNo", "in": "query", "description": "", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order/actual-freight/GetOrderPalletSummariesByOrderNosAsync": {"post": {"summary": "Delete actual freight", "deprecated": false, "description": "", "tags": ["ShipmentOrderActualFreight"], "parameters": [], "requestBody": {"content": {"application/json-patch+json": {"schema": {"type": "array", "items": {"type": "string"}}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ShipmentOrderPalletSummaryDto"}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order/estimate-freight/{shipmentOrderNo}": {"get": {"summary": "GetEstimateFreight", "deprecated": false, "description": "", "tags": ["ShipmentOrderEstimateFreight"], "parameters": [{"name": "shipmentOrderNo", "in": "path", "description": "Freight Order Number", "required": true, "example": "", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ShipmentOrderEstimateFreightResultDto"}}}, "headers": {}}}, "security": [{"apikey-header-fms-token": []}]}}, "/fms-platform-order/ShipmentOrderTrack/PackageTracks": {"get": {"summary": "/fms-platform-order/ShipmentOrderTrack/PackageTracks", "deprecated": false, "description": "", "tags": ["ShipmentOrderTrack"], "parameters": [{"name": "packageNo", "in": "query", "description": "", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ShipmentOrderTrackRecordDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order/dockcheck/get-dockcheck-pallets": {"post": {"summary": "/fms-platform-order/dockcheck/get-dockcheck-pallets", "deprecated": false, "description": "", "tags": ["DockCheck"], "parameters": [], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/DockCheckPalletInputDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/DockCheckPalletOutputDtoPagedResultDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order/rate-shopping/get-rate-order": {"post": {"summary": "Get rate order\r\nGet Quote Order", "deprecated": false, "description": "", "tags": ["OrderCarrierRateShopping"], "parameters": [], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/CarrierRateShoppingRequestDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/CarrierQuoteSearchResponseDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order/rate-shopping/search-quotes": {"post": {"summary": "Search carrier quotes\r\nSearch carrier quotes", "deprecated": false, "description": "", "tags": ["OrderCarrierRateShopping"], "parameters": [], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/CarrierQuoteSearchRequestDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/CarrierQuoteSearchResponseDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order/rate-shopping/export-rate-shopping-excel": {"get": {"summary": "Export rate shopping data to Excel\r\nExport quotation shopping data to Excel", "deprecated": false, "description": "", "tags": ["OrderCarrierRateShopping"], "parameters": [{"name": "batch_no", "in": "query", "description": "", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/public-location/current-company-terminals": {"get": {"summary": "AI: Get Terminals under the Company to which the current user belongs\r\nforeign", "deprecated": false, "description": "", "tags": ["PublicLocation"], "parameters": [{"name": "terminalType", "in": "query", "description": "", "required": false, "schema": {"type": "integer", "format": "int32", "default": 0}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StringFmsSelectListItemListResultDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/public-location/current-company-terminals-sort": {"get": {"summary": "AI: Get the Terminals under the Company to which the current user belongs and sort by type and name\r\nforeign", "deprecated": false, "description": "", "tags": ["PublicLocation"], "parameters": [{"name": "terminalType", "in": "query", "description": "", "required": false, "schema": {"type": "integer", "format": "int32", "default": 0}}], "responses": {"200": {"description": "Success", "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/PublicLocation/GetTerminalListByCompany": {"get": {"summary": "AI: Query the corresponding terminal address details based on terminal code", "deprecated": false, "description": "", "tags": ["PublicLocation"], "parameters": [{"name": "companyCode", "in": "query", "description": "", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TerminalInfoByCompany"}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/PublicLocation/GetPolygonByTerminalCode": {"get": {"summary": "Get all Polygon lists under Terminal", "deprecated": false, "description": "", "tags": ["PublicLocation"], "parameters": [{"name": "terminalCode", "in": "query", "description": "", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PolygonListByTerminalResponse"}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/PublicLocation/GetDistrictList": {"get": {"summary": "AI: Query the administrative division list", "deprecated": false, "description": "", "tags": ["PublicLocation"], "parameters": [{"name": "countryCode", "in": "query", "description": "", "required": false, "schema": {"type": "string"}}, {"name": "keywords", "in": "query", "description": "", "required": false, "schema": {"type": "string"}}, {"name": "subdistrict", "in": "query", "description": "", "required": false, "schema": {"type": "string"}}, {"name": "filterDistrict", "in": "query", "description": "", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageDataDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/Test/Test1": {"get": {"summary": "/fms-platform-dispatch-management/Test/Test1", "deprecated": false, "description": "", "tags": ["Test"], "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "boolean"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/Test/Test2": {"get": {"summary": "/fms-platform-dispatch-management/Test/Test2", "deprecated": false, "description": "", "tags": ["Test"], "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "boolean"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/Test/TestTerminalDetail": {"get": {"summary": "/fms-platform-dispatch-management/Test/TestTerminalDetail", "deprecated": false, "description": "", "tags": ["Test"], "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/Test/TestTerminalListByCompany": {"get": {"summary": "/fms-platform-dispatch-management/Test/TestTerminalListByCompany", "deprecated": false, "description": "", "tags": ["Test"], "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/Test/TestPushTripToTMS": {"get": {"summary": "/fms-platform-dispatch-management/Test/TestPushTripToTMS", "deprecated": false, "description": "", "tags": ["Test"], "parameters": [{"name": "tripNo", "in": "query", "description": "", "required": false, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/TripDetail/GetTrip": {"get": {"summary": "AI: Get itinerary details", "deprecated": false, "description": "", "tags": ["TripDetail"], "parameters": [{"name": "tripNo", "in": "query", "description": "Itinerary number", "required": false, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TdTripOutput"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/TripDetail/GetTaskStatusInfo": {"get": {"summary": "Get the status information of the Task, including the starting terminal, the target terminal, the status value and the status description", "deprecated": false, "description": "", "tags": ["TripDetail"], "parameters": [{"name": "tripNo", "in": "query", "description": "Itinerary number", "required": false, "schema": {"type": "integer", "format": "int64"}}, {"name": "taskNo", "in": "query", "description": "Task number", "required": false, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TaskStatusInfoDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}}, "components": {"schemas": {"CarrierTypeEnum": {"enum": [2, 6, 7, 8], "type": "integer", "format": "int32"}, "CarrierQuoteDetailDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "order_no": {"type": "string", "nullable": true}, "batch_no": {"type": "string", "nullable": true}, "pro": {"type": "string", "nullable": true}, "pu": {"type": "string", "nullable": true}, "shipper": {"$ref": "#/components/schemas/LocationDto"}, "consignee": {"$ref": "#/components/schemas/LocationDto"}, "bill_name": {"type": "string", "nullable": true}, "plts": {"type": "integer", "format": "int32"}, "wgt": {"type": "number", "format": "double"}, "from_location": {"type": "string", "nullable": true}, "to_location": {"type": "string", "nullable": true}, "order_status": {"type": "string", "nullable": true}, "order_sub_status": {"type": "string", "nullable": true}, "status": {"type": "integer", "format": "int32", "nullable": true}, "is_applicable": {"type": "integer", "format": "int32", "nullable": true}, "lh_to_terminal": {"type": "string", "nullable": true}, "task_type": {"type": "string", "nullable": true}, "task_status": {"type": "string", "nullable": true}, "message": {"type": "string", "nullable": true}, "carrier_tariffs": {"type": "array", "items": {"$ref": "#/components/schemas/CarrierTariffDto"}, "nullable": true}}, "additionalProperties": false}, "CarrierQuoteSearchRequestDto": {"type": "object", "properties": {"order_nos": {"type": "string", "nullable": true}, "batch_no": {"type": "string", "nullable": true}, "carrier_code": {"type": "string", "nullable": true}, "status": {"type": "integer", "format": "int32"}, "is_applicable": {"type": "integer", "format": "int32"}, "start_date": {"type": "string", "format": "date-time", "nullable": true}, "end_date": {"type": "string", "format": "date-time", "nullable": true}, "current_page": {"maximum": 2147483647, "minimum": 1, "type": "integer", "format": "int32"}, "page_size": {"maximum": 100, "minimum": 1, "type": "integer", "format": "int32"}}, "additionalProperties": false}, "CarrierQuoteSearchResponseDto": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string", "nullable": true}, "applicable_count": {"type": "integer", "format": "int64"}, "non_applicable_count": {"type": "integer", "format": "int64"}, "total_count": {"type": "integer", "format": "int64"}, "is_applicable": {"type": "integer", "format": "int32"}, "current_page": {"type": "integer", "format": "int32"}, "page_size": {"type": "integer", "format": "int32"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/CarrierQuoteDetailDto"}, "nullable": true}}, "additionalProperties": false}, "CarrierRateShoppingRequestDto": {"type": "object", "properties": {"trip_no": {"type": "string", "nullable": true}, "pro_no": {"type": "array", "items": {"type": "string"}, "nullable": true}, "pu_no": {"type": "array", "items": {"type": "string"}, "nullable": true}, "do_no": {"type": "array", "items": {"type": "string"}, "nullable": true}, "lh_no": {"type": "string", "nullable": true}, "task_type": {"type": "string", "nullable": true}, "carrier": {"type": "array", "items": {"type": "string"}, "nullable": true}, "driver": {"type": "string", "nullable": true}, "from_location": {"type": "string", "nullable": true}, "to_location": {"type": "string", "nullable": true}}, "additionalProperties": false}, "DistrictDto": {"type": "object", "properties": {"code": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "level": {"type": "string", "nullable": true}, "sub_distincts": {"type": "array", "items": {"$ref": "#/components/schemas/DistrictDto"}, "nullable": true}}, "additionalProperties": false}, "CarrierTariffDto": {"type": "object", "properties": {"quote_id": {"type": "integer", "format": "int64"}, "budget": {"type": "string", "nullable": true}, "carrier_name": {"type": "string", "nullable": true}, "carrier_code": {"type": "string", "nullable": true}, "carrier_type": {"type": "string", "nullable": true}, "total_cost": {"type": "string", "nullable": true}, "transit_time": {"type": "string", "nullable": true}, "is_unis_transpotation": {"type": "integer", "format": "int32", "nullable": true}}, "additionalProperties": false}, "PolygonListByTerminalResponse": {"type": "object", "properties": {"polygon_code": {"type": "string", "nullable": true}, "polygon_name": {"type": "string", "nullable": true}}, "additionalProperties": false}, "DockCheckPalletInputDto": {"type": "object", "properties": {"dock_check_terminal": {"type": "string", "nullable": true}, "dock_check_time_from": {"type": "string", "format": "date-time", "nullable": true}, "dock_check_time_to": {"type": "string", "format": "date-time", "nullable": true}, "pro_no": {"type": "array", "items": {"type": "string"}, "nullable": true}, "delivery_appointment_time": {"type": "string", "nullable": true}, "shipper_terminal": {"type": "array", "items": {"type": "string"}, "nullable": true}, "consignee_terminal": {"type": "array", "items": {"type": "string"}, "nullable": true}, "location": {"type": "string", "nullable": true}, "sorting": {"type": "string", "nullable": true}, "page_number": {"maximum": 2147483647, "minimum": 0, "type": "integer", "format": "int32"}, "page_size": {"maximum": 2147483647, "minimum": 1, "type": "integer", "format": "int32"}}, "additionalProperties": false}, "DockCheckPalletOutputDto": {"type": "object", "properties": {"dock_check_task_no": {"type": "string", "nullable": true}, "terminal": {"type": "string", "nullable": true}, "trip_no": {"type": "string", "nullable": true}, "order_no": {"type": "string", "nullable": true}, "pro_no": {"type": "string", "nullable": true}, "pallet_no": {"type": "string", "nullable": true}, "order_status": {"type": "integer", "format": "int32"}, "order_sub_status": {"type": "integer", "format": "int32"}, "order_status_desc": {"$ref": "#/components/schemas/ShipmentOrderStatusEnum"}, "order_sub_status_desc": {"$ref": "#/components/schemas/ShipmentOrderSubStatusEnum"}, "location": {"type": "string", "nullable": true}, "delivery_appointment_time": {"type": "string", "nullable": true}, "pickup_complete_time": {"type": "string", "nullable": true}, "delivery_complete_time": {"type": "string", "nullable": true}, "pickup_complete_time_new": {"type": "string", "format": "date-time"}, "delivery_complete_time_new": {"type": "string", "format": "date-time"}, "bill_to_name": {"type": "string", "nullable": true}, "shipper_name": {"type": "string", "nullable": true}, "shipper_state": {"type": "string", "nullable": true}, "shipper_zip_code": {"type": "string", "nullable": true}, "consignee_name": {"type": "string", "nullable": true}, "consignee_city": {"type": "string", "nullable": true}, "consignee_state": {"type": "string", "nullable": true}, "consignee_zip_code": {"type": "string", "nullable": true}, "pallet_count": {"type": "integer", "format": "int32"}, "weight": {"type": "string", "nullable": true}, "shipper_terminal": {"type": "string", "nullable": true}, "consignee_terminal": {"type": "string", "nullable": true}, "dock_operate_name": {"type": "string", "nullable": true}, "dock_operate_time": {"type": "string", "nullable": true}}, "additionalProperties": false}, "DockCheckPalletOutputDtoPagedResultDto": {"type": "object", "properties": {"total_count": {"type": "integer", "format": "int64"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/DockCheckPalletOutputDto"}, "nullable": true}}, "additionalProperties": false}, "LocationDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "nullable": true}, "name": {"type": "string", "nullable": true}, "street": {"type": "string", "nullable": true}, "city": {"type": "string", "nullable": true}, "state": {"type": "string", "nullable": true}, "zipcode": {"type": "string", "nullable": true}, "terminal": {"type": "string", "nullable": true}}, "additionalProperties": false}, "PageDataDto": {"type": "object", "properties": {"current_page": {"type": "integer", "format": "int32"}, "total_page": {"type": "integer", "format": "int32"}, "total_count": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/DistrictDto"}, "nullable": true}}, "additionalProperties": false}, "TripAssignStatusEnum": {"enum": [101, 102, 103, 104, 105], "type": "integer", "format": "int32"}, "TripOptimizeStatusEnum": {"enum": ["NotOptimized", "Optimized"], "type": "string"}, "PalletInfoDto": {"type": "object", "properties": {"pallet_no": {"type": "string", "nullable": true}, "length": {"type": "number", "format": "double"}, "width": {"type": "number", "format": "double"}, "height": {"type": "number", "format": "double"}, "linear": {"type": "number", "format": "double"}, "freight_class": {"type": "number", "format": "double"}, "weight": {"type": "number", "format": "double"}, "volume": {"type": "number", "format": "double"}}, "additionalProperties": false}, "ShipmentOrderEstimateActualInfo": {"type": "object", "properties": {"actual_qty": {"type": "integer", "format": "int32"}, "weight": {"type": "number", "format": "double"}, "space": {"type": "number", "format": "double"}, "linear": {"type": "number", "format": "double"}, "inner_pack_qty": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "ShipmentOrderEstimateFreightInfoDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "code": {"type": "string", "nullable": true}, "customer_pro": {"type": "string", "nullable": true}, "declared_value": {"type": "number", "format": "double"}, "description": {"type": "string", "nullable": true}, "freight_class": {"type": "number", "format": "double"}, "height": {"type": "number", "format": "double"}, "length": {"type": "number", "format": "double"}, "nmfc": {"type": "string", "nullable": true}, "package_no": {"type": "string", "nullable": true}, "quantity": {"type": "integer", "format": "int32"}, "quantity_uom": {"type": "string", "nullable": true}, "shipment_order_no": {"type": "string", "nullable": true}, "linear_uom": {"type": "string", "nullable": true}, "stackable": {"type": "integer", "format": "int32"}, "volume": {"type": "number", "format": "double"}, "volume_uom": {"type": "string", "nullable": true}, "weight": {"type": "number", "format": "double"}, "weight_uom": {"type": "string", "nullable": true}, "width": {"type": "number", "format": "double"}, "sku": {"type": "string", "nullable": true}, "lot_no": {"type": "string", "nullable": true}, "linear": {"type": "number", "format": "double"}, "space": {"type": "number", "format": "double"}, "inner_pack_qty": {"type": "integer", "format": "int32", "nullable": true}, "inner_pack_uom": {"type": "string", "nullable": true}, "bol": {"type": "string", "nullable": true}, "load_no": {"type": "string", "nullable": true}, "sales_order": {"type": "string", "nullable": true}, "purchase_order": {"type": "string", "nullable": true}, "rma": {"type": "string", "nullable": true}, "reference": {"type": "string", "nullable": true}, "reference2": {"type": "string", "nullable": true}, "reference3": {"type": "string", "nullable": true}, "reference4": {"type": "string", "nullable": true}, "reference5": {"type": "string", "nullable": true}, "serial": {"type": "string", "nullable": true}}, "additionalProperties": false}, "RpcOrderConsigneeAddress": {"type": "object", "properties": {"mabd": {"type": "string", "format": "date-time"}, "delivery_max_times": {"type": "integer", "format": "int32"}, "desired_delivery_date": {"type": "string", "format": "date-time"}, "pod_name": {"type": "string", "nullable": true}, "deliverd_by": {"type": "string", "nullable": true}, "deliverd_name": {"type": "string", "nullable": true}, "deliverd_time": {"type": "string", "format": "date-time"}, "deliverd_location": {"type": "string", "nullable": true}, "terminal": {"type": "string", "nullable": true}, "terminal_address1": {"type": "string", "nullable": true}, "terminal_address2": {"type": "string", "nullable": true}, "terminal_city": {"type": "string", "nullable": true}, "terminal_state": {"type": "string", "nullable": true}, "terminal_zipcode": {"type": "string", "nullable": true}, "terminal_lat": {"type": "number", "format": "double"}, "terminal_lng": {"type": "number", "format": "double"}, "polygon_id": {"type": "string", "nullable": true}, "polygon_code": {"type": "string", "nullable": true}, "note": {"type": "string", "nullable": true}, "appointment_date": {"type": "string", "format": "date-time"}, "appointment_time_from": {"type": "string", "format": "date-time"}, "appointment_time_to": {"type": "string", "format": "date-time"}, "requre_appt": {"type": "integer", "format": "int32"}, "appointment_no": {"type": "string", "nullable": true}, "puZoneCode": {"type": "string", "nullable": true}, "address1": {"type": "string", "nullable": true}, "address2": {"type": "string", "nullable": true}, "country": {"type": "string", "nullable": true}, "state": {"type": "string", "nullable": true}, "city": {"type": "string", "nullable": true}, "zip_code": {"type": "string", "nullable": true}, "location_id": {"type": "integer", "format": "int64"}, "location_name": {"type": "string", "nullable": true}, "contact": {"type": "string", "nullable": true}, "phone": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "latitude": {"type": "number", "format": "double"}, "longitude": {"type": "number", "format": "double"}, "open_time": {"type": "string", "nullable": true}, "close_time": {"type": "string", "nullable": true}, "location_type": {"type": "integer", "format": "int32"}, "order_key": {"type": "integer", "format": "int64"}, "location_code": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ShipmentOrderEstimateFreightResultDto": {"type": "object", "properties": {"estimate_freights": {"type": "array", "items": {"$ref": "#/components/schemas/ShipmentOrderEstimateFreightInfoDto"}, "nullable": true}, "actual_info": {"$ref": "#/components/schemas/ShipmentOrderEstimateActualInfo"}}, "additionalProperties": false}, "RpcOrderShipperAddress": {"type": "object", "properties": {"request_pickup_date": {"type": "string", "format": "date-time"}, "request_pickup_time_begin": {"type": "string", "format": "date-time"}, "request_pickup_time_end": {"type": "string", "format": "date-time"}, "terminal": {"type": "string", "nullable": true}, "terminal_address1": {"type": "string", "nullable": true}, "terminal_address2": {"type": "string", "nullable": true}, "terminal_city": {"type": "string", "nullable": true}, "terminal_state": {"type": "string", "nullable": true}, "terminal_zipcode": {"type": "string", "nullable": true}, "terminal_lat": {"type": "number", "format": "double"}, "terminal_lng": {"type": "number", "format": "double"}, "polygon_id": {"type": "string", "nullable": true}, "polygon_code": {"type": "string", "nullable": true}, "note": {"type": "string", "nullable": true}, "appointment_date": {"type": "string", "format": "date-time"}, "appointment_time_from": {"type": "string", "format": "date-time"}, "appointment_time_to": {"type": "string", "format": "date-time"}, "appointment_no": {"type": "string", "nullable": true}, "puZoneCode": {"type": "string", "nullable": true}, "address1": {"type": "string", "nullable": true}, "address2": {"type": "string", "nullable": true}, "country": {"type": "string", "nullable": true}, "state": {"type": "string", "nullable": true}, "city": {"type": "string", "nullable": true}, "zip_code": {"type": "string", "nullable": true}, "location_id": {"type": "integer", "format": "int64"}, "location_name": {"type": "string", "nullable": true}, "contact": {"type": "string", "nullable": true}, "phone": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "latitude": {"type": "number", "format": "double"}, "longitude": {"type": "number", "format": "double"}, "open_time": {"type": "string", "nullable": true}, "close_time": {"type": "string", "nullable": true}, "location_type": {"type": "integer", "format": "int32"}, "order_key": {"type": "integer", "format": "int64"}, "location_code": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ShipmentOrderTrackRecordDto": {"type": "object", "properties": {"trip_no": {"type": "integer", "format": "int64"}, "trigger_event": {"type": "string", "nullable": true}, "title": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "longitude": {"type": "number", "format": "double"}, "latitude": {"type": "number", "format": "double"}}, "additionalProperties": false}, "TdTripOutput": {"type": "object", "properties": {"trip_type": {"$ref": "#/components/schemas/TripTypeEnum"}, "trip_type_text": {"type": "string", "readOnly": true, "nullable": true}, "assign_status": {"$ref": "#/components/schemas/TripAssignStatusEnum"}, "assign_status_text": {"type": "string", "readOnly": true, "nullable": true}, "carrier_code": {"type": "string", "nullable": true}, "carrier_name": {"type": "string", "nullable": true}, "company_id": {"type": "integer", "format": "int32"}, "trip_crrent_time": {"type": "string", "format": "date-time"}, "complete_time": {"type": "string", "format": "date-time"}, "create_way": {"type": "integer", "format": "int32"}, "dispatch_date": {"type": "string", "nullable": true}, "driver_code": {"type": "string", "nullable": true}, "driver_name": {"type": "string", "nullable": true}, "dst_terminal": {"type": "string", "nullable": true}, "optimize_status": {"$ref": "#/components/schemas/TripOptimizeStatusEnum"}, "optimize_status_text": {"type": "string", "readOnly": true, "nullable": true}, "is_offline": {"type": "integer", "format": "int32"}, "org_terminal": {"type": "string", "nullable": true}, "route_id": {"type": "string", "nullable": true}, "route_name": {"type": "string", "nullable": true}, "start_time": {"type": "string", "format": "date-time"}, "time_elapsed": {"type": "string", "readOnly": true, "nullable": true}, "status": {"type": "integer", "format": "int32", "readOnly": true}, "status_text": {"type": "string", "readOnly": true, "nullable": true}, "total_miles": {"type": "number", "format": "double"}, "tractor": {"type": "string", "nullable": true}, "seal": {"type": "string", "nullable": true}, "trailer": {"type": "string", "nullable": true}, "trip_no": {"type": "string", "nullable": true}, "carrier_type": {"$ref": "#/components/schemas/CarrierTypeEnum"}, "carrier_type_text": {"type": "string", "readOnly": true, "nullable": true}, "tms_id": {"type": "integer", "format": "int64"}, "dispatcher_note": {"type": "string", "nullable": true}, "id": {"type": "integer", "format": "int64"}, "created_time": {"type": "string", "format": "date-time", "nullable": true}, "created_by": {"type": "integer", "format": "int64", "nullable": true}, "create_user": {"type": "string", "nullable": true}, "update_time": {"type": "string", "format": "date-time", "nullable": true}, "update_by": {"type": "integer", "format": "int64", "nullable": true}, "update_user": {"type": "string", "nullable": true}, "is_del": {"type": "boolean"}, "actual_create_time": {"type": "string", "format": "date-time"}, "actual_update_time": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "TerminalInfoByCompany": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "code": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "type": {"type": "string", "nullable": true}, "state": {"type": "string", "nullable": true}, "city": {"type": "string", "nullable": true}, "address1": {"type": "string", "nullable": true}, "address2": {"type": "string", "nullable": true}, "zip_code": {"type": "string", "nullable": true}, "lon": {"type": "number", "format": "double"}, "lat": {"type": "number", "format": "double"}, "contact_name": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "phone": {"type": "string", "nullable": true}}, "additionalProperties": false}, "TripTypeEnum": {"enum": [1, 2, 3, 4], "type": "integer", "format": "int32"}, "ShipmentOrderPalletSummaryDto": {"type": "object", "properties": {"order_id": {"type": "integer", "format": "int64"}, "order_no": {"type": "string", "nullable": true}, "total_weight": {"type": "number", "format": "double"}, "total_volume": {"type": "number", "format": "double"}, "max_length": {"type": "number", "format": "double"}, "max_width": {"type": "number", "format": "double"}, "max_height": {"type": "number", "format": "double"}, "pallets": {"type": "array", "items": {"$ref": "#/components/schemas/PalletInfoDto"}, "nullable": true}, "shipper_address": {"$ref": "#/components/schemas/RpcOrderShipperAddress"}, "consignee_address": {"$ref": "#/components/schemas/RpcOrderConsigneeAddress"}, "actual_pickup_date": {"type": "string", "format": "date-time", "nullable": true}, "shipper_terminal": {"type": "string", "nullable": true}, "consignee_terminal": {"type": "string", "nullable": true}, "service_terminal": {"type": "string", "nullable": true}, "pro_no": {"type": "string", "nullable": true}, "bill_to_name": {"type": "string", "nullable": true}, "bill_to_code": {"type": "string", "nullable": true}, "order_status": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "ShipmentOrderStatusEnum": {"enum": ["New", "Pickup Confirmed", "Out For Pickup", "Pickup Complete", "Pending Linehaul", "Line<PERSON>ul", "Pending Delivery", "Out For Delivery", "Delivered", "Partial Delivered", "Pending Service", "Delivered With Exception", "Delivery Failed", "Consignee Refusal", "Cancelled", "Incomplete", "Return To Shipper"], "type": "string"}, "ShipmentOrderSubStatusEnum": {"enum": ["NotValid", "New", "Package Ready", "Pending Pickup", "Pickup Dispatched", "Out For Pickup", "Departed Pickup Location", "Pickup Complete", "DirectlyPickupOffload", "Pending Linehaul", "<PERSON><PERSON><PERSON> Dispatched", "Linehaul Loaded", "Linehaul In Transit", "In Transit", "Complete", "Pending Dispatch", "Dispatched", "Out For Delivery", "Delivery", "Partial Delivered", "Pending Service", "Delivered with Exception", "Delivery Failed", "Consignee Refusal", "Cancelled", "Incomplete", "Return To Shipper"], "type": "string"}, "StringFmsSelectListItem": {"type": "object", "properties": {"text": {"type": "string", "nullable": true}, "value": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StringFmsSelectListItemListResultDto": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/StringFmsSelectListItem"}, "nullable": true}}, "additionalProperties": false}, "TaskStatusInfoDto": {"type": "object", "properties": {"task_org_terminal": {"type": "string", "nullable": true}, "task_dst_terminal": {"type": "string", "nullable": true}, "task_status": {"type": "integer", "format": "int32"}, "task_status_desc": {"type": "string", "nullable": true}}, "additionalProperties": false}}}}