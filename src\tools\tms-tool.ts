import { tool } from 'ai';
import { z } from 'zod';
import { createHash } from 'crypto';
import { BaseTool } from './baseTool';
import { ToolDefinition } from './types';

/**
 * 过滤TMS tracking数据中的敏感信息
 * 移除个人姓名、地址等隐私信息
 * 
 * 过滤规则：
 * 1. 移除个人/公司名称：pickup_name, delivery_name, bill_to_name
 * 2. 移除具体地址：pickup_street, delivery_street  
 * 3. 移除联系方式：pickup_phone, delivery_phone, contact_email
 * 4. 移除文件链接：files节点（包含BOL、发票、POD等隐私文档）
 * 5. 保留城市、州、邮编信息用于物流追踪
 * 6. 保留订单号、追踪号等业务信息
 * 7. 在order_line中只保留城市和州，移除具体街道地址
 */
function filterSensitiveTrackingData(data: any): any {
  if (!data || typeof data !== 'object') {
    return data;
  }

  // 如果是数组，递归处理每个元素
  if (Array.isArray(data)) {
    return data.map(item => filterSensitiveTrackingData(item));
  }

  // 创建过滤后的对象副本
  const filtered = { ...data };

  // 需要移除的敏感字段
  const sensitiveFields = [
    'pickup_name',
    'pickup_street', 
    'pickup_phone',
    'delivery_name',
    'delivery_street',
    'delivery_phone',
    'bill_to_name',
    'contact_email'
  ];

  // 移除敏感字段
  sensitiveFields.forEach(field => {
    if (filtered[field]) {
      filtered[field] = '[REDACTED]';
    }
  });

  // 处理嵌套的 order_line 对象中的地址信息
  if (filtered.order_line && typeof filtered.order_line === 'object') {
    const orderLine = { ...filtered.order_line };
    
    // 处理各个阶段的地址信息，只保留城市和州，移除具体地址
    const stageFields = ['processing', 'in_transit', 'pickup_complete', 'pickup', 'out_for_delivery', 'delivery', 'svcs_trm'];
    
    stageFields.forEach(stage => {
      if (orderLine[stage] && orderLine[stage].address) {
        // 提取城市和州信息，移除具体地址
        const address = orderLine[stage].address;
        // 匹配模式如 "HAYWARD CA|Aug 9 2017" 或 "SAN JOSE CA"
        const cityStateMatch = address.match(/([A-Z\s]+)\s+([A-Z]{2})(\|.*)?$/);
        if (cityStateMatch) {
          const city = cityStateMatch[1].trim();
          const state = cityStateMatch[2];
          const dateInfo = cityStateMatch[3] || '';
          orderLine[stage].address = `${city} ${state}${dateInfo}`;
        } else {
          // 如果无法解析，只保留州信息
          const stateMatch = address.match(/([A-Z]{2})/);
          if (stateMatch) {
            orderLine[stage].address = `[CITY REDACTED] ${stateMatch[1]}`;
          } else {
            orderLine[stage].address = '[ADDRESS REDACTED]';
          }
        }
      }
    });

    // 移除文件节点（包含BOL、发票等隐私文档）
    if (orderLine.files) {
      orderLine.files = '[REDACTED - Privacy Protected]';
    }

    filtered.order_line = orderLine;
  }

  // 递归处理其他嵌套对象
  Object.keys(filtered).forEach(key => {
    if (typeof filtered[key] === 'object' && filtered[key] !== null && key !== 'order_line') {
      filtered[key] = filterSensitiveTrackingData(filtered[key]);
    }
  });

  return filtered;
}

/**
 * TMS订单查询工具
 * 使用 Vercel AI SDK 的 tool 函数实现
 */
export const tmsShipmentOrderTrackingTool = tool({
  description: 'Query TMS (Transportation Management System) order status by tracking number, BOL, PRO, PU number, or other reference',
  parameters: z.object({
    searchText: z.string().describe('The tracking reference number (BOL, PRO, PU, Tracking #, etc.)'),
    page: z.number().optional().default(1).describe('Page number for pagination')
  }),
  execute: async ({ searchText, page = 1 }) => {
    console.log('TMSShipmentOrderTrackingTool.execute called with args:', JSON.stringify({ searchText, page }));
    
    try {
      // 从环境变量获取TMS基础URL，如果未设置则使用默认值
      const baseUrl = process.env.TMS_SHIP_BASEURL || 'https://ship.unisco.com';
      
      // 构建API请求
      const apiUrl = `${baseUrl}/apinew/cpv2/tracking/getOrders?search_text=${encodeURIComponent(searchText)}&page=${page}`;
      
      const response = await fetch(apiUrl, {
        method: 'GET',
        headers: {
          'Accept': 'application/json, text/plain, */*',
          'Accept-Language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7,ja;q=0.6',
          'Authorization': 'bnVsbDpudWxs', // Base64 encoded "null:null"
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      
      console.log('TMSShipmentOrderTrackingTool.execute response (before filtering):', JSON.stringify(data));
      
      // 检查是否启用数据过滤（默认启用）
      const enableDataFiltering = process.env.TMS_ENABLE_DATA_FILTERING !== 'false';
      
      // 过滤敏感信息
      const filteredData = enableDataFiltering ? filterSensitiveTrackingData(data) : data;
      
      if (enableDataFiltering) {
        console.log('TMSShipmentOrderTrackingTool.execute response (after filtering):', JSON.stringify(filteredData));
      } else {
        console.log('TMSShipmentOrderTrackingTool.execute: Data filtering disabled');
      }
      
      return {
        success: true,
        searchText,
        page,
        data: filteredData,
        baseUrl, // 包含使用的基础URL信息
        timestamp: new Date().toISOString()
      };
      
    } catch (error) {
      console.error('TMSShipmentOrderTrackingTool.execute error:', error);
      
      return {
        success: false,
        searchText,
        page,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        timestamp: new Date().toISOString()
      };
    }
  }
});

/**
 * TMS费用报价工具
 * 使用 Vercel AI SDK 的 tool 函数实现
 * 调用 portal.item.com 的 rate-shopping API
 */
export const tmsQuoteTool = tool({
  description: 'Calculate TMS (Transportation Management System) shipping quote for freight transportation',
  parameters: z.object({
    shipper: z.object({
     
      state: z.string().describe('Shipper state (2-letter code)'),
      zip: z.string().describe('Shipper ZIP code'),
      city: z.string().optional().describe('Shipper city'),
    }).describe('Shipper information'),
    consignee: z.object({
      state: z.string().describe('Consignee state (2-letter code)'),
      zip: z.string().describe('Consignee ZIP code'),
      city: z.string().optional().describe('Consignee city'),
    }).describe('Consignee information'),
    manifests: z.array(z.object({
      quantity: z.number().describe('Quantity of Pallets'),
      weight: z.number().describe('Weight in pounds'),
      width: z.number().optional().describe('Pallet Width in inches'),
      length: z.number().optional().describe('Pallet Length in inches'),
      height: z.number().optional().describe('Pallet Height in inches'),
      pallets_stackable: z.boolean().optional().default(false).describe('Whether pallets are stackable'),
      class: z.string().optional().describe('Freight class (e.g., "50", "70", "100")'),
      item: z.string().optional().default('').describe('Item description'),
      nmfc: z.string().optional().default('nmfc').describe('NMFC code'),
      description: z.string().optional().default('').describe('Item description')
    })).describe('Array of manifest items'),
    acc_lines: z.array(z.object({
      code: z.string().describe('Accessorial code (e.g., "LGPUP", "APPTD")'),
      name: z.string().describe('Accessorial name')
    })).optional().default([]).describe('Accessorial charges')
  }),
  execute: async (params) => {
    console.log('TMSQuoteTool.execute called with args:', JSON.stringify(params));
    
    try {
      // The API rejects empty strings for the 'class' field.
      // We process the manifests to remove the class property if it's an empty string.
      const processedManifests = params.manifests.map(m => {
        const manifest = {...m};
        if (manifest.class === '') {
          delete manifest.class;
        }
        return manifest;
      });

      // 构建请求体，使用新的API格式
      const requestBody = {
        acc_lines: params.acc_lines,
        consignee: params.consignee,
        manifests: processedManifests,
        shipper: params.shipper
      };
      
      console.log('TMSQuoteTool request body (processed):', JSON.stringify(requestBody));
      
      // 使用新的API端点
      const apiUrl = 'https://portal.item.com/api/v1/web/rate-shopping/quote';
      
      // 发送API请求
      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Accept': 'application/json, text/plain, */*',
          'Content-Type': 'application/json',
      
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      
      console.log('TMSQuoteTool.execute response:', JSON.stringify(data));
      
      return {
        success: true,
        quote_data: data,
        request_params: requestBody,
        apiUrl,
        timestamp: new Date().toISOString()
      };
      
    } catch (error) {
      console.error('TMSQuoteTool.execute error:', error);
      
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        timestamp: new Date().toISOString()
      };
    }
  }
});

/**
 * TMSTool 类，继承自 BaseTool
 * 适配旧的工具系统
 */
export class TMSTool extends BaseTool {
  constructor() {
    super();
  }
  
  getTools(): ToolDefinition[] {
    return [
      {
        name: 'queryTMSOrder',
        description: 'Query TMS (Transportation Management System) order status by tracking number, BOL, PRO, PU number, or other reference',
        parameters: {
          type: 'object',
          properties: {
            searchText: {
              type: 'string',
              description: 'The tracking reference number (BOL, PRO, PU, Tracking #, etc.)'
            },
            page: {
              type: 'number',
              description: 'Page number for pagination',
              default: 1
            }
          },
          required: ['searchText']
        }
      },
      {
        name: 'getTMSQuote',
        description: 'Calculate TMS (Transportation Management System) shipping quote for freight transportation',
        parameters: {
          type: 'object',
          properties: {
            shipper: {
              type: 'object',
              description: 'Shipper information',
              properties: {
                state: { type: 'string', description: 'Shipper state (2-letter code)' },
                zip: { type: 'string', description: 'Shipper ZIP code' },
                city: { type: 'string', description: 'Shipper city (Optional !)' }
              },
              required: ['state', 'zip']
            },
            consignee: {
              type: 'object',
              description: 'Consignee information',
              properties: {
                state: { type: 'string', description: 'Consignee state (2-letter code)' },
                zip: { type: 'string', description: 'Consignee ZIP code' },
                city: { type: 'string', description: 'Consignee city (Optional !)' },
              },
              required: ['state', 'zip']
            },
            manifests: {
              type: 'array',
              description: 'Array of manifest items',
              items: {
                type: 'object',
                properties: {
                  quantity: { type: 'number', description: 'Quantity of items' },
                  weight: { type: 'number', description: 'Weight in pounds' },
                  width: { type: 'number', description: 'Pallet Width in inches' },
                  length: { type: 'number', description: 'Pallet Length in inches' },
                  height: { type: 'number', description: 'Pallet Height in inches' },
                  pallets_stackable: { type: 'boolean', description: 'Whether pallets are stackable', default: false },
                  class: { type: 'string', description: 'Freight class (e.g., "50", "70", "100")' },                 
                  item: { type: 'string', description: 'Item description', default: '' },
                  nmfc: { type: 'string', description: 'NMFC code', default: 'nmfc' },
                  description: { type: 'string', description: 'Item description', default: '' }
                },
                required: ['quantity', 'weight']
              }
            },
            acc_lines: {
              type: 'array',
              description: 'Accessorial charges',
              items: {
                type: 'object',
                properties: {
                  code: { type: 'string', description: 'Accessorial code (e.g., "LGPUP", "APPTD")' },
                  name: { type: 'string', description: 'Accessorial name' }
                },
                required: ['code', 'name']
              },
              default: []
            }
          },
          required: ['shipper', 'consignee', 'manifests']
        }
      }
    ];
  }
  
  async queryTMSOrder(searchText: string, page: number = 1) {
    // 复用 tmsShipmentOrderTrackingTool 的实现
    const result = await tmsShipmentOrderTrackingTool.execute({ searchText, page }, { 
      toolCallId: `tms-${Date.now()}`, 
      messages: []
    });
    
    // 确保数据已经被过滤（tmsShipmentOrderTrackingTool 已经处理了过滤）
    return result;
  }
  
  async getTMSQuote(params: any) {
    // 复用 tmsQuoteTool 的实现
    return await tmsQuoteTool.execute(params, { 
      toolCallId: `tms-quote-${Date.now()}`, 
      messages: []
    });
  }
  
  static getSystemPrompt(): string {
    return `You can use the TMS tools to query order status and calculate shipping quotes.

Available tools:
1. queryTMSOrder - Query TMS order status by tracking number, BOL, PRO, PU number, or other reference
2. getTMSQuote - Calculate shipping quotes for freight transportation using portal.item.com API

For queryTMSOrder:
- Supported query fields include BOL, PRO, PU, Tracking numbers, and other order references
- Supports pagination queries, defaults to page 1
- Returns detailed order status information

For getTMSQuote:
- Calculates freight shipping quotes between origin and destination
- Required: shipper and consignee state and ZIP code (city is optional)
- Required manifest details: quantity and weight
- Optional manifest details: dimensions (width, length, height), freight class, stackability
- Supports accessorial charges (liftgate, appointment, etc.)
- Returns detailed pricing information from multiple carriers

Example quote parameters:
- Shipper: { state: "CA", zip: "94545", city: "Hayward" } (city optional)
- Consignee: { state: "CA", zip: "91789", city: "City of Industry" } (city optional)
- Manifests: [{ quantity: 1, weight: 1000, width: 48, length: 72, height: 80, class: "50" }] (only quantity and weight required)

Example queries:
- "Query order 1111 status"
- "Help me check BOL number ABC123"
- "Get a quote for shipping from Hayward, CA to City of Industry, CA"
- "Calculate freight cost for 1 pallet, 1000 lbs" (minimal required info)`;
  }
} 