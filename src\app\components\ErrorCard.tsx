'use client';

import React, { useState } from 'react';
import '@/styles/item-design-system.css';

interface ErrorCardProps {
  toolName: string;
  error: any;
  args?: any;
}

export default function ErrorCard({ toolName, error, args }: ErrorCardProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  
  // Extract error message from different error formats
  const getErrorMessage = () => {
    if (!error) return "Unknown error occurred";
    
    if (typeof error === 'string') {
      return error;
    }
    
    if (error.message) {
      return error.message;
    }
    
    if (error.error) {
      return typeof error.error === 'string' ? error.error : JSON.stringify(error.error);
    }
    
    return JSON.stringify(error);
  };
  
  // Try to extract validation errors if they exist
  const getValidationErrors = () => {
    try {
      // Look for validation errors in different formats
      if (error.cause && error.cause.issues) {
        return error.cause.issues;
      }
      
      // Check if error message contains a JSON string with validation errors
      if (typeof error.message === 'string') {
        const match = error.message.match(/\[(.*?)\]/s);
        if (match && match[0]) {
          try {
            const parsedErrors = JSON.parse(match[0]);
            if (Array.isArray(parsedErrors)) {
              return parsedErrors;
            }
          } catch {
            // Failed to parse, continue with other methods
          }
        }
      }
      
      return null;
    } catch (e) {
      console.error("Failed to parse validation errors:", e);
      return null;
    }
  };
  
  const validationErrors = getValidationErrors();
  const errorMessage = getErrorMessage();
  
  // Collapsed view
  if (!isExpanded) {
    return (
      <div 
        className="item-card bg-red-900/35 rounded-lg border border-red-500/40 p-3 text-white shadow-lg cursor-pointer my-2 hover:border-red-500/60 hover:shadow-xl transition-all duration-200 item-glow-red-subtle"
        onClick={() => setIsExpanded(true)}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div className="bg-red-500/20 p-1.5 rounded-lg mr-2.5">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-4 h-4 text-red-400">
                <path strokeLinecap="round" strokeLinejoin="round" d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z" />
              </svg>
            </div>
            <div>
              <div className="text-sm font-medium text-item-gray-500">Tool Call Failed</div>
              <div className="text-xs text-item-gray-500 font-medium">Error calling {toolName} tool</div>
            </div>
          </div>
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-4 h-4 text-red-400 transition-transform duration-200">
            <path strokeLinecap="round" strokeLinejoin="round" d="M19.5 8.25l-7.5 7.5-7.5-7.5" />
          </svg>
        </div>
      </div>
    );
  }
  
  // Expanded view
  return (
    <div className="item-card bg-red-900/35 rounded-lg border border-red-500/40 p-2 text-white shadow-lg my-2 item-glow-red-subtle">
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center">
          <div className="bg-red-500/20 p-1.5 rounded-lg mr-2.5">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-4 h-4 text-red-400">
              <path strokeLinecap="round" strokeLinejoin="round" d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z" />
            </svg>
          </div>
          <div>
            <div className="text-sm font-semibold text-white">Tool Call Failed</div>
            <div className="text-xs text-red-300 font-medium">Error calling {toolName} tool</div>
          </div>
        </div>
        <button 
          className="text-red-400 hover:text-red-300 hover:bg-red-500/20 p-1.5 rounded-lg transition-all duration-200"
          onClick={() => setIsExpanded(false)}
        >
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-4 h-4">
            <path strokeLinecap="round" strokeLinejoin="round" d="M4.5 15.75l7.5-7.5 7.5 7.5" />
          </svg>
        </button>
      </div>
      
      {/* Error message */}
      <div className="mb-3">
        <div className="text-sm font-semibold mb-2 text-red-400 uppercase tracking-wide">Error Message</div>
        <div className="bg-item-bg-card border border-red-500/30 rounded-lg p-2 text-sm text-white">
          {errorMessage}
        </div>
      </div>
      
      {/* Validation errors if they exist */}
      {validationErrors && validationErrors.length > 0 && (
        <div className="mb-3">
          <div className="text-sm font-semibold mb-2 text-red-400 uppercase tracking-wide">Validation Errors</div>
          <div className="space-y-1.5">
            {validationErrors.map((error: any, index: number) => (
              <div key={index} className="bg-item-bg-card border border-red-500/30 rounded-lg p-2 text-sm">
                <div className="font-semibold text-white">{error.path?.join('.') || 'Unknown field'}</div>
                <div className="text-red-300 text-xs mt-0.5 font-medium">{error.message || 'Invalid data'}</div>
                {error.expected && (
                  <div className="text-xs mt-0.5">
                    <span className="text-red-400 font-medium">Expected:</span> <span className="text-white">{error.expected}</span>
                  </div>
                )}
                {error.received && (
                  <div className="text-xs mt-0.5">
                    <span className="text-red-400 font-medium">Received:</span> <span className="text-white">{error.received}</span>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}
      
      {/* Parameters that were passed */}
      {args && (
        <div className="mb-3">
          <div className="text-sm font-semibold mb-2 text-red-400 uppercase tracking-wide">Parameters Passed</div>
          <div className="bg-item-bg-card border border-red-500/30 rounded-lg p-2">
            <pre className="text-xs overflow-x-auto whitespace-pre-wrap break-words text-item-gray-300 font-mono">
              {JSON.stringify(args, null, 2)}
            </pre>
          </div>
        </div>
      )}
      
      {/* Suggestions */}
      <div className="bg-item-bg-card border border-red-500/30 rounded-lg p-2 text-sm">
        <div className="font-semibold mb-2 text-red-400 uppercase tracking-wide">Suggested Solutions:</div>
        <ul className="list-disc list-inside space-y-1.5 text-white">
          <li>Check if parameter format is correct</li>
          <li>Ensure required fields are not empty</li>
          <li>Try using the correct data types</li>
          <li>If the problem persists, contact the administrator</li>
        </ul>
      </div>
    </div>
  );
} 