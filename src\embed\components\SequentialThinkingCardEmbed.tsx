import React from 'react';

export default function SequentialThinkingCardEmbed({ toolInvocation, allInvocations }: { toolInvocation: any, allInvocations?: any[] }) {
  // Extract thinking content from various possible locations
  const extractThoughtContent = (data: any): string => {
    if (!data) return '';
    
    // Check common thought content fields
    if (typeof data.thought === 'string' && data.thought.trim()) {
      return data.thought;
    }
    
    if (typeof data.thinking === 'string' && data.thinking.trim()) {
      return data.thinking;
    }
    
    // Check thought field in result object
    if (data.result) {
      if (typeof data.result.thought === 'string' && data.result.thought.trim()) {
        return data.result.thought;
      }
      if (typeof data.result.thinking === 'string' && data.result.thinking.trim()) {
        return data.result.thinking;
      }
    }
    
    // Look for "thought" related content in raw JSON
    const rawJson = JSON.stringify(data);
    const thoughtMatch = rawJson.match(/"thought":"([^"]+)"/);
    if (thoughtMatch && thoughtMatch[1]) {
      return thoughtMatch[1];
    }
    
    // Check if there are args or other fields containing thoughts
    if (data.args && typeof data.args.thinking === 'string') {
      return data.args.thinking;
    }
    
    return '';
  };
  
  // Get sequential thinking content
  const getThinkingContent = () => {
    // First try to get from current tool invocation
    let content = extractThoughtContent(toolInvocation);
    if (content) {
      return content;
    }
    
    // If not found, try to get from all tool invocations
    if (allInvocations && allInvocations.length > 0) {
      for (const inv of allInvocations) {
        content = extractThoughtContent(inv);
        if (content) {
          return content;
        }
      }
    }
    
    // If no thinking content found, return default message
    return '';
  };
  
  // Get the final content to display
  const displayContent = () => {
    const content = getThinkingContent();
    
    if (!content) {
      return "Thinking...";
    }
    
    // Truncate content if too long
    return content.length > 50 ? `${content.substring(0, 50)}...` : content;
  };
  
  // For partial results or when waiting for tool completion
  const isPartial = toolInvocation.state === 'partial-call' || 
                    toolInvocation.state === 'call';
  
  return (
    <div className="embed-card sequential-thinking p-1 my-1 bg-gray-800/10 rounded border border-gray-700/30">
      <div className="flex items-center" style={{ whiteSpace: 'nowrap', width: '100%' }}>
        <div className="mr-2 text-gray-400 flex-shrink-0">
          <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
          </svg>
        </div>
        <div className="text-xs text-gray-300 flex-shrink-0 mr-2">Thinking:</div>
        <div className="text-xs text-gray-400 overflow-hidden text-ellipsis" style={{ textOverflow: 'ellipsis', overflow: 'hidden', whiteSpace: 'nowrap' }}>
          {isPartial ? "Thinking..." : displayContent() || "No thinking content found"}
        </div>
      </div>
    </div>
  );
} 