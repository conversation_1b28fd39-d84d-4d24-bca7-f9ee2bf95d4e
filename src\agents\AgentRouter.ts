import { AgentExecutionContext } from './base/BaseAgent';
import { globalAgentManager } from './base/AgentManager';

// Agent路由器 - 处理Agent间的委托
export class AgentRouter {
  
  // 检查消息中是否有委托请求
  static checkForDelegation(messages: any[]): { shouldDelegate: boolean; agentId?: string } {
    console.log(`[AgentRouter] Checking for delegation in ${messages.length} messages`);
    
    // 查找最近的assistant消息中是否有委托工具调用
    for (let i = messages.length - 1; i >= 0; i--) {
      const message = messages[i];
      console.log(`[AgentRouter] Message ${i}: role=${message.role}, hasToolInvocations=${!!message.toolInvocations}`);
      
      if (message.role === 'assistant' && message.toolInvocations) {
        console.log(`[AgentRouter] Found ${message.toolInvocations.length} tool invocations in message ${i}`);
        
        for (const invocation of message.toolInvocations) {
          console.log(`[Agent<PERSON>outer] Tool: ${invocation.toolName}, result:`, invocation.result);
          
          if (invocation.toolName === 'delegateToAgent' && invocation.result?.delegated) {
            console.log(`[AgentRouter] Found delegation to ${invocation.result.agentId}`);
            return {
              shouldDelegate: true,
              agentId: invocation.result.agentId
            };
          }
        }
      }
    }
    
    console.log('[AgentRouter] No delegation found');
    return { shouldDelegate: false };
  }
  
  // 执行委托
  static async executeDelegation(agentId: string, context: AgentExecutionContext, modelInstance: any) {
    console.log(`[AgentRouter] Executing delegation to ${agentId}`);
    
    const targetAgent = globalAgentManager.getAgent(agentId);
    if (!targetAgent) {
      throw new Error(`Agent ${agentId} not found`);
    }
    
    // 保持完整的消息历史，让Agent看到完整上下文
    // Agent可以根据委托历史自行决定是否需要进一步操作
    return await targetAgent.execute(context, modelInstance);
  }
}