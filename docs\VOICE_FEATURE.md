# 🎙️ 实时语音功能文档

## 概述

基于 OpenAI Realtime API 和 Hybrid Agent 架构的实时语音对话功能，提供自然的语音交互体验和智能工具调用能力。

## 核心特性

### 🗣️ 实时语音对话
- **Live模式**: 自动语音检测，无需手动控制
- **PTT模式**: 按键说话，精确控制录音时机
- **实时语音转文本**: 用户和AI的语音都会实时转换为文本
- **音频播放**: AI回复支持语音播放

### 🧠 HybridAgent 智能处理
- **工具调用**: 自动识别复杂任务并调用相应工具
- **知识库访问**: 集成现有的知识库和文档搜索
- **JIRA集成**: 支持项目管理操作
- **WMS集成**: 仓库管理系统操作
- **IoT控制**: 物联网设备控制

### 🔧 技术架构
- **RealtimeClient**: 封装 OpenAI Realtime API
- **VoiceControls**: 语音控制UI组件
- **useRealtimeVoice**: React钩子管理语音状态
- **hybridAgentTool**: 智能代理工具

## 使用方法

### 1. 基本使用

```tsx
import { VoiceControls } from '@/components/VoiceControls';
import { useRealtimeVoice } from '@/hooks/useRealtimeVoice';

function MyComponent() {
  const voiceHook = useRealtimeVoice({
    onUserTranscript: (transcript) => {
      // 处理用户语音转文本
      console.log('用户说:', transcript);
    },
    onAssistantTranscript: (transcript) => {
      // 处理AI回复转文本
      console.log('AI回复:', transcript);
    },
    onAudioReceived: (audioData) => {
      // 处理接收到的音频数据
      console.log('收到音频:', audioData.byteLength, 'bytes');
    },
    model: 'gpt-4o-realtime-preview-2024-12-17',
    authToken: userToken,
  });

  return (
    <VoiceControls
      isConnected={voiceHook.isConnected}
      isConnecting={voiceHook.status === 'connecting'}
      isRecording={voiceHook.isRecording}
      status={voiceHook.status}
      voiceMode={voiceHook.voiceMode}
      onConnect={voiceHook.connectVoice}
      onDisconnect={voiceHook.disconnectVoice}
      onModeSwitch={voiceHook.switchVoiceMode}
      onPTTStart={voiceHook.startPTT}
      onPTTStop={voiceHook.stopPTT}
      onMute={voiceHook.setMuted}
    />
  );
}
```

### 2. 在Chat组件中使用

语音功能已集成到主Chat组件中：
- 语音控件位于顶部导航栏
- 语音转文本自动添加到聊天记录
- AI回复支持语音播放和文本显示

### 3. 测试页面

访问 `/test-voice` 查看完整的语音功能演示。

## API 接口

### 语音会话API
- **POST** `/api/voice/session` - 创建语音会话并获取ephemeral token

### HybridAgent API  
- **POST** `/api/hybrid-agent` - 处理复杂任务的智能代理

## 配置要求

### 环境变量
```bash
OPENAI_API_KEY=your_openai_api_key
```

### 依赖包
```json
{
  "@openai/agents": "^0.0.1",
  "uuid": "^11.0.4", 
  "zod": "^3.24.1"
}
```

## 工作流程

### Live模式流程
1. 用户点击"语音"按钮连接
2. 系统自动检测语音输入
3. 语音实时转换为文本
4. AI处理并回复（文本+语音）
5. 对话记录自动保存

### PTT模式流程
1. 用户选择PTT模式
2. 按住"按住说话"按钮录音
3. 松开按钮完成录音
4. 系统处理语音转文本
5. AI智能回复

### HybridAgent处理流程
1. 实时代理接收语音输入
2. 判断是否需要复杂处理
3. 调用hybridAgentTool
4. 执行相关工具和API调用
5. 返回处理结果
6. 语音播放回复

## 错误处理

### 常见错误
- **连接失败**: 检查网络和API密钥
- **权限错误**: 确保用户已登录且有权限
- **音频播放失败**: 检查浏览器音频权限

### 调试信息
```javascript
// 开启详细日志
localStorage.setItem('voice_debug', 'true');
```

## 最佳实践

### 性能优化
- 使用音频缓冲减少延迟
- 智能语音检测减少误触发
- 适当的超时设置

### 用户体验
- 清晰的状态指示
- 流畅的模式切换
- 直观的错误提示

### 安全考虑
- Token刷新机制
- 语音数据不存储
- 用户隐私保护

## 故障排除

### 1. 连接问题
```bash
# 检查API密钥
curl -H "Authorization: Bearer $OPENAI_API_KEY" \
     https://api.openai.com/v1/models
```

### 2. 音频问题
- 检查浏览器麦克风权限
- 确保音频设备正常工作
- 尝试刷新页面重新连接

### 3. 工具调用问题
- 检查hybridAgent API响应
- 查看浏览器控制台错误
- 验证用户权限设置

## 更新日志

### v1.0.0 (2024-12-XX)
- ✅ 基础实时语音功能
- ✅ Live/PTT双模式支持
- ✅ HybridAgent集成
- ✅ 完整的Chat组件集成
- ✅ 测试页面和文档

### 下个版本计划
- [ ] 语音情感识别
- [ ] 多语言支持
- [ ] 语音记忆功能
- [ ] 自定义语音模型

## 技术支持

如有问题，请查看：
1. 浏览器控制台错误信息
2. 网络请求状态
3. API响应内容
4. 用户认证状态

---

*本文档会随功能更新持续完善* 