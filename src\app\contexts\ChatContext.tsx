'use client';

import React, { createContext, useContext, useState, useEffect, useCallback, ReactNode, useRef } from 'react';
import { Message } from '@ai-sdk/react';
import { ChatHistory, generateChatTitle, AudioRecording } from '@/utils/chatHistoryUtils';
import { useAuth } from './AuthContext';
import { clientUserContextManager } from '@/utils/clientUserContext';
import api from '@/utils/apiClient';
import { ChatHistoryIndexItem } from '@/utils/storage/types';
import { nanoid } from 'nanoid';
import { log } from '@/utils/logger';

// 定义聊天上下文状态
interface ChatContextState {
  // 状态
  chatHistories: ChatHistory[];
  chatIndexes: ChatHistoryIndexItem[]; // 新增：聊天历史索引
  selectedChatId: string | null;
  isLoading: boolean;
  loadingChatId: string | null; // 新增：当前正在加载的聊天ID
  errorMessage: string | null;

  // 方法
  loadChatHistories: () => Promise<void>;
  loadChatIndexes: () => Promise<void>; // 新增：加载聊天历史索引
  selectChat: (id: string, isNewlyCreated?: boolean) => Promise<ChatHistory | null>;
  createNewChat: () => string; // 修改：返回新生成的聊天ID
  saveCurrentChat: (messages: Message[], model: string, chatId?: string | null, audioRecordings?: AudioRecording[]) => Promise<ChatHistory | null>;
  deleteChat: (id: string) => Promise<boolean>;
  deleteAllChats: () => Promise<boolean>; // 新增：批量删除所有聊天
  updateChatTitle: (id: string, newTitle: string) => Promise<boolean>;
}

// 创建聊天上下文
const ChatContext = createContext<ChatContextState | undefined>(undefined);

interface ChatProviderProps {
  children: ReactNode;
}

export const ChatProvider: React.FC<ChatProviderProps> = ({ children }) => {
  const [chatHistories, setChatHistories] = useState<ChatHistory[]>([]);
  const [chatIndexes, setChatIndexes] = useState<ChatHistoryIndexItem[]>([]); // 新增：聊天历史索引状态
  const [selectedChatId, setSelectedChatId] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  // 使用ref跟踪组件挂载状态，防止内存泄漏
  const isMounted = useRef(true);
  // 使用ref来防止重复加载和加载竞争条件
  const isLoadingRef = useRef(false);

  // 获取认证信息
  const { user, isAuthenticated, isLoading: authLoading, tokenData } = useAuth();

  // 获取用户ID - 尝试多种来源以确保获取到正确ID
  const currentUserId = user?.id ? String(user.id) :
                      tokenData?.data?.user_id ? tokenData.data.user_id :
                      tokenData?.sub ? tokenData.sub : null;

  // 获取请求头
  const getAuthHeaders = useCallback(() => {
    const headers = clientUserContextManager.getAuthHeaders();

    if (currentUserId) {
      // 添加用户ID头，确保服务器知道请求来自哪个用户
      headers['X-User-ID'] = currentUserId;
    }

    return headers;
  }, [currentUserId]);

  // 新增：加载聊天历史索引
  const loadChatIndexes = useCallback(async () => {
    // 防止并发加载请求
    if (isLoadingRef.current) {
      log.debug('Loading request already in progress, skipping duplicate index loading', undefined, 'ChatContext');
      return;
    }

    log.debug('loadChatIndexes called', undefined, 'ChatContext');

    try {
      isLoadingRef.current = true;
      setIsLoading(true);
      setErrorMessage(null);

      // 如果用户未登录或正在加载认证状态，则不加载聊天历史索引
      if (!isAuthenticated || authLoading) {
        setChatIndexes([]);
        return;
      }

      if (!currentUserId) {
        log.error('Unable to load chat history index: user ID not found', undefined, 'ChatContext');
        setErrorMessage('Unable to load chat history index: user ID not found');
        setChatIndexes([]);
        return;
      }

      log.info('Loading user chat history index, user ID:', currentUserId, 'ChatContext');

      const { data, error: apiError, status } = await api.get<ChatHistoryIndexItem[]>('/api/chat-history-index', {
        headers: getAuthHeaders()
      });

      // 处理响应
      if (apiError) {
        if (status === 401) {
          setChatIndexes([]);
          log.warn('Authentication failed, may need to re-login', undefined, 'ChatContext');
        }
        throw new Error(apiError);
      }

      // 确保组件仍然挂载
      if (isMounted.current) {
        const indexes = data || [];
        log.info(`Loaded ${indexes.length} chat history index records`, undefined, 'ChatContext');
        setChatIndexes(indexes);
      }
    } catch (err) {
      log.error('Failed to load chat history index:', err, 'ChatContext');
      if (isMounted.current) {
        setErrorMessage('加载聊天历史索引失败');
      }
    } finally {
      if (isMounted.current) {
        setIsLoading(false);
      }
      isLoadingRef.current = false;
    }
  }, [isAuthenticated, authLoading, currentUserId, getAuthHeaders]);

  // 加载所有聊天历史（保持原始功能以兼容）
  const loadChatHistories = useCallback(async () => {
    // 不再是主要加载方法，现在我们首选loadChatIndexes
    log.debug('loadChatHistories called (deprecated method, recommend using loadChatIndexes)', undefined, 'ChatContext');

    // 防止并发加载请求
    if (isLoadingRef.current) {
              log.debug('Loading request already in progress, skipping duplicate loading', undefined, 'ChatContext');
      return;
    }

    try {
      isLoadingRef.current = true;
      setIsLoading(true);
      setErrorMessage(null);

      // 如果用户未登录或正在加载认证状态，则不加载聊天历史
      if (!isAuthenticated || authLoading) {
        setChatHistories([]);
        return;
      }

      if (!currentUserId) {
        log.error('Unable to load chat history: user ID not found', undefined, 'ChatContext');
        setErrorMessage('Unable to load chat history: user ID not found');
        setChatHistories([]);
        return;
      }

      log.info('Loading user chat history, user ID:', currentUserId, 'ChatContext');

      const { data, error: apiError, status } = await api.get<ChatHistory[]>('/api/chat-history', {
        headers: getAuthHeaders()
      });

      // 处理响应
      if (apiError) {
        if (status === 401) {
          setChatHistories([]);
          log.warn('Authentication failed, may need to re-login', undefined, 'ChatContext');
        }
        throw new Error(apiError);
      }

      // 确保组件仍然挂载
      if (isMounted.current) {
        const histories = data || [];
        log.info(`Loaded ${histories.length} chat history records`, undefined, 'ChatContext');
        setChatHistories(histories);
      }
    } catch (err) {
      log.error('Failed to load chat history:', err, 'ChatContext');
      if (isMounted.current) {
        setErrorMessage('加载聊天历史失败');
      }
    } finally {
      if (isMounted.current) {
        setIsLoading(false);
      }
      isLoadingRef.current = false;
    }
  }, [isAuthenticated, authLoading, currentUserId, getAuthHeaders]);

  // 使用ref来跟踪初始化状态，确保加载聊天历史只发生一次
  const isInitializedRef = useRef(false);

  // 使用ref来跟踪selectedChatId的变化
  const selectedChatIdRef = useRef(selectedChatId);

  // 当selectedChatId变化时，记录日志
  useEffect(() => {
    log.debug(`selectedChatId changed: ${selectedChatIdRef.current} -> ${selectedChatId}`, undefined, 'ChatContext');
    selectedChatIdRef.current = selectedChatId;
  }, [selectedChatId]);

  // 添加一个状态来跟踪当前正在加载的聊天ID
  const [loadingChatId, setLoadingChatId] = useState<string | null>(null);

  // 选择特定聊天
  const selectChat = useCallback(async (id: string, isNewlyCreated: boolean = false): Promise<ChatHistory | null> => {
    log.debug('selectChat called, id:', { id, currentSelectedChatId: selectedChatId, isNewlyCreated }, 'ChatContext');

    // 如果已经选中了该聊天，则不重复加载
    if (id === selectedChatId) {
              log.debug('Chat already selected, skipping duplicate loading:', id, 'ChatContext');
        const existingChat = chatHistories.find(chat => chat.id === id);
        log.debug('Found chat in history:', existingChat ? 'yes' : 'no', 'ChatContext');
      return existingChat || null;
    }

    // 设置当前选中ID
          log.debug('Setting new selectedChatId:', id, 'ChatContext');
    setSelectedChatId(id);

    // 如果是新创建的聊天，跳过加载步骤
    if (isNewlyCreated) {
              log.debug('This is a newly created chat, skipping loading step', undefined, 'ChatContext');
      return null;
    }

    try {
      // 设置当前正在加载的聊天ID，而不是全局加载状态
      setLoadingChatId(id);
      setErrorMessage(null);

      // 如果用户未登录，则不加载聊天详情
      if (!isAuthenticated || authLoading) {
        return null;
      }

      if (!currentUserId) {
        log.error('Unable to get user ID, cannot select chat', undefined, 'ChatContext');
        setErrorMessage('未获取到用户ID');
        return null;
      }

      log.info('Selecting chat:', { id, userId: currentUserId }, 'ChatContext');

      const { data, error: apiError } = await api.get<ChatHistory>(`/api/chat-history?id=${id}`, {
        headers: getAuthHeaders()
      });

      if (apiError) {
        throw new Error(apiError);
      }

      if (data) {
        // 将获取的聊天历史添加到当前聊天历史列表中
        setChatHistories(prevHistories => {
          // 查找是否已存在该聊天
          const index = prevHistories.findIndex(chat => chat.id === data.id);
          if (index >= 0) {
            // 更新已有聊天
            const updatedHistories = [...prevHistories];
            updatedHistories[index] = data;
            return updatedHistories;
          } else {
            // 添加新聊天
            return [...prevHistories, data];
          }
        });
      }

      return data || null;
    } catch (err) {
      log.error('Failed to select chat:', err, 'ChatContext');
      if (isMounted.current) {
        setErrorMessage('选择聊天失败');
      }
      return null;
    } finally {
      if (isMounted.current) {
        // 清除当前正在加载的聊天ID
        setLoadingChatId(null);
      }
    }
  }, [isAuthenticated, authLoading, currentUserId, selectedChatId, chatHistories, getAuthHeaders]);

  // 创建新聊天
  const createNewChat = useCallback(() => {
    log.debug("Creating new chat: generating new chat ID", undefined, 'ChatContext');

    // 生成新的聊天ID，使用与后端相同的nanoid函数
    const newChatId = nanoid();
    log.debug("Generated new chat ID:", newChatId, 'ChatContext');

    // 设置新的聊天ID
    setSelectedChatId(newChatId);

    // 确保UI状态也被重置
    setIsLoading(false);
    setErrorMessage(null);

    return newChatId;
  }, []);

  // 保存当前聊天
  const saveCurrentChat = useCallback(async (messages: Message[], model: string, chatId?: string | null, audioRecordings?: AudioRecording[]): Promise<ChatHistory | null> => {
    // 使用传入的chatId或selectedChatId
    const effectiveChatId = chatId || selectedChatId;
    log.debug('Starting to save chat:', { selectedChatId, effectiveChatId, model, messageCount: messages.length, audioRecordingsCount: audioRecordings?.length || 0 }, 'ChatContext');

    try {
      // 只在真正发起API请求时设置loading
      if (!effectiveChatId) {
        setIsLoading(true);
      }
      setErrorMessage(null);

      // 如果用户未登录，则不保存聊天
      if (!isAuthenticated || authLoading) {
        throw new Error('用户未登录或会话已过期');
      }

      if (!currentUserId) {
        log.error('Unable to get user ID, cannot save chat', undefined, 'ChatContext');
        throw new Error('未获取到用户ID');
      }

      // 确定是否是新对话或现有对话
      const isNewChat = !effectiveChatId;

      // 生成标题
      let title = 'New Chat';
      if (messages.length > 0) {
        const firstUserMessage = messages.find(m => m.role === 'user');
        if (firstUserMessage) {
          const content = typeof firstUserMessage.content === 'string'
            ? firstUserMessage.content
            : JSON.stringify(firstUserMessage.content);
          title = generateChatTitle(content);
        }
      }

      const now = new Date().toISOString();
      const chatData = {
        id: effectiveChatId,
        title,
        messages,
        model,
        audioRecordings: audioRecordings || [],
        createdAt: isNewChat ? now : undefined,
        updatedAt: now,
        userId: currentUserId
      };

      // 减少日志输出
      if (isNewChat) {
        log.info('Creating new chat...', undefined, 'ChatContext');
      }

      const { data, error: apiError } = await api.post<ChatHistory>('/api/chat-history', chatData, {
        headers: getAuthHeaders()
      });

      if (apiError) {
        throw new Error(apiError);
      }

      if (!data) {
        throw new Error('保存聊天失败：服务器未返回数据');
      }

      const savedChat = data;

      // 减少日志输出
      if (isNewChat) {
        log.info('New chat created:', savedChat.id, 'ChatContext');

        // 如果是新聊天，确保selectedChatId被设置
        if (selectedChatId !== savedChat.id) {
          log.debug(`Updating selectedChatId: ${selectedChatId} -> ${savedChat.id}`, undefined, 'ChatContext');
          setSelectedChatId(savedChat.id);
        }
      }

      // 高效地更新本地聊天历史列表
      if (isMounted.current) {
        // 检查是否已经存在该聊天
        const chatExists = chatHistories.some(chat => chat.id === savedChat.id);
        const indexExists = chatIndexes.some(index => index.id === savedChat.id);

        log.debug(`Chat ID ${savedChat.id} in history list: ${chatExists ? 'exists' : 'not exists'}, in index: ${indexExists ? 'exists' : 'not exists'}`, undefined, 'ChatContext');
        log.debug(`Current selectedChatId: ${selectedChatId}, saved chat ID: ${savedChat.id}`, undefined, 'ChatContext');

        // 确保selectedChatId与savedChat.id一致
        if (selectedChatId !== savedChat.id) {
          log.warn(`Warning: selectedChatId (${selectedChatId}) does not match savedChat.id (${savedChat.id})`, undefined, 'ChatContext');
          // 更新selectedChatId以确保一致性
          setSelectedChatId(savedChat.id);
        }

        // 更新聊天历史列表 - 使用函数式更新确保原子性
        setChatHistories(prevHistories => {
          // 先检查是否已存在该聊天
          const existingIndex = prevHistories.findIndex(chat => chat.id === savedChat.id);

          if (existingIndex === -1) {
            // 不存在，添加新聊天
            log.debug(`Adding new chat to history list: ${savedChat.id}`, undefined, 'ChatContext');
            log.debug(`History list length before update: ${prevHistories.length}`, undefined, 'ChatContext');
            const newHistories = [savedChat, ...prevHistories];
            log.debug(`History list length after update: ${newHistories.length}`, undefined, 'ChatContext');
            return newHistories;
          } else {
            // 已存在，更新现有聊天
            log.debug(`Updating existing chat: ${savedChat.id} (index position: ${existingIndex})`, undefined, 'ChatContext');
            log.debug(`History list length before update: ${prevHistories.length}`, undefined, 'ChatContext');
            const updatedHistories = [...prevHistories];
            updatedHistories[existingIndex] = {
              ...updatedHistories[existingIndex],
              messages,
              updatedAt: now,
              model
            };
            log.debug(`History list length after update: ${updatedHistories.length}`, undefined, 'ChatContext');
            return updatedHistories;
          }
        });

        // 更新索引列表 - 使用函数式更新确保原子性
        setChatIndexes(prevIndexes => {
          // 先检查是否已存在该索引
          const existingIndex = prevIndexes.findIndex(index => index.id === savedChat.id);

          if (existingIndex === -1) {
            // 不存在，添加新索引项
           
            const newIndexItem: ChatHistoryIndexItem = {
              id: savedChat.id,
              title: savedChat.title,
              createdAt: savedChat.createdAt,
              updatedAt: savedChat.updatedAt
            };
            const newIndexes = [newIndexItem, ...prevIndexes];
            log.debug(`Index list length after update: ${newIndexes.length}`, undefined, 'ChatContext');
            return newIndexes;
          } else {
            // 已存在，更新现有索引项
            log.debug(`Updating existing index item: ${savedChat.id} (index position: ${existingIndex})`, undefined, 'ChatContext');
            log.debug(`Index list length before update: ${prevIndexes.length}`, undefined, 'ChatContext');
            const updatedIndexes = [...prevIndexes];
            updatedIndexes[existingIndex] = {
              ...updatedIndexes[existingIndex],
              title: savedChat.title,
              updatedAt: now
            };
            log.debug(`Index list length after update: ${updatedIndexes.length}`, undefined, 'ChatContext');
            return updatedIndexes;
          }
        });
      }

      return savedChat;
    } catch (err) {
      log.error('Failed to save chat:', err, 'ChatContext');
      if (isMounted.current) {
        setErrorMessage('保存聊天失败');
      }
      return null;
    } finally {
      if (isMounted.current) {
        setIsLoading(false);
      }
    }
  }, [selectedChatId, isAuthenticated, authLoading, currentUserId, getAuthHeaders]);

  // 从索引中移除聊天（假删除）
  const deleteChat = useCallback(async (id: string): Promise<boolean> => {
    try {
      setIsLoading(true);
      setErrorMessage(null);

      // 如果用户未登录，则不操作
      if (!isAuthenticated || authLoading) {
        throw new Error('用户未登录或会话已过期');
      }

      if (!currentUserId) {
        log.error('Unable to get user ID, cannot remove chat from index', undefined, 'ChatContext');
        throw new Error('未获取到用户ID');
      }

      log.info('Removing chat from index (soft delete):', id, 'ChatContext');

      const { error: apiError } = await api.delete(`/api/chat-history?id=${id}`, {
        headers: getAuthHeaders()
      });

      if (apiError) {
        throw new Error(apiError);
      }

              log.info('Chat removed from index:', id, 'ChatContext');

      // 从本地列表中移除
      if (isMounted.current) {
        setChatHistories(prevHistories =>
          prevHistories.filter(chat => chat.id !== id)
        );

        // 从索引列表中移除
        setChatIndexes(prevIndexes =>
          prevIndexes.filter(index => index.id !== id)
        );

        // 如果删除的是当前选中的聊天，则重置选中状态
        if (id === selectedChatId) {
          setSelectedChatId(null);
        }
      }

      return true;
    } catch (err) {
      log.error('Failed to remove chat from index:', err, 'ChatContext');
      if (isMounted.current) {
        setErrorMessage('从索引中移除聊天失败');
      }
      return false;
    } finally {
      if (isMounted.current) {
        setIsLoading(false);
      }
    }
  }, [isAuthenticated, authLoading, currentUserId, selectedChatId, getAuthHeaders]);

  // 更新聊天标题
  const updateChatTitle = useCallback(async (id: string, newTitle: string): Promise<boolean> => {
    try {
      setIsLoading(true);
      setErrorMessage(null);

      // 如果用户未登录，则不操作
      if (!isAuthenticated || authLoading) {
        throw new Error('用户未登录或会话已过期');
      }

      if (!currentUserId) {
        log.error('Unable to get user ID, cannot update chat title', undefined, 'ChatContext');
        throw new Error('未获取到用户ID');
      }

      log.info('Updating chat title:', { id, newTitle }, 'ChatContext');

      const { data, error: apiError } = await api.patch<ChatHistory>('/api/chat-history', {
        id,
        title: newTitle
      }, {
        headers: getAuthHeaders()
      });

      if (apiError) {
        throw new Error(apiError);
      }

      if (!data) {
        throw new Error('更新聊天标题失败：服务器未返回数据');
      }

              log.info('Chat title updated:', id, 'ChatContext');

      // 更新本地列表
      if (isMounted.current) {
        setChatHistories(prevHistories =>
          prevHistories.map(chat =>
            chat.id === id ? { ...chat, title: newTitle } : chat
          )
        );

        // 更新索引列表
        setChatIndexes(prevIndexes =>
          prevIndexes.map(index =>
            index.id === id ? { ...index, title: newTitle } : index
          )
        );
      }

      return true;
    } catch (err) {
      log.error('Failed to update chat title:', err, 'ChatContext');
      if (isMounted.current) {
        setErrorMessage('更新聊天标题失败');
      }
      return false;
    } finally {
      if (isMounted.current) {
        setIsLoading(false);
      }
    }
  }, [isAuthenticated, authLoading, currentUserId, getAuthHeaders]);

  // 当认证状态变化时，加载聊天历史索引
  useEffect(() => {
    // 只在认证状态有效且用户ID存在时加载，并且只在组件首次渲染或认证状态变化后加载一次
    if (!authLoading && isAuthenticated && currentUserId && !isInitializedRef.current) {
      isInitializedRef.current = true;
      // 使用索引API加载聊天列表
      loadChatIndexes();
    } else if (!authLoading && !isAuthenticated) {
      setChatHistories([]);
      setChatIndexes([]);
      setSelectedChatId(null);
      isInitializedRef.current = false;
    }
  }, [loadChatIndexes, isAuthenticated, authLoading, currentUserId]);

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      isMounted.current = false;
    };
  }, []);

  // 批量删除所有聊天
  const deleteAllChats = useCallback(async (): Promise<boolean> => {
    try {
      setIsLoading(true);
      setErrorMessage(null);

      // 如果用户未登录，则不操作
      if (!isAuthenticated || authLoading) {
        throw new Error('用户未登录或会话已过期');
      }

      if (!currentUserId) {
        log.error('Unable to get user ID, cannot delete all chats', undefined, 'ChatContext');
        throw new Error('未获取到用户ID');
      }

      log.info('Deleting all chat history...', undefined, 'ChatContext');

      const { data, error: apiError } = await api.delete('/api/chat-history/delete-all', {
        headers: getAuthHeaders()
      });

      if (apiError) {
        throw new Error(apiError);
      }

              log.info('All chat history deleted', undefined, 'ChatContext');

      // 清空本地列表
      if (isMounted.current) {
        setChatHistories([]);
        setChatIndexes([]);
        setSelectedChatId(null);
      }

      return true;
    } catch (err) {
      log.error('Failed to delete all chat history:', err, 'ChatContext');
      if (isMounted.current) {
        setErrorMessage('删除所有聊天历史失败');
      }
      return false;
    } finally {
      if (isMounted.current) {
        setIsLoading(false);
      }
    }
  }, [isAuthenticated, authLoading, currentUserId, getAuthHeaders]);

  return (
    <ChatContext.Provider
      value={{
        chatHistories,
        chatIndexes, // 新增：提供索引列表
        selectedChatId,
        isLoading,
        loadingChatId, // 新增：当前正在加载的聊天ID
        errorMessage,
        loadChatHistories,
        loadChatIndexes, // 新增：提供加载索引的方法
        selectChat,
        createNewChat,
        saveCurrentChat,
        deleteChat,
        deleteAllChats, // 新增：提供批量删除方法
        updateChatTitle
      }}
    >
      {children}
    </ChatContext.Provider>
  );
};

// 自定义钩子，用于访问聊天上下文
export const useChat = (): ChatContextState => {
  const context = useContext(ChatContext);
  if (context === undefined) {
    throw new Error('useChat must be used within a ChatProvider');
  }
  return context;
};