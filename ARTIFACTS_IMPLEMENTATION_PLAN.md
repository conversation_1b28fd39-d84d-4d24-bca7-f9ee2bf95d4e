# Artifacts 功能实现方案

本文档旨在为当前项目设计并规划一套可扩展的、统一的 Artifacts 系统。该系统能够支持从简单的内容展示（如 Iframe）到复杂的交互式应用（如实时代码沙箱）的多种场景。

## 前期准备：布局优化

在实施 Artifacts 功能之前，我们首先对聊天界面布局进行了优化：

### 布局改动说明
- **用户消息位置调整**：将用户消息从右侧移动到左侧，与 AI 消息保持一致的左对齐布局
- **视觉区分优化**：
  - 用户消息：使用蓝色主题 (`border-item-blue/30`, `item-glow-blue-subtle`) 和用户头像图标
  - AI 消息：保持原有的紫色/橙色主题，使用 AI 助手图标
- **空间优化**：左对齐布局为右侧 Artifacts 面板预留了更多空间，提供更好的用户体验

### 已完成的修改
- ✅ 修改 `src/app/components/ChatMessage.tsx` 中的布局逻辑
- ✅ 统一消息对齐方式为左对齐 (`justify-start`)
- ✅ 为用户消息添加专用头像和蓝色主题
- ✅ 保持 AI 助手消息的原有样式和头像

## 核心原则

本方案基于项目现有的 `streamText` + 前端 `toolComponentMap` 架构，秉持以下核心原则：

1.  **AI 逻辑稳定**: AI 的核心任务依然是处理和返回纯粹的 JSON 数据，以保证其决策的连贯性和准确性。
2.  **指令式渲染**: 我们仅在工具的返回数据中附加一个可选的 `artifact` 对象，用作给前端的"渲染指令"，告诉前端"除了在聊天流中展示，还请在侧边栏渲染这个东西"。
3.  **增量式开发**: 方案支持逐个工具地进行改造，而不会破坏现有功能。
4.  **前后端解耦**: 后端只提供"指令"，前端决定如何实现具体的 UI 展示，职责分明。

---

## 第一阶段：后端改造 - 扩展工具的数据契约

主要工作是在需要以 Artifact 形式展示的工具中，修改其返回的 JSON 结构。

### 1. 定义统一的 `artifact` 对象结构

所有希望在侧边栏展示的工具，其返回的 JSON 中都应包含一个 `artifact` 对象。

```typescript
interface ArtifactInstruction {
  id: string;          // 唯一ID，用于前端渲染和管理
  type: 'iframe' | 'sandbox' | 'html'; // Artifact 的类型
  title: string;       // 显示在 Artifact 面板顶部的标题
  props: Record<string, any>; // 渲染对应前端组件所需的所有 props
}
```

### 2. 修改目标工具的 `execute` 函数

#### 示例 A：简单的 GIS 地图工具 (返回 `iframe` 类型)

```typescript
// in src/tools/gis-tool.ts or similar
import { v4 as uuidv4 } from 'uuid';

// ...
async execute(args) {
  const { url, title } = await prepareGisMap(args);

  // AI 只需要知道 URL 和标题就够了
  // 但我们附加了 artifact 指令给前端
  return {
    url: url,
    title: title,
    artifact: {
      id: uuidv4(),
      type: 'iframe',
      title: title,
      props: {
        src: url
      }
    }
  };
}
```

#### 示例 B：复杂的 Sandbox 工具 (返回 `sandbox` 类型)

```typescript
// in src/tools/sandbox-tool.ts or similar
import { v4 as uuidv4 } from 'uuid';

// ...
async execute(args) {
  // 启动沙箱，获取会话ID和访问URL
  const { sessionId, sandboxUrl } = await sandboxManager.create(args);

  return {
    sessionId: sessionId,
    sandboxUrl: sandboxUrl,
    message: '我已经为您创建了沙箱环境，正在安装依赖...',
    artifact: {
      id: sessionId, // 使用 sessionId 作为唯一ID
      type: 'sandbox',
      title: 'Web 应用实时沙箱',
      props: {
        initialUrl: sandboxUrl,
        sessionId: sessionId // 用于后续的 WebSocket 通信
      }
    }
  };
}
```

---

## 第二阶段：前端状态管理 - 创建全局 Artifacts Store

我们将使用 [Zustand](https://github.com/pmndrs/zustand) 来管理全局 Artifacts 状态。

### 1. 安装 Zustand

```bash
npm install zustand
```

### 2. 创建 `useArtifactsStore.ts`

```typescript
// src/stores/useArtifactsStore.ts
import { create } from 'zustand';

interface Artifact {
  id: string;
  type: string;
  title: string;
  props: Record<string, any>;
}

interface ArtifactsState {
  artifacts: Artifact[];
  addArtifact: (artifact: Artifact) => void;
  removeArtifact: (id: string) => void;
  clearArtifacts: () => void;
}

export const useArtifactsStore = create<ArtifactsState>((set) => ({
  artifacts: [],
  addArtifact: (artifact) =>
    set((state) => {
      // 如果已存在相同ID的artifact，则替换它，否则添加
      const existingIndex = state.artifacts.findIndex((a) => a.id === artifact.id);
      if (existingIndex > -1) {
        const newArtifacts = [...state.artifacts];
        newArtifacts[existingIndex] = artifact;
        return { artifacts: newArtifacts };
      }
      return { artifacts: [...state.artifacts, artifact] };
    }),
  removeArtifact: (id) =>
    set((state) => ({
      artifacts: state.artifacts.filter((a) => a.id !== id),
    })),
  clearArtifacts: () => set({ artifacts: [] }),
}));
```

---

## 第三阶段：前端组件实现 - 消费数据并渲染 UI

### 1. 修改 `ChatMessage.tsx` 以触发状态更新

让 `ChatMessage` 负责检查工具结果，并将"渲染指令"发送到全局 Store。

```tsx
// src/app/components/ChatMessage.tsx
import { useEffect } from 'react';
import { useArtifactsStore } from '@/stores/useArtifactsStore';
// ... 其他 imports

export function ChatMessage({ message }: { message: Message }) {
  const { addArtifact } = useArtifactsStore();

  useEffect(() => {
    message.content.forEach(part => {
      if (part.type === 'tool-result' && part.result?.artifact) {
        // 发现 artifact 指令，添加到全局 store
        addArtifact(part.result.artifact);
      }
    });
  }, [message.content, addArtifact]);

  // ... 您现有的渲染逻辑保持不变 ...
}
```

### 2. 创建 Artifacts 面板和渲染器

**`ArtifactRenderer.tsx` (根据 `type` 渲染不同组件)**

```tsx
// src/app/components/artifacts/ArtifactRenderer.tsx
import { IframeArtifact } from './IframeArtifact';
import { SandboxArtifact } from './SandboxArtifact';

export function ArtifactRenderer({ artifact }) {
  switch (artifact.type) {
    case 'iframe':
      return <IframeArtifact {...artifact.props} />;
    case 'sandbox':
      return <SandboxArtifact {...artifact.props} />;
    // case 'html':
    //   return <HtmlArtifact {...artifact.props} />;
    default:
      return <div>未知的 Artifact 类型: {artifact.type}</div>;
  }
}
```

**`ArtifactsPanel.tsx` (主面板)**

```tsx
// src/app/components/artifacts/ArtifactsPanel.tsx
import { useArtifactsStore } from '@/stores/useArtifactsStore';
import { ArtifactRenderer } from './ArtifactRenderer';

export function ArtifactsPanel() {
  const { artifacts, removeArtifact } = useArtifactsStore();

  if (artifacts.length === 0) {
    return null; // 如果没有 artifacts，则不渲染任何东西
  }

  return (
    <div className="flex flex-col h-full border-l bg-gray-50">
      {/* 未来可以实现 Tab 切换来显示不同的 artifact */}
      {artifacts.map((artifact) => (
        <div key={artifact.id} className="flex-grow flex flex-col">
           <div className="p-2 border-b font-semibold bg-white flex justify-between items-center">
             <span>{artifact.title}</span>
             <button onClick={() => removeArtifact(artifact.id)} className="text-gray-500 hover:text-gray-800">
                &times;
             </button>
           </div>
           <div className="p-4 flex-grow">
             <ArtifactRenderer artifact={artifact} />
           </div>
        </div>
      ))}
    </div>
  );
}
```

---

## 第四阶段：整合到主布局

最后，在您的主聊天页面修改布局，以容纳 `ArtifactsPanel`。

```tsx
// src/app/page.tsx (或您的主聊天布局文件)
import { Chat } from '@/app/components/Chat';
import { ArtifactsPanel } from '@/app/components/artifacts/ArtifactsPanel';

export default function Home() {
  return (
    <div className="flex h-screen w-full">
      <div className="flex-grow h-full">
        {/* 这是您现有的聊天组件 */}
        <Chat />
      </div>
      <div className="w-1/2 h-full"> {/* 或者您希望的任何宽度 */}
        <ArtifactsPanel />
      </div>
    </div>
  );
}
``` 