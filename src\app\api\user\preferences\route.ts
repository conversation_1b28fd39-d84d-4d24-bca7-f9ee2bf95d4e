import { NextRequest, NextResponse } from 'next/server';
import { getUserIdFromRequest } from '@/utils/authUtils';
import { serverUserContextManager } from '@/utils/serverUserContext';

/**
 * GET - 获取用户偏好设置
 */
export async function GET(req: NextRequest) {
  try {
    // 获取当前用户ID
    const userId = await getUserIdFromRequest(req);
    
    // 如果未登录，返回错误
    if (!userId) {
      console.log('[用户偏好API] 未获取到用户ID，返回401错误');
      return NextResponse.json(
        { error: '用户未登录或会话已过期' },
        { status: 401 }
      );
    }
    
    console.log('[用户偏好API] 获取用户偏好，用户ID:', userId);
    
    // 获取用户上下文
    const userContext = await serverUserContextManager.getUserContext(userId);
    
    // 如果找不到用户上下文，尝试初始化
    if (!userContext) {
      console.log('[用户偏好API] 未找到用户上下文，尝试获取用户偏好');
      // 仅获取偏好设置，而不初始化完整上下文
      const preferences = await serverUserContextManager.updateUserPreferences(userId, {});
      return NextResponse.json(preferences);
    }
    
    // 返回用户偏好
    return NextResponse.json(userContext.preferences);
  } catch (error) {
    console.error('[用户偏好API] 获取用户偏好失败:', error);
    return NextResponse.json(
      { error: '获取用户偏好失败' },
      { status: 500 }
    );
  }
}

/**
 * PATCH - 更新用户偏好设置
 */
export async function PATCH(req: NextRequest) {
  try {
    // 获取当前用户ID
    const userId = await getUserIdFromRequest(req);
    
    // 如果未登录，返回错误
    if (!userId) {
      console.log('[用户偏好API] 未获取到用户ID，返回401错误');
      return NextResponse.json(
        { error: '用户未登录或会话已过期' },
        { status: 401 }
      );
    }
    
    // 解析请求数据
    const data = await req.json();
    
    console.log('[用户偏好API] 更新用户偏好，用户ID:', userId, '数据:', data);
    
    // 更新用户偏好
    const updatedPreferences = await serverUserContextManager.updateUserPreferences(userId, data);
    
    // 返回更新后的偏好
    return NextResponse.json(updatedPreferences);
  } catch (error) {
    console.error('[用户偏好API] 更新用户偏好失败:', error);
    return NextResponse.json(
      { error: '更新用户偏好失败' },
      { status: 500 }
    );
  }
} 