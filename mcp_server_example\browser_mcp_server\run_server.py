import os
import subprocess
import sys
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
)
logger = logging.getLogger("browser_mcp_server_launcher")

# Add parent directory to PATH
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

def main():
    """Start the Browser MCP Server"""
    try:
        # Check for required environment variables
        from dotenv import load_dotenv
        load_dotenv()
        
        if not os.getenv("OPENAI_API_KEY"):
            logger.warning("OPENAI_API_KEY environment variable is not set. Please set it or add to .env file.")
            response = input("Do you want to continue without setting OPENAI_API_KEY? (y/n): ")
            if response.lower() != 'y':
                logger.info("Exiting as requested.")
                sys.exit(1)
        
        # Install required dependencies if needed
        if not is_installed("browser-use") or not is_installed("mcp"):
            logger.info("Installing required packages...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", os.path.join(current_dir, "requirements.txt")])
        
        # Install Playwright browsers if needed
        try:
            import playwright
            try:
                # Check if browsers are installed
                with open(os.devnull, 'w') as devnull:
                    subprocess.check_call(
                        [sys.executable, "-m", "playwright", "install", "--help"],
                        stdout=devnull, stderr=devnull
                    )
            except subprocess.SubprocessError:
                logger.info("Installing Playwright browsers...")
                subprocess.check_call([sys.executable, "-m", "playwright", "install"])
        except ImportError:
            logger.warning("Playwright not found. Installing...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", "playwright"])
            subprocess.check_call([sys.executable, "-m", "playwright", "install"])
        
        # Parse command line arguments
        import argparse
        parser = argparse.ArgumentParser(description="Browser MCP Server")
        parser.add_argument("--host", default="127.0.0.1", help="Bind socket to this host")
        parser.add_argument("--port", type=int, default=8001, help="Bind socket to this port")
        parser.add_argument("--transport", choices=["stdio", "sse"], default="sse", help="Transport protocol (stdio or sse)")
        args = parser.parse_args()
        
        # Start MCP server
        logger.info(f"Starting Browser MCP Server with {args.transport} transport on {args.host}:{args.port}")
        from mcp_server.browser_mcp_server import mcp
        
        # 根据官方文档运行服务器
        if args.transport == "sse":
            # 使用starlette和uvicorn直接通过MCP的sse_app()运行
            import uvicorn
            from starlette.applications import Starlette
            from starlette.routing import Mount
            
            # 创建一个Starlette应用并挂载MCP的SSE应用
            app = Starlette(routes=[Mount("/", app=mcp.sse_app())])
            
            # 使用uvicorn运行
            uvicorn.run(
                app, 
                host=args.host, 
                port=args.port,
                timeout_keep_alive=300,  # 保持连接超时
                timeout_graceful_shutdown=300  # 优雅关闭超时
            )
        else:
            # 对于stdio模式，直接使用run方法
            mcp.run(transport="stdio")
        
    except Exception as e:
        logger.error(f"Error starting server: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

def is_installed(package):
    """Check if a package is installed"""
    try:
        __import__(package)
        return True
    except ImportError:
        return False

if __name__ == "__main__":
    main() 