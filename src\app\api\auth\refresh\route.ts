import { NextRequest, NextResponse } from 'next/server';
import axios from 'axios';

// IAM配置从环境变量获取
const IAM_CONFIG = {
  BASE_URL: process.env.NEXT_PUBLIC_IAM_ENDPOINT,
  TOKEN_PATH: '/oauth2/token'
};

export async function POST(req: NextRequest) {
  try {
    // 解析请求体
    const { refreshToken } = await req.json();
    
    if (!refreshToken) {
      return NextResponse.json(
        { error: 'Missing refresh token' },
        { status: 400 }
      );
    }
    
    // 获取客户端凭据（从环境变量）
    const clientId = process.env.NEXT_PUBLIC_IAM_CLIENT_ID;
    const clientSecret = process.env.IAM_CLIENT_SECRET;
    
    if (!clientId || !clientSecret) {
      console.error('Missing IAM client credentials', {
        hasClientId: !!clientId,
        hasClientSecret: !!clientSecret,
        envKeys: Object.keys(process.env).filter(key => key.includes('IAM'))
      });
      return NextResponse.json(
        { error: 'Server configuration error' },
        { status: 500 }
      );
    }

    // 验证IAM端点配置
    if (!IAM_CONFIG.BASE_URL) {
      console.error('Missing IAM endpoint configuration');
      return NextResponse.json(
        { error: 'Server configuration error: Missing IAM endpoint' },
        { status: 500 }
      );
    }
    
    // 构建完整的令牌端点URL
    const tokenEndpoint = `${IAM_CONFIG.BASE_URL.replace(/\/$/, '')}${IAM_CONFIG.TOKEN_PATH}`;
    
    // 准备Basic认证头
    const authStr = `${clientId}:${clientSecret}`;
    const authHeader = Buffer.from(authStr).toString('base64');
    
    // 请求体
    const params = new URLSearchParams();
    params.append('grant_type', 'refresh_token');
    params.append('refresh_token', refreshToken);
    params.append('scope', 'openid');
    
    console.log('Attempting to refresh token at:', tokenEndpoint);
    
    // 发送请求到IAM令牌端点
    const response = await axios.post(
      tokenEndpoint,
      params,
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Authorization': `Basic ${authHeader}`
        }
      }
    );
    
    // 返回令牌响应
    return NextResponse.json(response.data);
  } catch (error) {
    console.error('Token refresh error:', error);
    
    // 处理特定的错误响应
    if (axios.isAxiosError(error) && error.response) {
      const { status, data } = error.response;
      console.error('IAM server response:', {
        status,
        data
      });
      return NextResponse.json(
        { error: data.error || 'Token refresh failed', details: data },
        { status }
      );
    }
    
    // 通用错误处理
    return NextResponse.json(
      { error: 'Failed to refresh token' },
      { status: 500 }
    );
  }
} 