import React from 'react';

interface KnowledgeCardEmbedProps {
  toolInvocation: any;
}

const KnowledgeCardEmbed: React.FC<KnowledgeCardEmbedProps> = ({ toolInvocation }) => {
  // 解析知识库ID
  let kbIds: string[] = [];
  if (toolInvocation?.args?.kbId) {
    if (Array.isArray(toolInvocation.args.kbId)) {
      kbIds = toolInvocation.args.kbId;
    } else if (typeof toolInvocation.args.kbId === 'string') {
      kbIds = [toolInvocation.args.kbId];
    }
  }
  
  const uniqueKBs = Array.from(new Set(kbIds));
  const kbText = uniqueKBs.length > 0 ? uniqueKBs.join(', ') : 'Unknown';

  // 解析引用文档
  const sourcesArr = Array.isArray(toolInvocation?.result) ? toolInvocation.result : [];
  const docList = sourcesArr.map((item: any) => item.source_document).filter(Boolean);
  const uniqueDocs = Array.from(new Set(docList));
  const docCount = uniqueDocs.length;

  return (
    <div className="embed-card kb-search p-2 my-1 bg-blue-800/20 rounded border-l-2 border-blue-600/30 border-t border-r border-b border-blue-700/20">
      <div className="flex items-center" style={{ whiteSpace: 'nowrap', width: '100%' }}>
        <div className="mr-2 text-blue-400 flex-shrink-0">
          <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
          </svg>
        </div>
        <div className="text-xs text-blue-300 flex-shrink-0 mr-2">
          KB:
        </div>
        <div className="text-xs text-blue-400 overflow-hidden text-ellipsis mr-2" style={{ textOverflow: 'ellipsis', overflow: 'hidden', whiteSpace: 'nowrap' }}>
          {kbText}
        </div>
        <div className="text-xs text-gray-400 overflow-hidden text-ellipsis" style={{ textOverflow: 'ellipsis', overflow: 'hidden', whiteSpace: 'nowrap' }}>
          {docCount > 0 ? `${docCount} document${docCount !== 1 ? 's' : ''} referenced` : 'No documents found'}
        </div>
      </div>
    </div>
  );
};

export default KnowledgeCardEmbed; 