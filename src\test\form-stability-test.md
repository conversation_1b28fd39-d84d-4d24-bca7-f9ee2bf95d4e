# 表单稳定性优化测试

## 问题描述
在 SuperAgent 模式下，AI 会在最后输出额外的文本，导致 `requireUserInputTool` 显示的表单因为流式更新而不断重新渲染，造成表单闪烁和用户输入丢失。

## 优化措施

### 1. ChatMessage.tsx 优化
- **优化 useMemo 依赖项**：从依赖整个 `toolInvocation` 对象改为只依赖关键属性
- **精确依赖项**：
  - `JSON.stringify(props.toolInvocation.args)` - 表单配置
  - `props.toolInvocation.toolCallId` - 工具调用ID
  - `props.toolInvocation.state` - 状态变化
  - `props.toolInvocation.result?.canceled` - 取消状态
  - `props.toolInvocation.result?.__submitted` - 提交状态

### 2. EmbeddedChat.tsx 优化
- 应用相同的 useMemo 依赖项优化

### 3. DynamicForm.tsx 优化
- **添加 React.memo**：使用 `React.memo` 包装表单组件
- **自定义比较函数**：只在关键属性变化时重新渲染
- **表单状态稳定性**：添加 `isInitialized` 标记，防止不必要的重新初始化
- **保护用户输入**：如果表单已有值，不重新初始化

## 测试场景

### 测试 1：基本表单稳定性
1. 触发 requireUserInputTool
2. 在表单中输入一些内容
3. 观察 AI 继续输出时表单是否闪烁
4. 验证用户输入是否保持

### 测试 2：SuperAgent 模式
1. 使用 SuperAgent 模式
2. 触发需要用户输入的工具
3. 在主 agent 最后输出时观察表单稳定性

### 测试 3：表单状态变化
1. 提交表单后验证只读模式显示
2. 取消表单后验证取消状态显示
3. 从历史记录加载时验证状态正确

## 预期效果
- 表单在 AI 流式输出时不再闪烁
- 用户输入内容不会丢失
- 表单焦点保持稳定
- 性能提升，减少不必要的重新渲染
