import { ToolDefinition } from './types';

export abstract class BaseTool {
  protected context: Record<string, any> = {};

  /**
   * 更新工具上下文
   */
  async updateContext(context: Record<string, any> = {}): Promise<void> {
    this.context = { ...this.context, ...context };
  }

  /**
   * 获取工具定义
   */
  abstract getTools(): ToolDefinition[];

  /**
   * 获取系统提示（如果有）
   */
  static getSystemPrompt(): string | null {
    return null;
  }
} 