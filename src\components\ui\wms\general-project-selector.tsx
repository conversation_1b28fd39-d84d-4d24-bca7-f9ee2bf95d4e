'use client';

import * as React from 'react';
import { useState, useEffect, useRef } from 'react';
import { User, Search, Loader2 } from 'lucide-react';
// @ts-ignore 忽略类型检查
import { debounce } from 'lodash';
import { wmsApi } from '@/utils/wmsApiClient';

import { Input } from '@/components/ui/input';
import { cn } from '@/lib/utils';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
  getCyberAgentPortalContainer,
} from "@/components/ui/select";

interface ProjectOption {
  id: string;
  name: string;
  customerId?: string;
  customerName?: string;
  hourlyRate?: number;
  status?: string;
}

interface GeneralProjectSelectorProps {
  value?: string;
  onChange: (value: string, projectData?: ProjectOption) => void;
  placeholder?: string;
  disabled?: boolean;
  required?: boolean;
  facilityId?: string;
  apiHeaders?: Record<string, string>;
  defaultValue?: string;
}

// 定义WMS API响应类型
interface WmsApiResponse {
  success: boolean;
  msg?: string;
  data?: any[] | {
    list?: any[];
    [key: string]: any;
  };
  [key: string]: any;
}

export function GeneralProjectSelector({
  value,
  onChange,
  placeholder = 'Select project',
  disabled = false,
  required = false,
  apiHeaders = {},
  defaultValue
}: GeneralProjectSelectorProps) {
  // Fixed API path
  const API_PATH = 'wms-bam/task/general-project/search-by-paging';
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [options, setOptions] = useState<ProjectOption[]>([]);
  const [selectedProject, setSelectedProject] = useState<ProjectOption | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const searchInputRef = useRef<HTMLInputElement>(null);
  const initialLoadRef = useRef(false);

  // 初始化时如果有defaultValue但没有value，则加载defaultValue对应的项目
  useEffect(() => {
    if (!initialLoadRef.current && defaultValue && !value) {
      initialLoadRef.current = true;
      fetchProjectByName(defaultValue).then(project => {
        if (project) {
          onChange(project.name, project);
        }
      });
    }
  }, [defaultValue, value]);

  // 当value变化时，如果已有选中的项目name与新value不同，清除已选项目数据
  useEffect(() => {
    if (value !== selectedProject?.name) {
      setSelectedProject(null);
    }
  }, [value]);

  // 如果有value但没有selectedProject，尝试获取项目数据
  useEffect(() => {
    if (value && !selectedProject && !disabled) {
      fetchProjectByName(value);
    }
  }, [value, selectedProject, disabled]);

  // 当下拉框打开时自动聚焦搜索框
  useEffect(() => {
    if (open && searchInputRef.current) {
      setTimeout(() => {
        searchInputRef.current?.focus();
      }, 100);
    }
  }, [open]);

  // 提取项目数据的函数
  const extractProjectData = (project: any): ProjectOption => {
    return {
      id: project.id || '',
      name: project.projectName || 'Unknown Project',
      customerId: project.customerId || '',
      customerName: project.customerName || '',
      hourlyRate: project.hourlyRate || 0,
      status: project.status || ''
    };
  };

  // 解析API响应的函数
  const parseApiResponse = (response: any): any[] => {
    // 检查response.data是数组的情况
    if (Array.isArray(response.data)) {
      return response.data;
    }

    // 检查response.data.list的情况
    if (response.data && Array.isArray(response.data.list)) {
      return response.data.list;
    }

    // 检查response.data是对象但包含data字段的情况
    if (response.data && response.data.data && Array.isArray(response.data.data)) {
      return response.data.data;
    }

    // 直接检查response是否包含data字段为数组的情况
    if (response.data && Array.isArray(response.data)) {
      return response.data;
    }

    console.warn('Could not extract project list from API response:', response);
    return [];
  };

  // 通过名称获取项目信息
  const fetchProjectByName = async (projectName: string): Promise<ProjectOption | null> => {
    try {
      setLoading(true);
      console.log('Fetching project by name:', projectName);
      const response = await wmsApi.post<WmsApiResponse>(API_PATH, {
        projectNameLike: projectName,
        currentPage: 1,
        pageSize: 20
      });

      console.log('Fetch project by name response:', response);

      if (response.success) {
        const projectList = parseApiResponse(response);
        console.log('Parsed project list:', projectList);

        // 尝试精确匹配项目名称
        let project = projectList.find((p: any) => {
          return p.projectName && p.projectName.toString() === projectName.toString();
        });

        // 如果没有精确匹配，尝试模糊匹配
        if (!project && projectList.length > 0) {
          project = projectList[0]; // 取第一个结果
        }

        if (project) {
          const projectOption = extractProjectData(project);
          console.log('Found project:', projectOption);
          setSelectedProject(projectOption);
          return projectOption;
        } else {
          console.warn('Project not found with name:', projectName);
        }
      } else {
        console.error('Error fetching project data:', response.msg);
      }
    } catch (error) {
      console.error('Error fetching project data:', error);
    } finally {
      setLoading(false);
    }
    return null;
  };

  // 搜索项目的函数
  const searchProjects = async (query: string) => {
    if (!query.trim()) {
      setOptions([]);
      return;
    }

    try {
      setLoading(true);
      console.log('Searching projects with query:', query);

      const response = await wmsApi.post<WmsApiResponse>(API_PATH, {
        currentPage: 1,
        pageSize: 20,
        projectNameLike: query
      });

      console.log('Project search API response:', response);

      if (response.success) {
        const projectList = parseApiResponse(response);
        console.log('Parsed project list for search:', projectList);

        if (projectList.length > 0) {
          const projects = projectList.map((project: any) => extractProjectData(project));
          console.log('Mapped projects:', projects);
          setOptions(projects);
        } else {
          console.warn('No projects found in the response');
          setOptions([]);
        }
      } else {
        console.warn('No projects found or API error:', response.msg);
        setOptions([]);
      }
    } catch (error) {
      console.error('Error searching projects:', error);
      setOptions([]);
    } finally {
      setLoading(false);
    }
  };

  // 防抖搜索
  const debouncedSearch = useRef(
    debounce((query: string) => {
      searchProjects(query);
    }, 300)
  ).current;

  // 处理搜索输入变化
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchQuery(value);
    debouncedSearch(value);
  };

  // 处理项目选择
  const handleProjectSelect = (projectId: string) => {
    const project = options.find(opt => opt.id === projectId);
    if (project) {
      setSelectedProject(project);
      // 传递 name 而不是 id 作为值
      onChange(project.name, project);
      // 重置搜索状态
      setSearchQuery('');
      setOptions([]);
      setOpen(false);
    }
  };

  // 打开下拉框时触发搜索
  const handleOpenChange = (isOpen: boolean) => {
    setOpen(isOpen);

    // 如果关闭下拉框，清空搜索和结果
    if (!isOpen) {
      setSearchQuery('');
      setOptions([]);
    }
    // 如果打开下拉框且有搜索关键字，执行搜索
    else if (isOpen && searchQuery) {
      debouncedSearch(searchQuery);
    }
  };

  return (
    <div className="relative w-full">
      <Select
        value={selectedProject?.id || ''}
        onValueChange={handleProjectSelect}
        open={open}
        onOpenChange={handleOpenChange}
        disabled={disabled}
      >
        <SelectTrigger
          className={cn(
            "w-full h-10 rounded-md bg-transparent",
            "border border-item-gray-700/50",
            "hover:border-item-purple focus:border-item-purple",
            "flex items-center justify-between px-3 py-2 text-sm text-white",
            "focus:outline-none focus:ring-1 focus:ring-item-purple",
            "transition-all duration-200",
            "data-[placeholder]:text-item-gray-400"
          )}
        >
          <SelectValue placeholder={placeholder}>
            {loading ? (
              <div className="flex items-center">
                <Loader2 className="mr-2 h-4 w-4 animate-spin text-item-gray-400" />
                <span>Loading...</span>
              </div>
            ) : selectedProject ? (
              <div className="flex items-center">
                <User className="mr-2 h-4 w-4 text-item-gray-400" />
                <span>{selectedProject.name}</span>
              </div>
            ) : null}
          </SelectValue>
        </SelectTrigger>
        <SelectContent
          className={cn(
            "border border-item-gray-700/70 bg-item-bg-card text-white",
            "rounded-md shadow-lg min-w-[300px] overflow-hidden",
            "animate-in fade-in-80 zoom-in-95"
          )}
          portalContainer={getCyberAgentPortalContainer()}
        >
          <div className="flex items-center px-3 py-2 border-b border-item-gray-700/50">
            <Search className="mr-2 h-4 w-4 shrink-0 text-item-gray-400" />
            <Input
              ref={searchInputRef}
              className="h-8 border-0 p-0 bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0 text-white placeholder:text-item-gray-400"
              placeholder="Search projects..."
              value={searchQuery}
              onChange={handleSearchChange}
            />
          </div>

          <div className="max-h-[320px] overflow-auto scrollbar-thin scrollbar-thumb-item-gray-600 scrollbar-track-transparent">
            {loading ? (
              <div className="flex items-center justify-center py-6">
                <Loader2 className="h-6 w-6 animate-spin text-item-gray-400" />
              </div>
            ) : options.length > 0 ? (
              <SelectGroup>
                <SelectLabel className="px-3 text-xs font-medium text-item-gray-400">Projects</SelectLabel>
                {options.map((project) => (
                  <SelectItem
                    key={project.id}
                    value={project.id}
                    className={cn(
                      "py-2 px-3 cursor-pointer text-white",
                      "focus:bg-item-purple focus:text-white",
                      "data-[highlighted]:bg-item-purple data-[highlighted]:text-white"
                    )}
                  >
                    <div className="flex items-center">
                      <User className="mr-2 h-4 w-4 text-item-gray-400" />
                      <div>
                        <div>{project.name}</div>
                        {project.customerName && (
                          <div className="text-xs text-item-gray-400">{project.customerName}</div>
                        )}
                      </div>
                    </div>
                  </SelectItem>
                ))}
              </SelectGroup>
            ) : searchQuery ? (
              <div className="py-6 text-center text-sm text-item-gray-400">
                No projects found
              </div>
            ) : (
              <div className="py-6 text-center text-sm text-item-gray-400">
                Type to search projects
              </div>
            )}
          </div>
        </SelectContent>
      </Select>
    </div>
  );
}
