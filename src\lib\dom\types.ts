export interface DOMBaseNode {
    is_visible: boolean;
    parent?: DOMElementNode;
}

export interface DOMTextNode extends DOMBaseNode {
    text: string;
    type: 'TEXT_NODE';
}

export interface DOMElementNode extends DOMBaseNode {
    tag_name: string;
    xpath: string;
    attributes: Record<string, string>;
    children: DOMBaseNode[];
    is_interactive: boolean;
    is_top_element: boolean;
    shadow_root: boolean;
    highlight_index?: number;
}

export interface DOMState {
    url: string;
    tabs: string[];
    element_tree: DOMElementNode;
    selector_map: Record<string, DOMElementNode>;
}

// 新的动作类型定义
export type DOMActionType = 
    | { click_element: { index: number } }
    | { input_text: { index: number, text: string } }
    | { send_keys: { index: number, keys: string } }
    | { scroll_to_text: { text: string } }
    | { select_dropdown_option: { index: number, text: string } }
    | { get_dropdown_options: { index: number } }
    | { scroll: { amount?: number } };

export interface DOMOperationResult {
    success: boolean;
    error?: string;
    extracted_content?: string;
    include_in_memory?: boolean;
} 