import { tool } from 'ai';
import { z } from 'zod';
import { v4 as uuidv4 } from 'uuid';

/**
 * 获取登录 token
 */
async function getAuthToken(): Promise<{ success: boolean; token?: string; error?: string }> {
  const username = process.env.WISE_USERNAME || 'easonc';
  const password = process.env.WISE_PASSWORD;
  
  if (!password) {
    return { success: false, error: 'WISE_PASSWORD environment variable is required' };
  }
  
  try {
    const baseUrl = process.env.WISE_BASEURL || 'https://stage.logisticsteam.com';
    const loginUrl = `${baseUrl}/shared/idm-app/user/login`;
    
    const response = await fetch(loginUrl, {
      method: 'POST',
      headers: {
        'content-type': 'application/json;charset=UTF-8'
      },
      body: JSON.stringify({
        username,
        password
      })
    });
    
    if (!response.ok) {
      return { success: false, error: `Login failed: ${response.status} ${response.statusText}` };
    }
    
    const data = await response.json();
    
    console.log('Login response:', data);
    // 检查登录是否成功并获取 oAuthToken
    if (!data.success) {
      return { success: false, error: 'Login failed: Invalid credentials or server error' };
    }
    
    if (!data.oAuthToken) {
      return { success: false, error: 'No oAuthToken received from login API' };
    }
    
    return { success: true, token: data.oAuthToken };
  } catch (error) {
    console.error('Failed to get auth token:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error occurred' };
  }
}

/**
 * GIS 地图工具
 * 使用 Vercel AI SDK 的 tool 函数实现，支持 Artifacts 功能
 */
export const gisTool = tool({
  description: 'Display GIS map with facility/terminal/truck information in an artifact. Functions: currentInv (Current Inventory), worker (Worker position), tractorTrailer (Tractor Trailer), truck (Truck). Available facilities for currentInv and worker: valleyview (F1), fontana (ORG-7759), joliet (ORG-61213), willow (ORG-7941), seabrook (F19), savannah (ORG-35184)',
  parameters: z.object({
    function: z.enum(['currentInv', 'worker', 'tractorTrailer', 'truck']).describe('The GIS function to display. Options: currentInv (Current Inventory), worker (Worker position), tractorTrailer (Tractor Trailer), truck (Truck)'),
    facilityId: z.string().optional().describe('The facility ID to display on the GIS map. Required only for currentInv and worker functions. Options: F1 (valleyview), ORG-7759 (fontana), ORG-61213 (joliet), ORG-7941 (willow), F19 (seabrook), ORG-35184 (savannah)')
  }),
  execute: async ({ function: gisFunction, facilityId }) => {
    console.log('GISTool.execute called with args:', JSON.stringify({ function: gisFunction, facilityId }));
  
    // 获取动态 token
    const authResult = await getAuthToken();
    
    // 验证 facilityId 对于需要它的函数是否存在
    if ((gisFunction === 'currentInv' || gisFunction === 'worker') && !facilityId) {
      return {
        success: false,
        function: gisFunction,
        message: `facilityId is required for function '${gisFunction}'. Please provide a valid facility ID.`
      };
    }
    
    if (!authResult.success) {
      // 登录失败，返回错误信息给 AI
      return {
        success: false,
        function: gisFunction,
        facilityId: facilityId,
        message: `Failed to get access for GIS`
      };
    }
    
    // 构建 GIS URL
    const baseUrl = process.env.WISE_BASEURL || 'https://stage.logisticsteam.com';
    const ssoUrl = `${baseUrl}/#/sso`;
    const params = new URLSearchParams({
      token: authResult.token!,
      directView: 'gis.resources',
      isFull: 'true',
      function: gisFunction
    });
    
    // 只有当 facilityId 存在且函数需要时才添加
    if (facilityId && (gisFunction === 'currentInv' || gisFunction === 'worker')) {
      params.append('facilityId', facilityId);
    }
    
    const url = `${ssoUrl}?${params.toString()}`;
    const title = facilityId ? `GIS Map - ${gisFunction} (${facilityId})` : `GIS Map - ${gisFunction}`;
    
    console.log('GIS URL:', url);
    // 构建响应数据
    const gisData = {
      success: true,
      url: url,
      title: title,
      instructForAI: "Don't show the detail, like the URL to the user, just tell the user that artifact is ready",
      function: gisFunction,
      facilityId: facilityId,
      // 添加 artifact 对象，指示前端在侧边栏渲染 iframe
      artifact: {
        id: uuidv4(),
        type: 'iframe' as const,
        title: title,
        props: {
          src: url,
          width: '100%',
          height: '100%'
        }
      }
    };
    
    return gisData;
  }
});