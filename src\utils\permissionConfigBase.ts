/**
 * 权限配置基础文件
 * 包含前端和后端共享的权限定义和配置
 */

// 定义系统角色
export enum UserRole {
  USER = 'user',           // 普通用户
  ADMIN = 'admin',         // 管理员
  REPORT_VIEWER = 'report_viewer', // 报表查看者
}

// IAM 角色映射到系统角色
export const IAM_ROLE_MAPPING: Record<string, UserRole> = {
  'Admin': UserRole.ADMIN,
  // 可以添加更多 IAM 角色到系统角色的映射
};

// 定义受保护的路由及其所需权限
export const PROTECTED_ROUTES: Record<string, UserRole[]> = {
  '/admin/memories': [UserRole.ADMIN],
  '/chat-reports': [UserRole.ADMIN, UserRole.REPORT_VIEWER],
  '/admin/settings': [UserRole.ADMIN],
  // 添加更多受保护的路由
};

// 需要角色权限的 API 路由
export const ROLE_PROTECTED_API_ROUTES: Record<string, UserRole[]> = {
  '/api/chat-report': [UserRole.ADMIN, UserRole.REPORT_VIEWER],
  '/api/chat-report/generate-missing': [UserRole.ADMIN],
  // 添加更多需要保护的 API 路由
};

/**
 * 从 IAM 角色名称获取系统角色
 * @param iamRoles IAM角色名称数组
 * @returns 系统角色数组
 */
export const getUserRolesByIamRoles = (iamRoles: string[] | undefined): UserRole[] => {
  if (!iamRoles || iamRoles.length === 0) {
    return [UserRole.USER];
  }

  const roles: UserRole[] = [UserRole.USER]; // 所有登录用户都有基本用户角色

  // 将 IAM 角色映射到系统角色
  iamRoles.forEach(iamRole => {
    // 特殊处理 "Admin" 角色
    if (iamRole === 'Admin') {
      if (!roles.includes(UserRole.ADMIN)) {
        roles.push(UserRole.ADMIN);
      }
      return;
    }

    // 检查是否有映射
    const mappedRole = IAM_ROLE_MAPPING[iamRole];
    if (mappedRole) {
      if (!roles.includes(mappedRole)) {
        roles.push(mappedRole);
      }
    }
  });

  return roles;
};

/**
 * 检查用户是否有访问特定路由的权限
 * @param userRoles 用户角色数组
 * @param pathname 路由路径
 * @param routesConfig 路由配置
 * @returns 是否有权限
 */
export const hasPermission = (
  userRoles: string[] | undefined,
  pathname: string,
  routesConfig: Record<string, UserRole[]>
): boolean => {
  // 查找匹配的受保护路由
  // 首先尝试精确匹配
  let matchedRoute = pathname;
  let requiredRoles = routesConfig[matchedRoute];

  // 如果没有精确匹配，尝试前缀匹配
  if (!requiredRoles) {
    // 查找以 pathname 开头的路由
    const protectedRouteKeys = Object.keys(routesConfig);
    for (const route of protectedRouteKeys) {
      if (pathname.startsWith(route)) {
        matchedRoute = route;
        requiredRoles = routesConfig[route];
        break;
      }
    }
  }

  // 如果路由不在受保护列表中，允许访问
  if (!requiredRoles) {
    return true;
  }

  // 如果用户没有角色，拒绝访问受保护的路由
  if (!userRoles || userRoles.length === 0) {
    return false;
  }

  // 检查用户是否拥有所需角色
  return requiredRoles.some(role => userRoles.includes(role));
};
