'use client';

import * as React from 'react';
import { useState, useEffect, useRef } from 'react';
import { User, Search, Loader2 } from 'lucide-react';
// @ts-ignore 忽略类型检查
import { debounce } from 'lodash';
import { wmsApi } from '@/utils/wmsApiClient';

import { Input } from '@/components/ui/input';
import { cn } from '@/lib/utils';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface TitleOption {
  id: string;
  name: string;
  code?: string;
  status?: string;
}

interface TitleSelectorProps {
  value?: string;
  onChange: (value: string, titleData?: TitleOption) => void;
  placeholder?: string;
  disabled?: boolean;
  required?: boolean;
  facilityId?: string;
  apiHeaders?: Record<string, string>;
  defaultValue?: string;
}

// 定义WMS API响应类型
interface WmsApiResponse {
  success: boolean;
  msg?: string;
  data?: any[] | {
    list?: any[];
    [key: string]: any;
  };
  [key: string]: any;
}

export function TitleSelector({
  value,
  onChange,
  placeholder = 'Select title',
  disabled = false,
  required = false,
  apiHeaders = {},
  defaultValue
}: TitleSelectorProps) {
  // Fixed API path
  const API_PATH = 'wms-bam/organization/search-by-paging';
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [options, setOptions] = useState<TitleOption[]>([]);
  const [selectedTitle, setSelectedTitle] = useState<TitleOption | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const searchInputRef = useRef<HTMLInputElement>(null);
  const initialLoadRef = useRef(false);

  // 初始化时如果有defaultValue但没有value，则加载defaultValue对应的title
  useEffect(() => {
    if (!initialLoadRef.current && defaultValue && !value) {
      initialLoadRef.current = true;
      fetchTitleById(defaultValue).then(title => {
        if (title) {
          onChange(title.id, title);
        }
      });
    }
  }, [defaultValue, value]);

  // 当value变化时，如果已有选中的客户ID与新value不同，清除已选客户数据
  useEffect(() => {
    if (value !== selectedTitle?.id) {
      setSelectedTitle(null);
    }
  }, [value]);

  // 如果有value但没有selectedTitle，尝试获取客户数据
  useEffect(() => {
    if (value && !selectedTitle && !disabled) {
      fetchTitleById(value);
    }
  }, [value, selectedTitle, disabled]);

  // 当下拉框打开时自动聚焦搜索框
  useEffect(() => {
    if (open && searchInputRef.current) {
      setTimeout(() => {
        searchInputRef.current?.focus();
      }, 100);
    }
  }, [open]);

  // 提取客户数据的函数
  const extractTitleData = (title: any): TitleOption => {
    return {
      id: title.id || title.oemMasterId || '',
      name: title.name || title.fullName || 'Unknown',
      code: title.titleCode || '',
      status: title.status || ''
    };
  };

  // 解析API响应的函数
  const parseApiResponse = (response: any): any[] => {
    // 检查response.data是数组的情况
    if (Array.isArray(response.data)) {
      return response.data;
    }

    // 检查response.data.list的情况
    if (response.data && Array.isArray(response.data.list)) {
      return response.data.list;
    }

    // 检查response.data是对象但包含data字段的情况
    if (response.data && response.data.data && Array.isArray(response.data.data)) {
      return response.data.data;
    }

    // 直接检查response是否包含data字段为数组的情况
    if (response.data && Array.isArray(response.data)) {
      return response.data;
    }

    console.warn('Could not extract title list from API response:', response);
    return [];
  };

  // 通过ID获取客户信息
  const fetchTitleById = async (titleId: string): Promise<TitleOption | null> => {
    try {
      setLoading(true);
      console.log('Fetching title by ID:', titleId);
      const response = await wmsApi.post<WmsApiResponse>(API_PATH, {
        id: titleId
      });

      console.log('Fetch title by ID response:', response);

      if (response.success) {
        const titleList = parseApiResponse(response);
        console.log('Parsed title list:', titleList);

        const title = titleList.find((c: any) => {
          const idMatch = c.id && c.id.toString() === titleId.toString();
          const oemIdMatch = c.oemMasterId && c.oemMasterId.toString() === titleId.toString();
          return idMatch || oemIdMatch;
        });

        if (title) {
          const titleOption = extractTitleData(title);
          console.log('Found title:', titleOption);
          setSelectedTitle(titleOption);
          return titleOption;
        } else {
          console.warn('Title not found with ID:', titleId);
        }
      } else {
        console.error('Error fetching title data:', response.msg);
      }
    } catch (error) {
      console.error('Error fetching title data:', error);
    } finally {
      setLoading(false);
    }
    return null;
  };

  // 搜索客户的函数
  const searchTitles = async (query: string) => {
    if (!query.trim()) {
      setOptions([]);
      return;
    }

    try {
      setLoading(true);
      console.log('Searching titles with query:', query);

      const response = await wmsApi.post<WmsApiResponse>(API_PATH, {
        currentPage: 1,
        pageSize: 20,
        regexName: query,
        nameRegex: query,
        tags: ["TITLE"],
        status: "ACTIVE",
      });

      console.log('Title search API response:', response);

      if (response.success) {
        const titleList = parseApiResponse(response);
        console.log('Parsed title list for search:', titleList);

        if (titleList.length > 0) {
          const titles = titleList.map((title: any) => extractTitleData(title));
          console.log('Mapped titles:', titles);
          setOptions(titles);
        } else {
          console.warn('No titles found in the response');
          setOptions([]);
        }
      } else {
        console.warn('No titles found or API error:', response.msg);
        setOptions([]);
      }
    } catch (error) {
      console.error('Error searching titles:', error);
      setOptions([]);
    } finally {
      setLoading(false);
    }
  };

  // 防抖搜索
  const debouncedSearch = useRef(
    debounce((query: string) => {
      searchTitles(query);
    }, 300)
  ).current;

  // 处理搜索输入变化
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchQuery(value);
    debouncedSearch(value);
  };

  // 处理客户选择
  const handleTitleSelect = (titleId: string) => {
    const title = options.find(opt => opt.id === titleId);
    if (title) {
      setSelectedTitle(title);
      onChange(title.id, title);
      // 重置搜索状态
      setSearchQuery('');
      setOptions([]);
      setOpen(false);
    }
  };

  // 打开下拉框时触发搜索
  const handleOpenChange = (isOpen: boolean) => {
    setOpen(isOpen);

    // 如果关闭下拉框，清空搜索和结果
    if (!isOpen) {
      setSearchQuery('');
      setOptions([]);
    }
    // 如果打开下拉框且有搜索关键字，执行搜索
    else if (isOpen && searchQuery) {
      debouncedSearch(searchQuery);
    }
  };

  return (
    <div className="relative w-full">
      <Select
        value={value}
        onValueChange={handleTitleSelect}
        open={open}
        onOpenChange={handleOpenChange}
        disabled={disabled}
      >
        <SelectTrigger
          className={cn(
            "w-full h-10 rounded-md bg-transparent",
            "border border-item-gray-700/50",
            "hover:border-item-purple focus:border-item-purple",
            "flex items-center justify-between px-3 py-2 text-sm text-white",
            "focus:outline-none focus:ring-1 focus:ring-item-purple",
            "transition-all duration-200",
            "data-[placeholder]:text-item-gray-400"
          )}
        >
          <SelectValue placeholder={placeholder}>
            {loading ? (
              <div className="flex items-center">
                <Loader2 className="mr-2 h-4 w-4 animate-spin text-item-gray-400" />
                <span>Loading...</span>
              </div>
            ) : selectedTitle ? (
              <div className="flex items-center">
                <User className="mr-2 h-4 w-4 text-item-gray-400" />
                <span>{selectedTitle.name}</span>
              </div>
            ) : null}
          </SelectValue>
        </SelectTrigger>
        <SelectContent
          className={cn(
            "border border-item-gray-700/70 bg-item-bg-card text-white",
            "rounded-md shadow-lg min-w-[300px] overflow-hidden",
            "animate-in fade-in-80 zoom-in-95"
          )}
        >
          <div className="flex items-center px-3 py-2 border-b border-item-gray-700/50">
            <Search className="mr-2 h-4 w-4 shrink-0 text-item-gray-400" />
            <Input
              ref={searchInputRef}
              className="h-8 border-0 p-0 bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0 text-white placeholder:text-item-gray-400"
              placeholder="Search titles..."
              value={searchQuery}
              onChange={handleSearchChange}
            />
          </div>

          <div className="max-h-[320px] overflow-auto scrollbar-thin scrollbar-thumb-item-gray-600 scrollbar-track-transparent">
            {loading ? (
              <div className="flex items-center justify-center py-6">
                <Loader2 className="h-6 w-6 animate-spin text-item-gray-400" />
              </div>
            ) : options.length > 0 ? (
              <SelectGroup>
                <SelectLabel className="px-3 text-xs font-medium text-item-gray-400">Titles</SelectLabel>
                {options.map((title) => (
                  <SelectItem
                    key={title.id}
                    value={title.id}
                    className={cn(
                      "py-2 px-3 cursor-pointer text-white",
                      "focus:bg-item-purple focus:text-white",
                      "data-[highlighted]:bg-item-purple data-[highlighted]:text-white"
                    )}
                  >
                    <div className="flex items-center">
                      <User className="mr-2 h-4 w-4 text-item-gray-400" />
                      <div>
                        <div>{title.name}</div>
                        {title.code && (
                          <div className="text-xs text-item-gray-400">{title.code}</div>
                        )}
                      </div>
                    </div>
                  </SelectItem>
                ))}
              </SelectGroup>
            ) : searchQuery ? (
              <div className="py-6 text-center text-sm text-item-gray-400">
                No titles found
              </div>
            ) : (
              <div className="py-6 text-center text-sm text-item-gray-400">
                Type to search titles
              </div>
            )}
          </div>
        </SelectContent>
      </Select>
    </div>
  );
}