'use client';

import { useEffect } from 'react';
import { useSuccess } from '../contexts/SuccessContext';

export default function SuccessDisplay() {
  const { success, clearSuccess } = useSuccess();

  // Auto-close timer for success messages
  useEffect(() => {
    if (success) {
      const timer = setTimeout(() => {
        clearSuccess();
      }, 5000);

      return () => clearTimeout(timer);
    }
  }, [success, clearSuccess]);

  if (!success) return null;

  return (
    <div className="fixed top-4 right-4 z-50">
      <div className="bg-green-900/90 border border-green-800 shadow-lg rounded-lg px-4 py-3 text-white max-w-sm flex items-start">
        <div className="text-green-400 mr-3 flex-shrink-0">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-6 h-6">
            <path strokeLinecap="round" strokeLinejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </div>
        <div>
          <div className="font-semibold mb-1">Success</div>
          <div className="text-sm">{success}</div>
        </div>
        <button
          onClick={clearSuccess}
          className="ml-auto text-green-400 hover:text-white"
        >
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5">
            <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>
    </div>
  );
}
