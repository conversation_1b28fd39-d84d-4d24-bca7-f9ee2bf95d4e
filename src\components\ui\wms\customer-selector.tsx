'use client';

import * as React from 'react';
import { useState, useEffect, useRef } from 'react';
import { User, Search, Loader2 } from 'lucide-react';
// @ts-ignore 忽略类型检查
import { debounce } from 'lodash';
import { wmsApi } from '@/utils/wmsApiClient';

import { Input } from '@/components/ui/input';
import { cn } from '@/lib/utils';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
  getCyberAgentPortalContainer,
} from "@/components/ui/select";

interface CustomerOption {
  id: string;
  name: string;
  code?: string;
  status?: string;
}

interface CustomerSelectorProps {
  value?: string;
  onChange: (value: string, customerData?: CustomerOption) => void;
  placeholder?: string;
  disabled?: boolean;
  required?: boolean;
  facilityId?: string;
  apiHeaders?: Record<string, string>;
  defaultValue?: string;
}

// 定义WMS API响应类型
interface WmsApiResponse {
  success: boolean;
  msg?: string;
  data?: any[] | {
    list?: any[];
    [key: string]: any;
  };
  [key: string]: any;
}

export function CustomerSelector({
  value,
  onChange,
  placeholder = 'Select customer',
  disabled = false,
  required = false,
  apiHeaders = {},
  defaultValue
}: CustomerSelectorProps) {
  // Fixed API path
  const API_PATH = 'wms-bam/organization/search-by-paging';
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [options, setOptions] = useState<CustomerOption[]>([]);
  const [selectedCustomer, setSelectedCustomer] = useState<CustomerOption | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const searchInputRef = useRef<HTMLInputElement>(null);
  const initialLoadRef = useRef(false);

  // Initialize with defaultValue if provided and value is not set
  useEffect(() => {
    if (!initialLoadRef.current && defaultValue && !value) {
      initialLoadRef.current = true;
      fetchCustomerById(defaultValue).then(customer => {
        if (customer) {
          onChange(customer.id, customer);
        }
      });
    }
  }, [defaultValue, value]);

  // 当value变化时，如果已有选中的客户ID与新value不同，清除已选客户数据
  useEffect(() => {
    if (value !== selectedCustomer?.id) {
      setSelectedCustomer(null);
    }
  }, [value]);

  // 如果有value但没有selectedCustomer，尝试获取客户数据
  useEffect(() => {
    if (value && !selectedCustomer && !disabled) {
      fetchCustomerById(value).then(customer => {
        if (customer) {
          // 确保在获取到客户数据后调用onChange，传递完整的客户数据
          onChange(customer.id, customer);
        }
      });
    }
  }, [value, selectedCustomer, disabled]);

  // 当下拉框打开时自动聚焦搜索框
  useEffect(() => {
    if (open && searchInputRef.current) {
      setTimeout(() => {
        searchInputRef.current?.focus();
      }, 100);
    }
  }, [open]);

  // 提取客户数据的函数
  const extractCustomerData = (customer: any): CustomerOption => {
    return {
      id: customer.id || customer.oemMasterId || '',
      name: customer.name || customer.fullName || 'Unknown',
      code: customer.customerCode || '',
      status: customer.status || ''
    };
  };

  // 解析API响应的函数
  const parseApiResponse = (response: any): any[] => {
    // 检查response.data是数组的情况
    if (Array.isArray(response.data)) {
      return response.data;
    }

    // 检查response.data.list的情况
    if (response.data && Array.isArray(response.data.list)) {
      return response.data.list;
    }

    // 检查response.data是对象但包含data字段的情况
    if (response.data && response.data.data && Array.isArray(response.data.data)) {
      return response.data.data;
    }

    // 直接检查response是否包含data字段为数组的情况
    if (response.data && Array.isArray(response.data)) {
      return response.data;
    }

    console.warn('Could not extract customer list from API response:', response);
    return [];
  };

  // 通过ID获取客户信息
  const fetchCustomerById = async (customerId: string): Promise<CustomerOption | null> => {
    try {
      setLoading(true);
      console.log('Fetching customer by ID:', customerId);
      const response = await wmsApi.post<WmsApiResponse>(API_PATH, {
        id: customerId
      });

      console.log('Fetch customer by ID response:', response);

      if (response.success) {
        const customerList = parseApiResponse(response);
        console.log('Parsed customer list:', customerList);

        const customer = customerList.find((c: any) => {
          const idMatch = c.id && c.id.toString() === customerId.toString();
          const oemIdMatch = c.oemMasterId && c.oemMasterId.toString() === customerId.toString();
          return idMatch || oemIdMatch;
        });

        if (customer) {
          const customerOption = extractCustomerData(customer);
          console.log('Found customer:', customerOption);
          setSelectedCustomer(customerOption);
          return customerOption;
        } else {
          console.warn('Customer not found with ID:', customerId);
        }
      } else {
        console.error('Error fetching customer data:', response.msg);
      }
    } catch (error) {
      console.error('Error fetching customer data:', error);
    } finally {
      setLoading(false);
    }
    return null;
  };

  // 搜索客户的函数
  const searchCustomers = async (query: string) => {
    if (!query.trim()) {
      setOptions([]);
      return;
    }

    try {
      setLoading(true);
      console.log('Searching customers with query:', query);

      const response = await wmsApi.post<WmsApiResponse>(API_PATH, {
        currentPage: 1,
        pageSize: 20,
        regexName: query,
        nameRegex: query,
        tags: ["CUSTOMER"],
        status: "ACTIVE",
      });

      console.log('Customer search API response:', response);

      if (response.success) {
        const customerList = parseApiResponse(response);
        console.log('Parsed customer list for search:', customerList);

        if (customerList.length > 0) {
          const customers = customerList.map((customer: any) => extractCustomerData(customer));
          console.log('Mapped customers:', customers);
          setOptions(customers);
        } else {
          console.warn('No customers found in the response');
          setOptions([]);
        }
      } else {
        console.warn('No customers found or API error:', response.msg);
        setOptions([]);
      }
    } catch (error) {
      console.error('Error searching customers:', error);
      setOptions([]);
    } finally {
      setLoading(false);
    }
  };

  // 防抖搜索
  const debouncedSearch = useRef(
    debounce((query: string) => {
      searchCustomers(query);
    }, 300)
  ).current;

  // 处理搜索输入变化
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchQuery(value);
    debouncedSearch(value);
  };

  // 处理客户选择
  const handleCustomerSelect = (customerId: string) => {
    const customer = options.find(opt => opt.id === customerId);
    if (customer) {
      setSelectedCustomer(customer);
      onChange(customer.id, customer);
      // 重置搜索状态
      setSearchQuery('');
      setOptions([]);
      setOpen(false);
    }
  };

  // 打开下拉框时触发搜索
  const handleOpenChange = (isOpen: boolean) => {
    setOpen(isOpen);

    // 如果关闭下拉框，清空搜索和结果
    if (!isOpen) {
      setSearchQuery('');
      setOptions([]);
    }
    // 如果打开下拉框且有搜索关键字，执行搜索
    else if (isOpen && searchQuery) {
      debouncedSearch(searchQuery);
    }
  };

  return (
    <div className="relative w-full">
      <Select
        value={value}
        onValueChange={handleCustomerSelect}
        open={open}
        onOpenChange={handleOpenChange}
        disabled={disabled}
      >
        <SelectTrigger
          className={cn(
            "w-full h-10 rounded-md bg-transparent",
            "border border-item-gray-700/50",
            "hover:border-item-purple focus:border-item-purple",
            "flex items-center justify-between px-3 py-2 text-sm text-white",
            "focus:outline-none focus:ring-1 focus:ring-item-purple",
            "transition-all duration-200",
            "data-[placeholder]:text-item-gray-400"
          )}
        >
          <SelectValue placeholder={placeholder}>
            {loading ? (
              <div className="flex items-center">
                <Loader2 className="mr-2 h-4 w-4 animate-spin text-item-gray-400" />
                <span>Loading...</span>
              </div>
            ) : selectedCustomer ? (
              <div className="flex items-center">
                <User className="mr-2 h-4 w-4 text-item-gray-400" />
                <span>{selectedCustomer.name}</span>
              </div>
            ) : null}
          </SelectValue>
        </SelectTrigger>
        <SelectContent
          className={cn(
            "border border-item-gray-700/70 bg-item-bg-card text-white",
            "rounded-md shadow-lg min-w-[300px] overflow-hidden",
            "animate-in fade-in-80 zoom-in-95"
          )}
          portalContainer={getCyberAgentPortalContainer()}
        >
          <div className="flex items-center px-3 py-2 border-b border-item-gray-700/50">
            <Search className="mr-2 h-4 w-4 shrink-0 text-item-gray-400" />
            <Input
              ref={searchInputRef}
              className="h-8 border-0 p-0 bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0 text-white placeholder:text-item-gray-400"
              placeholder="Search customers..."
              value={searchQuery}
              onChange={handleSearchChange}
            />
          </div>

          <div className="max-h-[320px] overflow-auto scrollbar-thin scrollbar-thumb-item-gray-600 scrollbar-track-transparent">
            {loading ? (
              <div className="flex items-center justify-center py-6">
                <Loader2 className="h-6 w-6 animate-spin text-item-gray-400" />
              </div>
            ) : options.length > 0 ? (
              <SelectGroup>
                <SelectLabel className="px-3 text-xs font-medium text-item-gray-400">Customers</SelectLabel>
                {options.map((customer) => (
                  <SelectItem
                    key={customer.id}
                    value={customer.id}
                    className={cn(
                      "py-2 px-3 cursor-pointer text-white",
                      "focus:bg-item-purple focus:text-white",
                      "data-[highlighted]:bg-item-purple data-[highlighted]:text-white"
                    )}
                  >
                    <div className="flex items-center">
                      <User className="mr-2 h-4 w-4 text-item-gray-400" />
                      <div>
                        <div>{customer.name}</div>
                        {customer.code && (
                          <div className="text-xs text-item-gray-400">{customer.code}</div>
                        )}
                      </div>
                    </div>
                  </SelectItem>
                ))}
              </SelectGroup>
            ) : searchQuery ? (
              <div className="py-6 text-center text-sm text-item-gray-400">
                No customers found
              </div>
            ) : (
              <div className="py-6 text-center text-sm text-item-gray-400">
                Type to search customers
              </div>
            )}
          </div>
        </SelectContent>
      </Select>
    </div>
  );
}