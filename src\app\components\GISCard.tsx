'use client';

import React from 'react';
import ArtifactToolCard from './ArtifactToolCard';

interface GISCardProps {
  toolInvocation: any;
}

export default function GISCard({ toolInvocation }: GISCardProps) {
  const result = toolInvocation.result;
  const facility = toolInvocation.args?.facility || 'Unknown Facility';
  const hasArtifact = result?.artifact;
  

  const gisIcon = (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-4 h-4">
      <path strokeLinecap="round" strokeLinejoin="round" d="M9 6.75V15m6-6v8.25m.503 3.498l4.875-2.437c.381-.19.622-.58.622-1.006V4.82c0-.836-.88-1.38-1.628-1.006l-3.869 1.934c-.317.159-.69.159-1.006 0L9.503 3.252a1.125 1.125 0 00-1.006 0L3.622 5.689C3.24 5.88 3 6.27 3 6.695V19.18c0 .836.88 1.38 1.628 1.006l3.869-1.934c.317-.159.69-.159 1.006 0l4.994 2.497c.317.158.69.158 1.006 0z" />
    </svg>
  );

  return (
    <ArtifactToolCard
      toolInvocation={toolInvocation}
      title={`GIS`}
      icon={gisIcon}
      borderColor="border-item-blue/30"
      iconBgColor="bg-item-blue/20"
      iconTextColor="text-item-blue"
    />
  );
}