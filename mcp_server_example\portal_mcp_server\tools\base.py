from typing import Dict, Any, Callable, List, Optional

"""Base tool class for all tools"""

class BaseTool:
    """基础工具类，所有自定义工具应该继承此类"""
    
    def __init__(self):
        """初始化工具"""
        pass
    
    async def update_context(self, **context):
        """
        更新工具上下文
        
        可由子类重写以处理特定于工具的上下文更新
        
        Args:
            **context: 上下文参数
        """
        # 基础实现不做任何事情，子类可以重写
        pass
    
    def get_tools(self) -> list:
        """
        获取工具列表
        
        返回此工具提供的工具列表及其模式。
        子类应该重写此方法以提供特定工具信息。
        
        Returns:
            工具列表
        """
        # 默认返回空列表，子类应该重写
        return []
    
    @classmethod
    def get_system_prompt(cls) -> str:
        """
        获取系统提示词
        
        返回此工具使用的系统提示词。
        子类可以重写此方法以提供特定提示词。
        
        Returns:
            系统提示词
        """
        # 默认返回空字符串，子类可以重写
        return ""
        
    def __repr__(self) -> str:
        """返回工具的字符串表示"""
        return f"{self.__class__.__name__}()"

class Tool:
    """工具类，用于格式化 Claude API 所需的工具定义"""
    @classmethod
    def from_function(cls, function: Callable, name: str, description: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        从函数创建工具定义
        
        Args:
            function: 要封装的函数
            name: 工具名称
            description: 工具描述
            parameters: 参数定义
        
        Returns:
            工具定义字典
        """
        tool_definition = {
            "name": name,
            "description": description,
            "input_schema": {
                "type": "object",
                "properties": {},
                "required": []
            }
        }
        
        if parameters:
            properties = {}
            required_params = []
            
            for param_name, param_spec in parameters.items():
                # 创建基本参数定义
                param_def = {
                    "type": param_spec["type"],
                    "description": param_spec["description"]
                }
                
                # 处理特殊参数
                if param_name == "build_number":
                    param_def["minimum"] = 1
                elif param_name == "parameters":
                    param_def["additionalProperties"] = {
                        "type": "string"
                    }
                
                properties[param_name] = param_def
                
                # 处理必需参数
                if param_spec.get("required", True):
                    required_params.append(param_name)
            
            tool_definition["input_schema"]["properties"] = properties
            if required_params:
                tool_definition["input_schema"]["required"] = required_params
        
        return tool_definition 