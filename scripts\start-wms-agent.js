#!/usr/bin/env node

/**
 * WMS Agent启动脚本
 * 设置环境变量并启动WMS专业Agent
 * 提供A2A协议标准的/.well-known/agent.json端点
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

// 为WMS Agent创建独立的构建目录
const buildDir = '.next-wms';
if (!fs.existsSync(buildDir)) {
  fs.mkdirSync(buildDir, { recursive: true });
}

// 设置WMS Agent环境变量
const env = {
  ...process.env,
  AGENT_TYPE: 'wms',
  AGENT_NAME: 'CyberBot WMS Agent',
  AGENT_PORT: '3001',
  PORT: '3001', // 强制指定端口
  WMS_AGENT_URL: 'http://localhost:3001',
  MCP_SERVERS: 'wms', // 对应mcp.json中的wms服务器
  // 使用独立的构建目录
  NEXT_BUILD_DIR: buildDir
};

console.log('🚀 Starting CyberBot WMS Agent...');
console.log('📦 Agent Type: WMS');
console.log('🌐 Port: 3001');
console.log('🔧 MCP Servers: wms');
console.log('📁 Build Dir:', path.resolve(buildDir));
console.log('🔗 A2A Protocol: Enabled');
console.log('📋 Agent Card: http://localhost:3001/.well-known/agent.json');
console.log('');
console.log('ℹ️  This agent will be discovered by Super Agent via A2A protocol');
console.log('ℹ️  No manual registration required - Super Agent will find this agent automatically');
console.log('');

// 启动Next.js开发服务器
const nextProcess = spawn('npm', ['run', 'dev'], {
  env,
  stdio: 'inherit',
  shell: true,
  cwd: path.resolve(__dirname, '..')
});

// 监听Next.js启动事件
nextProcess.stdout?.on('data', (data) => {
  const output = data.toString();
  console.log('[Next.js]', output);
  
  // 检测到Ready信号时显示agent信息
  if (output.includes('Ready in') || output.includes('✓ Ready')) {
    console.log('');
    console.log('✅ WMS Agent is ready!');
    console.log('🔗 Agent Card URL: http://localhost:3001/.well-known/agent.json');
    console.log('🏥 Health Check: http://localhost:3001/api/health');
    console.log('📡 A2A Discovery: Ready for Super Agent discovery');
    console.log('');
    console.log('🎯 Super Agent can now discover this WMS Agent via A2A protocol');
    console.log('');
  }
});

nextProcess.stderr?.on('data', (data) => {
  console.log('[Next.js Error]', data.toString());
});

// 优雅关闭处理
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down WMS Agent...');
  nextProcess.kill('SIGINT');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Shutting down WMS Agent...');
  nextProcess.kill('SIGTERM');
  process.exit(0);
});

nextProcess.on('close', (code) => {
  console.log(`\n📊 WMS Agent process exited with code ${code}`);
  process.exit(code);
}); 