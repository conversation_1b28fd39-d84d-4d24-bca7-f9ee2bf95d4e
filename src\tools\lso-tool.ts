import { tool } from 'ai';
import { z } from 'zod';
import { BaseTool } from './baseTool';
import { ToolDefinition } from './types';

/**
 * LSO订单跟踪工具
 * 使用 Vercel AI SDK 的 tool 函数实现
 */
export const lsoTrackingTool = tool({
  description: 'Query LSO (Lone Star Overnight) shipment tracking information by tracking number',
  parameters: z.object({
    barcodes: z.string().describe('The tracking number(s) to query. Multiple barcodes can be comma-separated.')
  }),
  execute: async ({ barcodes }) => {
    console.log('LSOTrackingTool.execute called with args:', JSON.stringify({ barcodes }));
    
    try {
      // 从环境变量获取LSO基础URL，如果未设置则使用默认值
      const baseUrl = process.env.LSO_BASEURL || 'https://api-prod.lso.com';
      
      // 构建API请求
      const apiUrl = `${baseUrl}/tracking/track_barcode?barcodes=${encodeURIComponent(barcodes)}`;
      
      const response = await fetch(apiUrl, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      
      console.log('LSOTrackingTool.execute response:', JSON.stringify(data));
      
      return {
        success: true,
        barcodes,
        data,
        baseUrl,
        timestamp: new Date().toISOString()
      };
      
    } catch (error) {
      console.error('LSOTrackingTool.execute error:', error);
      
      return {
        success: false,
        barcodes,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        timestamp: new Date().toISOString()
      };
    }
  }
});

/**
 * LSO费率计算工具
 * 使用 Vercel AI SDK 的 tool 函数实现
 */
export const lsoCalcRateTool = tool({
  description: 'Calculate LSO (Lone Star Overnight) shipping rates',
  parameters: z.object({
    ground: z.string().describe('Whether to use ground service (true/false)'),
    weight: z.string().describe('Package weight in pounds'),
    from_zip: z.string().describe('Origin ZIP code'),
    to_zip: z.string().describe('Destination ZIP code'),
    pickup: z.string().default('false').describe('Whether pickup service is required (true/false)')
  }),
  execute: async (params) => {
    console.log('LSOCalcRateTool.execute called with args:', JSON.stringify(params));
    
    try {
      // 从环境变量获取LSO基础URL，如果未设置则使用默认值
      const baseUrl = process.env.LSO_BASEURL || 'https://api-prod.lso.com';
      const apiUrl = `${baseUrl}/pricing_service/calculate_rates`;
      
      // 发送API请求
      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(params)
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      
      console.log('LSOCalcRateTool.execute response:', JSON.stringify(data));
      
      return {
        success: true,
        rate_data: data,
        request_params: params,
        baseUrl,
        timestamp: new Date().toISOString()
      };
      
    } catch (error) {
      console.error('LSOCalcRateTool.execute error:', error);
      
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        timestamp: new Date().toISOString()
      };
    }
  }
});

/**
 * LSOTool 类，继承自 BaseTool
 * 适配旧的工具系统
 */
export class LSOTool extends BaseTool {
  constructor() {
    super();
  }
  
  getTools(): ToolDefinition[] {
    return [
      {
        name: 'queryLSOTracking',
        description: 'Query LSO (Lone Star Overnight) shipment tracking information by tracking number',
        parameters: {
          type: 'object',
          properties: {
            barcodes: {
              type: 'string',
              description: 'The tracking number(s) to query. Multiple barcodes can be comma-separated.'
            }
          },
          required: ['barcodes']
        }
      },
      {
        name: 'calculateLSORate',
        description: 'Calculate LSO (Lone Star Overnight) shipping rates',
        parameters: {
          type: 'object',
          properties: {
            ground: { type: 'string', description: 'Whether to use ground service (true/false)' },
            weight: { type: 'string', description: 'Package weight in pounds' },
            from_zip: { type: 'string', description: 'Origin ZIP code' },
            to_zip: { type: 'string', description: 'Destination ZIP code' },
            pickup: { type: 'string', description: 'Whether pickup service is required (true/false)' }
          },
          required: ['ground', 'weight', 'from_zip', 'to_zip', 'pickup']
        }
      }
    ];
  }
  
  async queryLSOTracking(barcodes: string) {
    // 复用 lsoTrackingTool 的实现
    return await lsoTrackingTool.execute({ barcodes }, { 
      toolCallId: `lso-${Date.now()}`, 
      messages: []
    });
  }
  
  async calculateLSORate(params: any) {
    // 复用 lsoCalcRateTool 的实现
    return await lsoCalcRateTool.execute(params, { 
      toolCallId: `lso-rate-${Date.now()}`, 
      messages: []
    });
  }
  
  static getSystemPrompt(): string {
    return `You can use the LSO tools to query shipment tracking and calculate shipping rates.

Available tools:
1. queryLSOTracking - Query LSO shipment tracking information by tracking number
2. calculateLSORate - Calculate LSO shipping rates

For queryLSOTracking:
- Supports single or multiple tracking numbers (comma-separated)
- Returns detailed tracking information

For calculateLSORate:
- Calculates shipping rates between ZIP codes
- Requires package weight and service type (ground)
- Supports pickup service option
- Returns detailed pricing information

Example queries:
- "Track LSO shipment 123456"
- "Check status of LSO tracking numbers ABC123,XYZ789"
- "Calculate LSO rate from 75001 to 75051 for 5 lbs ground service"
- "Get LSO shipping quote for 10 lbs with pickup from 75001 to 75051"`;
  }
} 