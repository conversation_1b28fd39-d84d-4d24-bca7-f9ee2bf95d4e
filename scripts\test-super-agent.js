#!/usr/bin/env node

/**
 * 超级智能体系统测试
 * 测试Super Agent的动态工具发现、任务委托和流式通信功能
 */

const http = require('http');

// 模拟IAM OAuth2 token
const MOCK_IAM_TOKEN = 'Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************.mock_signature';

async function testSuperAgentSystem() {
  console.log('🚀 Testing Super Agent System with Dynamic A2A Tools...\n');
  console.log('=' .repeat(80));
  
  try {
    // 1. 验证Super Agent在线
    console.log('1️⃣ Verifying Super Agent is online...');
    const superHealth = await testHealthCheck('http://localhost:3000');
    if (!superHealth.success) {
      console.log('❌ Super Agent is not running. Please start it first:');
      console.log('   npm run start:super');
      return;
    }
    console.log('✅ Super Agent is online\n');

    // 2. 验证其他agents状态
    console.log('2️⃣ Checking other agents status...');
    const agents = [
      { name: 'WMS Agent', url: 'http://localhost:3001' },
      { name: 'TMS Agent', url: 'http://localhost:3002' },
      { name: 'FMS Agent', url: 'http://localhost:3003' }
    ];

    for (const agent of agents) {
      const health = await testHealthCheck(agent.url);
      console.log(`   ${agent.name}: ${health.success ? '✅ Online' : '❌ Offline'}`);
    }
    console.log();

    // 3. 测试Agent Card发现
    console.log('3️⃣ Testing Agent Card discovery...');
    for (const agent of agents) {
      try {
        const agentCard = await fetchAgentCard(agent.url);
        console.log(`   ✅ ${agent.name} Agent Card: ${agentCard.name || 'Unknown'}`);
      } catch (error) {
        console.log(`   ❌ ${agent.name} Agent Card: ${error.message}`);
      }
    }
    console.log();

    // 4. 测试Super Agent的聊天功能（包含动态工具）
    console.log('4️⃣ Testing Super Agent chat with dynamic tools...');
    
    const testMessages = [
      {
        role: 'user',
        content: '你好！请告诉我你现在有哪些可用的工具，特别是可以委托给其他agents的工具？'
      }
    ];

    console.log('   Sending test message to Super Agent...');
    const chatResponse = await testChatAPI('http://localhost:3000', testMessages, MOCK_IAM_TOKEN);
    
    if (chatResponse.success) {
      console.log('   ✅ Super Agent responded successfully');
      console.log(`   Response preview: ${chatResponse.response.substring(0, 200)}...`);
    } else {
      console.log('   ❌ Super Agent chat failed:', chatResponse.error);
    }
    console.log();

    // 5. 测试任务委托（如果WMS Agent在线）
    const wmsHealth = await testHealthCheck('http://localhost:3001');
    if (wmsHealth.success) {
      console.log('5️⃣ Testing task delegation to WMS Agent...');
      
      const delegationMessages = [
        {
          role: 'user',
          content: '请帮我检查仓库中的库存情况，特别是COSTCO相关的商品。'
        }
      ];

      console.log('   Sending delegation request to Super Agent...');
      const delegationResponse = await testChatAPI('http://localhost:3000', delegationMessages, MOCK_IAM_TOKEN);
      
      if (delegationResponse.success) {
        console.log('   ✅ Task delegation initiated successfully');
        console.log(`   Response preview: ${delegationResponse.response.substring(0, 200)}...`);
      } else {
        console.log('   ❌ Task delegation failed:', delegationResponse.error);
      }
    } else {
      console.log('5️⃣ Skipping task delegation test (WMS Agent offline)');
    }
    console.log();

    // 6. 测试多agent协调
    console.log('6️⃣ Testing multi-agent coordination...');
    
    const coordinationMessages = [
      {
        role: 'user',
        content: '我需要分析一个完整的供应链流程：从仓库库存检查，到运输规划，再到车队管理。请协调相关的agents来完成这个任务。'
      }
    ];

    console.log('   Sending coordination request to Super Agent...');
    const coordinationResponse = await testChatAPI('http://localhost:3000', coordinationMessages, MOCK_IAM_TOKEN);
    
    if (coordinationResponse.success) {
      console.log('   ✅ Multi-agent coordination initiated successfully');
      console.log(`   Response preview: ${coordinationResponse.response.substring(0, 200)}...`);
    } else {
      console.log('   ❌ Multi-agent coordination failed:', coordinationResponse.error);
    }

    console.log('\n' + '=' .repeat(80));
    console.log('🎉 Super Agent System Test Completed!');
    console.log('\n📊 Test Summary:');
    console.log(`   - Super Agent: ${superHealth.success ? '✅ Online' : '❌ Offline'}`);
    console.log(`   - Dynamic Tools: ${chatResponse.success ? '✅ Working' : '❌ Failed'}`);
    console.log(`   - Task Delegation: ${wmsHealth.success ? (delegationResponse?.success ? '✅ Working' : '❌ Failed') : '⏭️ Skipped'}`);
    console.log(`   - Multi-Agent Coordination: ${coordinationResponse.success ? '✅ Working' : '❌ Failed'}`);

  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
  }
}

// 辅助函数
async function testHealthCheck(url) {
  return new Promise((resolve) => {
    const req = http.get(`${url}/api/health`, { timeout: 3000 }, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        try {
          const result = JSON.parse(data);
          resolve({ success: res.statusCode === 200, data: result });
        } catch (error) {
          resolve({ success: false, error: 'Invalid JSON response' });
        }
      });
    });

    req.on('error', (error) => {
      resolve({ success: false, error: error.message });
    });

    req.on('timeout', () => {
      req.destroy();
      resolve({ success: false, error: 'Request timeout' });
    });
  });
}

async function fetchAgentCard(url) {
  return new Promise((resolve, reject) => {
    const req = http.get(`${url}/.well-known/agent.json`, { timeout: 3000 }, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        try {
          if (res.statusCode === 200) {
            resolve(JSON.parse(data));
          } else {
            reject(new Error(`HTTP ${res.statusCode}`));
          }
        } catch (error) {
          reject(new Error('Invalid JSON response'));
        }
      });
    });

    req.on('error', reject);
    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
  });
}

async function testChatAPI(url, messages, authToken) {
  return new Promise((resolve) => {
    const postData = JSON.stringify({ messages });
    
    const options = {
      hostname: new URL(url).hostname,
      port: new URL(url).port,
      path: '/api/chat',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData),
        'Authorization': authToken
      },
      timeout: 30000
    };

    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        if (res.statusCode === 200) {
          resolve({ success: true, response: data });
        } else {
          resolve({ success: false, error: `HTTP ${res.statusCode}: ${data}` });
        }
      });
    });

    req.on('error', (error) => {
      resolve({ success: false, error: error.message });
    });

    req.on('timeout', () => {
      req.destroy();
      resolve({ success: false, error: 'Request timeout' });
    });

    req.write(postData);
    req.end();
  });
}

// 运行测试
if (require.main === module) {
  testSuperAgentSystem();
} 