{"openapi": "3.0.1", "info": {"title": "FMS", "description": "", "version": "1.0.0"}, "paths": {"/fms-platform-order/status/shipment/query-status-by-pro": {"post": {"summary": "QueryOrderStatusByPro", "deprecated": false, "description": "", "tags": ["ShipmentOrderStatus"], "parameters": [], "requestBody": {"content": {"application/json-patch+json": {"schema": {"type": "array", "items": {"type": "string"}}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/OrderStatusQueryResponse"}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order/work-orders/getworkorderinfo": {"get": {"summary": "Get the corresponding information of WorkOrderNo", "deprecated": false, "description": "", "tags": ["WorkOrder"], "parameters": [{"name": "workOrderNo", "in": "query", "description": "Work Order Number", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/FMSWorkOrderInfoResponseDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order/work-order/getwork-orderbasic/{orderNo}": {"get": {"summary": "Order (WO) details interface - Order information in the upper left corner", "deprecated": false, "description": "", "tags": ["WorkOrder"], "parameters": [{"name": "orderNo", "in": "path", "description": "Order number", "required": true, "example": "", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/WorkOrderBasicInfoDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order/work-order/billing/{orderNo}": {"get": {"summary": "Order (WO) Details Interface-Billing Information", "deprecated": false, "description": "", "tags": ["WorkOrder"], "parameters": [{"name": "orderNo", "in": "path", "description": "Order number", "required": true, "example": "", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/WorkOrderBillDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order/work-orders/{orderNo}/hold": {"get": {"summary": "Order (WO) Details Interface - Suspend Orders", "deprecated": false, "description": "", "tags": ["WorkOrder"], "parameters": [{"name": "orderNo", "in": "path", "description": "Order number", "required": true, "example": "", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order/work-orders/{orderNo}/release-hold": {"get": {"summary": "Order (WO) Details Interface - Recover Order", "deprecated": false, "description": "", "tags": ["WorkOrder"], "parameters": [{"name": "orderNo", "in": "path", "description": "Order number", "required": true, "example": "", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order/work-orders/status/all": {"get": {"summary": "Get the Status of the work order", "deprecated": false, "description": "", "tags": ["WorkOrder"], "parameters": [], "responses": {"200": {"description": "Success", "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order/work-orders/work-order-type/all": {"get": {"summary": "Get the work order type", "deprecated": false, "description": "", "tags": ["WorkOrder"], "parameters": [], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringFmsSelectListItemListResultDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order/work-orders/service-level/all": {"get": {"summary": "Get the service-level of the work order", "deprecated": false, "description": "", "tags": ["WorkOrder"], "parameters": [], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringFmsSelectListItemListResultDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order/work-orders/business-clients/all": {"get": {"summary": "Get business clients", "deprecated": false, "description": "", "tags": ["WorkOrder"], "parameters": [], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringFmsSelectListItemListResultDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order/work-order/customer/appointment/{workOrderNo}": {"get": {"summary": "View Customer-Appointment info", "deprecated": false, "description": "", "tags": ["WorkOrderCustomer"], "parameters": [{"name": "workOrderNo", "in": "path", "description": "Work Order Number", "required": true, "example": "", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/OrderAppointmentDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order/work-orders/{orderNo}/service-products": {"get": {"summary": "Order (WO) details interface - service-products information under Billing module", "deprecated": false, "description": "", "tags": ["WorkOrderPackageItems"], "parameters": [{"name": "orderNo", "in": "path", "description": "Work order number", "required": true, "example": "", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/WorkOrderPackageItemDtoListResultDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order/appointment-additions/get": {"post": {"summary": "Get the Packages information corresponding to ShipmentOrderNo", "deprecated": false, "description": "", "tags": ["AppointmentAdditions"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SelectAppointmentAdditionRequest"}, "example": ""}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AppointmentAdditionsResponse"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order/appointment-additions/get-additions": {"post": {"summary": "AI: Get the Packages information corresponding to ShipmentOrderNo", "deprecated": false, "description": "", "tags": ["AppointmentAdditions"], "parameters": [], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/SelectAppointmentAdditionRequest"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AppointmentAdditionsResponse"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order/order-reference/SelectOrderReferenceByShipmentOrderKey": {"get": {"summary": "Select order reference information based on delivery order key", "deprecated": false, "description": "", "tags": ["RpcOrderReference"], "parameters": [{"name": "shipmentOrderKey", "in": "query", "description": "Delivery Order Key", "required": false, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/RpcOrderReferenceDto"}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order/order-reference/SelectOrderReferenceByWorkOrderKey": {"get": {"summary": "Select order reference information based on the work order key", "deprecated": false, "description": "", "tags": ["RpcOrderReference"], "parameters": [{"name": "shipmentOrderKey", "in": "query", "description": "Delivery Order Key", "required": false, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/RpcOrderReferenceDto"}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order/shipment-order-history/{orderNo}": {"get": {"summary": "GetOrderHistory", "deprecated": false, "description": "Query the History information of the order, enter the parameter do#, and exit the parameter as the History information display of the order.", "tags": ["ShipmentOrderHistory"], "parameters": [{"name": "orderNo", "in": "path", "description": "Order number", "required": true, "example": "", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/OrderHistoryDto"}}}}, "headers": {}}}, "security": [{"apikey-header-fms-token": []}]}}, "/fms-platform-order/work-order-history/{orderNo}": {"get": {"summary": "Query WO History", "deprecated": false, "description": "", "tags": ["WorkOrderHistory"], "parameters": [{"name": "orderNo", "in": "path", "description": "Work order number", "required": true, "example": "", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/OrderHistoryDto"}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order/crm/business-client/{businessClient}": {"get": {"summary": "Order (WO) details interface - Get large customer information from CRM under the Billing module", "deprecated": false, "description": "", "tags": ["BusinessClient"], "parameters": [{"name": "businessClient", "in": "path", "description": "Business customer identification", "required": true, "example": "", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/CrmCustomerFullDataDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order/crm-customer/name/{customer_name}": {"get": {"summary": "GetCustomerByName", "deprecated": false, "description": "", "tags": ["CRM"], "parameters": [{"name": "customer_name", "in": "path", "description": "", "required": true, "example": "", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CrmRPCCustomerByNameDataItemValueResponse"}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}}, "components": {"schemas": {"AppointmentAdditionsServiceInformation": {"type": "object", "properties": {"business_client": {"type": "string", "nullable": true}, "service_level": {"$ref": "#/components/schemas/ServiceLevelEnum"}, "rma": {"type": "string", "nullable": true}}, "additionalProperties": false}, "AppointmentAdditionsResponse": {"type": "object", "properties": {"tracking_no": {"type": "string", "nullable": true}, "shipment_order_no": {"type": "string", "nullable": true}, "work_order_no": {"type": "string", "nullable": true}, "address": {"type": "string", "nullable": true}, "contact": {"type": "string", "nullable": true}, "reference": {"type": "string", "nullable": true}, "sales_order": {"type": "string", "nullable": true}, "purchase_order": {"type": "string", "nullable": true}, "load": {"type": "string", "nullable": true}, "bol": {"type": "string", "nullable": true}, "master_order_no": {"type": "string", "nullable": true}, "total_freight": {"type": "string", "nullable": true}, "desire_date": {"type": "string", "format": "date-time"}, "mabd": {"type": "string", "format": "date-time"}, "service_information": {"$ref": "#/components/schemas/AppointmentAdditionsServiceInformation"}, "service_products": {"type": "array", "items": {"$ref": "#/components/schemas/AppointmentAdditionsServiceProduct"}, "nullable": true}, "appointment_date": {"type": "string", "format": "date-time"}, "appointment_time_from": {"type": "string", "format": "date-time", "nullable": true}, "appointment_time_to": {"type": "string", "format": "date-time", "nullable": true}, "mabd_from": {"type": "string", "format": "date-time"}, "mabd_to": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "AppointmentAdditionsServiceProduct": {"type": "object", "properties": {"item_code": {"type": "string", "nullable": true}, "item_name": {"type": "string", "nullable": true}, "item_type": {"type": "string", "nullable": true}, "item_quantity": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "CrmCustomerAddressResponse": {"type": "object", "properties": {"country": {"type": "string", "nullable": true}, "city": {"type": "string", "nullable": true}, "state": {"type": "string", "nullable": true}, "street": {"type": "string", "nullable": true}, "unit": {"type": "string", "nullable": true}, "zip_code": {"type": "string", "nullable": true}, "type": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CrmCustomerBaseInfoResponse": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "nullable": true}, "customer_name": {"type": "string", "nullable": true}, "customer_code": {"type": "string", "nullable": true}, "print_name": {"type": "string", "nullable": true}, "bill_to_only": {"type": "boolean", "nullable": true}, "type": {"type": "integer", "format": "int32", "nullable": true}, "telnet_id": {"type": "string", "nullable": true}, "company": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CrmCustomerContactResponse": {"type": "object", "properties": {"type": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "work_phone_number": {"type": "string", "nullable": true}, "cell_phone_number": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CrmCustomerFullDataDto": {"type": "object", "properties": {"info": {"$ref": "#/components/schemas/CrmCustomerBaseInfoResponse"}, "address": {"$ref": "#/components/schemas/CrmCustomerAddressResponse"}, "contact": {"$ref": "#/components/schemas/CrmCustomerContactResponse"}}, "additionalProperties": false}, "CrmRPCCustomerByNameDataItemValueResponse": {"type": "object", "properties": {"customerName": {"type": "string", "nullable": true}, "customerCode": {"type": "string", "nullable": true}}, "additionalProperties": false}, "FMSWorkOrderCustomerAddressDto": {"type": "object", "properties": {"country": {"type": "string", "nullable": true}, "state": {"type": "string", "nullable": true}, "city": {"type": "string", "nullable": true}, "zip_code": {"type": "string", "nullable": true}, "address1": {"type": "string", "nullable": true}, "address2": {"type": "string", "nullable": true}, "primary_contact_name": {"type": "string", "nullable": true}, "latitude": {"type": "number", "format": "double"}, "longitude": {"type": "number", "format": "double"}, "location_name": {"type": "string", "nullable": true}}, "additionalProperties": false}, "FMSWorkOrderInfoCustomerDto": {"type": "object", "properties": {"delivered_by": {"type": "string", "nullable": true}, "delivered_location": {"type": "string", "nullable": true}, "delivered_time": {"type": "string", "format": "date-time"}, "desired_service_date": {"type": "string", "format": "date-time"}, "delivery_max_times": {"type": "integer", "format": "int32"}, "pod_name": {"type": "string", "nullable": true}, "polygon_id": {"type": "string", "nullable": true}, "terminal": {"type": "string", "nullable": true}, "terminal_address1": {"type": "string", "nullable": true}, "terminal_address2": {"type": "string", "nullable": true}, "terminal_city": {"type": "string", "nullable": true}, "terminal_lat": {"type": "number", "format": "double"}, "terminal_lng": {"type": "number", "format": "double"}, "terminal_state": {"type": "string", "nullable": true}, "terminal_zipcode": {"type": "string", "nullable": true}, "primary_contact_email": {"type": "string", "nullable": true}, "primary_contact_phone": {"type": "string", "nullable": true}, "polygon_code": {"type": "string", "nullable": true}}, "additionalProperties": false}, "OrderAppointmentDto": {"type": "object", "properties": {"tracking_no": {"type": "string", "nullable": true}, "shipment_order_no": {"type": "string", "nullable": true}, "appointment_time_from": {"type": "string", "format": "date-time", "nullable": true}, "appointment_time_to": {"type": "string", "format": "date-time", "nullable": true}, "confirmed_by_shipper": {"type": "integer", "format": "int32"}, "confirmed_by_contractor": {"type": "integer", "format": "int32"}, "open_time": {"type": "string", "nullable": true}, "close_time": {"type": "string", "nullable": true}, "shipper_name": {"type": "string", "nullable": true}, "address": {"type": "string", "nullable": true}, "request_pickup_date": {"type": "string", "format": "date-time", "nullable": true}, "reference": {"type": "string", "nullable": true}, "sales_order": {"type": "string", "nullable": true}, "purchase_order": {"type": "string", "nullable": true}, "load": {"type": "string", "nullable": true}, "bol": {"type": "string", "nullable": true}, "master_order": {"type": "string", "nullable": true}, "work_order_no": {"type": "string", "nullable": true}, "total_freight": {"type": "string", "nullable": true}, "shipment_order_id": {"type": "integer", "format": "int64"}}, "additionalProperties": false}, "FMSWorkOrderInfoResponseDto": {"type": "object", "properties": {"business_client": {"type": "string", "nullable": true}, "work_order_no": {"type": "string", "nullable": true}, "master_order_no": {"type": "string", "nullable": true}, "company_id": {"type": "integer", "format": "int32"}, "company_code": {"type": "string", "nullable": true}, "work_order_type": {"type": "integer", "format": "int32"}, "service_level": {"type": "integer", "format": "int32"}, "work_order_status": {"type": "integer", "format": "int32"}, "work_order_sub_status": {"type": "integer", "format": "int32"}, "shipment_type": {"type": "integer", "format": "int32"}, "tracking_no": {"type": "string", "nullable": true}, "work_order_customer_address": {"$ref": "#/components/schemas/FMSWorkOrderCustomerAddressDto"}, "work_order_packages": {"type": "array", "items": {"$ref": "#/components/schemas/FMSWorkOrderPackageDto"}, "nullable": true}, "customer": {"$ref": "#/components/schemas/FMSWorkOrderInfoCustomerDto"}}, "additionalProperties": false}, "FMSWorkOrderPackageDto": {"type": "object", "properties": {"package_sequence": {"type": "integer", "format": "int32"}, "package_id": {"type": "integer", "format": "int64"}, "package_no": {"type": "string", "nullable": true}, "tracking_no": {"type": "string", "nullable": true}, "length": {"type": "number", "format": "double"}, "width": {"type": "number", "format": "double"}, "height": {"type": "number", "format": "double"}, "weight": {"type": "number", "format": "double"}, "weight_uom": {"type": "string", "nullable": true}, "freight_class": {"type": "number", "format": "double"}, "volume": {"type": "number", "format": "double"}, "volume_uom": {"type": "string", "nullable": true}, "status": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "OrderHistoryDto": {"type": "object", "properties": {"event_time": {"type": "string", "format": "date-time"}, "event": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "geo_coordinate": {"$ref": "#/components/schemas/GeoCoordinate"}, "driver_name": {"type": "string", "nullable": true}, "carrier_name": {"type": "string", "nullable": true}, "trip_no_or_linehaul": {"type": "string", "nullable": true}, "user_name": {"type": "string", "nullable": true}, "location": {"type": "string", "nullable": true}}, "additionalProperties": false}, "DisplayAddress": {"type": "object", "properties": {"address1": {"type": "string", "nullable": true}, "address2": {"type": "string", "nullable": true}, "city": {"type": "string", "nullable": true}, "state": {"type": "string", "nullable": true}, "country": {"type": "string", "nullable": true}, "location": {"$ref": "#/components/schemas/GeoCoordinate"}, "zipcode": {"type": "string", "nullable": true}}, "additionalProperties": false}, "RpcOrderReferenceDto": {"type": "object", "properties": {"work_order_key": {"type": "integer", "format": "int64"}, "shipment_order_key": {"type": "integer", "format": "int64"}, "shipment_order_no": {"type": "string", "nullable": true}, "work_order_no": {"type": "string", "nullable": true}}, "additionalProperties": false}, "GeoCoordinate": {"type": "object", "properties": {"latitude": {"type": "number", "format": "double"}, "longitude": {"type": "number", "format": "double"}}, "additionalProperties": false}, "SelectAppointmentAdditionRequest": {"type": "object", "properties": {"appointment_type": {"type": "integer", "format": "int32"}, "order_no": {"type": "string", "nullable": true}}, "additionalProperties": false}, "OrderStatusQueryResponse": {"type": "object", "properties": {"order_no": {"type": "string", "nullable": true}, "pro_no": {"type": "string", "nullable": true}, "status_desc": {"type": "string", "nullable": true}, "sub_status_desc": {"type": "string", "nullable": true}, "status": {"type": "integer", "format": "int32"}, "sub_status": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "ServiceLevelEnum": {"enum": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 30], "type": "integer", "format": "int32"}, "WorkOrderPackageItemDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "tracking_no": {"type": "string", "nullable": true}, "code": {"type": "string", "nullable": true}, "shipment_order_no": {"type": "string", "readOnly": true, "nullable": true}, "shipment_order_key": {"type": "integer", "format": "int64"}, "description": {"type": "string", "nullable": true}, "type": {"type": "string", "nullable": true}, "quantity": {"type": "integer", "format": "int32"}, "service_level": {"$ref": "#/components/schemas/ServiceLevelEnum"}, "service_description": {"type": "string", "readOnly": true, "nullable": true}, "unit_price": {"type": "number", "format": "double"}, "invoice_amout": {"type": "number", "format": "double"}}, "additionalProperties": false}, "WorkOrderPackageItemDtoListResultDto": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/WorkOrderPackageItemDto"}, "nullable": true}}, "additionalProperties": false}, "PrimaryContact": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "phone": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ProcessingDays": {"type": "object", "additionalProperties": false, "properties": {}}, "StringFmsSelectListItem": {"type": "object", "properties": {"text": {"type": "string", "nullable": true}, "value": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StringFmsSelectListItemListResultDto": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/StringFmsSelectListItem"}, "nullable": true}}, "additionalProperties": false}, "WorkOrderBasicInfoDto": {"type": "object", "properties": {"order_no": {"type": "string", "nullable": true}, "order_status": {"$ref": "#/components/schemas/WorkOrderStatusEnum"}, "order_sub_status": {"$ref": "#/components/schemas/WorkOrderSubStatusEnum"}, "business_client": {"type": "string", "nullable": true}, "service_level": {"$ref": "#/components/schemas/ServiceLevelEnum"}, "reference": {"type": "string", "nullable": true}, "work_order_type": {"$ref": "#/components/schemas/WorkOrderTypeEnum"}, "master_order_no": {"type": "string", "nullable": true}, "rma": {"type": "string", "nullable": true}, "so_no": {"type": "string", "nullable": true}, "po_no": {"type": "string", "nullable": true}, "order_created_time": {"type": "string", "format": "date-time", "nullable": true}, "desired_service_date": {"type": "string", "format": "date-time", "nullable": true}, "appointment_date": {"type": "string", "format": "date-time", "nullable": true}, "appointment_from": {"type": "string", "format": "date-time", "nullable": true}, "appointment_to": {"type": "string", "format": "date-time", "nullable": true}, "service_complete_time": {"type": "string", "format": "date-time", "readOnly": true, "nullable": true}, "processing_days": {"$ref": "#/components/schemas/ProcessingDays"}, "hold": {"type": "boolean"}, "sales_order": {"type": "string", "nullable": true}}, "additionalProperties": false}, "WorkOrderBillDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "last_update_time": {"type": "string", "format": "date-time", "nullable": true}, "bill_to_contact_id": {"type": "string", "nullable": true}, "primary_contact": {"$ref": "#/components/schemas/PrimaryContact"}, "quote_id": {"type": "string", "nullable": true}, "quote_amount": {"type": "number", "format": "double"}, "note": {"type": "string", "nullable": true}, "last_update_name": {"type": "string", "nullable": true}, "address": {"$ref": "#/components/schemas/DisplayAddress"}, "freight_term": {"type": "string", "nullable": true}}, "additionalProperties": false}, "WorkOrderStatusEnum": {"enum": [1000, 1100, 1200, 1300, 1400, 1500, 1600, 1700, 1800, 1900, 2000], "type": "integer", "format": "int32"}, "WorkOrderSubStatusEnum": {"enum": [1000, 1100, 1210, 1211, 1300, 1400, 1500, 1600, 1700, 1800, 1900, 2000], "type": "integer", "format": "int32"}, "WorkOrderTypeEnum": {"enum": [0, 1], "type": "integer", "format": "int32"}}}}