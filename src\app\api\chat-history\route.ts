import { NextRequest, NextResponse } from 'next/server';
import {
  saveChat,
  getChatHistoryList,
  getChatHistory,
  deleteChat,
  updateChatTitle,
} from '@/utils/serverChatHistoryUtils';
import { ChatHistory } from '@/utils/chatHistoryUtils';
import { nanoid } from 'nanoid';
import { getUserIdFromRequest } from '@/utils/authUtils';
import { addCorsHeaders, createPreflightResponse } from '@/utils/cors';

// GET 请求处理 - 获取所有聊天历史
export async function GET(req: NextRequest) {
  try {
    console.log('GET 请求: 获取聊天历史');

    // 获取当前用户ID
    const userId = await getUserIdFromRequest(req);

    // 如果未登录，返回错误
    if (!userId) {
      console.log('未获取到用户ID，返回401错误');
      return NextResponse.json(
        { error: '用户未登录或会话已过期' },
        { status: 401 }
      );
    }

    console.log('使用用户ID:', userId);

    const url = new URL(req.url);
    const id = url.searchParams.get('id');

    // 如果提供了ID，获取单个聊天历史
    if (id) {
      console.log('获取单个聊天历史:', id);
      const chatHistory = await getChatHistory(id, userId);

      if (!chatHistory) {
        console.log('未找到聊天历史:', id);
        return NextResponse.json({ error: '未找到聊天历史' }, { status: 404 });
      }

      // 确保只返回属于当前用户的聊天历史
      if (chatHistory.userId && chatHistory.userId !== userId) {
        console.log('聊天历史所有者不匹配:', chatHistory.userId, '!=', userId);
        return NextResponse.json(
          { error: '无权访问该聊天历史' },
          { status: 403 }
        );
      }

      return NextResponse.json(chatHistory);
    }

    // 否则获取当前用户的所有聊天历史列表
    console.log('获取用户的所有聊天历史');
    const chatHistories = await getChatHistoryList(userId);
    return NextResponse.json(chatHistories);
  } catch (error) {
    console.error('获取聊天历史失败:', error);
    return NextResponse.json(
      { error: '获取聊天历史失败' },
      { status: 500 }
    );
  }
}

// 处理OPTIONS请求（预检请求）
export async function OPTIONS(req: NextRequest) {
  return createPreflightResponse();
}

// POST 请求处理 - 保存聊天历史
export async function POST(req: NextRequest) {
  try {
    console.log('POST 请求: 保存聊天历史');

    // 获取当前用户ID
    const userId = await getUserIdFromRequest(req);

    const siteId = req.headers.get('X-Site-ID') || '';

    const chatData = await req.json();
    console.log('接收到的聊天数据:', {
      id: chatData.id,
      title: chatData.title,
      messageCount: chatData.messages?.length,
      userId: chatData.userId
    });

    // 检查必要的字段是否存在
    if (!chatData.messages || !Array.isArray(chatData.messages)) {
      console.log('无效的聊天消息');
      return addCorsHeaders(NextResponse.json(
        { error: '无效的聊天消息' },
        { status: 400 }
      ));
    }

    // 如果没有userId但有siteId/domain，使用siteId或domain作为userId
    let finalUserId = userId;
    if (!finalUserId) {
      if (siteId) {
        finalUserId = siteId; 
      } else {
        // 理论上不会走到这里
        return addCorsHeaders(NextResponse.json(
          { error: '无法确定保存目录（无userId/站点/域名）' },
          { status: 400 }
        ));
      }
    }

    let chatHistory: ChatHistory;
    const now = new Date().toISOString();
    // 如果传入了ID，尝试更新现有对话
    if (chatData.id) {
      // 只查找当前userId/站点/域名下的历史
      const existingChat = await getChatHistory(chatData.id, finalUserId);
      if (existingChat) {
        chatHistory = {
          ...existingChat,
          messages: chatData.messages,
          model: chatData.model || existingChat.model,
          audioRecordings: chatData.audioRecordings || existingChat.audioRecordings || [],
          updatedAt: now,
          userId: finalUserId
        };
      } else {
        chatHistory = {
          id: chatData.id,
          title: chatData.title || '新对话',
          messages: chatData.messages,
          model: chatData.model || 'default',
          audioRecordings: chatData.audioRecordings || [],
          createdAt: now,
          updatedAt: now,
          userId: finalUserId
        };
      }
    } else {
      // 创建新的聊天历史
      const chatId = nanoid();
      chatHistory = {
        id: chatId,
        title: chatData.title || '新对话',
        messages: chatData.messages,
        model: chatData.model || 'default',
        audioRecordings: chatData.audioRecordings || [],
        createdAt: now,
        updatedAt: now,
        userId: finalUserId
      };
    }

    // 保存聊天历史
    console.log('保存聊天历史:', chatHistory.id, '用户:', finalUserId);
    await saveChat(chatHistory);

    // 返回带CORS头的响应
    return addCorsHeaders(NextResponse.json(chatHistory));
  } catch (error: any) {
    console.error('保存聊天历史失败:', error);
    return addCorsHeaders(NextResponse.json(
      { error: '保存聊天历史失败', details: error?.message || String(error) },
      { status: 500 }
    ));
  }
}

// DELETE 请求处理 - 从索引中移除聊天历史（不实际删除数据，只做假删除）
export async function DELETE(req: NextRequest) {
  try {
    console.log('DELETE 请求: 从索引中移除聊天历史（假删除）');

    // 获取当前用户ID
    const userId = await getUserIdFromRequest(req);

    // 如果未登录，返回错误
    if (!userId) {
      console.log('未获取到用户ID，返回401错误');
      return NextResponse.json(
        { error: '用户未登录或会话已过期' },
        { status: 401 }
      );
    }

    console.log('使用用户ID:', userId);

    const url = new URL(req.url);
    const id = url.searchParams.get('id');

    if (!id) {
      console.log('未提供聊天历史ID');
      return NextResponse.json(
        { error: '未提供聊天历史ID' },
        { status: 400 }
      );
    }

    // 验证聊天历史所有权
    console.log('验证聊天历史所有权');
    const chatHistory = await getChatHistory(id, userId);
    if (chatHistory && chatHistory.userId && chatHistory.userId !== userId) {
      console.log('聊天历史所有者不匹配:', chatHistory.userId, '!=', userId);
      return NextResponse.json(
        { error: '无权删除该聊天历史' },
        { status: 403 }
      );
    }

    console.log('从索引中移除聊天历史（假删除）:', id);
    const success = await deleteChat(id, userId);

    if (!success) {
      console.log('未找到聊天历史或从索引中移除失败:', id);
      return NextResponse.json(
        { error: '未找到聊天历史或从索引中移除失败' },
        { status: 404 }
      );
    }

    return NextResponse.json({ success: true });
  } catch (error: any) {
    console.error('从索引中移除聊天历史失败:', error);
    return NextResponse.json(
      { error: '从索引中移除聊天历史失败', details: error?.message || String(error) },
      { status: 500 }
    );
  }
}

// PATCH 请求处理 - 更新聊天标题
export async function PATCH(req: NextRequest) {
  try {
    console.log('PATCH 请求: 更新聊天标题');

    // 获取当前用户ID
    const userId = await getUserIdFromRequest(req);

    // 如果未登录，返回错误
    if (!userId) {
      console.log('未获取到用户ID，返回401错误');
      return NextResponse.json(
        { error: '用户未登录或会话已过期' },
        { status: 401 }
      );
    }

    console.log('使用用户ID:', userId);

    const data = await req.json();

    if (!data.id || !data.title) {
      console.log('缺少必要的字段:', data);
      return NextResponse.json(
        { error: '缺少必要的字段: id, title' },
        { status: 400 }
      );
    }

    // 验证聊天历史所有权
    console.log('验证聊天历史所有权');
    const chatHistory = await getChatHistory(data.id, userId);
    if (chatHistory && chatHistory.userId && chatHistory.userId !== userId) {
      console.log('聊天历史所有者不匹配:', chatHistory.userId, '!=', userId);
      return NextResponse.json(
        { error: '无权更新该聊天历史' },
        { status: 403 }
      );
    }

    console.log('更新聊天标题:', data.id, data.title);
    const updatedChat = await updateChatTitle(data.id, data.title, userId);

    if (!updatedChat) {
      console.log('未找到聊天历史或更新失败:', data.id);
      return NextResponse.json(
        { error: '未找到聊天历史或更新失败' },
        { status: 404 }
      );
    }

    return NextResponse.json(updatedChat);
  } catch (error: any) {
    console.error('更新聊天标题失败:', error);
    return NextResponse.json(
      { error: '更新聊天标题失败', details: error?.message || String(error) },
      { status: 500 }
    );
  }
}