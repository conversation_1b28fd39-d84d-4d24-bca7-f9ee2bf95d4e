import path from 'path';
import { ChatHistory } from '../chatHistoryUtils';
import { ChatHistoryStorage, StorageConfig, getStorageConfig } from './types';
import { LocalFileStorage } from './localFileStorage';
import { S3Storage } from './s3Storage';

// 存储配置
const STORAGE_CONFIG = getStorageConfig();

// 创建存储实例
const createStorage = (config: StorageConfig): ChatHistoryStorage => {
  console.log(`使用存储类型: ${config.type}`);

  if (config.type === 's3') {
    return new S3Storage(config);
  } else {
    return new LocalFileStorage(config);
  }
};

// 创建存储实例
const storage = createStorage(STORAGE_CONFIG);

// 导出公共API函数
export const ensureChatHistoryDirExists = (userId?: string) => {
  if (STORAGE_CONFIG.type === 'local' && userId) {
    (storage as LocalFileStorage).ensureUserDirExists(userId);
  }
};

export const saveChat = async (chatHistory: ChatHistory): Promise<void> => {
  return storage.saveChat(chatHistory);
};

export const getChatHistoryList = async (userId?: string): Promise<ChatHistory[]> => {
  return storage.getChatHistoryList(userId);
};

export const getChatHistory = async (id: string, userId?: string): Promise<ChatHistory | null> => {
  return storage.getChatHistory(id, userId);
};

export const deleteChat = async (id: string, userId?: string): Promise<boolean> => {
  return storage.deleteChat(id, userId);
};

// 更新聊天标题
export const updateChatTitle = async (id: string, title: string, userId?: string): Promise<ChatHistory | null> => {
  const chatHistory = await getChatHistory(id, userId);

  if (!chatHistory) {
    return null;
  }

  // 确保userId字段存在
  if (!chatHistory.userId && userId) {
    chatHistory.userId = userId;
  }

  // 更新标题
  chatHistory.title = title;
  chatHistory.updatedAt = new Date().toISOString();

  // 保存更新后的聊天历史
  await saveChat(chatHistory);

  return chatHistory;
};

export const getAllUserIds = async (): Promise<string[]> => {
    return storage.getAllUserIds();
};

// 导出类型和实现，以便在需要时可以直接使用
export * from './types';
export { LocalFileStorage } from './localFileStorage';
export { S3Storage } from './s3Storage';