// 可信任域名缓存管理工具
// 避免每次请求都重新读取环境变量

// 缓存变量
let cachedTrustedDomains: string[] | null = null;
let domainsLoadTimestamp: number = 0;

// 缓存有效期：1小时（可通过环境变量配置）
const DOMAINS_CACHE_TTL = parseInt(process.env.TRUSTED_DOMAINS_CACHE_TTL || '3600000'); // 1小时

/**
 * 获取可信任的域名列表（带缓存）
 * 从环境变量 TRUSTED_DOMAINS 读取，格式为逗号分隔的列表
 * 如果未设置则使用默认值
 * @returns 可信任域名列表
 */
export function getTrustedDomains(): string[] {
  const now = Date.now();

  // 检查缓存是否有效
  if (
    cachedTrustedDomains &&
    domainsLoadTimestamp &&
    now - domainsLoadTimestamp < DOMAINS_CACHE_TTL
  ) {
    return cachedTrustedDomains;
  }

  console.log('[TrustedDomains] 缓存过期或不存在，重新加载可信任域名列表...');

  // 尝试从环境变量获取，格式为逗号分隔的列表
  const domainsStr = process.env.TRUSTED_DOMAINS;
  let domains: string[];

  if (domainsStr) {
    domains = domainsStr.split(',').map(domain => domain.trim());
    console.log('[TrustedDomains] 从环境变量加载可信任域名列表:', domains);
  } else {
    // 使用默认值
    domains = ['localhost:3000', 'localhost', '127.0.0.1'];
    console.log('[TrustedDomains] 未找到环境变量配置，使用默认的可信任域名列表:', domains);
  }

  // 更新缓存
  cachedTrustedDomains = domains;
  domainsLoadTimestamp = now;

  return domains;
}

/**
 * 检查域名是否可信任
 * @param domain 要检查的域名
 * @returns 是否可信任
 */
export function isTrustedDomain(domain: string): boolean {
  if (!domain) return false;

  const trustedDomains = getTrustedDomains();
  
  return trustedDomains.some(trusted =>
    domain === trusted ||
    domain.endsWith(`.${trusted}`) ||
    trusted === '*' // 特殊情况：允许所有域名（仅用于开发）
  );
}

/**
 * 清除缓存（用于测试或强制重新加载）
 */
export function clearTrustedDomainsCache(): void {
  cachedTrustedDomains = null;
  domainsLoadTimestamp = 0;
  console.log('[TrustedDomains] 缓存已清除');
} 