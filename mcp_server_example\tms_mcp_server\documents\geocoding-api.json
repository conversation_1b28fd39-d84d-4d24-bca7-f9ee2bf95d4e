{"openapi": "3.0.0", "info": {"title": "地图地理编码 API", "description": "批量地理位置逆编码服务", "version": "1.0.0"}, "servers": [{"url": "/api", "description": "API 服务器"}], "security": [{"apiKey": []}], "paths": {"/apinew/map/query": {"post": {"security": [{"apiKey": []}], "summary": "批量逆地理编码查询", "description": "将一个或多个经纬度坐标转换为对应的地址信息", "tags": ["地图服务"], "parameters": [{"name": "X-API-KEY", "in": "header", "description": "API访问密钥", "required": true, "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"locations": {"type": "array", "items": {"$ref": "#/components/schemas/Location"}, "minItems": 1, "maxItems": 50, "description": "需要查询的坐标点列表"}}, "required": ["locations"]}}}}, "responses": {"200": {"description": "成功返回地址信息", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 0, "description": "状态码，0 表示成功"}, "message": {"type": "string", "example": "success", "description": "状态描述"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/GeocodingResult"}}}}}}}, "400": {"description": "请求参数错误"}, "401": {"description": "API Key 无效"}, "429": {"description": "超出请求限制"}, "500": {"description": "服务器内部错误"}}}}}, "components": {"securitySchemes": {"apiKey": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "X-API-KEY", "in": "header", "description": "请在请求头中添加 X-API-KEY"}}, "schemas": {"Location": {"type": "object", "properties": {"lat": {"type": "number", "format": "double", "minimum": -90, "maximum": 90, "description": "纬度", "example": 39.9042}, "lng": {"type": "number", "format": "double", "minimum": -180, "maximum": 180, "description": "经度", "example": 116.4074}}, "required": ["lat", "lng"]}, "GeocodingResult": {"type": "object", "properties": {"location": {"$ref": "#/components/schemas/Location"}, "address_components": {"type": "array", "items": {"type": "object", "properties": {"long_name": {"type": "string", "example": "东长安街"}, "short_name": {"type": "string", "example": "东长安街"}, "types": {"type": "array", "items": {"type": "string"}, "example": ["route"]}}}}, "formatted_address": {"type": "string", "description": "完整地址", "example": "北京市东城区东长安街1号"}, "place_id": {"type": "string", "description": "位置唯一标识", "example": "ABC123XYZ"}, "types": {"type": "array", "items": {"type": "string"}, "example": ["street_address"]}}}}}}