import { Message } from '@ai-sdk/react';

// 定义音频录制信息接口
export interface AudioRecording {
  fileName: string;
  path: string;
  size: number;
  duration: number; // 持续时间（秒）
  createdAt: string;
  displayName?: string; // 可选的显示名称，用于UI显示友好的文件名
}

// 定义对话历史接口
export interface ChatHistory {
  id: string;
  title: string;
  messages: Message[];
  model: string;
  createdAt: string;
  updatedAt: string;
  userId?: string; // 添加用户ID字段，可选是为了兼容已有数据
  audioRecordings?: AudioRecording[]; // 音频录制列表
}

// 从用户第一条消息生成对话标题
export const generateChatTitle = (firstUserMessage: string): string => {
  // 提取前20个字符作为标题，如果少于20个字符则使用全部
  if (!firstUserMessage) return 'New Chat';
  
  const messageText = typeof firstUserMessage === 'string' 
    ? firstUserMessage 
    : JSON.stringify(firstUserMessage);
  
  if (messageText.length <= 20) {
    return messageText;
  }
  
  return messageText.substring(0, 20) + '...';
}; 