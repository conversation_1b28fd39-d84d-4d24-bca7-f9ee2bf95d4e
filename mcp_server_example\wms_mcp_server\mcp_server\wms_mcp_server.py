import asyncio
import logging
import os
import sys
import traceback
from typing import Dict, Any, Optional, List
from contextlib import asynccontextmanager
from collections.abc import AsyncIterator
from dataclasses import dataclass

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
)
logger = logging.getLogger("wms_mcp_server")

# Add parent directory to PATH to import tools
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
print(f"Current working directory: {os.getcwd()}")
print(f"Added to path: {parent_dir}")
sys.path.append(parent_dir)

# Try to import FastMCP and WMS tools
try:
    from mcp.server.fastmcp import FastMCP, Context
    logger.info("Successfully imported MCP FastMCP")
except ImportError as e:
    # If modules not found, try to install them
    logger.warning(f"Failed to import required modules: {e}, trying to install...")
    try:
        import subprocess
        subprocess.check_call([sys.executable, "-m", "pip", "install", "mcp[cli]"])
        from mcp.server.fastmcp import FastMCP, Context
        logger.info("Successfully installed and imported MCP")
    except Exception as install_error:
        logger.error(f"Failed to install MCP: {install_error}")
        traceback.print_exc()
        sys.exit(1)

# Ensure config file exists
if not os.path.exists(os.path.join(parent_dir, "config.py")):
    try:
        with open(os.path.join(parent_dir, "config.py"), "w", encoding="utf-8") as f:
            f.write("""import os
from dotenv import load_dotenv

# Try to load environment variables from .env file (if exists)
try:
    load_dotenv()
except ImportError:
    pass

# WMS Configuration
WMS_BASE_URL = os.getenv("WMS_BASE_URL", "https://wms-staging.item.com/api")
CHATBOT_CLIENT_ID = os.getenv("CHATBOT_CLIENT_ID", "42ca5b12-4071-40c1-8eb8-6429ea53db52")
CHATBOT_CLIENT_SECRET = os.getenv("CHATBOT_CLIENT_SECRET", "2e18ed01-f866-4209-8e6e-1a32dec930e5")
IAM_URL = os.getenv("IAM_URL", "https://id-staging.item.com")
""")
        logger.info("Created default config.py file")
    except Exception as config_error:
        logger.error(f"Failed to create config.py: {config_error}")

# Try to import WMS tools
try:
    from tools.wms import WMSTools
    logger.info("Successfully imported WMS tools")
except ImportError as e:
    logger.error(f"Failed to import WMS tools: {e}")
    sys.exit(1)

# Define WMSContext type for storing global WMS tools instance
@dataclass
class WMSContext:
    wms_tools: Optional[WMSTools] = None

# Create server lifecycle manager
@asynccontextmanager
async def wms_lifespan(server: FastMCP) -> AsyncIterator[WMSContext]:
    """Initialize WMS tools instance and clean up resources when server stops"""
    logger.info("Starting WMS MCP Server lifecycle")
    
    # Initialize WMS tools
    try:
        logger.info("Initializing WMS tools")
        wms_tools = WMSTools()
        logger.info("WMS tools initialized successfully")
        
        # Create context object
        context = WMSContext(wms_tools=wms_tools)
        
        # Add context directly to server application state for access in tool functions
        if hasattr(server, 'app'):
            if not hasattr(server.app, 'state'):
                server.app.state = type('State', (object,), {})
            server.app.state.wms_context = context
        
        yield context
    except Exception as e:
        logger.error(f"Failed to initialize WMS tools: {e}")
        traceback.print_exc()
        # Continue even if initialization fails to avoid server startup failure
        yield WMSContext(wms_tools=None)
    finally:
        # Clean up resources when server shuts down
        logger.info("Shutting down WMS MCP Server")

# Create FastMCP instance with specified name and lifecycle manager
mcp = FastMCP(
    name="WMS MCP Server", 
    version="1.0.0",
    description="Warehouse Management System MCP Server",
    lifespan=wms_lifespan,
    dependencies=["aiohttp", "dotenv"]  # Add dependencies for easier installation
)

@mcp.tool()
async def find_wms_api(query: str, top_k: int = 5, ctx: Context = None) -> Dict[str, Any]:
    """
    Search for matching WMS API endpoints
    
    Args:
        query: Search query text
        top_k: Number of results to return
        ctx: MCP context object
        
    Returns:
        List of matching APIs
    """
    logger.info(f"Calling find_wms_api with query: {query}, top_k: {top_k}")
    
    # Get WMS tools instance - try different context attribute names
    wms_tools = None
    if ctx:
        # Check ctx itself and possible nested locations
        if hasattr(ctx, 'app') and hasattr(ctx.app, 'state') and hasattr(ctx.app.state, 'wms_context'):
            wms_tools = ctx.app.state.wms_context.wms_tools
        elif hasattr(ctx, 'lifespan_ctx'):
            wms_tools = getattr(ctx.lifespan_ctx, 'wms_tools', None)
        elif hasattr(ctx, 'wms_context'):
            wms_tools = ctx.wms_context.wms_tools
        elif hasattr(ctx, 'wms_tools'):
            wms_tools = ctx.wms_tools
        # Log context structure for debugging
        logger.debug(f"Context structure: {dir(ctx)}")
    
    # If cannot get from context, create a new instance
    if not wms_tools:
        logger.warning("Could not get WMS tools from context, creating a new instance")
        try:
            wms_tools = WMSTools()
        except Exception as e:
            error_msg = f"Failed to create WMS tools: {e}"
            logger.error(error_msg)
            raise Exception(error_msg)
    
    try:
        # Call find_wms_api method of WMS tools
        result = await wms_tools.find_wms_api(query=query, top_k=top_k)
        # Log processing, avoid string type errors
        if isinstance(result, dict):
            apis_count = len(result.get('apis', []))
            logger.info(f"find_wms_api found {apis_count} APIs")
        else:
            logger.info(f"find_wms_api completed successfully, result type: {type(result).__name__}")
        return result
    except Exception as e:
        logger.error(f"Error in find_wms_api execution: {e}")
        traceback.print_exc()
        raise

@mcp.tool()
async def call_wms_api(
    path: str, 
    method: str, 
    params: Dict[str, Any] = {}, 
    headers: Dict[str, Any] = None,
    ctx: Context = None
) -> Dict[str, Any]:
    """
    Call a specific WMS API
    
    Args:
        path: API path
        method: HTTP method (GET, POST, PUT, DELETE)
        params: API parameters
        headers: Request headers
        ctx: MCP context object
        
    Returns:
        API call result
    """
    logger.info(f"Calling call_wms_api with path: {path}, method: {method}, params: {params}")
    
    # Validate headers must contain necessary information
    if not headers or not isinstance(headers, dict):
        logger.error("Missing required headers for API call")
        return {
            "success": False,
            "error": "Missing headers",
            "message": "API call requires headers with Authorization, x-tenant-id, and x-facility-id"
        }
    
    # Check if headers include tenant and facility information
    # Get from requestContext first, then from headers parameter
    tenant_id = headers.get('x-tenant-id', '')
    facility_id = headers.get('x-facility-id', '')
    
    # Log passed tenant and facility IDs
    logger.info(f"Using tenant_id: {tenant_id}, facility_id: {facility_id}")
    
    # Get WMS tools instance - try different context attribute names
    wms_tools = None
    if ctx:
        # Check ctx itself and possible nested locations
        if hasattr(ctx, 'app') and hasattr(ctx.app, 'state') and hasattr(ctx.app.state, 'wms_context'):
            wms_tools = ctx.app.state.wms_context.wms_tools
        elif hasattr(ctx, 'lifespan_ctx'):
            wms_tools = getattr(ctx.lifespan_ctx, 'wms_tools', None)
        elif hasattr(ctx, 'wms_context'):
            wms_tools = ctx.wms_context.wms_tools
        elif hasattr(ctx, 'wms_tools'):
            wms_tools = ctx.wms_tools
    
    # If cannot get from context, create a new instance
    if not wms_tools:
        logger.warning("Could not get WMS tools from context, creating a new instance")
        try:
            wms_tools = WMSTools()
        except Exception as e:
            error_msg = f"Failed to create WMS tools: {e}"
            logger.error(error_msg)
            raise Exception(error_msg)
    
    try:
        # Call call_wms_api method of WMS tools
        result = await wms_tools.call_wms_api(
            path=path,
            method=method,
            params=params,
            headers=headers
        )
        # Safely log results
        logger.info(f"call_wms_api completed successfully, result type: {type(result).__name__}")
        if isinstance(result, dict):
            status = "Success" if result.get('success') else "Failure"
            logger.info(f"call_wms_api call {status}, status_code: {result.get('status_code')}")
        return result
    except Exception as e:
        logger.error(f"Error in call_wms_api execution: {e}")
        traceback.print_exc()
        raise

# Add WMS tools guide prompt
@mcp.prompt()
def wms_guide() -> str:
    """WMS tools usage guide prompt"""
    try:
        # Try to read prompt from file
        wms_prompt_path = os.path.join(parent_dir, "prompts", "wms_prompt.py")
        logger.info(f"Reading WMS prompt from: {wms_prompt_path}")
        
        if os.path.exists(wms_prompt_path):
            try:
                with open(wms_prompt_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    # Extract WMS_TOOLS_PROMPT variable value
                    if "WMS_TOOLS_PROMPT = '''" in content:
                        start_index = content.find("WMS_TOOLS_PROMPT = '''") + len("WMS_TOOLS_PROMPT = '''")
                        end_index = content.find("'''", start_index)
                        if end_index > start_index:
                            prompt_content = content[start_index:end_index]
                            logger.info(f"Successfully extracted WMS prompt content, length: {len(prompt_content)}")
                            return prompt_content
            except Exception as read_error:
                logger.error(f"Error reading prompt file: {read_error}")
    except Exception as e:
        logger.error(f"Error creating prompt: {e}")
    
    # If reading fails or file not found, return default prompt
    return """
    ## Guide for WMS Tools
    
    WMS Tools provides functionality for managing warehouse operations.
    
    Available tools:
    
    1. find_wms_api - Search for relevant WMS API endpoints
       Usage: Use this tool to find API endpoints that match your query
       
    2. call_wms_api - Call a specific WMS API endpoint
       Usage: After finding the right API, use this to execute the call
       
    When using these tools, follow these steps:
    1. First, use find_wms_api to discover the appropriate API endpoint
    2. Review the results to find the best matching API
    3. Use call_wms_api with the exact path and parameters from the API details
    
    Example workflow:
    1. find_wms_api(query="query inventory")
    2. Review results to find inventory API
    3. call_wms_api(path="/inventory/search", method="POST", params={"itemId": "123"})
    """

# Add resource node providing WMS API documentation
@mcp.resource("wms://api-docs")
def get_wms_api_docs() -> str:
    """Get WMS API documentation summary"""
    return """
    # WMS API Documentation Summary
    
    WMS system provides the following main API categories:
    
    ## Inventory Related
    - /inventory/search - Search inventory
    - /inventory/search-sum-by-paging - Paginated inventory summary search
    - /inventory/count - Inventory count
    
    ## Order Related
    - /order/search - Search orders
    - /order/{id} - Get order details
    - /order/commit - Commit order
    
    ## Receipt Related
    - /receipt/search - Search receipts
    - /receipt/{id} - Get receipt details
    
    Use the find_wms_api tool to search for more detailed APIs.
    """

# Add resource node providing WMS tools usage guide
@mcp.resource("wms://tools-guide")
def get_wms_tools_guide() -> str:
    """Get WMS tools usage guide"""
    return """
    # WMS Tools Usage Guide
    
    ## find_wms_api
    
    Used to search for matching WMS API endpoints.
    
    ```python
    await find_wms_api(query="search orders", top_k=5)
    ```
    
    Parameters:
    - query: Search query text
    - top_k: Number of results to return
    
    Returns:
    List of matching APIs including path, method, parameters, etc.
    
    ## call_wms_api
    
    Used to call a specific WMS API.
    
    ```python
    await call_wms_api(
        path="/order/search",
        method="POST",
        params={"orderId": "DN-12345"},
        headers={"Authorization": "Bearer YOUR_TOKEN"}
    )
    ```
    
    Parameters:
    - path: API path
    - method: HTTP method (GET, POST, PUT, DELETE)
    - params: API parameters
    - headers: Custom request headers, including authorization token (e.g., {"Authorization": "Bearer YOUR_TOKEN"})
    
    Returns:
    API call result
    """

if __name__ == "__main__":
    # Parse command line arguments
    import argparse
    parser = argparse.ArgumentParser(description="WMS MCP Server")
    parser.add_argument("--host", default="0.0.0.0", help="Bind socket to this host")
    parser.add_argument("--port", type=int, default=8000, help="Bind socket to this port")
    parser.add_argument("--transport", choices=["stdio", "sse"], default="sse", help="Transport protocol (stdio or sse)")
    args = parser.parse_args()
    
    logger.info(f"Starting WMS MCP server with {args.transport} transport")
    
    # 根据传输模式选择不同的启动方式
    if args.transport == "sse":
        # 使用 starlette 和 uvicorn 运行 SSE 服务器
        import uvicorn
        from starlette.applications import Starlette
        from starlette.routing import Mount
        
        # 创建一个 Starlette 应用并挂载 MCP 的 SSE 应用
        app = Starlette(routes=[Mount("/", app=mcp.sse_app())])
        
        # 使用 uvicorn 运行
        uvicorn.run(
            app, 
            host=args.host, 
            port=args.port,
            timeout_keep_alive=300,  # 保持连接超时
            timeout_graceful_shutdown=300  # 优雅关闭超时
        )
    else:
        # 对于 stdio 模式，直接使用 run 方法
        mcp.run(transport="stdio") 