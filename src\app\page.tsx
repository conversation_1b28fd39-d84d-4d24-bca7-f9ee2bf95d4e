'use client';

import Chat from './components/Chat';
import Sidebar from './components/Sidebar';
import { useChat } from './contexts/ChatContext';
import ProtectedRoute from './components/ProtectedRoute';
import WelcomeLogin from './components/WelcomeLogin';
import { useEffect, useCallback } from 'react';
import '@/styles/item-design-system.css';

export default function Home() {
  const {
    selectedChatId,
    createNewChat,
    selectChat,
    deleteChat,
    updateChatTitle
  } = useChat();

  // 创建新聊天的处理函数 - 使用useCallback包装
  const handleNewChat = useCallback(() => {
    // 创建新聊天并获取新的聊天ID
    const newChatId = createNewChat();

    // 确保通过selectChat函数选择这个新聊天，这步很重要
    // 传递isNewlyCreated=true参数，避免尝试加载不存在的聊天历史
    if (newChatId) {
      selectChat(newChatId, true);
    }
  }, [createNewChat, selectChat]);

  // 当页面加载时，如果没有选中的聊天，自动创建一个新聊天
  // 这样相当于页面加载时自动点击"New Connection"按钮
  useEffect(() => {
    if (!selectedChatId) {
      handleNewChat();
    }
  }, [selectedChatId, handleNewChat]);

  // 处理选择聊天
  const handleSelectChat = async (id: string) => {
    await selectChat(id);
  };

  // 处理删除聊天
  const handleDeleteChat = async (id: string) => {
    // 直接删除，Sidebar中会显示确认对话框
    await deleteChat(id);
  };

  // 处理更新聊天标题
  const handleEditChatTitle = async (id: string, newTitle: string) => {
    await updateChatTitle(id, newTitle);
  };

  return (
    <div className="flex h-screen overflow-hidden bg-item-bg-primary text-white">
      {/* Sidebar - Chat history */}
      <Sidebar
        onNewChat={handleNewChat}
        onSelectChat={handleSelectChat}
        onDeleteChat={handleDeleteChat}
        onEditChatTitle={handleEditChatTitle}
      />

      {/* Main content area */}
      <div className="flex-1 flex flex-col relative overflow-hidden">
        {/* Chat area or welcome screen */}
        <div className="flex-1 relative overflow-hidden">
          <ProtectedRoute fallback={<WelcomeLogin />}>
            <Chat />
          </ProtectedRoute>
        </div>
      </div>
    </div>
  );
}