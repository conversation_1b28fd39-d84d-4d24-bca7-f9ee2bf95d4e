'use client';

import { useEffect } from 'react';
import { useError } from '../contexts/ErrorContext';

export default function ErrorDisplay() {
  const { error, clearError } = useError();
  
  // 自动关闭错误的计时器
  useEffect(() => {
    if (error) {
      const timer = setTimeout(() => {
        clearError();
      }, 5000);
      
      return () => clearTimeout(timer);
    }
  }, [error, clearError]);
  
  if (!error) return null;
  
  // 从Error对象或字符串中获取错误消息
  const errorMessage = error instanceof Error ? error.message : error;
  
  return (
    <div className="fixed top-4 right-4 z-50">
      <div className="bg-red-900/90 border border-red-800 shadow-lg rounded-lg px-4 py-3 text-white max-w-sm flex items-start">
        <div className="text-red-400 mr-3 flex-shrink-0">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-6 h-6">
            <path strokeLinecap="round" strokeLinejoin="round" d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z" />
          </svg>
        </div>
        <div>
          <div className="font-semibold mb-1">错误</div>
          <div className="text-sm">{errorMessage}</div>
        </div>
        <button 
          onClick={clearError}
          className="ml-auto -mr-1 text-red-400 hover:text-white"
        >
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5">
            <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>
    </div>
  );
} 