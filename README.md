# CyberBot - Multimodal AI Chat System

CyberBot is a modern, extensible AI chat system built with Next.js, supporting multiple large language models, the MCP (Model Control Protocol) tool system, memory management, reporting, and secure multi-user authentication.

---

## Directory Structure

```
cyberbot/
├── src/
│   ├── app/                # Main Next.js application
│   │   ├── api/            # API routes (RESTful, MCP, auth, reports, etc.)
│   │   ├── components/     # UI components (Chat, Sidebar, Cards, UserMenu, etc.)
│   │   ├── contexts/       # Global state management (<PERSON><PERSON>, <PERSON><PERSON>, Error, Success)
│   │   ├── admin/          # Admin dashboard (memory management, index migration)
│   │   ├── chat-reports/   # Chat reports and analytics
│   │   ├── auth/           # Authentication and callbacks
│   │   ├── middleware/     # API/page middleware
│   │   ├── unauthorized/   # Unauthorized page
│   │   ├── memory/         # Memory-related pages
│   │   ├── embed/          # Embeddable pages
│   │   └── layout.tsx/page.tsx # Global layout and entry
│   ├── hooks/              # Custom React hooks
│   ├── utils/              # Utility functions and contexts
│   ├── prompts/            # System prompt templates
│   ├── services/           # Business service layer
│   ├── tools/              # MCP tool implementations
│   ├── chat-history/       # Local chat history storage
│   ├── chat-report/        # Report-related logic
│   ├── types/              # Type definitions
│   ├── lib/                # Shared libraries
│   └── styles/             # Global styles
├── public/                 # Static assets
├── mcp.json                # MCP server configuration
├── .env.local(.example)    # Environment variables
├── package.json            # Dependencies and scripts
├── README.md               # Project documentation
└── ...                     # Other configs and caches
```

---

## Tech Stack

- **Frontend**: Next.js 15, React 18, TypeScript, Tailwind CSS
- **Backend/API**: Next.js API, MCP protocol, OpenAI/Anthropic/Gemini SDKs
- **Tool System**: MCP (Model Control Protocol) plugin system
- **Database**: Local JSON/SQLite (extensible)
- **Security**: Multi-user authentication, permission middleware
- **Reporting/Memory**: Built-in reporting and memory management

---

## Key Features

- Multi-model chat (GPT-4o, Claude, Gemini, etc.)
- MCP tool extension (weather, clock, WMS warehouse management, etc.)
- Chat history and multi-user memory management
- Automated chat report generation and analytics
- Image upload and multimodal conversation
- Modern UI, dark theme, responsive design
- Robust user authentication and access control
- Admin dashboard (memory, index, user management)

---

## Main Directory Details

- `src/app/components/`: Core UI components (Chat, Sidebar, Cards, UserMenu, ErrorDisplay, etc.)
- `src/app/api/`: RESTful API routes (chat, history, reports, MCP tools, auth, etc.)
- `src/app/contexts/`: Global state (AuthContext, ChatContext, ErrorContext, SuccessContext)
- `src/app/admin/`: Admin dashboard pages (memory management, index migration)
- `src/app/chat-reports/`: Chat report pages and dynamic routes
- `src/hooks/`: Custom hooks (useChatHistory, usePermission, useUserPreferences)
- `src/utils/`: Utility functions and contexts (permissions, user, API wrappers, storage, etc.)
- `src/tools/`: MCP tool implementations (mcpTools, weatherTool, clockTool, memoryTools, toolRegistry)
- `src/services/`: Business services (e.g., memoryService)
- `src/prompts/`: System prompt templates
- `src/chat-history/`: Local chat history storage (per user)
- `src/types/`: Type definitions

---

## MCP Tool System

- **MCP Protocol**: Secure interaction between AI and external systems, auto-discovery of tools
- **MCP Server Example**: `mcp_server_example/` provides a Python WMS (Warehouse Management System) MCP server
- **Configuration**: `mcp.json` supports multiple MCP servers, hot-pluggable
- **Developing New Tools**: Implement the MCP protocol and register in `mcp.json` for automatic integration

---

## Authentication & Security

- Multi-user login/logout, user info display
- Permission middleware (e.g., apiAuthMiddleware, roleAuthMiddleware)
- Unauthenticated users are redirected to welcome/login page
- User info and state managed via React Context

---

## Reporting & Memory Management

- Automatic chat history archiving, multi-user support
- Report pages support dynamic queries by date/user
- Admin dashboard for managing memory, indexes, etc.

---

## Security Validation for Embedded Chat

### Security Mechanisms for Unauthenticated Usage

The embedded chat component (`src/embed/`) implements security validation through the following mechanisms:

#### 1. Token Lifecycle Management

##### Token Retrieval
- Automatic token detection from multiple sources
- Priority order for token keys:
  - `auth_token`
  - `iam_access_token`
  - `third_party_token`
  - `token`
  - `accessToken`
  - `access_token`
  - `jwt`
- Support for localStorage and cookie storage
- Extensible `TokenProvider` interface

##### Token Generation
- OAuth2.0 authorization code flow
- Token exchange via `/api/auth/token`
- Basic Auth for client authentication
- Customizable scope and redirect_uri
- Returns access_token and refresh_token

##### Token Refresh
- Refresh via `/api/auth/refresh` endpoint
- Uses refresh_token to obtain new access_token
- Automatic refresh mechanism
- Fallback to re-login on refresh failure

##### Token Validation
- JWKS (JSON Web Key Set) signature verification
- Validates token issuer and expiration
- Optional audience validation
- Token parsing and user info extraction

#### 2. Context Data Retrieval
- Automatic tenant and facility info retrieval
- Tenant ID keys:
  - `defaultCompanyId`
  - `tenantId`
  - `companyId`
  - `company_id`
- Facility ID keys:
  - `defaultFacility`
  - `facility`
  - `facilityId`
  - `facility_id`
- Error handling and JSON parsing
- Extensible `ContextDataProvider` interface

#### 3. Site Configuration
- Site-specific config via `siteId`
- Default config includes:
  - Theme (light/dark)
  - Position (bottom-right/bottom-left/top-right/top-left)
  - Custom site settings
- Dynamic config loading
- Extensible `ConfigLoader` interface

#### 4. Security Isolation
- Complete style and DOM isolation via Shadow DOM
- Closed mode Shadow DOM for external access prevention
- Event and style containment

#### 5. API Security Validation

##### Standard API Routes
- Unified API authentication via `apiAuthMiddleware`
- Multiple token sources:
  - Authorization header
  - Cookie
  - x-session-token header
- Token validation and user info extraction
- Custom error handling

##### Embedded API Routes
- Special handling for `/api/chat-embed` and `/api/chat-history`
- Two authentication methods:
  1. Bearer token (same as standard API)
  2. Temporary token (X-Temp-Token header)
- Site ID validation
- Rate limiting:
  - Applied only to temporary token requests
  - Based on site ID and client IP
  - Default: 60 requests per minute
  - Customizable via site config
  - Rate limit info in response headers:
    - X-RateLimit-Limit
    - X-RateLimit-Remaining
    - X-RateLimit-Reset
  - 429 status code on limit exceeded
  - In-memory storage (extensible to Redis)

##### Role-based Access Control
- Role-based access via `roleAuthMiddleware`
- Role extraction from token
- Role mapping and permission checks
- Custom role-protected routes

#### 6. Initialization
```typescript
CyberAgent.init({
  theme?: 'light' | 'dark';
  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';
  minimized?: boolean;
  siteId?: string;
  welcomeSuggestions?: string[];
  apiUrl?: string;
  hideToolUsage?: boolean;
  historyCacheTime?: number;
  publicKey?: string;
});
```

#### 7. Security Best Practices
- Secure storage for sensitive data
- Public key for encrypted communication
- Tool usage control via hideToolUsage
- Custom API URL support
- Runtime config updates
- Comprehensive error handling
- CORS and rate limiting
- Environment variable management

#### 8. Environment Configuration
```env
# IAM Configuration
NEXT_PUBLIC_IAM_ENDPOINT=your_iam_endpoint
NEXT_PUBLIC_IAM_CLIENT_ID=your_client_id
IAM_CLIENT_SECRET=your_client_secret

# Optional Configuration
NEXT_PUBLIC_API_URL=your_api_url
NEXT_PUBLIC_PUBLIC_KEY=your_public_key

# Rate Limiting (Optional, Default: 60/min)
SITE_RATE_LIMIT_MAX_REQUESTS=60
SITE_RATE_LIMIT_WINDOW_SECONDS=60
```

---

## Development & Deployment

1. Copy `.env.local.example` to `.env.local` and configure environment variables
2. Install dependencies:
   ```bash
   npm install
   ```
3. Start development server:
   ```bash
   npm run dev
   ```
4. Build for production:
   ```bash
   npm run build
   npm run start
   ```
5. (Optional) Start MCP server:
   ```bash
   cd mcp_server_example/wms_mcp_server
   pip install -r requirements.txt
   python mcp_server/wms_mcp_server.py
   ```

---

## Contribution & Best Practices

- Place components, tools, APIs, hooks, services, types in their respective directories
- Use TypeScript, clear Props, functional components, and hooks
- Tools and services should be well-commented and strongly typed
- UI uses Tailwind CSS, supports dark mode and responsive design
- New MCP tools: implement protocol and register in `mcp.json`
- Update this README for major changes

---

## Troubleshooting

- **MCP connection failure**: Check server status, `mcp.json` config, console logs
- **AI model errors**: Check API keys, environment variables, request format
- **Image upload issues**: Check format, size, browser console
- **Auth issues**: Check login state, Context, API responses

---

## License

MIT

---

For detailed developer docs, MCP protocol specs, or advanced customization, see subdirectory READMEs or contact the maintainers. 