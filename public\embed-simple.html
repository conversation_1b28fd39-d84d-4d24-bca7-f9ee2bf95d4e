<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>CyberBot 简单嵌入示例</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      margin: 0;
      padding: 20px;
      background-color: #f5f5f5;
    }
    .container {
      max-width: 800px;
      margin: 0 auto;
      background-color: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }
    h1 {
      color: #333;
      margin-top: 0;
    }
    .code-block {
      background-color: #f1f5f9;
      padding: 16px;
      border-radius: 6px;
      font-family: monospace;
      margin: 20px 0;
      overflow-x: auto;
    }
    .controls {
      margin-top: 30px;
      padding: 16px;
      border: 1px solid #e5e7eb;
      border-radius: 6px;
      background-color: #f9fafb;
    }
    button {
      background-color: #3b82f6;
      color: white;
      border: none;
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
      margin-right: 8px;
      font-size: 14px;
    }
    button:hover {
      background-color: #2563eb;
    }
    select {
      padding: 8px;
      border-radius: 4px;
      border: 1px solid #d1d5db;
      margin-left: 8px;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>CyberBot 简单嵌入示例</h1>
    <p>这个页面展示了如何以最简单的方式嵌入AI助手到任何网站。只需要添加一行脚本标签，AI助手就会自动初始化并显示在页面右下角。</p>

    <div class="code-block">
      &lt;!-- 只需添加这一行代码 --&gt;<br>
      &lt;script src="https://your-domain.com/embed-bundle.js"&gt;&lt;/script&gt;
    </div>

    <p>就是这么简单！AI助手会自动：</p>
    <ul>
      <li>创建一个浮动聊天窗口在页面右下角</li>
      <li>使用Shadow DOM实现样式隔离</li>
      <li>自动从localStorage中查找认证令牌（如果有）</li>
      <li>提供简单的API用于控制聊天窗口</li>
    </ul>

    <div class="controls">
      <h3>控制面板</h3>
      <div>
        <button onclick="window.CyberAgent.minimize()">最小化</button>
        <button onclick="window.CyberAgent.maximize()">最大化</button>
        <button onclick="window.CyberAgent.toggle()">切换</button>
      </div>

      <div style="margin-top: 16px;">
        <label for="theme-select">主题:</label>
        <select id="theme-select" onchange="window.CyberAgent.setTheme(this.value)">
          <option value="dark">深色</option>
          <option value="light">浅色</option>
        </select>
      </div>
    </div>

    <h3>高级用法</h3>
    <p>如果需要更多控制，可以使用以下API：</p>
    <div class="code-block">
      // 手动初始化（如果禁用了自动初始化）<br>
      window.CyberAgent.init({<br>
      &nbsp;&nbsp;theme: 'light', // 'light' 或 'dark'<br>
      &nbsp;&nbsp;position: 'bottom-left' // 'bottom-right', 'bottom-left', 'top-right', 'top-left'<br>
      });<br><br>

      // 控制聊天窗口<br>
      window.CyberAgent.minimize(); // 最小化<br>
      window.CyberAgent.maximize(); // 最大化<br>
      window.CyberAgent.toggle(); // 切换状态<br>
      window.CyberAgent.setTheme('light'); // 设置主题<br>
      window.CyberAgent.destroy(); // 完全移除聊天窗口<br>
    </div>

    <h3>禁用自动初始化</h3>
    <p>如果你希望完全控制聊天组件的初始化时机，可以在脚本标签中添加 <code>data-auto-init="false"</code> 属性来禁用自动初始化：</p>
    <div class="code-block">
      &lt;!-- 禁用自动初始化 --&gt;<br>
      &lt;script src="https://your-domain.com/embed-bundle.js" data-auto-init="false"&gt;&lt;/script&gt;
    </div>
    
    <p>然后，在你希望的时机手动初始化聊天组件：</p>
    <div class="code-block">
      // 在需要的时机手动初始化<br>
      document.getElementById('start-chat').addEventListener('click', function() {<br>
      &nbsp;&nbsp;window.CyberAgent.init({<br>
      &nbsp;&nbsp;&nbsp;&nbsp;theme: 'dark',<br>
      &nbsp;&nbsp;&nbsp;&nbsp;minimized: false<br>
      &nbsp;&nbsp;});<br>
      });<br>
    </div>
    
    <p>这种方式特别适合以下场景：</p>
    <ul>
      <li>希望在用户执行特定操作后才显示聊天窗口</li>
      <li>需要在初始化前准备特定的配置数据</li>
      <li>需要控制多个页面元素的加载顺序</li>
    </ul>
  </div>

  <!-- 加载嵌入脚本 - 禁用自动初始化 -->
  <script src="/embed-bundle.js" ></script>
  

</body>
</html>
