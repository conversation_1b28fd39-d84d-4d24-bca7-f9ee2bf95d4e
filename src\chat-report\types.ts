import { Message } from '@ai-sdk/react';

// Report analyzing results
export interface ChatAnalysisResult {
  summary: string;
  resolved: boolean;
  unresolvedReason?: string;
  userQuestions: string[];
  agentResponses: string[];
  keyInsights: string[];
  dataRetrievalIssue?: boolean;
  parameterExtractionIssue?: boolean;
  entityRecognitionIssue?: boolean;
  toolUsageIssue?: boolean;
  issueDetails?: string;
  failedTools?: string[];
  failedParameters?: any; // Can be a string (raw JSON) or an object
  rawToolCalls?: string[]; // Raw tool call JSON objects
  userActualIntent?: string;
  improvementSuggestions?: string[];
  chatId?: string;
}

// Chat Report interface
export interface ChatReport {
  id: string;
  date: string; // YYYY-MM-DD format
  chatIds: string[];
  chatUserMap: {
    [chatId: string]: string;  // chatId -> userId
  };
  totalChats: number;
  totalMessages: number;
  totalResolvedChats: number;
  totalUnresolvedChats: number;
  chatAnalyses: {
    [chatId: string]: ChatAnalysisResult;
  };
  createdAt: string;
}

// Report search params
export interface ReportSearchParams {
  fromDate?: string;
  toDate?: string;
  resolved?: boolean;
  query?: string;
}