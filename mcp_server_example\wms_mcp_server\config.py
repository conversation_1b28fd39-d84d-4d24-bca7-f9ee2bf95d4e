import os
from dotenv import load_dotenv

# 尝试加载.env文件中的环境变量（如果存在）
load_dotenv()

# WMS Configuration
WMS_BASE_URL = os.getenv("WMS_BASE_URL", "https://wms-staging.item.com/api")
CHATBOT_CLIENT_ID = os.getenv("CHATBOT_CLIENT_ID", "42ca5b12-4071-40c1-8eb8-6429ea53db52")
CHATBOT_CLIENT_SECRET = os.getenv("CHATBOT_CLIENT_SECRET", "2e18ed01-f866-4209-8e6e-1a32dec930e5")
IAM_URL = os.getenv("IAM_URL", "https://id-staging.item.com")