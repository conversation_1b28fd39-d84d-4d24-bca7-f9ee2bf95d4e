import { nanoid } from 'nanoid';
import { ChatReport, ReportSearchParams } from './types';
import { getStorageConfig } from '@/utils/storage/types';
import { createReportStorage } from '@/utils/storage/reportStorage';

// 获取存储配置
const STORAGE_CONFIG = getStorageConfig();

// 创建报告存储实例
const reportStorage = createReportStorage(STORAGE_CONFIG);

// Special report ID for storing metadata
const METADATA_REPORT_ID = 'report-metadata';

// Get the latest report date from backend storage
export const getLatestReportDate = async (): Promise<string | null> => {
  try {
    // Try to get the metadata report
    const metadata = await reportStorage.getReportById(METADATA_REPORT_ID);

    // First check for timestamp (new format), then fall back to date (old format)
    if (metadata?.latestReportTimestamp) {
      // Extract just the date part from the timestamp
      return metadata.latestReportTimestamp.split('T')[0] || null;
    }

    return metadata?.latestReportDate || null;
  } catch (error) {
    console.error('Error getting latest report date:', error);
    return null;
  }
};

// Get the latest report timestamp from backend storage (full ISO string)
export const getLatestReportTimestamp = async (): Promise<string | null> => {
  try {
    // Try to get the metadata report
    const metadata = await reportStorage.getReportById(METADATA_REPORT_ID);

    // First check for timestamp (new format), then convert date to timestamp if needed
    if (metadata?.latestReportTimestamp) {
      return metadata.latestReportTimestamp;
    } else if (metadata?.latestReportDate) {
      // Convert date to end-of-day timestamp for backward compatibility
      return `${metadata.latestReportDate}T23:59:59.999Z`;
    }

    return null;
  } catch (error) {
    console.error('Error getting latest report timestamp:', error);
    return null;
  }
};

// Set the latest report date in backend storage
export const setLatestReportDate = async (date: string): Promise<void> => {
  try {
    // Create a timestamp with current time
    const now = new Date().toISOString();
    const timestamp = now;

    // Get current metadata or create new one
    let metadata = await reportStorage.getReportById(METADATA_REPORT_ID);

    if (!metadata) {
      // Create new metadata object
      metadata = {
        id: METADATA_REPORT_ID,
        latestReportDate: date, // For backward compatibility
        latestReportTimestamp: timestamp, // New field with precise timestamp
        createdAt: now,
        updatedAt: now
      };
    } else {
      // Always update the timestamp to the current time
      metadata.latestReportDate = date; // For backward compatibility
      metadata.latestReportTimestamp = timestamp;
      metadata.updatedAt = now;
    }

    // Save the metadata
    await reportStorage.saveMetadata(metadata);
    console.log(`Updated latest report timestamp to ${timestamp}`);
  } catch (error) {
    console.error('Error setting latest report date:', error);
  }
};

// Generate a new report ID
const generateReportId = () => {
  return nanoid();
};

// Save a report
export const saveReport = async (report: ChatReport): Promise<ChatReport> => {
  const savedReport = await reportStorage.saveReport(report);

  // Update the latest report date in backend storage
  await setLatestReportDate(report.date);

  return savedReport;
};

// Get a report by date
export const getReportByDate = async (date: string): Promise<ChatReport | null> => {
  return reportStorage.getReportByDate(date);
};

// List all reports (metadata only, not full content)
export const listAllReports = async (): Promise<Array<Omit<ChatReport, 'chatAnalyses'>>> => {
  return reportStorage.listAllReports();
};

// Search reports
export const searchReports = async (params: ReportSearchParams): Promise<Array<Omit<ChatReport, 'chatAnalyses'>>> => {
  return reportStorage.searchReports(params);
};

// Create a new report
export const createReport = (date: string): ChatReport => {
  return {
    id: generateReportId(),
    date,
    chatIds: [],
    chatUserMap: {},
    totalChats: 0,
    totalMessages: 0,
    totalResolvedChats: 0,
    totalUnresolvedChats: 0,
    chatAnalyses: {},
    createdAt: new Date().toISOString()
  };
};