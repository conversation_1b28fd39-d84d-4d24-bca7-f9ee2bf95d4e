'use client';

import React, { useState } from 'react';

interface SequentialThinkingCardProps {
  toolInvocation: any;
}

export default function SequentialThinkingCard({ toolInvocation }: SequentialThinkingCardProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  
  // Extract thought content directly from toolInvocation args
  let thought = '';
  
  if (toolInvocation?.args) {
    thought = toolInvocation.args.thought || '';
  }
  
  return (
    <div className="bg-slate-800/30 rounded-lg border-l-4 border-slate-600/30 border-t border-r border-b border-slate-700/30 pl-3 pr-4 py-3 text-readable shadow-md my-2">
      <div className="flex justify-between cursor-pointer" onClick={() => setIsExpanded(!isExpanded)}>
        <div className="flex items-start flex-1">
          <div className="bg-gray-800/60 p-1 rounded mr-2 mt-0.5">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" strokeWidth={1.5} stroke="currentColor" className="w-4 h-4 text-gray-400">
              <path strokeLinecap="round" strokeLinejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456zM16.894 20.567L16.5 21.75l-.394-1.183a2.25 2.25 0 00-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 001.423-1.423l.394-1.183.394 1.183a2.25 2.25 0 001.423 1.423l1.183.394-1.183.394a2.25 2.25 0 00-1.423 1.423z" />
            </svg>
          </div>
          <div className="text-sm text-gray-300 font-normal pr-3 leading-relaxed tracking-wide">{thought}</div>
        </div>
        <button className="text-gray-500 hover:text-gray-300 ml-2 flex-shrink-0 mt-1">
          {isExpanded ? (
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-3.5 h-3.5">
              <path strokeLinecap="round" strokeLinejoin="round" d="M4.5 15.75l7.5-7.5 7.5 7.5" />
            </svg>
          ) : (
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-3.5 h-3.5">
              <path strokeLinecap="round" strokeLinejoin="round" d="M19.5 8.25l-7.5 7.5-7.5-7.5" />
            </svg>
          )}
        </button>
      </div>
      
      {isExpanded && (
        <div className="mt-3 pt-2 border-t border-slate-700/20">
          <div className="mb-1 text-xs text-gray-400">Parameters:</div>
          <pre className="text-xs bg-slate-800/40 p-3 rounded overflow-x-auto leading-relaxed">
            {JSON.stringify(toolInvocation.args, null, 2)}
          </pre>
          
          {toolInvocation.result && (
            <>
              <div className="mt-3 mb-1 text-xs text-gray-400">Results:</div>
              <pre className="text-xs bg-slate-800/40 p-3 rounded overflow-x-auto leading-relaxed">
                {JSON.stringify(toolInvocation.result, null, 2)}
              </pre>
            </>
          )}
          
          {toolInvocation.error && (
            <>
              <div className="mt-3 mb-1 text-xs text-red-400">Error:</div>
              <pre className="text-xs bg-red-950/20 border border-red-800/20 p-3 rounded overflow-x-auto text-red-300 leading-relaxed">
                {typeof toolInvocation.error === 'string' 
                  ? toolInvocation.error 
                  : JSON.stringify(toolInvocation.error, null, 2)}
              </pre>
            </>
          )}
        </div>
      )}
    </div>
  );
} 