// This file is only used on the server side
// Memory type enum
export enum MemoryType {
  USER = 'user',
  SYSTEM = 'system'
}

// Ensure this service only runs on the server side
if (typeof window !== 'undefined') {
  throw new Error('MemoryService can only be used on the server side');
}

// Define memory interface to avoid direct import of mem0ai
interface MemoryInterface {
  add(content: string | Array<{role: string, content: string}>, options: any): Promise<any>;
  search(query: string, options: any): Promise<any>;
  getAll(options: any): Promise<any>;
  get(id: string): Promise<any>;
  update(id: string, content: string): Promise<any>;
  delete(id: string): Promise<any>;
  deleteAll(options: any): Promise<any>;
  reset(): Promise<any>;
}

// Memory service class
export class MemoryService {
  private memories: Record<MemoryType, MemoryInterface | null> = {
    [MemoryType.USER]: null,
    [MemoryType.SYSTEM]: null
  };
  private static instance: MemoryService;
  private initialized = false;

  private constructor() {
    console.log('[MemoryService] Constructor called');
  }

  // Singleton pattern to get instance
  public static getInstance(): MemoryService {
    if (!MemoryService.instance) {
      MemoryService.instance = new MemoryService();
    }
    return MemoryService.instance;
  }

  // Initialize memory service
  public async initialize(): Promise<void> {
    if (this.initialized) {
      return;
    }

    try {
      console.log('[MemoryService] Starting initialization...');

      // Dynamically import mem0ai
      const mem0 = await import('mem0ai/oss');
      const Memory = mem0.Memory;

      // Get environment variables
      function getEnv(key: string, defaultValue: any) {
        return process.env[key] !== undefined ? process.env[key] : defaultValue;
      }

      // Create memory instances
      this.memories[MemoryType.USER] = new Memory({
        version: 'v2',
        embedder: {
          provider: getEnv('MEMORY_EMBEDDER_PROVIDER', 'openai'),
          config: {
            apiKey: getEnv('MEMORY_EMBEDDER_API_KEY', process.env.OPENAI_API_KEY || ''),
            model: getEnv('MEMORY_EMBEDDER_MODEL', 'text-embedding-3-small'),
          },
        },
        vectorStore: {
          provider: getEnv('MEMORY_VECTORSTORE_PROVIDER', 'memory'),
          config: {
            collectionName: 'user_memories',
            dimension: 1536,
            url: getEnv('MEMORY_VECTORSTORE_URL', undefined),
            apiKey: getEnv('MEMORY_VECTORSTORE_API_KEY', undefined),
          },
        },
        llm: {
          provider: getEnv('MEMORY_LLM_PROVIDER', 'openai'),
          config: {
            apiKey: getEnv('MEMORY_LLM_API_KEY', process.env.OPENAI_API_KEY || ''),
            model: getEnv('MEMORY_LLM_MODEL', 'o3-mini'),
          },
        },
        // 可扩展更多配置项
      });

      this.memories[MemoryType.SYSTEM] = new Memory({
        version: 'v2',
        embedder: {
          provider: getEnv('MEMORY_EMBEDDER_PROVIDER', 'openai'),
          config: {
            apiKey: getEnv('MEMORY_EMBEDDER_API_KEY', process.env.OPENAI_API_KEY || ''),
            model: getEnv('MEMORY_EMBEDDER_MODEL', 'text-embedding-3-small'),
          },
        },
        vectorStore: {
          provider: getEnv('MEMORY_VECTORSTORE_PROVIDER', 'memory'),
          config: {
            collectionName: 'system_memories',
            dimension: 1536,
            url: getEnv('MEMORY_VECTORSTORE_URL', undefined),
            apiKey: getEnv('MEMORY_VECTORSTORE_API_KEY', undefined),
          },
        },
        llm: {
          provider: getEnv('MEMORY_LLM_PROVIDER', 'openai'),
          config: {
            apiKey: getEnv('MEMORY_LLM_API_KEY', process.env.OPENAI_API_KEY || ''),
            model: getEnv('MEMORY_LLM_MODEL', 'gpt-4.1'),
          },
        }
        // 可扩展更多配置项
      });

      this.initialized = true;
      console.log('[MemoryService] Initialization completed');
    } catch (error) {
      console.error('[MemoryService] Initialization failed:', error);
      throw error;
    }
  }

  // Ensure memory service is initialized
  private async ensureInitialized(): Promise<void> {
    if (!this.initialized) {
      await this.initialize();
    }
  }

  // Add memory
  async addMemory(
    type: MemoryType,
    content: string | Array<{role: string, content: string}>,
    userId: string,
    metadata?: Record<string, any>,
    infer: boolean = true
  ) {
    await this.ensureInitialized();
    console.log(`[MemoryService] Adding ${type} memory, content: ${content}, user ID: ${userId}, infer: ${infer}`);

    try {
      const memory = this.memories[type];
      if (!memory) {
        throw new Error(`Memory type ${type} not initialized`);
      }

      return await memory.add(content, {
        userId,
        metadata: metadata || {},
        version: "v2", // 指定使用 v2 版本
        infer: infer // 添加 infer 参数，控制是否使用 LLM 推断记忆
      });
    } catch (error) {
      console.error(`[MemoryService] Failed to add ${type} memory:`, error);
      throw error;
    }
  }

  // Search memory
  async searchMemory(
    type: MemoryType,
    query: string,
    userId?: string,
    limit: number = 5,
    threshold: number = 0.3 // 使用threshold参数，与mem0官方文档保持一致，默认值为0.3
  ) {
    await this.ensureInitialized();
    console.log(`[MemoryService] Searching ${type} memory, query: "${query}"${userId ? `, user ID: ${userId}` : ''}`);

    try {
      const memory = this.memories[type];
      if (!memory) {
        throw new Error(`Memory type ${type} not initialized`);
      }

      // 创建过滤条件对象
      const filters = userId ? { userId: userId } : undefined;

      // 创建搜索选项对象，包含过滤条件、限制数量和相似度阈值
      const options: any = {
        filters: filters,
        limit: limit,
        threshold: threshold // 使用threshold参数，与mem0官方文档保持一致
      };

      console.log(`[MemoryService] Search options:`, JSON.stringify(options));

      // 执行搜索
      const results = await memory.search(query, options);

      // 记录搜索结果
      console.log(`[MemoryService] Search returned ${results.results?.length || 0} results`);

      return results;
    } catch (error) {
      console.error(`[MemoryService] Failed to search ${type} memory:`, error);
      throw error;
    }
  }

  // Get all memories
  async getAllMemories(type: MemoryType, userId?: string, page: number = 1, pageSize: number = 50) {
    await this.ensureInitialized();
    console.log(`[MemoryService] Getting all ${type} memories${userId ? `, user ID: ${userId}` : ''}`);

    try {
      const memory = this.memories[type];
      if (!memory) {
        throw new Error(`Memory type ${type} not initialized`);
      }

      // Create options object with required parameters
      const options: any = {
        page,
        pageSize,
        userId: userId || 'system' // Always provide a userId parameter
      };

      console.log(`[MemoryService] GetAll options:`, JSON.stringify(options));

      return await memory.getAll(options);
    } catch (error) {
      console.error(`[MemoryService] Failed to get all ${type} memories:`, error);
      throw error;
    }
  }

  // Get single memory
  async getMemory(type: MemoryType, memoryId: string) {
    await this.ensureInitialized();
    console.log(`[MemoryService] Getting ${type} memory, ID: ${memoryId}`);

    try {
      const memory = this.memories[type];
      if (!memory) {
        throw new Error(`Memory type ${type} not initialized`);
      }

      return await memory.get(memoryId);
    } catch (error) {
      console.error(`[MemoryService] Failed to get ${type} memory:`, error);
      throw error;
    }
  }

  // Update memory
  async updateMemory(type: MemoryType, memoryId: string, content: string) {
    await this.ensureInitialized();
    console.log(`[MemoryService] Updating ${type} memory, ID: ${memoryId}`);

    try {
      const memory = this.memories[type];
      if (!memory) {
        throw new Error(`Memory type ${type} not initialized`);
      }

      return await memory.update(memoryId, content);
    } catch (error) {
      console.error(`[MemoryService] Failed to update ${type} memory:`, error);
      throw error;
    }
  }

  // Delete memory
  async deleteMemory(type: MemoryType, memoryId: string) {
    await this.ensureInitialized();
    console.log(`[MemoryService] Deleting ${type} memory, ID: ${memoryId}`);

    try {
      const memory = this.memories[type];
      if (!memory) {
        throw new Error(`Memory type ${type} not initialized`);
      }

      return await memory.delete(memoryId);
    } catch (error) {
      console.error(`[MemoryService] Failed to delete ${type} memory:`, error);
      throw error;
    }
  }

  // Delete all user memories
  async deleteAllUserMemories(userId: string) {
    await this.ensureInitialized();
    console.log(`[MemoryService] Deleting all user memories, user ID: ${userId}`);

    try {
      const memory = this.memories[MemoryType.USER];
      if (!memory) {
        throw new Error(`Memory type ${MemoryType.USER} not initialized`);
      }

      // Due to possible API changes, we use any type to bypass type checking
      const options: any = { userId };
      return await memory.deleteAll(options);
    } catch (error) {
      console.error(`[MemoryService] Failed to delete all user memories:`, error);
      throw error;
    }
  }

  // Delete all memories of specific type
  async resetMemories(type: MemoryType) {
    await this.ensureInitialized();
    console.log(`[MemoryService] Deleting all ${type} memories`);

    try {
      const memory = this.memories[type];
      if (!memory) {
        throw new Error(`Memory type ${type} not initialized`);
      }

      // 使用deleteAll方法，并指定正确的userId
      // 对于系统记忆，指定userId='system'
      // 对于用户记忆，不指定userId将删除所有用户的记忆
      if (type === MemoryType.SYSTEM) {
        console.log(`[MemoryService] Deleting all system memories with userId='system'`);
        return await memory.deleteAll({ userId: 'system' });
      } else {
        console.log(`[MemoryService] Deleting all user memories (all users)`);
        return await memory.deleteAll({});
      }
    } catch (error) {
      console.error(`[MemoryService] Failed to delete all ${type} memories:`, error);
      throw error;
    }
  }
}

// Export singleton instance getter method
export const getMemoryService = () => MemoryService.getInstance();
