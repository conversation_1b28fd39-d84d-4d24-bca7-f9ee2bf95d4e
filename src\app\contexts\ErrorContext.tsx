'use client';

import React, { createContext, useState, useContext, ReactNode } from 'react';

interface ErrorContextType {
  error: Error | string | null;
  setError: (error: Error | string | null) => void;
  clearError: () => void;
}

const ErrorContext = createContext<ErrorContextType | undefined>(undefined);

export function ErrorProvider({ children }: { children: ReactNode }) {
  const [error, setErrorState] = useState<Error | string | null>(null);
  
  const setError = (error: Error | string | null) => {
    setErrorState(error);
  };
  
  const clearError = () => {
    setErrorState(null);
  };
  
  return (
    <ErrorContext.Provider value={{ error, setError, clearError }}>
      {children}
    </ErrorContext.Provider>
  );
}

export function useError() {
  const context = useContext(ErrorContext);
  if (context === undefined) {
    throw new Error('useError must be used within an ErrorProvider');
  }
  return context;
} 