{"openapi": "3.0.1", "info": {"title": "WMS", "description": "", "version": "1.0.0"}, "tags": [], "paths": {"/wms-bam/put-back-task/search-by-paging": {"post": {"summary": "Search By Paging Put Back Task", "deprecated": false, "description": "", "tags": [], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PutBackTaskQuery", "description": ""}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RPageResultBAMPutBackTaskDto"}, "examples": {"1": {"summary": "成功示例", "value": {"code": 0, "msg": "", "success": false, "data": {"list": [{"id": "", "customerId": "", "status": "", "note": "", "tags": [""], "preAssigneeUserId": "", "assigneeUserId": "", "lastAssignedWhen": "", "priority": "", "startTime": "", "endTime": "", "taskSteps": [{"id": 0, "customerId": "", "status": "", "exceptionCode": "", "sysNote": "", "note": "", "stepType": "", "taskId": "", "taskType": "", "stepSequence": 0, "assigneeUserIds": [""], "lastAssignedWhen": "", "priority": "", "startTime": "", "endTime": ""}], "createdTime": "", "createdBy": "", "updatedTime": "", "updatedBy": "", "orderId": "", "exceptionCode": "", "pickTaskIds": [""], "putBackType": "", "putBackTaskHistories": [{"createdTime": "", "createdBy": "", "updatedTime": "", "updatedBy": "", "id": 0, "putBackTaskId": "", "orderId": "", "itemId": "", "fromLPId": "", "toLPId": "", "fromLocationId": "", "toLocationId": "", "qty": 0, "uomId": "", "lotNo": "", "titleId": "", "assigneeUserName": "", "fromLocationName": "", "toLocationName": "", "itemName": "", "uom": ""}]}], "totalCount": 0, "currentPage": 0, "pageSize": 0, "totalPage": 0}}}}}}, "headers": {}}}, "security": []}}}, "components": {"schemas": {"TaskStepDto": {"type": "object", "properties": {"id": {"type": "integer", "description": ""}, "customerId": {"type": "string", "description": ""}, "status": {"type": "string", "description": "", "enum": ["NEW", "IN_PROGRESS", "EXCEPTION", "CLOSED", "FORCE_CLOSED", "CANCELLED"]}, "exceptionCode": {"type": "string", "description": ""}, "sysNote": {"type": "string", "description": ""}, "note": {"type": "string", "description": ""}, "stepType": {"type": "string", "description": "", "enum": ["OFFLOAD", "LP_SETUP", "SN_SCAN", "PICK", "STAGE", "SORTING_TO_WALL", "STAGE_TO_WALL", "ORDER_PICK_FROM_WALL", "REPLENISH", "GENERAL", "COLLECT", "DROP", "PUT_AWAY", "LOADING", "TRANSLOAD_RECEIVE", "TRANSLOAD_LOAD", "PUT_BACK", "PACK", "CLP_BONDING", "INTERNAL_TRANSFER_OUT", "INTERNAL_TRANSFER_RECEIVING", "MATERIAL_RECEIVING"]}, "taskId": {"type": "string", "description": ""}, "taskType": {"type": "string", "description": "", "enum": ["GENERAL", "RECEIVE", "PUT_AWAY", "PICK", "PICK_REGULAR", "PICK_DROPSHIP", "LOAD", "REPLENISH", "MOVEMENT", "PUT_BACK", "CYCLE_COUNT", "TRANSLOAD_RECEIVE", "TRANSLOAD_LOAD", "CLP_BONDING", "PACK", "INTERNAL_TRANSFER_OUT", "INTERNAL_TRANSFER_RECEIVE"]}, "stepSequence": {"type": "integer", "description": ""}, "assigneeUserIds": {"type": "array", "items": {"type": "string"}, "description": ""}, "lastAssignedWhen": {"type": "string", "description": ""}, "priority": {"type": "string", "description": "", "enum": ["LOW", "MIDDLE", "HIGH", "TOP"]}, "startTime": {"type": "string", "description": ""}, "endTime": {"type": "string", "description": ""}}}, "BAMPutBackTaskHistoryDto": {"type": "object", "properties": {"createdTime": {"type": "string", "description": ""}, "createdBy": {"type": "string", "description": ""}, "updatedTime": {"type": "string", "description": ""}, "updatedBy": {"type": "string", "description": ""}, "id": {"type": "integer", "description": ""}, "putBackTaskId": {"type": "string", "description": ""}, "orderId": {"type": "string", "description": ""}, "itemId": {"type": "string", "description": ""}, "fromLPId": {"type": "string", "description": ""}, "toLPId": {"type": "string", "description": ""}, "fromLocationId": {"type": "string", "description": ""}, "toLocationId": {"type": "string", "description": ""}, "qty": {"type": "number", "description": ""}, "uomId": {"type": "string", "description": ""}, "lotNo": {"type": "string", "description": ""}, "titleId": {"type": "string", "description": ""}, "assigneeUserName": {"type": "string", "description": ""}, "fromLocationName": {"type": "string", "description": ""}, "toLocationName": {"type": "string", "description": ""}, "itemName": {"type": "string", "description": ""}, "uom": {"type": "string", "description": ""}}}, "BAMPutBackTaskDto": {"type": "object", "properties": {"id": {"type": "string", "description": ""}, "customerId": {"type": "string", "description": ""}, "status": {"type": "string", "description": "", "enum": ["NEW", "IN_PROGRESS", "EXCEPTION", "CLOSE", "FORCE_CLOSE", "CANCELLED"]}, "note": {"type": "string", "description": ""}, "tags": {"type": "array", "items": {"type": "string"}, "description": ""}, "preAssigneeUserId": {"type": "string", "description": ""}, "assigneeUserId": {"type": "string", "description": ""}, "lastAssignedWhen": {"type": "string", "description": ""}, "priority": {"type": "string", "description": "", "enum": ["LOW", "MIDDLE", "HIGH", "TOP"]}, "startTime": {"type": "string", "description": ""}, "endTime": {"type": "string", "description": ""}, "taskSteps": {"type": "array", "items": {"$ref": "#/components/schemas/TaskStepDto", "description": "com.item.wms.application.task.common.dto.TaskStepDto"}, "description": ""}, "createdTime": {"type": "string", "description": ""}, "createdBy": {"type": "string", "description": ""}, "updatedTime": {"type": "string", "description": ""}, "updatedBy": {"type": "string", "description": ""}, "orderId": {"type": "string", "description": ""}, "exceptionCode": {"type": "string", "description": ""}, "pickTaskIds": {"type": "array", "items": {"type": "string"}, "description": ""}, "putBackType": {"type": "string", "description": "", "enum": ["ORDER_CANCEL", "MANUAL", "NOT_SHIPPED"]}, "putBackTaskHistories": {"type": "array", "items": {"$ref": "#/components/schemas/BAMPutBackTaskHistoryDto", "description": "com.item.wms.interfaces.bam.dtos.putBackTask.BAMPutBackTaskHistoryDto"}, "description": ""}}}, "PutBackTaskQuery": {"type": "object", "properties": {"currentPage": {"type": "integer", "description": ""}, "pageSize": {"type": "integer", "description": ""}, "sortingFields": {"type": "array", "items": {"$ref": "#/components/schemas/SortingField", "description": "com.item.xms.persistence.query.SortingField"}, "description": ""}, "ids": {"type": "array", "items": {"type": "string"}, "description": ""}, "id": {"type": "string", "description": ""}, "assigneeUserIds": {"type": "array", "items": {"type": "string"}, "description": ""}, "assigneeUserId": {"type": "string", "description": ""}, "status": {"type": "string", "description": "", "enum": ["NEW", "IN_PROGRESS", "EXCEPTION", "CLOSE", "FORCE_CLOSE", "CANCELLED"]}, "statuses": {"type": "array", "items": {"type": "string", "enum": ["NEW", "IN_PROGRESS", "EXCEPTION", "CLOSE", "FORCE_CLOSE", "CANCELLED"]}, "description": ""}, "priority": {"type": "string", "description": "", "enum": ["LOW", "MIDDLE", "HIGH", "TOP"]}}}, "SortingField": {"type": "object", "properties": {"field": {"type": "string", "description": ""}, "orderBy": {"type": "string", "description": "", "enum": ["NONE", "ASC", "DESC"]}}}, "RPageResultBAMPutBackTaskDto": {"type": "object", "properties": {"code": {"type": "integer", "description": ""}, "msg": {"type": "string", "description": ""}, "success": {"type": "boolean", "description": ""}, "data": {"$ref": "#/components/schemas/PageResultBAMPutBackTaskDto", "description": ""}}}, "PageResultBAMPutBackTaskDto": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/components/schemas/BAMPutBackTaskDto", "description": "com.item.wms.interfaces.bam.dtos.putBackTask.BAMPutBackTaskDto"}, "description": ""}, "totalCount": {"type": "integer", "description": ""}, "currentPage": {"type": "integer", "description": ""}, "pageSize": {"type": "integer", "description": ""}, "totalPage": {"type": "integer", "description": ""}}}}, "securitySchemes": {}}, "servers": []}