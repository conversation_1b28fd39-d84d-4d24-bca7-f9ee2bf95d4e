// EmbedTokenManager接口定义
export interface EmbedTokenManager {
  getRefreshToken(): string | null;
  getCurrentToken(): string | null;
  refreshToken(): Promise<string | null>;
  updateClientToken(token: string): void;
  removeToken(): void;
}

// 工厂方法，根据siteId获取对应的TokenManager
import { DefaultEmbedTokenManager } from '@/embed/EmbedTokenManagerImpl/DefaultEmbedTokenManager';
import { WmsEmbedTokenManager } from '@/embed/EmbedTokenManagerImpl/WmsEmbedTokenManager';
import { PortalEmbedTokenManager } from '@/embed/EmbedTokenManagerImpl/PortalEmbedTokenManager';
import { MarketplaceEmbedTokenManager } from '@/embed/EmbedTokenManagerImpl/MarketplaceEmbedTokenManager';

export function getEmbedTokenManager(siteId?: string): EmbedTokenManager {
  const normalizedSiteId = siteId?.toLowerCase();
  
  if (normalizedSiteId === 'wms') {
    return new WmsEmbedTokenManager();
  }
  if (normalizedSiteId === 'portal') {
    return new PortalEmbedTokenManager();
  }
  if (normalizedSiteId === 'marketplace') {
    return new MarketplaceEmbedTokenManager();
  }
  return new DefaultEmbedTokenManager();
} 