'use client';

import React, { useState } from 'react';
import { Brain, Search, Plus, Trash2, Clock, ChevronDown, ChevronUp, User, BookOpen } from 'lucide-react';
import '@/styles/item-design-system.css';

interface Memory {
  id: string;
  memory: string;
  content?: string; // Add content field for different API response formats
  userId?: string;
  score?: number;
  createdAt?: string;
  metadata?: {
    importance?: number;
    source?: string;
    category?: string;
    [key: string]: any;
  };
}

interface MemoryToolCardProps {
  toolInvocation?: any;
  // Direct properties
  type?: 'search' | 'add' | 'delete';
  memories?: Memory[];
  query?: string;
  memoryType?: 'user' | 'system';
  result?: any;
}

export default function MemoryToolCard({
  toolInvocation,
  type: propType,
  memories: propMemories,
  query: propQuery,
  memoryType: propMemoryType,
  result: propResult
}: MemoryToolCardProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  // Extract data from tool invocation or direct properties
  let type: 'search' | 'add' | 'delete' | undefined,
      memories: Memory[] = [],
      query: string | undefined,
      memoryType: 'user' | 'system' | undefined,
      result: any = null,
      success: boolean = false,
      message: string | undefined;

  if (toolInvocation) {
    if (toolInvocation.state === 'result') {
      // Determine type based on tool name
      if (toolInvocation.toolName === 'searchMemoriesTool') {
        type = 'search';
      } else if (toolInvocation.toolName === 'saveMemoryTool') {
        type = 'add';
      } else if (toolInvocation.toolName === 'deleteMemoryTool') {
        type = 'delete';
      } else if (toolInvocation.toolName === 'updateMemoryTool') {
        type = 'add'; // Treat update as add for display purposes
      }

      // Extract result data
      if (toolInvocation.result) {
        result = toolInvocation.result;

        // Extract success status
        if (typeof result.success === 'boolean') {
          success = result.success;
        } else if (result.error) {
          success = false;
        } else {
          success = true; // Assume success if no error
        }

        // Extract message
        message = result.message || result.error;

        // Extract memories for search results
        if (type === 'search' && Array.isArray(result.memories)) {
          memories = result.memories;
        } else if (type === 'search' && Array.isArray(result.content)) {
          // Handle alternative format
          memories = result.content.map((item: any) => ({
            id: item.id,
            memory: item.content,
            score: item.score,
            createdAt: item.createdAt,
            metadata: item.metadata
          }));
        } else if (type === 'add' && result.memory_id) {
          // For add operation with just an ID, create a placeholder
          memories = [{
            id: result.memory_id,
            memory: toolInvocation.args?.content || '(Memory content not available)',
            metadata: {
              importance: toolInvocation.args?.importance
            }
          }];
        }
      }

      // Extract query for search
      if (type === 'search') {
        query = toolInvocation.args?.query;
      }

      // Extract memory type
      memoryType = toolInvocation.args?.memory_type;
    } else if (toolInvocation.state === 'call') {
      // Tool is being called but hasn't returned yet
      if (toolInvocation.toolName === 'searchMemoriesTool') {
        type = 'search';
        query = toolInvocation.args?.query;
      } else if (toolInvocation.toolName === 'saveMemoryTool') {
        type = 'add';
      } else if (toolInvocation.toolName === 'deleteMemoryTool') {
        type = 'delete';
      } else if (toolInvocation.toolName === 'updateMemoryTool') {
        type = 'add'; // Treat update as add for display purposes
      }

      memoryType = toolInvocation.args?.memory_type;
    }
  } else {
    // Use directly passed properties
    type = propType;
    memories = propMemories || [];
    query = propQuery;
    memoryType = propMemoryType;
    result = propResult;

    // Extract success and message from result if available
    if (result) {
      success = result.success;
      message = result.message;
    }
  }

  // Format timestamp
  const formatTime = (timestamp: string) => {
    if (!timestamp) return '';
    try {
      return new Date(timestamp).toLocaleString();
    } catch (e) {
      return timestamp;
    }
  };

  // Get memory type icon
  const getMemoryTypeIcon = () => {
    if (memoryType === 'system') {
      return <BookOpen className="h-4 w-4 text-item-gray-400" />;
    }
    return <User className="h-4 w-4 text-item-purple-light" />;
  };

  // Get memory type label
  const getMemoryTypeLabel = () => {
    if (memoryType === 'system') {
      return 'System Knowledge';
    }
    return 'Personal Memory';
  };

  // Get card border color based on memory type
  const getBorderColor = () => {
    if (memoryType === 'system') {
      return 'border-item-gray-700/30';
    }
    return 'border-item-purple/30';
  };

  // Get operation icon
  const getOperationIcon = () => {
    switch (type) {
      case 'search':
        return <Search className="h-4 w-4 text-item-gray-400" />;
      case 'add':
        return <Plus className="h-4 w-4 text-item-gray-400" />;
      case 'delete':
        return <Trash2 className="h-4 w-4 text-item-gray-400" />;
      default:
        return <Brain className="h-4 w-4 text-item-gray-400" />;
    }
  };

  // Get operation title
  const getOperationTitle = () => {
    switch (type) {
      case 'search':
        return `Memory Search: "${query}"`;
      case 'add':
        return 'Memory Added';
      case 'delete':
        return 'Memory Deleted';
      default:
        return 'Memory Operation';
    }
  };

  // Tool invocation in progress
  if (toolInvocation && toolInvocation.state !== 'result') {
    return (
      <div className="item-card bg-item-purple/20 rounded-lg border border-item-purple/30 p-2 text-white shadow-lg my-2 item-glow-purple-subtle">
        <div className="flex items-center">
          <div className="bg-item-bg-hover p-1.5 rounded-lg mr-2.5 flex items-center">
            {getOperationIcon()}
          </div>
          <div className="flex-1 text-sm text-white font-medium pr-2 leading-snug">
            {type === 'search'
              ? `Searching memories: "${query}"...`
              : type === 'add'
                ? 'Adding new memory...'
                : 'Deleting memory...'
            }
          </div>
        </div>
      </div>
    );
  }

  // Collapsed view
  if (!isExpanded) {
    // 获取要显示的内容摘要
    let contentSummary = '';
    if (type === 'search' && memories && memories.length > 0) {
      // For search results, show the first memory content
      // Handle both memory and content fields (different API response formats)
      contentSummary = memories[0]?.memory || memories[0]?.content || '';
      // Truncate if too long
      if (contentSummary && contentSummary.length > 80) {
        contentSummary = contentSummary.substring(0, 80) + '...';
      }
    } else if (type === 'add' && memories && memories.length > 0) {
      // For add operations, show the added memory content
      contentSummary = memories[0]?.memory || memories[0]?.content || '';
      if (contentSummary && contentSummary.length > 80) {
        contentSummary = contentSummary.substring(0, 80) + '...';
      }
    } else if (type === 'delete') {
      // For delete operations, show success message
      contentSummary = message || 'Memory successfully deleted';
    }

    return (
      <div className="item-card bg-item-purple/20 rounded-lg border border-item-purple/30 p-2 text-white shadow-lg my-2 cursor-pointer hover:border-item-purple/50 hover:shadow-xl transition-all duration-200 item-glow-purple-subtle">
        <div className="flex justify-between" onClick={() => setIsExpanded(true)}>
          <div className="flex items-center flex-1">
            <div className="bg-item-bg-hover p-1.5 rounded-lg mr-2.5 flex items-center">
              <Brain className="h-4 w-4 text-item-purple" />
            </div>
            <div className="flex-1">
              {/* 只显示操作标题，例如 "Memory Search: 'query'" */}
              <div className="text-sm text-item-gray-500 font-medium">
                {getOperationTitle()}
              </div>
            </div>
          </div>
          <button className="text-item-purple hover:text-item-purple-light ml-2 flex-shrink-0 flex items-center transition-colors duration-200">
            <ChevronDown className="h-4 w-4" />
          </button>
        </div>
      </div>
    );
  }

  // Full view
  return (
    <div className="item-card bg-item-purple/20 rounded-lg border border-item-purple/30 p-2 text-white shadow-lg my-2 item-glow-purple-subtle">
      <div className="flex justify-between cursor-pointer" onClick={() => setIsExpanded(!isExpanded)}>
        <div className="flex items-center flex-1">
          <div className="bg-item-bg-hover p-1.5 rounded-lg mr-2.5 flex items-center">
            <Brain className="h-4 w-4 text-item-purple" />
          </div>
          <div className="text-sm text-white font-semibold pr-2">
            {getOperationTitle()}
          </div>
        </div>
        <button className="text-item-purple hover:text-item-purple-light hover:bg-item-bg-hover p-1.5 rounded-lg ml-2 flex-shrink-0 flex items-center transition-all duration-200">
          <ChevronUp className="h-4 w-4" />
        </button>
      </div>

      {/* Memory search results */}
      {type === 'search' && (
        <div className="mt-3 pt-2.5 border-t border-item-gray-800/40">
          {/* 显示搜索请求参数 */}
          <div className="text-xs text-white mb-3 bg-item-bg-hover/50 p-2 rounded-lg border border-item-gray-800/30">
            <div className="font-semibold mb-1.5 text-item-purple border-b border-item-gray-800/30 pb-1.5 uppercase tracking-wide">Search Parameters:</div>
            <div className="grid grid-cols-2 gap-x-4 gap-y-1.5 pt-1.5">
              <div><span className="text-item-gray-400 font-medium">Query:</span> <span className="text-item-purple-light font-semibold">"{query}"</span></div>
              <div><span className="text-item-gray-400 font-medium">Type:</span> <span className="text-item-purple-light font-semibold">{memoryType === 'system' ? 'System Knowledge' : 'Personal Memory'}</span></div>
              <div><span className="text-item-gray-400 font-medium">Limit:</span> <span className="text-item-purple-light font-semibold">
                {toolInvocation?.args?.limit ||
                 (propResult && propResult.limit) ||
                 5}
              </span></div>
              <div><span className="text-item-gray-400 font-medium">Threshold:</span> <span className="text-item-purple-light font-semibold">
                {toolInvocation?.args?.threshold ||
                 (propResult && propResult.threshold) ||
                 0.3}
              </span></div>
            </div>
          </div>

          <div className="text-xs text-item-gray-400 mb-1.5">
            {success
              ? `Found ${memories && memories.length ? memories.length : 0} ${memoryType === 'system' ? 'System Knowledge' : 'personal memory'} ${memories && memories.length === 1 ? 'record' : 'records'}`
              : 'Search failed'
            }
          </div>

          {success ? (
            memories && memories.length > 0 ? (
              <div className="space-y-2">
                {memories.map((memory, index) => (
                  <div key={index} className="bg-item-bg-card/60 rounded-lg p-2 border border-item-gray-800/30">
                    <div className="flex justify-between items-start">
                      <div className="flex items-center gap-2">
                        {getMemoryTypeIcon()}
                        <div className="text-xs px-2 py-0.5 rounded bg-item-gray-700/40 text-item-gray-300">
                          {getMemoryTypeLabel()}
                        </div>
                        {memory.score !== undefined && (
                          <div className="text-xs px-2 py-0.5 rounded bg-item-purple/30 text-item-purple-light">
                            Relevance: {Math.round(memory.score * 100)}%
                          </div>
                        )}
                      </div>

                      {memory.metadata?.importance !== undefined && (
                        <div className="text-xs px-2 py-0.5 rounded bg-item-gray-700/40 text-item-gray-300">
                          Importance: {memory.metadata.importance}
                        </div>
                      )}
                    </div>

                    {(memory.memory || memory.content) && (
                      <div className="text-sm mt-1.5 font-medium text-item-gray-300">{memory.memory || memory.content}</div>
                    )}

                    <div className="flex justify-between items-center mt-1.5 text-xs text-item-gray-400">
                      {memory.createdAt && (
                        <div className="flex items-center">
                          <Clock className="h-3 w-3 mr-1" />
                          {formatTime(memory.createdAt)}
                        </div>
                      )}

                      {memory.metadata?.source && (
                        <div>Source: {memory.metadata.source}</div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="bg-item-bg-card/60 rounded-lg p-2 text-sm text-item-gray-300">
                No memories found matching "{query}"
              </div>
            )
          ) : (
            <div className="bg-red-900/20 p-2 rounded-lg border border-red-800/20 text-sm text-red-300">
              {message || 'Failed to search memories'}
            </div>
          )}
        </div>
      )}

      {/* Memory add result */}
      {type === 'add' && (
        <div className="mt-2.5 pt-2 border-t border-slate-700/20">
          {success ? (
            <div>
              <div className="text-xs text-item-gray-400 mb-1.5">
                Successfully added new memory
              </div>

              {memories.length > 0 && (
                <div className="bg-item-bg-card/60 rounded-lg p-2 border border-item-gray-800/30">
                  <div className="flex items-center gap-2 mb-1.5">
                    {getMemoryTypeIcon()}
                    <div className="text-xs px-2 py-0.5 rounded bg-item-gray-700/40 text-item-gray-300">
                      {getMemoryTypeLabel()}
                    </div>

                    {memories[0].metadata?.importance !== undefined && (
                      <div className="text-xs px-2 py-0.5 rounded bg-item-gray-700/40 text-item-gray-300">
                        Importance: {memories[0].metadata.importance}
                      </div>
                    )}
                  </div>

                  {(memories[0]?.memory || memories[0]?.content) && (
                    <div className="text-sm font-medium text-item-gray-300">{memories[0]?.memory || memories[0]?.content}</div>
                  )}
                </div>
              )}
            </div>
          ) : (
            <div className="bg-red-900/20 p-2 rounded-lg border border-red-800/20 text-sm text-red-300">
              {message || 'Failed to add memory'}
            </div>
          )}
        </div>
      )}

      {/* Memory delete result */}
      {type === 'delete' && (
        <div className="mt-2.5 pt-2 border-t border-slate-700/20">
          {success ? (
            <div className="bg-item-bg-card/60 rounded-lg p-2.5 text-sm text-item-gray-300">
              {message || 'Memory successfully deleted'}
            </div>
          ) : (
            <div className="bg-red-900/20 p-2 rounded-lg border border-red-800/20 text-sm text-red-300">
              {message || 'Failed to delete memory'}
            </div>
          )}
        </div>
      )}

      <div className="text-xs text-item-gray-500 mt-2.5 text-right">
        Memory Management
      </div>
    </div>
  );
}
