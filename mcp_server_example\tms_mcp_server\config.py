import os
from dotenv import load_dotenv

# 尝试加载.env文件中的环境变量（如果存在）
load_dotenv()

# Environment Configuration
ENV = os.getenv('ENV', 'staging')  # development, staging, production

# Environment-specific base URLs
ENV_URLS = {
    'development': {
        'wms': "https://wms-dev.item.com/api",
        'itemmaster': "https://itemmaster-dev.item.com/api/itemmaster",
        'iam': "https://id-dev.item.com",
        'wise': "https://wise-dev.logisticsteam.com",
        'tms': "https://ship-dev.unisco.com",
        'tms_php': "https://ship-dev.unisco.com"
    },
    'staging': {
        'wms': "https://wms-staging.item.com/api",
        'itemmaster': "https://itemmaster-staging.item.com/api/itemmaster",
        'iam': "https://id-staging.item.com",
        'wise': "https://stage.logisticsteam.com",
        'tms': "https://shipstage.unisco.com",
        'tms_php': "https://shipstage.unisco.com"
    },
    'production': {
        'wms': "https://wms.item.com/api",
        'itemmaster': "https://itemmaster.item.com/api/itemmaster",
        'iam': "https://id.item.com",
        'wise': "https://wise.logisticsteam.com",
        'tms': "https://ship.unisco.com",
        'tms_php': "https://ship.unisco.com"
    }
}

# Get current environment URLs
CURRENT_ENV_URLS = ENV_URLS.get(ENV, ENV_URLS['development'])

# TMS Configuration
TMS_API_KEY = os.getenv('TMS_API_KEY', "61bed41a2d8f1262cf7b0357d6777f5d")
TMS_BASE_URL = CURRENT_ENV_URLS['tms']
TMS_PHP_BASE_URL = CURRENT_ENV_URLS['tms_php']

TMS_TRACKING_URL_PREFIX = "https://portal-staging.item.com"