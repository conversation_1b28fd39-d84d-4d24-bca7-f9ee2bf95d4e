import { DOMService } from './DOMService';
import { DOMActionType, DOMOperationResult, DOMElementNode, DOMState } from './types';
import { loadStateManager } from './LoadStateManager';

export class DOMController {
    private static instance: DOMController;
    private domService: DOMService;

    public static getInstance(): DOMController {
        if (!DOMController.instance) {
            DOMController.instance = new DOMController();
        }
        return DOMController.instance;
    }

    private constructor() {
        this.domService = DOMService.getInstance();
    }

    // 等待页面状态稳定
    private async waitForStable() {
        try {
            await loadStateManager.waitForComplete();
        } catch (error) {
            console.warn('Wait for stable state failed:', error);
        }
    }

    public async executeActions(actions: DOMActionType[]): Promise<DOMOperationResult[]> {
        const results: DOMOperationResult[] = [];
        
        if (!Array.isArray(actions)) {
            throw new Error('Invalid actions format');
        }

        for (const action of actions) {
            try {
                console.log('Executing action:', action);
                const result = await this.executeAction(action);
                results.push(result);

                if (!result.success) {
                    break;
                }

                // 在每个动作之后等待页面状态稳定
                await this.waitForStable();
            } catch (error) {
                console.error('Action failed:', action, error);
                results.push({
                    success: false,
                    error: error instanceof Error ? error.message : 'Unknown error'
                });
                break;
            }
        }

        return results;
    }

    private async executeAction(action: DOMActionType): Promise<DOMOperationResult> {
        const domState = await this.domService.getDOMState();
        console.log('DOM State:', domState);
        try {
            let result: DOMOperationResult;

            if ('click_element' in action) {
                result = await this.handleClickElement(action.click_element.index, domState);
            }
            else if ('input_text' in action) {
                result = await this.handleInputText(action.input_text.index, action.input_text.text, domState);
            }
            else if ('send_keys' in action) {
                result = await this.handleSendKeys(action.send_keys.index, action.send_keys.keys, domState);
            }
            else if ('scroll_to_text' in action) {
                result = await this.handleScrollToText(action.scroll_to_text.text);
            }
            else if ('select_dropdown_option' in action) {
                result = await this.handleSelectDropdownOption(
                    action.select_dropdown_option.index,
                    action.select_dropdown_option.text,
                    domState
                );
            }
            else if ('get_dropdown_options' in action) {
                result = await this.handleGetDropdownOptions(action.get_dropdown_options.index, domState);
            }
            else if ('scroll' in action) {
                result = await this.handleScroll(action.scroll.amount);
            }
            else {
                result = {
                    success: false,
                    error: 'Unsupported action type'
                };
            }

            // 在每个动作执行后等待页面状态稳定
            await this.waitForStable();
            return result;

        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error'
            };
        }
    }

    private async getElementByIndex(index: number, domState: DOMState): Promise<Element | null> {
        const element = Object.values(domState.selector_map).find(e => (e as DOMElementNode).highlight_index === index);
        if (!element) {
            return null;
        }

        return await this.getElementByXPath((element as DOMElementNode).xpath);
    }

    private async getElementByXPath(xpath: string, maxRetries = 3): Promise<Element | null> {
        for (let i = 0; i < maxRetries; i++) {
            const result = document.evaluate(
                xpath,
                document,
                null,
                XPathResult.FIRST_ORDERED_NODE_TYPE,
                null
            );
            const element = result.singleNodeValue as Element;
            if (element) {
                return element;
            }
            if (i < maxRetries - 1) {
                await new Promise(resolve => setTimeout(resolve, 500));
            }
        }
        return null;
    }

    private async handleClickElement(index: number, domState: DOMState): Promise<DOMOperationResult> {
        const element = await this.getElementByIndex(index, domState);
        if (!element) {
            return {
                success: false,
                error: `Element with index ${index} not found`
            };
        }
        console.log('Element found:', element);
        try {
            if (element.tagName.toLowerCase() === 'a') {
                const href = element.getAttribute('href');
                const target = element.getAttribute('target') || '_self'; // 如果没有指定 target，默认为 _self
                const isNewWindow = target === '_blank';

                // 对于 target="_self" 或未指定 target 的链接，尝试使用更自然的点击方式
                if (!isNewWindow && href) {
                    try {
                        // 尝试使用原生点击
                        (element as HTMLElement).click();
                        
                        // 等待网络空闲和DOM稳定
                        await Promise.all([
                            loadStateManager.waitForLoadState('networkIdle'),
                            loadStateManager.waitForLoadState('domStable')
                        ]);
                        
                        return {
                            success: true,
                            extracted_content: `Clicked link with href ${href}`,
                            include_in_memory: true
                        };
                    } catch (clickError) {
                        console.warn('Native click failed, falling back to synthetic click:', clickError);
                        // 如果原生点击失败，继续使用合成事件
                    }
                }

                // 创建并分发点击事件
                const clickEvent = new MouseEvent('click', {
                    view: window,
                    bubbles: true,
                    cancelable: true,
                    ctrlKey: isNewWindow
                });

                const clickResult = element.dispatchEvent(clickEvent);

                // 如果事件被取消且有 href，则手动导航
                if (!clickResult && href) {
                    if (isNewWindow) {
                        window.open(href, '_blank');
                    } else {
                        // 使用更温和的导航方式
                        try {
                            // 如果支持 History API，尝试使用它
                            if (window.history && window.history.pushState) {
                                window.history.pushState({}, '', href);
                                // 触发 popstate 事件，让可能的路由系统知道 URL 已更改
                                window.dispatchEvent(new Event('popstate'));
                            } else {
                                // 回退到传统导航
                                window.location.href = href;
                            }
                        } catch (navError) {
                            console.warn('Advanced navigation failed, using direct location change:', navError);
                            window.location.href = href;
                        }
                    }
                }

                // 等待网络空闲和DOM稳定
                await Promise.all([
                    loadStateManager.waitForLoadState('networkIdle'),
                    loadStateManager.waitForLoadState('domStable')
                ]);
            } else {
                (element as HTMLElement).click();
                
                // 对于普通元素点击，主要等待DOM稳定
                await loadStateManager.waitForLoadState('domStable');
            }

            return {
                success: true,
                extracted_content: `Clicked element with index ${index}`,
                include_in_memory: true
            };
        } catch (error) {
            return {
                success: false,
                error: `Failed to click element: ${error instanceof Error ? error.message : 'Unknown error'}`
            };
        }
    }

    private async handleInputText(index: number, text: string, domState: DOMState): Promise<DOMOperationResult> {
        const element = await this.getElementByIndex(index, domState);
        if (!element) {
            return {
                success: false,
                error: `Element with index ${index} not found`
            };
        }
        console.log('Element Input Text found:', element);
        try {
            const inputElement = element as HTMLInputElement;
            inputElement.value = text;
            
            // 按顺序触发所有相关事件
            inputElement.dispatchEvent(new Event('focus', { bubbles: true }));
            await loadStateManager.waitForLoadState('domStable');

            inputElement.dispatchEvent(new InputEvent('input', {
                bubbles: true,
                cancelable: true,
                inputType: 'insertText',
                data: text
            }));
            await loadStateManager.waitForLoadState('domStable');

            inputElement.dispatchEvent(new Event('change', { bubbles: true }));
            await loadStateManager.waitForLoadState('domStable');

            inputElement.dispatchEvent(new Event('blur', { bubbles: true }));
            
            // 等待可能的自动完成/建议列表加载
            await Promise.all([
                loadStateManager.waitForLoadState('networkIdle'),
                loadStateManager.waitForLoadState('domStable')
            ]);

            return {
                success: true,
                extracted_content: `Input text "${text}" into element with index ${index}`,
                include_in_memory: true
            };
        } catch (error) {
            return {
                success: false,
                error: `Failed to input text: ${error instanceof Error ? error.message : 'Unknown error'}`
            };
        }
    }

    private async handleSendKeys(index: number, keys: string, domState: DOMState): Promise<DOMOperationResult> {
        const element = await this.getElementByIndex(index, domState);
        if (!element) {
            return {
                success: false,
                error: `Element with index ${index} not found`
            };
        }

        try {
            for (const eventType of ['keydown', 'keypress', 'keyup']) {
                element.dispatchEvent(new KeyboardEvent(eventType, {
                    key: keys,
                    code: keys,
                    bubbles: true
                }));
                await loadStateManager.waitForLoadState('domStable');
            }

            // 等待可能的键盘事件触发的网络请求
            await Promise.all([
                loadStateManager.waitForLoadState('networkIdle'),
                loadStateManager.waitForLoadState('domStable')
            ]);

            return {
                success: true,
                extracted_content: `Sent keys "${keys}" to element with index ${index}`,
                include_in_memory: true
            };
        } catch (error) {
            return {
                success: false,
                error: `Failed to send keys: ${error instanceof Error ? error.message : 'Unknown error'}`
            };
        }
    }

    private async handleScrollToText(text: string): Promise<DOMOperationResult> {
        try {
            const walker = document.createTreeWalker(
                document.body,
                NodeFilter.SHOW_TEXT,
                {
                    acceptNode: node => 
                        node.textContent?.toLowerCase().includes(text.toLowerCase())
                            ? NodeFilter.FILTER_ACCEPT
                            : NodeFilter.FILTER_REJECT
                }
            );

            let node = walker.nextNode();
            while (node) {
                if (node.textContent?.toLowerCase().includes(text.toLowerCase())) {
                    (node.parentElement as HTMLElement).scrollIntoView({
                        behavior: 'smooth',
                        block: 'center'
                    });
                    return {
                        success: true,
                        extracted_content: `Scrolled to text "${text}"`,
                        include_in_memory: true
                    };
                }
                node = walker.nextNode();
            }

            return {
                success: false,
                error: `Text "${text}" not found on page`
            };
        } catch (error) {
            return {
                success: false,
                error: `Failed to scroll to text: ${error instanceof Error ? error.message : 'Unknown error'}`
            };
        }
    }

    private async handleSelectDropdownOption(index: number, text: string, domState: DOMState): Promise<DOMOperationResult> {
        const element = await this.getElementByIndex(index, domState);
        if (!element || !(element instanceof HTMLSelectElement)) {
            return {
                success: false,
                error: `Select element with index ${index} not found or invalid`
            };
        }

        try {
            const option = Array.from(element.options).find(opt => opt.text.trim() === text);
            if (!option) {
                return {
                    success: false,
                    error: `Option "${text}" not found in dropdown`
                };
            }

            element.value = option.value;
            element.dispatchEvent(new Event('change', { bubbles: true }));

            // 等待下拉选择可能触发的变化
            await Promise.all([
                loadStateManager.waitForLoadState('networkIdle'),
                loadStateManager.waitForLoadState('domStable')
            ]);

            return {
                success: true,
                extracted_content: `Selected option "${text}" in dropdown with index ${index}`,
                include_in_memory: true
            };
        } catch (error) {
            return {
                success: false,
                error: `Failed to select option: ${error instanceof Error ? error.message : 'Unknown error'}`
            };
        }
    }

    private async handleGetDropdownOptions(index: number, domState: DOMState): Promise<DOMOperationResult> {
        const element = await this.getElementByIndex(index, domState);
        if (!element || !(element instanceof HTMLSelectElement)) {
            return {
                success: false,
                error: `Select element with index ${index} not found or invalid`
            };
        }

        try {
            const options = Array.from(element.options).map((opt, i) => 
                `${i}: ${opt.text.trim()} (value=${opt.value})`
            );

            return {
                success: true,
                extracted_content: options.join('\n'),
                include_in_memory: true
            };
        } catch (error) {
            return {
                success: false,
                error: `Failed to get dropdown options: ${error instanceof Error ? error.message : 'Unknown error'}`
            };
        }
    }

    private async handleScroll(amount?: number): Promise<DOMOperationResult> {
        try {
            if (amount !== undefined) {
                window.scrollBy(0, amount);
            } else {
                // 如果没有指定滚动量，滚动一页
                window.scrollBy(0, window.innerHeight);
            }

            return {
                success: true,
                extracted_content: `Scrolled ${amount !== undefined ? amount + ' pixels' : 'one page'}`,
                include_in_memory: true
            };
        } catch (error) {
            return {
                success: false,
                error: `Failed to scroll: ${error instanceof Error ? error.message : 'Unknown error'}`
            };
        }
    }
} 