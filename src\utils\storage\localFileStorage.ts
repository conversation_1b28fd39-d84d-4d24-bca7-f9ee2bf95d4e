import fs from 'fs';
import path from 'path';
import { ChatHistory } from '../chatHistoryUtils';
import {
  ChatHistoryStorage,
  StorageConfig,
  ChatHistoryIndexItem,
  UserChatHistoryIndex
} from './types';

// 本地文件系统存储实现
export class LocalFileStorage implements ChatHistoryStorage {
  private readonly basePath: string;

  constructor(config: StorageConfig) {
    this.basePath = config.local?.dir || path.join(process.cwd(), 'src', 'chat-history');
    this.ensureBaseDirExists();
  }

  /**
   * 确保基础目录存在
   */
  private ensureBaseDirExists(): void {
    if (!fs.existsSync(this.basePath)) {
      fs.mkdirSync(this.basePath, { recursive: true });
    }
  }

  /**
   * 获取用户目录路径
   */
  private getUserDir(userId: string): string {
    return path.join(this.basePath, userId);
  }

  /**
   * 确保用户目录存在
   */
  public ensureUserDirExists(userId: string): void {
    const userDir = this.getUserDir(userId);
    if (!fs.existsSync(userDir)) {
      fs.mkdirSync(userDir, { recursive: true });
    }
  }

  /**
   * 获取用户特定的索引文件路径
   */
  private getUserIndexFilePath(userId: string): string {
    return path.join(this.getUserDir(userId), 'user_index.json');
  }

  /**
   * 读取用户特定的索引文件
   */
  private async readUserIndexFile(userId: string): Promise<UserChatHistoryIndex> {
    try {
      const userIndexPath = this.getUserIndexFilePath(userId);

      // 确保用户目录存在
      this.ensureUserDirExists(userId);

      if (!fs.existsSync(userIndexPath)) {
        // 如果用户索引文件不存在，创建一个空的索引
        const emptyUserIndex: UserChatHistoryIndex = {
          userId,
          chats: []
        };

        console.log(`[LocalFileStorage] 用户 ${userId} 的索引文件不存在，创建空索引`);

        await fs.promises.writeFile(
          userIndexPath,
          JSON.stringify(emptyUserIndex, null, 2),
          'utf-8'
        );

        return emptyUserIndex;
      }

      const fileContent = await fs.promises.readFile(userIndexPath, 'utf-8');
      console.log(`[LocalFileStorage] 读取用户 ${userId} 的索引文件，内容长度: ${fileContent.length}`);

      try {
        const userIndex = JSON.parse(fileContent) as UserChatHistoryIndex;
        console.log(`[LocalFileStorage] 用户 ${userId} 的索引文件包含 ${userIndex.chats.length} 条聊天记录`);
        return userIndex;
      } catch (parseError) {
        console.error(`[LocalFileStorage] 解析用户 ${userId} 的索引文件失败:`, parseError);
        console.log(`[LocalFileStorage] 索引文件内容: ${fileContent.substring(0, 200)}...`);
        throw parseError;
      }
    } catch (error) {
      console.error(`[LocalFileStorage] 读取用户索引文件失败 (${userId}):`, error);
      // 返回空索引
      return {
        userId,
        chats: []
      };
    }
  }

  /**
   * 写入用户特定的索引文件
   */
  private async writeUserIndexFile(userIndex: UserChatHistoryIndex): Promise<void> {
    try {
      const userIndexPath = this.getUserIndexFilePath(userIndex.userId);

      // 确保用户目录存在
      this.ensureUserDirExists(userIndex.userId);

      await fs.promises.writeFile(
        userIndexPath,
        JSON.stringify(userIndex, null, 2),
        'utf-8'
      );
      console.log(`[LocalFileStorage] 成功写入用户 ${userIndex.userId} 的索引文件，包含 ${userIndex.chats.length} 条聊天记录`);
    } catch (error) {
      console.error(`[LocalFileStorage] 写入用户索引文件失败 (${userIndex.userId}):`, error);
      throw new Error(`写入用户索引文件失败: ${error}`);
    }
  }

  /**
   * 从聊天历史生成索引项
   */
  private createIndexItem(chatHistory: ChatHistory): ChatHistoryIndexItem {
    return {
      id: chatHistory.id,
      title: chatHistory.title,
      createdAt: chatHistory.createdAt,
      updatedAt: chatHistory.updatedAt
    };
  }

  /**
   * 更新聊天历史索引
   */
  async updateChatHistoryIndex(chatHistory: ChatHistory, isDelete: boolean = false): Promise<void> {
    if (!chatHistory.userId) {
      throw new Error('更新索引需要提供用户ID');
    }

    const userId = chatHistory.userId;

    console.log(`[LocalFileStorage] 更新用户 ${userId} 的聊天历史索引`);

    // 读取用户索引
    const userIndex = await this.readUserIndexFile(userId);

    if (isDelete) {
      // 删除模式：从索引中移除聊天历史
      userIndex.chats = userIndex.chats.filter(
        chat => chat.id !== chatHistory.id
      );
    } else {
      // 更新模式：更新或添加聊天历史到索引
      const indexItem = this.createIndexItem(chatHistory);
      const existingIndex = userIndex.chats.findIndex(
        chat => chat.id === chatHistory.id
      );

      if (existingIndex >= 0) {
        // 更新现有项
        userIndex.chats[existingIndex] = indexItem;
      } else {
        // 添加新项
        userIndex.chats.push(indexItem);
      }

      // 按更新时间倒序排序
      userIndex.chats.sort((a, b) =>
        new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
      );
    }

    // 保存用户索引
    await this.writeUserIndexFile(userIndex);
  }

  /**
   * 获取用户聊天历史索引
   */
  async getUserChatHistoryIndex(userId: string): Promise<ChatHistoryIndexItem[]> {
    console.log(`[LocalFileStorage] 获取用户 ${userId} 的聊天历史索引`);
    const userIndex = await this.readUserIndexFile(userId);
    return userIndex.chats;
  }

  /**
   * 获取所有聊天历史索引
   */
  async getAllChatHistoryIndex(): Promise<Record<string, ChatHistoryIndexItem[]>> {
    console.log('[LocalFileStorage] 获取所有用户的聊天历史索引');

    const result: Record<string, ChatHistoryIndexItem[]> = {};

    try {
      // 获取所有用户目录
      const rootFiles = await fs.promises.readdir(this.basePath);
      const userDirs = [];

      for (const dir of rootFiles) {
        const dirPath = path.join(this.basePath, dir);
        try {
          const stat = await fs.promises.stat(dirPath);
          if (stat.isDirectory() && !dir.startsWith('.')) {
            userDirs.push(dir);
          }
        } catch (e) {
          // 忽略错误
        }
      }

      console.log(`[LocalFileStorage] 找到 ${userDirs.length} 个用户目录`);

      // 为每个用户读取索引
      for (const userId of userDirs) {
        try {
          // 读取用户特定的索引文件
          const userIndex = await this.readUserIndexFile(userId);
          result[userId] = userIndex.chats;
          console.log(`[LocalFileStorage] 用户 ${userId} 有 ${userIndex.chats.length} 条聊天记录`);
        } catch (userError) {
          console.error(`[LocalFileStorage] 读取用户 ${userId} 索引失败:`, userError);
          // 如果读取用户索引失败，设置为空数组
          result[userId] = [];
        }
      }
    } catch (error) {
      console.error('[LocalFileStorage] 列出用户目录失败:', error);
    }

    return result;
  }

  /**
   * 重建索引文件（用于初始化或修复）
   */
  public async rebuildIndex(): Promise<void> {
    try {
      console.log('[LocalFileStorage] 开始重建用户索引文件');

      // 获取所有用户目录
      const rootFiles = await fs.promises.readdir(this.basePath);
      const userDirs = [];

      for (const dir of rootFiles) {
        const dirPath = path.join(this.basePath, dir);
        try {
          const stat = await fs.promises.stat(dirPath);
          if (stat.isDirectory() && !dir.startsWith('.')) {
            userDirs.push(dir);
          }
        } catch (e) {
          // 忽略错误
        }
      }

      console.log(`[LocalFileStorage] 找到 ${userDirs.length} 个用户目录`);

      // 为每个用户构建索引
      for (const userId of userDirs) {
        try {
          console.log(`[LocalFileStorage] 重建用户 ${userId} 的索引`);

          const userDir = path.join(this.basePath, userId);
          const files = await fs.promises.readdir(userDir);
          const jsonFiles = files.filter(file => file.endsWith('.json') && file !== 'user_index.json');

          if (jsonFiles.length === 0) {
            console.log(`[LocalFileStorage] 用户 ${userId} 没有聊天记录，跳过`);
            continue;
          }

          // 创建用户索引
          const userIndex: UserChatHistoryIndex = {
            userId,
            chats: []
          };

          for (const file of jsonFiles) {
            try {
              const filePath = path.join(userDir, file);
              const fileContent = await fs.promises.readFile(filePath, 'utf-8');
              const chatHistory = JSON.parse(fileContent) as ChatHistory;

              // 确保userId存在
              chatHistory.userId = userId;

              // 添加到索引
              userIndex.chats.push(this.createIndexItem(chatHistory));
            } catch (e) {
              console.error(`[LocalFileStorage] 处理文件 ${file} 失败:`, e);
            }
          }

          // 排序
          if (userIndex.chats.length > 0) {
            userIndex.chats.sort((a, b) =>
              new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
            );
          }

          // 保存用户索引
          await this.writeUserIndexFile(userIndex);
          console.log(`[LocalFileStorage] 用户 ${userId} 的索引重建完成，包含 ${userIndex.chats.length} 条聊天记录`);
        } catch (userError) {
          console.error(`[LocalFileStorage] 重建用户 ${userId} 索引失败:`, userError);
        }
      }

      console.log('[LocalFileStorage] 所有用户索引重建完成');
    } catch (error) {
      console.error('[LocalFileStorage] 重建索引失败:', error);
      throw new Error('重建索引失败');
    }
  }

  /**
   * 保存聊天历史
   */
  async saveChat(chatHistory: ChatHistory): Promise<void> {
    // 确保userId存在
    if (!chatHistory.userId) {
      throw new Error('保存聊天历史需要提供用户ID');
    }

    this.ensureUserDirExists(chatHistory.userId);

    const userDir = this.getUserDir(chatHistory.userId);
    const filePath = path.join(userDir, `${chatHistory.id}.json`);

    // 更新 updatedAt 时间
    const now = new Date().toISOString();
    chatHistory.updatedAt = now;

    // 如果是新对话，设置创建时间
    if (!chatHistory.createdAt) {
      chatHistory.createdAt = now;
    }

    try {
      await fs.promises.writeFile(
        filePath,
        JSON.stringify(chatHistory, null, 2),
        'utf-8'
      );
      console.log(`保存聊天历史: ${chatHistory.id} 用户: ${chatHistory.userId}`);

      // 更新索引
      await this.updateChatHistoryIndex(chatHistory);
    } catch (error) {
      console.error('保存聊天历史失败:', error);
      throw new Error('保存聊天历史失败');
    }
  }

  /**
   * 获取聊天历史列表
   */
  async getChatHistoryList(userId?: string): Promise<ChatHistory[]> {
    this.ensureBaseDirExists();

    try {
      // 如果提供了用户ID并且索引文件存在，使用索引加速查询
      if (userId) {
        // 使用索引获取聊天历史列表
        const indexItems = await this.getUserChatHistoryIndex(userId);

        if (indexItems.length > 0) {
          const chatHistories: ChatHistory[] = [];
          const userDir = this.getUserDir(userId);

          // 根据索引读取实际文件
          for (const item of indexItems) {
            try {
              const filePath = path.join(userDir, `${item.id}.json`);

              if (fs.existsSync(filePath)) {
                const fileContent = await fs.promises.readFile(filePath, 'utf-8');
                const chatHistory = JSON.parse(fileContent) as ChatHistory;
                // 确保userId字段存在
                chatHistory.userId = userId;
                chatHistories.push(chatHistory);
              }
            } catch (e) {
              console.error(`读取聊天历史文件失败: ${item.id}`, e);
            }
          }

          return chatHistories;
        }
      }

      // 如果没有索引或索引为空，回退到传统方式
      let chatHistories: ChatHistory[] = [];

      if (userId) {
        // 获取特定用户的聊天历史
        const userDir = this.getUserDir(userId);

        // 检查用户目录是否存在
        if (!fs.existsSync(userDir)) {
          return [];
        }

        const files = await fs.promises.readdir(userDir);
        const jsonFiles = files.filter(file => file.endsWith('.json') && file !== 'user_index.json');

        for (const file of jsonFiles) {
          const filePath = path.join(userDir, file);
          const fileContent = await fs.promises.readFile(filePath, 'utf-8');
          try {
            const chatHistory = JSON.parse(fileContent) as ChatHistory;
            // 确保userId字段存在
            chatHistory.userId = userId;
            chatHistories.push(chatHistory);
          } catch (e) {
            console.error(`解析文件 ${file} 失败:`, e);
          }
        }

        // 如果找到了聊天记录但没有用户索引文件，重建用户索引
        const userIndexPath = this.getUserIndexFilePath(userId);
        if (chatHistories.length > 0 && !fs.existsSync(userIndexPath)) {
          console.log(`[LocalFileStorage] 用户 ${userId} 的索引文件不存在，重建索引`);
          await this.rebuildIndex();
        }
      } else {
        // 获取所有用户目录内的聊天历史
        const rootFiles = await fs.promises.readdir(this.basePath);
        const userDirs = rootFiles.filter(async (dir) => {
          const dirPath = path.join(this.basePath, dir);
          try {
            const stat = await fs.promises.stat(dirPath);
            return stat.isDirectory() && !dir.startsWith('.');
          } catch (e) {
            return false;
          }
        });

        for (const userDir of userDirs) {
          try {
            const userDirPath = path.join(this.basePath, userDir);
            const stat = await fs.promises.stat(userDirPath);

            if (stat.isDirectory()) {
              const userFiles = await fs.promises.readdir(userDirPath);
              const userJsonFiles = userFiles.filter(file => file.endsWith('.json') && file !== 'user_index.json');

              for (const file of userJsonFiles) {
                const filePath = path.join(userDirPath, file);
                const fileContent = await fs.promises.readFile(filePath, 'utf-8');
                try {
                  const chatHistory = JSON.parse(fileContent) as ChatHistory;
                  // 确保userId字段存在
                  if (!chatHistory.userId) {
                    chatHistory.userId = userDir;
                  }
                  chatHistories.push(chatHistory);
                } catch (e) {
                  console.error(`解析文件 ${file} 失败:`, e);
                }
              }
            }
          } catch (e) {
            console.error(`处理用户目录 ${userDir} 失败:`, e);
          }
        }

        // 如果找到了聊天记录，重建所有用户的索引
        if (chatHistories.length > 0) {
          console.log(`[LocalFileStorage] 找到 ${chatHistories.length} 条聊天记录，重建所有用户索引`);
          await this.rebuildIndex();
        }
      }

      // 按创建时间倒序排序，最新的排在前面
      return chatHistories.sort((a, b) =>
        new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      );
    } catch (error) {
      console.error('获取聊天历史列表失败:', error);
      return [];
    }
  }

  /**
   * 获取单个聊天历史
   */
  async getChatHistory(id: string, userId?: string): Promise<ChatHistory | null> {
    this.ensureBaseDirExists();

    let filePath = '';
    let found = false;

    if (userId) {
      // 在用户特定目录中查找
      filePath = path.join(this.getUserDir(userId), `${id}.json`);

      if (fs.existsSync(filePath)) {
        found = true;
      } else {
        // 如果在用户目录中找不到，返回null
        return null;
      }
    } else {
      // 在所有用户目录中查找
      const rootFiles = await fs.promises.readdir(this.basePath);
      const userDirs = rootFiles.filter(async (dir) => {
        const dirPath = path.join(this.basePath, dir);
        try {
          const stat = await fs.promises.stat(dirPath);
          return stat.isDirectory() && !dir.startsWith('.');
        } catch (e) {
          return false;
        }
      });

      for (const userDir of userDirs) {
        const userFilePath = path.join(this.basePath, userDir, `${id}.json`);

        if (fs.existsSync(userFilePath)) {
          filePath = userFilePath;
          found = true;
          break;
        }
      }

      if (!found) {
        return null;
      }
    }

    try {
      const fileContent = await fs.promises.readFile(filePath, 'utf-8');
      const chatHistory = JSON.parse(fileContent) as ChatHistory;

      // 如果没有userId字段，根据目录结构推断
      if (!chatHistory.userId && userId) {
        chatHistory.userId = userId;
      } else if (!chatHistory.userId && filePath.includes(path.sep)) {
        // 从文件路径中提取userId（如果路径包含用户目录）
        const parts = filePath.split(path.sep);
        const userDirIndex = parts.findIndex(part => part === 'chat-history') + 1;

        if (userDirIndex < parts.length && !parts[userDirIndex].endsWith('.json')) {
          chatHistory.userId = parts[userDirIndex];
        }
      }

      return chatHistory;
    } catch (error) {
      console.error(`获取聊天历史 ${id} 失败:`, error);
      return null;
    }
  }

  /**
   * 从索引中移除聊天历史（假删除）
   */
  async deleteChat(id: string, userId?: string): Promise<boolean> {
    this.ensureBaseDirExists();

    let filePath = '';
    let found = false;
    let foundUserId = userId;

    if (userId) {
      // 在用户特定目录中查找
      filePath = path.join(this.getUserDir(userId), `${id}.json`);

      if (fs.existsSync(filePath)) {
        found = true;
      }
    } else {
      // 在所有用户目录中查找
      const rootFiles = await fs.promises.readdir(this.basePath);

      for (const dir of rootFiles) {
        const dirPath = path.join(this.basePath, dir);

        try {
          const stat = await fs.promises.stat(dirPath);

          if (stat.isDirectory() && !dir.startsWith('.')) {
            const userFilePath = path.join(dirPath, `${id}.json`);

            if (fs.existsSync(userFilePath)) {
              filePath = userFilePath;
              foundUserId = dir;
              found = true;
              break;
            }
          }
        } catch (e) {
          console.error(`检查目录 ${dir} 失败:`, e);
        }
      }
    }

    if (!found) {
      return false;
    }

    try {
      // 读取聊天历史，以便更新索引
      let chatHistory: ChatHistory | null = null;
      try {
        const fileContent = await fs.promises.readFile(filePath, 'utf-8');
        chatHistory = JSON.parse(fileContent) as ChatHistory;

        // 确保userId字段存在
        if (!chatHistory.userId && foundUserId) {
          chatHistory.userId = foundUserId;
        }
      } catch (e) {
        console.error(`读取文件 ${filePath} 失败:`, e);
      }

      // 只更新索引，不删除实际文件（假删除）
      if (chatHistory && chatHistory.userId) {
        await this.updateChatHistoryIndex(chatHistory, true);
      }

      return true;
    } catch (error) {
      console.error(`从索引中移除聊天历史 ${id} 失败:`, error);
      return false;
    }
  }

  public async getAllUserIds(): Promise<string[]> {
    this.ensureBaseDirExists();
    const rootFiles = await fs.promises.readdir(this.basePath);
    const userIds: string[] = [];
    for (const dir of rootFiles) {
      const dirPath = path.join(this.basePath, dir);
      try {
        const stat = await fs.promises.stat(dirPath);
        if (stat.isDirectory() && !dir.startsWith('.')) {
          userIds.push(dir);
        }
      } catch (e) {
        // 忽略错误
      }
    }
    return userIds;
  }
}
