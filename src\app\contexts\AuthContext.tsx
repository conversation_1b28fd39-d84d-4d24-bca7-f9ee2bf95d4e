'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { iamAuth, UserInfo, TokenResponse, TokenData } from '@/utils/iamAuth';
import { userContextManager, UserContext } from '@/utils/userContext';
import { clientUserContextManager } from '@/utils/clientUserContext';
import api from '@/utils/apiClient';
import { initializeWmsUserInfo } from '@/utils/wmsService';
import { saveUserInfoAsMemory, saveFacilitiesAsMemory } from '@/utils/userMemoryUtils';
import { BiAuth } from '@/utils/biAuth';

// 定义认证上下文状态
interface AuthContextState {
  isAuthenticated: boolean;
  isLoading: boolean;
  user: UserInfo | null;
  tokenData: TokenData | null;
  error: string | null;
  login: () => void;
  logout: () => void;
  refreshAccessToken: () => Promise<boolean>;
  userContext: UserContext | null;
  getUserContext: () => Promise<UserContext | null>;
}

// 创建认证上下文
const AuthContext = createContext<AuthContextState | undefined>(undefined);

// 本地存储密钥
const STORAGE_KEYS = {
  ACCESS_TOKEN: 'iam_access_token',
  REFRESH_TOKEN: 'iam_refresh_token',
  ID_TOKEN: 'iam_id_token',
  TOKEN_EXPIRY: 'iam_token_expiry',
  USER_INFO: 'iam_user_info',
  PROCESSED_CODE: 'iam_processed_code'
};

interface AuthProviderProps {
  children: ReactNode;
}

/**
 * 认证上下文提供者
 *
 * 架构说明:
 * 1. 单一责任原则 - 每个函数只负责一个特定任务
 * 2. 状态管理 - 使用React状态和localStorage共同管理认证状态
 * 3. 明确的执行路径:
 *    - 回调页面: 由processAuthCallback负责处理授权回调
 *    - 其他页面: 由initAuthState负责初始化现有认证状态
 * 4. 防止重复处理:
 *    - 使用localStorage存储已处理的授权码
 *    - 精确检测回调页面和授权码状态
 *    - 避免使用全局状态变量
 * 5. 错误处理: 包含重试机制和清晰的错误报告
 */
export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [user, setUser] = useState<UserInfo | null>(null);
  const [tokenData, setTokenData] = useState<TokenData | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [userContextData, setUserContextData] = useState<UserContext | null>(null);

  const router = useRouter();
  // 使用Suspense兼容的方式获取查询参数
  const searchParamsRef = React.useRef<URLSearchParams | null>(null);

  // 客户端安全地获取查询参数
  const getQueryParam = (name: string): string | null => {
    if (typeof window === 'undefined') return null;

    if (!searchParamsRef.current) {
      searchParamsRef.current = new URLSearchParams(window.location.search);
    }

    return searchParamsRef.current.get(name);
  };

  // 获取用户ID（确保返回字符串类型）
  const getUserId = (): string | null => {
    if (!user && !tokenData) return null;

    // 优先使用tokenData中的sub作为用户ID
    if (tokenData?.sub) {
      // 如果ID是数字格式的字符串，直接返回
      return String(tokenData.sub);
    }

    // 否则使用user中的id
    if (user?.id) {
      // 保证字符串格式
      return String(user.id);
    }

    return null;
  };

  // 保存令牌到本地存储
  const saveTokens = (tokens: TokenResponse) => {
    if (typeof window === 'undefined') return;

    localStorage.setItem(STORAGE_KEYS.ACCESS_TOKEN, tokens.access_token);
    localStorage.setItem(STORAGE_KEYS.REFRESH_TOKEN, tokens.refresh_token);

    if (tokens.id_token) {
      localStorage.setItem(STORAGE_KEYS.ID_TOKEN, tokens.id_token);
    }

    const expiryTime = Date.now() + tokens.expires_in * 1000;
    localStorage.setItem(STORAGE_KEYS.TOKEN_EXPIRY, expiryTime.toString());

    // 解析访问令牌
    const parsedToken = iamAuth.parseToken(tokens.access_token);
    if (parsedToken) {
      setTokenData(parsedToken);
    }
  };

  // 清除令牌
  const clearTokens = () => {
    if (typeof window === 'undefined') return;

    localStorage.removeItem(STORAGE_KEYS.ACCESS_TOKEN);
    localStorage.removeItem(STORAGE_KEYS.REFRESH_TOKEN);
    localStorage.removeItem(STORAGE_KEYS.ID_TOKEN);
    localStorage.removeItem(STORAGE_KEYS.TOKEN_EXPIRY);
    localStorage.removeItem(STORAGE_KEYS.USER_INFO);
    localStorage.removeItem(STORAGE_KEYS.PROCESSED_CODE);

    // 清除BI登录信息
    BiAuth.clearBiInfo();

    setTokenData(null);
    setUser(null);
  };

  // 获取当前访问令牌
  const getAccessToken = (): string | null => {
    if (typeof window === 'undefined') return null;
    return localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);
  };

  // 获取当前刷新令牌
  const getRefreshToken = (): string | null => {
    if (typeof window === 'undefined') return null;
    return localStorage.getItem(STORAGE_KEYS.REFRESH_TOKEN);
  };

  // 获取令牌过期时间
  const getTokenExpiry = (): number => {
    if (typeof window === 'undefined') return 0;
    const expiry = localStorage.getItem(STORAGE_KEYS.TOKEN_EXPIRY);
    return expiry ? parseInt(expiry, 10) : 0;
  };

  // 检查令牌是否过期
  const isTokenExpired = (): boolean => {
    const expiry = getTokenExpiry();
    return expiry ? Date.now() >= expiry : true;
  };

  // 获取用户上下文
  const getUserContext = async (): Promise<UserContext | null> => {
    const userId = getUserId();
    if (!userId) return null;

    const context = await userContextManager.getUserContext(userId);
    setUserContextData(context);
    return context;
  };

  // 加载用户信息
  const loadUserInfo = async (accessToken: string) => {
    try {
      const userInfo = await iamAuth.getUserInfo(accessToken);
      setUser(userInfo);

      // 缓存用户信息
      if (typeof window !== 'undefined') {
        localStorage.setItem(STORAGE_KEYS.USER_INFO, JSON.stringify(userInfo));
      }

      return userInfo;
    } catch (error) {
      console.error('Failed to load user info:', error);
      setError('Failed to load user info');
      return null;
    }
  };

  // 登录方法，重定向到IAM授权页面
  const login = () => {
    if (typeof window === 'undefined') return;

    // 生成随机状态值，用于防止CSRF攻击
    const state = Math.random().toString(36).substring(2, 15);
    localStorage.setItem('iam_auth_state', state);

    // 获取授权URL并重定向
    const authUrl = iamAuth.getAuthorizationUrl(state);
    window.location.href = authUrl;
  };

  // 重置认证状态，用于登出或会话过期
  const resetAuthState = () => {
    clearTokens();
    setIsAuthenticated(false);
    setError(null);
  };

  // 登出方法
  const logout = () => {
    if (typeof window === 'undefined') return;

    // 获取用户ID用于清除会话
    const userId = getUserId();

    // 清除用户上下文
    if (userId) {
      userContextManager.clearUserSession(userId);
      setUserContextData(null);
    }

    // 清除BI登录信息
    BiAuth.clearBiInfo();

    const idToken = localStorage.getItem(STORAGE_KEYS.ID_TOKEN);
    resetAuthState();

    if (idToken) {
      // 构建注销URL，并设置回调到当前应用
      const logoutUrl = iamAuth.getLogoutUrl(
        idToken,
        window.location.origin
      );
      window.location.href = logoutUrl;
    } else {
      // 如果没有ID令牌，直接回到首页
      router.push('/');
    }
  };

  // 刷新访问令牌
  const refreshAccessToken = async (): Promise<boolean> => {
    const refreshToken = getRefreshToken();
    if (!refreshToken) return false;

    try {
      setIsLoading(true);
      const response = await api.post<TokenResponse>('/api/auth/refresh', { refreshToken }, { skipAuth: true });

      if (response.error) {
        console.error('Failed to refresh token:', response.error);
        setError('Session expired, please login again');
        clearTokens();
        setIsAuthenticated(false);
        return false;
      }

      const tokens = response.data!;
      saveTokens(tokens);
      setIsAuthenticated(true);
      return true;
    } catch (error) {
      console.error('Failed to refresh token:', error);
      setError('Session expired, please login again');
      clearTokens();
      setIsAuthenticated(false);
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * 初始化WMS用户信息，包含重试逻辑
   * @param userId 用户ID
   * @param maxRetries 最大重试次数
   * @param currentRetry 当前重试次数
   */
  const initWmsUserInfo = async (userId: string, maxRetries = 1, currentRetry = 0) => {
    try {
      console.log(`初始化WMS用户信息 (尝试 ${currentRetry + 1}/${maxRetries + 1})...`);

      // 使用新的初始化方法
      const wmsInitialized = await initializeWmsUserInfo(userId);

      if (wmsInitialized) {
        console.log('WMS用户信息初始化成功');
        return true;
      } else if (currentRetry < maxRetries) {
        console.warn(`WMS用户信息初始化失败，将重试 (${currentRetry + 1}/${maxRetries})`);
        // 短暂延时后重试
        await new Promise(resolve => setTimeout(resolve, 500));
        return await initWmsUserInfo(userId, maxRetries, currentRetry + 1);
      } else {
        console.warn('WMS用户信息初始化失败，已达到最大重试次数');
        return false;
      }
    } catch (error) {
      console.error('初始化WMS用户信息时发生错误:', error);
      if (currentRetry < maxRetries) {
        console.warn(`将重试初始化WMS用户信息 (${currentRetry + 1}/${maxRetries})`);
        await new Promise(resolve => setTimeout(resolve, 500));
        return await initWmsUserInfo(userId, maxRetries, currentRetry + 1);
      }
      return false;
    }
  };

  /**
   * 初始化BI用户信息，包含重试逻辑
   * @param maxRetries 最大重试次数
   * @param currentRetry 当前重试次数
   */
  const initBiUserInfo = async (maxRetries = 1, currentRetry = 0) => {
    try {
      console.log(`初始化BI用户信息 (尝试 ${currentRetry + 1}/${maxRetries + 1})...`);

      // 检查是否已经有BI登录信息
      if (BiAuth.hasBiInfo()) {
        console.log('BI登录信息已存在，跳过初始化');
        return true;
      }

      // 调用BI登录
      const biLoginResponse = await BiAuth.login();

      if (BiAuth.isValidResponse(biLoginResponse)) {
        // 保存BI登录信息到localStorage
        BiAuth.saveBiInfo(biLoginResponse);
        console.log('BI用户信息初始化成功');
        return true;
      } else if (currentRetry < maxRetries) {
        console.warn(`BI用户信息初始化失败，将重试 (${currentRetry + 1}/${maxRetries})`);
        // 短暂延时后重试
        await new Promise(resolve => setTimeout(resolve, 500));
        return await initBiUserInfo(maxRetries, currentRetry + 1);
      } else {
        console.warn('BI用户信息初始化失败，已达到最大重试次数');
        return false;
      }
    } catch (error) {
      console.error('初始化BI用户信息时发生错误:', error);
      if (currentRetry < maxRetries) {
        console.warn(`将重试初始化BI用户信息 (${currentRetry + 1}/${maxRetries})`);
        await new Promise(resolve => setTimeout(resolve, 500));
        return await initBiUserInfo(maxRetries, currentRetry + 1);
      }
      return false;
    }
  };

  /**
   * 处理授权回调
   * 这个函数只在回调页面执行一次，由 useEffect 直接调用，负责整个授权回调过程
   */
  const processAuthCallback = async () => {
    if (typeof window === 'undefined') return;

    // 声明一个变量用于存储WMS用户信息，以便在登录完成后使用
    let wmsUserInfoForMemory: any = null;

    const code = getQueryParam('code');
    const state = getQueryParam('state');

    if (!code) {
      console.log('没有授权码，无法处理回调');
      setError('No authorization code received');
      setIsLoading(false);
      return;
    }

    // 如果该code已经处理过，直接返回
    const processedCode = localStorage.getItem(STORAGE_KEYS.PROCESSED_CODE);
    if (processedCode === code) {
      console.log('该授权码已处理过，跳过处理');
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      // 验证状态参数以防止CSRF攻击
      const savedState = localStorage.getItem('iam_auth_state');
      console.log('savedState:', savedState);

      console.log('Authorization callback info:', {
        hasCode: true,
        stateMatches: state === savedState,
        codeLength: code.length,
        hasClientId: !!process.env.NEXT_PUBLIC_IAM_CLIENT_ID,
        currentDomain: typeof window !== 'undefined' ? window.location.host : 'server-side',
        dynamicRedirectUri: typeof window !== 'undefined' ? `${window.location.protocol}//${window.location.host}/auth/callback` : 'N/A'
      });

      if (state !== savedState) {
        throw new Error('State validation failed, possible CSRF attack');
      }

      // 先记录已处理的授权码，防止重复处理
      localStorage.setItem(STORAGE_KEYS.PROCESSED_CODE, code);

      // 清除保存的状态
      localStorage.removeItem('iam_auth_state');

      // 使用授权码获取令牌
      const tokens = await iamAuth.getTokenByCode(code);

      // 保存令牌
      saveTokens(tokens);

      // 加载用户信息
      const userInfo = await loadUserInfo(tokens.access_token);

      // 初始化用户上下文
      if (userInfo && tokens.access_token) {
        const tokenData = iamAuth.parseToken(tokens.access_token);
        // 确保使用一致的用户ID格式
        const userId = tokenData?.sub ? String(tokenData.sub) : userInfo.id ? String(userInfo.id) : null;

        if (userId) {
          // 清除可能存在的旧WMS用户数据，避免ID不匹配问题
          await userContextManager.clearUserSession(userId);

          const userContext = await userContextManager.initUserSession(
            userId,
            userInfo.userName,
            userInfo.email
          );

          setUserContextData(userContext);
          console.log('用户上下文已初始化:', userContext);

          // 初始化WMS用户信息
          try {
            console.log('开始初始化WMS用户信息...');
            // 使用新的初始化函数，允许一次重试
            await initWmsUserInfo(userId, 1);
          } catch (error) {
            console.error('初始化WMS用户信息时发生错误:', error);
            // 不抛出异常，避免阻止登录流程
          }

          // 初始化BI用户信息
          try {
            // 使用新的初始化函数，允许一次重试
            await initBiUserInfo(1);
          } catch (error) {
            console.error('初始化BI用户信息时发生错误:', error);
            // 不抛出异常，避免阻止登录流程
          }

          // 获取WMS用户信息，但不立即保存为记忆
          const wmsUserInfo = clientUserContextManager.getWmsUserInfoByUserId(userId);

          // 将WMS用户信息保存到函数作用域变量中，以便在登录完成后使用
          wmsUserInfoForMemory = wmsUserInfo;
        }
      }

      setIsAuthenticated(true);

      // 清除URL中的查询参数，防止用户刷新时再次处理授权码
      window.history.replaceState({}, document.title, window.location.pathname);

      // 重定向到首页或保存的URL
      router.push('/');

      // 延迟保存用户记忆，不阻塞登录流程
      if (wmsUserInfoForMemory) {
        setTimeout(async () => {
          try {
            console.log('延迟保存用户信息为记忆（并行执行）...');

            // 使用 Promise.all 同时执行两个记忆保存操作
            const [userInfoResult, facilitiesResult] = await Promise.all([
              // 1. 保存基本用户信息（不包含profile节点）
              saveUserInfoAsMemory(wmsUserInfoForMemory)
                .then(success => {
                  if (success) {
                    console.log('WMS用户基本信息已成功保存为记忆');
                  }
                  return success;
                })
                .catch(error => {
                  console.error('保存用户基本信息时出错:', error);
                  return false;
                }),

              // 2. 将设施信息转换为自然语言并单独保存
              saveFacilitiesAsMemory(wmsUserInfoForMemory)
                .then(success => {
                  if (success) {
                    console.log('设施信息已成功以自然语言形式保存为记忆');
                  }
                  return success;
                })
                .catch(error => {
                  console.error('保存设施信息时出错:', error);
                  return false;
                })
            ]);

            console.log(`记忆保存完成，结果: 用户信息=${userInfoResult}, 设施信息=${facilitiesResult}`);
          } catch (error) {
            console.error('延迟保存WMS用户信息为记忆时发生错误:', error);
            // 不影响用户体验，仅记录错误
          }
        }, 3000); // 延迟3秒执行，确保登录流程已完成
      }
    } catch (error) {
      console.error('Authentication callback handling failed:', error);
      setError(error instanceof Error ? error.message : 'Authentication failed');
      setIsAuthenticated(false);
      // 发生错误时清除已处理标记，以便可以重试
      localStorage.removeItem(STORAGE_KEYS.PROCESSED_CODE);
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * 初始化认证状态
   * 这个函数只在非回调页面执行，负责检查和恢复现有的认证状态
   */
  const initAuthState = async () => {
    try {
      setIsLoading(true);
      console.log('DEBUG-AUTH: 设置 isLoading = true');

      // 检查是否有访问令牌
      const accessToken = getAccessToken();
      if (!accessToken) {
        setIsAuthenticated(false);
        console.log('DEBUG-AUTH: 没有访问令牌，设置 isAuthenticated = false');
        setIsLoading(false);
        console.log('DEBUG-AUTH: 设置 isLoading = false');
        return;
      }

      // 检查令牌是否过期
      if (isTokenExpired()) {
        // 尝试刷新令牌
        const refreshSuccess = await refreshAccessToken();
        if (!refreshSuccess) return;
      }

      // 解析令牌数据
      const token = iamAuth.parseToken(accessToken);
      if (token) {
        setTokenData(token);
      }

      // 加载用户信息，优先使用缓存
      const cachedUserInfo = localStorage.getItem(STORAGE_KEYS.USER_INFO);
      if (cachedUserInfo) {
        setUser(JSON.parse(cachedUserInfo));
      } else {
        await loadUserInfo(accessToken);
      }

      // 加载用户上下文
      if (token && token.sub) {
        const userId = String(token.sub);
        const context = await userContextManager.getUserContext(userId);
        if (context) {
          setUserContextData(context);
          // 如果已经有上下文，设置认证状态为true
          console.log('DEBUG-AUTH: 已有上下文，设置 isAuthenticated = true');
          setIsAuthenticated(true);
          // 如果已经有上下文，设置isLoading为false
          console.log('DEBUG-AUTH: 已有上下文，设置 isLoading = false');
          setIsLoading(false);
        } else {
          // 如果上下文不存在，但用户已经通过认证，则创建上下文
          const userInfo = user || (cachedUserInfo ? JSON.parse(cachedUserInfo) : null);
          if (userInfo) {
            const newContext = await userContextManager.initUserSession(
              userId,
              userInfo.userName,
              userInfo.email
            );
            setUserContextData(newContext);

            // 初始化WMS用户信息
            try {
              console.log('初始化WMS用户信息...');
              // 使用新的初始化函数，允许一次重试
              await initWmsUserInfo(userId, 1);
              // 只有在WMS初始化成功后才设置认证状态为true和isLoading为false
              console.log('DEBUG-AUTH: WMS初始化成功，设置 isAuthenticated = true');
              setIsAuthenticated(true);
              // 在WMS初始化完成后，设置isLoading为false
              console.log('DEBUG-AUTH: WMS初始化成功，设置 isLoading = false');
              setIsLoading(false);
            } catch (error) {
              console.error('初始化WMS用户信息失败:', error);
              // 即使WMS初始化失败，也设置认证状态为true，因为用户已经通过了IAM认证
              console.log('DEBUG-AUTH: WMS初始化失败，但仍设置 isAuthenticated = true');
              setIsAuthenticated(true);
              // 在WMS初始化失败后，也设置isLoading为false
              console.log('DEBUG-AUTH: WMS初始化失败，设置 isLoading = false');
              setIsLoading(false);
            }

            // 初始化BI用户信息
            try {
              // 使用新的初始化函数，允许一次重试
              await initBiUserInfo(1);
            } catch (error) {
              console.error('初始化BI用户信息时发生错误:', error);
              // 不抛出异常，避免阻止登录流程
            }
          } else {
            // 如果没有用户信息，也设置认证状态为true
            console.log('DEBUG-AUTH: 没有用户信息，但仍设置 isAuthenticated = true');
            setIsAuthenticated(true);
            // 如果没有用户信息，也设置isLoading为false
            console.log('DEBUG-AUTH: 没有用户信息，设置 isLoading = false');
            setIsLoading(false);
          }
        }
      } else {
        // 如果没有token或sub，也设置认证状态为true
        console.log('DEBUG-AUTH: 没有token或sub，但仍设置 isAuthenticated = true');
        setIsAuthenticated(true);
        // 如果没有token或sub，也设置isLoading为false
        console.log('DEBUG-AUTH: 没有token或sub，设置 isLoading = false');
        setIsLoading(false);
      }
    } catch (error) {
      console.error('Failed to initialize auth state:', error);
      setError('Failed to initialize auth state');
      clearTokens();
      setIsAuthenticated(false);
      // 在出错时设置isLoading为false
      console.log('DEBUG-AUTH: 初始化出错，设置 isLoading = false');
      setIsLoading(false);
    }
    // 移除finally块，因为我们已经在各个分支中设置了isLoading
  };

  // 使用单个useEffect处理所有初始化逻辑
  useEffect(() => {
    const init = async () => {
      // 获取当前路径和授权码（将它们保存为变量避免引用searchParams对象）
      const pathname = typeof window !== 'undefined' ? window.location.pathname : '';
      const code = getQueryParam('code');
      const processedCode = typeof window !== 'undefined' ? localStorage.getItem(STORAGE_KEYS.PROCESSED_CODE) : null;

      // 在useEffect中更新searchParamsRef
      if (typeof window !== 'undefined') {
        searchParamsRef.current = new URLSearchParams(window.location.search);
      }

      // 更精确的回调页面检测 - 完全匹配/auth/callback而不是部分匹配
      const isCallbackPage = pathname === '/auth/callback';

      // 检查当前授权码是否已经被处理过
      const isAlreadyProcessed = code && processedCode === code;

    if (isCallbackPage && code && !isAlreadyProcessed) {
      // 在回调页面，且有授权码，且未处理过 - 执行回调处理
      console.log('检测到授权回调，开始处理');
        await processAuthCallback();
    } else {
      // 不是回调页面或已处理过 - 初始化认证状态
      console.log('初始化认证状态');
        await initAuthState();
    }
    };

    init().catch(error => {
      console.error('认证初始化失败:', error);
      setError('认证初始化失败');
      setIsLoading(false);
    });
  // 使用window.location.search变化作为依赖项触发更新
  }, [typeof window !== 'undefined' ? window.location.search : '', typeof window !== 'undefined' ? window.location.pathname : '']);

  // 提供上下文值
  const contextValue: AuthContextState = {
    isAuthenticated,
    isLoading,
    user,
    tokenData,
    error,
    login,
    logout,
    refreshAccessToken,
    userContext: userContextData,
    getUserContext
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};

// 创建认证钩子
export const useAuth = (): AuthContextState => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};