# AI模型API密钥
OPENAI_API_KEY=your_openai_api_key
ANTHROPIC_API_KEY=your_anthropic_api_key
GOOGLE_GENERATIVE_AI_API_KEY=your_google_api_key
DEEPSEEK_API_KEY=your_deepseek_api_key

# IAM配置（Stage环境）
# 客户端ID（在IAM管理控制台中创建，需要公开可访问，所以前缀为NEXT_PUBLIC_）
NEXT_PUBLIC_IAM_CLIENT_ID=0b006bce-fd62-42d7-b031-5d099e08c55b
# 客户端密钥（保密）
IAM_CLIENT_SECRET=90e3ee8b-8178-40b4-8749-42b9ebc3b0f1
# 重定向URI（需要与IAM注册的重定向URI完全匹配，前端需要访问所以使用NEXT_PUBLIC_）
NEXT_PUBLIC_IAM_REDIRECT_URI=http://localhost:3000/auth/callback


NEXT_PUBLIC_IAM_ENDPOINT=https://id-staging.item.com

# WMS服务端点
NEXT_PUBLIC_WMS_ENDPOINT=https://wms-staging.item.com/api/wms-bam

# 存储配置
STORAGE_TYPE=local
LOCAL_STORAGE_DIR=src/chat-history
LOCAL_REPORTS_DIR=data/reports

# S3存储配置（仅当STORAGE_TYPE=s3时使用）
# S3_BUCKET_NAME=your-bucket-name
# AWS_REGION=us-east-1
# S3_PREFIX=chat-history/
# S3_REPORTS_PREFIX=reports/
# AWS_ACCESS_KEY_ID=your-access-key
# AWS_SECRET_ACCESS_KEY=your-secret-key
# 重要提示：
# 1. 确保IAM_CLIENT_SECRET确实存在并正确设置
# 2. NEXT_PUBLIC_前缀的变量在客户端和服务器端都可访问
# 3. 没有NEXT_PUBLIC_前缀的变量仅在服务器端可访问

KB_BASE_URL=http://************:3005
siteToolConfig={"default":{"mcpServerIds":[],"tools":["sequentialthinking"]},"wms":{"mcpServerIds":["wms"],"tools":["sequentialthinking","find_wms_api","call_wms_api","kbtool"]}}
TRUSTED_DOMAINS=localhost:3000, localhost, 127.0.0.1

# Email Configuration
# SMTP server settings
SMTP_HOST=mail.unisco.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=abc12345
SMTP_FROM=<EMAIL>
SMTP_USE_TLS=true

# Note: Copy this file to .env and update with your actual values
# Make sure to add .env to .gitignore to keep sensitive data secure