import { clientUserContextManager, WmsUserInfo, Facility } from './clientUserContext';
import { iamAuth } from './iamAuth';
import { createWmsInitStrategy } from './wmsInitStrategies';
import { wmsStateManager } from './wmsStateManager';
import { log } from './logger';

// WMS API 端点
const WMS_ENDPOINT = process.env.NEXT_PUBLIC_WMS_ENDPOINT || 'https://wms-staging.item.com';

/**
 * 获取WMS用户信息
 * @param skipDefaultFacility 是否跳过设置默认设施，通常在刷新页面时设为true
 */
export async function fetchWmsUserInfo(skipDefaultFacility: boolean = false): Promise<WmsUserInfo | null> {
  try {
    // 获取令牌
    const token = clientUserContextManager.getAuthToken();
    if (!token) {
      log.error('未找到访问令牌，无法获取WMS用户信息', undefined, 'wmsService');
      return null;
    }

    // 从令牌解析用户数据
    const tokenData = iamAuth.parseToken(token);
    if (!tokenData || !tokenData.data || !tokenData.data.user_id) {
      log.error('无法从令牌解析用户ID', undefined, 'wmsService');
      return null;
    }

    // 从令牌中获取用户信息和租户ID
    const userId = tokenData.data.user_id;
    // 从 token data 中正确获取租户 ID，优先使用 tenant_id，其次使用 company_code
    const tenantId = tokenData.data.tenant_id || tokenData.data.company_code || 'LT';

    log.debug(`正在获取WMS用户信息，用户ID: ${userId}, 租户ID: ${tenantId}`, undefined, 'wmsService');

    // 确保userId是数字格式 - 防止类型转换错误
    // Java端期望Long类型，所以我们需要确保传递的是数字而不是字符串
    const numericUserId = /^\d+$/.test(userId) ? userId : null;
    
    if (!numericUserId) {
      log.error('用户ID不是有效的数字格式:', userId, 'wmsService');
      return null;
    }

    // 发起 API 请求获取用户信息和设施
    const response = await fetch(`${WMS_ENDPOINT}/api/wms-bam/user/${numericUserId}`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'x-tenant-id': tenantId
      }
    });

    if (!response.ok) {
      const errorText = await response.text();
      log.error(`获取WMS用户信息失败: ${response.status}`, errorText, 'wmsService');
      return null;
    }

    const userData = await response.json();
    log.debug('获取到WMS用户数据:', userData, 'wmsService');

    // 提取设施信息
    let facilities: Facility[] = [];
    // 如果不成功，直接返回
    if (!userData.success) {
      log.error('获取WMS用户信息返回失败:', userData.msg, 'wmsService');
      return null;
    }

    // 修改: 设施数据在profile.facilities中，不是facilityCodes
    if (userData.data && userData.data.profile && Array.isArray(userData.data.profile.facilities)) {
      facilities = userData.data.profile.facilities.map((facility: any) => ({
        id: facility.id,
        name: facility.name,
        code: facility.id.split('_').pop() || facility.id // 从ID中提取代码部分
      }));
      
      log.debug('解析到的设施列表:', facilities, 'wmsService');
    } else {
      log.warn('未找到设施数据或格式不正确', undefined, 'wmsService');
      log.debug('userData:', JSON.stringify(userData.data), 'wmsService');
    }

    // 获取默认设施，只在skipDefaultFacility为false时设置
    let defaultFacility: Facility | undefined = undefined;
    
    if (!skipDefaultFacility) {
      // 只有在非跳过模式下才设置默认设施
      if (userData.data.profile && userData.data.profile.defaultFacility) {
        const defaultFacilityData = userData.data.profile.defaultFacility;
        defaultFacility = {
          id: defaultFacilityData.id,
          name: defaultFacilityData.name,
          code: defaultFacilityData.id.split('_').pop() || defaultFacilityData.id
        };
        log.debug('使用系统配置的默认设施:', defaultFacility.name, 'wmsService');
      } else if (facilities.length > 0) {
        // 如果未设置默认设施，则使用第一个设施
        defaultFacility = facilities[0];
        log.debug('系统未配置默认设施，使用第一个设施作为默认:', defaultFacility.name, 'wmsService');
      }
    } else {
      log.debug('跳过设置默认设施，等待后续处理决定使用哪个设施', undefined, 'wmsService');
    }

    // 构建WMS用户信息
    const wmsUserInfo: WmsUserInfo = {
      id: userId,
      username: tokenData.data.user_name || '',
      fullName: userData.data?.firstName ? `${userData.data.firstName} ${userData.data.lastName || ''}` : undefined,
      email: userData.data?.email || undefined,
      tenantId: tenantId,
      facilities: facilities,
      currentFacility: defaultFacility,
      roles: userData.data?.userRoles?.map((role: any) => role.name) || []
    };

    log.debug('构建的WMS用户信息:', {
      username: wmsUserInfo.username,
      facilitiesCount: wmsUserInfo.facilities.length,
      currentFacility: wmsUserInfo.currentFacility?.name || '未设置',
      roles: wmsUserInfo.roles
    }, 'wmsService');

    return wmsUserInfo;
  } catch (error) {
    log.error('获取WMS用户信息时发生错误:', error, 'wmsService');
    return null;
  }
}

/**
 * 初始化WMS用户信息
 * 在用户登录后调用
 */
export async function initializeWmsUserInfo(userId: string): Promise<boolean> {
  try {
    log.info('初始化WMS用户信息...', undefined, 'wmsService');
    
    // 使用策略工厂创建合适的初始化策略
    const strategy = createWmsInitStrategy();
    
    // 使用策略初始化状态
    const wmsUserInfo = await wmsStateManager.initializeState(userId, strategy);
    
    if (!wmsUserInfo) {
      log.error('WMS用户信息初始化失败', undefined, 'wmsService');
      return false;
    }
    
    // 如果会话中有用户上下文，更新WMS用户信息
    const sessionData = clientUserContextManager.getUserSession(userId);
    if (sessionData) {
      clientUserContextManager.updateWmsUserInfo(userId, wmsUserInfo);
    }
    
    log.info('WMS用户信息初始化成功', undefined, 'wmsService');
    return true;
  } catch (error) {
    log.error('初始化WMS用户信息失败:', error, 'wmsService');
    return false;
  }
}

/**
 * 切换当前设施
 */
export function switchFacility(userId: string, facilityId: string): boolean {
  try {
    // 获取WMS用户信息
    const wmsUserInfo = clientUserContextManager.getWmsUserInfoByUserId(userId);
    if (!wmsUserInfo) {
      console.error('切换设施失败：无法获取WMS用户信息');
      return false;
    }
    
    // 设置当前设施
    const updatedUserInfo = wmsStateManager.setCurrentFacility(wmsUserInfo, facilityId);
    if (!updatedUserInfo) {
      console.error('切换设施失败：无法设置当前设施');
      return false;
    }
    
    // 更新会话中的用户信息
    clientUserContextManager.updateWmsUserInfo(userId, updatedUserInfo);
    
    return true;
  } catch (error) {
    console.error('切换设施失败:', error);
    return false;
  }
}

/**
 * 获取用户的所有设施
 */
export function getUserFacilities(userId: string): Facility[] {
  // 从会话或localStorage获取用户信息
  const wmsUserInfo = clientUserContextManager.getWmsUserInfoByUserId(userId);
  
  if (!wmsUserInfo || !Array.isArray(wmsUserInfo.facilities)) {
    console.warn('获取用户设施失败：无法获取WMS用户信息或设施列表');
    return [];
  }
  
  return wmsUserInfo.facilities;
}

/**
 * 获取用户当前设施
 */
export function getCurrentFacility(userId: string): Facility | undefined {
  // 从会话或localStorage获取用户信息
  const wmsUserInfo = clientUserContextManager.getWmsUserInfoByUserId(userId);
  
  if (!wmsUserInfo) {
    console.warn('获取当前设施失败：无法获取WMS用户信息');
    return undefined;
  }
  
  return wmsUserInfo.currentFacility;
} 