import axios from 'axios';

// 从环境变量获取IAM端点
const IAM_ENDPOINT = process.env.NEXT_PUBLIC_IAM_ENDPOINT || 'https://id-staging.item.com';

// IAM路径配置
const IAM_PATHS = {
  authorizationEndpoint: '/oauth2/authorize',
  tokenEndpoint: '/oauth2/token',
  userInfoEndpoint: '/user-info',
  logoutEndpoint: '/oauth2/logout',
  jwksUri: '/.well-known/jwks.json'
};

// 定义OAuth2客户端配置接口
export interface OAuthClientConfig {
  clientId: string;
  clientSecret: string;
  redirectUri?: string; // 改为可选，支持动态生成
  scope: string[];
}

// 定义令牌接口
export interface TokenResponse {
  access_token: string;
  token_type: string;
  expires_in: number;
  refresh_token: string;
  id_token?: string;
}

// 定义用户信息接口
export interface UserInfo {
  id: number;
  accountId: number;
  contactNumber: string;
  email: string;
  firstName: string;
  lastName: string;
  userName: string;
  userStatus: string;
}

// 定义解析后的访问令牌内容
export interface TokenData {
  sub: string;
  aud: string;
  iss: string;
  exp: number;
  iat: number;
  jti: string;
  scope: string[];
  client_id?: string;
  client_name?: string;
  client_category?: number;
  nbf?: number;
  grant_type?: string;
  data: {
    grant_type?: string;
    client_type?: string;
    user_id: string;
    user_name: string;
    account_id?: string;
    company_id?: string;
    tenant_id?: string;
    tenants?: string[];
    company_code?: string;
    role_ids: string[];
  };
}

/**
 * IAM身份验证工具类
 */
export class IamAuth {
  private config: OAuthClientConfig;

  constructor(config: OAuthClientConfig) {
    this.config = config;
  }

  /**
   * 动态生成 redirect URI
   * @returns 当前域名对应的 redirect URI
   */
  private getRedirectUri(): string {
    // 如果配置中有固定的 redirectUri，优先使用
    if (this.config.redirectUri) {
      return this.config.redirectUri;
    }

    // 在浏览器环境中动态生成
    if (typeof window !== 'undefined') {
      const { protocol, host } = window.location;
      return `${protocol}//${host}/auth/callback`;
    }

    // 服务器端环境，尝试从环境变量获取，或使用默认值
    return process.env.NEXT_PUBLIC_IAM_REDIRECT_URI || 'http://localhost:3000/auth/callback';
  }

  /**
   * 获取授权URL
   * @param state 随机状态值，用于防止CSRF攻击
   * @returns 完整的授权URL
   */
  getAuthorizationUrl(state: string): string {
    const { clientId, scope } = this.config;
    const redirectUri = this.getRedirectUri();
    
    const queryParams = new URLSearchParams({
      response_type: 'code',
      client_id: clientId,
      redirect_uri: redirectUri,
      scope: scope.join(' '),
      state
    });

    return `${IAM_ENDPOINT}${IAM_PATHS.authorizationEndpoint}?${queryParams.toString()}`;
  }

  /**
   * 使用授权码交换访问令牌
   * @param code 授权码
   * @returns 令牌响应
   */
  async getTokenByCode(code: string): Promise<TokenResponse> {
    const { clientId, clientSecret } = this.config;
    const redirectUri = this.getRedirectUri();
    
    // 在浏览器中，使用API端点而不是直接调用
    if (typeof window !== 'undefined') {
      try {
        console.log('在客户端使用API端点获取令牌');
        const response = await fetch('/api/auth/token', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            code,
            redirectUri
          }),
        });
        
        if (!response.ok) {
          const errorData = await response.json();
          console.error('API令牌交换失败:', errorData);
          throw new Error(errorData.error || '获取访问令牌失败');
        }
        
        return await response.json();
      } catch (error) {
        console.error('获取访问令牌失败:', error);
        throw error;
      }
    }
    
    // 服务器端代码路径 - 直接调用IAM API
    // 检查必要参数
    if (!clientId || !clientSecret) {
      console.error('缺少客户端ID或密钥');
      throw new Error('配置错误：缺少客户端ID或密钥');
    }
    
    // 准备Basic认证头
    const authStr = `${clientId}:${clientSecret}`;
    const authHeader = Buffer.from(authStr).toString('base64');
    
    console.log('授权信息:', {
      tokenEndpoint: `${IAM_ENDPOINT}${IAM_PATHS.tokenEndpoint}`,
      redirectUri,
      authStrLength: authStr.length,
      // 安全日志，只显示部分信息用于调试
      clientIdPrefix: clientId.substring(0, 5) + '...',
      hasClientSecret: !!clientSecret,
      clientSecretLength: clientSecret ? clientSecret.length : 0
    });
    
    try {
      // 准备请求参数
      const params = new URLSearchParams();
      params.append('grant_type', 'authorization_code');
      params.append('code', code);
      params.append('redirect_uri', redirectUri);
      
      const response = await axios.post(
        `${IAM_ENDPOINT}${IAM_PATHS.tokenEndpoint}`,
        params,
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Authorization': `Basic ${authHeader}`
          }
        }
      );
      
      return response.data as TokenResponse;
    } catch (error) {
      console.error('获取访问令牌失败:', error);
      if (axios.isAxiosError(error) && error.response) {
        console.error('服务器响应:', {
          status: error.response.status,
          data: error.response.data
        });
      }
      throw new Error('获取访问令牌失败');
    }
  }

  /**
   * 刷新访问令牌
   * @param refreshToken 刷新令牌
   * @returns 新的令牌响应
   */
  async refreshToken(refreshToken: string): Promise<TokenResponse> {
    const { clientId, clientSecret, scope } = this.config;
    
    // 在浏览器中，使用API端点而不是直接调用
    if (typeof window !== 'undefined') {
      try {
        console.log('在客户端使用API端点刷新令牌');
        const response = await fetch('/api/auth/refresh', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            refreshToken
          }),
        });
        
        if (!response.ok) {
          const errorData = await response.json();
          console.error('API刷新令牌失败:', errorData);
          throw new Error(errorData.error || '刷新令牌失败');
        }
        
        return await response.json();
      } catch (error) {
        console.error('刷新令牌失败:', error);
        throw error;
      }
    }
    
    // 服务器端代码路径
    // 检查必要参数
    if (!clientId || !clientSecret) {
      console.error('缺少客户端ID或密钥');
      throw new Error('配置错误：缺少客户端ID或密钥');
    }
    
    // 准备Basic认证头
    const authStr = `${clientId}:${clientSecret}`;
    const authHeader = Buffer.from(authStr).toString('base64');
    
    try {
      // 准备请求参数
      const params = new URLSearchParams();
      params.append('grant_type', 'refresh_token');
      params.append('refresh_token', refreshToken);
      
      // 添加scope参数（如果有）
      if (scope && scope.length > 0) {
        params.append('scope', scope.join(' '));
      }
      
      const response = await axios.post(
        `${IAM_ENDPOINT}${IAM_PATHS.tokenEndpoint}`,
        params,
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Authorization': `Basic ${authHeader}`
          }
        }
      );
      
      return response.data as TokenResponse;
    } catch (error) {
      console.error('刷新令牌失败:', error);
      if (axios.isAxiosError(error) && error.response) {
        console.error('服务器响应:', {
          status: error.response.status,
          data: error.response.data
        });
      }
      throw new Error('刷新令牌失败');
    }
  }

  /**
   * 获取用户信息
   * @param accessToken 访问令牌
   * @returns 用户信息
   */
  async getUserInfo(accessToken: string): Promise<UserInfo> {
    try {
      const response = await axios.get(
        `${IAM_ENDPOINT}${IAM_PATHS.userInfoEndpoint}`,
        {
          headers: {
            'Authorization': `Bearer ${accessToken}`
          }
        }
      );
      
      // 解析响应数据，根据文档中的API返回格式
      if (response.data.success) {
        return response.data.data as UserInfo;
      } else {
        throw new Error(response.data.msg || '获取用户信息失败');
      }
    } catch (error) {
      console.error('获取用户信息失败:', error);
      throw new Error('获取用户信息失败');
    }
  }

  /**
   * 获取注销URL
   * @param idToken ID令牌
   * @param postLogoutRedirectUri 注销后重定向的URI
   * @param state 状态参数
   * @returns 完整的注销URL
   */
  getLogoutUrl(idToken: string, postLogoutRedirectUri?: string, state?: string): string {
    const params: Record<string, string> = {
      id_token_hint: idToken
    };
    
    if (postLogoutRedirectUri) {
      params.post_logout_redirect_uri = postLogoutRedirectUri;
    }
    
    if (state) {
      params.state = state;
    }
    
    const queryParams = new URLSearchParams(params);
    return `${IAM_ENDPOINT}${IAM_PATHS.logoutEndpoint}?${queryParams.toString()}`;
  }

  /**
   * 解析JWT令牌（简化版，生产环境应该验证签名）
   * @param token JWT令牌
   * @returns 解析后的令牌数据
   */
  parseToken(token: string): TokenData | null {
    try {
      const [, payload] = token.split('.');
      const decodedPayload = Buffer.from(payload, 'base64').toString('utf-8');
      return JSON.parse(decodedPayload) as TokenData;
    } catch (error) {
      console.error('解析令牌失败:', error);
      return null;
    }
  }
}

// 创建默认的IAM实例，从环境变量获取配置
export const createDefaultIamAuth = () => {
  const config: OAuthClientConfig = {
    clientId: process.env.NEXT_PUBLIC_IAM_CLIENT_ID || '',
    // 客户端不应该尝试访问IAM_CLIENT_SECRET
    clientSecret: '',  // 客户端不需要secret，会通过API调用在服务器端使用
    // redirectUri 不再从环境变量获取，改为动态生成
    // redirectUri: process.env.NEXT_PUBLIC_IAM_REDIRECT_URI || '',
    scope: ['openid']
  };
  
  return new IamAuth(config);
};

// 导出默认实例
export const iamAuth = createDefaultIamAuth(); 