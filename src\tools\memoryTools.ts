import { tool } from 'ai';
import { z } from 'zod';
import { getMemoryService, MemoryType } from '@/services/memoryService';
import { getUserIdFromRequestContext } from '@/utils/requestContext';

// 记忆查询工具
export const searchMemoriesTool = tool({
  description: 'Search for relevant information in personal memories or knowledge base. Use this tool when you need to recall user preferences, past interactions, or system knowledge.',
  parameters: z.object({
    query: z.string().describe('Search query, can be a specific question or keywords'),
    memory_type: z.enum(['user', 'system']).describe('Memory type: user (personal memory) or system (knowledge base)'),
    limit: z.number().int().default(5).describe('Maximum number of results to return'),
    threshold: z.number().default(0.3).describe('Similarity threshold (0-1), only return results with similarity greater than this value')
  }),
  execute: async ({ query, memory_type, limit = 5, threshold = 0.3 }, { toolCallId }) => {
    console.log(`[MemoryTool] Searching ${memory_type} memories, query: "${query}", threshold: ${threshold}`);

    try {
      // Get memory service instance
      const memoryService = getMemoryService();

      // Get user ID from request context
      const userId = getUserIdFromRequestContext(toolCallId);
      console.log(`[MemoryTool] User ID from context: ${userId || 'not found'}`);

      if (!userId && memory_type === 'user') {
        return {
          error: 'Unable to get user ID, cannot search user memories',
          memories: []
        };
      }

      // Determine memory type
      const type = memory_type === 'user' ? MemoryType.USER : MemoryType.SYSTEM;

      // For user memories, use the user ID; for system memories, use 'system'
      const searchUserId = memory_type === 'user' ? userId : 'system';

      try {
        // Search memories with similarity threshold
        const results = await memoryService.searchMemory(type, query, searchUserId, limit, threshold);

        // 如果没有找到结果，返回明确的消息
        if (!results.results || results.results.length === 0) {
          return {
            memories: [],
            count: 0,
            message: `No ${memory_type === 'user' ? 'personal memory' : 'knowledge base'} content found related to "${query}"`
          };
        }

        // Format results
        return {
          memories: results.results.map((m: any) => ({
            content: m.memory,
            id: m.id,
            score: m.score
          })),
          count: results.results.length
        };
      } catch (memoryError) {
        console.error('[MemoryTool] Memory search operation failed:', memoryError);
        return {
          error: 'Memory service temporarily unavailable, please try again later',
          memories: [],
          details: process.env.NODE_ENV === 'development' ? String(memoryError) : undefined
        };
      }
    } catch (error) {
      console.error('[MemoryTool] Memory search failed:', error);
      return {
        error: 'Memory search failed: ' + (error instanceof Error ? error.message : String(error)),
        memories: []
      };
    }
  }
});

// 记忆保存工具
export const saveMemoryTool = tool({
  description: 'Save important information to personal memory or knowledge base. Use this tool when you discover important user preferences, facts, or information that needs to be remembered long-term.',
  parameters: z.object({
    content: z.string().describe('Memory content to save, should be a concise statement'),
    memory_type: z.enum(['user', 'system']).describe('Memory type: user (personal memory) or system (knowledge base)'),
    importance: z.number().int().min(1).max(10).default(5).describe('Importance of the memory (1-10), higher numbers indicate greater importance')
  }),
  execute: async ({ content, memory_type, importance = 5 }, { toolCallId }) => {
    console.log(`[MemoryTool] Saving ${memory_type} memory: "${content.substring(0, 50)}..."`);

    try {
      // Get memory service instance
      const memoryService = getMemoryService();

      // Get user ID from request context
      const userId = getUserIdFromRequestContext(toolCallId);
      console.log(`[MemoryTool] User ID from context: ${userId || 'not found'}`);

      if (!userId && memory_type === 'user') {
        return {
          error: 'Unable to get user ID, cannot save user memory',
          success: false
        };
      }

      // Determine memory type
      const type = memory_type === 'user' ? MemoryType.USER : MemoryType.SYSTEM;

      // For user memories, use user ID; for system memories, use 'system'
      const memoryUserId = memory_type === 'user' ? userId : 'system';

      // Ensure memoryUserId is not undefined (should never happen now)
      if (!memoryUserId) {
        return {
          error: 'Unable to get valid user ID, cannot save memory',
          success: false
        };
      }

      console.log(`[MemoryTool] Using user ID for memory: ${memoryUserId}`);

      try {
        // Save memory
        const result = await memoryService.addMemory(
          type,
          content,
          memoryUserId,
          {
            importance,
            timestamp: new Date().toISOString(),
            source: 'ai_tool'
          }
        );

        return {
          success: true,
          memory_id: result.id,
          message: `Successfully saved ${memory_type} memory`
        };
      } catch (memoryError) {
        console.error('[MemoryTool] Memory save operation failed:', memoryError);
        return {
          error: 'Memory service temporarily unavailable, please try again later',
          success: false,
          details: process.env.NODE_ENV === 'development' ? String(memoryError) : undefined
        };
      }
    } catch (error) {
      console.error('[MemoryTool] Memory save failed:', error);
      return {
        error: 'Memory save failed: ' + (error instanceof Error ? error.message : String(error)),
        success: false
      };
    }
  }
});

// 记忆更新工具
export const updateMemoryTool = tool({
  description: 'Update existing memory content. Use this tool when you need to modify previously saved memory information.',
  parameters: z.object({
    memory_id: z.string().describe('ID of the memory to update'),
    content: z.string().describe('New content for the memory'),
    memory_type: z.enum(['user', 'system']).describe('Memory type: user (personal memory) or system (knowledge base)')
  }),
  execute: async ({ memory_id, content, memory_type }) => {
    console.log(`[MemoryTool] Updating ${memory_type} memory: ID ${memory_id}`);

    try {
      // Get memory service instance
      const memoryService = getMemoryService();

      // Determine memory type
      const type = memory_type === 'user' ? MemoryType.USER : MemoryType.SYSTEM;

      try {
        // Update memory
        await memoryService.updateMemory(type, memory_id, content);

        return {
          success: true,
          memory_id: memory_id,
          message: `Successfully updated ${memory_type === 'user' ? 'personal memory' : 'knowledge base'} content`
        };
      } catch (memoryError) {
        console.error('[MemoryTool] Memory update operation failed:', memoryError);
        return {
          error: 'Memory service temporarily unavailable, please try again later',
          success: false,
          details: process.env.NODE_ENV === 'development' ? String(memoryError) : undefined
        };
      }
    } catch (error) {
      console.error('[MemoryTool] Memory update failed:', error);
      return {
        error: 'Memory update failed: ' + (error instanceof Error ? error.message : String(error)),
        success: false
      };
    }
  }
});

// 记忆删除工具
export const deleteMemoryTool = tool({
  description: 'Delete existing memory. Use this tool when a memory is no longer needed or has become outdated.',
  parameters: z.object({
    memory_id: z.string().describe('ID of the memory to delete'),
    memory_type: z.enum(['user', 'system']).describe('Memory type: user (personal memory) or system (knowledge base)')
  }),
  execute: async ({ memory_id, memory_type }) => {
    console.log(`[MemoryTool] Deleting ${memory_type} memory: ID ${memory_id}`);

    try {
      // Get memory service instance
      const memoryService = getMemoryService();

      // Determine memory type
      const type = memory_type === 'user' ? MemoryType.USER : MemoryType.SYSTEM;

      try {
        // Delete memory
        await memoryService.deleteMemory(type, memory_id);

        return {
          success: true,
          memory_id: memory_id,
          message: `Successfully deleted ${memory_type === 'user' ? 'personal memory' : 'knowledge base'} content`
        };
      } catch (memoryError) {
        console.error('[MemoryTool] Memory delete operation failed:', memoryError);
        return {
          error: 'Memory service temporarily unavailable, please try again later',
          success: false,
          details: process.env.NODE_ENV === 'development' ? String(memoryError) : undefined
        };
      }
    } catch (error) {
      console.error('[MemoryTool] Memory delete failed:', error);
      return {
        error: 'Memory deletion failed: ' + (error instanceof Error ? error.message : String(error)),
        success: false
      };
    }
  }
});

// 导出所有记忆工具
export const memoryTools = {
  searchMemoriesTool,
  saveMemoryTool,
  updateMemoryTool,
  deleteMemoryTool
};
