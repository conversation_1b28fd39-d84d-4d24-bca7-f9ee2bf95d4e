import { NextRequest, NextResponse } from 'next/server';
import { getUserIdFromRequest } from '@/utils/authUtils';
import fs from 'fs';
import path from 'path';
import { getChatHistory } from '@/utils/serverChatHistoryUtils';
import { log } from '@/utils/logger';

// GET request handler - Download audio file
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string; fileName: string }> }
) {
  try {
    const resolvedParams = await params;
    const chatId = resolvedParams.id;
    const fileName = resolvedParams.fileName;

    if (!chatId || !fileName) {
      log.warn('Invalid request path', { chatId, fileName }, 'audio-download');
      return NextResponse.json(
        { error: 'Invalid request path' },
        { status: 400 }
      );
    }

    // Get current user ID
    const userId = await getUserIdFromRequest(req);
    const siteId = req.headers.get('X-Site-ID') || '';

    // Use siteId as userId if no userId available
    let finalUserId = userId;
    if (!finalUserId) {
      if (siteId) {
        finalUserId = siteId; 
      } else {
        log.warn('No userId or siteId found', undefined, 'audio-download');
        return NextResponse.json(
          { error: 'User not authenticated or session expired' },
          { status: 401 }
        );
      }
    }

    // Verify chat history ownership
    const chatHistory = await getChatHistory(chatId, finalUserId);
    
    if (!chatHistory) {
      log.warn('Chat history not found', { chatId, userId: finalUserId }, 'audio-download');
      return NextResponse.json(
        { error: 'Chat history not found' },
        { status: 404 }
      );
    }
    
    if (chatHistory.userId && chatHistory.userId !== finalUserId) {
      log.warn('User ID mismatch', { chatHistoryUserId: chatHistory.userId, finalUserId }, 'audio-download');
      return NextResponse.json(
        { error: 'Access denied to this audio file' },
        { status: 403 }
      );
    }

    // Build audio file path
    const basePath = path.join(process.cwd(), 'src', 'chat-history');
    const audioFilePath = path.join(basePath, finalUserId, 'audio', fileName);

    // Check if file exists
    const fileExists = fs.existsSync(audioFilePath);
    
    if (!fileExists) {
      log.warn('Audio file not found', { fileName, chatId, userId: finalUserId }, 'audio-download');
      return NextResponse.json(
        { error: 'Audio file not found' },
        { status: 404 }
      );
    }

    // Read file
    const fileBuffer = await fs.promises.readFile(audioFilePath);
    
    // Set correct Content-Type based on file extension
    let contentType = 'audio/webm';
    if (fileName.endsWith('.wav')) {
      contentType = 'audio/wav';
    } else if (fileName.endsWith('.mp3')) {
      contentType = 'audio/mpeg';
    }

    log.info('Audio file downloaded', { fileName, size: fileBuffer.length, chatId }, 'audio-download');

    // Return audio file
    return new NextResponse(fileBuffer, {
      status: 200,
      headers: {
        'Content-Type': contentType,
        'Content-Length': fileBuffer.length.toString(),
        'Content-Disposition': `inline; filename="${fileName}"`,
        'Cache-Control': 'public, max-age=3600', // Cache for 1 hour
      },
    });
  } catch (error: any) {
    log.error('Failed to get audio file', error, 'audio-download');
    return NextResponse.json(
      { error: 'Failed to get audio file', details: error?.message || String(error) },
      { status: 500 }
    );
  }
} 