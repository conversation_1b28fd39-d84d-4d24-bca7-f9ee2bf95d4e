{"openapi": "3.0.1", "info": {"title": "FMS", "description": "", "version": "1.0.0"}, "paths": {"/fms-platform-order-account/trip-carrier-pay/charge-type": {"get": {"summary": "Get TripCarrierPayChargeType", "deprecated": false, "description": "", "tags": ["TripCarrierPay"], "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Int32FmsSelectListItemListResultDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order-account/trip-carrier-pay/print-display-contract": {"get": {"summary": "If there is a PDF, it will be returned directly to Url, and if there is no, it will be generated", "deprecated": false, "description": "", "tags": ["TripCarrierPay"], "parameters": [{"name": "tripNo", "in": "query", "description": "", "required": false, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FileInfoDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-syncdata/Home/HealthCheck": {"get": {"summary": "AI: Health check endpoint, allowing anonymous access", "deprecated": false, "description": "", "tags": ["Home"], "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "string"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-syncdata/Home/ClearRedisKey": {"get": {"summary": "/fms-platform-syncdata/Home/ClearRedisKey", "deprecated": false, "description": "", "tags": ["Home"], "parameters": [{"name": "key", "in": "query", "description": "", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-syncdata/samples/init-es-local-dispatch": {"get": {"summary": "Repair Local Dispatch es data", "deprecated": false, "description": "", "tags": [], "parameters": [{"name": "taskNo", "in": "query", "description": "", "required": false, "example": "480000035332", "schema": {"type": "string"}}, {"name": "Fms-Token", "in": "header", "description": "", "required": false, "example": "{{FmsToken}}", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-syncdata/test": {"get": {"summary": "/fms-platform-syncdata/test", "deprecated": false, "description": "", "tags": ["Home"], "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-cost-module/basic/ManualInvokeEstTripOrderCostEmployeeDriver": {"get": {"summary": "/fms-platform-cost-module/basic/ManualInvokeEstTripOrderCostEmployeeDriver", "deprecated": false, "description": "", "tags": ["CostModule"], "parameters": [{"name": "tripNo", "in": "query", "description": "250000000234", "required": false, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "boolean"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-cost-module/basic/ManualInvokeEstTripOrderCostBroker": {"get": {"summary": "/fms-platform-cost-module/basic/ManualInvokeEstTripOrderCostBroker", "deprecated": false, "description": "", "tags": ["CostModule"], "parameters": [{"name": "totalCost", "in": "query", "description": "", "required": false, "schema": {"type": "number", "format": "double"}}, {"name": "tripNo", "in": "query", "description": "", "required": false, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "boolean"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-cost-module/basic/ManualInvokeEstTripOrderCostContractorAndAgent": {"get": {"summary": "/fms-platform-cost-module/basic/ManualInvokeEstTripOrderCostContractorAndAgent", "deprecated": false, "description": "", "tags": ["CostModule"], "parameters": [{"name": "tripNo", "in": "query", "description": "", "required": false, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "boolean"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-cost-module/basic/MultiGetOrderCostSummary": {"post": {"summary": "/fms-platform-cost-module/basic/MultiGetOrderCostSummary", "deprecated": false, "description": "", "tags": ["CostModule"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "example": ""}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CostModuleSummaryDto"}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-cost-module/Home/HealthCheck": {"get": {"summary": "AI: Health check endpoint, allowing anonymous access", "deprecated": false, "description": "", "tags": ["Home"], "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "string"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-cost-module/HistoryDataImport/GetErrorActualCostTripId": {"get": {"summary": "Output import failed TMSTripId", "deprecated": false, "description": "", "tags": ["HistoryDataImport"], "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dockapp/DispatchToDockApp/OperateTaskToDock": {"get": {"summary": "AI: Operation task is sent to WMS", "deprecated": false, "description": "", "tags": ["DispatchToDockApp"], "parameters": [{"name": "TripNo", "in": "query", "description": "Trip number", "required": false, "schema": {"type": "integer", "format": "int64"}}, {"name": "TaskNo", "in": "query", "description": "Task number", "required": false, "schema": {"type": "integer", "format": "int64"}}, {"name": "OperateType", "in": "query", "description": "Operation Type", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dockapp/DispatchToDockApp/OperateTaskToDockNew": {"get": {"summary": "AI: Operation task is sent to WMS", "deprecated": false, "description": "", "tags": ["DispatchToDockApp"], "parameters": [{"name": "TripNo", "in": "query", "description": "Trip number", "required": false, "schema": {"type": "integer", "format": "int64"}}, {"name": "TaskNo", "in": "query", "description": "Task number", "required": false, "schema": {"type": "integer", "format": "int64"}}, {"name": "OperateType", "in": "query", "description": "Operation Type", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dockapp/DispatchToDockApp/OperateTripToDock": {"get": {"summary": "AI: Operation task is sent to WMS", "deprecated": false, "description": "", "tags": ["DispatchToDockApp"], "parameters": [{"name": "TripNo", "in": "query", "description": "", "required": false, "schema": {"type": "integer", "format": "int64"}}, {"name": "OperateType", "in": "query", "description": "", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dockapp/DispatchToDockApp/OperateLineHaulTaskDstTerminalToDock": {"get": {"summary": "AI: Update linehaul task dst terminal", "deprecated": false, "description": "", "tags": ["DispatchToDockApp"], "parameters": [{"name": "tripNo", "in": "query", "description": "", "required": false, "schema": {"type": "integer", "format": "int64"}}, {"name": "taskNo", "in": "query", "description": "", "required": false, "schema": {"type": "integer", "format": "int64"}}, {"name": "oldDstTerminal", "in": "query", "description": "", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dockapp/DispatchToDockApp/OperateDoNoToDock": {"get": {"summary": "Operate DO data to WMS", "deprecated": false, "description": "", "tags": ["DispatchToDockApp"], "parameters": [{"name": "DoNo", "in": "query", "description": "", "required": false, "schema": {"type": "string"}}, {"name": "OperateType", "in": "query", "description": "", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dockapp/DispatchToDockApp/AutoPushDoInventoryToDock": {"get": {"summary": "Automatically push all DO data below Trip", "deprecated": false, "description": "", "tags": ["DispatchToDockApp"], "parameters": [{"name": "TripNo", "in": "query", "description": "", "required": false, "schema": {"type": "integer", "format": "int64"}}, {"name": "OperateType", "in": "query", "description": "", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dockapp/Home/HealthCheck": {"get": {"summary": "AI: Health check endpoint, allowing anonymous access", "deprecated": false, "description": "", "tags": ["Home"], "parameters": [], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "string"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dockapp/Home/Ding": {"get": {"summary": "AI: Simple echo endpoint that allows anonymous access", "deprecated": false, "description": "", "tags": ["Home"], "parameters": [{"name": "dong", "in": "query", "description": "Enter string", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "string"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}}, "components": {"schemas": {"CostModuleSummaryDto": {"type": "object", "properties": {"primary_key": {"type": "string", "nullable": true}, "company_code": {"type": "string", "nullable": true}, "trip_no": {"type": "integer", "format": "int64"}, "order_id": {"type": "integer", "format": "int64"}, "task_no": {"type": "integer", "format": "int64"}, "task_type": {"type": "integer", "format": "int32"}, "task_type_desc": {"type": "string", "nullable": true}, "trip_org_terminal": {"type": "string", "nullable": true}, "trip_dst_terminal": {"type": "string", "nullable": true}, "trip_date": {"type": "string", "format": "date-time"}, "trip_complete_date": {"type": "string", "format": "date-time"}, "payment_reviewed_date": {"type": "string", "format": "date-time"}, "payment_approved_date": {"type": "string", "format": "date-time"}, "carrier_code": {"type": "string", "nullable": true}, "carrier_name": {"type": "string", "nullable": true}, "carrier_type": {"type": "integer", "format": "int32"}, "carrier_type_desc": {"type": "string", "nullable": true}, "driver_code": {"type": "string", "nullable": true}, "driver_type": {"type": "integer", "format": "int32"}, "driver_type_desc": {"type": "string", "nullable": true}, "driver_name": {"type": "string", "nullable": true}, "trip_status": {"type": "integer", "format": "int32"}, "trip_status_desc": {"type": "string", "nullable": true}, "task_status": {"type": "integer", "format": "int32"}, "task_status_desc": {"type": "string", "nullable": true}, "work_hours": {"type": "number", "format": "double"}, "mileage": {"type": "number", "format": "double"}, "order_total_revenue": {"type": "number", "format": "double"}, "task_allocated_revenue": {"type": "number", "format": "double"}, "trip_estimate_total_cost": {"type": "number", "format": "double"}, "task_estimate_allocated_cost": {"type": "number", "format": "double"}, "trip_actual_total_cost": {"type": "number", "format": "double"}, "task_actual_allocated_cost": {"type": "number", "format": "double"}, "task_org_terminal": {"type": "string", "nullable": true}, "task_dst_terminal": {"type": "string", "nullable": true}, "cost_terminal": {"type": "string", "nullable": true}, "upload_time": {"type": "string", "format": "date-time"}, "is_del": {"type": "integer", "format": "int32"}, "shipper_zip": {"type": "string", "nullable": true}, "shipper_terminal": {"type": "string", "nullable": true}, "shipper_state": {"type": "string", "nullable": true}, "consignee_zip": {"type": "string", "nullable": true}, "consignee_terminal": {"type": "string", "nullable": true}, "consignee_state": {"type": "string", "nullable": true}, "service_terminal": {"type": "string", "nullable": true}, "un_rated": {"type": "string", "nullable": true}, "task_total_cost": {"type": "number", "format": "double"}, "pallet": {"type": "integer", "format": "int32"}, "weight": {"type": "number", "format": "double"}, "pickup_complete_date": {"type": "string", "format": "date-time"}, "invoice_date": {"type": "string", "format": "date-time"}, "invoice_ar_locked_date": {"type": "string", "format": "date-time"}, "invoice_posted_date": {"type": "string", "format": "date-time"}, "pro_no": {"type": "string", "nullable": true}, "bill_to_name": {"type": "string", "nullable": true}, "bill_to_code": {"type": "string", "nullable": true}, "order_status": {"type": "string", "nullable": true}}, "additionalProperties": false}, "FileInfoDto": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "url": {"type": "string", "nullable": true}, "size": {"type": "integer", "format": "int64"}, "source_url": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Int32FmsSelectListItem": {"type": "object", "properties": {"text": {"type": "string", "nullable": true}, "value": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "Int32FmsSelectListItemListResultDto": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/Int32FmsSelectListItem"}, "nullable": true}}, "additionalProperties": false}}}}