'use client';

import { useEffect, useState } from 'react';
import { usePathname, useRouter } from 'next/navigation';
import { useAuth } from '@/app/contexts/AuthContext';
import { hasPermission, getUserRolesByUserId, getUserRolesByIamRoles, UserRole, PROTECTED_ROUTES } from '@/utils/permissionConfig';

export const usePermission = () => {
  const { user, tokenData, isAuthenticated, isLoading } = useAuth();
  const pathname = usePathname();
  const router = useRouter();
  const [userRoles, setUserRoles] = useState<UserRole[]>([]);
  const [isAuthorized, setIsAuthorized] = useState<boolean | null>(null);
  const [isChecking, setIsChecking] = useState(true);

  // 添加一个额外的useEffect来监听isAuthenticated和isLoading的变化
  useEffect(() => {
    console.log('DEBUG-PERMISSION-AUTH: 认证状态变化', { isAuthenticated, isLoading });
  }, [isAuthenticated, isLoading]);

  useEffect(() => {
    const checkPermission = async () => {
      console.log('DEBUG-PERMISSION: 开始检查权限', { isAuthenticated, isLoading, pathname });

      // 如果认证状态正在加载中，则不进行权限检查
      if (isLoading) {
        console.log('DEBUG-PERMISSION: 认证状态正在加载中，不进行权限检查');
        setIsChecking(true);
        return;
      }

      // 设置检查状态为 true
      setIsChecking(true);
      console.log('DEBUG-PERMISSION: 设置 isChecking = true');

      // 如果用户未登录，则没有权限
      if (!isAuthenticated) {
        console.log('DEBUG-PERMISSION: 用户未登录');
        setUserRoles([]);
        console.log('DEBUG-PERMISSION: 设置 userRoles = []');
        // 设置授权状态为 false
        setIsAuthorized(false);
        console.log('DEBUG-PERMISSION: 设置 isAuthorized = false');
        // 延迟设置检查完成状态，避免闪烁
        setTimeout(() => {
          console.log('DEBUG-PERMISSION: 延迟设置 isChecking = false (未登录)');
          setIsChecking(false);
        }, 300);
        return;
      }

      // 尝试从 tokenData 中获取角色信息
      let roles: UserRole[] = [];

      // 添加调试日志
      console.log('Debug - tokenData:', tokenData);
      console.log('Debug - user:', user);

      // 尝试所有可能的方式获取角色
      let allPossibleRoles: string[] = [];

      // 1. 从 tokenData.data.role_ids 获取
      if (tokenData?.data?.role_ids) {
        console.log('Debug - Found roles in tokenData.data.role_ids:', tokenData.data.role_ids);
        allPossibleRoles = [...allPossibleRoles, ...tokenData.data.role_ids];
        roles = getUserRolesByIamRoles(tokenData.data.role_ids);
      }

      // 2. 从 user.userRoles 获取
      if (user && 'userRoles' in user && Array.isArray((user as any).userRoles)) {
        const iamRoles = (user as any).userRoles.map((role: any) => role.name || role);
        console.log('Debug - Found roles in user.userRoles:', iamRoles);
        allPossibleRoles = [...allPossibleRoles, ...iamRoles];

        // 如果之前没有找到角色，使用这个来源的角色
        if (roles.length <= 1) { // 只有 USER 角色或没有角色
          roles = getUserRolesByIamRoles(iamRoles);
        }
      }

      // 3. 从 localStorage 获取
      try {
        if (typeof window !== 'undefined') {
          // 从 wms_user_info 获取
          const wmsUserInfoStr = localStorage.getItem('wms_user_info');
          if (wmsUserInfoStr) {
            const wmsUserInfo = JSON.parse(wmsUserInfoStr);
            if (wmsUserInfo && wmsUserInfo.roles && Array.isArray(wmsUserInfo.roles)) {
              console.log('Debug - Found roles in localStorage wms_user_info:', wmsUserInfo.roles);
              allPossibleRoles = [...allPossibleRoles, ...wmsUserInfo.roles];
            }
          }

          // 从 iam_user_info 获取
          const userInfoStr = localStorage.getItem('iam_user_info');
          if (userInfoStr) {
            const userInfo = JSON.parse(userInfoStr);
            if (userInfo && userInfo.userRoles && Array.isArray(userInfo.userRoles)) {
              const localRoles = userInfo.userRoles.map((role: any) => role.name || role);
              console.log('Debug - Found roles in localStorage iam_user_info:', localRoles);
              allPossibleRoles = [...allPossibleRoles, ...localRoles];
            }
          }

          // 从 token 获取
          const tokenStr = localStorage.getItem('iam_access_token');
          if (tokenStr) {
            try {
              const payload = JSON.parse(atob(tokenStr.split('.')[1]));
              if (payload && payload.data && payload.data.role_ids && Array.isArray(payload.data.role_ids)) {
                console.log('Debug - Found roles in localStorage token:', payload.data.role_ids);
                allPossibleRoles = [...allPossibleRoles, ...payload.data.role_ids];
              }
            } catch (e) {
              console.error('Debug - Error parsing token:', e);
            }
          }
        }
      } catch (error) {
        console.error('Debug - Error accessing localStorage:', error);
      }

      // 如果仍然没有找到角色，使用 userId 方法
      if (roles.length <= 1) { // 只有 USER 角色或没有角色
        const userId = tokenData?.sub ||
                      tokenData?.data?.user_id ||
                      (user?.id ? String(user.id) : null);
        console.log('Debug - Using userId method with userId:', userId);
        roles = getUserRolesByUserId(userId);
      }

      // 直接添加 Admin 角色进行测试
      if (allPossibleRoles.includes('Admin')) {
        console.log('Debug - Found "Admin" role, ensuring ADMIN role is added');
        if (!roles.includes(UserRole.ADMIN)) {
          roles.push(UserRole.ADMIN);
        }
      }

      console.log('Debug - Final roles:', roles);
      console.log('Debug - Current pathname:', pathname);
      console.log('Debug - Protected routes:', PROTECTED_ROUTES);

      setUserRoles(roles);

      // 检查用户是否有权限访问当前路径
      const authorized = hasPermission(roles, pathname || '');

      console.log('DEBUG-PERMISSION: 权限检查结果', { authorized, roles, pathname });

      // 先设置授权状态
      setIsAuthorized(authorized);
      console.log('DEBUG-PERMISSION: 设置 isAuthorized =', authorized);

      // 添加一个较长的延迟，确保状态更新顺序正确
      // 这样可以避免在权限检查过程中闪烁显示未授权页面
      setTimeout(() => {
        console.log('DEBUG-PERMISSION: 延迟设置 isChecking = false (已登录)');
        setIsChecking(false);
      }, 300);
    };

    checkPermission();
  }, [isAuthenticated, isLoading, pathname, tokenData, user]);

  // 重定向到未授权页面
  const redirectToUnauthorized = () => {
    router.push('/unauthorized');
  };

  // 检查特定角色
  const hasRole = (role: UserRole): boolean => {
    return userRoles.includes(role);
  };

  return {
    isAuthorized,
    isChecking,
    userRoles,
    hasRole,
    redirectToUnauthorized
  };
};

export default usePermission;
