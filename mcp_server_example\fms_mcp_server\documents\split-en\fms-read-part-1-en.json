{"openapi": "3.0.1", "info": {"title": "FMS", "description": "", "version": "1.0.0"}, "paths": {"/fms-platform-order/Home/HealthCheck": {"get": {"summary": "AI: Health check endpoint, allowing anonymous access", "deprecated": false, "description": "", "tags": ["Home"], "parameters": [], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "string"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order/Home/OrderList": {"get": {"summary": "example Get the order list", "deprecated": false, "description": "", "tags": ["Home"], "parameters": [], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/FMSOrderListItemDto"}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order/order-charge/acclist": {"post": {"summary": "Internally, Create Shipment Order page to query the optional Acc list", "deprecated": false, "description": "", "tags": ["OrderCharge"], "parameters": [{"name": "businessClient", "in": "query", "description": "Business Customers", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/RpcAccService"}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order/order-charge/acc-quick-selection-list": {"post": {"summary": "Internally, Create Shipment Order page, Shipment Order details page, ShipperAndConsignee module, you can quickly select the added Acc list", "deprecated": false, "description": "", "tags": ["OrderCharge"], "parameters": [{"name": "businessClient", "in": "query", "description": "Business Customers", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/RpcAccServiceShipperAndConsigneeQuickSelectionDto"}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order/order-reception/receive/oms": {"get": {"summary": "/fms-platform-order/order-reception/receive/oms", "deprecated": false, "description": "", "tags": ["OrderReception"], "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "boolean"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order/OrderReception/NewFmsOrderPushKafka": {"get": {"summary": "/fms-platform-order/OrderReception/NewFmsOrderPushKafka", "deprecated": false, "description": "", "tags": ["OrderReception"], "parameters": [{"name": "orderNo", "in": "query", "description": "", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order/shipment-orders/generate-pro-number": {"get": {"summary": "Generate Pro#\r\nInside", "deprecated": false, "description": "", "tags": ["OrderReception"], "parameters": [], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "string"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order/order-reception/check": {"get": {"summary": "/fms-platform-order/order-reception/check", "deprecated": false, "description": "", "tags": ["OrderReception"], "parameters": [{"name": "orderNo", "in": "query", "description": "", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order/OrderReception/ExisitsRedisKey": {"get": {"summary": "/fms-platform-order/OrderReception/ExisitsRedisKey", "deprecated": false, "description": "", "tags": ["OrderReception"], "parameters": [{"name": "orderno", "in": "query", "description": "", "required": false, "schema": {"type": "string"}}, {"name": "key", "in": "query", "description": "", "required": false, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "boolean"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/api/samples/customer-by-code": {"get": {"summary": "/api/samples/customer-by-code", "deprecated": false, "description": "", "tags": ["<PERSON><PERSON>"], "parameters": [], "responses": {"200": {"description": "Success", "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/api/samples/customer-by-name": {"get": {"summary": "/api/samples/customer-by-name", "deprecated": false, "description": "", "tags": ["<PERSON><PERSON>"], "parameters": [], "responses": {"200": {"description": "Success", "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/api/samples/revenuecode": {"get": {"summary": "Generate RevenueCode", "deprecated": false, "description": "", "tags": ["<PERSON><PERSON>"], "parameters": [{"name": "no", "in": "query", "description": "", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "string"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/api/samples/properties": {"get": {"summary": "Get all attribute names of ShipmentOrderBillEntity class", "deprecated": false, "description": "", "tags": ["<PERSON><PERSON>"], "parameters": [], "responses": {"200": {"description": "Success", "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order/consignee/{shipmentOrderNo}": {"get": {"summary": "internal\r\nQuery Consignee Info", "deprecated": false, "description": "", "tags": ["ShipmentOrderConsignee"], "parameters": [{"name": "shipmentOrderNo", "in": "path", "description": "Order number", "required": true, "example": "", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ShipmentOrderConsigneeDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order/consignee/appointment/shipmentOrderNo={shipmentOrderNo}&taskNo={taskNo}": {"get": {"summary": "internal\r\nView consignee-Appointment info", "deprecated": false, "description": "", "tags": ["ShipmentOrderConsignee"], "parameters": [{"name": "shipmentOrderNo", "in": "path", "description": "Order number", "required": true, "example": "", "schema": {"type": "string"}}, {"name": "taskNo", "in": "path", "description": "Mission number", "required": true, "example": 0, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ShipmentOrderConsigneeAppointmentDtoNew"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order/consignee/getconsignee-detail/{consigneeId}": {"get": {"summary": "internal\r\nQuery Consignee details (modify page data)", "deprecated": false, "description": "", "tags": ["ShipmentOrderConsignee"], "parameters": [{"name": "consigneeId", "in": "path", "description": "Consignee ID", "required": true, "example": 0, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ShipmentOrderConsigneeDetailDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order/shipment-order-data-provide/order-full-data": {"get": {"summary": "internal\r\nGet the Packages information corresponding to ShipmentOrderNo", "deprecated": false, "description": "", "tags": ["ShipmentOrderDataProvide"], "parameters": [{"name": "orderNo", "in": "query", "description": "Order number", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RpcShipmentOrderDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order/shipment-order-data-provide/query-packagestatus/{packageNo}": {"get": {"summary": "Query the package status", "deprecated": false, "description": "", "tags": ["ShipmentOrderDataProvide"], "parameters": [{"name": "packageNo", "in": "path", "description": "", "required": true, "example": "", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/QueryPackageStatusDto"}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order/shipment-order-data-provide/order-full-data/order/no": {"get": {"summary": "internal\r\nGet the Packages information corresponding to ShipmentOrderNo (tracking_no, pro_no, invoice_pro)", "deprecated": false, "description": "", "tags": ["ShipmentOrderDataProvide"], "parameters": [{"name": "tracking_no", "in": "query", "description": "", "required": false, "schema": {"type": "string", "default": ""}}, {"name": "pro_no", "in": "query", "description": "", "required": false, "schema": {"type": "string", "default": ""}}, {"name": "invoice_pro", "in": "query", "description": "", "required": false, "schema": {"type": "string", "default": ""}}, {"name": "tms_order_id", "in": "query", "description": "", "required": false, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/RpcShipmentOrderDto"}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order/shipment-order-data-provide/order-full-data/package-pallet/no": {"get": {"summary": "internal\r\nGet the Packages information corresponding to ShipmentOrderNo (package_no, pallet_no)", "deprecated": false, "description": "", "tags": ["ShipmentOrderDataProvide"], "parameters": [{"name": "package_no", "in": "query", "description": "", "required": false, "schema": {"type": "string", "default": ""}}, {"name": "pallet_no", "in": "query", "description": "", "required": false, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RpcShipmentOrderDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}}, "components": {"schemas": {"AppointmentCarrierInfo": {"type": "object", "properties": {"contractor_or_carrier_name": {"type": "string", "nullable": true}, "contractor_or_carrier_code": {"type": "string", "nullable": true}}, "additionalProperties": false}, "FMSOrderListItemDto": {"type": "object", "properties": {"order_number": {"type": "string", "nullable": true}, "total_price": {"type": "number", "format": "double"}, "user_id": {"type": "integer", "format": "int64"}}, "additionalProperties": false}, "RpcOrderEstimateFreightDto": {"type": "object", "properties": {"code": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "quantity": {"type": "integer", "format": "int32"}, "quantity_uom": {"type": "string", "nullable": true}, "weight": {"type": "number", "format": "double"}, "weight_uom": {"type": "string", "nullable": true}, "volume": {"type": "number", "format": "double"}, "volume_uom": {"type": "string", "nullable": true}, "linear": {"type": "number", "format": "double"}, "linear_uom": {"type": "string", "nullable": true}, "length": {"type": "number", "format": "double"}, "width": {"type": "number", "format": "double"}, "height": {"type": "number", "format": "double"}, "space": {"type": "number", "format": "double"}, "stackable": {"type": "integer", "format": "int32"}, "freight_class": {"type": "number", "format": "double"}, "nmfc": {"type": "string", "nullable": true}, "declared_value": {"type": "number", "format": "double"}, "unit_price": {"type": "number", "format": "double"}, "customer_pro": {"type": "string", "nullable": true}, "inner_pack_qty": {"type": "integer", "format": "int32"}, "inner_pack_uom": {"type": "string", "nullable": true}, "reference": {"type": "string", "nullable": true}, "reference2": {"type": "string", "nullable": true}, "reference3": {"type": "string", "nullable": true}, "reference4": {"type": "string", "nullable": true}, "reference5": {"type": "string", "nullable": true}, "load_no": {"type": "string", "nullable": true}, "po_no": {"type": "string", "nullable": true}, "bol": {"type": "string", "nullable": true}, "bol_no": {"type": "string", "nullable": true}}, "additionalProperties": false}, "RpcAccServiceShipperAndConsigneeQuickSelectionDto": {"type": "object", "properties": {"module_name": {"type": "string", "nullable": true}, "sort_num": {"type": "integer", "format": "int32"}, "id": {"type": "integer", "format": "int32"}, "acc_code": {"type": "string", "nullable": true}, "quantity": {"type": "integer", "format": "int32"}, "description": {"type": "string", "nullable": true}, "uom": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ShipmentOrderCreateFromEnum": {"enum": [1, 2, 3], "type": "integer", "format": "int32"}, "ShipmentOrderSourceTypeEnum": {"enum": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], "type": "integer", "format": "int32"}, "ShipmentTypeEnum": {"enum": [1, 2, 3, 4], "type": "integer", "format": "int32"}, "QueryPackageStatusDto": {"type": "object", "properties": {"status": {"$ref": "#/components/schemas/ShipmentOrderStatusEnum"}, "sub_status": {"$ref": "#/components/schemas/ShipmentOrderSubStatusEnum"}, "status_desc": {"$ref": "#/components/schemas/ShipmentOrderStatusEnum"}, "sub_status_desc": {"$ref": "#/components/schemas/ShipmentOrderSubStatusEnum"}, "package_no": {"type": "string", "nullable": true}, "tracking_no": {"type": "string", "nullable": true}, "pallet_no": {"type": "string", "nullable": true}, "shipment_type": {"$ref": "#/components/schemas/ShipmentTypeEnum"}, "event": {"type": "string", "nullable": true}, "title": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "address1": {"type": "string", "nullable": true}, "address2": {"type": "string", "nullable": true}, "city": {"type": "string", "nullable": true}, "state": {"type": "string", "nullable": true}, "country": {"type": "string", "nullable": true}, "zipcode": {"type": "string", "nullable": true}, "longitude": {"type": "number", "format": "double"}, "latitude": {"type": "number", "format": "double"}, "driver": {"type": "string", "nullable": true}, "carrier": {"type": "string", "nullable": true}, "actual_arrive_time": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "RpcAccService": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "acc_code": {"type": "string", "nullable": true}, "quantity": {"type": "integer", "format": "int32"}, "description": {"type": "string", "nullable": true}, "uom": {"type": "string", "nullable": true}}, "additionalProperties": false}, "RpcBillingDto": {"type": "object", "properties": {"freight_term": {"type": "string", "nullable": true}, "bill_to_account": {"type": "string", "nullable": true}, "bill_to_name": {"type": "string", "nullable": true}, "quote_id": {"type": "string", "nullable": true}, "quote_amount": {"type": "number", "format": "double", "nullable": true}, "address1": {"type": "string", "nullable": true}, "address2": {"type": "string", "nullable": true}, "country": {"type": "string", "nullable": true}, "state": {"type": "string", "nullable": true}, "city": {"type": "string", "nullable": true}, "zip_code": {"type": "string", "nullable": true}, "location_id": {"type": "integer", "format": "int64"}, "location_name": {"type": "string", "nullable": true}, "contact": {"type": "string", "nullable": true}, "phone": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "latitude": {"type": "number", "format": "double"}, "longitude": {"type": "number", "format": "double"}, "open_time": {"type": "string", "nullable": true}, "close_time": {"type": "string", "nullable": true}, "location_type": {"type": "integer", "format": "int32"}, "order_key": {"type": "integer", "format": "int64"}, "location_code": {"type": "string", "nullable": true}}, "additionalProperties": false}, "RpcOrderConsigneeAddress": {"type": "object", "properties": {"mabd": {"type": "string", "format": "date-time"}, "delivery_max_times": {"type": "integer", "format": "int32"}, "desired_delivery_date": {"type": "string", "format": "date-time"}, "pod_name": {"type": "string", "nullable": true}, "deliverd_by": {"type": "string", "nullable": true}, "deliverd_name": {"type": "string", "nullable": true}, "deliverd_time": {"type": "string", "format": "date-time"}, "deliverd_location": {"type": "string", "nullable": true}, "terminal": {"type": "string", "nullable": true}, "terminal_address1": {"type": "string", "nullable": true}, "terminal_address2": {"type": "string", "nullable": true}, "terminal_city": {"type": "string", "nullable": true}, "terminal_state": {"type": "string", "nullable": true}, "terminal_zipcode": {"type": "string", "nullable": true}, "terminal_lat": {"type": "number", "format": "double"}, "terminal_lng": {"type": "number", "format": "double"}, "polygon_id": {"type": "string", "nullable": true}, "polygon_code": {"type": "string", "nullable": true}, "note": {"type": "string", "nullable": true}, "appointment_date": {"type": "string", "format": "date-time"}, "appointment_time_from": {"type": "string", "format": "date-time"}, "appointment_time_to": {"type": "string", "format": "date-time"}, "requre_appt": {"type": "integer", "format": "int32"}, "appointment_no": {"type": "string", "nullable": true}, "puZoneCode": {"type": "string", "nullable": true}, "address1": {"type": "string", "nullable": true}, "address2": {"type": "string", "nullable": true}, "country": {"type": "string", "nullable": true}, "state": {"type": "string", "nullable": true}, "city": {"type": "string", "nullable": true}, "zip_code": {"type": "string", "nullable": true}, "location_id": {"type": "integer", "format": "int64"}, "location_name": {"type": "string", "nullable": true}, "contact": {"type": "string", "nullable": true}, "phone": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "latitude": {"type": "number", "format": "double"}, "longitude": {"type": "number", "format": "double"}, "open_time": {"type": "string", "nullable": true}, "close_time": {"type": "string", "nullable": true}, "location_type": {"type": "integer", "format": "int32"}, "order_key": {"type": "integer", "format": "int64"}, "location_code": {"type": "string", "nullable": true}}, "additionalProperties": false}, "RpcOrderItem": {"type": "object", "properties": {"package_id": {"type": "integer", "format": "int64"}, "code": {"type": "string", "nullable": true}, "customer_pro": {"type": "string", "nullable": true}, "declared_value": {"type": "number", "format": "double"}, "description": {"type": "string", "nullable": true}, "freight_class": {"type": "number", "format": "double"}, "height": {"type": "number", "format": "double"}, "length": {"type": "number", "format": "double"}, "nmfc": {"type": "string", "nullable": true}, "package_no": {"type": "string", "nullable": true}, "quantity": {"type": "integer", "format": "int32"}, "quantity_uom": {"type": "string", "nullable": true}, "shipment_order_no": {"type": "string", "nullable": true}, "linear_uom": {"type": "string", "nullable": true}, "stackable": {"type": "integer", "format": "int32"}, "volume": {"type": "number", "format": "double"}, "volume_uom": {"type": "string", "nullable": true}, "weight": {"type": "number", "format": "double"}, "weight_uom": {"type": "string", "nullable": true}, "width": {"type": "number", "format": "double"}, "sku": {"type": "string", "nullable": true}, "lot_no": {"type": "string", "nullable": true}}, "additionalProperties": false}, "RpcOrderPackages": {"type": "object", "properties": {"package_id": {"type": "integer", "format": "int64"}, "sequence": {"type": "integer", "format": "int32"}, "package_no": {"type": "string", "nullable": true}, "tracking_no": {"type": "string", "nullable": true}, "length": {"type": "number", "format": "double"}, "width": {"type": "number", "format": "double"}, "height": {"type": "number", "format": "double"}, "weight": {"type": "number", "format": "double"}, "weight_uom": {"type": "string", "nullable": true}, "freight_class": {"type": "number", "format": "double"}, "volume": {"type": "number", "format": "double"}, "volume_uom": {"type": "string", "nullable": true}, "status": {"type": "integer", "format": "int32"}, "stackable": {"type": "integer", "format": "int32"}, "inner_count": {"type": "integer", "format": "int32"}, "is_over_size": {"type": "integer", "format": "int32"}, "is_over_load": {"type": "integer", "format": "int32"}, "linear": {"type": "number", "format": "double"}, "linear_uom": {"type": "string", "nullable": true}, "item_count": {"type": "integer", "format": "int32"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/RpcOrderItem"}, "nullable": true}}, "additionalProperties": false}, "RpcOrderPallets": {"type": "object", "properties": {"pallet_id": {"type": "integer", "format": "int64"}, "sequence": {"type": "integer", "format": "int32"}, "pallets_no": {"type": "string", "nullable": true}, "tracking_no": {"type": "string", "nullable": true}, "length": {"type": "number", "format": "double"}, "width": {"type": "number", "format": "double"}, "height": {"type": "number", "format": "double"}, "weight": {"type": "number", "format": "double"}, "weight_uom": {"type": "string", "nullable": true}, "freight_class": {"type": "number", "format": "double"}, "volume": {"type": "number", "format": "double"}, "volume_uom": {"type": "string", "nullable": true}, "status": {"type": "integer", "format": "int32"}, "stackable": {"type": "integer", "format": "int32"}, "inner_count": {"type": "integer", "format": "int32"}, "is_over_size": {"type": "integer", "format": "int32"}, "is_over_load": {"type": "integer", "format": "int32"}, "linear": {"type": "number", "format": "double"}, "linear_uom": {"type": "string", "nullable": true}, "item_count": {"type": "integer", "format": "int32"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/RpcOrderItem"}, "nullable": true}, "dock_location_name": {"type": "string", "nullable": true}}, "additionalProperties": false}, "RpcOrderShipperAddress": {"type": "object", "properties": {"request_pickup_date": {"type": "string", "format": "date-time"}, "request_pickup_time_begin": {"type": "string", "format": "date-time"}, "request_pickup_time_end": {"type": "string", "format": "date-time"}, "terminal": {"type": "string", "nullable": true}, "terminal_address1": {"type": "string", "nullable": true}, "terminal_address2": {"type": "string", "nullable": true}, "terminal_city": {"type": "string", "nullable": true}, "terminal_state": {"type": "string", "nullable": true}, "terminal_zipcode": {"type": "string", "nullable": true}, "terminal_lat": {"type": "number", "format": "double"}, "terminal_lng": {"type": "number", "format": "double"}, "polygon_id": {"type": "string", "nullable": true}, "polygon_code": {"type": "string", "nullable": true}, "note": {"type": "string", "nullable": true}, "appointment_date": {"type": "string", "format": "date-time"}, "appointment_time_from": {"type": "string", "format": "date-time"}, "appointment_time_to": {"type": "string", "format": "date-time"}, "appointment_no": {"type": "string", "nullable": true}, "puZoneCode": {"type": "string", "nullable": true}, "address1": {"type": "string", "nullable": true}, "address2": {"type": "string", "nullable": true}, "country": {"type": "string", "nullable": true}, "state": {"type": "string", "nullable": true}, "city": {"type": "string", "nullable": true}, "zip_code": {"type": "string", "nullable": true}, "location_id": {"type": "integer", "format": "int64"}, "location_name": {"type": "string", "nullable": true}, "contact": {"type": "string", "nullable": true}, "phone": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "latitude": {"type": "number", "format": "double"}, "longitude": {"type": "number", "format": "double"}, "open_time": {"type": "string", "nullable": true}, "close_time": {"type": "string", "nullable": true}, "location_type": {"type": "integer", "format": "int32"}, "order_key": {"type": "integer", "format": "int64"}, "location_code": {"type": "string", "nullable": true}}, "additionalProperties": false}, "RpcShipmentOrderDto": {"type": "object", "properties": {"shipment_order_id": {"type": "integer", "format": "int64"}, "shipment_order_no": {"type": "string", "nullable": true}, "order_no": {"type": "string", "nullable": true}, "note": {"type": "string", "nullable": true}, "order_status": {"type": "integer", "format": "int32"}, "order_sub_status": {"type": "integer", "format": "int32"}, "business_client": {"type": "string", "nullable": true}, "facility_id": {"type": "string", "nullable": true}, "master_order_no": {"type": "string", "nullable": true}, "company_id": {"type": "integer", "format": "int32"}, "company_code": {"type": "string", "nullable": true}, "shipments_type": {"type": "integer", "format": "int32"}, "shipment_type": {"type": "integer", "format": "int32"}, "service_level": {"type": "integer", "format": "int32"}, "shipper_terminal": {"type": "string", "nullable": true}, "consignee_terminal": {"type": "string", "nullable": true}, "org_terminal": {"type": "string", "nullable": true}, "dest_terminal": {"type": "string", "nullable": true}, "package_quantity": {"type": "integer", "format": "int32"}, "acc_service_list": {"type": "array", "items": {"$ref": "#/components/schemas/RpcAccService"}, "nullable": true}, "shipper_address": {"$ref": "#/components/schemas/RpcOrderShipperAddress"}, "consignee_address": {"$ref": "#/components/schemas/RpcOrderConsigneeAddress"}, "is_need_delivery": {"type": "boolean"}, "is_need_pickup": {"type": "boolean"}, "is_need_linehaul": {"type": "boolean"}, "is_need_service": {"type": "boolean"}, "billing_to": {"$ref": "#/components/schemas/RpcBillingDto"}, "packages": {"type": "array", "items": {"$ref": "#/components/schemas/RpcOrderPackages"}, "nullable": true}, "pallets": {"type": "array", "items": {"$ref": "#/components/schemas/RpcOrderPallets"}, "nullable": true}, "bol": {"type": "string", "nullable": true}, "bol_no": {"type": "string", "nullable": true}, "load_no": {"type": "string", "nullable": true}, "load_date": {"type": "string", "format": "date-time"}, "post_date": {"type": "string", "format": "date-time"}, "rate": {"type": "number", "format": "double"}, "dpls": {"type": "integer", "format": "int32"}, "po_no": {"type": "string", "nullable": true}, "rma": {"type": "string", "nullable": true}, "insurance_price": {"type": "number", "format": "double"}, "current_trip": {"type": "string", "nullable": true}, "next_trip": {"type": "string", "nullable": true}, "current_transit_org_terminal": {"type": "string", "nullable": true}, "current_transit_dest_terminal": {"type": "string", "nullable": true}, "tracking_no": {"type": "string", "nullable": true}, "dispatched_order_no": {"type": "string", "nullable": true}, "freight_term": {"type": "string", "nullable": true}, "create_from": {"$ref": "#/components/schemas/ShipmentOrderCreateFromEnum"}, "actual_create_time": {"type": "string", "format": "date-time"}, "service_terminal": {"type": "string", "nullable": true}, "is_hold": {"type": "integer", "format": "int32"}, "foreign_master_order_id": {"type": "string", "nullable": true}, "reference": {"type": "string", "nullable": true}, "reference2": {"type": "string", "nullable": true}, "pro_no": {"type": "string", "nullable": true}, "current_location": {"type": "string", "nullable": true}, "invoice_pro": {"type": "string", "nullable": true}, "invoice_pro_prefix": {"type": "string", "nullable": true}, "estimate_freights": {"type": "array", "items": {"$ref": "#/components/schemas/RpcOrderEstimateFreightDto"}, "nullable": true}, "total_weight": {"type": "number", "format": "double"}, "total_volume": {"type": "number", "format": "double"}, "source": {"$ref": "#/components/schemas/ShipmentOrderSourceTypeEnum"}, "reference3": {"type": "string", "nullable": true}, "reference4": {"type": "string", "nullable": true}, "reference5": {"type": "string", "nullable": true}, "reference6": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ShipmentOrderConsigneeAppointmentDtoNew": {"type": "object", "properties": {"desire_delivery_date": {"type": "string", "format": "date-time", "nullable": true}, "tracking_no": {"type": "string", "nullable": true}, "shipment_order_no": {"type": "string", "nullable": true}, "appointment_time_from": {"type": "string", "nullable": true}, "appointment_time_to": {"type": "string", "nullable": true}, "appointment_date": {"type": "string", "format": "date-time", "nullable": true}, "confirmed_by_shipper": {"type": "integer", "format": "int32"}, "confirmed_by_contractor": {"type": "integer", "format": "int32"}, "open_time": {"type": "string", "nullable": true}, "close_time": {"type": "string", "nullable": true}, "shipper_name": {"type": "string", "nullable": true}, "address": {"type": "string", "nullable": true}, "request_pickup_date": {"type": "string", "format": "date-time", "nullable": true}, "reference": {"type": "string", "nullable": true}, "sales_order": {"type": "string", "nullable": true}, "purchase_order": {"type": "string", "nullable": true}, "load": {"type": "string", "nullable": true}, "bol": {"type": "string", "nullable": true}, "master_order": {"type": "string", "nullable": true}, "work_order": {"type": "string", "nullable": true}, "total_freight": {"type": "string", "nullable": true}, "order_status": {"type": "string", "nullable": true}, "appointtment_status": {"$ref": "#/components/schemas/ShipmentOrderShipperAppointmentStatusDto"}, "shipment_order_appointment_status": {"type": "integer", "format": "int32"}, "carriers": {"type": "array", "items": {"$ref": "#/components/schemas/AppointmentCarrierInfo"}, "nullable": true}, "shipment_order_id": {"type": "integer", "format": "int64"}, "tripno_or_taskno": {"type": "integer", "format": "int64"}}, "additionalProperties": false}, "ShipmentOrderConsigneeDetailDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "name": {"type": "string", "nullable": true}, "address1": {"type": "string", "nullable": true}, "address2": {"type": "string", "nullable": true}, "city": {"type": "string", "nullable": true}, "state": {"type": "string", "nullable": true}, "zip_code": {"type": "string", "nullable": true}, "country": {"type": "string", "nullable": true}, "longitude": {"type": "number", "format": "double"}, "latitude": {"type": "number", "format": "double"}, "open_time": {"type": "string", "nullable": true}, "close_time": {"type": "string", "nullable": true}, "primary_contact_name": {"type": "string", "nullable": true}, "primary_contact_phone": {"type": "string", "nullable": true}, "primary_contact_email": {"type": "string", "nullable": true}, "secondary_contact_name": {"type": "string", "nullable": true}, "secondary_contact_phone": {"type": "string", "nullable": true}, "secondary_contact_email": {"type": "string", "nullable": true}, "note": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ShipmentOrderConsigneeDto": {"type": "object", "properties": {"consignee": {"type": "string", "nullable": true}, "delivery_appointment": {"type": "string", "format": "date-time", "nullable": true}, "delivered_time": {"type": "string", "format": "date-time", "nullable": true}, "transit_days": {"type": "number", "format": "double"}, "name": {"type": "string", "nullable": true}, "contact_id": {"type": "integer", "format": "int64"}, "location_code": {"type": "integer", "format": "int32"}, "address": {"type": "string", "nullable": true}, "contact": {"type": "string", "nullable": true}, "phone": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "desired_delivery_date": {"type": "string", "nullable": true}, "mabd": {"type": "string", "format": "date-time", "nullable": true}, "appointment_date": {"type": "string", "nullable": true}, "appointment_from": {"type": "string", "nullable": true}, "appointment_to": {"type": "string", "nullable": true}, "consignee_note": {"type": "string", "nullable": true}, "consignee_id": {"type": "integer", "format": "int64"}, "order_status": {"type": "integer", "format": "int32"}, "appointment_status": {"type": "integer", "format": "int32"}, "open_time": {"type": "string", "nullable": true}, "close_time": {"type": "string", "nullable": true}, "is_new": {"type": "boolean"}, "task_no": {"type": "integer", "format": "int64"}, "mabd_from": {"type": "string", "nullable": true}, "mabd_to": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ShipmentOrderShipperAppointmentStatusDto": {"type": "object", "properties": {"appointment_id": {"type": "integer", "format": "int64"}, "create_time": {"type": "string", "format": "date-time", "nullable": true}, "order_status": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "ShipmentOrderStatusEnum": {"enum": ["New", "Pickup Confirmed", "Out For Pickup", "Pickup Complete", "Pending Linehaul", "Line<PERSON>ul", "Pending Delivery", "Out For Delivery", "Delivered", "Partial Delivered", "Pending Service", "Delivered With Exception", "Delivery Failed", "Consignee Refusal", "Cancelled", "Incomplete", "Return To Shipper"], "type": "string"}, "ShipmentOrderSubStatusEnum": {"enum": ["NotValid", "New", "Package Ready", "Pending Pickup", "Pickup Dispatched", "Out For Pickup", "Departed Pickup Location", "Pickup Complete", "DirectlyPickupOffload", "Pending Linehaul", "<PERSON><PERSON><PERSON> Dispatched", "Linehaul Loaded", "Linehaul In Transit", "In Transit", "Complete", "Pending Dispatch", "Dispatched", "Out For Delivery", "Delivery", "Partial Delivered", "Pending Service", "Delivered with Exception", "Delivery Failed", "Consignee Refusal", "Cancelled", "Incomplete", "Return To Shipper"], "type": "string"}}}}