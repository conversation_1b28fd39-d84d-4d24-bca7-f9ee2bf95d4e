import unisSitePrompt  from './unisSitePrompts';
import industrySitePrompt from './industrySitePrompts';
import itemSitePrompt from './itemSitePrompts';
import wmsSitePrompt from './wmsSitePrompts';
import fmsSitePrompt from './fmsSitePrompts';
import cubeworkSitePrompt from './cubeworkSitePrompts';
import lsoSitePrompt from './lsoSitePrompts';
import clientPortalSitePrompt from './clientPortalPrompts';
import marketplaceSitePrompt from './marketplaceSitePrompts';

const sitePromptsMap: Record<string, string> = {
  wms: wmsSitePrompt,
  unis: unisSitePrompt,
  fms: fmsSitePrompt,
  cubework: cubeworkSitePrompt,
  lso: lsoSitePrompt,
  portal: clientPortalSitePrompt,
  marketplace: marketplaceSitePrompt,
};

/**
 * Get the system prompt for a specific site ID
 * @param siteId The site ID to get the prompt for
 * @returns The system prompt for the specified site ID
 */
export function getSitePrompt(siteId: string): string | null {
  return sitePromptsMap[siteId] || null;
} 