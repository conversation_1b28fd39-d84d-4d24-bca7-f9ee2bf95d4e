'use client';

import React, { FormEvent, ChangeEvent, useRef, useEffect, useState, useCallback, useMemo } from 'react';
import { useRealtimeVoice, type VoiceMode } from '@/hooks/useRealtimeVoice';
import { log } from '@/utils/logger';
import '@/styles/item-design-system.css';

// 类型定义
interface HybridAgentStreamData {
  toolCallId: string;
  type: string;
  data?: string;
  error?: string;
}

interface ChatInputProps {
  input: string;
  handleSubmit: (content: string, file?: File | null) => void;
  isLoading: boolean;
  model: string;
  uploadedImage?: File | null;
  onStopGeneration?: () => void;
  onVoiceTranscriptDelta?: (delta: string) => void;
  onVoiceTranscriptComplete?: () => void;
  onUserVoiceTranscript?: (transcript: string) => void;
  onHybridAgentStream?: (data: HybridAgentStreamData) => void;
  onVoiceConnectionChange?: (connected: boolean, audioElement?: HTMLAudioElement) => void;
  isAudioRecording?: boolean; // 新增：音频录制状态
}

// 常量定义
const MAX_FILE_SIZE = 25 * 1024 * 1024; // 25MB
const MAX_TEXTAREA_HEIGHT = 200;
const FREQUENCY_BANDS = 6;

// 工具函数
const formatFileSize = (bytes: number): string => {
  if (bytes < 1024) return bytes + ' B';
  if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + ' KB';
  return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
};

const getModelInfo = (model: string) => {
  if (model.includes('gpt-4o')) {
    return { name: 'GPT-4o (2024-11-20)', color: 'text-item-purple-light' };
  } else if (model.includes('gpt-3.5')) {
    return { name: 'GPT-3.5', color: 'text-item-gray-400' };
  }
  return { name: model, color: 'text-item-gray-400' };
};

export default function ChatInput({
  input: externalInput,
  handleSubmit,
  isLoading,
  model,
  uploadedImage: externalUploadedImage,
  onStopGeneration,
  onVoiceTranscriptDelta,
  onVoiceTranscriptComplete,
  onUserVoiceTranscript,
  onHybridAgentStream,
  onVoiceConnectionChange,
  isAudioRecording = false
}: ChatInputProps) {
  // 状态管理
  const [localInput, setLocalInput] = useState(externalInput);
  const [uploadedImage, setUploadedImage] = useState<File | null>(externalUploadedImage || null);
  const [frequencyData, setFrequencyData] = useState<number[]>(new Array(FREQUENCY_BANDS).fill(0));

  // 使用 useRef 来存储回调函数，避免依赖循环
  const onUserVoiceTranscriptRef = useRef(onUserVoiceTranscript);
  const onVoiceTranscriptDeltaRef = useRef(onVoiceTranscriptDelta);
  const onVoiceTranscriptCompleteRef = useRef(onVoiceTranscriptComplete);
  const onHybridAgentStreamRef = useRef(onHybridAgentStream);
  const onVoiceConnectionChangeRef = useRef(onVoiceConnectionChange);

  // 更新 ref 值
  useEffect(() => {
    onUserVoiceTranscriptRef.current = onUserVoiceTranscript;
    onVoiceTranscriptDeltaRef.current = onVoiceTranscriptDelta;
    onVoiceTranscriptCompleteRef.current = onVoiceTranscriptComplete;
    onHybridAgentStreamRef.current = onHybridAgentStream;
    onVoiceConnectionChangeRef.current = onVoiceConnectionChange;
  }, [onUserVoiceTranscript, onVoiceTranscriptDelta, onVoiceTranscriptComplete, onHybridAgentStream, onVoiceConnectionChange]);

  // 语音功能配置
  const voiceConfig = useMemo(() => ({
    onUserTranscript: (transcript: string) => {
      log.debug('User transcript received:', transcript, 'ChatInput');
      onUserVoiceTranscriptRef.current?.(transcript);
    },
    onAssistantTranscriptDelta: (delta: string) => {
      onVoiceTranscriptDeltaRef.current?.(delta);
    },
    onAssistantTranscriptComplete: () => {
      onVoiceTranscriptCompleteRef.current?.();
    },
    onError: (error: any) => {
      log.error('Voice error:', error, 'ChatInput');
    },
    onHybridAgentStream: (data: any) => {
      onHybridAgentStreamRef.current?.(data);
    },
    onFrequencyData: (frequencies: number[]) => {
      setFrequencyData(frequencies);
    },
  }), []); // 移除所有回调依赖，使用ref来访问最新值

  const {
    isConnected: voiceConnected,
    isConnecting: voiceConnecting,
    mode: voiceMode,
    isPTTActive,
    isRecording,
    isMuted,
    error: voiceError,
    toggleConnection: toggleVoiceConnection,
    toggleMode: toggleVoiceMode,
    startPTT,
    stopPTT,
    toggleMute,
    getAudioElement,
  } = useRealtimeVoice(voiceConfig);

  // Refs
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [selectedFile, setSelectedFile] = useState<File | null>(uploadedImage || null);
  const [filePreview, setFilePreview] = useState<string | null>(null);
  const [isImage, setIsImage] = useState(false);

  // 计算值
  const modelInfo = useMemo(() => getModelInfo(model), [model]);
  const canSubmit = useMemo(() => 
    (localInput.trim() || selectedFile) && !isLoading, 
    [localInput, selectedFile, isLoading]
  );

  // 语音按钮状态
  const voiceButtonStateComputed = useMemo(() => {
    if (!voiceConnected) return 'disconnected';
    if (voiceMode === 'live' && isRecording) return 'recording';
    if (voiceMode === 'ptt' && isPTTActive) return 'ptt-active';
    return 'connected';
  }, [voiceConnected, voiceMode, isRecording, isPTTActive]);

  const voiceStatusText = useMemo(() => {
    if (!voiceConnected) return 'Disconnected';
    if (voiceMode === 'ptt') {
      return isPTTActive ? 'PTT • Recording' : 'PTT • Hold to Talk';
    } else {
      return isRecording ? 'LIVE • Recording' : 'LIVE • Listening';
    }
  }, [voiceConnected, voiceMode, isPTTActive, isRecording]);

  // 事件处理函数
  const handleInputChange = useCallback((e: ChangeEvent<HTMLTextAreaElement>) => {
    setLocalInput(e.target.value);
  }, []);

  const resetFileSelection = useCallback(() => {
    setSelectedFile(null);
    setFilePreview(null);
    setIsImage(false);
    setUploadedImage(null);
    
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  }, []);

  const submitMessage = useCallback(() => {
    if (canSubmit) {
      handleSubmit(localInput, selectedFile);
      resetFileSelection();
      setLocalInput('');
      // 提交后立即将焦点返回到输入框
      textareaRef.current?.focus();
    }
  }, [canSubmit, handleSubmit, localInput, selectedFile, resetFileSelection]);

  const handleKeyDown = useCallback((e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      if (canSubmit) {
        submitMessage();
      }
    }

    if (e.key === 'Escape' && isLoading && onStopGeneration) {
      e.preventDefault();
      onStopGeneration();
    }
  }, [canSubmit, submitMessage, isLoading, onStopGeneration]);

  // 副作用
  useEffect(() => {
    setLocalInput(externalInput);
  }, [externalInput]);

  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${Math.min(textareaRef.current.scrollHeight, MAX_TEXTAREA_HEIGHT)}px`;
    }
  }, [localInput]);

  useEffect(() => {
    if (uploadedImage !== undefined) {
      setSelectedFile(uploadedImage);

      if (uploadedImage) {
        const isImageFile = uploadedImage.type.startsWith('image/');
        setIsImage(isImageFile);

        if (isImageFile) {
          const reader = new FileReader();
          reader.onload = (e) => setFilePreview(e.target?.result as string);
          reader.readAsDataURL(uploadedImage);
        } else {
          setFilePreview(null);
        }
      } else {
        setFilePreview(null);
        setIsImage(false);
      }
    }
  }, [uploadedImage]);

  useEffect(() => {
    const handleGlobalKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isLoading && onStopGeneration) {
        e.preventDefault();
        onStopGeneration();
      }
    };

    window.addEventListener('keydown', handleGlobalKeyDown);
    return () => window.removeEventListener('keydown', handleGlobalKeyDown);
  }, [isLoading, onStopGeneration]);

  const handleFileChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];

    if (!file) {
      resetFileSelection();
      return;
    }

    if (file.size > MAX_FILE_SIZE) {
      alert('文件过大，请选择25MB以内的文件');
      resetFileSelection();
      return;
    }

    setSelectedFile(file);
    setUploadedImage(file);

    const isImageFile = file.type.startsWith('image/');
    setIsImage(isImageFile);

    if (isImageFile) {
      const reader = new FileReader();
      reader.onload = (e) => setFilePreview(e.target?.result as string);
      reader.readAsDataURL(file);
    } else {
      setFilePreview(null);
    }
  }, [resetFileSelection]);

  const triggerFileUpload = useCallback(() => {
    fileInputRef.current?.click();
  }, []);

  // 使用计算的模型信息

  const handlePTTMouseDown = useCallback(() => {
    if (voiceMode === 'ptt' && voiceConnected && !isPTTActive) {
      startPTT();
    }
  }, [voiceMode, voiceConnected, isPTTActive, startPTT]);

  const handlePTTMouseUp = useCallback(() => {
    if (voiceMode === 'ptt' && voiceConnected && isPTTActive) {
      stopPTT();
    }
  }, [voiceMode, voiceConnected, isPTTActive, stopPTT]);

  // 使用计算的语音按钮状态
  const voiceButtonState = voiceButtonStateComputed;

  // 使用计算的语音状态文本

  // 通知父组件语音连接状态变化
  useEffect(() => {
    const callback = onVoiceConnectionChangeRef.current;
    if (callback) {
      if (voiceConnected) {
        // 连接成功后，获取音频元素
        const audioElement = getAudioElement();
        if (audioElement) {
          callback(true, audioElement);
        } else {
          // 如果没有音频元素，稍后重试
          setTimeout(() => {
            const retryAudioElement = getAudioElement();
            if (retryAudioElement) {
              callback(true, retryAudioElement);
            }
          }, 500);
        }
      } else {
        callback(false);
      }
    }
  }, [voiceConnected, getAudioElement]); // 移除onVoiceConnectionChange依赖，使用ref

  // 录音状态指示器 - 独立的一闪一闪红色圆点
  const RecordingIndicator = () => {
    const showRecording = voiceConnected && isAudioRecording;
    
    if (!showRecording) return null; // 不录音时不显示
    
    return (
      <div className="flex items-center space-x-2 bg-red-900/20 px-2 py-1 rounded-full border border-red-500/30 backdrop-blur-sm">
        {/* 一闪一闪的红色圆点 */}
        <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse shadow-sm"></div>
        <span className="text-red-400 text-xs font-medium">REC</span>
      </div>
    );
  };

  // 动态频谱可视化组件 - 反映真实频率分布
  const AudioSpectrumBars = () => {
    const isActive = isRecording || (voiceMode === 'ptt' && isPTTActive);
    
    return (
      <div className="flex items-end space-x-0.5 h-3">
        {frequencyData.map((frequency, index) => {
          // 每个条形代表不同的频率范围
          const baseHeight = 2;
          const maxHeight = 12;
          const level = isActive ? frequency : 0;
          const height = baseHeight + (maxHeight - baseHeight) * level;
          
          // 不同频段使用稍微不同的颜色，模拟真实频谱
          const getBarColor = (freq: number, active: boolean) => {
            if (!active) return 'bg-item-gray-500';
            
            // 根据频率强度调整颜色亮度
            const intensity = Math.min(freq * 1.5, 1);
            if (intensity > 0.7) return 'bg-item-purple';
            if (intensity > 0.4) return 'bg-item-purple-light';
            return 'bg-item-gray-400';
          };
          
          return (
            <div
              key={index}
              className={`w-0.5 rounded-full transition-all duration-100 ease-out ${
                getBarColor(frequency, isActive)
              } ${isActive ? 'shadow-sm' : ''}`}
              style={{ 
                height: `${height}px`,
                transform: isActive ? 'scaleY(1)' : 'scaleY(0.6)',
              }}
              title={`频段 ${index + 1}: ${Math.round(frequency * 100)}%`}
            />
          );
        })}
      </div>
    );
  };

  return (
    <div className="relative">
      {/* 处理中指示器 */}
      {isLoading && (
        <div className="absolute -top-6 left-0 right-0 flex justify-center">
          <div className="flex items-center space-x-2 bg-item-purple/20 px-3 py-1 rounded-t-lg border-t border-l border-r border-item-purple/40">
            <div className="w-4 h-4 relative flex justify-center items-center">
              <div className="absolute w-full h-full border-2 border-t-item-purple border-r-transparent border-b-transparent border-l-transparent rounded-full animate-spin"></div>
            </div>
            <span className="text-xs text-item-purple-light">Generating response...</span>
            {isLoading && onStopGeneration && (
              <button
                onClick={onStopGeneration}
                className="ml-2 bg-red-900/60 hover:bg-red-800/80 text-red-200 text-xs px-2 py-0.5 rounded border border-red-700/50 transition-colors"
                title="Press Esc to stop"
              >
                Stop <span className="opacity-70 text-[10px]">(Esc)</span>
              </button>
            )}
          </div>
        </div>
      )}

      {/* 语音状态指示器 */}
      {voiceConnected && (
        <div className="absolute -top-8 left-0 flex items-center space-x-2">
          {/* 录音指示器 - 在同一行最左边 */}
          <RecordingIndicator />
          
          <div className="flex items-center space-x-2 bg-item-bg-card/90 px-2 py-1 rounded-md border border-item-gray-800/50 text-xs">
            {/* 动态频谱可视化 */}
            <AudioSpectrumBars />
            <span className="text-item-gray-300">
              {voiceStatusText}
            </span>
            {voiceError && (
              <span className="text-red-400 ml-1">Error</span>
            )}
            {/* 断开连接按钮 - 简洁版 */}
            <button
              onClick={toggleVoiceConnection}
              className="ml-2 text-red-400 hover:text-red-300 hover:bg-red-900/20 px-1 py-0.5 rounded transition-colors"
              title="Disconnect Voice"
            >
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-3 h-3">
                <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>
      )}

      {/* 文件预览区域 */}
      {selectedFile && (
        <div className="absolute bottom-full left-0 mb-3 p-3 item-card bg-item-bg-card border border-item-gray-800 rounded-lg shadow-xl item-animate-in">
          <div className="flex items-center">
            <div className="mr-2">
              {isImage && filePreview ? (
                <img
                  src={filePreview}
                  alt="Preview"
                  className="w-10 h-10 object-cover rounded"
                />
              ) : (
                <div className="w-10 h-10 flex items-center justify-center bg-item-bg-hover rounded-lg">
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-6 h-6 text-item-purple">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z" />
                  </svg>
                </div>
              )}
            </div>
            <div className="flex-1 min-w-0">
              <div className="text-sm truncate max-w-[200px]">{selectedFile.name}</div>
              <div className="text-xs text-item-gray-400">{formatFileSize(selectedFile.size)}</div>
            </div>
            <button
              onClick={resetFileSelection}
              className="ml-2 p-1.5 rounded-lg hover:bg-item-bg-hover transition-all duration-200"
              aria-label="Remove file"
            >
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-4 h-4">
                <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>
      )}

      <form onSubmit={(e) => {
        e.preventDefault();
        submitMessage();
      }} className="relative">
        <div className="flex items-end bg-item-bg-card rounded-xl p-3 transition-all duration-200">
          {/* 图像上传按钮 */}
          <button
            type="button"
            onClick={triggerFileUpload}
            disabled={isLoading}
            className={`p-2 rounded-lg text-item-gray-400 hover:text-item-gray-300 hover:bg-item-bg-hover transition-all duration-200 ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
          >
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5">
              <path strokeLinecap="round" strokeLinejoin="round" d="M2.25 15.75l5.159-5.159a2.25 2.25 0 013.182 0l5.159 5.159m-1.5-1.5l1.409-1.409a2.25 2.25 0 013.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 001.5-1.5V6a1.5 1.5 0 00-1.5-1.5H3.75A1.5 1.5 0 002.25 6v12a1.5 1.5 0 001.5 1.5zm10.5-11.25h.008v.008h-.008V8.25zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0z" />
            </svg>
          </button>
          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            onChange={handleFileChange}
            className="hidden"
          />

          {/* 语音按钮 */}
          <div className="relative">
            {/* 主语音按钮 */}
            <button
              type="button"
              onClick={() => {
                // Live模式下已连接时不做任何操作，只作为状态指示器
                if (voiceConnected && voiceMode === 'live') {
                  return;
                }
                // 其他情况下切换连接状态
                toggleVoiceConnection();
              }}
              disabled={isLoading || voiceConnecting}
              className={`p-2 rounded-full transition-all duration-200 ${
                voiceButtonState === 'disconnected'
                  ? 'text-item-gray-400 hover:text-item-gray-300 hover:bg-item-bg-hover'
                  : voiceButtonState === 'recording'
                  ? 'text-red-400 bg-red-900/30 animate-pulse'
                  : voiceButtonState === 'ptt-active'
                  ? 'text-red-400 bg-red-900/50'
                  : voiceMode === 'live' && voiceConnected
                  ? 'text-item-gray-300 bg-item-bg-hover cursor-default'  // Live模式下已连接时不可点击
                  : 'text-item-gray-300 bg-item-bg-hover'
              } ${isLoading || voiceConnecting ? 'opacity-50 cursor-not-allowed' : ''}`}
              title={
                voiceConnected 
                  ? voiceMode === 'live' 
                    ? `Voice: ${voiceMode.toUpperCase()} - Listening`
                    : `Voice: ${voiceMode.toUpperCase()}`
                  : 'Connect Voice'
              }
            >
              {voiceConnecting ? (
                <svg className="w-5 h-5 animate-spin" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              ) : (
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M12 18.75a6 6 0 006-6v-1.5m-6 7.5a6 6 0 01-6-6v-1.5m6 7.5v3.75m-3.75 0h7.5M12 15.75a3 3 0 01-3-3V4.5a3 3 0 116 0v8.25a3 3 0 01-3 3z" />
                </svg>
              )}
            </button>

            {/* PTT 覆盖按钮 */}
            {voiceConnected && voiceMode === 'ptt' && (
              <button
                type="button"
                onMouseDown={handlePTTMouseDown}
                onMouseUp={handlePTTMouseUp}
                onMouseLeave={handlePTTMouseUp}
                onTouchStart={handlePTTMouseDown}
                onTouchEnd={handlePTTMouseUp}
                disabled={isLoading}
                className={`absolute inset-0 p-2 rounded-full transition-all duration-200 ${
                  isPTTActive
                    ? 'text-red-400 bg-red-900/50 scale-110'
                    : 'text-item-gray-300 hover:bg-item-bg-hover'
                } ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
                title={isPTTActive ? 'Recording...' : 'Hold to Talk'}
              >
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M12 18.75a6 6 0 006-6v-1.5m-6 7.5a6 6 0 01-6-6v-1.5m6 7.5v3.75m-3.75 0h7.5M12 15.75a3 3 0 01-3-3V4.5a3 3 0 116 0v8.25a3 3 0 01-3 3z" />
                </svg>
              </button>
            )}

            {/* 模式切换小按钮 */}
            {voiceConnected && (
              <button
                type="button"
                onClick={toggleVoiceMode}
                className="absolute -top-1 -right-1 w-3 h-3 bg-item-purple hover:bg-item-purple-dark rounded-full text-white text-[8px] flex items-center justify-center transition-colors"
                title={`Switch to ${voiceMode === 'live' ? 'PTT' : 'Live'}`}
              >
                {voiceMode === 'live' ? 'L' : 'P'}
              </button>
            )}
          </div>

          <textarea
            ref={textareaRef}
            value={localInput}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
            placeholder="Type a message or use voice..."
            className={`flex-1 bg-transparent outline-none resize-none max-h-[200px] text-white placeholder-item-gray-500 ml-3 font-medium ${
              isLoading ? 'opacity-60' : ''
            }`}
            rows={1}
          />
          <div className="flex items-center">
            {/* 显示当前模型 */}
            <span className={`text-xs mr-3 ${modelInfo.color}`}>
              {modelInfo.name}
            </span>

            <button
              type="submit"
              disabled={isLoading || (!localInput.trim() && !selectedFile)}
              className={`ml-2 p-2 rounded-lg transition-all duration-200 ${
                isLoading || (!localInput.trim() && !selectedFile)
                  ? 'bg-item-gray-700 text-item-gray-400 cursor-not-allowed'
                  : 'bg-item-purple text-white hover:bg-item-purple-dark hover:shadow-md'
              }`}
            >
              {isLoading ? (
                <svg className="w-5 h-5 animate-spin" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
              ) : (
                <svg
                  className="w-5 h-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                </svg>
              )}
            </button>
          </div>
        </div>

        {/* Decorative cyber elements */}
        <div className="absolute -bottom-0.5 left-0 w-full h-0.5 bg-gradient-to-r from-item-purple/0 via-item-purple/40 to-item-purple/0"></div>
        <div className="absolute -top-1 left-10 w-2 h-2 bg-item-purple rounded-full animate-pulse"></div>
        <div className="absolute -top-1 right-10 w-2 h-2 bg-item-purple rounded-full animate-pulse" style={{ animationDelay: '0.5s' }}></div>
      </form>
    </div>
  );
}