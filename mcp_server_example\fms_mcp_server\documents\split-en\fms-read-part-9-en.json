{"openapi": "3.0.1", "info": {"title": "FMS", "description": "", "version": "1.0.0"}, "paths": {"/fms-platform-dispatch-management/task/get-linehaul-details": {"post": {"summary": "Get Linehaul details", "deprecated": false, "description": "", "tags": ["TaskList"], "parameters": [], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/GetLinehaulListInputDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/GetLinehaulListOutputDto"}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/tms/trip/send-tms-trip-to-fafka": {"get": {"summary": "/fms-platform-dispatch-management/tms/trip/send-tms-trip-to-fafka", "deprecated": false, "description": "", "tags": ["TmsTripOperation"], "parameters": [{"name": "tripIds", "in": "query", "description": "", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "boolean"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/tms/trip/get-not-exist-tms-trips": {"post": {"summary": "/fms-platform-dispatch-management/tms/trip/get-not-exist-tms-trips", "deprecated": false, "description": "", "tags": ["TmsTripOperation"], "parameters": [], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/GetDispatchedTmsForFmsTripsRequestDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"type": "string"}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/TripDetail/GetTripDetailBolPdf": {"get": {"summary": "Get pdf", "deprecated": false, "description": "", "tags": ["TripDetail"], "parameters": [{"name": "tripNo", "in": "query", "description": "", "required": false, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/TripDispatch/ManualChangeTaskByOrderServiceLevelMsgConsume": {"post": {"summary": "/fms-platform-dispatch-management/TripDispatch/ManualChangeTaskByOrderServiceLevelMsgConsume", "deprecated": false, "description": "", "tags": ["TripDispatch"], "parameters": [{"name": "msg", "in": "query", "description": "", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/TripDispatch/AppendPackageToRouteResultByTaskNo": {"get": {"summary": "Press task to push route engine information Append", "deprecated": false, "description": "", "tags": ["TripDispatch"], "parameters": [{"name": "taskNo", "in": "query", "description": "", "required": false, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "boolean"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/trip-dispatch/get-delivery-task": {"post": {"summary": "/fms-platform-dispatch-management/trip-dispatch/get-delivery-task", "deprecated": false, "description": "", "tags": ["TripDispatch"], "parameters": [], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/GetPendingTaskInputDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RouteEngineDispatchLocalDeliveryTaskResponsePagedResultDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/trip-dispatch/get-pickup-task": {"post": {"summary": "/fms-platform-dispatch-management/trip-dispatch/get-pickup-task", "deprecated": false, "description": "", "tags": ["TripDispatch"], "parameters": [], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/GetPendingTaskInputDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RouteEngineDispatchLocalPickupTaskResponsePagedResultDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/trip-dispatch/get-service-task": {"post": {"summary": "/fms-platform-dispatch-management/trip-dispatch/get-service-task", "deprecated": false, "description": "", "tags": ["TripDispatch"], "parameters": [], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/GetPendingTaskInputDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RouteEngineDispatchLocalServiceTaskResponsePagedResultDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/dispatching-dashboard/search": {"post": {"summary": "Dispatch Dashboard page query Order", "deprecated": false, "description": "", "tags": ["TripDispatchEnhance"], "parameters": [], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/GetDispatchOrderRequest_V2"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/DispatchDashboardCardDtoPagedResultDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/dispatching-dashboard/get-trips": {"post": {"summary": "/fms-platform-dispatch-management/dispatching-dashboard/get-trips", "deprecated": false, "description": "", "tags": ["TripDispatchEnhance"], "parameters": [], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/GetTripsInputDto_V2"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/GetTripsOutputDtoPagedResultDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/local-dispatch-routing/get-local-task": {"post": {"summary": "Order Filter Query List\r\nA few other Tab tags besides the LH interface", "deprecated": false, "description": "", "tags": ["TripDispatchEnhance"], "parameters": [], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/LocalDispatchOrderFilterRequest"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/LocalDispatchOrderFilterResponsePagedResultDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/local-dispatch-routing/get-local-task-count": {"post": {"summary": "Order Filter Query List\r\nA few other Tab tags besides the LH interface", "deprecated": false, "description": "", "tags": ["TripDispatchEnhance"], "parameters": [], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/LocalDispatchOrderFilterRequest"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/LocalDispatchOrderTabCountResponse"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/Trips/GetMapInfo": {"post": {"summary": "Get map information", "deprecated": false, "description": "", "tags": ["Trips"], "parameters": [{"name": "tripNo", "in": "query", "description": "", "required": false, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/MapOutputDtoSuccessResponse"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/Trips/GetMapService": {"post": {"summary": "/fms-platform-dispatch-management/Trips/GetMapService", "deprecated": false, "description": "", "tags": ["Trips"], "parameters": [], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/GetMapServiceRequest"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/GetMapServiceResponse"}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/Trips/GetTaskSummeryByTripId": {"post": {"summary": "Task statistics under Trip list page single Trip", "deprecated": false, "description": "", "tags": ["Trips"], "parameters": [], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/SummeryTaskInfoInputDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/SummeryTaskInfoOutputDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/Trips/GetTripHistory": {"get": {"summary": "GetTripHistory", "deprecated": false, "description": "", "tags": ["Trips"], "parameters": [{"name": "tripNo", "in": "query", "description": "", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TripHistoryOutputDtoSuccessResponse"}}}, "headers": {}}}, "security": [{"apikey-header-fms-token": []}]}}, "/fms-platform-dispatch-management/Trips/GetTripInfoStatus": {"get": {"summary": "Get the Trip status pull-down enum", "deprecated": false, "description": "", "tags": ["Trips"], "parameters": [], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Int32FmsSelectListItemListResultDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/Trips/GetTripListSummery": {"post": {"summary": "Statistical interface on the upper left of Trip", "deprecated": false, "description": "", "tags": ["Trips"], "parameters": [], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/TripListInPutDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TripListOutPutDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/Trips/GetAllTrackingByTripId": {"post": {"summary": "Used for File Page File Dropdown All Tracking for Current Trip", "deprecated": false, "description": "", "tags": ["Trips"], "parameters": [], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/FileInputDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/FileAllTrackingOutputDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}}, "components": {"schemas": {"DispatchDashboardCardDto": {"type": "object", "properties": {"dispatch_type": {"type": "integer", "format": "int32"}, "task_no": {"type": "integer", "format": "int64"}, "tracking_no": {"type": "string", "nullable": true}, "task_dst_terminal": {"type": "string", "nullable": true}, "task_org_terminal": {"type": "string", "nullable": true}, "task_status": {"type": "integer", "format": "int32"}, "task_type": {"type": "integer", "format": "int32"}, "task_type_group": {"type": "string", "nullable": true}, "order_no": {"type": "string", "nullable": true}, "order_status": {"type": "string", "nullable": true}, "service_level": {"type": "string", "nullable": true}, "order_current_location": {"type": "string", "nullable": true}, "shipment_type": {"type": "string", "nullable": true}, "package_count": {"type": "integer", "format": "int32"}, "weight": {"type": "number", "format": "double"}, "weight_uom": {"type": "string", "nullable": true}, "shipper_name": {"type": "string", "nullable": true}, "consignee_name": {"type": "string", "nullable": true}, "customer_name": {"type": "string", "nullable": true}, "from_lat": {"type": "number", "format": "double"}, "from_lng": {"type": "number", "format": "double"}, "to_lat": {"type": "number", "format": "double"}, "to_lng": {"type": "number", "format": "double"}, "city": {"type": "string", "nullable": true}, "state": {"type": "string", "nullable": true}, "bill_to": {"type": "string", "nullable": true}, "eta": {"type": "string", "format": "date-time"}, "etd": {"type": "string", "format": "date-time"}, "appointment_date": {"type": "string", "format": "date-time"}, "assign_status": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "DispatchDashboardCardDtoPagedResultDto": {"type": "object", "properties": {"total_count": {"type": "integer", "format": "int64"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/DispatchDashboardCardDto"}, "nullable": true}}, "additionalProperties": false}, "BatchFileInputDto": {"type": "object", "properties": {"public_url": {"type": "string", "nullable": true}, "file_extension": {"type": "string", "nullable": true}}, "additionalProperties": false}, "GetLinehaulListInputDto": {"type": "object", "properties": {"linehaul_no": {"type": "array", "items": {"type": "integer", "format": "int64"}, "nullable": true}, "org_terminal": {"type": "array", "items": {"type": "string"}, "nullable": true}, "dst_terminal": {"type": "array", "items": {"type": "string"}, "nullable": true}, "delivery_appt_date": {"type": "string", "nullable": true}, "date_type": {"type": "integer", "format": "int32", "nullable": true}, "from_date": {"type": "string", "nullable": true}, "to_date": {"type": "string", "nullable": true}}, "additionalProperties": false}, "GetLinehaulListOrderOutputDto": {"type": "object", "properties": {"order_no": {"type": "string", "nullable": true}, "tracking_no": {"type": "string", "nullable": true}, "shipper_name": {"type": "string", "nullable": true}, "consignee_name": {"type": "string", "nullable": true}, "org_terminal": {"type": "string", "nullable": true}, "dst_terminal": {"type": "string", "nullable": true}, "bill_to_code": {"type": "string", "nullable": true}, "bill_to_name": {"type": "string", "nullable": true}, "consignee_terminal": {"type": "string", "nullable": true}, "consignee_state": {"type": "string", "nullable": true}, "consignee_zipcode": {"type": "string", "nullable": true}, "pallets": {"type": "integer", "format": "int32"}, "weight": {"type": "string", "nullable": true}, "is_dispatch_trip": {"type": "integer", "format": "int32"}, "consignee_city": {"type": "string", "nullable": true}, "eta": {"type": "string", "format": "date-time"}, "est_rev": {"type": "number", "format": "double"}}, "additionalProperties": false}, "GetLinehaulListOutputDto": {"type": "object", "properties": {"is_dispatch_trip": {"type": "integer", "format": "int32"}, "linehaul_no": {"type": "integer", "format": "int64"}, "org_terminal": {"type": "string", "nullable": true}, "dst_terminal": {"type": "string", "nullable": true}, "delivery_appt_date": {"type": "string", "nullable": true}, "appointment_time_from": {"type": "string", "nullable": true}, "appointment_time_to": {"type": "string", "nullable": true}, "pallets": {"type": "integer", "format": "int32"}, "weight": {"type": "string", "nullable": true}, "create_date": {"type": "string", "nullable": true}, "dst_terminal_street": {"type": "string", "nullable": true}, "dst_terminal_city": {"type": "string", "nullable": true}, "est_depature_date": {"type": "string", "nullable": true}, "est_depature_time_from": {"type": "string", "nullable": true}, "est_depature_time_to": {"type": "string", "nullable": true}, "est_arrival_date": {"type": "string", "nullable": true}, "est_arrival_time_from": {"type": "string", "nullable": true}, "est_arrival_time_to": {"type": "string", "nullable": true}, "order_list": {"type": "array", "items": {"$ref": "#/components/schemas/GetLinehaulListOrderOutputDto"}, "nullable": true}, "trip_no": {"type": "integer", "format": "int64"}, "linehaul_status_text": {"type": "string", "nullable": true}}, "additionalProperties": false}, "GetDispatchOrderRequest_V2": {"type": "object", "properties": {"dispatch_type": {"type": "integer", "format": "int32"}, "sorting": {"type": "string", "nullable": true}, "order_nos": {"type": "array", "items": {"type": "string"}, "nullable": true}, "trackingno_or_pronos": {"type": "array", "items": {"type": "string"}, "nullable": true}, "status": {"type": "array", "items": {"type": "string"}, "nullable": true}, "task_types": {"type": "array", "items": {"type": "string"}, "nullable": true}, "shpr_trms": {"type": "array", "items": {"type": "string"}, "nullable": true}, "cnse_trms": {"type": "array", "items": {"type": "string"}, "nullable": true}, "svcs_trms": {"type": "array", "items": {"type": "string"}, "nullable": true}, "shipment_types": {"type": "array", "items": {"type": "string"}, "nullable": true}, "shipper_zip": {"type": "string", "nullable": true}, "shipper_state": {"type": "string", "nullable": true}, "shipper_city": {"type": "string", "nullable": true}, "shipper_names": {"type": "array", "items": {"type": "string"}, "nullable": true}, "service_levels": {"type": "array", "items": {"type": "string"}, "nullable": true}, "consignee_zip": {"type": "string", "nullable": true}, "consignee_state": {"type": "string", "nullable": true}, "consignee_city": {"type": "string", "nullable": true}, "consignee_names": {"type": "array", "items": {"type": "string"}, "nullable": true}, "skip": {"type": "integer", "format": "int32"}, "max_result_count": {"type": "integer", "format": "int32"}, "pickup_appointment_from": {"type": "string", "format": "date-time", "nullable": true}, "pickup_appointment_to": {"type": "string", "format": "date-time", "nullable": true}, "delivery_appointment_from": {"type": "string", "format": "date-time", "nullable": true}, "delivery_appointment_to": {"type": "string", "format": "date-time", "nullable": true}, "have_appointment": {"type": "boolean"}, "appointment_is_null": {"type": "boolean"}}, "additionalProperties": false}, "GetDispatchedTmsForFmsTripsRequestDto": {"type": "object", "properties": {"key": {"type": "string", "nullable": true}, "company_id": {"type": "integer", "format": "int32"}, "limit": {"type": "integer", "format": "int32"}, "date_from": {"type": "string", "nullable": true}, "date_to": {"type": "string", "nullable": true}, "trip_ids": {"type": "string", "nullable": true}, "original_terminal_codes": {"type": "string", "nullable": true}, "destination_terminal_codes": {"type": "string", "nullable": true}}, "additionalProperties": false}, "GetTripsOutputDtoPagedResultDto": {"type": "object", "properties": {"total_count": {"type": "integer", "format": "int64"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/GetTripsOutputDto"}, "nullable": true}}, "additionalProperties": false}, "FileAllTrackingOutputDto": {"type": "object", "properties": {"trip_no": {"type": "integer", "format": "int64"}, "tracking_lists": {"type": "array", "items": {"$ref": "#/components/schemas/TrackingList"}, "nullable": true}}, "additionalProperties": false}, "FileInputDto": {"type": "object", "properties": {"trip_no": {"type": "integer", "format": "int64"}, "file_name": {"type": "string", "nullable": true}, "file_id": {"type": "integer", "format": "int32", "nullable": true}, "rotation_angle": {"type": "string", "nullable": true}, "tracking_no": {"type": "string", "nullable": true}, "file_type": {"type": "string", "nullable": true}, "file_extension": {"type": "string", "nullable": true}, "image_type": {"type": "string", "nullable": true}, "public_url": {"type": "string", "nullable": true}, "public_url_array": {"type": "array", "items": {"$ref": "#/components/schemas/BatchFileInputDto"}, "nullable": true}}, "additionalProperties": false}, "GetTripsInputDto_V2": {"type": "object", "properties": {"trip_no": {"type": "array", "items": {"type": "integer", "format": "int64"}, "nullable": true}, "trip_type": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}, "org_terminal": {"type": "array", "items": {"type": "string"}, "nullable": true}, "dst_terminal": {"type": "array", "items": {"type": "string"}, "nullable": true}, "trip_status": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}, "route_id": {"type": "array", "items": {"type": "string"}, "nullable": true}, "route_name": {"type": "string", "nullable": true}, "carrier_code": {"type": "string", "nullable": true}, "driver_code": {"type": "string", "nullable": true}, "trip_date": {"type": "string", "nullable": true}, "tractor_code": {"type": "string", "nullable": true}, "trailer_code": {"type": "string", "nullable": true}, "page": {"type": "integer", "format": "int32"}, "per_page": {"type": "integer", "format": "int32"}, "user_terminal": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "GetPendingTaskInputDto": {"type": "object", "properties": {"order_nos": {"type": "array", "items": {"type": "string"}, "nullable": true}, "tracking_nos": {"type": "array", "items": {"type": "string"}, "nullable": true}, "terminal_code": {"type": "string", "nullable": true}, "pu_nos": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "GetMapServiceRequest": {"type": "object", "properties": {"points": {"type": "array", "items": {"$ref": "#/components/schemas/PointsItem"}, "nullable": true}}, "additionalProperties": false}, "GetMapServiceResponse": {"type": "object", "properties": {"lat": {"type": "number", "format": "double"}, "lng": {"type": "number", "format": "double"}}, "additionalProperties": false}, "LocalDispatchOrderFilterRequest": {"type": "object", "properties": {"task_types": {"type": "array", "items": {"type": "string"}, "nullable": true}, "trackingno_or_pronos": {"type": "array", "items": {"type": "string"}, "nullable": true}, "pu_no": {"type": "array", "items": {"type": "string"}, "nullable": true}, "order_nos": {"type": "array", "items": {"type": "string"}, "nullable": true}, "shipment_types": {"type": "array", "items": {"type": "string"}, "nullable": true}, "service_levels": {"type": "array", "items": {"type": "string"}, "nullable": true}, "bill_to_codes": {"type": "array", "items": {"type": "string"}, "nullable": true}, "route_name": {"type": "string", "nullable": true}, "shpr_trms": {"type": "array", "items": {"type": "string"}, "nullable": true}, "cnse_trms": {"type": "array", "items": {"type": "string"}, "nullable": true}, "svcs_trms": {"type": "array", "items": {"type": "string"}, "nullable": true}, "current_terminal": {"type": "string", "nullable": true}, "shipper_name": {"type": "string", "nullable": true}, "shipper_state": {"type": "string", "nullable": true}, "shipper_city": {"type": "string", "nullable": true}, "shipper_zip": {"type": "string", "nullable": true}, "consignee_name": {"type": "string", "nullable": true}, "consignee_state": {"type": "string", "nullable": true}, "consignee_city": {"type": "string", "nullable": true}, "consignee_zip": {"type": "string", "nullable": true}, "address": {"type": "string", "nullable": true}, "date": {"type": "string", "format": "date-time"}, "start_date": {"type": "string", "format": "date-time", "nullable": true}, "mabd_start": {"type": "string", "format": "date-time", "nullable": true}, "mabd_end": {"type": "string", "format": "date-time", "nullable": true}, "appointment_is_null": {"type": "boolean"}, "tab": {"$ref": "#/components/schemas/LocalDispatchOrderFilterTabEnum"}, "skip": {"type": "integer", "format": "int32"}, "max_result_count": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "GetTripsOutputDto": {"type": "object", "properties": {"trip_no": {"type": "integer", "format": "int64"}, "tms_trip_id": {"type": "integer", "format": "int64"}, "trip_status": {"type": "integer", "format": "int32"}, "org_terminal": {"type": "string", "nullable": true}, "dst_terminal": {"type": "string", "nullable": true}, "carrier_code": {"type": "string", "nullable": true}, "carrier_name": {"type": "string", "nullable": true}, "driver_code": {"type": "string", "nullable": true}, "driver_name": {"type": "string", "nullable": true}, "trip_type": {"type": "integer", "format": "int32"}, "route_id": {"type": "string", "nullable": true}, "route_name": {"type": "string", "nullable": true}, "stop_total": {"type": "integer", "format": "int32"}, "appt_total": {"type": "integer", "format": "int32"}, "dispatch_date": {"type": "string", "nullable": true}, "pallets_current": {"type": "integer", "format": "int32"}, "pallets_total": {"type": "integer", "format": "int32"}, "weight_current": {"type": "string", "nullable": true}, "weight_total": {"type": "string", "nullable": true}, "complete_pallets": {"type": "integer", "format": "int32"}, "complete_weight": {"type": "string", "nullable": true}, "is_auto": {"type": "boolean"}, "note": {"type": "string", "nullable": true}, "dispatcher_note": {"type": "string", "nullable": true}, "complete_tasks": {"type": "integer", "format": "int32"}, "total_tasks": {"type": "integer", "format": "int32"}, "tractor": {"type": "string", "nullable": true}, "trailer": {"type": "string", "nullable": true}}, "additionalProperties": false}, "LocalDispatchOrderFilterResponse": {"type": "object", "properties": {"pro_no": {"type": "string", "nullable": true}, "order_no": {"type": "string", "nullable": true}, "pu_no": {"type": "string", "nullable": true}, "order_status": {"type": "integer", "format": "int32"}, "task_no": {"type": "integer", "format": "int64"}, "trip_no": {"type": "string", "nullable": true}, "tms_trip_id": {"type": "integer", "format": "int64"}, "task_type": {"type": "string", "nullable": true}, "route_name": {"type": "string", "nullable": true}, "shipper_name": {"type": "string", "nullable": true}, "shipper_city": {"type": "string", "nullable": true}, "shipper_address": {"type": "string", "nullable": true}, "shipper_zipcode": {"type": "string", "nullable": true}, "shipper_state": {"type": "string", "nullable": true}, "shipper_open_time": {"type": "string", "nullable": true}, "shipper_close_time": {"type": "string", "nullable": true}, "shipper_latitude": {"type": "number", "format": "double"}, "shipper_longitude": {"type": "number", "format": "double"}, "pickup_appointment_no": {"type": "string", "nullable": true}, "shipper_appointment_date": {"type": "string", "nullable": true}, "shipper_appointment_time_from": {"type": "string", "nullable": true}, "shipper_appointment_time_to": {"type": "string", "nullable": true}, "billto_name": {"type": "string", "nullable": true}, "consingee_name": {"type": "string", "nullable": true}, "consingee_city": {"type": "string", "nullable": true}, "consingee_address": {"type": "string", "nullable": true}, "consingee_zipcode": {"type": "string", "nullable": true}, "consingee_state": {"type": "string", "nullable": true}, "consingee_open_time": {"type": "string", "nullable": true}, "consingee_close_time": {"type": "string", "nullable": true}, "consignee_latitude": {"type": "number", "format": "double"}, "consignee_longitude": {"type": "number", "format": "double"}, "delivery_appointment_no": {"type": "string", "nullable": true}, "consignee_appointment_date": {"type": "string", "nullable": true}, "consignee_appointment_time_from": {"type": "string", "nullable": true}, "consignee_appointment_time_to": {"type": "string", "nullable": true}, "consignee_terminal": {"type": "string", "nullable": true}, "from_lat": {"type": "number", "format": "double"}, "from_lng": {"type": "number", "format": "double"}, "to_lat": {"type": "number", "format": "double"}, "to_lng": {"type": "number", "format": "double"}, "pu_zone_code": {"type": "string", "nullable": true}, "plt": {"type": "integer", "format": "int32", "readOnly": true}, "weight": {"type": "number", "format": "double", "readOnly": true}, "space": {"type": "number", "format": "double", "readOnly": true}, "appt": {"type": "string", "nullable": true}, "appt_from": {"type": "string", "nullable": true}, "appt_to": {"type": "string", "nullable": true}, "user": {"type": "string", "nullable": true}, "note": {"type": "string", "nullable": true}, "reason": {"type": "string", "nullable": true}, "shipment_type": {"type": "integer", "format": "int32"}, "service_level": {"type": "integer", "format": "int32"}, "lh_appt": {"type": "string", "nullable": true}, "tms_revenue": {"type": "number", "format": "double"}, "guide_condition_fail_logs": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "LocalDispatchOrderFilterResponsePagedResultDto": {"type": "object", "properties": {"total_count": {"type": "integer", "format": "int64"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/LocalDispatchOrderFilterResponse"}, "nullable": true}}, "additionalProperties": false}, "LocalDispatchOrderFilterTabEnum": {"enum": [0, 1, 2, 3, 4, 5, 6, 7, 8], "type": "integer", "format": "int32"}, "Int32FmsSelectListItem": {"type": "object", "properties": {"text": {"type": "string", "nullable": true}, "value": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "LocalDispatchOrderTabCountResponse": {"type": "object", "properties": {"tabs": {"type": "array", "items": {"$ref": "#/components/schemas/LocalDispatchOrderTabItem"}, "nullable": true}}, "additionalProperties": false}, "LocalDispatchOrderTabItem": {"type": "object", "properties": {"tab": {"type": "string", "nullable": true}, "count": {"type": "integer", "format": "int64"}}, "additionalProperties": false}, "Int32FmsSelectListItemListResultDto": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/Int32FmsSelectListItem"}, "nullable": true}}, "additionalProperties": false}, "MapInfo": {"type": "object", "properties": {"stop_no": {"type": "integer", "format": "int64"}, "stop_type": {"type": "integer", "format": "int64"}, "sequence": {"type": "integer", "format": "int32"}, "actual_update_time": {"type": "string", "format": "date-time"}, "stop_status": {"type": "string", "nullable": true}, "latitude": {"type": "number", "format": "double"}, "longitude": {"type": "number", "format": "double"}, "stop_address1": {"type": "string", "nullable": true}, "stop_address2": {"type": "string", "nullable": true}, "stop_zipcode": {"type": "string", "nullable": true}, "stop_city": {"type": "string", "nullable": true}, "stop_state": {"type": "string", "nullable": true}, "stop_country": {"type": "string", "nullable": true}, "stop_terminal_code": {"type": "string", "nullable": true}, "stop_terminal_type": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "MapOutputDto": {"type": "object", "properties": {"trip_status": {"type": "string", "nullable": true}, "map_infos": {"type": "array", "items": {"$ref": "#/components/schemas/MapInfo"}, "nullable": true}}, "additionalProperties": false}, "MapOutputDtoSuccessResponse": {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/MapOutputDto"}, "is_success": {"type": "boolean", "readOnly": true}, "status": {"type": "string", "readOnly": true, "nullable": true}, "message": {"type": "string", "readOnly": true, "nullable": true}, "code": {"type": "integer", "format": "int32", "readOnly": true}}, "additionalProperties": false}, "PointsItem": {"type": "object", "properties": {"lat": {"type": "number", "format": "double"}, "lng": {"type": "number", "format": "double"}}, "additionalProperties": false}, "RouteEngineDispatchLocalDeliveryTaskResponse": {"type": "object", "properties": {"quantity": {"type": "integer", "format": "int32"}, "package_count": {"type": "integer", "format": "int32", "nullable": true}, "weight": {"type": "number", "format": "double"}, "volume": {"type": "number", "format": "double"}, "order_no": {"type": "string", "nullable": true}, "tracking_no": {"type": "string", "nullable": true}, "task_no": {"type": "string", "nullable": true}, "task_type": {"type": "string", "nullable": true}, "task_status": {"type": "string", "nullable": true}, "address_info": {"$ref": "#/components/schemas/RouteEngineDispatchLocalAddressInfoResponse"}, "shipment_type": {"$ref": "#/components/schemas/ShipmentTypeEnum"}, "service_level": {"$ref": "#/components/schemas/ServiceLevelEnum"}, "terminal": {"type": "string", "nullable": true}, "polygon": {"type": "string", "nullable": true}, "customer": {"type": "string", "nullable": true}, "customer_id": {"type": "integer", "format": "int32", "nullable": true}, "desire_date": {"type": "string", "format": "date-time"}, "pickup_appointment_from": {"type": "string", "nullable": true}, "pickup_appointment_to": {"type": "string", "nullable": true}, "delivery_appointment_from": {"type": "string", "nullable": true}, "delivery_appointment_to": {"type": "string", "nullable": true}, "carrier_name": {"type": "string", "nullable": true}, "driver_name": {"type": "string", "nullable": true}, "carrier_code": {"type": "string", "nullable": true}, "driver_code": {"type": "string", "nullable": true}, "state": {"type": "string", "nullable": true}, "city": {"type": "string", "nullable": true}, "zip_code": {"type": "string", "nullable": true}, "pu_no": {"type": "string", "nullable": true}, "order_status": {"type": "integer", "format": "int32"}, "order_sub_status": {"type": "integer", "format": "int32"}, "pu_zone_code": {"type": "string", "nullable": true}}, "additionalProperties": false}, "RouteEngineDispatchLocalDeliveryTaskResponsePagedResultDto": {"type": "object", "properties": {"total_count": {"type": "integer", "format": "int64"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/RouteEngineDispatchLocalDeliveryTaskResponse"}, "nullable": true}}, "additionalProperties": false}, "RouteEngineDispatchLocalPickupTaskResponse": {"type": "object", "properties": {"quantity": {"type": "integer", "format": "int32"}, "package_count": {"type": "integer", "format": "int32", "nullable": true}, "weight": {"type": "number", "format": "double"}, "volume": {"type": "number", "format": "double"}, "order_no": {"type": "string", "nullable": true}, "tracking_no": {"type": "string", "nullable": true}, "task_no": {"type": "string", "nullable": true}, "task_type": {"type": "string", "nullable": true}, "task_status": {"type": "string", "nullable": true}, "address_info": {"$ref": "#/components/schemas/RouteEngineDispatchLocalAddressInfoResponse"}, "shipment_type": {"$ref": "#/components/schemas/ShipmentTypeEnum"}, "service_level": {"$ref": "#/components/schemas/ServiceLevelEnum"}, "terminal": {"type": "string", "nullable": true}, "polygon": {"type": "string", "nullable": true}, "customer": {"type": "string", "nullable": true}, "customer_id": {"type": "integer", "format": "int32", "nullable": true}, "desire_date": {"type": "string", "format": "date-time"}, "pickup_appointment_from": {"type": "string", "nullable": true}, "pickup_appointment_to": {"type": "string", "nullable": true}, "delivery_appointment_from": {"type": "string", "nullable": true}, "delivery_appointment_to": {"type": "string", "nullable": true}, "carrier_name": {"type": "string", "nullable": true}, "driver_name": {"type": "string", "nullable": true}, "carrier_code": {"type": "string", "nullable": true}, "driver_code": {"type": "string", "nullable": true}, "state": {"type": "string", "nullable": true}, "city": {"type": "string", "nullable": true}, "zip_code": {"type": "string", "nullable": true}, "pu_no": {"type": "string", "nullable": true}, "order_status": {"type": "integer", "format": "int32"}, "order_sub_status": {"type": "integer", "format": "int32"}, "pu_zone_code": {"type": "string", "nullable": true}}, "additionalProperties": false}, "RouteEngineDispatchLocalPickupTaskResponsePagedResultDto": {"type": "object", "properties": {"total_count": {"type": "integer", "format": "int64"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/RouteEngineDispatchLocalPickupTaskResponse"}, "nullable": true}}, "additionalProperties": false}, "RouteEngineDispatchLocalServiceTaskResponse": {"type": "object", "properties": {"service_product": {"type": "string", "nullable": true}, "package_count": {"type": "integer", "format": "int32", "nullable": true}, "weight": {"type": "number", "format": "double"}, "volume": {"type": "number", "format": "double"}, "order_no": {"type": "string", "nullable": true}, "tracking_no": {"type": "string", "nullable": true}, "task_no": {"type": "string", "nullable": true}, "task_type": {"type": "string", "nullable": true}, "task_status": {"type": "string", "nullable": true}, "address_info": {"$ref": "#/components/schemas/RouteEngineDispatchLocalAddressInfoResponse"}, "shipment_type": {"$ref": "#/components/schemas/ShipmentTypeEnum"}, "service_level": {"$ref": "#/components/schemas/ServiceLevelEnum"}, "terminal": {"type": "string", "nullable": true}, "polygon": {"type": "string", "nullable": true}, "customer": {"type": "string", "nullable": true}, "customer_id": {"type": "integer", "format": "int32", "nullable": true}, "desire_date": {"type": "string", "format": "date-time"}, "pickup_appointment_from": {"type": "string", "nullable": true}, "pickup_appointment_to": {"type": "string", "nullable": true}, "delivery_appointment_from": {"type": "string", "nullable": true}, "delivery_appointment_to": {"type": "string", "nullable": true}, "carrier_name": {"type": "string", "nullable": true}, "driver_name": {"type": "string", "nullable": true}, "carrier_code": {"type": "string", "nullable": true}, "driver_code": {"type": "string", "nullable": true}, "state": {"type": "string", "nullable": true}, "city": {"type": "string", "nullable": true}, "zip_code": {"type": "string", "nullable": true}, "pu_no": {"type": "string", "nullable": true}, "order_status": {"type": "integer", "format": "int32"}, "order_sub_status": {"type": "integer", "format": "int32"}, "pu_zone_code": {"type": "string", "nullable": true}}, "additionalProperties": false}, "RouteEngineDispatchLocalServiceTaskResponsePagedResultDto": {"type": "object", "properties": {"total_count": {"type": "integer", "format": "int64"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/RouteEngineDispatchLocalServiceTaskResponse"}, "nullable": true}}, "additionalProperties": false}, "RouteEngineDispatchLocalAddressInfoResponse": {"type": "object", "properties": {"from_info": {"$ref": "#/components/schemas/RouteEngineDispatchLocalAddressItemInfoResponse"}, "to_info": {"$ref": "#/components/schemas/RouteEngineDispatchLocalAddressItemInfoResponse"}}, "additionalProperties": false}, "RouteEngineDispatchLocalAddressItemInfoResponse": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "address": {"type": "string", "nullable": true}, "state": {"type": "string", "nullable": true}, "city": {"type": "string", "nullable": true}, "zip_code": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ServiceLevelEnum": {"enum": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 30], "type": "integer", "format": "int32"}, "ShipmentTypeEnum": {"enum": [1, 2, 3, 4], "type": "integer", "format": "int32"}, "SummeryTaskInfoInputDto": {"type": "object", "properties": {"trip_no": {"type": "integer", "format": "int64"}}, "additionalProperties": false}, "SummeryTaskInfoOutputDto": {"type": "object", "properties": {"delivery_all_task": {"type": "integer", "format": "int32"}, "delivery_complete_task": {"type": "integer", "format": "int32"}, "pickup_all_task": {"type": "integer", "format": "int32"}, "pickup_complete_task": {"type": "integer", "format": "int32"}, "pickup_delivery_all_task": {"type": "integer", "format": "int32"}, "pickup_delivery_complete_task": {"type": "integer", "format": "int32"}, "linehaul_all_task": {"type": "integer", "format": "int32"}, "linehaul_complete_task": {"type": "integer", "format": "int32"}, "service_all_task": {"type": "integer", "format": "int32"}, "service_complete_task": {"type": "integer", "format": "int32"}, "large_parcel_all": {"type": "integer", "format": "int32"}, "large_parcel_complete": {"type": "integer", "format": "int32"}, "small_parcel_all": {"type": "integer", "format": "int32"}, "small_parcel_complete": {"type": "integer", "format": "int32"}, "ltl_all": {"type": "integer", "format": "int32"}, "ltl_complete": {"type": "integer", "format": "int32"}, "ftl_all": {"type": "integer", "format": "int32"}, "ftl_complete": {"type": "integer", "format": "int32"}, "work_order_all": {"type": "integer", "format": "int32"}, "work_order_complete": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "TripHistoryList": {"type": "object", "properties": {"trip_no": {"type": "string", "nullable": true}, "stop_no": {"type": "string", "nullable": true}, "task_no": {"type": "string", "nullable": true}, "service_level": {"type": "integer", "format": "int32"}, "order_no": {"type": "string", "nullable": true}, "task_type": {"type": "string", "nullable": true}, "tracking_no": {"type": "string", "nullable": true}, "package_no": {"type": "string", "nullable": true}, "event_description": {"type": "string", "nullable": true}, "operate": {"type": "string", "nullable": true}, "system_from": {"type": "string", "nullable": true}, "os": {"type": "string", "nullable": true}, "version": {"type": "string", "nullable": true}, "equipment_information": {"type": "string", "nullable": true}, "trigger_source": {"type": "string", "nullable": true}, "user": {"type": "string", "nullable": true}, "time": {"type": "string", "format": "date-time", "nullable": true}, "submit_type": {"type": "string", "nullable": true}}, "additionalProperties": false}, "TripHistoryOutputDto": {"type": "object", "properties": {"trip_history_list": {"type": "array", "items": {"$ref": "#/components/schemas/TripHistoryList"}, "nullable": true}}, "additionalProperties": false}, "TripHistoryOutputDtoSuccessResponse": {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/TripHistoryOutputDto"}, "is_success": {"type": "boolean", "readOnly": true}, "status": {"type": "string", "readOnly": true, "nullable": true}, "message": {"type": "string", "readOnly": true, "nullable": true}, "code": {"type": "integer", "format": "int32", "readOnly": true}}, "additionalProperties": false}, "TaskListItemOutput": {"type": "object", "properties": {"dispatched_count": {"type": "integer", "format": "int32"}, "inprogress_count": {"type": "integer", "format": "int32"}, "exception_count": {"type": "integer", "format": "int32"}, "complete_count": {"type": "integer", "format": "int32"}, "total_count": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "TrackingList": {"type": "object", "properties": {"task_no": {"type": "integer", "format": "int64"}, "tracking": {"type": "string", "nullable": true}, "shipment_type": {"type": "string", "nullable": true}, "task_type": {"type": "string", "nullable": true}}, "additionalProperties": false}, "TripListInPutDto": {"type": "object", "properties": {"trip_status": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}, "org_terminal": {"type": "array", "items": {"type": "string"}, "nullable": true}, "dst_terminal": {"type": "array", "items": {"type": "string"}, "nullable": true}, "optimized": {"type": "string", "nullable": true}, "carrier": {"type": "string", "nullable": true}, "driver": {"type": "string", "nullable": true}, "dispatch_date": {"type": "string", "nullable": true}, "dispatch_date_from": {"type": "string", "nullable": true}, "dispatch_date_to": {"type": "string", "nullable": true}, "trip_no": {"type": "string", "nullable": true}, "tracking_no": {"type": "string", "nullable": true}, "route_name": {"type": "string", "nullable": true}, "route_no": {"type": "string", "nullable": true}, "order_no": {"type": "string", "nullable": true}, "trailer_no": {"type": "string", "nullable": true}, "inputsearch": {"type": "string", "nullable": true}, "tms_trip_no": {"type": "string", "nullable": true}}, "additionalProperties": false}, "TripListOutPutDto": {"type": "object", "properties": {"trip_output": {"$ref": "#/components/schemas/TripOutput"}, "delivery_tasks": {"$ref": "#/components/schemas/TaskListItemOutput"}, "service_tasks": {"$ref": "#/components/schemas/TaskListItemOutput"}, "pickup_tasks": {"$ref": "#/components/schemas/TaskListItemOutput"}, "linehaul_tasks": {"$ref": "#/components/schemas/TaskListItemOutput"}}, "additionalProperties": false}, "TripOutput": {"type": "object", "properties": {"un_assign": {"type": "integer", "format": "int32"}, "assigning": {"type": "integer", "format": "int32"}, "dispatched": {"type": "integer", "format": "int32"}, "checkin": {"type": "integer", "format": "int32"}, "in_progress": {"type": "integer", "format": "int32"}, "complete": {"type": "integer", "format": "int32"}, "cancelled": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int32"}}, "additionalProperties": false}}}}