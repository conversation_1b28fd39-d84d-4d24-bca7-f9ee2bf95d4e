import {
  UserRole,
  IAM_ROLE_MAPPING,
  PROTECTED_ROUTES,
  ROLE_PROTECTED_API_ROUTES,
  getUserRolesByIamRoles as baseGetUserRolesByIamRoles,
  hasPermission as baseHasPermission
} from '@/utils/permissionConfigBase';

// 重新导出基础配置
export { UserRole, IAM_ROLE_MAPPING, PROTECTED_ROUTES, ROLE_PROTECTED_API_ROUTES };

// 检查用户是否有访问特定路由的权限
export const hasPermission = (
  userRoles: string[] | undefined,
  pathname: string
): boolean => {
  // 使用基础配置中的 hasPermission 函数
  return baseHasPermission(userRoles, pathname, PROTECTED_ROUTES);
};

import axios from 'axios';

// IAM配置
const IAM_CONFIG = {
  USER_INFO_ENDPOINT: `${process.env.NEXT_PUBLIC_IAM_ENDPOINT}/user-info`
};

// 检查环境变量
if (!process.env.NEXT_PUBLIC_IAM_ENDPOINT) {
  console.error('Missing NEXT_PUBLIC_IAM_ENDPOINT configuration');
}

// 从IAM API获取用户信息
async function getIAMUserInfo(accessToken: string) {
  try {
    // 请求IAM用户信息端点
    const response = await axios.get(IAM_CONFIG.USER_INFO_ENDPOINT, {
      headers: {
        'Authorization': `Bearer ${accessToken}`
      }
    });

    // 根据IAM文档中的响应格式进行处理
    const { data } = response;

    if (data.success) {
      return data.data;
    } else {
      console.error('Failed to get IAM user info:', data.msg);
      return null;
    }
  } catch (error) {
    console.error('Error getting IAM user info:', error);
    return null;
  }
}

// 从角色 ID 获取角色名称 - 服务端版本
// 使用IAM API获取用户信息和角色
export const getRoleNamesByIds = async (roleIds: string[] | undefined, accessToken?: string): Promise<string[]> => {
  if (!roleIds || roleIds.length === 0) {
    return [];
  }

  if (!accessToken) {
    console.error('No access token provided, cannot get IAM user info');
    return [];
  }

  try {
    // 从IAM API获取用户信息
    const userInfo = await getIAMUserInfo(accessToken);

    if (!userInfo) {
      console.error('Failed to get IAM user info');
      return [];
    }

    // 从用户信息中提取角色
    if (!userInfo.userRoles || !Array.isArray(userInfo.userRoles)) {
      return [];
    }

    // 创建角色ID到角色名称的映射
    const roleIdToName: Record<string, string> = {};

    // 填充映射
    userInfo.userRoles.forEach((role: any) => {
      if (role && role.id && role.name) {
        roleIdToName[role.id] = role.name;
      }
    });

    const roleNames: string[] = [];

    // 将角色 ID 映射到角色名称
    roleIds.forEach(roleId => {
      const roleName = roleIdToName[roleId];
      if (roleName && !roleNames.includes(roleName)) {
        roleNames.push(roleName);
      }
    });

    return roleNames;
  } catch (error) {
    console.error('Error in getRoleNamesByIds:', error);
    return [];
  }
};

// 从 IAM 角色名称获取系统角色
export const getUserRolesByIamRoles = (iamRoles: string[] | undefined): UserRole[] => {
  // 使用基础配置中的 getUserRolesByIamRoles 函数
  return baseGetUserRolesByIamRoles(iamRoles);
};
