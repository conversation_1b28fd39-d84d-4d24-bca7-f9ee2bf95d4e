{"openapi": "3.0.1", "info": {"title": "WMS3.0", "description": "", "version": "1.0.0"}, "tags": [], "paths": {"/wms-bam/outbound/pick-task/search-by-paging": {"post": {"summary": "Search Pick Task by Paging", "deprecated": false, "description": "", "tags": [], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PickTaskQuery", "description": ""}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RPageResultPickTaskDto", "description": ""}, "example": {"code": 0, "msg": "", "success": false, "data": {"list": [{"id": "", "status": "", "note": "", "tags": [""], "preAssigneeUserId": "", "assigneeUserId": "", "lastAssignedWhen": "", "priority": "", "startTime": "", "endTime": "", "taskSteps": [{"id": 0, "customerId": "", "status": "", "exceptionCode": "", "sysNote": "", "note": "", "stepType": "", "taskId": "", "taskType": "", "stepSequence": 0, "assigneeUserIds": [""], "lastAssignedWhen": "", "priority": "", "startTime": "", "endTime": ""}], "createdTime": "", "createdBy": "", "updatedTime": "", "updatedBy": "", "orderPlanId": "", "pickType": "", "pickMethod": "", "taskHLPId": "", "isRush": false, "skipCLP": false, "shippingRule": "", "exceptionCode": "", "sysNote": "", "orderIds": [""]}], "totalCount": 0, "currentPage": 0, "pageSize": 0, "totalPage": 0}}}}, "headers": {}}}, "security": [{"oauth21": ["profile", "email", "phone", "openid"]}]}}}, "components": {"schemas": {"TaskStepDto": {"type": "object", "properties": {"id": {"type": "integer", "description": "", "format": "int64"}, "customerId": {"type": "string", "description": ""}, "status": {"type": "string", "description": "", "enum": ["NEW", "IN_PROGRESS", "EXCEPTION", "CLOSED", "FORCE_CLOSED", "CANCELLED"]}, "exceptionCode": {"type": "string", "description": ""}, "sysNote": {"type": "string", "description": ""}, "note": {"type": "string", "description": ""}, "stepType": {"type": "string", "description": "", "enum": ["OFFLOAD", "LP_SETUP", "SN_SCAN", "PICK", "STAGE", "SORTING_TO_WALL", "STAGE_TO_WALL", "ORDER_PICK_FROM_WALL", "REPLENISH", "GENERAL", "COLLECT", "DROP", "PUT_AWAY", "LOADING", "TRANSLOAD_RECEIVE", "TRANSLOAD_LOAD", "PUT_BACK", "PACK", "CLP_BONDING", "INTERNAL_TRANSFER_OUT", "INTERNAL_TRANSFER_RECEIVING", "MATERIAL_RECEIVING", "ASSEMBLY_PICK", "ASSEMBLY_PUTAWAY", "DOCK_CHECK_IN", "INSPECTION_CONTENT", "FIX_CONTENT"]}, "taskId": {"type": "string", "description": ""}, "taskType": {"type": "string", "description": "", "enum": ["GENERAL", "RECEIVE", "PUT_AWAY", "PICK", "PICK_REGULAR", "PICK_DROPSHIP", "LOAD", "REPLENISH", "MOVEMENT", "PUT_BACK", "CYCLE_COUNT", "TRANSLOAD_RECEIVE", "TRANSLOAD_LOAD", "CLP_BONDING", "PACK", "INTERNAL_TRANSFER_OUT", "INTERNAL_TRANSFER_RECEIVE", "ASSEMBLY", "QC"]}, "stepSequence": {"type": "integer", "description": ""}, "assigneeUserIds": {"type": "array", "items": {"type": "string"}, "description": ""}, "lastAssignedWhen": {"type": "string", "description": ""}, "priority": {"type": "string", "description": "", "enum": ["SKIPPED", "LOW", "MIDDLE", "HIGH", "TOP", "USER_HIGH"]}, "startTime": {"type": "string", "description": ""}, "endTime": {"type": "string", "description": ""}}}, "PickItemLineDto": {"type": "object", "properties": {"createdTime": {"type": "string", "description": ""}, "createdBy": {"type": "string", "description": ""}, "updatedTime": {"type": "string", "description": ""}, "updatedBy": {"type": "string", "description": ""}, "id": {"type": "string", "description": ""}, "pickTaskId": {"type": "string", "description": ""}, "itemId": {"type": "string", "description": ""}, "uomId": {"type": "string", "description": ""}, "titleId": {"type": "string", "description": ""}, "lotNo": {"type": "string", "description": ""}, "qty": {"type": "number", "description": ""}, "baseQty": {"type": "number", "description": ""}, "goodsType": {"type": "string", "description": ""}, "orderId": {"type": "string", "description": ""}, "locationId": {"type": "string", "description": ""}}}, "PickTaskDto": {"type": "object", "properties": {"id": {"type": "string", "description": ""}, "customerId": {"type": "string", "description": ""}, "status": {"type": "string", "description": "", "enum": ["NEW", "IN_PROGRESS", "EXCEPTION", "NEEDS_APPROVAL", "CLOSED", "FORCE_CLOSED", "CANCELLED"]}, "note": {"type": "string", "description": ""}, "tags": {"type": "array", "items": {"type": "string"}, "description": ""}, "preAssigneeUserId": {"type": "string", "description": ""}, "assigneeUserId": {"type": "string", "description": ""}, "lastAssignedWhen": {"type": "string", "description": ""}, "priority": {"type": "string", "description": "", "enum": ["LOW", "MIDDLE", "HIGH", "TOP"]}, "startTime": {"type": "string", "description": ""}, "endTime": {"type": "string", "description": ""}, "taskSteps": {"type": "array", "items": {"$ref": "#/components/schemas/TaskStepDto", "description": "com.item.wms.application.task.common.dto.TaskStepDto"}, "description": ""}, "projectId": {"type": "string", "description": ""}, "createdTime": {"type": "string", "description": ""}, "createdBy": {"type": "string", "description": ""}, "updatedTime": {"type": "string", "description": ""}, "updatedBy": {"type": "string", "description": ""}, "orderPlanId": {"type": "string", "description": ""}, "pickType": {"type": "string", "description": "", "enum": ["PALLET_PICK", "CASE_PICK", "PIECE_PICK", "NONE"]}, "pickMethod": {"type": "string", "description": "", "enum": ["ORDER_PICK", "WAVE_PICK_BY_ITEM", "WAVE_PICK_BY_ORDER", "BATCH_ORDER_PICK"]}, "pickMode": {"type": "string", "description": "", "enum": ["PICK_TO_WALL", "OPPORTUNITY_PICK", "ROBOTIC_SORTING", "PICK_BY_TOTE_CART"]}, "taskHLPId": {"type": "string", "description": ""}, "isRush": {"type": "boolean", "description": ""}, "skipCLP": {"type": "boolean", "description": ""}, "shippingRule": {"type": "string", "description": "", "enum": ["FIFO", "LIFO", "FEFO", "LSFO", "LEFO"]}, "exceptionCode": {"type": "string", "description": ""}, "sysNote": {"type": "string", "description": ""}, "orderIds": {"type": "array", "items": {"type": "string"}, "description": ""}, "pickItemLines": {"type": "array", "items": {"$ref": "#/components/schemas/PickItemLineDto", "description": "com.item.wms.application.task.pick.dto.PickItemLineDto"}, "description": ""}}}, "PickTaskQuery": {"type": "object", "properties": {"currentPage": {"type": "integer", "description": ""}, "pageSize": {"type": "integer", "description": ""}, "sortingFields": {"type": "array", "items": {"$ref": "#/components/schemas/SortingField", "description": "com.item.xms.persistence.query.SortingField"}, "description": ""}, "id": {"type": "string", "description": ""}, "ids": {"type": "array", "items": {"type": "string"}, "description": ""}, "orderPlanId": {"type": "string", "description": ""}, "orderPlanIds": {"type": "array", "items": {"type": "string"}, "description": ""}, "pickType": {"type": "string", "description": ""}, "pickTypes": {"type": "array", "items": {"type": "string"}, "description": ""}, "pickMethod": {"type": "string", "description": ""}, "pickMethods": {"type": "array", "items": {"type": "string"}, "description": ""}, "excludeStatuses": {"type": "array", "items": {"type": "string", "enum": ["NEW", "IN_PROGRESS", "EXCEPTION", "NEEDS_APPROVAL", "CLOSED", "FORCE_CLOSED", "CANCELLED"]}, "description": ""}, "taskHLPId": {"type": "string", "description": ""}, "taskHLPIds": {"type": "array", "items": {"type": "string"}, "description": ""}, "isRush": {"type": "boolean", "description": ""}, "skipCLP": {"type": "boolean", "description": ""}, "shippingRule": {"type": "string", "description": ""}, "shippingRules": {"type": "array", "items": {"type": "string"}, "description": ""}, "exceptionCode": {"type": "string", "description": ""}, "sysNote": {"type": "string", "description": ""}, "orderIds": {"type": "array", "items": {"type": "string"}, "description": ""}, "customerId": {"type": "string", "description": ""}, "customerIds": {"type": "array", "items": {"type": "string"}, "description": ""}, "status": {"type": "string", "description": "", "enum": ["NEW", "IN_PROGRESS", "EXCEPTION", "NEEDS_APPROVAL", "CLOSED", "FORCE_CLOSED", "CANCELLED"]}, "statuses": {"type": "array", "items": {"type": "string"}, "description": ""}, "note": {"type": "string", "description": ""}, "tags": {"type": "array", "items": {"type": "string"}, "description": ""}, "preAssigneeUserId": {"type": "string", "description": ""}, "preAssigneeUserIds": {"type": "array", "items": {"type": "string"}, "description": ""}, "assigneeUserId": {"type": "string", "description": ""}, "assigneeUserIds": {"type": "array", "items": {"type": "string"}, "description": ""}, "lastAssignedWhenFrom": {"type": "string", "description": ""}, "lastAssignedWhenTo": {"type": "string", "description": ""}, "priority": {"type": "string", "description": "", "enum": ["LOW", "MIDDLE", "HIGH", "TOP"]}, "startTimeFrom": {"type": "string", "description": ""}, "startTimeTo": {"type": "string", "description": ""}, "endTimeFrom": {"type": "string", "description": ""}, "endTimeTo": {"type": "string", "description": ""}, "pickMode": {"type": "string", "description": "", "enum": ["PICK_TO_WALL", "OPPORTUNITY_PICK", "ROBOTIC_SORTING", "PICK_BY_TOTE_CART"]}, "withTaskSteps": {"type": "boolean", "description": ""}, "locationIds": {"type": "array", "items": {"type": "string"}, "description": ""}, "itemIds": {"type": "array", "items": {"type": "string"}, "description": ""}}}, "SortingField": {"type": "object", "properties": {"field": {"type": "string", "description": ""}, "orderBy": {"type": "string", "description": "", "enum": ["NONE", "ASC", "DESC"]}}}, "RPageResultPickTaskDto": {"type": "object", "properties": {"code": {"type": "integer", "description": ""}, "msg": {"type": "string", "description": ""}, "success": {"type": "boolean", "description": ""}, "data": {"$ref": "#/components/schemas/PageResultPickTaskDto", "description": ""}}}, "PageResultPickTaskDto": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/components/schemas/PickTaskDto", "description": "com.item.wms.application.task.pick.dto.PickTaskDto"}, "description": ""}, "totalCount": {"type": "integer", "description": ""}, "currentPage": {"type": "integer", "description": ""}, "pageSize": {"type": "integer", "description": ""}, "totalPage": {"type": "integer", "description": ""}}}}, "securitySchemes": {"oauth21": {"type": "oauth2", "flows": {"password": {"authorizationUrl": "{{authUrl}}", "tokenUrl": "{{accessTokenUrl}}", "refreshUrl": "", "scopes": {"profile": "", "email": "", "phone": "", "openid": ""}}}}}}, "servers": [], "security": [{"oauth21": ["profile", "email", "phone", "openid"]}]}