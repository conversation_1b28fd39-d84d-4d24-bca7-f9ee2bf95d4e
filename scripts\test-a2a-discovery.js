#!/usr/bin/env node

/**
 * A2A协议发现流程测试
 * 测试Super Agent的agent发现功能
 */

const http = require('http');

async function testA2ADiscovery() {
  console.log('🔍 Testing A2A Discovery Flow...\n');
  console.log('=' .repeat(60));
  
  try {
    // 1. 测试Super Agent的发现API
    console.log('1️⃣ Testing Super Agent Discovery API...');
    const discoveryResult = await testDiscoveryAPI();
    
    if (!discoveryResult.success) {
      console.log('❌ Discovery API test failed');
      return;
    }
    
    console.log('✅ Discovery API test passed');
    console.log(`   Found ${discoveryResult.agents.length} agents`);
    
    // 2. 测试每个发现的agent的Agent Card
    console.log('\n2️⃣ Testing discovered agents\' Agent Cards...');
    
    for (const agent of discoveryResult.agents) {
      console.log(`\n🔍 Testing ${agent.name} (${agent.id})`);
      console.log(`   URL: ${agent.url}`);
      console.log(`   Status: ${agent.status}`);
      console.log(`   Agent Card: ${agent.agentCardUrl}`);
      
      if (agent.status === 'online') {
        const cardResult = await testAgentCard(agent.agentCardUrl);
        if (cardResult.success) {
          console.log(`   ✅ Agent Card OK - ${cardResult.agentCard.name}`);
          console.log(`   📋 Capabilities: ${cardResult.agentCard.capabilities?.length || 0}`);
          console.log(`   🎯 Skills: ${cardResult.agentCard.skills?.length || 0}`);
        } else {
          console.log(`   ❌ Agent Card failed: ${cardResult.error}`);
        }
      } else {
        console.log(`   ⚠️ Agent offline, skipping Agent Card test`);
      }
    }
    
    // 3. 测试重新发现功能
    console.log('\n3️⃣ Testing rediscovery functionality...');
    const rediscoveryResult = await testRediscovery();
    
    if (rediscoveryResult.success) {
      console.log('✅ Rediscovery test passed');
      console.log(`   Rediscovered ${rediscoveryResult.agents.length} agents`);
    } else {
      console.log('❌ Rediscovery test failed');
    }
    
    // 4. 测试发现状态
    console.log('\n4️⃣ Testing discovery status...');
    const statusResult = await testDiscoveryStatus();
    
    if (statusResult.success) {
      console.log('✅ Discovery status test passed');
      console.log(`   Total agents: ${statusResult.data.totalAgents}`);
      console.log(`   Online agents: ${statusResult.data.onlineAgents}`);
      console.log(`   Offline agents: ${statusResult.data.offlineAgents}`);
    } else {
      console.log('❌ Discovery status test failed');
    }
    
    console.log('\n' + '=' .repeat(60));
    console.log('🎉 A2A Discovery Flow Test Completed!');
    console.log('=' .repeat(60));
    
  } catch (error) {
    console.error('❌ Test failed:', error);
    process.exit(1);
  }
}

// 测试发现API
function testDiscoveryAPI() {
  return new Promise((resolve) => {
    const req = http.get('http://localhost:3000/api/a2a/discovery', (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          if (res.statusCode === 200) {
            const result = JSON.parse(data);
            resolve({
              success: true,
              agents: result.agents || []
            });
          } else {
            resolve({
              success: false,
              error: `HTTP ${res.statusCode}`,
              response: data
            });
          }
        } catch (error) {
          resolve({
            success: false,
            error: error.message,
            response: data
          });
        }
      });
    });
    
    req.on('error', (error) => {
      resolve({
        success: false,
        error: error.message
      });
    });
    
    req.setTimeout(10000, () => {
      req.destroy();
      resolve({
        success: false,
        error: 'Timeout'
      });
    });
  });
}

// 测试Agent Card
function testAgentCard(agentCardUrl) {
  return new Promise((resolve) => {
    const req = http.get(agentCardUrl, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          if (res.statusCode === 200) {
            const agentCard = JSON.parse(data);
            resolve({
              success: true,
              agentCard
            });
          } else {
            resolve({
              success: false,
              error: `HTTP ${res.statusCode}`,
              response: data
            });
          }
        } catch (error) {
          resolve({
            success: false,
            error: error.message,
            response: data
          });
        }
      });
    });
    
    req.on('error', (error) => {
      resolve({
        success: false,
        error: error.message
      });
    });
    
    req.setTimeout(5000, () => {
      req.destroy();
      resolve({
        success: false,
        error: 'Timeout'
      });
    });
  });
}

// 测试重新发现
function testRediscovery() {
  return new Promise((resolve) => {
    const req = http.get('http://localhost:3000/api/a2a/discovery?action=rediscover', (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          if (res.statusCode === 200) {
            const result = JSON.parse(data);
            resolve({
              success: true,
              agents: result.data?.agents || []
            });
          } else {
            resolve({
              success: false,
              error: `HTTP ${res.statusCode}`,
              response: data
            });
          }
        } catch (error) {
          resolve({
            success: false,
            error: error.message,
            response: data
          });
        }
      });
    });
    
    req.on('error', (error) => {
      resolve({
        success: false,
        error: error.message
      });
    });
    
    req.setTimeout(15000, () => {
      req.destroy();
      resolve({
        success: false,
        error: 'Timeout'
      });
    });
  });
}

// 测试发现状态
function testDiscoveryStatus() {
  return new Promise((resolve) => {
    const req = http.get('http://localhost:3000/api/a2a/discovery?action=status', (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          if (res.statusCode === 200) {
            const result = JSON.parse(data);
            resolve({
              success: true,
              data: result.data
            });
          } else {
            resolve({
              success: false,
              error: `HTTP ${res.statusCode}`,
              response: data
            });
          }
        } catch (error) {
          resolve({
            success: false,
            error: error.message,
            response: data
          });
        }
      });
    });
    
    req.on('error', (error) => {
      resolve({
        success: false,
        error: error.message
      });
    });
    
    req.setTimeout(5000, () => {
      req.destroy();
      resolve({
        success: false,
        error: 'Timeout'
      });
    });
  });
}

// 运行测试
testA2ADiscovery().catch(error => {
  console.error('❌ Test failed:', error);
  process.exit(1);
}); 