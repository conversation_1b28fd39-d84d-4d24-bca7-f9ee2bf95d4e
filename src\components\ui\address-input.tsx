'use client';

import * as React from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { CustomSelect } from '@/components/ui/custom-select';

// 美国州列表 - 显示全名，值为缩写
const US_STATES = [
  { label: 'Alabama', value: 'AL' },
  { label: 'Alaska', value: 'AK' },
  { label: 'Arizona', value: 'AZ' },
  { label: 'Arkansas', value: 'AR' },
  { label: 'California', value: 'CA' },
  { label: 'Colorado', value: 'CO' },
  { label: 'Connecticut', value: 'CT' },
  { label: 'Delaware', value: 'DE' },
  { label: 'Florida', value: 'FL' },
  { label: 'Georgia', value: 'GA' },
  { label: 'Hawaii', value: 'HI' },
  { label: 'Idaho', value: 'ID' },
  { label: 'Illinois', value: 'IL' },
  { label: 'Indiana', value: 'IN' },
  { label: 'Iowa', value: 'IA' },
  { label: 'Kansas', value: 'KS' },
  { label: 'Kentucky', value: 'KY' },
  { label: 'Louisiana', value: 'LA' },
  { label: 'Maine', value: 'ME' },
  { label: 'Maryland', value: 'MD' },
  { label: 'Massachusetts', value: 'MA' },
  { label: 'Michigan', value: 'MI' },
  { label: 'Minnesota', value: 'MN' },
  { label: 'Mississippi', value: 'MS' },
  { label: 'Missouri', value: 'MO' },
  { label: 'Montana', value: 'MT' },
  { label: 'Nebraska', value: 'NE' },
  { label: 'Nevada', value: 'NV' },
  { label: 'New Hampshire', value: 'NH' },
  { label: 'New Jersey', value: 'NJ' },
  { label: 'New Mexico', value: 'NM' },
  { label: 'New York', value: 'NY' },
  { label: 'North Carolina', value: 'NC' },
  { label: 'North Dakota', value: 'ND' },
  { label: 'Ohio', value: 'OH' },
  { label: 'Oklahoma', value: 'OK' },
  { label: 'Oregon', value: 'OR' },
  { label: 'Pennsylvania', value: 'PA' },
  { label: 'Rhode Island', value: 'RI' },
  { label: 'South Carolina', value: 'SC' },
  { label: 'South Dakota', value: 'SD' },
  { label: 'Tennessee', value: 'TN' },
  { label: 'Texas', value: 'TX' },
  { label: 'Utah', value: 'UT' },
  { label: 'Vermont', value: 'VT' },
  { label: 'Virginia', value: 'VA' },
  { label: 'Washington', value: 'WA' },
  { label: 'West Virginia', value: 'WV' },
  { label: 'Wisconsin', value: 'WI' },
  { label: 'Wyoming', value: 'WY' },
  { label: 'District of Columbia', value: 'DC' }
];

interface AddressData {
  name?: string;
  street?: string;
  street2?: string;
  city?: string;
  state?: string;
  zip?: string;
  phone?: string;
}

interface AddressInputProps {
  value?: AddressData;
  onChange?: (value: AddressData) => void;
  placeholder?: string;
  required?: boolean;
  label?: string;
  showName?: boolean;
  showPhone?: boolean;
  className?: string;
}

export function AddressInput({
  value = {},
  onChange,
  placeholder = "Enter address",
  required = false,
  label = "Address",
  showName = true,
  showPhone = true,
  className = ""
}: AddressInputProps) {
  const handleFieldChange = (field: keyof AddressData, fieldValue: string) => {
    const newValue = {
      ...value,
      [field]: fieldValue
    };
    onChange?.(newValue);
  };

  return (
    <div className={`space-y-3 ${className}`}>
      {showName && (
        <div>
          <Label className="text-gray-300 text-sm">
            Name {required && <span className="text-red-400">*</span>}
          </Label>
          <Input
            value={value.name || ''}
            onChange={(e) => handleFieldChange('name', e.target.value)}
            placeholder="Company or person name"
            className="bg-gray-800 border-gray-700 text-white focus:ring-1 focus:ring-blue-500 focus:border-blue-500 transition placeholder:text-gray-500"
            required={required}
          />
        </div>
      )}
      
      <div>
        <Label className="text-gray-300 text-sm">
          Street Address {required && <span className="text-red-400">*</span>}
        </Label>
        <Input
          value={value.street || ''}
          onChange={(e) => handleFieldChange('street', e.target.value)}
          placeholder="Street address"
          className="bg-gray-800 border-gray-700 text-white focus:ring-1 focus:ring-blue-500 focus:border-blue-500 transition placeholder:text-gray-500"
          required={required}
        />
      </div>
      
      <div>
        <Label className="text-gray-300 text-sm">Street Address 2</Label>
        <Input
          value={value.street2 || ''}
          onChange={(e) => handleFieldChange('street2', e.target.value)}
          placeholder="Apartment, suite, etc. (optional)"
          className="bg-gray-800 border-gray-700 text-white focus:ring-1 focus:ring-blue-500 focus:border-blue-500 transition placeholder:text-gray-500"
        />
      </div>
      
      <div>
        <Label className="text-gray-300 text-sm">
          City {required && <span className="text-red-400">*</span>}
        </Label>
        <Input
          value={value.city || ''}
          onChange={(e) => handleFieldChange('city', e.target.value)}
          placeholder="City"
          className="bg-gray-800 border-gray-700 text-white focus:ring-1 focus:ring-blue-500 focus:border-blue-500 transition placeholder:text-gray-500"
          required={required}
        />
      </div>
      
      <div className="grid grid-cols-2 gap-3">
        <div>
          <Label className="text-gray-300 text-sm">
            State {required && <span className="text-red-400">*</span>}
          </Label>
          <CustomSelect
            value={value.state || ''}
            onChange={(selectedValue) => handleFieldChange('state', selectedValue)}
            options={US_STATES}
            placeholder="Select State"
          />
        </div>
        
        <div>
          <Label className="text-gray-300 text-sm">
            ZIP Code {required && <span className="text-red-400">*</span>}
          </Label>
          <Input
            value={value.zip || ''}
            onChange={(e) => handleFieldChange('zip', e.target.value)}
            placeholder="ZIP code"
            className="bg-gray-800 border-gray-700 text-white focus:ring-1 focus:ring-blue-500 focus:border-blue-500 transition placeholder:text-gray-500"
            required={required}
          />
        </div>
      </div>

      
      {showPhone && (
        <div>
          <Label className="text-gray-300 text-sm">Phone</Label>
          <Input
            value={value.phone || ''}
            onChange={(e) => handleFieldChange('phone', e.target.value)}
            placeholder="Phone number (optional)"
            className="bg-gray-800 border-gray-700 text-white focus:ring-1 focus:ring-blue-500 focus:border-blue-500 transition placeholder:text-gray-500"
          />
        </div>
      )}

    </div>
  );
} 