# Performance Monitoring for Chat Components

This document describes the performance monitoring and logging system added to analyze rendering performance and identify frequent re-renders in the Chat application.

## What Was Added

### 1. Chat.tsx Performance Monitoring

#### Core Logging Functions
- `logRender()`: Logs component renders with timestamps and reasons
- `logStateChange()`: Tracks state changes between renders

#### Render Tracking
- **Render Counter**: Tracks total number of renders for the Chat component
- **State Monitoring**: Monitors all major state variables for changes
- **Props Comparison**: Detects which specific state values changed between renders

#### Critical Performance Points
- **Message Rendering**: Logs when first and last messages are rendered with details
- **Scroll Operations**: Monitors scroll-to-bottom triggers and skips
- **Voice Processing**: Tracks voice-related callback executions
- **Save Operations**: Monitors chat save function calls and skips
- **Effect Triggers**: Logs when key useEffect hooks are triggered

### 2. ChatMessage.tsx Performance Monitoring

#### Component-Level Tracking
- **Individual Message Renders**: Each ChatMessage tracks its own render count
- **Props Change Detection**: Identifies which props changed to cause re-renders
- **Tool Component Monitoring**: Tracks renders of tool cards and sub-components

#### React.memo Optimization
- **Custom Comparison Function**: Deep comparison of message properties
- **Skip Logging**: Logs when renders are successfully skipped
- **Re-render Reasoning**: Explains why components need to re-render

#### Sub-Component Monitoring
- **ToolResultCollapsible**: Tracks expansion/collapse operations
- **FinishTaskCard**: Monitors task completion card renders
- **ToolInvocationGroup**: Logs tool group rendering
- **requireUserInputTool**: Tracks form memoization recalculations

## Log Format

All performance logs use the prefix `[PERF-Chat]` or `[PERF-ChatMessage]` and include:

```javascript
{
  timestamp: Date.now(),
  component: 'ComponentName',
  messageId?: 'message-id',
  reason?: 'render reason',
  // Additional context data
}
```

## How to Use

### 1. Open Browser Developer Tools
Open the browser console to see the performance logs.

### 2. Trigger AI Responses
Start a conversation with the AI to see rendering behavior, especially when:
- Tool cards are displayed
- Messages are streaming
- Voice processing is active
- Multiple messages are in the conversation

### 3. Look for Performance Issues

#### High Frequency Renders
```
[PERF-Chat] Chat rendered - Render #45
[PERF-Chat] State change: isThinking
[PERF-Chat] Chat rendered - Render #46
```

#### Unnecessary Re-renders
```
[PERF-ChatMessage] Re-render detected for message msg-123:
  changedProps: ["onUserInputSubmit"]
```

#### Successful Optimizations
```
[PERF-ChatMessage] Skipping re-render for message msg-123
```

### 4. Analyze Patterns

Look for:
- **Rapid consecutive renders** - indicates potential performance issues
- **Function reference changes** - callback dependencies causing re-renders
- **State thrashing** - rapid state changes back and forth
- **Message array mutations** - unnecessary message list updates

## Common Issues to Watch For

### 1. Callback Dependencies
- Functions passed as props to ChatMessage changing on every render
- useCallback dependencies causing function recreation

### 2. State Updates
- Rapid setState calls during AI streaming
- Status changes triggering multiple re-renders
- Voice processing state changes

### 3. Message Array Changes
- Adding/updating messages triggering full re-render of all messages
- Parts array changes in individual messages
- Tool invocation state updates

### 4. Scroll Operations
- Frequent scroll triggers during streaming
- Multiple scroll calculations per message update

## Performance Optimization Tips

Based on the logging data, consider:

1. **Memoize Callbacks**: Ensure callback functions have stable references
2. **Debounce State Updates**: Group rapid state changes together
3. **Optimize Message Updates**: Update only changed message parts
4. **Lazy Rendering**: Only render visible messages for long conversations
5. **Tool Card Memoization**: Cache expensive tool card calculations

## Removing the Logging

To remove the performance logging for production:

1. Remove the `PERF_LOG_PREFIX` constants and logging functions
2. Remove `logRender()` and `logStateChange()` calls
3. Keep the React.memo optimizations and useCallback improvements
4. Remove render counters and monitoring useEffects

The actual performance optimizations (React.memo, memoization) should remain as they provide real benefits.