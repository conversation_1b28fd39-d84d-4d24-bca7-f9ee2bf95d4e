import { tool } from 'ai';
import { z } from 'zod';
import { v4 as uuidv4 } from 'uuid';
import { BaseTool } from './baseTool';
import { ToolDefinition } from './types';

/**
 * IoT Tools
 * Used to control and monitor IoT devices including robot dogs and cameras
 */

// Robot dog patrol tool
export const robotDogPatrolTool = tool({
  description: 'Control robot dog patrol operations - send robot dog to specific routes ， will return a url for the patrol video stream if success',
  parameters: z.object({
    facility_id: z.enum(['cubework-ca-fontana-production', 'yard-25']).optional().default('cubework-ca-fontana-production').describe('The facility ID where the robot dog operates. cubework-ca-fontana-production for Fontana, yard-25 for Valleyview'),
    dog_id: z.string().optional().default('rbt_dog').describe('The robot dog ID'),
    type: z.string().optional().default('route').describe('Type of navigation - always route'),
    spot: z.number().optional().default(1).describe('Route spot number')
  }),
  execute: async ({ facility_id = 'cubework-ca-fontana-production', dog_id = 'rbt_dog', type = 'route', spot = 1 }) => {
 
    
    try {
      // Build request data
      const requestData: any = {
        facility_id,
        dog_id,
        type,
        spot
      };

      console.log('RobotDogPatrolTool.execute requestData:', JSON.stringify(requestData));
      // Call robot dog API
      const response = await fetch('https://p5rhcfdc72.execute-api.us-west-2.amazonaws.com/v1/robotdog/goto', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData)
      });

      if (!response.ok) {
        throw new Error(`Robot dog API error: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      
      console.log('RobotDogPatrolTool.execute response:', JSON.stringify(result));
      
      // 如果成功，修改 live URL
      
      const facilityName = facility_id === 'cubework-ca-fontana-production' ? 'Fontana' : 'Valleyview';
      const title = `Robot Dog Live Feed - ${facilityName}`;
      const fixedLiveUrl = 'https://srs.unisco.com/live/viewer.html?channels=robot';
        
        // Remove the original live field and add the fixed one
      const { live: originalLive, ...resultWithoutLive } = result;
        
      return {
          ...resultWithoutLive,
          instruct: "Don't show the live URL to the user, just tell them the robot dog patrol is active and the live feed is ready",
          artifact: {
            id: uuidv4(),
            type: 'iframe' as const,
            title: title,
            props: {
              src: fixedLiveUrl,
              width: '100%',
              height: '100%',
              style: {
                minHeight: '700px'
              }
            }
          }
      };
      
      

    } catch (error) {
      console.error('RobotDogPatrolTool.execute error:', error);
      
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      return {
        error: errorMessage,
        message: `Failed to initiate robot dog patrol: ${errorMessage}`
      };
    }
  }
});

// Camera live stream tool
export const cameraLiveStreamTool = tool({
  description: 'Start camera live streams for warehouse monitoring. Returns viewer URL for the live camera feed. IMPORTANT: Can start multiple cameras in ONE call using cameras array - do NOT make separate calls for each camera.',
  parameters: z.object({
    yard: z.enum(['cubework-ca-fontana-production', 'yard-25']).optional().default('cubework-ca-fontana-production')
      .describe('The yard/facility to view cameras from. cubework-ca-fontana-production for Fontana, yard-25 for Valleyview'),
    cameras: z.array(z.object({
      ip: z.string().describe('Camera IP address'),
      camera: z.string().describe('Camera name/identifier')
    })).optional().describe('Array of cameras to start in ONE call. For multiple cameras (e.g. spot2 and spot3), include ALL in this array. Each camera needs ip and camera name. Available cameras: Fontana - Spot1: {ip:"**************", camera:"roofing-camera-1"}, Spot2: {ip:"**************", camera:"roofing-camera-2"}, Spot3: {ip:"**************", camera:"roofing-camera-3"}. Valleyview - Spot1: {ip:"***************", camera:"roofing-camera-1"}. Example for multiple: [{ip:"**************", camera:"roofing-camera-2"}, {ip:"**************", camera:"roofing-camera-3"}]')
  }),
  execute: async ({ yard = 'cubework-ca-fontana-production', cameras }) => {
    try {
      let items: any[] = [];
      
      // Handle cameras array
      if (cameras && cameras.length > 0) {
        items = cameras.map(cam => ({
          ip: cam.ip,
          camera: cam.camera
        }));
      }
      // Default to first camera of the yard
      else {
        const defaultCameras: Record<string, any> = {
          'cubework-ca-fontana-production': { ip: '**************', camera: 'roofing-camera-1' },
          'yard-25': { ip: '***************', camera: 'roofing-camera-1' }
        };
        items = [defaultCameras[yard]];
      }

      // Build channels parameter for URL
      const channels = items.map(item => item.camera).join('|');
      const baseUrl = 'https://srs.unisco.com/live/viewer.html';
      const viewer_url = `${baseUrl}?channels=${channels}&embedded=true&compact=true`;
      
      console.log('CameraLiveStreamTool.execute generated URL:', viewer_url);
      
      const facilityName = yard === 'cubework-ca-fontana-production' ? 'Fontana' : 'Valleyview';
      
      // Generate title based on cameras
      let title = `Camera Live Feed - ${facilityName}`;
      if (cameras && cameras.length > 0) {
        if (cameras.length === 1) {
          title += ` - ${cameras[0].camera}`;
        } else {
          title += ` - ${cameras.length} Cameras`;
        }
      } else {
        // Default camera case
        title += ` - roofing-camera-1`;
      }
      
      return {
        status: 'streams_started',
        viewer_url,
        message: 'Camera streams are ready',
        instruct: "Don't show the viewer URL to the user, just tell them the camera live stream is active and ready to view",
        artifact: {
          id: uuidv4(),
          type: 'iframe' as const,
          title: title,
          props: {
            src: viewer_url,
            width: '100%',
            height: '100%',
            style: {
              minHeight: '700px'
            }
          }
        }
      };

    } catch (error) {
      console.error('CameraLiveStreamTool.execute error:', error);
      
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      return {
        error: errorMessage,
        message: `Failed to generate camera live stream URL: ${errorMessage}`
      };
    }
  }
});

/**
 * IoT Tool class, extends BaseTool
 * Adapts to legacy tool system
 */
export class IoTTool extends BaseTool {
  constructor() {
    super();
  }
  
  getTools(): ToolDefinition[] {
    return [
      {
        name: 'robotDogPatrol',
        description: 'Control robot dog patrol operations - send robot dog to specific routes',
        parameters: {
          type: 'object',
          properties: {
            facility_id: {
              type: 'string',
              enum: ['cubework-ca-fontana-production', 'yard-25'],
              description: 'The facility ID where the robot dog operates. cubework-ca-fontana-production for Fontana, yard-25 for Valleyview',
              default: 'cubework-ca-fontana-production'
            },
            dog_id: {
              type: 'string',
              description: 'The robot dog ID',
              default: 'rbt_dog'
            },
            type: {
              type: 'string',
              description: 'Type of navigation - always route',
              default: 'route'
            },
            spot: {
              type: 'number',
              description: 'Route spot number',
              default: 1
            }
          },
          required: []
        }
      },
      {
        name: 'cameraLiveStream',
        description: 'Start camera live streams for warehouse monitoring. IMPORTANT: Can start multiple cameras in ONE call using cameras array - do NOT make separate calls for each camera.',
        parameters: {
          type: 'object',
          properties: {
            yard: {
              type: 'string',
              enum: ['cubework-ca-fontana-production', 'yard-25'],
              description: 'The yard/facility to view cameras from. cubework-ca-fontana-production for Fontana, yard-25 for Valleyview',
              default: 'cubework-ca-fontana-production'
            },
            cameras: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  ip: {
                    type: 'string',
                    description: 'Camera IP address'
                  },
                  camera: {
                    type: 'string',
                    description: 'Camera name/identifier'
                  }
                },
                required: ['ip', 'camera']
              },
              description: 'Array of cameras to start in ONE call. For multiple cameras (e.g. spot2 and spot3), include ALL in this array. Each camera needs ip and camera name. Available cameras: Fontana - Spot1: {ip:"**************", camera:"roofing-camera-1"}, Spot2: {ip:"**************", camera:"roofing-camera-2"}, Spot3: {ip:"**************", camera:"roofing-camera-3"}. Valleyview - Spot1: {ip:"***************", camera:"roofing-camera-1"}. Example for multiple: [{ip:"**************", camera:"roofing-camera-2"}, {ip:"**************", camera:"roofing-camera-3"}]'
            }
          },
          required: []
        }
      }
    ];
  }
  
  async robotDogPatrol(args: any) {
    return await robotDogPatrolTool.execute(args, { 
      toolCallId: `patrol-${Date.now()}`, 
      messages: []
    });
  }
  
  async cameraLiveStream(args: any) {
    return await cameraLiveStreamTool.execute(args, { 
      toolCallId: `camera-${Date.now()}`, 
      messages: []
    });
  }
  
  static getSystemPrompt(): string {
    return `You can use IoT tools to control robot dogs and view camera feeds:
- robotDogPatrol: Send robot dog to patrol specific routes
- cameraLiveStream: Start live camera streams for warehouse monitoring (supports single or multiple cameras)

Available facilities:
- Fontana warehouse (cubework-ca-fontana-production) - DEFAULT
- Valleyview warehouse (yard-25)

Camera configurations (use cameras array parameter for multiple cameras):
- Fontana warehouse (cubework-ca-fontana-production):
  * Spot1: {ip: "**************", camera: "roofing-camera-1"}
  * Spot2: {ip: "**************", camera: "roofing-camera-2"}  
  * Spot3: {ip: "**************", camera: "roofing-camera-3"}
- Valleyview warehouse (yard-25):
  * Spot1: {ip: "***************", camera: "roofing-camera-1"}

For camera feeds, use the cameras array parameter with objects containing both ip and camera name. IMPORTANT: You can and SHOULD specify multiple cameras in ONE request when user asks for multiple spots (e.g. spot2 and spot3). Do NOT make separate calls for each camera.

Default values: facility_id='cubework-ca-fontana-production', dog_id='rbt_dog', type='route', spot=1. You can specify different spot numbers for different patrol routes.`;
  }
}

// Export all tools
export const iotTools = {
  robotDogPatrolTool,
  cameraLiveStreamTool
}; 