import { NextRequest, NextResponse } from 'next/server';
import { getChatHistory } from '@/utils/storage';
import { getUserIdFromRequest } from '@/utils/authUtils';
import { getSiteToolConfig } from '@/config/index';

export async function GET(req: NextRequest) {
  try {
    // 获取当前用户ID（用于权限验证）
    const currentUserId = await getUserIdFromRequest(req);
    if (!currentUserId) {
      return NextResponse.json(
        { error: '用户未登录或会话已过期' },
        { status: 401 }
      );
    }

    const url = new URL(req.url);
    const chatId = url.searchParams.get('chatId');
    let userId = url.searchParams.get('userId');
    if (!userId) {
        userId = currentUserId;
    }
    if (!chatId || !userId) {
      return NextResponse.json(
        { error: '聊天ID和用户ID都是必需的' },
        { status: 400 }
      );
    }

    // 获取聊天历史
    const chatHistory = await getChatHistory(chatId, userId);

    if (!chatHistory) {
        // 新增：遍历所有站点ID
        const siteToolConfig = getSiteToolConfig();
        console.log('站点配置:', siteToolConfig);
        console.log('站点配置keys:', Object.keys(siteToolConfig));
        for (const siteId of Object.keys(siteToolConfig)) {
          console.log('遍历站点ID:', siteId);
          const siteChat = await getChatHistory(chatId, siteId);
          if (siteChat) {
            return NextResponse.json({ ...siteChat, siteLevel: true });
          }
        }
        console.log('未找到聊天历史:', chatId);
        return NextResponse.json({ error: '未找到聊天历史' }, { status: 404 });
      }

    return NextResponse.json(chatHistory);
  } catch (error) {
    console.error('获取聊天历史失败:', error);
    return NextResponse.json(
      { error: '获取聊天历史失败' },
      { status: 500 }
    );
  }
}