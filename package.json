{"name": "cyber-bot", "version": "0.5.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "build:embed": "webpack --config webpack.embed.config.js", "build:embed:unis:dev": "cross-env SITE=unis ENV=dev webpack --config webpack.embed.config.js", "build:embed:unis:staging": "cross-env SITE=unis ENV=staging webpack --config webpack.embed.config.js", "build:embed:unis:prod": "cross-env SITE=unis ENV=prod webpack --config webpack.embed.config.js", "build:embed:wms:staging": "cross-env SITE=wms ENV=staging webpack --config webpack.embed.config.js", "build:embed:wms:dev": "cross-env SITE=wms ENV=dev webpack --config webpack.embed.config.js", "build:embed:wms:prod": "cross-env SITE=wms ENV=prod webpack --config webpack.embed.config.js", "build:embed:fms:staging": "cross-env SITE=fms ENV=staging webpack --config webpack.embed.config.js", "build:embed:fms:dev": "cross-env SITE=fms ENV=dev webpack --config webpack.embed.config.js", "build:embed:fms:prod": "cross-env SITE=fms ENV=prod webpack --config webpack.embed.config.js", "build:embed:cubework:prod": "cross-env SITE=cubework ENV=prod webpack --config webpack.embed.config.js", "build:embed:lso:prod": "cross-env SITE=lso ENV=prod webpack --config webpack.embed.config.js", "build:embed:portal:dev": "cross-env SITE=portal ENV=dev webpack --config webpack.embed.config.js", "build:embed:portal:staging": "cross-env SITE=portal ENV=staging webpack --config webpack.embed.config.js", "build:embed:marketplace:dev": "cross-env SITE=marketplace ENV=dev webpack --config webpack.embed.config.js", "build:embed:marketplace:prod": "cross-env SITE=marketplace ENV=prod webpack --config webpack.embed.config.js", "build:all": "npm run build && npm run build:embed", "start": "next start", "lint": "next lint", "verify-lancedb": "node scripts/verify-lancedb.js", "mcp-server": "python ../start_mcp_server.py", "test-mcp": "node ../test_mcp_tools.js", "test-multi-mcp": "node ../test_mcp_multi_servers.js", "start-all": "start-server-and-test mcp-server http://localhost:8000/health dev", "test-report-storage": "ts-node src/test-report-storage.ts", "generate-report": "ts-node src/script-trigger-report.ts"}, "dependencies": {"@ai-sdk/anthropic": "^1.2.0", "@ai-sdk/deepseek": "^0.2.14", "@ai-sdk/google": "^1.2.1", "@ai-sdk/openai": "^1.3.0", "@aws-sdk/client-s3": "^3.787.0", "@hookform/resolvers": "^5.0.1", "@modelcontextprotocol/sdk": "^1.8.0", "@openai/agents": "^0.0.7", "@radix-ui/react-checkbox": "^1.2.3", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-popover": "^1.1.11", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.2.4", "@radix-ui/react-tabs": "^1.1.9", "@radix-ui/react-tooltip": "^1.2.4", "@types/jszip": "^3.4.0", "@types/node": "^20.11.19", "@types/react": "^18.2.55", "@types/react-dom": "^18.2.19", "@types/uuid": "^10.0.0", "@vercel/blob": "^0.18.0", "@xenova/transformers": "^2.17.2", "ai": "^4.2.0", "autoprefixer": "^10.4.17", "axios": "^1.8.3", "better-sqlite3": "^11.9.1", "class-variance-authority": "^0.7.1", "date-fns": "^4.1.0", "dotenv": "^16.4.1", "eslint": "^8.56.0", "eslint-config-next": "^15.2.1", "highlight.js": "^11.9.0", "jose": "^6.0.10", "jsencrypt": "^3.3.2", "jszip": "^3.10.1", "lucide-react": "^0.503.0", "marked": "^11.1.1", "marked-highlight": "^2.1.0", "mem0ai": "^2.1.18", "next": "15.2.3", "node-fetch": "^2.7.0", "nodemailer": "^7.0.3", "openai": "^4.39.0", "postcss": "^8.4.35", "rate-limiter-flexible": "^3.0.0", "react": "^18", "react-day-picker": "^9.6.7", "react-dom": "^18", "react-hook-form": "^7.56.1", "react-markdown": "^9.0.1", "remark-gfm": "^4.0.1", "start-server-and-test": "^2.0.3", "swr": "^2.2.4", "tailwind-merge": "^3.2.0", "tailwindcss": "^3.4.1", "typescript": "^5.3.3", "uuid": "^11.1.0", "zod": "^3.25.63", "zod-to-json-schema": "^3.24.4", "zustand": "^5.0.6"}, "devDependencies": {"@babel/core": "^7.26.10", "@babel/preset-env": "^7.26.9", "@babel/preset-react": "^7.26.3", "@babel/preset-typescript": "^7.27.0", "@types/better-sqlite3": "^7.6.13", "@types/nodemailer": "^6.4.17", "babel-loader": "^9.2.1", "cross-env": "^7.0.3", "css-loader": "^6.11.0", "mysql2": "^3.14.0", "node-loader": "^2.1.0", "postcss-loader": "^8.1.1", "terser-webpack-plugin": "^5.3.14", "to-string-loader": "^1.2.0", "webpack": "^5.99.7", "webpack-cli": "^5.1.4"}}