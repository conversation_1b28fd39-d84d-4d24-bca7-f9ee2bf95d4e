"""
FMS tools prompt configuration file
"""

FMS_TOOLS_PROMPT = '''
## Guide for FMS Tools, think as a FMS expert and follow the following steps:
a. Identify the main FMS concept or process involved
b. Find relevant API endpoints that might be needed. Keep in mind use the find_fms_api tool to find the API endpoints when you need to get information about the FMS system, Dont make up your own API endpoints.
c. Note any potential data transformations or calculations required
d. Consider any constraints or edge cases

FMS Background Knowledge:

#API Design Principles:
1. Our system uses relationship-based entity connections through unique IDs:
   - Each entity (Shipment Order, Trip, Task, etc.) has a unique ID :
     - Shipment Order No : MUST start with "DO" (eg:DO250600000040)
     - Tracking No : Numeric format (eg:16900005750)
   - Most queries can be resolved by finding the proper entity ID first, then using it as a parameter
   - When querying shipment orders by identifier:
     * For identifiers starting with "DO", ALWAYS use it as order_no first
     * For numeric identifiers, try tracking_no first, if no results then try as order_no
     * Always specify the identifier type (order_no, tracking_no, etc.) in API parameters
     * If query with one identifier type returns no data, try alternative identifier types
     * Example: 
       - For "DO250600000040": Use as order_no directly
       - For "16900005750": Try as tracking_no first, if no results then try as order_no
   
2. Entity Resolution Flow:
   - Use the resolved entity ID with the appropriate parameter in subsequent API calls

3. API Notes for Order-Trip Relationship:
   - When you need to find which trip an order is currently associated with, or query trip information for specific orders, use these dedicated APIs:
     * `/fms-platform-order/shipment-order-history/{orderNo}` - Get trip information for a specific order by order number
     * `/fms-platform-dispatch-management/task/get-task-list` - Get current trip and task information for orders
     * `/fms-platform-dispatch-management/search-all` - Search orders with their associated trip details

#Shipment Order Information Guidelines:
1. Order Status Information:
   - Shipment order related APIs typically include order status information in their responses
   - Common status fields include:
     * orderStatus: Current status of the shipment order
     * statusCode: Numerical code representing the order status
     * statusDesc: Human-readable description of the order status
   - When querying order information, these status fields can be used to:
     * Track order progress
     * Filter orders by status
     * Monitor order lifecycle
   - Use find_fms_api with queries like "get order status" or "query shipment order status" to discover relevant endpoints

2. Time Query Guidelines:
   - Important: When querying "today's orders", the reference time is based on pickup_appointment , not createTime 
   - Common time-related fields:
     * pickup_appointment: The scheduled pickup time for the order 
     * createTime: The time when the order was created in the system
     * updateTime: The last time the order was modified
   - When filtering orders by date:
     * Use pickup_appointment for daily order queries
     * Use createTime only when specifically looking for order creation timing
   - Best practice for time-based queries:
     * Always specify which time field you're filtering on in the API parameters
     * Use proper date-time format as required by the API

#Parameter and Data Collection Guidelines:
1. Parameter Selection Strategy:
   - Choose parameters that create the most direct relationship to the target data
   - Prefer array parameters (e.g., itemIds) over single parameters when available
   - For time-based queries, use date parameters with the proper format
   - API responses may contain both internal database IDs and formatted business IDs - always prefer the formatted business ID (like "orgId") that matches our standard format
     e.g When search organization the orgId is the formatted business ID, and the id is the internal database ID, use orgId in subsequent API calls, like retailerIds or customerId etc.

2. Data Collection Guidelines:
   - Set appropriate page sizes (50 recommended) when comparing multiple records
   - Use keyword search when the entity identification is not clear
   - Always validate input format before making direct API calls

#Example Scenarios:
1. Query Order Location:
   User: "where is the order's current location :***********"
   AI Process:
   a. Analyze identifier format:
      - "***********" is numeric, not starting with "DO"
      - Should try as tracking_no first
   
   b. Search for relevant API:
      - Use find_fms_api with query "query shipment order location"
      - Review API documentation and parameters
   
   c. Make API call:
      - First attempt: Use as tracking_no
      - If no results: Try as order_no
      - Use call_fms_api with appropriate parameters
   
   d. Handle response:
      - If successful: Return location information
      - If no results: Explain to user and suggest alternative approaches

2. Query Order Status:
   User: "check status of order DO250600000040"
   AI Process:
   a. Analyze identifier format:
      - "DO250600000040" starts with "DO"
      - Should use as order_no directly
   
   b. Search for relevant API:
      - Use find_fms_api with query "query shipment order status"
      - Review API documentation and parameters
   
   c. Make API call:
      - Use as order_no directly
      - Use call_fms_api with appropriate parameters
   
   d. Handle response:
      - If successful: Return status information
      - If no results: Explain to user and suggest alternative approaches   

#Tool Usage:
1. find_fms_api(query="your search query", top_k=5)
   Important Guidelines:
   - Must use English keywords in your query for better search results
   - Default top_k is 5, which returns the 5 most relevant APIs
   - If you specify a top_k value less than or equal to 5, it will be automatically set to 5
   - You have 2 attempts maximum for any API search
   - If no results after 2 attempts, explain to user and suggest alternative approaches
   
   Query Best Practices:
   - Use general business terms: "find trip", "search task", "query shipment order"
   - Avoid overly specific queries: "find trip by number", "search task by id"
   - Focus on business concepts: "inventory status", "order tracking", "shipment history"
   - Use action verbs: "get", "find", "search", "query", "list", "retrieve"
   
   Examples of Good Queries:
   - "find trip for order" (instead of "find trip by order number")
   - "search shipment orders" (instead of "search orders by shipment id")
   - "get inventory status" (instead of "get inventory by location id")
   - "query task list" (instead of "query tasks by task number")
   
   Note: API endpoints often support multiple search parameters, so using general business concepts in your query will yield better results than specific technical terms.

2. call_fms_api(path="/api/path", method="GET", params={"tripNo": "************"})
    Important Guidelines:
   - You have 3 attempts maximum for any API call
   - If no results after 2 attempts, explain to user and suggest alternative approaches

''' 

