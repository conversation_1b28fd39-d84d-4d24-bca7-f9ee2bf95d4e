import { BaseAgent, AgentConfig, AgentExecutionContext } from './base/BaseAgent';
import { CoreTool, tool, streamText, createDataStream, createDataStreamResponse, pipeDataStreamToResponse } from 'ai';
import { z } from 'zod';
import { globalAgentManager } from './base/AgentManager';
import { superAgentSystemPrompt } from '@/prompts/agents/superPrompt';

// 导入专业工具（基础工具由BaseAgent提供）
import { gisTool } from '@/tools/gisTool';
import { finishTaskTool } from '@/tools/commonTools';
import { kbTool } from '@/tools/kbTool';
import { jiraTools } from '@/tools/jira-tools';
import { iotTools } from '@/tools/iotTool';
import { twilioTool } from '@/tools/twilioTool';
import nodemailer from 'nodemailer';
import { 
  portalQuoteTool, 
  portalSaveShippingQuoteTool, 
  portalGetUserPaymentCardsTool,
  portalCreateShippingOrderTool, 
  portalAccListTool, 
  getUtCustomers4IamTool,
  getUfCustomers4IamTool 
} from '@/tools/portal-tool';
import { tmsShipmentOrderTrackingTool } from '@/tools/tms-tool';
import { memoryTools } from '@/tools/memoryTools';

export class SuperAgent extends BaseAgent {
  constructor() {
    const config: AgentConfig = {
      id: 'super-agent',
      name: 'Super Agent',
      description: 'The main coordinator agent that handles general tasks and delegates specialized tasks to other agents',
      tools: [
        // 委托工具
        'delegateToAgent',
        // 专业工具（基础工具由BaseAgent自动提供）
        'gisTool',
        'finishTaskTool',
        'kbTool',
        'emailTool',
        'twilioTool',
        // Portal工具
        'portalQuoteTool',
        'portalSaveShippingQuoteTool',
        'portalGetUserPaymentCardsTool',
        'portalCreateShippingOrderTool',
        'portalAccListTool',
        'getUtCustomers4IamTool',
        // TMS工具
        'tmsShipmentOrderTrackingTool',
        // Jira工具
        'getJiraCreateMeta',
        'createJiraIssue',
        'getJiraProjects',
        'searchJiraIssues',
        'getJiraIssueTransitions',
        'doJiraIssueTransition',
        // IoT工具
        'robotDogPatrolTool',
        'cameraLiveStreamTool',
        // Memory工具（如果启用）
        'searchMemoriesTool',
        'saveMemoryTool'
      ],
      systemPrompt: superAgentSystemPrompt
    };
    
    super(config);
  }
  
  
  // 创建邮件工具
  private createEmailTool(): CoreTool {
    return tool({
      description: 'Send email to specified recipients. IMPORTANT: You must first compose the complete email body with all necessary content before calling this tool.',
      parameters: z.object({
        to_addresses: z.array(z.string()).describe("List of recipient email addresses"),
        subject: z.string().describe('Email subject'),
        body: z.string().describe('Email content - REQUIRED'),
        cc_addresses: z.array(z.string()).optional().describe('List of CC recipients'),
        bcc_addresses: z.array(z.string()).optional().describe('List of BCC recipients'),
        is_html: z.boolean().optional().describe('Whether the email body is HTML')
      }),
      execute: async ({ to_addresses, subject, body, cc_addresses = [], bcc_addresses = [], is_html = false }) => {
        const smtpConfig = {
          host: process.env.SMTP_HOST || 'smtp.example.com',
          port: Number(process.env.SMTP_PORT) || 587,
          secure: false,
          auth: {
            user: process.env.SMTP_USER || '<EMAIL>',
            pass: process.env.SMTP_PASS || 'password',
          },
        };
        const from = process.env.SMTP_FROM || smtpConfig.auth.user;
        
        try {
          const transporter = nodemailer.createTransport(smtpConfig);
          const info = await transporter.sendMail({
            from,
            to: to_addresses,
            cc: cc_addresses.length > 0 ? cc_addresses : undefined,
            bcc: bcc_addresses.length > 0 ? bcc_addresses : undefined,
            subject,
            [is_html ? 'html' : 'text']: body
          });
          
          return {
            success: true,
            message: 'Email sent successfully',
            details: {
              to: to_addresses,
              cc: cc_addresses,
              bcc: bcc_addresses,
              subject,
              messageId: info.messageId
            }
          };
        } catch (e: any) {
          return {
            success: false,
            error: e.message || String(e),
            message: 'Failed to send email'
          };
        }
      }
    });
  }
  
  // 加载SuperAgent专业工具
  protected async loadSpecializedTools(): Promise<Record<string, CoreTool>> {
    console.log('[SuperAgent] Loading tools...');
    
    // 检查是否启用记忆工具
    const isMemoryEnabled = process.env.MEMORY_CHAT_ENABLED === undefined || 
                           process.env.MEMORY_CHAT_ENABLED.toLowerCase() === 'true';
    
    const tools: Record<string, CoreTool> = {
      // 委托工具在execute方法中内联定义
      
      // 专业工具（基础工具由BaseAgent自动提供）
      gisTool,
      finishTaskTool,
      kbTool,
      emailTool: this.createEmailTool(),
      twilioTool,
      
      // Portal工具
      portalQuoteTool,
      portalSaveShippingQuoteTool,
      portalGetUserPaymentCardsTool,
      portalCreateShippingOrderTool,
      portalAccListTool,
      getUtCustomers4IamTool,
      
      // TMS工具
      tmsShipmentOrderTrackingTool,
      
      // Jira工具
      getJiraCreateMeta: jiraTools.getJiraCreateMeta,
      createJiraIssue: jiraTools.createJiraIssue,
      getJiraProjects: jiraTools.getJiraProjects,
      searchJiraIssues: jiraTools.searchJiraIssues,
      getJiraIssueTransitions: jiraTools.getJiraIssueTransitions,
      doJiraIssueTransition: jiraTools.doJiraIssueTransition,
      
      // IoT工具
      robotDogPatrolTool: iotTools.robotDogPatrolTool,
      cameraLiveStreamTool: iotTools.cameraLiveStreamTool
    };
    
    // 条件加载记忆工具
    if (isMemoryEnabled) {
      tools.searchMemoriesTool = memoryTools.searchMemoriesTool;
      tools.saveMemoryTool = memoryTools.saveMemoryTool;
    }
    
    console.log(`[SuperAgent] Loaded ${Object.keys(tools).length} tools`);
    return tools;
  }
  
  // 重写execute方法以支持委托工具
  async execute(context: AgentExecutionContext, modelInstance: any): Promise<any> {
    console.log(`[SuperAgent] Executing with unified stream support`);
    
    // 获取系统提示词
    const systemPrompt = await this.getSystemPrompt();
    
    // 获取基础和专业工具
    const baseTools = await this.getTools();
    
    // 创建委托工具并添加到工具中
    const delegateToAgentTool = this.createDelegateToAgentTool(context, modelInstance);
    const allTools = {
      ...baseTools,
      delegateToAgent: delegateToAgentTool
    };
    
    console.log(`[SuperAgent] Using ${Object.keys(allTools).length} tools (${Object.keys(baseTools).length} base + 1 delegate)`);
    
    // 包装工具以传递context
    const wrappedTools = this.wrapToolsWithContext(allTools, context);
    
    // 将系统提示词作为最后一条消息
    const messagesWithSystemPrompt = [
      ...context.messages, 
      { 
        role: 'system' as const, 
        content: systemPrompt 
      },
    ];
    
    // 创建SuperAgent的streamText
    const result = await streamText({
      model: modelInstance,
      messages: messagesWithSystemPrompt,
      temperature: 0.1,
      tools: wrappedTools,
      maxSteps: 30,
      headers: context.timezone ? { 'X-Timezone': context.timezone } : undefined,
      onFinish: async (result) => {
        console.log(`[SuperAgent] Finished. Tokens: ${result.usage?.totalTokens || 0}`);
      },
      onError: (error) => {
        console.error(`[SuperAgent] Error:`, error);
      }
    });
    
    return result;
  }
  
  // 创建委托工具的方法 - 简化版，使用统一的dataStream
  private createDelegateToAgentTool(context: AgentExecutionContext, modelInstance: any): CoreTool {
    const self = this;
    
    return tool({
      description: 'Delegate tasks to specialized agents. Use this for WMS operations (warehouse, inventory, shipping) or BI operations (reports, dashboards, analysis). The agent will execute in real-time and provide streaming output.',
      parameters: z.object({
        agentId: z.string().describe('The ID of the agent to delegate to: "wms-agent" or "bi-agent"')
      }),
      execute: async ({ agentId }) => {
        console.log(`[SuperAgent] Delegating to ${agentId}`);
        
        const agent = globalAgentManager.getAgent(agentId);
        if (!agent) {
          return {
            success: false,
            error: `Agent ${agentId} not found. Available agents: wms-agent, bi-agent`
          };
        }

        try {
          console.log(`[SuperAgent] Executing agent ${agentId} with shared dataStream`);
          
          // 执行专业Agent - 它会直接写入共享的dataStream
          const agentResult = await agent.execute(context, modelInstance);
          
          // 如果context包含dataStream，agent的输出会自动合并
          if (context.dataStream) {
            console.log(`[SuperAgent] Agent ${agentId} will merge its output to shared dataStream`);
            agentResult.mergeIntoDataStream(context.dataStream);
          }
          
          // 读取完整内容用于工具返回（供SuperAgent了解执行结果）
          let fullContent = '';
          try {
            for await (const chunk of agentResult.textStream) {
              fullContent += chunk;
            }
          } catch (error) {
            console.error(`[SuperAgent] Error reading agent ${agentId} content:`, error);
          }
          
          console.log(`[SuperAgent] Agent ${agentId} execution completed`);
          
          return {
            success: true,
            agentId,
            agentName: agent.name,
            instruction: "The delegated agent has already output to the user stream. Do not duplicate the content.",
            contentLength: fullContent.length
          };

        } catch (error) {
          console.error(`[SuperAgent] Delegation to ${agentId} failed:`, error);
          return {
            success: false,
            error: `Failed to execute ${agentId}: ${error instanceof Error ? error.message : String(error)}`
          };
        }
      }
    });
  }
  
}