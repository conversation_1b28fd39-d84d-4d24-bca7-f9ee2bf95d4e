'use client';

import React, { useState } from 'react';
import '@/styles/item-design-system.css';

interface JiraIssue {
  key: string;
  id: string;
  fields: {
    summary?: string;
    status?: {
      name: string;
      id: string;
    };
    assignee?: {
      name: string;
      displayName: string;
    };
    reporter?: {
      name: string;
      displayName: string;
    };
    priority?: {
      name: string;
      id: string;
    };
    issuetype?: {
      name: string;
      id: string;
    };
    project?: {
      key: string;
      name: string;
      id: string;
    };
    created?: string;
    updated?: string;
    description?: string;
  };
}

interface JiraProject {
  id: string;
  name: string;
  key: string;
}

interface JiraTransition {
  id: string;
  name: string;
  to: {
    id: string;
    name: string;
    description?: string;
  };
}

interface JiraCreateMeta {
  projects: Array<{
    key: string;
    name: string;
    issuetypes: Array<{
      id: string;
      name: string;
      fields: Record<string, any>;
    }>;
  }>;
}

interface JiraCardProps {
  toolInvocation?: any;
  type?: 'createMeta' | 'createIssue' | 'projects' | 'search' | 'transitions' | 'doTransition';
  result?: any;
}

export default function JiraCard({
  toolInvocation,
  type: propType,
  result: propResult
}: JiraCardProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [showDebug, setShowDebug] = useState(false);

  // Extract data from tool invocation or direct properties
  let type: string | undefined,
      rawResult: any = null,
      result: any,
      toolName: string = '',
      args: any = {};

  if (toolInvocation) {
    toolName = toolInvocation.toolName;
    args = toolInvocation.args || {};

    // Determine type based on tool name
    if (toolName === 'getJiraCreateMeta') {
      type = 'createMeta';
    } else if (toolName === 'createJiraIssue') {
      type = 'createIssue';
    } else if (toolName === 'getJiraProjects') {
      type = 'projects';
    } else if (toolName === 'searchJiraIssues') {
      type = 'search';
    } else if (toolName === 'getJiraIssueTransitions') {
      type = 'transitions';
    } else if (toolName === 'doJiraIssueTransition') {
      type = 'doTransition';
    }

    if (toolInvocation.state === 'result') {
      rawResult = toolInvocation.result;
    }
  } else {
    type = propType;
    result = propResult;
  }

  // Extract actual data
  const extractActualData = (data: any): any => {
    if (!data) return null;

    // Check if it's already the correct format
    if (data.success !== undefined) {
      return data;
    }

    // Handle string format JSON
    if (typeof data === 'string') {
      try {
        return JSON.parse(data);
      } catch (e) {
        return { success: false, error: "Data parsing error" };
      }
    }

    return data;
  };

  result = extractActualData(rawResult);

  // Tool invocation processing (loading state)
  if (toolInvocation && toolInvocation.state !== 'result') {
    return (
      <div className="item-card bg-blue-600/20 rounded-lg border border-blue-500/30 p-2 text-white shadow-lg my-2 item-glow-blue-subtle">
        <div className="flex items-center">
          <div className="bg-item-bg-hover p-1.5 rounded-lg mr-2.5">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-4 h-4 text-blue-400">
              <path strokeLinecap="round" strokeLinejoin="round" d="M9 12h3.75M9 15h3.75M9 18h3.75m3-6h3.75m-3.75 3h3.75m-3.75 3h3.75M5.25 6.75h13.5V18a2.25 2.25 0 01-2.25 2.25H7.5A2.25 2.25 0 015.25 18V6.75z" />
            </svg>
          </div>
          <div className="flex-1 text-sm text-white font-medium pr-2 leading-snug">
            {getLoadingMessage(type, args)}
          </div>
        </div>
      </div>
    );
  }

  // Get summary for collapsed view
  const getSummary = () => {
    if (!result) return 'No result';

    switch (type) {
      case 'createMeta':
        return result.success ? 'Retrieved field metadata' : 'Failed to get metadata';
      case 'createIssue':
        return result.success ? `Created issue ${result.issueKey || result.data?.key}` : 'Failed to create issue';
      case 'projects':
        return result.success ? `Found ${result.data?.length || 0} projects` : 'Failed to get projects';
      case 'search':
        return result.success ? `Found ${result.data?.total || 0} issues` : 'Search failed';
      case 'transitions':
        return result.success ? `Found ${result.data?.transitions?.length || 0} transitions` : 'Failed to get transitions';
      case 'doTransition':
        return result.success ? 'Transition completed' : 'Transition failed';
      default:
        return 'Jira operation completed';
    }
  };

  // Collapsed view
  if (!isExpanded) {
    return (
      <div className="item-card bg-blue-600/20 rounded-lg border border-blue-500/30 p-2 text-white shadow-lg my-2 cursor-pointer hover:border-blue-500/50 hover:shadow-xl transition-all duration-200 item-glow-blue-subtle">
        <div className="flex justify-between" onClick={() => setIsExpanded(true)}>
          <div className="flex items-center flex-1">
            <div className="bg-item-bg-hover p-1.5 rounded-lg mr-2.5">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-4 h-4 text-blue-400">
                <path strokeLinecap="round" strokeLinejoin="round" d="M9 12h3.75M9 15h3.75M9 18h3.75m3-6h3.75m-3.75 3h3.75m-3.75 3h3.75M5.25 6.75h13.5V18a2.25 2.25 0 01-2.25 2.25H7.5A2.25 2.25 0 015.25 18V6.75z" />
              </svg>
            </div>
            <div className="text-sm text-item-gray-500 font-medium pr-2 leading-snug">
              {getTitle(type, args)} - {getSummary()}
            </div>
          </div>
          <button className="text-blue-400 hover:text-blue-300 ml-2 flex-shrink-0 transition-colors duration-200">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-4 h-4">
              <path strokeLinecap="round" strokeLinejoin="round" d="M19.5 8.25l-7.5 7.5-7.5-7.5" />
            </svg>
          </button>
        </div>
      </div>
    );
  }

  // Expanded view
  return (
    <div className="item-card bg-blue-600/20 rounded-lg border border-blue-500/30 p-2 text-white shadow-lg my-2 item-glow-blue-subtle">
      <div className="flex justify-between cursor-pointer" onClick={() => setIsExpanded(!isExpanded)}>
        <div className="flex items-center flex-1">
          <div className="bg-item-bg-hover p-1.5 rounded-lg mr-2.5">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-4 h-4 text-blue-400">
              <path strokeLinecap="round" strokeLinejoin="round" d="M9 12h3.75M9 15h3.75M9 18h3.75m3-6h3.75m-3.75 3h3.75m-3.75 3h3.75M5.25 6.75h13.5V18a2.25 2.25 0 01-2.25 2.25H7.5A2.25 2.25 0 015.25 18V6.75z" />
            </svg>
          </div>
          <div className="text-sm text-white font-semibold pr-2 leading-snug">
            {getTitle(type, args)}
          </div>
        </div>
        <button className="text-blue-400 hover:text-blue-300 hover:bg-item-bg-hover p-1.5 rounded-lg ml-2 flex-shrink-0 transition-all duration-200">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-4 h-4">
            <path strokeLinecap="round" strokeLinejoin="round" d="M4.5 15.75l7.5-7.5 7.5 7.5" />
          </svg>
        </button>
      </div>

      {/* Debug view */}
      {showDebug && (
        <div className="mt-3 pt-2.5 border-t border-item-gray-800/40">
          <div className="text-xs text-blue-400 mb-1.5 font-semibold uppercase tracking-wide">Raw data:</div>
          <pre className="text-xs overflow-auto max-h-40 font-mono whitespace-pre-wrap break-words text-item-gray-300 bg-item-bg-card p-2 rounded-lg border border-item-gray-800/30">
            {JSON.stringify({rawResult, result, type, args}, null, 2)}
          </pre>
        </div>
      )}

      {/* Content based on type */}
      <div className="mt-3">
        {renderContent(type, result, args)}
      </div>

      <div className="text-xs text-item-gray-400 mt-2.5 text-right font-medium">
        Jira Results
      </div>
    </div>
  );
}

function getLoadingMessage(type: string | undefined, args: any): string {
  switch (type) {
    case 'createMeta':
      return `Getting field metadata for project ${args.projectKey}...`;
    case 'createIssue':
      return `Creating Jira issue in project ${args.fields?.project?.key}...`;
    case 'projects':
      return 'Getting Jira projects...';
    case 'search':
      return `Searching Jira issues: "${args.jql}"...`;
    case 'transitions':
      return `Getting transitions for issue ${args.issueIdOrKey}...`;
    case 'doTransition':
      return `Executing transition on issue ${args.issueIdOrKey}...`;
    default:
      return 'Processing Jira request...';
  }
}

function getTitle(type: string | undefined, args: any): string {
  switch (type) {
    case 'createMeta':
      return `Jira Create Metadata: ${args.projectKey}`;
    case 'createIssue':
      return `Jira Issue Creation`;
    case 'projects':
      return 'Jira Projects';
    case 'search':
      return 'Jira Issue Search';
    case 'transitions':
      return `Jira Transitions: ${args.issueIdOrKey}`;
    case 'doTransition':
      return `Jira Transition: ${args.issueIdOrKey}`;
    default:
      return 'Jira Operation';
  }
}

function renderContent(type: string | undefined, result: any, args: any): React.ReactNode {
  if (!result) {
    return (
      <div className="text-sm bg-red-900/20 p-3 rounded-lg border border-red-800/20">
        No result data available
      </div>
    );
  }

  if (!result.success) {
    return (
      <div className="text-sm bg-red-900/20 p-3 rounded-lg border border-red-800/20">
        Operation failed: {result.error || result.message || 'Unknown error'}
      </div>
    );
  }

  switch (type) {
    case 'createMeta':
      return renderCreateMeta(result);
    case 'createIssue':
      return renderCreateIssue(result);
    case 'projects':
      return renderProjects(result);
    case 'search':
      return renderSearch(result);
    case 'transitions':
      return renderTransitions(result);
    case 'doTransition':
      return renderDoTransition(result);
    default:
      return (
        <div className="bg-gray-800/60 rounded-lg p-3 border border-gray-700/30">
          <div className="text-xs text-gray-400 mb-1">Response:</div>
          <pre className="text-xs overflow-auto max-h-60 font-mono whitespace-pre-wrap break-words text-gray-300">
            {JSON.stringify(result, null, 2)}
          </pre>
        </div>
      );
  }
}

function renderCreateMeta(result: any): React.ReactNode {
  const projects = result.data?.projects || [];
  
  return (
    <div>
      <div className="text-xs text-gray-400 mb-2">
        {result.message}
      </div>
      
      {projects.map((project: any, index: number) => (
        <div key={index} className="bg-gray-800/60 rounded-lg p-2 border border-gray-700/30 mb-1.5">
          <div className="font-medium text-sm mb-1.5">{project.name} ({project.key})</div>
          
          {project.issuetypes?.map((issueType: any, itIndex: number) => (
            <div key={itIndex} className="mb-3">
              <div className="text-sm text-blue-300 mb-1">{issueType.name}</div>
              
              {issueType.fields && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                  {Object.entries(issueType.fields).map(([fieldKey, fieldValue]: [string, any]) => (
                    <div key={fieldKey} className="bg-gray-800/50 p-2 rounded text-xs">
                      <div className="text-cyan-300 font-medium">{fieldValue.name}</div>
                      <div className="text-gray-400">
                        {fieldKey} {fieldValue.required && <span className="text-red-400">*</span>}
                      </div>
                      {fieldValue.allowedValues && (
                        <div className="text-gray-500 mt-1">
                          {fieldValue.allowedValues.length} options available
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>
          ))}
        </div>
      ))}
    </div>
  );
}

function renderCreateIssue(result: any): React.ReactNode {
  return (
    <div className="bg-green-900/20 rounded-lg p-2 border border-green-800/20">
      <div className="text-sm text-green-300 mb-1.5">✅ Issue Created Successfully</div>
      <div className="space-y-1 text-xs">
        <div><span className="text-gray-400">Issue Key:</span> <span className="text-cyan-300">{result.issueKey}</span></div>
        <div><span className="text-gray-400">Issue ID:</span> <span className="text-gray-300">{result.issueId}</span></div>
        {result.issueUrl && (
          <div><span className="text-gray-400">URL:</span> <span className="text-blue-300">{result.issueUrl}</span></div>
        )}
      </div>
    </div>
  );
}

function renderProjects(result: any): React.ReactNode {
  const projects = result.data || [];
  
  return (
    <div>
      <div className="text-xs text-gray-400 mb-2">
        Found {projects.length} accessible projects
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
        {projects.map((project: JiraProject, index: number) => (
          <div key={index} className="bg-gray-800/60 rounded-lg p-2 border border-gray-700/30">
            <div className="font-medium text-sm">{project.name}</div>
            <div className="text-xs text-gray-400">Key: {project.key}</div>
            <div className="text-xs text-gray-500">ID: {project.id}</div>
          </div>
        ))}
      </div>
    </div>
  );
}

function renderSearch(result: any): React.ReactNode {
  const issues = result.data?.issues || [];
  const total = result.data?.total || 0;
  const pagination = result.data?.pagination;
  
  return (
    <div>
      <div className="text-xs text-gray-400 mb-2">
        Found {total} issues, showing {issues.length} results
        {pagination && (
          <span> (Page {pagination.currentPage} of {pagination.totalPages})</span>
        )}
      </div>
      
      {issues.length > 0 ? (
        <div className="space-y-2">
          {issues.map((issue: JiraIssue, index: number) => (
            <div key={index} className="bg-gray-800/60 rounded-lg p-2 border border-gray-700/30">
              <div className="flex justify-between items-start mb-2">
                <div className="font-medium text-sm text-cyan-300">{issue.key}</div>
                <div className="text-xs px-2 py-0.5 rounded bg-gray-700/40 text-gray-300">
                  {issue.fields.status?.name}
                </div>
              </div>
              
              <div className="text-sm text-gray-300 mb-2">{issue.fields.summary}</div>
              
              <div className="grid grid-cols-2 gap-2 text-xs">
                {issue.fields.assignee && (
                  <div><span className="text-gray-400">Assignee:</span> {issue.fields.assignee.displayName}</div>
                )}
                {issue.fields.priority && (
                  <div><span className="text-gray-400">Priority:</span> {issue.fields.priority.name}</div>
                )}
                {issue.fields.issuetype && (
                  <div><span className="text-gray-400">Type:</span> {issue.fields.issuetype.name}</div>
                )}
                {issue.fields.project && (
                  <div><span className="text-gray-400">Project:</span> {issue.fields.project.key}</div>
                )}
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-sm bg-gray-800/60 p-3 rounded-lg">
          No issues found
        </div>
      )}
    </div>
  );
}

function renderTransitions(result: any): React.ReactNode {
  const transitions = result.data?.transitions || [];
  
  return (
    <div>
      <div className="text-xs text-gray-400 mb-2">
        Available transitions for issue {result.data?.issueKey}
      </div>
      
      {transitions.length > 0 ? (
        <div className="space-y-2">
          {transitions.map((transition: JiraTransition, index: number) => (
            <div key={index} className="bg-gray-800/60 rounded-lg p-2 border border-gray-700/30">
              <div className="flex justify-between items-center">
                <div className="font-medium text-sm">{transition.name}</div>
                <div className="text-xs px-2 py-0.5 rounded bg-blue-700/40 text-blue-300">
                  ID: {transition.id}
                </div>
              </div>
              <div className="text-xs text-gray-400 mt-1">
                → {transition.to.name}
                {transition.to.description && (
                  <span className="ml-2">({transition.to.description})</span>
                )}
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-sm bg-gray-800/60 p-3 rounded-lg">
          No transitions available
        </div>
      )}
    </div>
  );
}

function renderDoTransition(result: any): React.ReactNode {
  return (
    <div className="bg-green-900/20 rounded-lg p-3 border border-green-800/20">
      <div className="text-sm text-green-300 mb-2">✅ Transition Completed Successfully</div>
      <div className="space-y-1 text-xs">
        <div><span className="text-gray-400">Issue:</span> <span className="text-cyan-300">{result.issueKey}</span></div>
        <div><span className="text-gray-400">Transition ID:</span> <span className="text-gray-300">{result.transitionId}</span></div>
        {result.fieldsUpdated && result.fieldsUpdated.length > 0 && (
          <div><span className="text-gray-400">Fields Updated:</span> <span className="text-gray-300">{result.fieldsUpdated.join(', ')}</span></div>
        )}
      </div>
    </div>
  );
} 