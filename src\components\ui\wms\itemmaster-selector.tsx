'use client';

import * as React from 'react';
import { useState, useEffect, useRef } from 'react';
import { Package, Search, Loader2 } from 'lucide-react';
// @ts-ignore 忽略类型检查
import { debounce } from 'lodash';
import { wmsApi } from '@/utils/wmsApiClient';

import { Input } from '@/components/ui/input';
import { cn } from '@/lib/utils';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
  getCyberAgentPortalContainer,
} from "@/components/ui/select";

interface ItemMasterOption {
  id: string;
  name: string;
  code?: string;
  status?: string;
}

interface ItemMasterSelectorProps {
  value?: string;
  onChange: (value: string, itemData?: ItemMasterOption) => void;
  placeholder?: string;
  disabled?: boolean;
  required?: boolean;
  customerId?: string; // 依赖的客户ID
  apiHeaders?: Record<string, string>;
  defaultValue?: string;
  tags?: string[]; // 物品标签，如 ["PRODUCT"]
}

// 定义WMS API响应类型
interface WmsApiResponse {
  success: boolean;
  msg?: string;
  data?: any[] | {
    list?: any[];
    [key: string]: any;
  };
  [key: string]: any;
}

export function ItemMasterSelector({
  value,
  onChange,
  placeholder = 'Select item',
  disabled = false,
  required = false,
  customerId,
  apiHeaders = {},
  defaultValue,
  tags = ["PRODUCT"]
}: ItemMasterSelectorProps) {
  // Fixed API path for item master
  const API_PATH = 'wms-bam/item/search-by-paging';
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [options, setOptions] = useState<ItemMasterOption[]>([]);
  const [selectedItem, setSelectedItem] = useState<ItemMasterOption | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const searchInputRef = useRef<HTMLInputElement>(null);
  const initialLoadRef = useRef(false);

  // Initialize with defaultValue if provided and value is not set
  useEffect(() => {
    if (!initialLoadRef.current && defaultValue && !value) {
      initialLoadRef.current = true;
      fetchItemById(defaultValue).then(item => {
        if (item) {
          onChange(item.id, item);
        }
      });
    }
  }, [defaultValue, value]);

  // 当value变化时，如果已有选中的物品ID与新value不同，清除已选物品数据
  useEffect(() => {
    if (value !== selectedItem?.id) {
      setSelectedItem(null);
    }
  }, [value]);

  // 如果有value但没有selectedItem，尝试获取物品数据
  useEffect(() => {
    if (value && !selectedItem && !disabled) {
      fetchItemById(value);
    }
  }, [value, selectedItem, disabled]);

  // 当下拉框打开时自动聚焦搜索框
  useEffect(() => {
    if (open && searchInputRef.current) {
      setTimeout(() => {
        searchInputRef.current?.focus();
      }, 100);
    }
  }, [open]);

  // 提取物品数据的函数
  const extractItemData = (item: any): ItemMasterOption => {
    return {
      id: item.id || '',
      name: item.name || 'Unknown',
      code: item.itemCode || '',
      status: item.status || ''
    };
  };

  // 解析API响应的函数
  const parseApiResponse = (response: any): any[] => {
    // 检查response.data是数组的情况
    if (Array.isArray(response.data)) {
      return response.data;
    }

    // 检查response.data.list的情况
    if (response.data && Array.isArray(response.data.list)) {
      return response.data.list;
    }

    // 检查response.data是对象但包含data字段的情况
    if (response.data && response.data.data && Array.isArray(response.data.data)) {
      return response.data.data;
    }

    // 直接检查response是否包含data字段为数组的情况
    if (response.data && Array.isArray(response.data)) {
      return response.data;
    }

    console.warn('Could not extract item list from API response:', response);
    return [];
  };

  // 通过ID获取物品信息
  const fetchItemById = async (itemId: string): Promise<ItemMasterOption | null> => {
    try {
      setLoading(true);
      console.log('Fetching item by ID:', itemId);
      const response = await wmsApi.post<WmsApiResponse>(API_PATH, {
        id: itemId
      });

      console.log('Fetch item by ID response:', response);

      if (response.success) {
        const itemList = parseApiResponse(response);
        console.log('Parsed item list:', itemList);

        const item = itemList.find((c: any) => {
          return c.id && c.id.toString() === itemId.toString();
        });

        if (item) {
          const itemOption = extractItemData(item);
          console.log('Found item:', itemOption);
          setSelectedItem(itemOption);
          return itemOption;
        } else {
          console.warn('Item not found with ID:', itemId);
        }
      } else {
        console.error('Error fetching item data:', response.msg);
      }
    } catch (error) {
      console.error('Error fetching item data:', error);
    } finally {
      setLoading(false);
    }
    return null;
  };

  // 搜索物品的函数
  const searchItems = async (query: string) => {
    if (!query.trim()) {
      setOptions([]);
      return;
    }

    // 如果需要customerId但没有提供，则不执行搜索
    if (!customerId) {
      console.warn('Customer ID is required for item search');
      return;
    }

    try {
      setLoading(true);
      console.log('Searching items with query:', query);

      const response = await wmsApi.post<WmsApiResponse>(API_PATH, {
        currentPage: 1,
        pageSize: 20,
        name: query,
        tags: tags,
        customerId: customerId,
        status: "ACTIVE",
        scenario: "AUTO_COMPLETE"
      });

      console.log('Item search API response:', response);

      if (response.success) {
        const itemList = parseApiResponse(response);
        console.log('Parsed item list for search:', itemList);

        if (itemList.length > 0) {
          const items = itemList.map((item: any) => extractItemData(item));
          console.log('Mapped items:', items);
          setOptions(items);
        } else {
          console.warn('No items found in the response');
          setOptions([]);
        }
      } else {
        console.warn('No items found or API error:', response.msg);
        setOptions([]);
      }
    } catch (error) {
      console.error('Error searching items:', error);
      setOptions([]);
    } finally {
      setLoading(false);
    }
  };

  // 防抖搜索
  const debouncedSearch = useRef(
    debounce((query: string) => {
      searchItems(query);
    }, 300)
  ).current;

  // 处理搜索输入变化
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchQuery(value);
    debouncedSearch(value);
  };

  // 处理物品选择
  const handleItemSelect = (itemId: string) => {
    const item = options.find(opt => opt.id === itemId);
    if (item) {
      setSelectedItem(item);
      onChange(item.id, item);
      // 重置搜索状态
      setSearchQuery('');
      setOptions([]);
      setOpen(false);
    }
  };

  // 打开下拉框时触发搜索
  const handleOpenChange = (isOpen: boolean) => {
    setOpen(isOpen);

    // 如果关闭下拉框，清空搜索和结果
    if (!isOpen) {
      setSearchQuery('');
      setOptions([]);
    }
    // 如果打开下拉框且有搜索关键字，执行搜索
    else if (isOpen && searchQuery) {
      debouncedSearch(searchQuery);
    }
  };

  return (
    <div className="relative w-full">
      <Select
        value={value}
        onValueChange={handleItemSelect}
        open={open}
        onOpenChange={handleOpenChange}
        disabled={disabled || !customerId}
      >
        <SelectTrigger
          className={cn(
            "w-full h-10 rounded-md bg-transparent",
            "border border-slate-700/50",
            "hover:border-slate-600 focus:border-slate-500",
            "flex items-center justify-between px-3 py-2 text-sm text-slate-200",
            "focus:outline-none focus:ring-0 focus:ring-offset-0",
            "data-[placeholder]:text-slate-400"
          )}
        >
          <SelectValue placeholder={!customerId ? "Select customer first" : placeholder}>
            {loading ? (
              <div className="flex items-center">
                <Loader2 className="mr-2 h-4 w-4 animate-spin text-slate-400" />
                <span>Loading...</span>
              </div>
            ) : selectedItem ? (
              <div className="flex items-center">
                <Package className="mr-2 h-4 w-4 text-slate-400" />
                <span>{selectedItem.name}</span>
              </div>
            ) : null}
          </SelectValue>
        </SelectTrigger>
        <SelectContent
          className={cn(
            "border border-slate-700/70 bg-slate-800/90 text-slate-200",
            "rounded-md shadow-lg min-w-[300px] overflow-hidden",
            "animate-in fade-in-80 zoom-in-95"
          )}
          portalContainer={getCyberAgentPortalContainer()}
        >
          <div className="flex items-center px-3 py-2 border-b border-slate-700/50">
            <Search className="mr-2 h-4 w-4 shrink-0 text-slate-400" />
            <Input
              ref={searchInputRef}
              className="h-8 border-0 p-0 bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0 text-slate-200 placeholder:text-slate-400"
              placeholder="Search items..."
              value={searchQuery}
              onChange={handleSearchChange}
            />
          </div>

          <div className="max-h-[320px] overflow-auto scrollbar-thin scrollbar-thumb-slate-600 scrollbar-track-transparent">
            {loading ? (
              <div className="flex items-center justify-center py-6">
                <Loader2 className="h-6 w-6 animate-spin text-slate-400" />
              </div>
            ) : options.length > 0 ? (
              <SelectGroup>
                <SelectLabel className="px-3 text-xs font-medium text-slate-400">Items</SelectLabel>
                {options.map((item) => (
                  <SelectItem
                    key={item.id}
                    value={item.id}
                    className={cn(
                      "py-2 px-3 cursor-pointer text-slate-200",
                      "focus:bg-slate-700/70 focus:text-slate-200",
                      "data-[highlighted]:bg-slate-700/70 data-[highlighted]:text-slate-200"
                    )}
                  >
                    <div className="flex items-center">
                      <Package className="mr-2 h-4 w-4 text-slate-400" />
                      <div>
                        <div>{item.name}</div>
                        {item.code && (
                          <div className="text-xs text-slate-400">{item.code}</div>
                        )}
                      </div>
                    </div>
                  </SelectItem>
                ))}
              </SelectGroup>
            ) : searchQuery ? (
              <div className="py-6 text-center text-sm text-slate-400">
                No items found
              </div>
            ) : !customerId ? (
              <div className="py-6 text-center text-sm text-slate-400">
                Please select a customer first
              </div>
            ) : (
              <div className="py-6 text-center text-sm text-slate-400">
                Type to search items
              </div>
            )}
          </div>
        </SelectContent>
      </Select>
    </div>
  );
}
