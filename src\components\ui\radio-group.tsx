"use client"

import * as React from "react"
import * as RadioGroupPrimitive from "@radix-ui/react-radio-group"
import { Circle } from "lucide-react"

import { cn } from "@/lib/utils"

const RadioGroup = React.forwardRef<
  React.ElementRef<typeof RadioGroupPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof RadioGroupPrimitive.Root>
>(({ className, ...props }, ref) => {
  return (
    <RadioGroupPrimitive.Root
      className={cn("grid gap-2", className)}
      {...props}
      ref={ref}
    />
  )
})
RadioGroup.displayName = RadioGroupPrimitive.Root.displayName

const RadioGroupItem = React.forwardRef<
  React.ElementRef<typeof RadioGroupPrimitive.Item>,
  React.ComponentPropsWithoutRef<typeof RadioGroupPrimitive.Item>
>(({ className, ...props }, ref) => {
  return (
    <RadioGroupPrimitive.Item
      ref={ref}
      className={cn(
        "aspect-square h-4 w-4 rounded-full border border-item-purple text-item-purple ring-offset-background focus:outline-none focus-visible:ring-2 focus-visible:ring-item-purple focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 transition-all duration-200",
        className
      )}
      {...props}
    >
      <RadioGroupPrimitive.Indicator className="flex items-center justify-center">
        <Circle className="h-2.5 w-2.5 fill-current text-current" />
      </RadioGroupPrimitive.Indicator>
    </RadioGroupPrimitive.Item>
  )
})
RadioGroupItem.displayName = RadioGroupPrimitive.Item.displayName

interface RadioOption {
  label: string
  value: string
  description?: string
}

interface CustomRadioGroupProps {
  options: RadioOption[]
  value?: string
  onChange?: (value: string) => void
  className?: string
  disabled?: boolean
  required?: boolean
}

export function CustomRadioGroup({
  options,
  value,
  onChange,
  className,
  disabled,
  required,
}: CustomRadioGroupProps) {
  return (
    <RadioGroup
      value={value}
      onValueChange={onChange}
      className={cn("space-y-2", className)}
      disabled={disabled}
      required={required}
    >
      {options.map((option) => (
        <div key={option.value} className="flex items-center space-x-3 py-1">
          <RadioGroupItem 
            value={option.value} 
            id={option.value} 
            className="h-5 w-5 border-2 border-item-gray-400 data-[state=checked]:border-item-purple data-[state=checked]:bg-item-purple data-[state=unchecked]:bg-item-bg-card transition-all duration-200 focus-visible:ring-2 focus-visible:ring-item-purple focus-visible:ring-offset-2 focus-visible:ring-offset-background"
          />
          <div className="flex flex-col justify-center">
            <label
              htmlFor={option.value}
              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 text-white cursor-pointer"
            >
              {option.label}
            </label>
            {option.description && (
              <p className="text-sm text-item-gray-400">
                {option.description}
              </p>
            )}
          </div>
        </div>
      ))}
    </RadioGroup>
  )
}

export { RadioGroup, RadioGroupItem } 