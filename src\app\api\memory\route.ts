import { NextRequest, NextResponse } from 'next/server';
import { getMemoryService, MemoryType } from '@/services/memoryService';
import { getUserIdFromRequest } from '@/utils/authUtils';

// GET 请求处理 - 获取记忆
export async function GET(req: NextRequest) {
  try {
    console.log('[MemoryAPI] GET 请求: 获取记忆');

    // 获取当前用户ID
    const userId = await getUserIdFromRequest(req);

    // 如果未登录，返回错误
    if (!userId) {
      console.log('[MemoryAPI] 未获取到用户ID，返回401错误');
      return NextResponse.json(
        { error: '用户未登录或会话已过期' },
        { status: 401 }
      );
    }

    console.log('[MemoryAPI] 使用用户ID:', userId);

    const url = new URL(req.url);
    const type = url.searchParams.get('type') as MemoryType;
    const memoryId = url.searchParams.get('id');
    const page = parseInt(url.searchParams.get('page') || '1');
    const pageSize = parseInt(url.searchParams.get('pageSize') || '50');

    // 获取记忆服务实例
    const memoryService = getMemoryService();

    // 如果提供了ID，获取单个记忆
    if (memoryId) {
      console.log(`[MemoryAPI] 获取单个${type}记忆:`, memoryId);
      const memory = await memoryService.getMemory(type, memoryId);

      if (!memory) {
        console.log('[MemoryAPI] 未找到记忆:', memoryId);
        return NextResponse.json({ error: '未找到记忆' }, { status: 404 });
      }

      // 确保只返回属于当前用户的记忆（对于用户记忆）
      if (type === MemoryType.USER && memory.userId !== userId) {
        console.log('[MemoryAPI] 记忆所有者不匹配:', memory.userId, '!=', userId);
        return NextResponse.json(
          { error: '无权访问该记忆' },
          { status: 403 }
        );
      }

      return NextResponse.json(memory);
    }

    // 否则，获取记忆列表
    if (!type) {
      console.log('[MemoryAPI] 未提供记忆类型');
      return NextResponse.json(
        { error: '未提供记忆类型' },
        { status: 400 }
      );
    }

    console.log(`[MemoryAPI] 获取${type}记忆列表`);

    // 对于用户记忆，只返回当前用户的记忆
    // 对于系统记忆，管理员可以查看所有记忆
    const memories = await memoryService.getAllMemories(
      type,
      type === MemoryType.USER ? userId : undefined,
      page,
      pageSize
    );

    return NextResponse.json(memories);
  } catch (error: any) {
    console.error('[MemoryAPI] 获取记忆失败:', error);
    return NextResponse.json(
      { error: '获取记忆失败', details: error?.message || String(error) },
      { status: 500 }
    );
  }
}

// POST 请求处理 - 保存记忆
export async function POST(req: NextRequest) {
  try {
    console.log('[MemoryAPI] POST 请求: 保存记忆');

    // 获取当前用户ID
    const userId = await getUserIdFromRequest(req);

    // 如果未登录，返回错误
    if (!userId) {
      console.log('[MemoryAPI] 未获取到用户ID，返回401错误');
      return NextResponse.json(
        { error: '用户未登录或会话已过期' },
        { status: 401 }
      );
    }

    console.log('[MemoryAPI] 使用用户ID:', userId);

    const { type, content, metadata, infer = true } = await req.json();

    if (!type || !content) {
      console.log('[MemoryAPI] 未提供记忆类型或内容');
      return NextResponse.json(
        { error: '未提供记忆类型或内容' },
        { status: 400 }
      );
    }

    // 获取记忆服务实例
    const memoryService = getMemoryService();

    // 保存记忆
    console.log(`[MemoryAPI] 保存${type}记忆，内容：${content}, 用户:`, userId, `infer:`, infer);
    const result = await memoryService.addMemory(
      type as MemoryType,
      content,
      type === MemoryType.USER ? userId : 'system',
      {
        ...metadata,
        source: 'api',
        timestamp: new Date().toISOString()
      },
      infer
    );

    return NextResponse.json(result);
  } catch (error: any) {
    console.error('[MemoryAPI] 保存记忆失败:', error);
    return NextResponse.json(
      { error: '保存记忆失败', details: error?.message || String(error) },
      { status: 500 }
    );
  }
}

// DELETE 请求处理 - 删除记忆
export async function DELETE(req: NextRequest) {
  try {
    console.log('[MemoryAPI] DELETE 请求: 删除记忆');

    // 获取当前用户ID
    const userId = await getUserIdFromRequest(req);

    // 如果未登录，返回错误
    if (!userId) {
      console.log('[MemoryAPI] 未获取到用户ID，返回401错误');
      return NextResponse.json(
        { error: '用户未登录或会话已过期' },
        { status: 401 }
      );
    }

    console.log('[MemoryAPI] 使用用户ID:', userId);

    const url = new URL(req.url);
    const type = url.searchParams.get('type') as MemoryType;
    const memoryId = url.searchParams.get('id');
    const deleteAll = url.searchParams.get('all') === 'true';

    if (!type) {
      console.log('[MemoryAPI] 未提供记忆类型');
      return NextResponse.json(
        { error: '未提供记忆类型' },
        { status: 400 }
      );
    }

    // 获取记忆服务实例
    const memoryService = getMemoryService();

    // 如果是删除所有记忆
    if (deleteAll) {
      if (type === MemoryType.USER) {
        console.log(`[MemoryAPI] 删除用户所有记忆，用户:`, userId);
        await memoryService.deleteAllUserMemories(userId);
      } else if (type === MemoryType.SYSTEM) {
        console.log(`[MemoryAPI] 删除所有系统记忆`);
        // 确保只删除系统记忆，不影响用户记忆
        await memoryService.resetMemories(MemoryType.SYSTEM);
      }

      return NextResponse.json({ success: true });
    }

    // 否则，删除单个记忆
    if (!memoryId) {
      console.log('[MemoryAPI] 未提供记忆ID');
      return NextResponse.json(
        { error: '未提供记忆ID' },
        { status: 400 }
      );
    }

    // 如果是用户记忆，验证所有权
    if (type === MemoryType.USER) {
      const memory = await memoryService.getMemory(type, memoryId);
      if (memory && memory.userId !== userId) {
        console.log('[MemoryAPI] 记忆所有者不匹配:', memory.userId, '!=', userId);
        return NextResponse.json(
          { error: '无权删除该记忆' },
          { status: 403 }
        );
      }
    }

    console.log(`[MemoryAPI] 删除${type}记忆:`, memoryId);
    await memoryService.deleteMemory(type, memoryId);

    return NextResponse.json({ success: true });
  } catch (error: any) {
    console.error('[MemoryAPI] 删除记忆失败:', error);
    return NextResponse.json(
      { error: '删除记忆失败', details: error?.message || String(error) },
      { status: 500 }
    );
  }
}
