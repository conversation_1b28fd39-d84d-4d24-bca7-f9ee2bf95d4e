'use client';

import { useState, useEffect } from 'react';
import api from '@/utils/apiClient';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog';
import { Brain, User, BookOpen, Search, Plus, Trash2, Clock, RefreshCw, Database, HelpCircle, ShieldAlert } from 'lucide-react';
import ConfirmDialog from '@/app/components/ConfirmDialog';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Checkbox } from "@/components/ui/checkbox";
import { NumberInput } from "@/components/ui/number-input";
import '@/styles/item-design-system.css';

import { UserRole } from '@/utils/permissionConfig';
import { Access } from '@/components/Access';

// Define memory type enum (consistent with server-side)
enum MemoryType {
  USER = 'user',
  SYSTEM = 'system'
}

interface Memory {
  id: string;
  memory: string;
  userId: string;
  createdAt: string;
  score?: number; // 添加相似度分数属性
  metadata?: {
    importance?: number;
    source?: string;
    chatId?: string;
    category?: string;
    [key: string]: any;
  };
}

export default function MemoriesPage() {
  const [memories, setMemories] = useState<Memory[]>([]);
  const [selectedType, setSelectedType] = useState<string>(MemoryType.USER);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [newMemory, setNewMemory] = useState<string>('');
  const [importance, setImportance] = useState<number>(5);
  const [page, setPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(50);
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [isAddDialogOpen, setIsAddDialogOpen] = useState<boolean>(false);
  const [isConfirmDialogOpen, setIsConfirmDialogOpen] = useState<boolean>(false);
  const [inferMemory, setInferMemory] = useState<boolean>(true);

  // 不再需要直接使用 usePermission hook，因为我们使用 Access 组件

  const fetchMemories = async () => {
    setLoading(true);
    setError(null);

    try {
      const params = new URLSearchParams();
      params.append('type', selectedType);
      params.append('page', page.toString());
      params.append('pageSize', pageSize.toString());

      // 使用普通API获取所有记忆
      const { data, error: apiError } = await api.get<{ results: Memory[] }>(`/api/memory?${params.toString()}`);

      if (apiError) {
        throw new Error(apiError);
      }

      setMemories(data?.results || []);
    } catch (err) {
      console.error('Error fetching memories:', err);
      setError('Failed to fetch memories');
    } finally {
      setLoading(false);
    }
  };

  // Search memories
  const handleSearch = async () => {
    if (!searchQuery.trim()) {
      // 如果搜索查询为空，则获取所有记忆
      setPage(1);
      fetchMemories();
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const params = new URLSearchParams();
      params.append('type', selectedType);
      params.append('query', searchQuery);
      params.append('limit', '50'); // 搜索时使用较大的限制
      params.append('minScore', '0.7'); // 设置最小相似度阈值

      // 使用专门的搜索API
      const { data, error: apiError } = await api.get<{ results: Memory[] }>(`/api/memory/search?${params.toString()}`);

      if (apiError) {
        throw new Error(apiError);
      }

      setMemories(data?.results || []);

      // 记录搜索结果
      console.log(`Search for "${searchQuery}" returned ${data?.results?.length || 0} results`);

      if (data?.results?.length === 0) {
        setError(`No memories found matching "${searchQuery}"`);
      }
    } catch (err) {
      console.error('Error searching memories:', err);
      setError('Failed to search memories');
    } finally {
      setLoading(false);
    }
  };

  // 当选择类型、页码或页面大小变化时，获取记忆
  useEffect(() => {
    fetchMemories();
  }, [selectedType, page, pageSize]);

  // State for single memory deletion
  const [memoryToDelete, setMemoryToDelete] = useState<string | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState<boolean>(false);

  const handleDeleteMemory = (memoryId: string) => {
    setMemoryToDelete(memoryId);
    setIsDeleteDialogOpen(true);
  };

  const confirmDeleteMemory = async () => {
    if (!memoryToDelete) return;

    setIsDeleteDialogOpen(false);
    setLoading(true);

    try {
      const params = new URLSearchParams();
      params.append('type', selectedType);
      params.append('id', memoryToDelete);

      const { error: apiError } = await api.delete(`/api/memory?${params.toString()}`);

      if (apiError) {
        throw new Error(apiError);
      }

      // Update memory list in state
      setMemories(prev => prev.filter(memory => memory.id !== memoryToDelete));
      setMemoryToDelete(null);
    } catch (err) {
      console.error('Error deleting memory:', err);
      setError('Failed to delete memory');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteAllMemories = () => {
    setIsConfirmDialogOpen(true);
  };

  const confirmDeleteAllMemories = async () => {
    setIsConfirmDialogOpen(false);
    setLoading(true);

    try {
      const params = new URLSearchParams();
      params.append('type', selectedType); // 只删除当前选中类型的记忆
      params.append('all', 'true');

      const { error: apiError } = await api.delete(`/api/memory?${params.toString()}`);

      if (apiError) {
        throw new Error(apiError);
      }

      // 只清空当前显示的记忆列表，不影响其他类型的记忆
      setMemories([]);
    } catch (err) {
      console.error('Error deleting all memories:', err);
      setError('Failed to delete all memories');
    } finally {
      setLoading(false);
    }
  };

  const handleAddMemory = async () => {
    if (!newMemory.trim()) {
      setError('Memory content cannot be empty');
      return;
    }

    setLoading(true);

    try {
      const { error: apiError } = await api.post('/api/memory', {
        type: selectedType,
        content: newMemory,
        infer: inferMemory, // Add infer parameter
        metadata: {
          importance,
          source: 'admin',
          timestamp: new Date().toISOString()
        }
      });

      if (apiError) {
        throw new Error(apiError);
      }

      // Reset form
      setNewMemory('');
      setImportance(5);
      // Don't reset inferMemory as it's a preference

      // Close dialog
      setIsAddDialogOpen(false);

      // 刷新记忆列表，而不是直接添加到列表中
      // 这样可以确保从服务器获取完整的记忆对象
      fetchMemories();
    } catch (err) {
      console.error('Error adding memory:', err);
      setError('Failed to add memory');
    } finally {
      setLoading(false);
    }
  };

  // Memory card component
  const MemoryCard = ({ memory }: { memory: Memory }) => {
    // Format timestamp with error handling
    let formattedTime = "Invalid Date";
    try {
      if (memory.createdAt) {
        const date = new Date(memory.createdAt);
        if (!isNaN(date.getTime())) {
          formattedTime = date.toLocaleString();
        }
      }
    } catch (error) {
      console.error("Error formatting date:", error);
    }

    return (
      <div className="bg-item-bg-card/80 backdrop-blur-sm p-5 rounded-xl border border-item-gray-800 shadow-lg hover:shadow-item-purple/10 transition-all"
        style={{ borderLeft: `4px solid ${selectedType === MemoryType.USER ? 'var(--item-purple)' : 'var(--item-gray-400)'}` }}>
        <div className="flex justify-between items-start mb-3">
          <div className="flex flex-wrap gap-2">
            <Badge className={`mb-2 ${selectedType === MemoryType.USER ? 'bg-item-purple/70 hover:bg-item-purple/60' : 'bg-item-gray-700/70 hover:bg-item-gray-700/60'}`}>
              {selectedType === MemoryType.USER ? 'Personal Memory' : 'System Knowledge'}
            </Badge>
            {memory.score !== undefined && (
              <Badge variant="outline" className="border-item-purple/50 text-item-purple-light mb-2">
                Relevance: {(memory.score * 100).toFixed(2)}%
              </Badge>
            )}
          </div>
          {memory.metadata?.importance && (
            <Badge variant="outline" className="border-item-gray-400/50 text-item-gray-400">
              Importance: {memory.metadata.importance}
            </Badge>
          )}
        </div>

        <div className="text-white mb-4 text-sm">{memory.memory}</div>

        <div className="flex justify-between items-center text-sm text-item-gray-400/70">
          <div className="flex items-center gap-4">
            <span className="flex items-center text-xs">
              <Clock className="h-3 w-3 mr-1" />
              {formattedTime}
            </span>
            {memory.metadata?.source && (
              <span className="text-xs">Source: {memory.metadata.source}</span>
            )}
            <span className="text-xs">User ID: {memory.userId}</span>
          </div>

          <div className="flex gap-2">
            <Button
              variant="ghost"
              size="sm"
              className="h-7 px-2 text-red-400 hover:text-red-300 hover:bg-red-900/30"
              onClick={() => handleDeleteMemory(memory.id)}
              disabled={loading}
            >
              <Trash2 className="h-3.5 w-3.5 mr-1" />
              Delete
            </Button>
          </div>
        </div>
      </div>
    );
  };

  return (
    <>
      <div className="min-h-screen bg-item-bg-primary text-white">
        {/* Decorative overlay elements */}
        <div className="absolute top-0 left-0 right-0 h-20 bg-gradient-to-b from-item-purple/10 to-transparent pointer-events-none z-10"></div>
        <div className="absolute bottom-0 left-0 right-0 h-20 bg-gradient-to-t from-item-purple/10 to-transparent pointer-events-none z-10"></div>

        {/* Confirm Delete All Dialog */}
        <ConfirmDialog
          isOpen={isConfirmDialogOpen}
          title={`Delete All ${selectedType === MemoryType.USER ? 'Personal' : 'System Knowledge'} Memories`}
          message={`Are you sure you want to delete all ${selectedType === MemoryType.USER ? 'personal' : 'System Knowledge'} memories? This action cannot be undone and only affects ${selectedType === MemoryType.USER ? 'personal' : 'System Knowledge'} memories.`}
          onConfirm={confirmDeleteAllMemories}
          onCancel={() => setIsConfirmDialogOpen(false)}
        />

        {/* Confirm Delete Single Memory Dialog */}
        <ConfirmDialog
          isOpen={isDeleteDialogOpen}
          title="Delete Memory"
          message="Are you sure you want to delete this memory? This action cannot be undone!"
          onConfirm={confirmDeleteMemory}
          onCancel={() => setIsDeleteDialogOpen(false)}
        />

        <div className="container mx-auto px-4 py-8 relative z-20">
          <h1 className="text-3xl font-bold mb-8 text-center text-white font-mono tracking-tight w-full">Memory Management</h1>

          <div className="bg-item-bg-card/80 backdrop-blur-sm p-6 rounded-xl border border-item-gray-800 shadow-lg mb-8">
            <div className="flex items-center mb-4">
              <Brain className="h-6 w-6 mr-2 text-item-gray-400" />
              <h2 className="text-xl font-semibold text-white">AI Assistant Memory Management</h2>
            </div>
            <p className="text-white/80 mb-4">View and manage AI assistant memories, including personal memories and System Knowledge.</p>

            {error && (
              <div className="bg-red-900/30 border border-red-700/50 text-red-200 px-4 py-3 rounded-lg mb-4">
                {error}
              </div>
            )}

            <Tabs defaultValue={selectedType} value={selectedType} onValueChange={setSelectedType}>
              <div className="flex items-center justify-between mb-4">
                <div className="flex bg-item-bg-primary/60 p-1 rounded-xl shadow-inner overflow-hidden">
                  <button
                    className={`flex items-center px-5 py-2.5 font-medium text-sm transition-all duration-200 rounded-lg ${
                      selectedType === MemoryType.USER
                        ? 'bg-[#7E5FF7] text-white shadow-md shadow-[#7E5FF7]/30 transform scale-102'
                        : 'bg-transparent text-item-gray-400 hover:text-item-gray-300 hover:bg-item-bg-hover'
                    }`}
                    onClick={() => setSelectedType(MemoryType.USER)}
                  >
                    <div className={`flex items-center justify-center w-6 h-6 rounded-full mr-2 transition-all duration-200 ${
                      selectedType === MemoryType.USER
                        ? 'bg-white/10 text-white'
                        : 'bg-gray-800/40 text-item-gray-400'
                    }`}>
                      <User className="h-3.5 w-3.5" />
                    </div>
                    <span>Personal Memories</span>
                  </button>

                  {/* 使用 Access 组件控制 System Knowledge 标签页的显示 */}
                  <Access role={UserRole.ADMIN}>
                    <button
                      className={`flex items-center px-5 py-2.5 font-medium text-sm transition-all duration-200 rounded-lg ${
                        selectedType === MemoryType.SYSTEM
                          ? 'bg-[#7E5FF7] text-white shadow-md shadow-[#7E5FF7]/30 transform scale-102'
                          : 'bg-transparent text-item-gray-400 hover:text-item-gray-300 hover:bg-item-bg-hover'
                      }`}
                      onClick={() => setSelectedType(MemoryType.SYSTEM)}
                    >
                      <div className={`flex items-center justify-center w-6 h-6 rounded-full mr-2 transition-all duration-200 ${
                        selectedType === MemoryType.SYSTEM
                          ? 'bg-white/10 text-white'
                          : 'bg-gray-800/40 text-item-gray-400'
                      }`}>
                        <BookOpen className="h-3.5 w-3.5" />
                      </div>
                      <span>System Knowledge</span>
                    </button>
                  </Access>
                </div>
              </div>

              {/* Search Panel */}
              <div className="mb-6 bg-item-bg-card/60 p-6 rounded-xl border border-item-gray-800">
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium mb-1 text-white">Search Memories</label>
                    <div className="flex gap-2">
                      <Input
                        type="text"
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        placeholder="Enter search keywords"
                        disabled={loading}
                        onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                        className="bg-item-bg-primary/70 border-item-gray-800 text-white"
                      />
                      <Button
                        variant="secondary"
                        onClick={handleSearch}
                        disabled={loading}
                        className="flex-shrink-0 bg-item-bg-hover hover:bg-item-gray-700 text-white"
                      >
                        <Search className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex flex-wrap gap-2 mb-6 justify-between">
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    onClick={fetchMemories}
                    disabled={loading}
                    className="flex items-center bg-item-bg-hover border-item-gray-800 text-white hover:bg-item-gray-700"
                  >
                    <RefreshCw className="h-4 w-4 mr-1" />
                    {loading ? 'Loading...' : 'Refresh'}
                  </Button>

                  {selectedType === MemoryType.USER ? (
                    // 个人记忆删除按钮 - 所有用户都可以使用
                    <Button
                      variant="destructive"
                      onClick={handleDeleteAllMemories}
                      disabled={loading}
                      className="flex items-center bg-red-900/30 border-red-700/50 hover:bg-red-800/50"
                    >
                      <Trash2 className="h-4 w-4 mr-1" />
                      Delete All Personal Memories
                    </Button>
                  ) : (
                    // 知识库删除按钮 - 使用 Access 组件控制
                    <Access
                      auth="admin"
                      fallback={
                        <Button
                          variant="destructive"
                          disabled={true}
                          className="flex items-center bg-red-900/30 border-red-700/50"
                        >
                          <Trash2 className="h-4 w-4 mr-1" />
                          Delete All System Knowledge Memories
                          <span className="ml-2 text-xs bg-red-800/50 px-1.5 py-0.5 rounded">Admin Only</span>
                        </Button>
                      }
                    >
                      <Button
                        variant="destructive"
                        onClick={handleDeleteAllMemories}
                        disabled={loading}
                        className="flex items-center bg-red-900/30 border-red-700/50 hover:bg-red-800/50"
                      >
                        <Trash2 className="h-4 w-4 mr-1" />
                        Delete All System Knowledge Memories
                      </Button>
                    </Access>
                  )}
                </div>

                <Button
                  onClick={() => setIsAddDialogOpen(true)}
                  className="flex items-center bg-[#7E5FF7] hover:bg-[#7E5FF7]/90 text-white"
                >
                  <Plus className="h-4 w-4 mr-1" />
                  Add New Memory
                </Button>
              </div>

              <TabsContent value={MemoryType.USER}>
                {loading ? (
                  <div className="flex justify-center items-center h-32">
                    <div className="animate-pulse text-item-gray-400">Loading...</div>
                  </div>
                ) : memories.length === 0 ? (
                  <div className="text-center py-12 bg-item-bg-card/40 rounded-xl border border-item-gray-800/20 p-8">
                    <User className="h-12 w-12 mx-auto mb-3 text-item-gray-400/50" />
                    <p className="text-item-gray-400">No personal memories found</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {memories.map((memory) => (
                      <MemoryCard key={memory.id} memory={memory} />
                    ))}
                  </div>
                )}
              </TabsContent>

              {/* 使用 Access 组件控制 System Knowledge 内容的显示 */}
              <Access role={UserRole.ADMIN}>
                <TabsContent value={MemoryType.SYSTEM}>
                  {loading ? (
                    <div className="flex justify-center items-center h-32">
                      <div className="animate-pulse text-item-gray-400">Loading...</div>
                    </div>
                  ) : memories.length === 0 ? (
                    <div className="text-center py-12 bg-item-bg-card/40 rounded-xl border border-item-gray-800/20 p-8">
                      <BookOpen className="h-12 w-12 mx-auto mb-3 text-item-gray-400/50" />
                      <p className="text-item-gray-400">No System Knowledge memories found</p>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {memories.map((memory) => (
                        <MemoryCard key={memory.id} memory={memory} />
                      ))}
                    </div>
                  )}
                </TabsContent>
              </Access>

              {/* Pagination Controls */}
              <div className="mt-8 flex justify-between items-center bg-item-bg-card/60 p-4 rounded-xl border border-item-gray-800">
                <div className="flex items-center gap-4">
                  <Button
                    variant="outline"
                    onClick={() => setPage(prev => Math.max(prev - 1, 1))}
                    disabled={loading || page === 1}
                    className="bg-item-bg-hover border-item-gray-800 text-white hover:bg-item-gray-700"
                  >
                    Previous Page
                  </Button>
                  <span className="py-2 text-white font-mono">Page {page}</span>
                  <Button
                    variant="outline"
                    onClick={() => setPage(prev => prev + 1)}
                    disabled={loading || memories.length < pageSize}
                    className="bg-item-bg-hover border-item-gray-800 text-white hover:bg-item-gray-700"
                  >
                    Next Page
                  </Button>
                </div>

                <div className="flex items-center gap-2">
                  <span className="text-sm text-white">Items Per Page:</span>
                  <select
                    value={pageSize}
                    onChange={(e) => setPageSize(parseInt(e.target.value))}
                    className="flex h-9 rounded-md border border-item-gray-800 bg-item-bg-primary/70 px-3 py-1 text-sm text-white"
                    disabled={loading}
                  >
                    <option value={5}>5</option>
                    <option value={10}>10</option>
                    <option value={20}>20</option>
                    <option value={50}>50</option>
                    <option value={100}>100</option>
                  </select>
                </div>
              </div>
            </Tabs>
          </div>
        </div>
      </div>

      {/* Add Memory Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent className="bg-item-bg-card border-item-gray-800 text-white sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="text-white">Add New Memory</DialogTitle>
            <DialogDescription className="text-item-gray-400/70">
              Create a new memory for the AI assistant. This will be stored in the {selectedType === MemoryType.USER ? 'personal' : 'System Knowledge'} memory.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            <div>
              <label className="block text-sm font-medium mb-1 text-white">Memory Content</label>
              <Textarea
                value={newMemory}
                onChange={(e) => setNewMemory(e.target.value)}
                placeholder="Enter memory content..."
                disabled={loading}
                className="min-h-[120px] bg-item-bg-primary/70 border-item-gray-800 text-white"
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-1 text-white">Importance (1-10)</label>
              <NumberInput
                value={importance}
                onChange={setImportance}
                min={1}
                max={10}
                disabled={loading}
                className="w-full"
              />
            </div>

            <div className="flex items-center space-x-2">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="infer"
                  checked={inferMemory}
                  onCheckedChange={(checked: boolean | "indeterminate") => setInferMemory(checked === true)}
                  disabled={loading}
                />
                <label
                  htmlFor="infer"
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 text-white"
                >
                  Infer Memory
                </label>
              </div>

              <TooltipProvider delayDuration={50} skipDelayDuration={0}>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div className="cursor-help text-item-gray-400 hover:text-white">
                      <HelpCircle size={16} />
                    </div>
                  </TooltipTrigger>
                  <TooltipContent className="max-w-xs">
                    <p>When enabled, Mem0 will use AI to process and enhance the memory content. When disabled, the memory will be stored exactly as entered without AI processing.</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          </div>

          <DialogFooter className="sm:justify-between">
            <Button
              variant="ghost"
              onClick={() => setIsAddDialogOpen(false)}
              disabled={loading}
              className="text-item-gray-400 hover:text-white hover:bg-item-bg-hover/50"
            >
              Cancel
            </Button>
            <Button
              onClick={handleAddMemory}
              disabled={loading || !newMemory.trim()}
              className="bg-[#7E5FF7] hover:bg-[#7E5FF7]/90 text-white"
            >
              {loading ? 'Adding...' : 'Add Memory'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
