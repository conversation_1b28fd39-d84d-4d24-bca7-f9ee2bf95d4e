/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  webpack: (config, { isServer }) => {
    // 只在客户端构建时应用此配置
    if (!isServer) {
      // Node模块在客户端不可用
      config.resolve.fallback = {
        fs: false,
        path: false,
        os: false,
        crypto: false,
        net: false,
        tls: false,
        dns: false,
        child_process: false,
        stream: false,
        http: false,
        https: false,
        zlib: false,
        util: false,
        url: false,
        querystring: false,
      };
    }

    // 将 LanceDB 添加到 externals 中，避免 webpack 打包它
    if (isServer) {
      config.externals.push('lancedb');
    }

    return config;
  },
  env: {
    NEXT_PUBLIC_IAM_ENDPOINT: process.env.NEXT_PUBLIC_IAM_ENDPOINT,
    NEXT_PUBLIC_WMS_ENDPOINT: process.env.NEXT_PUBLIC_WMS_ENDPOINT,
  },
  experimental: {
    // Next.js 15+ 需要将 serverActions 改为对象
    serverActions: {
      allowedOrigins: ["localhost:3000"],
      bodySizeLimit: "2mb",
    },
  },
  // 添加开发环境的代理配置
  async rewrites() {
    return [
      {
        source: '/api/wms-bam/:path*',
        destination: 'https://wms-staging.item.com/api/wms-bam/:path*',
      },
      {
        source: '/api/wms/:path*',
        destination: 'https://wms-staging.item.com/api/wms/:path*',
      },
      {
        source: '/api/mdm/:path*',
        destination: 'https://wms-staging.item.com/api/mdm/:path*',
      },
    ];
  },
};

module.exports = nextConfig;