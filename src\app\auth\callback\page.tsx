'use client';

import React, { useEffect, useState, Suspense } from 'react';
import { useSearchParams } from 'next/navigation';
import { useAuth } from '@/app/contexts/AuthContext';
import '@/styles/item-design-system.css';

// Create an internal component to use useSearchParams
function CallbackContent() {
  const searchParams = useSearchParams();
  const { error } = useAuth();
  const [debugInfo, setDebugInfo] = useState<any>(null);
  
  useEffect(() => {
    // Collect debug information
    const code = searchParams.get('code');
    const state = searchParams.get('state');
    const savedState = typeof window !== 'undefined' ? localStorage.getItem('iam_auth_state') : null;
    
    const info = {
      hasCode: !!code,
      codeLength: code ? code.length : 0,
      hasState: !!state,
      stateLength: state ? state.length : 0,
      stateMatches: state === savedState,
      env: {
        hasClientId: !!process.env.NEXT_PUBLIC_IAM_CLIENT_ID,
        clientIdValue: process.env.NEXT_PUBLIC_IAM_CLIENT_ID ? process.env.NEXT_PUBLIC_IAM_CLIENT_ID.substring(0, 5) + '...' : null,
        currentDomain: typeof window !== 'undefined' ? window.location.host : 'server-side',
        dynamicRedirectUri: typeof window !== 'undefined' ? `${window.location.protocol}//${window.location.host}/auth/callback` : 'N/A',
        isClient: typeof window !== 'undefined',
      },
      error: error,
      fixStatus: ''
    };
    
    console.log('Authorization callback debug info:', info);
    setDebugInfo(info);
  }, [searchParams, error]);

  return (
    <div className="max-w-md w-full p-8 bg-item-bg-card rounded-lg shadow-lg border border-item-gray-800">
      <h1 className="text-2xl font-bold text-center mb-6 text-white">Processing Login...</h1>
      
      {error ? (
        <div className="p-4 bg-red-900/30 border border-red-800 rounded-md mb-4">
          <p className="text-red-300">{error}</p>
          
          {debugInfo && (
            <div className="mt-4 p-3 bg-item-bg-primary rounded text-xs overflow-auto">
              <pre className="text-item-gray-300">{JSON.stringify(debugInfo, null, 2)}</pre>
            </div>
          )}
        </div>
      ) : (
        <div className="flex flex-col items-center">
          <div className="w-16 h-16 border-t-4 border-item-purple border-solid rounded-full animate-spin mb-4"></div>
          <p className="text-item-gray-400">Processing login request...</p>
        </div>
      )}
    </div>
  );
}

// Loading component
function LoadingCallback() {
  return (
    <div className="max-w-md w-full p-8 bg-item-bg-card rounded-lg shadow-lg border border-item-gray-800">
      <h1 className="text-2xl font-bold text-center mb-6 text-white">Loading...</h1>
      <div className="flex flex-col items-center">
        <div className="w-16 h-16 border-t-4 border-item-purple border-solid rounded-full animate-spin mb-4"></div>
        <p className="text-item-gray-400">Initializing...</p>
      </div>
    </div>
  );
}

export default function AuthCallbackPage() {
  return (
    <div className="flex flex-col items-center justify-center h-screen bg-item-bg-primary text-white">
      <Suspense fallback={<LoadingCallback />}>
        <CallbackContent />
      </Suspense>
    </div>
  );
} 