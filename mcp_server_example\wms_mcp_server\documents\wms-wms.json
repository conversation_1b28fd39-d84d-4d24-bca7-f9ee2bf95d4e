{"openapi": "3.1.0", "info": {"title": "WMS", "description": "", "version": "1.0.0"}, "tags": [{"name": "appointment"}], "paths": {"/wms-bam/db-change-logs/search-by-paging": {"post": {"summary": "DB Change Log", "deprecated": false, "description": "", "tags": [], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DbChangeLogQuery"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseObject"}}}, "headers": {}}}, "security": []}}, "/wms-bam/inbound/receipt/search-by-paging": {"post": {"summary": "Search Receipts by Paging.", "deprecated": false, "description": "", "tags": ["appointment"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReceiptSearch", "description": "the search requests"}, "example": {"currentPage": 0, "pageSize": 0, "sortingFields": [{"field": "string", "orderBy": "NONE"}], "receiptIds": ["string"], "receiptId": "string", "regexReceiptId": "string", "omsId": "string", "omsIds": ["string"], "excludeReceiptIds": ["string"], "referenceNo": "string", "poNo": "string", "eqPoNo": "string", "eqPoNos": ["string"], "bolNo": "string", "exactlyBolNo": "string", "exactlyBolNos": ["string"], "status": "IMPORTED", "statuses": ["IMPORTED"], "excludeStatuses": ["string"], "excludeReceiptTypes": ["REGULAR_RECEIPT"], "receiptType": "REGULAR_RECEIPT", "receiveType": "BULK_RECEIVING", "receiptSubType": "string", "receiptSubTypes": ["string"], "customerIds": ["string"], "customerId": "string", "titleIds": ["string"], "carrierIds": ["string"], "containerNo": "string", "eqContainerNo": "string", "eqContainerNos": ["string"], "trailerNos": ["string"], "trailerNo": "string", "referenceNos": ["string"], "eqReferenceNo": "string", "eqReferenceNos": ["string"], "poNos": ["string"], "bolNos": ["string"], "sealNo": "string", "sealNos": ["string"], "appointmentTimeFrom": "string", "appointmentTimeTo": "string", "receivedTimeFrom": "string", "receivedTimeTo": "string", "isRush": true, "trackingNo": "string", "eqTrackingNo": "string", "createdBy": "string", "createdTimeFrom": "string", "createdTimeTo": "string", "updatedTimeFrom": "string", "updatedTimeTo": "string", "sources": ["EDI"], "excludeSources": ["string"], "inYardTimeFrom": "string", "inYardTimeTo": "string", "trackingNos": ["string"], "note": "string", "withItemLine": true}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/R%C2%ABPageResult%C2%ABBAMReceiptDto%C2%BB%C2%BB", "description": "the search result with pagination"}, "examples": {"1": {"summary": "成功示例", "value": {"code": 0, "msg": "", "success": false, "data": {"list": [{"id": "", "status": "", "omsId": "", "receiptType": "", "receiptSubType": "", "carrierId": "", "carrierName": "", "customerId": "", "customerName": "", "titleId": "", "titleName": "", "poNo": "", "referenceNo": "", "sealNo": "", "shippingMethod": "", "source": "", "dynamicFields": {"dynTxtPropertyValue01": "", "dynTxtPropertyValue02": "", "dynTxtPropertyValue03": "", "dynTxtPropertyValue04": "", "dynTxtPropertyValue05": "", "dynTxtPropertyValue06": "", "dynTxtPropertyValue07": "", "dynTxtPropertyValue08": "", "dynTxtPropertyValue09": "", "dynTxtPropertyValue10": "", "dynTxtPropertyValue11": "", "dynTxtPropertyValue12": "", "dynTxtPropertyValue13": "", "dynTxtPropertyValue14": "", "dynTxtPropertyValue15": "", "dynTxtPropertyValue16": "", "dynTxtPropertyValue17": "", "dynTxtPropertyValue18": "", "dynTxtPropertyValue19": "", "dynTxtPropertyValue20": "", "dynTxtPropertyValue21": "", "dynTxtPropertyValue22": "", "dynTxtPropertyValue23": "", "dynTxtPropertyValue24": "", "dynTxtPropertyValue25": "", "dynDatePropertyValue01": "", "dynDatePropertyValue02": "", "dynDatePropertyValue03": "", "dynDatePropertyValue04": "", "dynDatePropertyValue05": ""}, "appointmentTime": "", "closedTime": "", "scheduleDate": "", "inYardTime": "", "receivedTime": "", "isRush": false, "note": "", "sysNote": "", "bolNo": "", "mbolNo": "", "containerNo": "", "containerSize": "", "exceptionReason": "", "shippingInstruction": "", "trailerNo": "", "trailerSize": "", "trackingNo": "", "itemLines": [{"id": "", "receiptId": "", "itemId": "", "itemName": "", "qty": 0, "uomId": "", "uomName": "", "titleId": "", "titleName": "", "lotNo": "", "destination": "", "destinationAddress": {"name": "", "country": "", "state": "", "city": "", "zipCode": "", "fax": "", "address1": "", "address2": "", "contact": "", "phone": "", "extension": "", "email": ""}, "destinationGroup": "", "supplierId": "", "supplierName": "", "expectedPalletQty": 0, "goodsType": "", "palletQty": 0, "receivedQty": 0, "receivedUomId": "", "expirationDate": "", "manufactureDate": "", "note": "", "dynamicFields": {"dynTxtPropertyValue01": "", "dynTxtPropertyValue02": "", "dynTxtPropertyValue03": "", "dynTxtPropertyValue04": "", "dynTxtPropertyValue05": "", "dynTxtPropertyValue06": "", "dynTxtPropertyValue07": "", "dynTxtPropertyValue08": "", "dynTxtPropertyValue09": "", "dynTxtPropertyValue10": "", "dynTxtPropertyValue11": "", "dynTxtPropertyValue12": "", "dynTxtPropertyValue13": "", "dynTxtPropertyValue14": "", "dynTxtPropertyValue15": "", "dynTxtPropertyValue16": "", "dynTxtPropertyValue17": "", "dynTxtPropertyValue18": "", "dynTxtPropertyValue19": "", "dynTxtPropertyValue20": "", "dynTxtPropertyValue21": "", "dynTxtPropertyValue22": "", "dynTxtPropertyValue23": "", "dynTxtPropertyValue24": "", "dynTxtPropertyValue25": "", "dynDatePropertyValue01": "", "dynDatePropertyValue02": "", "dynDatePropertyValue03": "", "dynDatePropertyValue04": "", "dynDatePropertyValue05": ""}, "lineNo": "", "snList": [""], "cartonNos": [""], "createdBy": "", "createdTime": "", "updatedBy": "", "updatedTime": ""}], "createdBy": "", "createdTime": "", "updatedBy": "", "updatedTime": ""}], "totalCount": 0, "currentPage": 0, "pageSize": 0, "totalPage": 0}}}}}}, "headers": {}}}, "security": []}}, "/wms-bam/inbound/receipt/item-line/search": {"post": {"summary": "Search Receipt Itemlines", "deprecated": false, "description": "", "tags": [], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReceiptItemLineQuery", "description": ""}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RListReceiptItemLineDto", "description": ""}, "examples": {"1": {"summary": "成功示例", "value": {"code": 0, "msg": "", "success": false, "data": [{"id": "", "receiptId": "", "itemId": "", "qty": 0, "uomId": "", "titleId": "", "lotNo": "", "supplierId": "", "expectedPalletQty": 0, "goodsType": "", "palletQty": 0, "receivedQty": 0, "receivedUomId": "", "expirationDate": "", "manufactureDate": "", "note": "", "dynamicFields": {"dynTxtPropertyValue01": "", "dynTxtPropertyValue02": "", "dynTxtPropertyValue03": "", "dynTxtPropertyValue04": "", "dynTxtPropertyValue05": "", "dynTxtPropertyValue06": "", "dynTxtPropertyValue07": "", "dynTxtPropertyValue08": "", "dynTxtPropertyValue09": "", "dynTxtPropertyValue10": "", "dynTxtPropertyValue11": "", "dynTxtPropertyValue12": "", "dynTxtPropertyValue13": "", "dynTxtPropertyValue14": "", "dynTxtPropertyValue15": "", "dynTxtPropertyValue16": "", "dynTxtPropertyValue17": "", "dynTxtPropertyValue18": "", "dynTxtPropertyValue19": "", "dynTxtPropertyValue20": "", "dynTxtPropertyValue21": "", "dynTxtPropertyValue22": "", "dynTxtPropertyValue23": "", "dynTxtPropertyValue24": "", "dynTxtPropertyValue25": "", "dynDatePropertyValue01": "", "dynDatePropertyValue02": "", "dynDatePropertyValue03": "", "dynDatePropertyValue04": "", "dynDatePropertyValue05": ""}, "lineNo": "", "snList": [""], "cartonNos": [""], "createdBy": "", "createdTime": "", "updatedBy": "", "updatedTime": ""}]}}}}}, "headers": {}}}, "security": []}}, "/wms-bam/inbound/receive-task/search-by-paging": {"post": {"summary": "Search By Paging Receive Task", "deprecated": false, "description": "", "tags": [], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReceiveTaskQuery", "description": ""}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/R%C2%ABPageResult%C2%ABReceiveTaskView%C2%BB%C2%BB"}, "examples": {"1": {"summary": "成功示例", "value": {"code": 0, "msg": "", "success": false, "data": {"list": [{"id": "", "customerId": "", "status": "", "note": "", "tags": [""], "preAssigneeUserId": "", "assigneeUserId": "", "lastAssignedWhen": "", "priority": "", "startTime": "", "endTime": "", "taskSteps": [{"id": 0, "customerId": "", "status": "", "exceptionCode": "", "sysNote": "", "note": "", "stepType": "", "taskId": "", "taskType": "", "stepSequence": 0, "assigneeUserIds": [""], "lastAssignedWhen": "", "priority": "", "startTime": "", "endTime": ""}], "createdTime": "", "createdBy": "", "updatedTime": "", "updatedBy": "", "sysNote": "", "dockId": "", "entryId": "", "receiptIds": [""], "receipts": [{"id": "", "status": "", "omsId": "", "receiptType": "", "receiptSubType": "", "carrierId": "", "customerId": "", "titleId": "", "poNo": "", "referenceNo": "", "sealNo": "", "shippingMethod": "", "source": "", "dynamicFields": {"dynTxtPropertyValue01": "", "dynTxtPropertyValue02": "", "dynTxtPropertyValue03": "", "dynTxtPropertyValue04": "", "dynTxtPropertyValue05": "", "dynTxtPropertyValue06": "", "dynTxtPropertyValue07": "", "dynTxtPropertyValue08": "", "dynTxtPropertyValue09": "", "dynTxtPropertyValue10": "", "dynTxtPropertyValue11": "", "dynTxtPropertyValue12": "", "dynTxtPropertyValue13": "", "dynTxtPropertyValue14": "", "dynTxtPropertyValue15": "", "dynTxtPropertyValue16": "", "dynTxtPropertyValue17": "", "dynTxtPropertyValue18": "", "dynTxtPropertyValue19": "", "dynTxtPropertyValue20": "", "dynTxtPropertyValue21": "", "dynTxtPropertyValue22": "", "dynTxtPropertyValue23": "", "dynTxtPropertyValue24": "", "dynTxtPropertyValue25": "", "dynDatePropertyValue01": "", "dynDatePropertyValue02": "", "dynDatePropertyValue03": "", "dynDatePropertyValue04": "", "dynDatePropertyValue05": ""}, "appointmentTime": "", "closedTime": "", "scheduleDate": "", "inYardTime": "", "receivedTime": "", "isRush": false, "note": "", "sysNote": "", "bolNo": "", "mbolNo": "", "containerNo": "", "containerSize": "", "exceptionReason": "", "shippingInstruction": "", "trailerNo": "", "trailerSize": "", "trackingNo": "", "itemLines": [{"id": "", "receiptId": "", "itemId": "", "qty": 0, "uomId": "", "titleId": "", "lotNo": "", "destination": "", "destinationAddress": {"name": "", "country": "", "state": "", "city": "", "zipCode": "", "fax": "", "address1": "", "address2": "", "contact": "", "phone": "", "extension": "", "email": ""}, "destinationGroup": "", "supplierId": "", "expectedPalletQty": 0, "goodsType": "", "palletQty": 0, "receivedQty": 0, "receivedUomId": "", "expirationDate": "", "manufactureDate": "", "note": "", "dynamicFields": {"dynTxtPropertyValue01": "", "dynTxtPropertyValue02": "", "dynTxtPropertyValue03": "", "dynTxtPropertyValue04": "", "dynTxtPropertyValue05": "", "dynTxtPropertyValue06": "", "dynTxtPropertyValue07": "", "dynTxtPropertyValue08": "", "dynTxtPropertyValue09": "", "dynTxtPropertyValue10": "", "dynTxtPropertyValue11": "", "dynTxtPropertyValue12": "", "dynTxtPropertyValue13": "", "dynTxtPropertyValue14": "", "dynTxtPropertyValue15": "", "dynTxtPropertyValue16": "", "dynTxtPropertyValue17": "", "dynTxtPropertyValue18": "", "dynTxtPropertyValue19": "", "dynTxtPropertyValue20": "", "dynTxtPropertyValue21": "", "dynTxtPropertyValue22": "", "dynTxtPropertyValue23": "", "dynTxtPropertyValue24": "", "dynTxtPropertyValue25": "", "dynDatePropertyValue01": "", "dynDatePropertyValue02": "", "dynDatePropertyValue03": "", "dynDatePropertyValue04": "", "dynDatePropertyValue05": ""}, "lineNo": "", "snList": [""], "cartonNos": [""], "createdBy": "", "createdTime": "", "updatedBy": "", "updatedTime": ""}], "createdBy": "", "createdTime": "", "updatedBy": "", "updatedTime": ""}], "dockName": "", "customerName": "", "assigneeUserName": ""}], "totalCount": 0, "currentPage": 0, "pageSize": 0, "totalPage": 0}}}}}}, "headers": {}}}, "security": []}}, "/wms-bam/outbound/order/search-by-paging": {"post": {"summary": "Search Order by Paging", "deprecated": false, "description": "", "tags": [], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderQuery", "description": "The query parameters for searching orders with pagination"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/R%C2%ABPageResult%C2%ABBAMOrderDto%C2%BB%C2%BB", "description": "Paginated list of orders"}, "examples": {"1": {"summary": "成功示例", "value": {"code": 0, "msg": "", "success": false, "data": {}}}}}}, "headers": {}}}, "security": []}}, "/wms-bam/outbound/order-plan/search-by-paging": {"post": {"summary": "Search Order Plan by Paging", "deprecated": false, "description": "", "tags": [], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderPlanQuery", "description": ""}, "example": {"currentPage": 1, "pageSize": 1}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/R%C2%ABPageResult%C2%ABOrderPlanDto%C2%BB%C2%BB", "description": ""}, "examples": {"1": {"summary": "成功示例", "value": {"code": 0, "msg": "", "success": false, "data": {"list": [{"id": "", "pickType": "", "pickMethod": "", "enableAutoGroup": false, "orderPlanItemLineGroupBys": [""], "defaultAssigneeUserId": "", "orderIds": [""], "pickTaskIds": [""], "replenishmentTaskIds": [""], "status": "", "customerId": "", "orderDispatchSettingId": "", "taskTags": [""], "taskPriority": "", "maxOrderQtyPerPickTask": 0, "maxItemLineQtyPerPickTask": 0, "shippingRule": "", "removePlanOrdersWithErrorAndMarkException": false, "noNeedAutoRelease": false, "createdBy": "", "createdTime": "", "updatedBy": "", "updatedTime": ""}], "totalCount": 0, "currentPage": 0, "pageSize": 0, "totalPage": 0}}}}}}, "headers": {}}}, "security": []}}, "/wms-bam/outbound/load/search-by-paging": {"post": {"summary": "Load Paging Search", "deprecated": false, "description": "", "tags": ["appointment"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BAMLoadQuery", "description": ""}, "example": {"currentPage": 0, "pageSize": 0, "sortingFields": [{"field": "string", "orderBy": "NONE"}], "status": "NEW", "id": "string", "ids": ["string"], "statuses": ["NEW"], "loadNo": "string", "customerId": "string", "carrierId": "string", "proNo": "string", "freightTerm": "COLLECT", "loadType": "LTL", "loadTypes": ["LTL"], "masterBolNo": "string", "startTimePeriod": ["string"], "endTimePeriod": ["string"], "trailerNo": "string", "destination": "string", "orderId": "string"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/R%C2%ABPageResult%C2%ABBAMLoadDto%C2%BB%C2%BB", "description": ""}, "examples": {"1": {"summary": "成功示例", "value": {"code": 0, "msg": "", "success": false, "data": {"list": [{"createdTime": "", "createdBy": "", "updatedTime": "", "updatedBy": "", "id": "", "status": "", "loadNo": "", "customerId": "", "customerName": "", "carrierId": "", "carrierName": "", "proNo": "", "freightTerm": "", "freightCost": 0, "loadType": "", "appointmentTime": "", "masterBolNo": "", "shipFrom": {"name": "", "country": "", "state": "", "city": "", "zipCode": "", "fax": "", "address1": "", "address2": "", "contact": "", "phone": "", "extension": "", "email": ""}, "shipTo": {"name": "", "country": "", "state": "", "city": "", "zipCode": "", "fax": "", "address1": "", "address2": "", "contact": "", "phone": "", "extension": "", "email": ""}, "note": "", "startTime": "", "endTime": "", "sequence": 0, "trailerNo": "", "trailerPickUpMode": "", "loadPhotos": [""], "countingSheetPhotos": [""], "carrierSignatureFileId": "", "shipperSignatureFileId": "", "carrierSignatureTime": "", "shipperSignatureTime": "", "destination": "", "orderLines": [{"id": 0, "omsId": "", "omsMasterId": "", "status": "", "customerId": "", "carrierId": "", "retailerId": "", "shipMethod": "", "deliveryService": "", "orderType": "", "source": "", "referenceNo": "", "batchNo": "", "poNo": "", "soNo": "", "shippingAccountNo": "", "freightTerm": "", "freightCost": 0, "subOrderType": "", "shipFromAddress": {"name": "", "country": "", "state": "", "city": "", "zipCode": "", "fax": "", "address1": "", "address2": "", "contact": "", "phone": "", "extension": "", "email": ""}, "skipCollectSn": false, "masterTrackingNo": "", "labelCode": "", "isInternational": false, "containHazardous": false, "exceptionReason": "", "incoterm": "", "mbolNo": "", "proNo": "", "bolNo": "", "loadNo": "", "orderedDate": "", "shipNotBefore": "", "shipNoLater": "", "totalWeight": 0, "totalCubicFeet": 0, "containerSize": "", "mabd": "", "appointmentTime": "", "inYardTime": "", "canceledDate": "", "scheduleDate": "", "shippedTime": "", "soldToAddress": {"name": "", "country": "", "state": "", "city": "", "zipCode": "", "fax": "", "address1": "", "address2": "", "contact": "", "phone": "", "extension": "", "email": ""}, "shipToAddress": {"name": "", "country": "", "state": "", "city": "", "zipCode": "", "fax": "", "address1": "", "address2": "", "contact": "", "phone": "", "extension": "", "email": ""}, "storeAddress": {"name": "", "country": "", "state": "", "city": "", "zipCode": "", "fax": "", "address1": "", "address2": "", "contact": "", "phone": "", "extension": "", "email": ""}, "billToAddress": {"name": "", "country": "", "state": "", "city": "", "zipCode": "", "fax": "", "address1": "", "address2": "", "contact": "", "phone": "", "extension": "", "email": ""}, "createdBy": "", "createdTime": "", "updatedBy": "", "updatedTime": "", "historyStatuses": [""], "dynamicFields": {"dynTxtPropertyValue01": "", "dynTxtPropertyValue02": "", "dynTxtPropertyValue03": "", "dynTxtPropertyValue04": "", "dynTxtPropertyValue05": "", "dynTxtPropertyValue06": "", "dynTxtPropertyValue07": "", "dynTxtPropertyValue08": "", "dynTxtPropertyValue09": "", "dynTxtPropertyValue10": "", "dynTxtPropertyValue11": "", "dynTxtPropertyValue12": "", "dynTxtPropertyValue13": "", "dynTxtPropertyValue14": "", "dynTxtPropertyValue15": "", "dynTxtPropertyValue16": "", "dynTxtPropertyValue17": "", "dynTxtPropertyValue18": "", "dynTxtPropertyValue19": "", "dynTxtPropertyValue20": "", "dynTxtPropertyValue21": "", "dynTxtPropertyValue22": "", "dynTxtPropertyValue23": "", "dynTxtPropertyValue24": "", "dynTxtPropertyValue25": "", "dynDatePropertyValue01": "", "dynDatePropertyValue02": "", "dynDatePropertyValue03": "", "dynDatePropertyValue04": "", "dynDatePropertyValue05": ""}, "totalPallets": 0, "uniqueKey": "", "isRush": false, "isSingleQTY": false, "sysNote": "", "blockNote": "", "orderNote": "", "cancelledNote": "", "isUCCLabelPrinted": false, "isPackingListPrinted": false, "isPalletLabelPrinted": false, "destination": "", "itemLines": [{"id": "", "orderId": "", "qty": 0, "itemId": "", "uomId": "", "titleId": "", "lotNo": "", "upcCode": "", "destination": "", "destinationAddress": {"name": "", "country": "", "state": "", "city": "", "zipCode": "", "fax": "", "address1": "", "address2": "", "contact": "", "phone": "", "extension": "", "email": ""}, "destinationGroup": "", "supplierId": "", "shippedQty": 0, "palletQty": 0, "adjustedPalletQty": 0, "orderedQty": 0, "palletLength": 0, "palletWidth": 0, "palletHeight": 0, "palletWeight": 0, "lineNo": "", "lpConfigurationId": "", "note": "", "kitItemId": "", "kitItemlineId": "", "itemCondition": "", "totalInsuranceAmount": 0, "unitPrice": 0, "dynamicFields": {"dynTxtPropertyValue01": "", "dynTxtPropertyValue02": "", "dynTxtPropertyValue03": "", "dynTxtPropertyValue04": "", "dynTxtPropertyValue05": "", "dynTxtPropertyValue06": "", "dynTxtPropertyValue07": "", "dynTxtPropertyValue08": "", "dynTxtPropertyValue09": "", "dynTxtPropertyValue10": "", "dynTxtPropertyValue11": "", "dynTxtPropertyValue12": "", "dynTxtPropertyValue13": "", "dynTxtPropertyValue14": "", "dynTxtPropertyValue15": "", "dynTxtPropertyValue16": "", "dynTxtPropertyValue17": "", "dynTxtPropertyValue18": "", "dynTxtPropertyValue19": "", "dynTxtPropertyValue20": "", "dynTxtPropertyValue21": "", "dynTxtPropertyValue22": "", "dynTxtPropertyValue23": "", "dynTxtPropertyValue24": "", "dynTxtPropertyValue25": "", "dynDatePropertyValue01": "", "dynDatePropertyValue02": "", "dynDatePropertyValue03": "", "dynDatePropertyValue04": "", "dynDatePropertyValue05": ""}, "snList": [""], "createdTime": "", "createdBy": "", "updatedTime": "", "updatedBy": "", "baseQty": 0, "goodsType": ""}], "loadId": "", "orderId": "", "sequence": 0, "customerName": "", "carrierName": "", "retailerName": ""}]}], "totalCount": 0, "currentPage": 0, "pageSize": 0, "totalPage": 0}}}}}}, "headers": {}}}, "security": []}}, "/wms-bam/outbound/pick-task/search": {"post": {"summary": "Search Pick Task", "deprecated": false, "description": "", "tags": [], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PickTaskQuery", "description": ""}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RBAMPickItemLineDto"}, "examples": {"1": {"summary": "成功示例", "value": {"code": 0, "msg": "", "success": false, "data": {"id": "", "customerId": "", "status": "", "note": "", "tags": [""], "preAssigneeUserId": "", "assigneeUserId": "", "lastAssignedWhen": "", "priority": "", "startTime": "", "endTime": "", "taskSteps": [{"id": 0, "customerId": "", "status": "", "exceptionCode": "", "sysNote": "", "note": "", "stepType": "", "taskId": "", "taskType": "", "stepSequence": 0, "assigneeUserIds": [""], "lastAssignedWhen": "", "priority": "", "startTime": "", "endTime": ""}], "createdTime": "", "createdBy": "", "updatedTime": "", "updatedBy": "", "orderPlanId": "", "pickType": "", "pickMethod": "", "pickMode": "", "taskHLPId": "", "isRush": false, "skipCLP": false, "shippingRule": "", "exceptionCode": "", "sysNote": "", "orderIds": [""], "pickItemLines": [{"createdTime": "", "createdBy": "", "updatedTime": "", "updatedBy": "", "id": "", "pickTaskId": "", "itemId": "", "uomId": "", "titleId": "", "lotNo": "", "qty": 0, "baseQty": 0, "goodsType": "", "orderId": ""}], "customerName": "", "retailerName": "", "carrierName": "", "assigneeUserName": "", "preAssigneeUserName": ""}}}}}}, "headers": {}}}, "security": []}}, "/wms-bam/outbound/load-task/search-by-paging": {"post": {"summary": "Load Task Paging Search", "deprecated": false, "description": "", "tags": [], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BAMLoadTaskQuery", "description": ""}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/R%C2%ABPageResult%C2%ABBAMLoadTaskDto%C2%BB%C2%BB", "description": ""}, "examples": {"1": {"summary": "成功示例", "value": {"code": 0, "msg": "", "success": false, "data": {"list": [{"createdTime": "", "createdBy": "", "updatedTime": "", "updatedBy": "", "id": "", "status": "", "loadNo": "", "customerId": "", "customerName": "", "carrierId": "", "carrierName": "", "proNo": "", "freightTerm": "", "freightCost": 0, "loadType": "", "appointmentTime": "", "masterBolNo": "", "shipFrom": {"name": "", "country": "", "state": "", "city": "", "zipCode": "", "fax": "", "address1": "", "address2": "", "contact": "", "phone": "", "extension": "", "email": ""}, "shipTo": {"name": "", "country": "", "state": "", "city": "", "zipCode": "", "fax": "", "address1": "", "address2": "", "contact": "", "phone": "", "extension": "", "email": ""}, "note": "", "startTime": "", "endTime": "", "sequence": 0, "trailerNo": "", "trailerPickUpMode": "", "loadPhotos": [""], "countingSheetPhotos": [""], "carrierSignatureFileId": "", "shipperSignatureFileId": "", "carrierSignatureTime": "", "shipperSignatureTime": "", "destination": "", "orderLines": [{"id": 0, "omsId": "", "omsMasterId": "", "status": "", "customerId": "", "carrierId": "", "retailerId": "", "shipMethod": "", "deliveryService": "", "orderType": "", "source": "", "referenceNo": "", "batchNo": "", "poNo": "", "soNo": "", "shippingAccountNo": "", "freightTerm": "", "freightCost": 0, "subOrderType": "", "shipFromAddress": {"name": "", "country": "", "state": "", "city": "", "zipCode": "", "fax": "", "address1": "", "address2": "", "contact": "", "phone": "", "extension": "", "email": ""}, "skipCollectSn": false, "masterTrackingNo": "", "labelCode": "", "isInternational": false, "containHazardous": false, "exceptionReason": "", "incoterm": "", "mbolNo": "", "proNo": "", "bolNo": "", "loadNo": "", "orderedDate": "", "shipNotBefore": "", "shipNoLater": "", "totalWeight": 0, "totalCubicFeet": 0, "containerSize": "", "mabd": "", "appointmentTime": "", "inYardTime": "", "canceledDate": "", "scheduleDate": "", "shippedTime": "", "soldToAddress": {"name": "", "country": "", "state": "", "city": "", "zipCode": "", "fax": "", "address1": "", "address2": "", "contact": "", "phone": "", "extension": "", "email": ""}, "shipToAddress": {"name": "", "country": "", "state": "", "city": "", "zipCode": "", "fax": "", "address1": "", "address2": "", "contact": "", "phone": "", "extension": "", "email": ""}, "storeAddress": {"name": "", "country": "", "state": "", "city": "", "zipCode": "", "fax": "", "address1": "", "address2": "", "contact": "", "phone": "", "extension": "", "email": ""}, "billToAddress": {"name": "", "country": "", "state": "", "city": "", "zipCode": "", "fax": "", "address1": "", "address2": "", "contact": "", "phone": "", "extension": "", "email": ""}, "createdBy": "", "createdTime": "", "updatedBy": "", "updatedTime": "", "historyStatuses": [""], "dynamicFields": {"dynTxtPropertyValue01": "", "dynTxtPropertyValue02": "", "dynTxtPropertyValue03": "", "dynTxtPropertyValue04": "", "dynTxtPropertyValue05": "", "dynTxtPropertyValue06": "", "dynTxtPropertyValue07": "", "dynTxtPropertyValue08": "", "dynTxtPropertyValue09": "", "dynTxtPropertyValue10": "", "dynTxtPropertyValue11": "", "dynTxtPropertyValue12": "", "dynTxtPropertyValue13": "", "dynTxtPropertyValue14": "", "dynTxtPropertyValue15": "", "dynTxtPropertyValue16": "", "dynTxtPropertyValue17": "", "dynTxtPropertyValue18": "", "dynTxtPropertyValue19": "", "dynTxtPropertyValue20": "", "dynTxtPropertyValue21": "", "dynTxtPropertyValue22": "", "dynTxtPropertyValue23": "", "dynTxtPropertyValue24": "", "dynTxtPropertyValue25": "", "dynDatePropertyValue01": "", "dynDatePropertyValue02": "", "dynDatePropertyValue03": "", "dynDatePropertyValue04": "", "dynDatePropertyValue05": ""}, "totalPallets": 0, "uniqueKey": "", "isRush": false, "isSingleQTY": false, "sysNote": "", "blockNote": "", "orderNote": "", "cancelledNote": "", "isUCCLabelPrinted": false, "isPackingListPrinted": false, "isPalletLabelPrinted": false, "destination": "", "itemLines": [{"id": "", "orderId": "", "qty": 0, "itemId": "", "uomId": "", "titleId": "", "lotNo": "", "upcCode": "", "destination": "", "destinationAddress": {"name": "", "country": "", "state": "", "city": "", "zipCode": "", "fax": "", "address1": "", "address2": "", "contact": "", "phone": "", "extension": "", "email": ""}, "destinationGroup": "", "supplierId": "", "shippedQty": 0, "palletQty": 0, "adjustedPalletQty": 0, "orderedQty": 0, "palletLength": 0, "palletWidth": 0, "palletHeight": 0, "palletWeight": 0, "lineNo": "", "lpConfigurationId": "", "note": "", "kitItemId": "", "kitItemlineId": "", "itemCondition": "", "totalInsuranceAmount": 0, "unitPrice": 0, "dynamicFields": {"dynTxtPropertyValue01": "", "dynTxtPropertyValue02": "", "dynTxtPropertyValue03": "", "dynTxtPropertyValue04": "", "dynTxtPropertyValue05": "", "dynTxtPropertyValue06": "", "dynTxtPropertyValue07": "", "dynTxtPropertyValue08": "", "dynTxtPropertyValue09": "", "dynTxtPropertyValue10": "", "dynTxtPropertyValue11": "", "dynTxtPropertyValue12": "", "dynTxtPropertyValue13": "", "dynTxtPropertyValue14": "", "dynTxtPropertyValue15": "", "dynTxtPropertyValue16": "", "dynTxtPropertyValue17": "", "dynTxtPropertyValue18": "", "dynTxtPropertyValue19": "", "dynTxtPropertyValue20": "", "dynTxtPropertyValue21": "", "dynTxtPropertyValue22": "", "dynTxtPropertyValue23": "", "dynTxtPropertyValue24": "", "dynTxtPropertyValue25": "", "dynDatePropertyValue01": "", "dynDatePropertyValue02": "", "dynDatePropertyValue03": "", "dynDatePropertyValue04": "", "dynDatePropertyValue05": ""}, "snList": [""], "createdTime": "", "createdBy": "", "updatedTime": "", "updatedBy": "", "baseQty": 0, "goodsType": ""}], "loadId": "", "orderId": "", "sequence": 0, "customerName": "", "carrierName": "", "retailerName": ""}], "sysNote": "", "dockId": "", "loadIds": [""], "exceptionCode": "", "loadMode": "", "checkedIn": false, "checkInTime": "", "carrierPhotos": [""], "sealNo": "", "sealPhotos": [""], "loadingPhotoIds": [""], "assigneeUserName": "", "retailerName": ""}], "totalCount": 0, "currentPage": 0, "pageSize": 0, "totalPage": 0}}}}}}, "headers": {}}}, "security": []}}}, "components": {"schemas": {"DbChangeLogQuery": {"$id": "#/definitions/2a527e6c-9e08-4ae5-9e78-6fec67bb83c0", "type": "object", "properties": {"source": {"type": "string", "description": "Source of the change log required, default is 'wmsApp'"}, "operator": {"type": "number"}, "dataId": {"type": "string"}, "dataFor": {"type": "string"}, "tableName": {"type": "string"}, "diff": {"type": "string"}, "traceId": {"type": "string"}, "requestPath": {"type": "string"}, "requestMethod": {"type": "string"}, "screenId": {"type": "string"}, "createdBy": {"type": "string"}, "createdTimeFrom": {"type": "string"}, "createdTimeTo": {"type": "string"}, "currentPage": {"type": "number"}, "pageSize": {"type": "number"}}, "required": ["currentPage", "pageSize"]}, "BAMPickItemLineDto": {"type": "object", "properties": {"createdTime": {"type": "string", "description": ""}, "createdBy": {"type": "string", "description": ""}, "updatedTime": {"type": "string", "description": ""}, "updatedBy": {"type": "string", "description": ""}, "id": {"type": "string", "description": ""}, "pickTaskId": {"type": "string", "description": ""}, "itemId": {"type": "string", "description": ""}, "uomId": {"type": "string", "description": ""}, "titleId": {"type": "string", "description": ""}, "lotNo": {"type": "string", "description": ""}, "qty": {"type": "number", "description": ""}, "baseQty": {"type": "number", "description": ""}, "goodsType": {"type": "string", "description": ""}, "orderId": {"type": "string", "description": ""}, "itemName": {"type": "string", "description": ""}, "uomName": {"type": "string", "description": ""}, "titleName": {"type": "string", "description": ""}}}, "AddressDto": {"type": "object", "properties": {"id": {"type": "integer", "description": ""}, "tenantId": {"type": "string", "description": ""}, "orgId": {"type": "string", "description": ""}, "shorthand": {"type": "string", "description": ""}, "tags": {"type": "array", "items": {"type": "string"}, "description": ""}, "name": {"type": "string", "description": ""}, "country": {"type": "string", "description": ""}, "address1": {"type": "string", "description": ""}, "address2": {"type": "string", "description": ""}, "state": {"type": "string", "description": ""}, "city": {"type": "string", "description": ""}, "zipCode": {"type": "string", "description": ""}, "fax": {"type": "string", "description": ""}, "storeNo": {"type": "string", "description": ""}, "contact": {"type": "string", "description": ""}, "phone": {"type": "string", "description": ""}, "email": {"type": "string", "description": ""}, "extension": {"type": "string", "description": ""}, "crmAddressId": {"type": "string", "description": ""}, "latitude": {"type": "string", "description": ""}, "longitude": {"type": "string", "description": ""}, "referenceNo": {"type": "string", "description": ""}, "batchCode": {"type": "string", "description": ""}, "toHome": {"type": "boolean", "description": ""}, "channel": {"type": "string", "description": "", "enum": ["EDI", "FTP", "INT_FTP", "WEB", "MANUAL", "UPLOAD", "ANDROID", "EXTERNAL_WMS", "CLIENT_PORTAL", "ITEM_MASTER", "DTS", "CONVEYOR_LINE", "PUBLIC_API", "CARRIER_WEB", "EXTERNAL_FMS", "AIR_ROB"]}, "createdTime": {"type": "string", "description": ""}, "createdBy": {"type": "string", "description": ""}, "updatedTime": {"type": "string", "description": ""}, "updatedBy": {"type": "string", "description": ""}}}, "BAMOrderItemLineDto": {"type": "object", "properties": {"id": {"type": ["string", "null"], "description": ""}, "orderId": {"type": ["string", "null"], "description": ""}, "qty": {"type": ["number", "null"], "description": ""}, "itemId": {"type": ["string", "null"], "description": ""}, "uomId": {"type": ["string", "null"], "description": ""}, "titleId": {"type": ["string", "null"], "description": ""}, "lotNo": {"type": ["string", "null"], "description": ""}, "upcCode": {"type": ["string", "null"], "description": ""}, "destination": {"type": ["string", "null"], "description": ""}, "destinationAddress": {"$ref": "#/components/schemas/AddressDto", "description": ""}, "destinationGroup": {"type": ["string", "null"], "description": ""}, "supplierId": {"type": ["string", "null"], "description": ""}, "shippedQty": {"type": ["number", "null"], "description": ""}, "palletQty": {"type": ["number", "null"], "description": ""}, "adjustedPalletQty": {"type": ["number", "null"], "description": ""}, "orderedQty": {"type": ["number", "null"], "description": ""}, "palletLength": {"type": ["number", "null"], "description": ""}, "palletWidth": {"type": ["number", "null"], "description": ""}, "palletHeight": {"type": ["number", "null"], "description": ""}, "palletWeight": {"type": ["number", "null"], "description": ""}, "lineNo": {"type": ["string", "null"], "description": ""}, "lpConfigurationId": {"type": ["string", "null"], "description": ""}, "note": {"type": ["string", "null"], "description": ""}, "kitItemId": {"type": ["string", "null"], "description": ""}, "kitItemlineId": {"type": ["string", "null"], "description": ""}, "itemCondition": {"type": ["string", "null"], "description": ""}, "totalInsuranceAmount": {"type": ["number", "null"], "description": ""}, "unitPrice": {"type": ["number", "null"], "description": ""}, "unitPriceCurrency": {"type": ["string", "null"], "description": "", "enum": ["CNY", "USD", "EUR"]}, "dynamicFields": {"$ref": "#/components/schemas/DynamicFields", "description": "Assuming JSON as String"}, "snList": {"type": ["array", "null"], "items": {"type": "string"}, "description": ""}, "createdTime": {"type": ["string", "null"], "description": ""}, "createdBy": {"type": ["string", "null"], "description": ""}, "updatedTime": {"type": ["string", "null"], "description": ""}, "updatedBy": {"type": ["string", "null"], "description": ""}, "baseQty": {"type": ["number", "null"], "description": ""}, "goodsType": {"type": ["string", "null"], "description": ""}, "itemName": {"type": ["string", "null"], "description": ""}, "uomName": {"type": ["string", "null"], "description": ""}, "titleName": {"type": ["string", "null"], "description": ""}, "supplierName": {"type": ["string", "null"], "description": ""}}}, "DynamicFields": {"type": "object", "properties": {"dynTxtPropertyValue01": {"type": "string", "description": ""}, "dynTxtPropertyValue02": {"type": "string", "description": ""}, "dynTxtPropertyValue03": {"type": "string", "description": ""}, "dynTxtPropertyValue04": {"type": "string", "description": ""}, "dynTxtPropertyValue05": {"type": "string", "description": ""}, "dynTxtPropertyValue06": {"type": "string", "description": ""}, "dynTxtPropertyValue07": {"type": "string", "description": ""}, "dynTxtPropertyValue08": {"type": "string", "description": ""}, "dynTxtPropertyValue09": {"type": "string", "description": ""}, "dynTxtPropertyValue10": {"type": "string", "description": ""}, "dynTxtPropertyValue11": {"type": "string", "description": ""}, "dynTxtPropertyValue12": {"type": "string", "description": ""}, "dynTxtPropertyValue13": {"type": "string", "description": ""}, "dynTxtPropertyValue14": {"type": "string", "description": ""}, "dynTxtPropertyValue15": {"type": "string", "description": ""}, "dynTxtPropertyValue16": {"type": "string", "description": ""}, "dynTxtPropertyValue17": {"type": "string", "description": ""}, "dynTxtPropertyValue18": {"type": "string", "description": ""}, "dynTxtPropertyValue19": {"type": "string", "description": ""}, "dynTxtPropertyValue20": {"type": "string", "description": ""}, "dynTxtPropertyValue21": {"type": "string", "description": ""}, "dynTxtPropertyValue22": {"type": "string", "description": ""}, "dynTxtPropertyValue23": {"type": "string", "description": ""}, "dynTxtPropertyValue24": {"type": "string", "description": ""}, "dynTxtPropertyValue25": {"type": "string", "description": ""}, "dynDatePropertyValue01": {"type": "string", "description": ""}, "dynDatePropertyValue02": {"type": "string", "description": ""}, "dynDatePropertyValue03": {"type": "string", "description": ""}, "dynDatePropertyValue04": {"type": "string", "description": ""}, "dynDatePropertyValue05": {"type": "string", "description": ""}}}, "ReceiptSearch": {"type": "object", "properties": {"currentPage": {"type": ["integer", "null"], "description": ""}, "pageSize": {"type": ["integer", "null"], "description": ""}, "sortingFields": {"type": ["array", "null"], "items": {"$ref": "#/components/schemas/SortingField", "description": "com.item.xms.persistence.query.SortingField"}, "description": ""}, "receiptIds": {"type": ["array", "null"], "items": {"type": "string"}, "description": ""}, "receiptId": {"type": ["string", "null"], "description": ""}, "regexReceiptId": {"type": ["string", "null"], "description": ""}, "omsId": {"type": ["string", "null"], "description": ""}, "omsIds": {"type": ["array", "null"], "items": {"type": "string"}, "description": ""}, "excludeReceiptIds": {"type": ["array", "null"], "items": {"type": "string"}, "description": "@QueryCondition(field = \"id\", operator = QueryOperator.NOT_IN)"}, "referenceNo": {"type": ["string", "null"], "description": ""}, "poNo": {"type": ["string", "null"], "description": ""}, "eqPoNo": {"type": ["string", "null"], "description": ""}, "eqPoNos": {"type": ["array", "null"], "items": {"type": "string"}, "description": ""}, "bolNo": {"type": ["string", "null"], "description": ""}, "exactlyBolNo": {"type": ["string", "null"], "description": ""}, "exactlyBolNos": {"type": ["array", "null"], "items": {"type": "string"}, "description": ""}, "status": {"type": ["string", "null"], "description": "", "enum": ["IMPORTED", "OPEN", "APPOINTMENT_MADE", "IN_PROGRESS", "TASK_COMPLETED", "EXCEPTION", "PARTIAL_RECEIVED", "CLOSED", "FORCE_CLOSED", "CANCELLED", "REOPENED"]}, "statuses": {"type": ["array", "null"], "items": {"type": "string", "enum": ["IMPORTED", "OPEN", "APPOINTMENT_MADE", "IN_PROGRESS", "TASK_COMPLETED", "EXCEPTION", "PARTIAL_RECEIVED", "CLOSED", "FORCE_CLOSED", "CANCELLED", "REOPENED"]}, "description": ""}, "excludeStatuses": {"type": ["array", "null"], "items": {"type": "string"}, "description": ""}, "excludeReceiptTypes": {"type": ["array", "null"], "items": {"type": "string", "enum": ["REGULAR_RECEIPT", "TITLE_TRANSFER_RECEIPT", "MIGO_TRANSFER_RECEIPT", "INTERNAL_TRANSFER_RECEIVING", "TRANSLOAD"]}, "description": ""}, "receiptType": {"type": ["string", "null"], "description": "", "enum": ["REGULAR_RECEIPT", "TITLE_TRANSFER_RECEIPT", "MIGO_TRANSFER_RECEIPT", "INTERNAL_TRANSFER_RECEIVING", "TRANSLOAD"]}, "receiveType": {"type": ["string", "null"], "description": "", "enum": ["BULK_RECEIVING", "REGULAR_RECEIVING", "PARCEL_RECEIVING", "SMALL_PARCEL_RECEIVING", "MIXED_RECEIVING"]}, "receiptSubType": {"type": ["string", "null"], "description": ""}, "receiptSubTypes": {"type": ["array", "null"], "items": {"type": "string"}, "description": ""}, "customerIds": {"type": ["array", "null"], "items": {"type": "string"}, "description": ""}, "customerId": {"type": ["string", "null"], "description": ""}, "titleIds": {"type": ["array", "null"], "items": {"type": "string"}, "description": ""}, "carrierIds": {"type": ["array", "null"], "items": {"type": "string"}, "description": ""}, "containerNo": {"type": ["string", "null"], "description": "@QueryCondition(field = \"containerNo\", operator = QueryOperator.LIKE)"}, "eqContainerNo": {"type": ["string", "null"], "description": ""}, "eqContainerNos": {"type": ["array", "null"], "items": {"type": "string"}, "description": ""}, "trailerNos": {"type": ["array", "null"], "items": {"type": "string"}, "description": ""}, "trailerNo": {"type": ["string", "null"], "description": ""}, "referenceNos": {"type": ["array", "null"], "items": {"type": "string"}, "description": ""}, "eqReferenceNo": {"type": ["string", "null"], "description": ""}, "eqReferenceNos": {"type": ["array", "null"], "items": {"type": "string"}, "description": ""}, "poNos": {"type": ["array", "null"], "items": {"type": "string"}, "description": ""}, "bolNos": {"type": ["array", "null"], "items": {"type": "string"}, "description": ""}, "sealNo": {"type": ["string", "null"], "description": ""}, "sealNos": {"type": ["array", "null"], "items": {"type": "string"}, "description": ""}, "appointmentTimeFrom": {"type": ["string", "null"], "description": ""}, "appointmentTimeTo": {"type": ["string", "null"], "description": ""}, "receivedTimeFrom": {"type": ["string", "null"], "description": ""}, "receivedTimeTo": {"type": ["string", "null"], "description": ""}, "isRush": {"type": ["boolean", "null"], "description": ""}, "trackingNo": {"type": ["string", "null"], "description": ""}, "eqTrackingNo": {"type": ["string", "null"], "description": ""}, "createdBy": {"type": ["string", "null"], "description": ""}, "createdTimeFrom": {"type": ["string", "null"], "description": ""}, "createdTimeTo": {"type": ["string", "null"], "description": ""}, "updatedTimeFrom": {"type": ["string", "null"], "description": ""}, "updatedTimeTo": {"type": ["string", "null"], "description": ""}, "sources": {"type": ["array", "null"], "items": {"type": "string", "enum": ["EDI", "FTP", "INT_FTP", "WEB", "MANUAL", "UPLOAD", "ANDROID", "EXTERNAL_WMS", "CLIENT_PORTAL", "ITEM_MASTER", "DTS", "CONVEYOR_LINE", "PUBLIC_API", "CARRIER_WEB", "EXTERNAL_FMS"]}, "description": ""}, "excludeSources": {"type": ["array", "null"], "items": {"type": "string"}, "description": ""}, "inYardTimeFrom": {"type": ["string", "null"], "description": ""}, "inYardTimeTo": {"type": ["string", "null"], "description": ""}, "trackingNos": {"type": ["array", "null"], "items": {"type": "string"}, "description": ""}, "note": {"type": ["string", "null"], "description": ""}, "withItemLine": {"type": ["boolean", "null"], "description": ""}}}, "SortingField": {"type": "object", "properties": {"field": {"type": "string", "description": ""}, "orderBy": {"type": "string", "description": "", "enum": ["NONE", "ASC", "DESC"]}}}, "BAMReceiptItemLineDto": {"type": "object", "properties": {"id": {"type": ["string", "null"], "description": ""}, "receiptId": {"type": ["string", "null"], "description": ""}, "itemId": {"type": ["string", "null"], "description": ""}, "qty": {"type": ["number", "null"], "description": ""}, "uomId": {"type": ["string", "null"], "description": ""}, "titleId": {"type": ["string", "null"], "description": ""}, "lotNo": {"type": ["string", "null"], "description": ""}, "destination": {"type": ["string", "null"], "description": ""}, "destinationAddress": {"$ref": "#/components/schemas/AddressDto", "description": ""}, "destinationGroup": {"type": ["string", "null"], "description": ""}, "supplierId": {"type": ["string", "null"], "description": ""}, "expectedPalletQty": {"type": ["number", "null"], "description": ""}, "goodsType": {"type": ["string", "null"], "description": ""}, "palletQty": {"type": ["number", "null"], "description": ""}, "receivedQty": {"type": ["number", "null"], "description": ""}, "receivedUomId": {"type": ["string", "null"], "description": ""}, "expirationDate": {"type": ["string", "null"], "description": ""}, "manufactureDate": {"type": ["string", "null"], "description": ""}, "note": {"type": ["string", "null"], "description": ""}, "dynamicFields": {"$ref": "#/components/schemas/DynamicFields", "description": "Assuming JSON as String"}, "lineNo": {"type": ["string", "null"], "description": ""}, "snList": {"type": ["array", "null"], "items": {"type": "string"}, "description": ""}, "cartonNos": {"type": ["array", "null"], "items": {"type": "string"}, "description": ""}, "createdBy": {"type": ["string", "null"], "description": ""}, "createdTime": {"type": ["string", "null"], "description": ""}, "updatedBy": {"type": ["string", "null"], "description": ""}, "updatedTime": {"type": ["string", "null"], "description": ""}, "itemName": {"type": ["string", "null"], "description": ""}, "supplierName": {"type": ["string", "null"], "description": ""}, "titleName": {"type": ["string", "null"], "description": ""}, "uomName": {"type": ["string", "null"], "description": ""}, "receivedUomName": {"type": ["string", "null"], "description": ""}}}, "R«PageResult«BAMReceiptDto»»": {"type": "object", "properties": {"code": {"type": ["integer", "null"], "description": ""}, "msg": {"type": ["string", "null"], "description": ""}, "success": {"type": ["boolean", "null"], "description": ""}, "data": {"$ref": "#/components/schemas/PageResult%C2%ABBAMReceiptDto%C2%BB", "description": ""}}}, "PageResult«BAMReceiptDto»": {"type": "object", "properties": {"list": {"type": ["array", "null"], "items": {"$ref": "#/components/schemas/BAMReceiptDto", "description": "com.item.wms.interfaces.rest.bam.dtos.receipt.BAMReceiptDto"}, "description": ""}, "totalCount": {"type": ["integer", "null"], "description": ""}, "currentPage": {"type": ["integer", "null"], "description": ""}, "pageSize": {"type": ["integer", "null"], "description": ""}, "totalPage": {"type": ["integer", "null"], "description": ""}}}, "BAMReceiptDto": {"type": "object", "properties": {"id": {"type": ["string", "null"], "description": ""}, "status": {"type": ["string", "null"], "description": "", "enum": ["IMPORTED", "OPEN", "APPOINTMENT_MADE", "IN_PROGRESS", "TASK_COMPLETED", "EXCEPTION", "CLOSED", "FORCE_CLOSED", "CANCELLED", "REOPENED", "PARTIAL_RECEIVED"]}, "omsId": {"type": ["string", "null"], "description": ""}, "receiptType": {"type": ["string", "null"], "description": "", "enum": ["REGULAR_RECEIPT", "TITLE_TRANSFER_RECEIPT", "MIGO_TRANSFER_RECEIPT", "INTERNAL_TRANSFER_RECEIVING", "TRANSLOAD"]}, "receiptSubType": {"type": ["string", "null"], "description": ""}, "carrierId": {"type": ["string", "null"], "description": ""}, "customerId": {"type": ["string", "null"], "description": ""}, "titleId": {"type": ["string", "null"], "description": ""}, "poNo": {"type": ["string", "null"], "description": ""}, "referenceNo": {"type": ["string", "null"], "description": ""}, "sealNo": {"type": ["string", "null"], "description": ""}, "shippingMethod": {"type": ["string", "null"], "description": "", "enum": ["TL", "LTL", "WILL_CALL", "SP"]}, "source": {"type": ["string", "null"], "description": "", "enum": ["EDI", "FTP", "INT_FTP", "WEB", "MANUAL", "UPLOAD", "ANDROID", "EXTERNAL_WMS", "CLIENT_PORTAL", "ITEM_MASTER", "DTS", "CONVEYOR_LINE", "PUBLIC_API", "CARRIER_WEB"]}, "dynamicFields": {"$ref": "#/components/schemas/DynamicFields", "description": "Assuming JSON as String"}, "appointmentTime": {"type": ["string", "null"], "description": ""}, "closedTime": {"type": ["string", "null"], "description": ""}, "scheduleDate": {"type": ["string", "null"], "description": ""}, "inYardTime": {"type": ["string", "null"], "description": ""}, "receivedTime": {"type": ["string", "null"], "description": ""}, "isRush": {"type": ["boolean", "null"], "description": ""}, "note": {"type": ["string", "null"], "description": ""}, "sysNote": {"type": ["string", "null"], "description": ""}, "bolNo": {"type": ["string", "null"], "description": ""}, "mbolNo": {"type": ["string", "null"], "description": ""}, "containerNo": {"type": ["string", "null"], "description": ""}, "containerSize": {"type": ["string", "null"], "description": ""}, "exceptionReason": {"type": ["string", "null"], "description": ""}, "shippingInstruction": {"type": ["string", "null"], "description": ""}, "trailerNo": {"type": ["string", "null"], "description": ""}, "trailerSize": {"type": ["string", "null"], "description": ""}, "trackingNo": {"type": ["string", "null"], "description": ""}, "itemLines": {"type": ["array", "null"], "items": {"$ref": "#/components/schemas/BAMReceiptItemLineDto", "description": "com.item.wms.interfaces.bam.dtos.receipt.BANReceiptItemLineDto"}, "description": ""}, "createdBy": {"type": ["string", "null"], "description": ""}, "createdTime": {"type": ["string", "null"], "description": ""}, "updatedBy": {"type": ["string", "null"], "description": ""}, "updatedTime": {"type": ["string", "null"], "description": ""}, "carrierName": {"type": ["string", "null"], "description": ""}, "customerName": {"type": ["string", "null"], "description": ""}, "titleName": {"type": ["string", "null"], "description": ""}}}, "ReceiptItemLineQuery": {"type": "object", "properties": {"currentPage": {"type": "integer", "description": ""}, "pageSize": {"type": "integer", "description": ""}, "sortingFields": {"type": "array", "items": {"$ref": "#/components/schemas/SortingField", "description": "com.item.xms.persistence.query.SortingField"}, "description": ""}, "receiptIds": {"type": "array", "items": {"type": "string"}, "description": ""}, "receiptId": {"type": "string", "description": ""}, "id": {"type": "string", "description": ""}, "ids": {"type": "array", "items": {"type": "string"}, "description": ""}, "itemId": {"type": "string", "description": ""}, "itemIds": {"type": "array", "items": {"type": "string"}, "description": ""}, "dynTxtPropertyValue01": {"type": "string", "description": ""}, "dynTxtPropertyValue02": {"type": "string", "description": ""}, "dynTxtPropertyValue03": {"type": "string", "description": ""}, "palletNos": {"type": "array", "items": {"type": "string"}, "description": ""}, "dynTxtPropertyValue02s": {"type": "array", "items": {"type": "string"}, "description": ""}, "orDynTxtPropertyValue01": {"type": "string", "description": ""}, "orDynTxtPropertyValue02": {"type": "string", "description": ""}, "orDynTxtPropertyValue03": {"type": "string", "description": ""}, "orPalletNos": {"type": "array", "items": {"type": "string"}, "description": ""}, "dynTxtPropertyValue04": {"type": "string", "description": ""}, "dynTxtPropertyValue05": {"type": "string", "description": ""}, "dynTxtPropertyValue06": {"type": "string", "description": ""}, "dynTxtPropertyValue07": {"type": "string", "description": ""}, "dynTxtPropertyValue08": {"type": "string", "description": ""}, "dynTxtPropertyValue09": {"type": "string", "description": ""}, "dynTxtPropertyValue10": {"type": "string", "description": ""}, "dynTxtPropertyValue11": {"type": "string", "description": ""}, "dynTxtPropertyValue12": {"type": "string", "description": ""}, "dynTxtPropertyValue13": {"type": "string", "description": ""}, "dynTxtPropertyValue14": {"type": "string", "description": ""}, "dynTxtPropertyValue15": {"type": "string", "description": ""}, "dynTxtPropertyValue16": {"type": "string", "description": ""}, "dynTxtPropertyValue17": {"type": "string", "description": ""}, "dynTxtPropertyValue18": {"type": "string", "description": ""}, "dynTxtPropertyValue19": {"type": "string", "description": ""}, "dynTxtPropertyValue20": {"type": "string", "description": ""}, "dynTxtPropertyValue21": {"type": "string", "description": ""}, "dynTxtPropertyValue22": {"type": "string", "description": ""}, "dynTxtPropertyValue23": {"type": "string", "description": ""}, "dynTxtPropertyValue24": {"type": "string", "description": ""}, "dynTxtPropertyValue25": {"type": "string", "description": ""}, "dynDatePropertyValue01": {"type": "string", "description": ""}, "dynDatePropertyValue02": {"type": "string", "description": ""}, "dynDatePropertyValue03": {"type": "string", "description": ""}, "dynDatePropertyValue04": {"type": "string", "description": ""}, "dynDatePropertyValue05": {"type": "string", "description": ""}}}, "TaskStepDto": {"type": "object", "properties": {"id": {"type": "integer", "description": ""}, "customerId": {"type": "string", "description": ""}, "status": {"type": "string", "description": "", "enum": ["NEW", "IN_PROGRESS", "EXCEPTION", "CLOSED", "FORCE_CLOSED", "CANCELLED"]}, "exceptionCode": {"type": "string", "description": ""}, "sysNote": {"type": "string", "description": ""}, "note": {"type": "string", "description": ""}, "stepType": {"type": "string", "description": "", "enum": ["OFFLOAD", "LP_SETUP", "SN_SCAN", "PICK", "STAGE", "SORTING_TO_WALL", "STAGE_TO_WALL", "ORDER_PICK_FROM_WALL", "REPLENISH", "GENERAL", "COLLECT", "DROP", "PUT_AWAY", "LOADING", "TRANSLOAD_RECEIVE", "TRANSLOAD_LOAD", "PUT_BACK", "PACK", "CLP_BONDING", "INTERNAL_TRANSFER_OUT", "INTERNAL_TRANSFER_RECEIVING", "MATERIAL_RECEIVING"]}, "taskId": {"type": "string", "description": ""}, "taskType": {"type": "string", "description": "", "enum": ["GENERAL", "RECEIVE", "PUT_AWAY", "PICK", "PICK_REGULAR", "PICK_DROPSHIP", "LOAD", "REPLENISH", "MOVEMENT", "PUT_BACK", "CYCLE_COUNT", "TRANSLOAD_RECEIVE", "TRANSLOAD_LOAD", "CLP_BONDING", "PACK", "INTERNAL_TRANSFER_OUT", "INTERNAL_TRANSFER_RECEIVE"]}, "stepSequence": {"type": "integer", "description": ""}, "assigneeUserIds": {"type": "array", "items": {"type": "string"}, "description": ""}, "lastAssignedWhen": {"type": "string", "description": ""}, "priority": {"type": "string", "description": "", "enum": ["LOW", "MIDDLE", "HIGH", "TOP"]}, "startTime": {"type": "string", "description": ""}, "endTime": {"type": "string", "description": ""}}}, "BAMOrderDto": {"type": "object", "properties": {"id": {"type": ["string", "null"], "description": ""}, "omsId": {"type": ["string", "null"], "description": ""}, "omsMasterId": {"type": ["string", "null"], "description": ""}, "status": {"type": ["string", "null"], "description": "", "enum": ["IMPORTED", "OPEN", "COMMITTED", "PARTIAL_COMMITTED", "COMMIT_BLOCKED", "COMMIT_FAILED", "PLANNING", "PLANNED", "PICKING", "PICKED", "PACKING", "PACKED", "STAGED", "LOADING", "LOADED", "PARTIAL_SHIPPED", "SHORT_SHIPPED", "SHIPPED", "REOPEN", "CANCELLED", "EXCEPTION", "ON_HOLD"]}, "customerId": {"type": ["string", "null"], "description": ""}, "carrierId": {"type": ["string", "null"], "description": ""}, "retailerId": {"type": ["string", "null"], "description": ""}, "shipMethod": {"type": ["string", "null"], "description": "", "enum": ["TL", "LTL", "WILL_CALL", "SP"]}, "deliveryService": {"type": ["string", "null"], "description": ""}, "orderType": {"type": ["string", "null"], "description": "", "enum": ["RG", "TT", "MT", "DS", "ITO", "TRANSLOAD"]}, "source": {"type": ["string", "null"], "description": "", "enum": ["EDI", "FTP", "INT_FTP", "WEB", "MANUAL", "UPLOAD", "ANDROID", "EXTERNAL_WMS", "CLIENT_PORTAL", "ITEM_MASTER", "DTS", "CONVEYOR_LINE", "PUBLIC_API", "CARRIER_WEB"]}, "referenceNo": {"type": ["string", "null"], "description": ""}, "batchNo": {"type": ["string", "null"], "description": ""}, "poNo": {"type": ["string", "null"], "description": ""}, "soNo": {"type": ["string", "null"], "description": ""}, "shippingAccountNo": {"type": ["string", "null"], "description": ""}, "freightTerm": {"type": ["string", "null"], "description": "", "enum": ["COLLECT", "PREPAID", "THIRD_PARTY"]}, "freightCost": {"type": ["number", "null"], "description": ""}, "subOrderType": {"type": ["string", "null"], "description": ""}, "shipFromAddress": {"$ref": "#/components/schemas/AddressDto", "description": ""}, "skipCollectSn": {"type": ["boolean", "null"], "description": ""}, "masterTrackingNo": {"type": ["string", "null"], "description": ""}, "labelCode": {"type": ["string", "null"], "description": ""}, "isInternational": {"type": ["boolean", "null"], "description": ""}, "containHazardous": {"type": ["boolean", "null"], "description": ""}, "exceptionReason": {"type": ["string", "null"], "description": ""}, "incoterm": {"type": ["string", "null"], "description": "", "enum": ["DAP", "DDP", "EXW", "DDU"]}, "mbolNo": {"type": ["string", "null"], "description": ""}, "proNo": {"type": ["string", "null"], "description": ""}, "bolNo": {"type": ["string", "null"], "description": ""}, "loadNo": {"type": ["string", "null"], "description": ""}, "orderedDate": {"type": ["string", "null"], "description": ""}, "shipNotBefore": {"type": ["string", "null"], "description": ""}, "shipNoLater": {"type": ["string", "null"], "description": ""}, "totalWeight": {"type": ["number", "null"], "description": ""}, "totalCubicFeet": {"type": ["number", "null"], "description": ""}, "containerSize": {"type": ["string", "null"], "description": ""}, "mabd": {"type": ["string", "null"], "description": ""}, "appointmentTime": {"type": ["string", "null"], "description": ""}, "inYardTime": {"type": ["string", "null"], "description": ""}, "canceledDate": {"type": ["string", "null"], "description": ""}, "scheduleDate": {"type": ["string", "null"], "description": ""}, "shippedTime": {"type": ["string", "null"], "description": ""}, "soldToAddress": {"$ref": "#/components/schemas/AddressDto", "description": ""}, "shipToAddress": {"$ref": "#/components/schemas/AddressDto", "description": ""}, "storeAddress": {"$ref": "#/components/schemas/AddressDto", "description": ""}, "billToAddress": {"$ref": "#/components/schemas/AddressDto", "description": ""}, "createdBy": {"type": ["string", "null"], "description": ""}, "createdTime": {"type": ["string", "null"], "description": ""}, "updatedBy": {"type": ["string", "null"], "description": ""}, "updatedTime": {"type": ["string", "null"], "description": ""}, "historyStatuses": {"type": ["array", "null"], "items": {"type": "string", "enum": ["IMPORTED", "OPEN", "COMMITTED", "PARTIAL_COMMITTED", "COMMIT_BLOCKED", "COMMIT_FAILED", "PLANNING", "PLANNED", "PICKING", "PICKED", "PACKING", "PACKED", "STAGED", "LOADING", "LOADED", "PARTIAL_SHIPPED", "SHORT_SHIPPED", "SHIPPED", "REOPEN", "CANCELLED", "EXCEPTION", "ON_HOLD"]}, "description": ""}, "dynamicFields": {"$ref": "#/components/schemas/DynamicFields", "description": "Assuming JSON as String"}, "totalPallets": {"type": ["number", "null"], "description": ""}, "uniqueKey": {"type": ["string", "null"], "description": ""}, "isRush": {"type": ["boolean", "null"], "description": ""}, "isSingleQTY": {"type": ["boolean", "null"], "description": ""}, "sysNote": {"type": ["string", "null"], "description": ""}, "blockNote": {"type": ["string", "null"], "description": ""}, "orderNote": {"type": ["string", "null"], "description": ""}, "cancelledNote": {"type": ["string", "null"], "description": ""}, "isUCCLabelPrinted": {"type": ["boolean", "null"], "description": ""}, "isPackingListPrinted": {"type": ["boolean", "null"], "description": ""}, "isPalletLabelPrinted": {"type": ["boolean", "null"], "description": ""}, "destination": {"type": ["string", "null"], "description": ""}, "itemLines": {"type": ["array", "null"], "items": {"$ref": "#/components/schemas/BAMOrderItemLineDto", "description": "com.item.wms.application.outbound.order.dtos.OrderItemLineDto"}, "description": ""}, "customerName": {"type": ["string", "null"], "description": ""}, "carrierName": {"type": ["string", "null"], "description": ""}, "retailerName": {"type": ["string", "null"], "description": ""}, "orderPlanId": {"type": ["string", "null"], "description": ""}, "pickTaskIds": {"type": ["string", "null"], "description": ""}, "loadTaskIds": {"type": ["string", "null"], "description": ""}}}, "RListReceiptItemLineDto": {"type": "object", "properties": {"code": {"type": "integer", "description": ""}, "msg": {"type": "string", "description": ""}, "success": {"type": "boolean", "description": ""}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/ReceiptItemLineDto", "description": ""}, "description": ""}}}, "ReceiptItemLineDto": {"type": "object", "properties": {"id": {"type": "string", "description": ""}, "receiptId": {"type": "string", "description": ""}, "itemId": {"type": "string", "description": ""}, "productId": {"type": "string", "description": ""}, "qty": {"type": "number", "description": ""}, "uomId": {"type": "string", "description": ""}, "unitId": {"type": "string", "description": ""}, "titleId": {"type": "string", "description": ""}, "lotNo": {"type": "string", "description": ""}, "destination": {"type": "string", "description": ""}, "destinationAddress": {"$ref": "#/components/schemas/AddressExt", "description": ""}, "destinationGroup": {"type": "string", "description": ""}, "supplierId": {"type": "string", "description": ""}, "expectedPalletQty": {"type": "number", "description": ""}, "goodsType": {"type": "string", "description": ""}, "palletQty": {"type": "number", "description": ""}, "receivedQty": {"type": "number", "description": ""}, "receivedUomId": {"type": "string", "description": ""}, "receivedUnitId": {"type": "string", "description": ""}, "expirationDate": {"type": "string", "description": ""}, "manufactureDate": {"type": "string", "description": ""}, "note": {"type": "string", "description": ""}, "dynamicFields": {"$ref": "#/components/schemas/DynamicFields", "description": "Assuming JSON as String"}, "lineNo": {"type": "string", "description": ""}, "snList": {"type": "array", "items": {"type": "string"}, "description": ""}, "cartonNos": {"type": "array", "items": {"type": "string"}, "description": ""}, "createdBy": {"type": "string", "description": ""}, "createdTime": {"type": "string", "description": ""}, "updatedBy": {"type": "string", "description": ""}, "updatedTime": {"type": "string", "description": ""}, "palletNos": {"type": "array", "items": {"type": "string"}, "description": ""}, "source": {"type": "string", "description": "", "enum": ["EDI", "FTP", "INT_FTP", "WEB", "MANUAL", "UPLOAD", "ANDROID", "EXTERNAL_WMS", "CLIENT_PORTAL", "ITEM_MASTER", "DTS", "CONVEYOR_LINE", "PUBLIC_API", "CARRIER_WEB", "EXTERNAL_FMS", "AIR_ROB"]}, "snDetails": {"type": "array", "items": {"$ref": "#/components/schemas/ReceiptSnDetailDto", "description": "com.item.wms.application.inbound.receiptsn.dto.ReceiptSnDetailDto"}, "description": ""}, "poLineNo": {"type": "string", "description": ""}, "companyId": {"type": "string", "description": ""}, "packagingTypeSpecId": {"type": "string", "description": ""}, "combineReceiptItemLines": {"type": "array", "items": {"$ref": "#/components/schemas/CombineReceiptItemLine", "description": "com.item.wms.domain.common.CombineReceiptItemLine"}, "description": ""}, "priorityPoints": {"type": "integer", "description": ""}, "soIds": {"type": "array", "items": {"type": "string"}, "description": ""}, "vendorItemId": {"type": "string", "description": ""}, "purchasePrice": {"type": "number", "description": ""}, "purchasePriceCurrency": {"type": "string", "description": ""}, "groupId": {"type": "string", "description": ""}, "purchaseDate": {"type": "string", "description": ""}, "cost": {"type": "number", "description": ""}, "maker": {"type": "string", "description": ""}, "model": {"type": "string", "description": ""}, "modelYear": {"type": "string", "description": ""}, "origin": {"type": "string", "description": ""}}}, "CombineReceiptItemLine": {"type": "object", "properties": {"poLineNo": {"type": "string", "description": ""}, "poNo": {"type": "string", "description": ""}, "poItem": {"type": "string", "description": ""}, "qty": {"type": "number", "description": ""}, "dynamicFields": {"$ref": "#/components/schemas/DynamicFields", "description": "Assuming JSON as String"}}}, "ReceiptSnDetailDto": {"type": "object", "properties": {"id": {"type": "integer", "description": ""}, "receiptId": {"type": "string", "description": ""}, "lineNo": {"type": "string", "description": ""}, "itemId": {"type": "string", "description": ""}, "sn": {"type": "string", "description": ""}, "weight": {"type": "number", "description": ""}, "weightUom": {"type": "string", "description": ""}, "length": {"type": "number", "description": ""}, "width": {"type": "number", "description": ""}, "height": {"type": "number", "description": ""}, "linearUom": {"type": "string", "description": ""}, "createdBy": {"type": "string", "description": ""}, "createdTime": {"type": "string", "description": ""}, "updatedBy": {"type": "string", "description": ""}, "updatedTime": {"type": "string", "description": ""}}}, "AddressExt": {"type": "object", "properties": {"id": {"type": "string", "description": ""}, "name": {"type": "string", "description": ""}, "country": {"type": "string", "description": ""}, "state": {"type": "string", "description": ""}, "city": {"type": "string", "description": ""}, "zipCode": {"type": "string", "description": ""}, "fax": {"type": "string", "description": ""}, "address1": {"type": "string", "description": ""}, "address2": {"type": "string", "description": ""}, "contact": {"type": "string", "description": ""}, "phone": {"type": "string", "description": ""}, "extension": {"type": "string", "description": ""}, "email": {"type": "string", "description": ""}, "orgId": {"type": "string", "description": ""}, "orgName": {"type": "string", "description": ""}, "referenceNo": {"type": "string", "description": ""}, "batchCode": {"type": "string", "description": ""}, "storeNo": {"type": "string", "description": ""}, "shorthand": {"type": "string", "description": ""}, "toHome": {"type": "boolean", "description": ""}, "tags": {"type": "array", "items": {"type": "string"}, "description": ""}, "channel": {"type": "string", "description": "", "enum": ["EDI", "FTP", "INT_FTP", "WEB", "MANUAL", "UPLOAD", "ANDROID", "EXTERNAL_WMS", "CLIENT_PORTAL", "ITEM_MASTER", "DTS", "CONVEYOR_LINE", "PUBLIC_API", "CARRIER_WEB", "EXTERNAL_FMS", "AIR_ROB"]}, "latitude": {"type": "string", "description": ""}, "longitude": {"type": "string", "description": ""}, "crmAddressId": {"type": "string", "description": ""}}}, "ReceiveTaskQuery": {"type": "object", "properties": {"currentPage": {"type": "integer", "description": ""}, "pageSize": {"type": "integer", "description": ""}, "sortingFields": {"type": "array", "items": {"$ref": "#/components/schemas/SortingField", "description": "com.item.xms.persistence.query.SortingField"}, "description": ""}, "id": {"type": "string", "description": ""}, "ids": {"type": "array", "items": {"type": "string"}, "description": ""}, "assigneeUserId": {"type": "string", "description": ""}, "customerId": {"type": "string", "description": ""}, "customerIds": {"type": "array", "items": {"type": "string"}, "description": ""}, "status": {"type": "string", "description": "", "enum": ["NEW", "IN_PROGRESS", "EXCEPTION", "CLOSE", "FORCE_CLOSE", "CANCELLED"]}, "statuses": {"type": "array", "items": {"type": "string", "enum": ["NEW", "IN_PROGRESS", "EXCEPTION", "CLOSE", "FORCE_CLOSE", "CANCELLED"]}, "description": ""}, "assigneeUserIds": {"type": "array", "items": {"type": "string"}, "description": ""}, "priority": {"type": "string", "description": ""}, "dockId": {"type": "integer", "description": ""}, "receiptIds": {"type": "array", "items": {"type": "string"}, "description": ""}, "startTimePeriod": {"type": "array", "items": {"type": "string"}, "description": ""}, "endTimePeriod": {"type": "array", "items": {"type": "string"}, "description": ""}, "createdBy": {"type": "string", "description": ""}, "createdTimePeriod": {"type": "array", "items": {"type": "string"}, "description": ""}, "updatedBy": {"type": "string", "description": ""}, "updatedTimePeriod": {"type": "array", "items": {"type": "string"}, "description": ""}, "withTaskSteps": {"type": "boolean", "description": ""}, "entryId": {"type": "string", "description": ""}, "entryIds": {"type": "array", "items": {"type": "string"}, "description": ""}}}, "BAMOrderLineDTO": {"type": "object", "properties": {"id": {"type": ["string", "null"], "description": ""}, "omsId": {"type": ["string", "null"], "description": ""}, "omsMasterId": {"type": ["string", "null"], "description": ""}, "status": {"type": ["string", "null"], "description": "", "enum": ["IMPORTED", "OPEN", "COMMITTED", "PARTIAL_COMMITTED", "COMMIT_BLOCKED", "COMMIT_FAILED", "PLANNING", "PLANNED", "PICKING", "PICKED", "PACKING", "PACKED", "STAGED", "LOADING", "LOADED", "PARTIAL_SHIPPED", "SHORT_SHIPPED", "SHIPPED", "REOPEN", "CANCELLED", "EXCEPTION", "ON_HOLD"]}, "customerId": {"type": ["string", "null"], "description": ""}, "carrierId": {"type": ["string", "null"], "description": ""}, "retailerId": {"type": ["string", "null"], "description": ""}, "shipMethod": {"type": ["string", "null"], "description": "", "enum": ["TL", "LTL", "WILL_CALL", "SP"]}, "deliveryService": {"type": ["string", "null"], "description": ""}, "orderType": {"type": ["string", "null"], "description": "", "enum": ["RG", "TT", "MT", "DS", "ITO", "TRANSLOAD"]}, "source": {"type": ["string", "null"], "description": "", "enum": ["EDI", "FTP", "INT_FTP", "WEB", "MANUAL", "UPLOAD", "ANDROID", "EXTERNAL_WMS", "CLIENT_PORTAL", "ITEM_MASTER", "DTS", "CONVEYOR_LINE", "PUBLIC_API", "CARRIER_WEB"]}, "referenceNo": {"type": ["string", "null"], "description": ""}, "batchNo": {"type": ["string", "null"], "description": ""}, "poNo": {"type": ["string", "null"], "description": ""}, "soNo": {"type": ["string", "null"], "description": ""}, "shippingAccountNo": {"type": ["string", "null"], "description": ""}, "freightTerm": {"type": ["string", "null"], "description": "", "enum": ["COLLECT", "PREPAID", "THIRD_PARTY"]}, "freightCost": {"type": ["number", "null"], "description": ""}, "subOrderType": {"type": ["string", "null"], "description": ""}, "shipFromAddress": {"$ref": "#/components/schemas/AddressDto", "description": ""}, "skipCollectSn": {"type": ["boolean", "null"], "description": ""}, "masterTrackingNo": {"type": ["string", "null"], "description": ""}, "labelCode": {"type": ["string", "null"], "description": ""}, "isInternational": {"type": ["boolean", "null"], "description": ""}, "containHazardous": {"type": ["boolean", "null"], "description": ""}, "exceptionReason": {"type": ["string", "null"], "description": ""}, "incoterm": {"type": ["string", "null"], "description": "", "enum": ["DAP", "DDP", "EXW", "DDU"]}, "mbolNo": {"type": ["string", "null"], "description": ""}, "proNo": {"type": ["string", "null"], "description": ""}, "bolNo": {"type": ["string", "null"], "description": ""}, "loadNo": {"type": ["string", "null"], "description": ""}, "orderedDate": {"type": ["string", "null"], "description": ""}, "shipNotBefore": {"type": ["string", "null"], "description": ""}, "shipNoLater": {"type": ["string", "null"], "description": ""}, "totalWeight": {"type": ["number", "null"], "description": ""}, "totalCubicFeet": {"type": ["number", "null"], "description": ""}, "containerSize": {"type": ["string", "null"], "description": ""}, "mabd": {"type": ["string", "null"], "description": ""}, "appointmentTime": {"type": ["string", "null"], "description": ""}, "inYardTime": {"type": ["string", "null"], "description": ""}, "canceledDate": {"type": ["string", "null"], "description": ""}, "scheduleDate": {"type": ["string", "null"], "description": ""}, "shippedTime": {"type": ["string", "null"], "description": ""}, "soldToAddress": {"$ref": "#/components/schemas/AddressDto", "description": ""}, "shipToAddress": {"$ref": "#/components/schemas/AddressDto", "description": ""}, "storeAddress": {"$ref": "#/components/schemas/AddressDto", "description": ""}, "billToAddress": {"$ref": "#/components/schemas/AddressDto", "description": ""}, "createdBy": {"type": ["string", "null"], "description": ""}, "createdTime": {"type": ["string", "null"], "description": ""}, "updatedBy": {"type": ["string", "null"], "description": ""}, "updatedTime": {"type": ["string", "null"], "description": ""}, "historyStatuses": {"type": ["array", "null"], "items": {"type": "string", "enum": ["IMPORTED", "OPEN", "COMMITTED", "PARTIAL_COMMITTED", "COMMIT_BLOCKED", "COMMIT_FAILED", "PLANNING", "PLANNED", "PICKING", "PICKED", "PACKING", "PACKED", "STAGED", "LOADING", "LOADED", "PARTIAL_SHIPPED", "SHORT_SHIPPED", "SHIPPED", "REOPEN", "CANCELLED", "EXCEPTION", "ON_HOLD"]}, "description": ""}, "dynamicFields": {"$ref": "#/components/schemas/DynamicFields", "description": "Assuming JSON as String"}, "totalPallets": {"type": ["number", "null"], "description": ""}, "uniqueKey": {"type": ["string", "null"], "description": ""}, "isRush": {"type": ["boolean", "null"], "description": ""}, "isSingleQTY": {"type": ["boolean", "null"], "description": ""}, "sysNote": {"type": ["string", "null"], "description": ""}, "blockNote": {"type": ["string", "null"], "description": ""}, "orderNote": {"type": ["string", "null"], "description": ""}, "cancelledNote": {"type": ["string", "null"], "description": ""}, "isUCCLabelPrinted": {"type": ["boolean", "null"], "description": ""}, "isPackingListPrinted": {"type": ["boolean", "null"], "description": ""}, "isPalletLabelPrinted": {"type": ["boolean", "null"], "description": ""}, "destination": {"type": ["string", "null"], "description": ""}, "itemLines": {"type": ["array", "null"], "items": {"$ref": "#/components/schemas/OrderItemLineDto", "description": "com.item.wms.application.outbound.order.dtos.OrderItemLineDto"}, "description": ""}, "loadId": {"type": ["string", "null"], "description": ""}, "orderId": {"type": ["string", "null"], "description": ""}, "sequence": {"type": ["integer", "null"], "description": ""}, "customerName": {"type": ["string", "null"], "description": ""}, "carrierName": {"type": ["string", "null"], "description": ""}, "retailerName": {"type": ["string", "null"], "description": ""}}}, "OrderItemLineDto": {"type": "object", "properties": {"id": {"type": "string", "description": ""}, "orderId": {"type": "string", "description": ""}, "qty": {"type": "number", "description": ""}, "itemId": {"type": "string", "description": ""}, "uomId": {"type": "string", "description": ""}, "titleId": {"type": "string", "description": ""}, "lotNo": {"type": "string", "description": ""}, "upcCode": {"type": "string", "description": ""}, "destination": {"type": "string", "description": ""}, "destinationAddress": {"$ref": "#/components/schemas/AddressExt", "description": ""}, "destinationGroup": {"type": "string", "description": ""}, "supplierId": {"type": "string", "description": ""}, "shippedQty": {"type": "number", "description": ""}, "palletQty": {"type": "number", "description": ""}, "adjustedPalletQty": {"type": "number", "description": ""}, "orderedQty": {"type": "number", "description": ""}, "palletLength": {"type": "number", "description": ""}, "palletWidth": {"type": "number", "description": ""}, "palletHeight": {"type": "number", "description": ""}, "palletWeight": {"type": "number", "description": ""}, "lineNo": {"type": "string", "description": ""}, "lpConfigurationId": {"type": "string", "description": ""}, "note": {"type": "string", "description": ""}, "kitItemId": {"type": "string", "description": ""}, "kitItemlineId": {"type": "string", "description": ""}, "itemCondition": {"type": "string", "description": ""}, "totalInsuranceAmount": {"type": "number", "description": ""}, "unitPrice": {"type": "number", "description": ""}, "unitPriceCurrency": {"type": "string", "description": "", "enum": ["CNY", "USD", "EUR"]}, "dynamicFields": {"$ref": "#/components/schemas/DynamicFields", "description": "Assuming JSON as String"}, "snList": {"type": "array", "items": {"type": "string"}, "description": ""}, "createdTime": {"type": "string", "description": ""}, "createdBy": {"type": "string", "description": ""}, "updatedTime": {"type": "string", "description": ""}, "updatedBy": {"type": "string", "description": ""}, "baseQty": {"type": "number", "description": ""}, "goodsType": {"type": "string", "description": ""}, "estimatedPalletQty": {"type": "number", "description": ""}, "palletSlpIds": {"type": "array", "items": {"type": "string"}, "description": ""}, "productId": {"type": "string", "description": ""}, "buyerItemId": {"type": "string", "description": ""}, "type": {"type": "string", "description": ""}, "properties": {"type": "array", "items": {"$ref": "#/components/schemas/ProductProperty", "description": "com.item.wms.domain.outbound.order.model.entity.OrderItemLine.ProductPropertyEntity"}, "description": ""}, "receiptProperties": {"type": "array", "items": {"$ref": "#/components/schemas/ReceiptProperty", "description": "com.item.wms.domain.outbound.order.model.entity.OrderItemLine.ReceiptPropertyEntity"}, "description": ""}, "unitId": {"type": "string", "description": ""}, "customerPalletQty": {"type": "number", "description": ""}, "packageConfigure": {"type": "string", "description": ""}, "commodityDescription": {"type": "string", "description": ""}, "itemCodeId": {"type": "string", "description": ""}, "pickLocations": {"type": "array", "items": {"$ref": "#/components/schemas/PickLocation", "description": "com.item.wms.domain.outbound.order.model.entity.OrderItemLine.PickLocationEntity"}, "description": ""}, "source": {"type": "string", "description": ""}, "originalOrderQty": {"type": "number", "description": ""}, "cartons": {"type": "array", "items": {"$ref": "#/components/schemas/Carton", "description": "com.item.wms.domain.outbound.order.model.entity.OrderItemLine.CartonEntity"}, "description": ""}, "packSlipDescription": {"type": "string", "description": ""}, "palletLabelLPIds": {"type": "array", "items": {"type": "string"}, "description": ""}, "orderWeight": {"type": "number", "description": ""}, "uccType": {"type": "string", "description": ""}, "ssccs": {"type": "array", "items": {"type": "string"}, "description": ""}, "palletSsccs": {"type": "array", "items": {"type": "string"}, "description": ""}, "mergeItemByEDI": {"type": "string", "description": ""}, "originalItemProductNumber": {"type": "string", "description": ""}, "originalItemLineFromEDIs": {"type": "array", "items": {"$ref": "#/components/schemas/OriginalItemLineFromEDI", "description": "com.item.wms.domain.outbound.order.model.entity.OrderItemLine.OriginalItemLineFromEDIEntity"}, "description": ""}, "prePackDescription": {"type": "string", "description": ""}, "prePackQty": {"type": "number", "description": ""}, "bundleItemSpecId": {"type": "string", "description": ""}, "originalBundleItemSpecId": {"type": "string", "description": ""}, "originalBundleQty": {"type": "number", "description": ""}, "originalBundleItemSpecName": {"type": "string", "description": ""}, "originalBundleItemUnitName": {"type": "string", "description": ""}, "countryOfOriginByLabel": {"type": "string", "description": ""}, "modelNoByLabel": {"type": "string", "description": ""}, "expirationDateByLabel": {"type": "string", "description": ""}, "colorByLabel": {"type": "string", "description": ""}, "sizeByLabel": {"type": "string", "description": ""}, "styleByLabel": {"type": "string", "description": ""}, "estimatedFreightCost": {"type": "number", "description": ""}}}, "OriginalItemLineFromEDI": {"type": "object", "properties": {"lineNo": {"type": "string", "description": ""}, "qty": {"type": "number", "description": ""}, "requireReturnLabel": {"type": "boolean", "description": ""}, "dynamicFields": {"$ref": "#/components/schemas/DynamicFields", "description": "Assuming JSON as String"}, "returnTrackingNo": {"type": "string", "description": ""}, "originalItemProductNumber": {"type": "string", "description": ""}}}, "Carton": {"type": "object", "properties": {"cartonNo": {"type": "string", "description": ""}, "poundPerCarton": {"type": "number", "description": ""}, "qty": {"type": "number", "description": ""}, "lotNo": {"type": "string", "description": ""}, "dynamicFields": {"$ref": "#/components/schemas/DynamicFields", "description": "Assuming JSON as String"}, "cartonDynamicFields": {"$ref": "#/components/schemas/DynamicFields", "description": "Assuming JSON as String"}, "destination": {"type": "string", "description": ""}, "destinationAddress": {"$ref": "#/components/schemas/AddressExt", "description": ""}, "oriDestination": {"type": "string", "description": ""}, "weight": {"type": "number", "description": ""}, "weightUnit": {"type": "string", "description": ""}, "originalWeight": {"type": "number", "description": ""}, "originalWeightUnit": {"type": "string", "description": ""}, "source": {"type": "string", "description": ""}, "isDamaged": {"type": "boolean", "description": ""}, "isExtraCarton": {"type": "boolean", "description": ""}, "mode": {"type": "string", "description": ""}, "isCrossMatched": {"type": "boolean", "description": ""}, "cube": {"type": "number", "description": ""}, "scanningMemo": {"type": "string", "description": ""}, "volumeUnit": {"type": "string", "description": ""}, "appointmentDate": {"type": "string", "description": ""}, "status": {"type": "string", "description": ""}}}, "PickLocation": {"type": "object", "properties": {"locationId": {"type": "string", "description": ""}, "unitId": {"type": "string", "description": ""}, "productId": {"type": "string", "description": ""}, "pickQty": {"type": "number", "description": "", "default": 0}}}, "ReceiptProperty": {"type": "object", "properties": {"field": {"type": "string", "description": ""}, "value": {"type": "string", "description": ""}}}, "ProductProperty": {"type": "object", "properties": {"propertyId": {"type": "string", "description": ""}, "value": {"type": "string", "description": ""}, "unit": {"type": "string", "description": ""}}}, "RBAMPickItemLineDto": {"type": "object", "properties": {"code": {"type": "integer", "description": ""}, "msg": {"type": "string", "description": ""}, "success": {"type": "boolean", "description": ""}, "data": {"$ref": "#/components/schemas/BAMPickItemLineDto", "description": ""}}}, "R«PageResult«ReceiveTaskView»»": {"type": "object", "properties": {"code": {"type": ["integer", "null"], "description": ""}, "msg": {"type": ["string", "null"], "description": ""}, "success": {"type": ["boolean", "null"], "description": ""}, "data": {"$ref": "#/components/schemas/PageResult%C2%ABReceiveTaskView%C2%BB", "description": ""}}}, "PageResult«ReceiveTaskView»": {"type": "object", "properties": {"list": {"type": ["array", "null"], "items": {"$ref": "#/components/schemas/ReceiveTaskView", "description": "com.item.wms.interfaces.bam.dtos.receivetask.ReceiveTaskView"}, "description": ""}, "totalCount": {"type": ["integer", "null"], "description": ""}, "currentPage": {"type": ["integer", "null"], "description": ""}, "pageSize": {"type": ["integer", "null"], "description": ""}, "totalPage": {"type": ["integer", "null"], "description": ""}}}, "ReceiveTaskView": {"type": "object", "properties": {"id": {"type": ["string", "null"], "description": ""}, "customerId": {"type": ["string", "null"], "description": ""}, "status": {"type": ["string", "null"], "description": "", "enum": ["NEW", "IN_PROGRESS", "EXCEPTION", "CLOSE", "FORCE_CLOSE", "CANCELLED"]}, "note": {"type": ["string", "null"], "description": ""}, "tags": {"type": ["array", "null"], "items": {"type": "string"}, "description": ""}, "preAssigneeUserId": {"type": ["string", "null"], "description": ""}, "assigneeUserId": {"type": ["string", "null"], "description": ""}, "lastAssignedWhen": {"type": ["string", "null"], "description": ""}, "priority": {"type": ["string", "null"], "description": "", "enum": ["LOW", "MIDDLE", "HIGH", "TOP"]}, "startTime": {"type": ["string", "null"], "description": ""}, "endTime": {"type": ["string", "null"], "description": ""}, "taskSteps": {"type": ["array", "null"], "items": {"$ref": "#/components/schemas/TaskStepDto", "description": "com.item.wms.application.task.common.dto.TaskStepDto"}, "description": ""}, "createdTime": {"type": ["string", "null"], "description": ""}, "createdBy": {"type": ["string", "null"], "description": ""}, "updatedTime": {"type": ["string", "null"], "description": ""}, "updatedBy": {"type": ["string", "null"], "description": ""}, "sysNote": {"type": ["string", "null"], "description": ""}, "dockId": {"type": ["string", "null"], "description": ""}, "entryId": {"type": ["string", "null"], "description": ""}, "receiptIds": {"type": ["array", "null"], "items": {"type": "string"}, "description": ""}, "receipts": {"type": ["array", "null"], "items": {"$ref": "#/components/schemas/BAMReceiptDto", "description": "com.item.wms.interfaces.rest.bam.dtos.receipt.BAMReceiptDto"}, "description": ""}, "dockName": {"type": ["string", "null"], "description": ""}, "customerName": {"type": ["string", "null"], "description": ""}, "assigneeUserName": {"type": ["string", "null"], "description": ""}}}, "OrderQuery": {"type": "object", "properties": {"currentPage": {"type": "integer", "description": ""}, "pageSize": {"type": "integer", "description": ""}, "sortingFields": {"type": "array", "items": {"$ref": "#/components/schemas/SortingField", "description": "com.item.xms.persistence.query.SortingField"}, "description": ""}, "id": {"type": "string", "description": ""}, "ids": {"type": "array", "items": {"type": "string"}, "description": ""}, "omsId": {"type": "string", "description": ""}, "omsIds": {"type": "array", "items": {"type": "string"}, "description": ""}, "omsMasterId": {"type": "string", "description": ""}, "omsMasterIds": {"type": "array", "items": {"type": "string"}, "description": ""}, "facilityId": {"type": "string", "description": ""}, "status": {"type": "string", "description": "", "enum": ["IMPORTED", "OPEN", "COMMITTED", "PARTIAL_COMMITTED", "COMMIT_FAILED", "COMMIT_BLOCKED", "PLANNING", "PLANNED", "PICKING", "PICKED", "STAGED", "PACKING", "PACKED", "LOADING", "LOADED", "PARTIAL_SHIPPED", "SHORT_SHIPPED", "SHIPPED", "REOPEN", "CANCELLED", "EXCEPTION", "ON_HOLD"]}, "statuses": {"type": "array", "items": {"type": "string", "enum": ["IMPORTED", "OPEN", "COMMITTED", "PARTIAL_COMMITTED", "COMMIT_FAILED", "COMMIT_BLOCKED", "PLANNING", "PLANNED", "PICKING", "PICKED", "STAGED", "PACKING", "PACKED", "LOADING", "LOADED", "PARTIAL_SHIPPED", "SHORT_SHIPPED", "SHIPPED", "REOPEN", "CANCELLED", "EXCEPTION", "ON_HOLD"]}, "description": ""}, "excludeStatuses": {"type": "array", "items": {"type": "string"}, "description": ""}, "customerId": {"type": "string", "description": ""}, "carrierId": {"type": "string", "description": ""}, "retailerId": {"type": "string", "description": ""}, "shipMethod": {"type": "string", "description": "", "enum": ["TL", "LTL", "WILL_CALL", "SP"]}, "deliveryService": {"type": "string", "description": ""}, "orderType": {"type": "string", "description": "", "enum": ["RG", "TT", "MT", "DS", "ITO", "TRANSLOAD"]}, "orderTypes": {"type": "array", "items": {"type": "string", "enum": ["RG", "TT", "MT", "DS", "ITO", "TRANSLOAD"]}, "description": ""}, "excludeOrderTypes": {"type": "array", "items": {"type": "string"}, "description": ""}, "source": {"type": "string", "description": "", "enum": ["EDI", "FTP", "INT_FTP", "WEB", "MANUAL", "UPLOAD", "ANDROID", "EXTERNAL_WMS", "CLIENT_PORTAL", "ITEM_MASTER", "DTS", "CONVEYOR_LINE", "PUBLIC_API", "CARRIER_WEB", "EXTERNAL_FMS", "AIR_ROB"]}, "referenceNo": {"type": "string", "description": ""}, "referenceNos": {"type": "array", "items": {"type": "string"}, "description": ""}, "batchNo": {"type": "string", "description": ""}, "poNo": {"type": "string", "description": ""}, "shippingAccountNo": {"type": "string", "description": ""}, "freightTerm": {"type": "string", "description": "", "enum": ["COLLECT", "PREPAID", "THIRD_PARTY"]}, "freightCost": {"type": "number", "description": ""}, "subOrderType": {"type": "string", "description": ""}, "allowPartialLockInventory": {"type": "boolean", "description": ""}, "skipCollectSn": {"type": "boolean", "description": ""}, "masterTrackingNo": {"type": "string", "description": ""}, "labelCode": {"type": "string", "description": ""}, "isInternational": {"type": "boolean", "description": ""}, "containBattery": {"type": "boolean", "description": ""}, "exceptionReason": {"type": "string", "description": ""}, "incoterm": {"type": "string", "description": "", "enum": ["DAP", "DDP", "EXW", "DDU"]}, "mblNo": {"type": "string", "description": ""}, "proNo": {"type": "string", "description": ""}, "bolNo": {"type": "string", "description": ""}, "loadNo": {"type": "string", "description": ""}, "loadNos": {"type": "array", "items": {"type": "string"}, "description": ""}, "orderedDateFrom": {"type": "string", "description": ""}, "orderedDateTo": {"type": "string", "description": ""}, "shipNotBeforeDateFrom": {"type": "string", "description": ""}, "shipNotBeforeDateTo": {"type": "string", "description": ""}, "shipNoLaterDateFrom": {"type": "string", "description": ""}, "shipNoLaterDateTo": {"type": "string", "description": ""}, "routedDateFrom": {"type": "string", "description": ""}, "routedDateTo": {"type": "string", "description": ""}, "totalWeight": {"type": "number", "description": ""}, "totalCubicFeet": {"type": "number", "description": ""}, "containerSize": {"type": "string", "description": ""}, "mabdFrom": {"type": "string", "description": ""}, "mabdTo": {"type": "string", "description": ""}, "appointmentTimeFrom": {"type": "string", "description": ""}, "appointmentTimeTo": {"type": "string", "description": ""}, "appointmentStartTimePeriod": {"type": "array", "items": {"type": "string"}, "description": ""}, "inYardTimeFrom": {"type": "string", "description": ""}, "inYardTimeTo": {"type": "string", "description": ""}, "inYardTimePeriod": {"type": "array", "items": {"type": "string"}, "description": ""}, "canceledDateFrom": {"type": "string", "description": ""}, "canceledDateTo": {"type": "string", "description": ""}, "canceledDatePeriod": {"type": "array", "items": {"type": "string"}, "description": ""}, "scheduleDateFrom": {"type": "string", "description": ""}, "scheduleDateTo": {"type": "string", "description": ""}, "scheduleDatePeriod": {"type": "array", "items": {"type": "string"}, "description": ""}, "shippedTimeFrom": {"type": "string", "description": ""}, "shippedTimeTo": {"type": "string", "description": ""}, "shippedTimePeriod": {"type": "array", "items": {"type": "string"}, "description": ""}, "createdBy": {"type": "string", "description": ""}, "createdTimeFrom": {"type": "string", "description": ""}, "createdTimeTo": {"type": "string", "description": ""}, "createdTimePeriod": {"type": "array", "items": {"type": "string"}, "description": ""}, "updatedBy": {"type": "string", "description": ""}, "updatedTimeFrom": {"type": "string", "description": ""}, "updatedTimeTo": {"type": "string", "description": ""}, "updatedTimePeriod": {"type": "array", "items": {"type": "string"}, "description": ""}, "preStatus": {"type": "string", "description": ""}, "totalPallets": {"type": "number", "description": ""}, "enableAutoCommit": {"type": "boolean", "description": ""}, "uniqueKey": {"type": "string", "description": ""}, "exceptionCode": {"type": "string", "description": ""}, "stakeholder": {"type": "string", "description": "", "enum": ["UNIS", "UNIS_Transportation", "Customer", "End_Customer", "Carrier", "Other"]}, "isRush": {"type": "boolean", "description": ""}, "consolidationNo": {"type": "string", "description": ""}, "isSingleQTY": {"type": "boolean", "description": ""}, "sysNote": {"type": "string", "description": ""}, "blockNote": {"type": "string", "description": ""}, "orderNote": {"type": "string", "description": ""}, "cancelledNote": {"type": "string", "description": ""}, "isUCCLabelPrinted": {"type": "boolean", "description": ""}, "isPackingListPrinted": {"type": "boolean", "description": ""}, "isPalletLabelPrinted": {"type": "boolean", "description": ""}, "destination": {"type": "string", "description": ""}, "soNos": {"type": "array", "items": {"type": "string"}, "description": ""}, "soNo": {"type": "string", "description": ""}, "orPoNo": {"type": "string", "description": ""}, "orBolNo": {"type": "string", "description": ""}, "orReferenceNo": {"type": "string", "description": ""}, "orId": {"type": "string", "description": ""}, "orderEqSearchForSelf": {"type": "string", "description": "only used by self-checkin"}, "itemId": {"type": "string", "description": ""}, "itemIds": {"type": "array", "items": {"type": "string"}, "description": ""}, "withItemLine": {"type": "boolean", "description": "", "default": false}, "keyword": {"type": "string", "description": ""}, "shipmentTrackingType": {"type": "string", "description": "", "enum": ["LP_LEVEL", "ORDER_LEVEL"]}, "dynTxtPropertyValue01": {"type": "string", "description": ""}, "dynTxtPropertyValue02": {"type": "string", "description": ""}, "dynTxtPropertyValue03": {"type": "string", "description": ""}, "dynTxtPropertyValue04": {"type": "string", "description": ""}, "dynTxtPropertyValue05": {"type": "string", "description": ""}, "dynTxtPropertyValue06": {"type": "string", "description": ""}, "dynTxtPropertyValue07": {"type": "string", "description": ""}, "dynTxtPropertyValue08": {"type": "string", "description": ""}, "dynTxtPropertyValue09": {"type": "string", "description": ""}, "dynTxtPropertyValue10": {"type": "string", "description": ""}, "dynTxtPropertyValue11": {"type": "string", "description": ""}, "dynTxtPropertyValue12": {"type": "string", "description": ""}, "dynTxtPropertyValue13": {"type": "string", "description": ""}, "dynTxtPropertyValue14": {"type": "string", "description": ""}, "dynTxtPropertyValue15": {"type": "string", "description": ""}, "dynTxtPropertyValue16": {"type": "string", "description": ""}, "dynTxtPropertyValue17": {"type": "string", "description": ""}, "dynTxtPropertyValue18": {"type": "string", "description": ""}, "dynTxtPropertyValue19": {"type": "string", "description": ""}, "dynTxtPropertyValue20": {"type": "string", "description": ""}, "dynTxtPropertyValue21": {"type": "string", "description": ""}, "dynTxtPropertyValue22": {"type": "string", "description": ""}, "dynTxtPropertyValue23": {"type": "string", "description": ""}, "dynTxtPropertyValue24": {"type": "string", "description": ""}, "dynTxtPropertyValue25": {"type": "string", "description": ""}, "dynDatePropertyValue01": {"type": "string", "description": ""}, "dynDatePropertyValue02": {"type": "string", "description": ""}, "dynDatePropertyValue03": {"type": "string", "description": ""}, "dynDatePropertyValue04": {"type": "string", "description": ""}, "dynDatePropertyValue05": {"type": "string", "description": ""}}}, "R«PageResult«BAMOrderDto»»": {"type": "object", "properties": {"code": {"type": ["integer", "null"], "description": ""}, "msg": {"type": ["string", "null"], "description": ""}, "success": {"type": ["boolean", "null"], "description": ""}, "data": {"$ref": "#/components/schemas/PageResult%C2%ABBAMOrderDto%C2%BB", "description": ""}}}, "PageResult«BAMOrderDto»": {"type": "object", "properties": {"list": {"type": ["array", "null"], "items": {"$ref": "#/components/schemas/BAMOrderDto", "description": "com.item.wms.interfaces.rest.bam.dtos.order.BAMOrderDto"}, "description": ""}, "totalCount": {"type": ["integer", "null"], "description": ""}, "currentPage": {"type": ["integer", "null"], "description": ""}, "pageSize": {"type": ["integer", "null"], "description": ""}, "totalPage": {"type": ["integer", "null"], "description": ""}}}, "OrderPlanQuery": {"type": "object", "properties": {"currentPage": {"type": ["integer", "null"], "description": ""}, "pageSize": {"type": ["integer", "null"], "description": ""}, "sortingFields": {"type": ["array", "null"], "items": {"$ref": "#/components/schemas/SortingField", "description": "com.item.xms.persistence.query.SortingField"}, "description": ""}, "id": {"type": ["string", "null"], "description": ""}, "ids": {"type": ["array", "null"], "items": {"type": "string"}, "description": ""}, "pickType": {"type": ["string", "null"], "description": "", "enum": ["PALLET_PICK", "CASE_PICK", "PIECE_PICK"]}, "pickTypes": {"type": ["array", "null"], "items": {"type": "string", "enum": ["PALLET_PICK", "CASE_PICK", "PIECE_PICK"]}, "description": ""}, "pickMethod": {"type": ["string", "null"], "description": "", "enum": ["ORDER_PICK", "WAVE_PICK_BY_ITEM", "WAVE_PICK_BY_ORDER", "BATCH_ORDER_PICK"]}, "pickMethods": {"type": ["array", "null"], "items": {"type": "string", "enum": ["ORDER_PICK", "WAVE_PICK_BY_ITEM", "WAVE_PICK_BY_ORDER", "BATCH_ORDER_PICK"]}, "description": ""}, "pickMode": {"type": ["string", "null"], "description": "", "enum": ["PICK_TO_WALL", "OPPORTUNITY_PICK"]}, "pickModes": {"type": ["array", "null"], "items": {"type": "string", "enum": ["PICK_TO_WALL", "OPPORTUNITY_PICK"]}, "description": ""}, "enableAutoGroup": {"type": ["boolean", "null"], "description": ""}, "orderPlanItemLineGroupBys": {"type": ["array", "null"], "items": {"type": "string", "enum": ["VLG", "CARRIER", "SHIP_TO"]}, "description": ""}, "defaultAssigneeUserId": {"type": ["string", "null"], "description": ""}, "orderIds": {"type": ["array", "null"], "items": {"type": "string"}, "description": ""}, "pickTaskIds": {"type": ["array", "null"], "items": {"type": "string"}, "description": ""}, "replenishmentTaskIds": {"type": ["array", "null"], "items": {"type": "string"}, "description": ""}, "status": {"type": ["string", "null"], "description": "", "enum": ["BUILDING", "PICK_SUGGESTED", "TASK_CREATED", "SCHEDULED", "RELEASED", "COMPLETED", "CANCELLED"]}, "statuses": {"type": ["array", "null"], "items": {"type": "string", "enum": ["BUILDING", "PICK_SUGGESTED", "TASK_CREATED", "SCHEDULED", "RELEASED", "COMPLETED", "CANCELLED"]}, "description": ""}, "createdBy": {"type": ["string", "null"], "description": ""}, "createdTimeFrom": {"type": ["string", "null"], "description": ""}, "createdTimeTo": {"type": ["string", "null"], "description": ""}, "updatedBy": {"type": ["string", "null"], "description": ""}, "updatedTimeFrom": {"type": ["string", "null"], "description": ""}, "updatedTimeTo": {"type": ["string", "null"], "description": ""}, "customerId": {"type": ["string", "null"], "description": ""}, "customerIds": {"type": ["array", "null"], "items": {"type": "string"}, "description": ""}, "orderDispatchSettingId": {"type": ["string", "null"], "description": ""}, "taskTags": {"type": ["array", "null"], "items": {"type": "string"}, "description": ""}, "taskPriority": {"type": ["string", "null"], "description": "", "enum": ["LOW", "MIDDLE", "HIGH", "TOP"]}, "maxOrderQtyPerPickTask": {"type": ["integer", "null"], "description": ""}, "maxItemLineQtyPerPickTask": {"type": ["integer", "null"], "description": ""}, "shippingRule": {"type": ["string", "null"], "description": "", "enum": ["FIFO", "LIFO", "FEFO", "LSFO", "LEFO"]}, "shippingRules": {"type": ["array", "null"], "items": {"type": "string", "enum": ["FIFO", "LIFO", "FEFO", "LSFO", "LEFO"]}, "description": ""}, "removePlanOrdersWithErrorAndMarkEx": {"type": ["boolean", "null"], "description": ""}, "noNeedAutoRelease": {"type": ["boolean", "null"], "description": ""}, "orderDispatchSettingIdIsNotNull": {"type": ["boolean", "null"], "description": "", "default": "false"}, "orderDispatchSettingIdIsNull": {"type": ["boolean", "null"], "description": "", "default": "false"}, "createdByOrderDispatch": {"type": ["boolean", "null"], "description": ""}}}, "BAMLoadDto": {"type": "object", "properties": {"createdTime": {"type": ["string", "null"], "description": ""}, "createdBy": {"type": ["string", "null"], "description": ""}, "updatedTime": {"type": ["string", "null"], "description": ""}, "updatedBy": {"type": ["string", "null"], "description": ""}, "id": {"type": ["string", "null"], "description": ""}, "status": {"type": ["string", "null"], "description": "", "enum": ["NEW", "LOADING", "LOADED", "SHIPPED", "CANCELLED"]}, "loadNo": {"type": ["string", "null"], "description": ""}, "customerId": {"type": ["string", "null"], "description": ""}, "customerName": {"type": ["string", "null"], "description": ""}, "carrierId": {"type": ["string", "null"], "description": ""}, "carrierName": {"type": ["string", "null"], "description": ""}, "proNo": {"type": ["string", "null"], "description": ""}, "freightTerm": {"type": ["string", "null"], "description": "", "enum": ["COLLECT", "PREPAID", "THIRD_PARTY"]}, "freightCost": {"type": ["number", "null"], "description": ""}, "loadType": {"type": ["string", "null"], "description": "", "enum": ["LTL", "TL", "TRANSLOAD"]}, "appointmentTime": {"type": ["string", "null"], "description": ""}, "masterBolNo": {"type": ["string", "null"], "description": ""}, "shipFrom": {"$ref": "#/components/schemas/AddressDto", "description": ""}, "shipTo": {"$ref": "#/components/schemas/AddressDto", "description": ""}, "note": {"type": ["string", "null"], "description": ""}, "startTime": {"type": ["string", "null"], "description": ""}, "endTime": {"type": ["string", "null"], "description": ""}, "sequence": {"type": ["integer", "null"], "description": ""}, "trailerNo": {"type": ["string", "null"], "description": ""}, "trailerPickUpMode": {"type": ["string", "null"], "description": "", "enum": ["DROP_LOAD", "LIVE_LOAD"]}, "loadPhotos": {"type": ["array", "null"], "items": {"type": "string"}, "description": ""}, "countingSheetPhotos": {"type": ["array", "null"], "items": {"type": "string"}, "description": ""}, "carrierSignatureFileId": {"type": ["string", "null"], "description": ""}, "shipperSignatureFileId": {"type": ["string", "null"], "description": ""}, "carrierSignatureTime": {"type": ["string", "null"], "description": ""}, "shipperSignatureTime": {"type": ["string", "null"], "description": ""}, "destination": {"type": ["string", "null"], "description": ""}, "orderLines": {"type": ["array", "null"], "items": {"$ref": "#/components/schemas/BAMOrderLineDTO", "description": ""}, "description": ""}}}, "R«PageResult«OrderPlanDto»»": {"type": "object", "properties": {"code": {"type": ["integer", "null"], "description": ""}, "msg": {"type": ["string", "null"], "description": ""}, "success": {"type": ["boolean", "null"], "description": ""}, "data": {"$ref": "#/components/schemas/PageResult%C2%ABOrderPlanDto%C2%BB", "description": ""}}}, "PageResult«OrderPlanDto»": {"type": "object", "properties": {"list": {"type": ["array", "null"], "items": {"$ref": "#/components/schemas/OrderPlanDto", "description": "com.item.wms.application.outbound.orderplan.dto.OrderPlanDto"}, "description": ""}, "totalCount": {"type": ["integer", "null"], "description": ""}, "currentPage": {"type": ["integer", "null"], "description": ""}, "pageSize": {"type": ["integer", "null"], "description": ""}, "totalPage": {"type": ["integer", "null"], "description": ""}}}, "OrderPlanDto": {"type": "object", "properties": {"id": {"type": ["string", "null"], "description": ""}, "pickType": {"type": ["string", "null"], "description": "", "enum": ["PALLET_PICK", "CASE_PICK", "PIECE_PICK"]}, "pickMethod": {"type": ["string", "null"], "description": "", "enum": ["ORDER_PICK", "WAVE_PICK_BY_ITEM", "WAVE_PICK_BY_ORDER", "BATCH_ORDER_PICK"]}, "pickMode": {"type": ["string", "null"], "description": "", "enum": ["PICK_TO_WALL", "OPPORTUNITY_PICK"]}, "enableAutoGroup": {"type": ["boolean", "null"], "description": ""}, "orderPlanItemLineGroupBys": {"type": ["array", "null"], "items": {"type": "string", "enum": ["VLG", "CARRIER", "SHIP_TO"]}, "description": ""}, "defaultAssigneeUserId": {"type": ["string", "null"], "description": ""}, "orderIds": {"type": ["array", "null"], "items": {"type": "string"}, "description": ""}, "pickTaskIds": {"type": ["array", "null"], "items": {"type": "string"}, "description": ""}, "replenishmentTaskIds": {"type": ["array", "null"], "items": {"type": "string"}, "description": ""}, "status": {"type": ["string", "null"], "description": "", "enum": ["BUILDING", "PICK_SUGGESTED", "TASK_CREATED", "SCHEDULED", "RELEASED", "COMPLETED", "CANCELLED"]}, "customerId": {"type": ["string", "null"], "description": ""}, "orderDispatchSettingId": {"type": ["string", "null"], "description": ""}, "taskTags": {"type": ["array", "null"], "items": {"type": "string"}, "description": ""}, "taskPriority": {"type": ["string", "null"], "description": "", "enum": ["LOW", "MIDDLE", "HIGH", "TOP"]}, "maxOrderQtyPerPickTask": {"type": ["integer", "null"], "description": ""}, "maxItemLineQtyPerPickTask": {"type": ["integer", "null"], "description": ""}, "shippingRule": {"type": ["string", "null"], "description": "", "enum": ["FIFO", "LIFO", "FEFO", "LSFO", "LEFO"]}, "removePlanOrdersWithErrorAndMarkException": {"type": ["boolean", "null"], "description": ""}, "noNeedAutoRelease": {"type": ["boolean", "null"], "description": ""}, "createdBy": {"type": ["string", "null"], "description": ""}, "createdTime": {"type": ["string", "null"], "description": ""}, "updatedBy": {"type": ["string", "null"], "description": ""}, "updatedTime": {"type": ["string", "null"], "description": ""}, "createdByOrderDispatch": {"type": ["boolean", "null"], "description": ""}}}, "BAMLoadTaskDto": {"type": "object", "properties": {"createdTime": {"type": ["string", "null"], "description": ""}, "createdBy": {"type": ["string", "null"], "description": ""}, "updatedTime": {"type": ["string", "null"], "description": ""}, "updatedBy": {"type": ["string", "null"], "description": ""}, "id": {"type": ["string", "null"], "description": ""}, "status": {"type": ["string", "null"], "description": "", "enum": ["NEW", "LOADING", "LOADED", "SHIPPED", "CANCELLED"]}, "loadNo": {"type": ["string", "null"], "description": ""}, "customerId": {"type": ["string", "null"], "description": ""}, "customerName": {"type": ["string", "null"], "description": ""}, "carrierId": {"type": ["string", "null"], "description": ""}, "carrierName": {"type": ["string", "null"], "description": ""}, "proNo": {"type": ["string", "null"], "description": ""}, "freightTerm": {"type": ["string", "null"], "description": "", "enum": ["COLLECT", "PREPAID", "THIRD_PARTY"]}, "freightCost": {"type": ["number", "null"], "description": ""}, "loadType": {"type": ["string", "null"], "description": "", "enum": ["LTL", "TL", "TRANSLOAD"]}, "appointmentTime": {"type": ["string", "null"], "description": ""}, "masterBolNo": {"type": ["string", "null"], "description": ""}, "shipFrom": {"$ref": "#/components/schemas/AddressDto", "description": ""}, "shipTo": {"$ref": "#/components/schemas/AddressDto", "description": ""}, "note": {"type": ["string", "null"], "description": ""}, "startTime": {"type": ["string", "null"], "description": ""}, "endTime": {"type": ["string", "null"], "description": ""}, "sequence": {"type": ["integer", "null"], "description": ""}, "trailerNo": {"type": ["string", "null"], "description": ""}, "trailerPickUpMode": {"type": ["string", "null"], "description": "", "enum": ["DROP_LOAD", "LIVE_LOAD"]}, "loadPhotos": {"type": ["array", "null"], "items": {"type": "string"}, "description": ""}, "countingSheetPhotos": {"type": ["array", "null"], "items": {"type": "string"}, "description": ""}, "carrierSignatureFileId": {"type": ["string", "null"], "description": ""}, "shipperSignatureFileId": {"type": ["string", "null"], "description": ""}, "carrierSignatureTime": {"type": ["string", "null"], "description": ""}, "shipperSignatureTime": {"type": ["string", "null"], "description": ""}, "destination": {"type": ["string", "null"], "description": ""}, "orderLines": {"type": ["array", "null"], "items": {"$ref": "#/components/schemas/BAMOrderLineDTO", "description": ""}, "description": ""}, "sysNote": {"type": ["string", "null"], "description": ""}, "dockId": {"type": ["string", "null"], "description": ""}, "loadIds": {"type": ["array", "null"], "items": {"type": "string"}, "description": ""}, "exceptionCode": {"type": ["string", "null"], "description": ""}, "loadMode": {"type": ["string", "null"], "description": "", "enum": ["LIVE_LOAD", "PRE_LOAD"]}, "checkedIn": {"type": ["boolean", "null"], "description": ""}, "checkInTime": {"type": ["string", "null"], "description": ""}, "carrierPhotos": {"type": ["array", "null"], "items": {"type": "string"}, "description": ""}, "sealNo": {"type": ["string", "null"], "description": ""}, "sealPhotos": {"type": ["array", "null"], "items": {"type": "string"}, "description": ""}, "loadingPhotoIds": {"type": ["array", "null"], "items": {"type": "string"}, "description": ""}, "assigneeUserName": {"type": ["string", "null"], "description": ""}, "retailerName": {"type": ["string", "null"], "description": ""}}}, "BAMLoadQuery": {"type": "object", "properties": {"currentPage": {"type": ["integer", "null"], "description": ""}, "pageSize": {"type": ["integer", "null"], "description": ""}, "sortingFields": {"type": ["array", "null"], "items": {"$ref": "#/components/schemas/SortingField", "description": "com.item.xms.persistence.query.SortingField"}, "description": ""}, "status": {"type": ["string", "null"], "description": "", "enum": ["NEW", "LOADING", "LOADED", "SHIPPED", "CANCELLED"]}, "id": {"type": ["string", "null"], "description": ""}, "ids": {"type": ["array", "null"], "items": {"type": "string"}, "description": ""}, "statuses": {"type": ["array", "null"], "items": {"type": "string", "enum": ["NEW", "LOADING", "LOADED", "SHIPPED", "CANCELLED"]}, "description": ""}, "loadNo": {"type": ["string", "null"], "description": ""}, "customerId": {"type": ["string", "null"], "description": ""}, "carrierId": {"type": ["string", "null"], "description": ""}, "proNo": {"type": ["string", "null"], "description": ""}, "freightTerm": {"type": ["string", "null"], "description": "", "enum": ["COLLECT", "PREPAID", "THIRD_PARTY"]}, "loadType": {"type": ["string", "null"], "description": "", "enum": ["LTL", "TL", "TRANSLOAD"]}, "loadTypes": {"type": ["array", "null"], "items": {"type": "string", "enum": ["LTL", "TL", "TRANSLOAD"]}, "description": ""}, "masterBolNo": {"type": ["string", "null"], "description": ""}, "startTimePeriod": {"type": ["array", "null"], "items": {"type": "string"}, "description": ""}, "endTimePeriod": {"type": ["array", "null"], "items": {"type": "string"}, "description": ""}, "trailerNo": {"type": ["string", "null"], "description": ""}, "destination": {"type": ["string", "null"], "description": ""}, "orderId": {"type": ["string", "null"], "description": ""}}}, "R«PageResult«BAMLoadDto»»": {"type": "object", "properties": {"code": {"type": ["integer", "null"], "description": ""}, "msg": {"type": ["string", "null"], "description": ""}, "success": {"type": ["boolean", "null"], "description": ""}, "data": {"$ref": "#/components/schemas/PageResult%C2%ABBAMLoadDto%C2%BB", "description": ""}}}, "PageResult«BAMLoadDto»": {"type": "object", "properties": {"list": {"type": ["array", "null"], "items": {"$ref": "#/components/schemas/BAMLoadDto", "description": "com.item.wms.interfaces.bam.dtos.load.BAMLoadDto"}, "description": ""}, "totalCount": {"type": ["integer", "null"], "description": ""}, "currentPage": {"type": ["integer", "null"], "description": ""}, "pageSize": {"type": ["integer", "null"], "description": ""}, "totalPage": {"type": ["integer", "null"], "description": ""}}}, "PickTaskQuery": {"type": "object", "properties": {"currentPage": {"type": "integer", "description": ""}, "pageSize": {"type": "integer", "description": ""}, "sortingFields": {"type": "array", "items": {"$ref": "#/components/schemas/SortingField", "description": "com.item.xms.persistence.query.SortingField"}, "description": ""}, "id": {"type": "string", "description": ""}, "ids": {"type": "array", "items": {"type": "string"}, "description": ""}, "orderPlanId": {"type": "string", "description": ""}, "orderPlanIds": {"type": "array", "items": {"type": "string"}, "description": ""}, "pickType": {"type": "string", "description": ""}, "pickTypes": {"type": "array", "items": {"type": "string"}, "description": ""}, "pickMethod": {"type": "string", "description": ""}, "pickMethods": {"type": "array", "items": {"type": "string"}, "description": ""}, "taskHLPId": {"type": "string", "description": ""}, "taskHLPIds": {"type": "array", "items": {"type": "string"}, "description": ""}, "isRush": {"type": "boolean", "description": ""}, "skipCLP": {"type": "boolean", "description": ""}, "shippingRule": {"type": "string", "description": ""}, "shippingRules": {"type": "array", "items": {"type": "string"}, "description": ""}, "exceptionCode": {"type": "string", "description": ""}, "sysNote": {"type": "string", "description": ""}, "orderIds": {"type": "array", "items": {"type": "string"}, "description": ""}, "customerId": {"type": "string", "description": ""}, "customerIds": {"type": "array", "items": {"type": "string"}, "description": ""}, "status": {"type": "string", "description": ""}, "statuses": {"type": "array", "items": {"type": "string"}, "description": ""}, "note": {"type": "string", "description": ""}, "tags": {"type": "array", "items": {"type": "string"}, "description": ""}, "preAssigneeUserId": {"type": "string", "description": ""}, "preAssigneeUserIds": {"type": "array", "items": {"type": "string"}, "description": ""}, "assigneeUserId": {"type": "string", "description": ""}, "assigneeUserIds": {"type": "array", "items": {"type": "string"}, "description": ""}, "lastAssignedWhenFrom": {"type": "string", "description": ""}, "lastAssignedWhenTo": {"type": "string", "description": ""}, "priority": {"type": "string", "description": "", "enum": ["LOW", "MIDDLE", "HIGH", "TOP"]}, "startTimeFrom": {"type": "string", "description": ""}, "startTimeTo": {"type": "string", "description": ""}, "endTimeFrom": {"type": "string", "description": ""}, "endTimeTo": {"type": "string", "description": ""}, "pickMode": {"type": "string", "description": ""}, "withTaskSteps": {"type": "boolean", "description": ""}}}, "BAMLoadTaskQuery": {"type": "object", "properties": {"ids": {"type": ["array", "null"], "items": {"type": "string"}, "description": ""}, "excludeIds": {"type": ["array", "null"], "items": {"type": "string"}, "description": ""}, "loadIds": {"type": ["array", "null"], "items": {"type": "string"}, "description": ""}, "orderIds": {"type": ["array", "null"], "items": {"type": "string"}, "description": ""}, "customerIds": {"type": ["array", "null"], "items": {"type": "string"}, "description": ""}, "statuses": {"type": ["array", "null"], "items": {"type": "string", "enum": ["NEW", "IN_PROGRESS", "EXCEPTION", "CLOSE", "FORCE_CLOSE", "CANCELLED"]}, "description": ""}, "assigneeUserIds": {"type": ["array", "null"], "items": {"type": "string"}, "description": ""}, "dockId": {"type": ["integer", "null"], "description": ""}, "priority": {"type": ["string", "null"], "description": ""}, "startTimeFrom": {"type": ["string", "null"], "description": ""}, "startTimeTo": {"type": ["string", "null"], "description": ""}, "endTimeFrom": {"type": ["string", "null"], "description": ""}, "endTimeTo": {"type": ["string", "null"], "description": ""}, "withTaskSteps": {"type": ["boolean", "null"], "description": ""}}}, "R«PageResult«BAMLoadTaskDto»»": {"type": "object", "properties": {"code": {"type": ["integer", "null"], "description": ""}, "msg": {"type": ["string", "null"], "description": ""}, "success": {"type": ["boolean", "null"], "description": ""}, "data": {"$ref": "#/components/schemas/PageResult%C2%ABBAMLoadTaskDto%C2%BB", "description": ""}}}, "PageResult«BAMLoadTaskDto»": {"type": "object", "properties": {"list": {"type": ["array", "null"], "items": {"$ref": "#/components/schemas/BAMLoadTaskDto", "description": ""}, "description": ""}, "totalCount": {"type": ["integer", "null"], "description": ""}, "currentPage": {"type": ["integer", "null"], "description": ""}, "pageSize": {"type": ["integer", "null"], "description": ""}, "totalPage": {"type": ["integer", "null"], "description": ""}}}}, "securitySchemes": {}}, "servers": []}