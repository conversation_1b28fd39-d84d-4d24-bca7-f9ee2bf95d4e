'use client';

import React, { useState } from 'react';
import '@/styles/item-design-system.css';

// 天气组件属性
interface WeatherCardProps {
  toolInvocation?: any;
  // 直接传递的属性
  location?: string;
  temperature?: number;
  condition?: string;
  humidity?: number;
  windSpeed?: number;
  unit?: string;
  lastUpdated?: string;
}

export default function WeatherCard({
  toolInvocation,
  location: propLocation,
  temperature: propTemperature,
  condition: propCondition,
  humidity: propHumidity,
  windSpeed: propWindSpeed,
  unit: propUnit,
  lastUpdated: propLastUpdated
}: WeatherCardProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  // 如果存在工具调用，从中提取数据
  let location, temperature, condition, humidity, windSpeed, unit, lastUpdated;

  if (toolInvocation) {
    // 只记录必要的信息，避免打印整个对象
   
    // 根据工具调用状态获取不同的数据
    if (toolInvocation.state === 'result') {
      // 完整结果
      location = toolInvocation.args?.location;
      temperature = toolInvocation.result?.temperature;
      condition = toolInvocation.result?.condition;

      // 检查humidity是数字还是已格式化的字符串
      if (typeof toolInvocation.result?.humidity === 'string' && toolInvocation.result?.humidity.includes('%')) {
        humidity = toolInvocation.result?.humidity; // 直接使用已格式化的字符串
      } else {
        humidity = toolInvocation.result?.humidity; // 使用数字
      }

      // 检查windSpeed是数字还是已格式化的字符串
      if (typeof toolInvocation.result?.windSpeed === 'string' && toolInvocation.result?.windSpeed.includes('km/h')) {
        windSpeed = toolInvocation.result?.windSpeed; // 直接使用已格式化的字符串
      } else {
        windSpeed = toolInvocation.result?.windSpeed; // 使用数字
      }

      unit = toolInvocation.result?.unit;
      lastUpdated = toolInvocation.result?.lastUpdated;
    } else if (toolInvocation.state === 'call') {
      // 仅请求参数
      location = toolInvocation.args?.location;
    } else {
      // 部分调用
      location = 'Loading...';
    }
  } else {
    // 使用直接传递的属性
    location = propLocation;
    temperature = propTemperature;
    condition = propCondition;
    humidity = propHumidity;
    windSpeed = propWindSpeed;
    unit = propUnit;
    lastUpdated = propLastUpdated;
  }

  // 天气图标映射
  const weatherIcons: Record<string, React.ReactNode> = {
    'sunny': (
      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" className="text-yellow-300 w-10 h-10">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
      </svg>
    ),
    'cloudy': (
      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" className="text-gray-300 w-10 h-10">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M3 15a4 4 0 004 4h9a5 5 0 10-.1-9.999 5.002 5.002 0 10-9.78 2.096A4.001 4.001 0 003 15z" />
      </svg>
    ),
    'rainy': (
      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" className="text-blue-300 w-10 h-10">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M19 13.5a4 4 0 11-8 0 4 4 0 018 0zm-18 0a4 4 0 11-8 0 4 4 0 018 0z" />
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M3 19.5L7 15M9 19.5l4-4m4 0l4 4" />
      </svg>
    ),
    'stormy': (
      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" className="text-purple-300 w-10 h-10">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M17.73 12.91L12 22.28V14h-1a1 1 0 01-1-1V7a1 1 0 011-1h6a1 1 0 01.77 1.64l-5 6.36z" />
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M20 16.64A6 6 0 0018 7h-1.264a6 6 0 00-11.521 0H4a6 6 0 00-1 11.9" />
      </svg>
    ),
    'snowy': (
      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" className="text-blue-100 w-10 h-10">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M5 12m5 7h4M5 12a7 7 0 1114 0 7 7 0 01-14 0z" />
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 9v6m0 0l-3-3m3 3l3-3" />
      </svg>
    ),
    'foggy': (
      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" className="text-gray-400 w-10 h-10">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M3 8h.007M7 8h.007M11 8h.007M15 8h.007M19 8h.007M3 12h.007M7 12h10M19 12h.007M3 16h10M15 16h.007M19 16h.007" />
      </svg>
    ),
    'default': (
      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" className="text-gray-400 w-10 h-10">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
      </svg>
    )
  };

  // 根据天气状况选择合适的图标
  const getWeatherIcon = (condition: string | undefined) => {
    if (!condition) return weatherIcons.default;

    const lowerCaseCondition = condition.toLowerCase();

    if (lowerCaseCondition.includes('sun') || lowerCaseCondition.includes('clear')) return weatherIcons.sunny;
    if (lowerCaseCondition.includes('cloud')) return weatherIcons.cloudy;
    if (lowerCaseCondition.includes('rain')) return weatherIcons.rainy;
    if (lowerCaseCondition.includes('storm') || lowerCaseCondition.includes('thunder')) return weatherIcons.stormy;
    if (lowerCaseCondition.includes('snow')) return weatherIcons.snowy;
    if (lowerCaseCondition.includes('fog') || lowerCaseCondition.includes('mist')) return weatherIcons.foggy;

    return weatherIcons.default;
  };

  // 工具调用处理中
  if (toolInvocation && toolInvocation.state !== 'result') {
  return (
      <div className="item-card bg-item-bg-card rounded-lg border border-item-gray-800 p-2 text-white shadow-lg my-2 ">
        <div className="flex items-center mb-1.5">
          <div className="animate-pulse mr-2.5">
            <div className="rounded-full bg-item-gray-700 h-6 w-6"></div>
          </div>
          <div className="flex-1">
            <div className="h-3 bg-item-gray-700 rounded w-3/4 mb-1.5 animate-pulse"></div>
            <div className="h-2 bg-item-bg-card rounded w-1/2 animate-pulse"></div>
          </div>
        </div>
        <div className="text-sm text-item-gray-400-light font-medium">
          正在获取 {location} 的天气信息...
        </div>
      </div>
    );
  }

  // 折叠视图
  if (!isExpanded) {
    return (
      <div
        className="item-card bg-item-bg-card rounded-lg border border-item-gray-800 p-3 text-white shadow-lg cursor-pointer my-2 hover:border-item-gray-700 hover:shadow-xl transition-all duration-200 "
        onClick={() => setIsExpanded(true)}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div className="w-8 h-8 flex items-center justify-center bg-item-bg-hover rounded-lg">
              {React.cloneElement(getWeatherIcon(condition) as React.ReactElement, {
                className: "w-5 h-5"
              })}
            </div>
            <div className="ml-2.5">
              <h3 className="font-medium text-sm text-item-gray-500">{location}</h3>
              <div className="text-base font-medium text-item-gray-500">
                {temperature !== undefined ? `${temperature}°${unit || 'C'}` : ''}
              </div>
            </div>
          </div>
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-4 h-4 text-item-gray-400 transition-transform duration-200">
            <path strokeLinecap="round" strokeLinejoin="round" d="M19.5 8.25l-7.5 7.5-7.5-7.5" />
          </svg>
        </div>
      </div>
    );
  }

  // 完整视图
  return (
    <div className="item-card bg-item-bg-card rounded-lg border border-item-gray-800 p-2 text-white shadow-lg my-2 ">
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center">
          <div className="w-8 h-8 flex items-center justify-center bg-item-bg-hover rounded-lg">
            {React.cloneElement(getWeatherIcon(condition) as React.ReactElement, {
              className: "w-5 h-5"
            })}
          </div>
          <div className="ml-2.5">
            <h3 className="font-semibold text-sm text-white">{location}</h3>
            <div className="text-base font-bold text-item-gray-400-light">
              {temperature !== undefined ? `${temperature}°${unit || 'C'}` : ''}
            </div>
          </div>
        </div>
        <button
          className="text-item-gray-400 hover:text-item-gray-400-light hover:bg-item-bg-hover p-1.5 rounded-lg transition-all duration-200"
          onClick={() => setIsExpanded(false)}
        >
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-4 h-4">
            <path strokeLinecap="round" strokeLinejoin="round" d="M4.5 15.75l7.5-7.5 7.5 7.5" />
          </svg>
        </button>
      </div>

      <div className="bg-item-bg-hover/50 rounded-lg p-2 grid grid-cols-2 gap-y-1.5 text-sm">
        <div>
          <span className="text-item-gray-400 font-medium">状况:</span> <span className="text-white">{condition || '--'}</span>
        </div>
        <div>
          <span className="text-item-gray-400 font-medium">湿度:</span> <span className="text-white">{humidity ? (typeof humidity === 'string' && humidity.includes('%') ? humidity : `${humidity}%`) : '--'}</span>
        </div>
        <div>
          <span className="text-item-gray-400 font-medium">风速:</span> <span className="text-white">{windSpeed ? (typeof windSpeed === 'string' && windSpeed.includes('km/h') ? windSpeed : `${windSpeed} km/h`) : '--'}</span>
        </div>
        <div>
          <span className="text-item-gray-400 font-medium">更新时间:</span> <span className="text-white">{lastUpdated || '--'}</span>
        </div>
      </div>

      <div className="text-xs text-item-gray-400 mt-2.5 text-right font-medium">
        数据仅供参考
      </div>
    </div>
  );
}