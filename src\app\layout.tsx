import './globals.css';
import '../styles/calendar-override.css';
import type { Metadata } from 'next';

import { AuthProvider } from './contexts/AuthContext';
import { ChatProvider } from './contexts/ChatContext';
import { ErrorProvider } from './contexts/ErrorContext';
import { SuccessProvider } from './contexts/SuccessContext';
import ErrorDisplay from './components/ErrorDisplay';
import SuccessDisplay from './components/SuccessDisplay';

export const metadata: Metadata = {
  title: 'Atlas (ITEM AI Chatbot) ',
  description: 'An intelligent conversational system based on Next.js and multiple AI models, with MCP tool extension support.',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="zh-CN">
      <head>
        <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
      </head>
      <body>
        <ErrorProvider>
          <SuccessProvider>
            <AuthProvider>
              <ChatProvider>
                <ErrorDisplay />
                <SuccessDisplay />
                {children}
              </ChatProvider>
            </AuthProvider>
          </SuccessProvider>
        </ErrorProvider>
      </body>
    </html>
  );
}