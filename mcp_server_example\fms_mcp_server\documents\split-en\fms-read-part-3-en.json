{"openapi": "3.0.1", "info": {"title": "FMS", "description": "", "version": "1.0.0"}, "paths": {"/fms-platform-order/shipment-order-task-allocated-revenue/query": {"post": {"summary": "foreign\r\nGet the Allocated Revenue information of Task", "deprecated": false, "description": "", "tags": ["ShipmentOrders"], "parameters": [], "requestBody": {"content": {"application/json-patch+json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/RpcOrderTaskAllocatedRevenueQueryDto"}}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/RpcOrderTaskAllocatedRevenueQueryResultDto"}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order/shipment-orders/get-transitdays": {"get": {"summary": "internal\r\nGet the number of transfer days", "deprecated": false, "description": "", "tags": ["ShipmentOrders"], "parameters": [{"name": "fromTerminalCode", "in": "query", "description": "Starting terminal code", "required": false, "schema": {"type": "string"}}, {"name": "toTerminalCode", "in": "query", "description": "Purpose terminal code", "required": false, "schema": {"type": "string"}}, {"name": "orderNo", "in": "query", "description": "", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "string"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order/shipment-orders/getvendorbasiccompanyinfo/{searchText}": {"get": {"summary": "Call external interface, internal use\r\nCall the VRM interface to query Vendor information", "deprecated": false, "description": "", "tags": ["ShipmentOrders"], "parameters": [{"name": "searchText", "in": "path", "description": "Search for text", "required": true, "example": "", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/VendorBasicCompanyInfoResultDto"}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order/shipment-orders/query-woappointment-bydo/{orderNo}": {"get": {"summary": "Query whether the associated WO under DO has an appointment record, and True returns the required pop-up box", "deprecated": false, "description": "", "tags": ["ShipmentOrders"], "parameters": [{"name": "orderNo", "in": "path", "description": "Order number", "required": true, "example": "", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "boolean"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order/billing-information/getbillinginformationList": {"get": {"summary": "internal\r\nQuery bill info", "deprecated": false, "description": "", "tags": ["ShipmentOrders"], "parameters": [], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/Bill<PERSON>earchDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BillingInformationDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order/billing-information/getbillinginformationbyid": {"get": {"summary": "GetBillingInformatioByOrderId", "deprecated": false, "description": "", "tags": ["ShipmentOrders"], "parameters": [{"name": "order_no", "in": "query", "description": "Order number", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BillingInformationDto"}}}, "headers": {}}}, "security": [{"apikey-header-fms-token": []}]}}, "/fms-platform-order/freight-commodity/getfreightcommoditylist": {"get": {"summary": "internal\r\nQuery Item information", "deprecated": false, "description": "", "tags": ["ShipmentOrders"], "parameters": [{"name": "order_no", "in": "query", "description": "Order number", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/FreightCommodityLineDto"}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order/shipment-orders/shipper-terminal/all": {"get": {"summary": "Shipper Terminal drop-down list of Shipment Order list page Data query interface", "deprecated": false, "description": "", "tags": ["ShipmentOrders"], "parameters": [{"name": "Ids", "in": "query", "description": "", "required": false, "schema": {"uniqueItems": true, "type": "array", "items": {"type": "integer", "format": "int64"}}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StringFmsSelectListItemListResultDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order/test-location/searLocation/{keyWord}": {"get": {"summary": "internal\r\nEncapsulated three-party interface, fuzzy search location", "deprecated": false, "description": "", "tags": ["ShipmentOrders"], "parameters": [{"name": "key<PERSON>ord", "in": "path", "description": "Keywords", "required": true, "example": "", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/SearLocationRequest"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order/shipment-orders/status/all": {"get": {"summary": "Shipment Order list page Status drop-down list Data query interface", "deprecated": false, "description": "", "tags": ["ShipmentOrders"], "parameters": [], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringFmsSelectListItemWithChildrenListResultDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order/shipment-orders/shipment-types/all": {"get": {"summary": "Shipment Type drop-down list of Shipment Order list page Data query interface", "deprecated": false, "description": "", "tags": ["ShipmentOrders"], "parameters": [], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringFmsSelectListItemListResultDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order/shipment-orders/shipment-types/with-service-level": {"get": {"summary": "Shipment Order list page Service Level drop-down list Data query interface", "deprecated": false, "description": "", "tags": ["ShipmentOrders"], "parameters": [], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StringFmsSelectListItemWithChildren"}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order/shipment-orders/service-level/all": {"get": {"summary": "Shipment Order list page Service Level drop-down list Data query interface", "deprecated": false, "description": "", "tags": ["ShipmentOrders"], "parameters": [], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringFmsSelectListItemListResultDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order/shipper/getshipment-orderbasic/{shipmentOrderNo}": {"get": {"summary": "GetOrderBasic", "deprecated": false, "description": "Query the general information of the order, enter the parameter do#, and exit the parameter include <PERSON><PERSON>, Consignee information, billing information, and Freight Commodity information.", "tags": ["ShipmentOrderShipper"], "parameters": [{"name": "shipmentOrderNo", "in": "path", "description": "Order number", "required": true, "example": "", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ShipmentOrderBasicInfoDto"}}}, "headers": {}}}, "security": [{"apikey-header-fms-token": []}]}}, "/fms-platform-order/shipper/{shipmentOrderNo}": {"get": {"summary": "Internal use\r\nQuery single shipper information", "deprecated": false, "description": "", "tags": ["ShipmentOrderShipper"], "parameters": [{"name": "shipmentOrderNo", "in": "path", "description": "Order number", "required": true, "example": "", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ShipmentOrderShipperDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order/shipper/getshipper-detail/{shipperId}": {"get": {"summary": "Internal use\r\nQuery Shipper details (modify page data)", "deprecated": false, "description": "", "tags": ["ShipmentOrderShipper"], "parameters": [{"name": "shipperId", "in": "path", "description": "Shipper primary key ID", "required": true, "example": 0, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ShipmentOrderShipperDetailDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order/shipper/appointment/shipmentorderNo={shipmentOrderNo}&taskNo={taskNo}": {"get": {"summary": "View Shipper-Appointment info", "deprecated": false, "description": "", "tags": ["ShipmentOrderShipper"], "parameters": [{"name": "shipmentOrderNo", "in": "path", "description": "", "required": true, "example": "", "schema": {"type": "string"}}, {"name": "taskNo", "in": "path", "description": "", "required": true, "example": 0, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ShipmentOrderShipperAppointmentDtoNew"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order/shipper/getshipment-orderbasic-headinfo/{shipmentOrderNo}": {"get": {"summary": "Internal use\r\nDetails page to check order status", "deprecated": false, "description": "", "tags": ["ShipmentOrderShipper"], "parameters": [{"name": "shipmentOrderNo", "in": "path", "description": "Order number", "required": true, "example": "", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ShipmentOrderBasicHeadInfoDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order/shipper/appointment_exist_bytaskno/{taskNo}": {"get": {"summary": "/fms-platform-order/shipper/appointment_exist_bytaskno/{taskNo}", "deprecated": false, "description": "", "tags": ["ShipmentOrderShipper"], "parameters": [{"name": "taskNo", "in": "path", "description": "", "required": true, "example": "", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "boolean"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order/status/shipment/order-relation": {"get": {"summary": "Query the relationship between DO WO", "deprecated": false, "description": "", "tags": ["ShipmentOrderStatus"], "parameters": [{"name": "order_no", "in": "query", "description": "Order number", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "boolean"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}}, "components": {"schemas": {"AppointmentCarrierInfo": {"type": "object", "properties": {"contractor_or_carrier_name": {"type": "string", "nullable": true}, "contractor_or_carrier_code": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Contacts": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "contact_type": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "phone": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BillSearchDto": {"type": "object", "properties": {"bill_to_search": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BillingInformationDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "freight_term": {"type": "string", "nullable": true}, "bill_to_account_id": {"type": "string", "nullable": true}, "bill_to_name": {"type": "string", "nullable": true}, "address1": {"type": "string", "nullable": true}, "address2": {"type": "string", "nullable": true}, "city": {"type": "string", "nullable": true}, "state": {"type": "string", "nullable": true}, "zipcode": {"type": "string", "nullable": true}, "quote_id": {"type": "string", "nullable": true}, "quote_amount": {"type": "number", "format": "double", "nullable": true}, "primary_contact": {"type": "string", "nullable": true}, "phone": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "bill_note": {"type": "string", "nullable": true}, "last_update_name": {"type": "string", "nullable": true}, "last_update_time": {"type": "string", "format": "date-time", "nullable": true}, "name": {"type": "string", "nullable": true}, "revenue_code": {"type": "string", "nullable": true}, "business_client": {"type": "string", "nullable": true}}, "additionalProperties": false}, "FreightCommodityLineDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "code": {"type": "string", "nullable": true}, "commodity_description": {"type": "string", "nullable": true}, "quantity": {"type": "integer", "format": "int32", "nullable": true}, "uom": {"type": "string", "nullable": true}, "weight": {"type": "number", "format": "double", "nullable": true}, "freight_class": {"type": "number", "format": "double"}, "stackable": {"type": "integer", "format": "int32", "nullable": true}, "dimension_l": {"type": "number", "format": "double", "nullable": true}, "dimension_w": {"type": "number", "format": "double", "nullable": true}, "dimension_h": {"type": "number", "format": "double", "nullable": true}, "nmfc": {"type": "string", "nullable": true}, "declared_value": {"type": "number", "format": "double", "nullable": true}, "customer_pro": {"type": "string", "nullable": true}, "operation_type": {"type": "integer", "format": "int32", "nullable": true}, "shipment_order_no": {"type": "string", "nullable": true}, "volume": {"type": "number", "format": "double", "nullable": true}, "volume_uom": {"type": "string", "nullable": true}, "weight_uom": {"type": "string", "nullable": true}, "linear_uom": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ServiceLevelEnum": {"enum": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 30], "type": "integer", "format": "int32"}, "ShipmentTypeEnum": {"enum": [1, 2, 3, 4], "type": "integer", "format": "int32"}, "ResultsItem": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "address": {"$ref": "#/components/schemas/SearAddress"}, "created_at": {"type": "string", "nullable": true}, "updated_at": {"type": "string", "nullable": true}, "deleted_at": {"type": "integer", "format": "int32"}, "type": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "open_time": {"type": "string", "nullable": true}, "close_time": {"type": "string", "nullable": true}, "contacts": {"type": "array", "items": {"$ref": "#/components/schemas/Contacts"}, "nullable": true}}, "additionalProperties": false}, "RpcOrderTaskAllocatedRevenueQueryDto": {"type": "object", "properties": {"order_no": {"type": "string", "nullable": true}, "order_key": {"type": "integer", "format": "int64"}, "task_no": {"type": "integer", "format": "int64"}, "task_type": {"type": "integer", "format": "int32"}, "task_type_name": {"type": "string", "nullable": true}}, "additionalProperties": false}, "RpcOrderTaskAllocatedRevenueQueryResultDto": {"type": "object", "properties": {"order_no": {"type": "string", "nullable": true}, "order_key": {"type": "integer", "format": "int64"}, "task_no": {"type": "integer", "format": "int64"}, "task_type": {"type": "integer", "format": "int32"}, "task_type_name": {"type": "string", "nullable": true}, "revenue_amount": {"type": "number", "format": "double"}, "allocated_revenue_amount": {"type": "number", "format": "double"}, "allocated_revenue_time": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "SearAddress": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "created_at": {"type": "string", "nullable": true}, "updated_at": {"type": "string", "nullable": true}, "deleted_at": {"type": "integer", "format": "int32"}, "location_type": {"type": "integer", "format": "int32"}, "country": {"type": "string", "nullable": true}, "street": {"type": "string", "nullable": true}, "zip_code": {"type": "string", "nullable": true}, "address1": {"type": "string", "nullable": true}, "address2": {"type": "string", "nullable": true}, "address3": {"type": "string", "nullable": true}, "address4": {"type": "string", "nullable": true}, "lon": {"type": "string", "nullable": true}, "lat": {"type": "string", "nullable": true}, "city": {"type": "string", "nullable": true}, "state": {"type": "string", "nullable": true}, "aka": {"type": "string", "nullable": true}, "company_group_id": {"type": "integer", "format": "int32"}, "is_validate": {"type": "integer", "format": "int32"}, "zipcode": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SearLocationRequest": {"type": "object", "properties": {"total": {"type": "integer", "format": "int32"}, "results": {"type": "array", "items": {"$ref": "#/components/schemas/ResultsItem"}, "nullable": true}}, "additionalProperties": false}, "ShipmentOrderBasicHeadInfoDto": {"type": "object", "properties": {"tracking_no": {"type": "string", "nullable": true}, "pro_no": {"type": "string", "nullable": true}, "invoice_pro": {"type": "string", "nullable": true}, "shipper_order_no": {"type": "string", "nullable": true}, "order_status": {"type": "integer", "format": "int32"}, "order_status_describe": {"type": "string", "nullable": true}, "is_hold": {"type": "integer", "format": "int32"}, "order_sub_status": {"type": "integer", "format": "int32"}, "order_sub_status_describe": {"type": "string", "nullable": true}, "shipmenttype_is_update": {"type": "boolean"}, "lock": {"type": "boolean"}, "show_unlock": {"type": "boolean"}, "hold_reason": {"type": "string", "nullable": true}, "tms_order_stage": {"type": "string", "nullable": true}, "tms_order_status": {"type": "string", "nullable": true}, "tms_order_stage_txt": {"type": "string", "nullable": true}, "tms_order_status_txt": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StringFmsSelectListItemWithChildren": {"type": "object", "properties": {"children": {"type": "array", "items": {"$ref": "#/components/schemas/StringFmsSelectListItemWithChildren"}, "nullable": true}, "text": {"type": "string", "nullable": true}, "value": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StringFmsSelectListItemWithChildrenListResultDto": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/StringFmsSelectListItemWithChildren"}, "nullable": true}}, "additionalProperties": false}, "ShipmentOrderBasicInfoDto": {"type": "object", "properties": {"shipment_id": {"type": "integer", "format": "int64"}, "shipper_terminal": {"type": "string", "nullable": true}, "consignee_terminal": {"type": "string", "nullable": true}, "current_transit_org_terminal": {"type": "string", "nullable": true}, "current_transit_dest_terminal": {"type": "string", "nullable": true}, "service_terminal": {"type": "string", "nullable": true}, "current_location": {"type": "string", "nullable": true}, "shipment_type": {"$ref": "#/components/schemas/ShipmentTypeEnum"}, "service_level": {"$ref": "#/components/schemas/ServiceLevelEnum"}, "reference": {"type": "string", "nullable": true}, "load_no": {"type": "string", "nullable": true}, "sales_order": {"type": "string", "nullable": true}, "bol": {"type": "string", "nullable": true}, "purchase_order": {"type": "string", "nullable": true}, "master_order": {"type": "string", "nullable": true}, "trip_original_terminal": {"type": "string", "nullable": true}, "trip_dest_terminal": {"type": "string", "nullable": true}, "consignee_dto": {"$ref": "#/components/schemas/ShipmentOrderConsigneeDto"}, "shipper_dto": {"$ref": "#/components/schemas/ShipmentOrderShipperDto"}, "reference2": {"type": "string", "nullable": true}, "reference3": {"type": "string", "nullable": true}, "reference4": {"type": "string", "nullable": true}, "reference5": {"type": "string", "nullable": true}, "mabd": {"type": "string", "nullable": true}, "invoice_pro_prefix": {"type": "string", "nullable": true}, "expected_transit_days": {"type": "number", "format": "double", "nullable": true}, "actual_transit_days": {"type": "integer", "format": "int32"}, "oms_do_no": {"type": "string", "nullable": true}, "tracking_no": {"type": "string", "nullable": true}, "pro_no": {"type": "string", "nullable": true}, "invoice_pro": {"type": "string", "nullable": true}, "shipper_order_no": {"type": "string", "nullable": true}, "order_status": {"type": "integer", "format": "int32"}, "order_status_describe": {"type": "string", "nullable": true}, "is_hold": {"type": "integer", "format": "int32"}, "order_sub_status": {"type": "integer", "format": "int32"}, "order_sub_status_describe": {"type": "string", "nullable": true}, "shipmenttype_is_update": {"type": "boolean"}, "lock": {"type": "boolean"}, "show_unlock": {"type": "boolean"}, "hold_reason": {"type": "string", "nullable": true}, "tms_order_stage": {"type": "string", "nullable": true}, "tms_order_status": {"type": "string", "nullable": true}, "tms_order_stage_txt": {"type": "string", "nullable": true}, "tms_order_status_txt": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ShipmentOrderConsigneeDto": {"type": "object", "properties": {"consignee": {"type": "string", "nullable": true}, "delivery_appointment": {"type": "string", "format": "date-time", "nullable": true}, "delivered_time": {"type": "string", "format": "date-time", "nullable": true}, "transit_days": {"type": "number", "format": "double"}, "name": {"type": "string", "nullable": true}, "contact_id": {"type": "integer", "format": "int64"}, "location_code": {"type": "integer", "format": "int32"}, "address": {"type": "string", "nullable": true}, "contact": {"type": "string", "nullable": true}, "phone": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "desired_delivery_date": {"type": "string", "nullable": true}, "mabd": {"type": "string", "format": "date-time", "nullable": true}, "appointment_date": {"type": "string", "nullable": true}, "appointment_from": {"type": "string", "nullable": true}, "appointment_to": {"type": "string", "nullable": true}, "consignee_note": {"type": "string", "nullable": true}, "consignee_id": {"type": "integer", "format": "int64"}, "order_status": {"type": "integer", "format": "int32"}, "appointment_status": {"type": "integer", "format": "int32"}, "open_time": {"type": "string", "nullable": true}, "close_time": {"type": "string", "nullable": true}, "is_new": {"type": "boolean"}, "task_no": {"type": "integer", "format": "int64"}, "mabd_from": {"type": "string", "nullable": true}, "mabd_to": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ShipmentOrderShipperAppointmentDtoNew": {"type": "object", "properties": {"tracking_no": {"type": "string", "nullable": true}, "shipment_order_no": {"type": "string", "nullable": true}, "appointment_time_from": {"type": "string", "nullable": true}, "appointment_time_to": {"type": "string", "nullable": true}, "appointment_date": {"type": "string", "format": "date-time", "nullable": true}, "confirmed_by_shipper": {"type": "integer", "format": "int32"}, "confirmed_by_contractor": {"type": "integer", "format": "int32"}, "open_time": {"type": "string", "nullable": true}, "close_time": {"type": "string", "nullable": true}, "shipper_name": {"type": "string", "nullable": true}, "address": {"type": "string", "nullable": true}, "request_pickup_date": {"type": "string", "format": "date-time", "nullable": true}, "reference": {"type": "string", "nullable": true}, "sales_order": {"type": "string", "nullable": true}, "purchase_order": {"type": "string", "nullable": true}, "load": {"type": "string", "nullable": true}, "bol": {"type": "string", "nullable": true}, "master_order": {"type": "string", "nullable": true}, "work_order": {"type": "string", "nullable": true}, "total_freight": {"type": "string", "nullable": true}, "order_status": {"type": "string", "nullable": true}, "appointtment_status": {"$ref": "#/components/schemas/ShipmentOrderShipperAppointmentStatusDto"}, "shipment_order_appointment_status": {"type": "integer", "format": "int32"}, "carriers": {"type": "array", "items": {"$ref": "#/components/schemas/AppointmentCarrierInfo"}, "nullable": true}, "shipment_order_id": {"type": "integer", "format": "int64"}, "tripno_or_taskno": {"type": "integer", "format": "int64"}}, "additionalProperties": false}, "ShipmentOrderShipperAppointmentStatusDto": {"type": "object", "properties": {"appointment_id": {"type": "integer", "format": "int64"}, "create_time": {"type": "string", "format": "date-time", "nullable": true}, "order_status": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "ShipmentOrderShipperDetailDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "name": {"type": "string", "nullable": true}, "address1": {"type": "string", "nullable": true}, "address2": {"type": "string", "nullable": true}, "city": {"type": "string", "nullable": true}, "state": {"type": "string", "nullable": true}, "zip_code": {"type": "string", "nullable": true}, "country": {"type": "string", "nullable": true}, "longitude": {"type": "number", "format": "double"}, "latitude": {"type": "number", "format": "double"}, "open_time": {"type": "string", "nullable": true}, "close_time": {"type": "string", "nullable": true}, "primary_contact_name": {"type": "string", "nullable": true}, "primary_contact_phone": {"type": "string", "nullable": true}, "primary_contact_email": {"type": "string", "nullable": true}, "secondary_contact_name": {"type": "string", "nullable": true}, "secondary_contact_phone": {"type": "string", "nullable": true}, "secondary_contact_email": {"type": "string", "nullable": true}, "note": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ShipmentOrderShipperDto": {"type": "object", "properties": {"shipper": {"type": "string", "nullable": true}, "pickup_appointment": {"type": "string", "format": "date-time", "nullable": true}, "time_from": {"type": "string", "format": "date-time", "nullable": true}, "time_to": {"type": "string", "format": "date-time", "nullable": true}, "pickup_completed_time": {"type": "string", "format": "date-time", "nullable": true}, "pick_appointment_date": {"type": "string", "format": "date-time", "nullable": true}, "order_created_time": {"type": "string", "format": "date-time", "nullable": true}, "name": {"type": "string", "nullable": true}, "contact_id": {"type": "integer", "format": "int64"}, "location_code": {"type": "integer", "format": "int32"}, "address": {"type": "string", "nullable": true}, "contact": {"type": "string", "nullable": true}, "phone": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "request_pickup_date": {"type": "string", "nullable": true}, "shipper_note": {"type": "string", "nullable": true}, "shipper_id": {"type": "integer", "format": "int64"}, "appointment_from": {"type": "string", "nullable": true}, "appointment_to": {"type": "string", "nullable": true}, "appointment_date": {"type": "string", "nullable": true}, "order_status": {"type": "integer", "format": "int32"}, "appointment_status": {"type": "integer", "format": "int32"}, "open_time": {"type": "string", "nullable": true}, "close_time": {"type": "string", "nullable": true}, "is_new": {"type": "boolean"}, "task_no": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StringFmsSelectListItem": {"type": "object", "properties": {"text": {"type": "string", "nullable": true}, "value": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StringFmsSelectListItemListResultDto": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/StringFmsSelectListItem"}, "nullable": true}}, "additionalProperties": false}, "VendorBasicCompanyInfoResultDto": {"type": "object", "properties": {"result_content": {"type": "string", "nullable": true}, "type": {"type": "integer", "format": "int32"}}, "additionalProperties": false}}}}