{"openapi": "3.0.1", "info": {"title": "WES", "description": "", "version": "1.0.0"}, "tags": [], "paths": {"/wms-bam/outbound/pick/pick-suggest/search": {"post": {"summary": "Search Pick Suggests", "deprecated": false, "description": "", "tags": [], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Search", "description": ""}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/R%C2%ABList%C2%ABPickSuggest%C2%BB%C2%BB", "description": ""}, "examples": {"1": {"summary": "成功示例", "value": {"code": 0, "msg": "", "success": false, "data": [{"id": 0, "orderPlanId": "", "orderId": "", "itemId": "", "taskId": "", "lotNo": "", "titleId": "", "locationId": "", "lpId": "", "qty": 0, "baseQty": 0, "uomId": "", "pickType": "", "status": "", "cancelReason": "", "goodsType": ""}]}}}}}, "headers": {}}}, "security": []}}}, "components": {"schemas": {"Search": {"type": "object", "properties": {"id": {"type": "string", "description": "", "nullable": true}, "ids": {"type": "array", "items": {"type": "string"}, "description": "", "nullable": true}, "orderPlanId": {"type": "string", "description": "", "nullable": true}, "taskId": {"type": "string", "description": "", "nullable": true}, "itemId": {"type": "string", "description": "", "nullable": true}, "locationId": {"type": "string", "description": "", "nullable": true}, "statuses": {"type": "array", "items": {"type": "string", "enum": ["PENDING", "CANCELLED", "PART_CLOSED", "CLOSED", "ERROR", "REPLENISHMENT"]}, "description": "", "default": "List.of(PickStrategyStatus.PENDING)", "nullable": true}}}, "PickSuggest": {"type": "object", "properties": {"id": {"type": "integer", "description": "", "nullable": true}, "orderPlanId": {"type": "string", "description": "", "nullable": true}, "orderId": {"type": "string", "description": "", "nullable": true}, "itemId": {"type": "string", "description": "", "nullable": true}, "taskId": {"type": "string", "description": "", "nullable": true}, "lotNo": {"type": "string", "description": "", "nullable": true}, "titleId": {"type": "string", "description": "", "nullable": true}, "locationId": {"type": "string", "description": "", "nullable": true}, "lpId": {"type": "string", "description": "", "nullable": true}, "qty": {"type": "number", "description": "", "nullable": true}, "baseQty": {"type": "number", "description": "", "nullable": true}, "uomId": {"type": "string", "description": "", "nullable": true}, "pickType": {"type": "string", "description": "", "enum": ["BULK_PICK", "PIECE_PICK", "CASE_PICK", "PALLET_PICK", "PARTIAL_PALLET", "PARTIAL_CASE", "NONE"], "nullable": true}, "status": {"type": "string", "description": "", "enum": ["PENDING", "CANCELLED", "PART_CLOSED", "CLOSED", "ERROR", "REPLENISHMENT"], "nullable": true}, "cancelReason": {"type": "string", "description": "", "nullable": true}, "goodsType": {"type": "string", "description": "", "nullable": true}}}, "R«List«PickSuggest»»": {"type": "object", "properties": {"code": {"type": "integer", "description": "", "nullable": true}, "msg": {"type": "string", "description": "", "nullable": true}, "success": {"type": "boolean", "description": "", "nullable": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/PickSuggest", "description": "com.item.wes.application.outbound.pick.dto.PickSuggest"}, "description": "", "nullable": true}}}}, "securitySchemes": {}}, "servers": []}