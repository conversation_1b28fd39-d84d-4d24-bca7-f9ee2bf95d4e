import { NextRequest, NextResponse } from 'next/server';
import { getMemoryService, MemoryType } from '@/services/memoryService';
import { getUserIdFromRequest } from '@/utils/authUtils';

// GET 请求处理 - 搜索记忆
export async function GET(req: NextRequest) {
  try {
    console.log('[MemorySearchAPI] GET 请求: 搜索记忆');

    // 获取当前用户ID
    const userId = await getUserIdFromRequest(req);

    // 如果未登录，返回错误
    if (!userId) {
      console.log('[MemorySearchAPI] 未获取到用户ID，返回401错误');
      return NextResponse.json(
        { error: '用户未登录或会话已过期' },
        { status: 401 }
      );
    }

    console.log('[MemorySearchAPI] 使用用户ID:', userId);

    const url = new URL(req.url);
    const type = url.searchParams.get('type') as MemoryType;
    const query = url.searchParams.get('query');
    const limit = parseInt(url.searchParams.get('limit') || '10');
    const minScore = parseFloat(url.searchParams.get('minScore') || '0.7');

    if (!type) {
      console.log('[MemorySearchAPI] 未提供记忆类型');
      return NextResponse.json(
        { error: '未提供记忆类型' },
        { status: 400 }
      );
    }

    if (!query) {
      console.log('[MemorySearchAPI] 未提供搜索查询');
      return NextResponse.json(
        { error: '未提供搜索查询' },
        { status: 400 }
      );
    }

    console.log(`[MemorySearchAPI] 搜索${type}记忆，查询: "${query}", 最小相似度: ${minScore}`);

    // 获取记忆服务实例
    const memoryService = getMemoryService();

    // 对于用户记忆，只搜索当前用户的记忆
    // 对于系统记忆，使用'system'用户ID
    const searchUserId = type === MemoryType.USER ? userId : 'system';

    // 执行搜索
    const results = await memoryService.searchMemory(
      type,
      query,
      searchUserId,
      limit,
      minScore
    );

    // 记录搜索结果
    console.log(`[MemorySearchAPI] 搜索返回 ${results.results?.length || 0} 条结果`);

    return NextResponse.json(results);
  } catch (error: any) {
    console.error('[MemorySearchAPI] 搜索记忆失败:', error);
    return NextResponse.json(
      { error: '搜索记忆失败', details: error?.message || String(error) },
      { status: 500 }
    );
  }
}
