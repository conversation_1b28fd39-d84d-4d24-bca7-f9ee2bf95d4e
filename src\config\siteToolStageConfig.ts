import { SiteToolConfigType } from './types';

export const siteToolStageConfig: SiteToolConfigType = {
  "portal": {
    "mcpServerIds": [],
    "tools": ["portalQuoteTool", "portalTrackingOrderTool", "getPageSchema", "domOperator", "portalSaveShippingQuoteTool", "portalGetUserPaymentCardsTool", 
      "portalCreateShippingOrderTool", "portalUtCustomersTool", "portalUfCustomersTool","portalAccListTool"]
  },
  "fms": {
    "mcpServerIds": ["fms"],
    "tools": ["sequentialthinking", "find_fms_api", "call_fms_api", "getPageSchema", "domOperator"]
  },
  "unis": {
    "mcpServerIds": [],
    "tools": ["kbTool", "tmsShipmentOrderTrackingTool", "tmsQuoteTool"]
  },
  "wms": {
    "mcpServerIds": ["wms"],
    "tools": ["sequentialthinking", "find_wms_api", "call_wms_api", "kbtool", "getPageSchema", "domOperator"]
  }
}; 