{"openapi": "3.0.1", "info": {"title": "FMS", "description": "", "version": "1.0.0"}, "paths": {"/fms-platform-dispatch-management/TripDispatchEnhance/InitLocalDispatchEsData": {"get": {"summary": "/fms-platform-dispatch-management/TripDispatchEnhance/InitLocalDispatchEsData", "deprecated": false, "description": "", "tags": ["TripDispatchEnhance"], "parameters": [{"name": "taskNo", "in": "query", "description": "", "required": false, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "string"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/dispatching-dashboard/get-terminal-detail-by-code": {"get": {"summary": "Query the corresponding terminal address details according to the terminal code", "deprecated": false, "description": "", "tags": ["TripDispatchEnhance"], "parameters": [{"name": "terminalCode", "in": "query", "description": "", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TerminalAddressResponseDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/local-dispatch-routing/get-local-zone-task-suggest-trip": {"post": {"summary": "Get local zone task suggestions trip", "deprecated": false, "description": "", "tags": ["TripDispatchEnhance"], "parameters": [], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/LocalZoneTaskSuggestTripRequest"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/LocalZoneTaskSuggestTripResponse"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/local-dispatch-routing/search-bill-to": {"get": {"summary": "Get <PERSON><PERSON><PERSON> pull down", "deprecated": false, "description": "", "tags": ["TripDispatchEnhance"], "parameters": [{"name": "search", "in": "query", "description": "", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringFmsSelectListItemListResultDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/local-dispatch-routing/get-local-task-original-data": {"get": {"summary": "Used for debugging and comparing data", "deprecated": false, "description": "", "tags": ["TripDispatchEnhance"], "parameters": [], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/LocalDispatchOrderFilterRequest"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "string"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/local-dispatch-routing/get-local-task-original-data-by-string": {"get": {"summary": "Used for debugging and comparing data", "deprecated": false, "description": "", "tags": ["TripDispatchEnhance"], "parameters": [{"name": "msg", "in": "query", "description": "", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "string"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/route-setting/all": {"get": {"summary": "/fms-platform-dispatch-management/route-setting/all", "deprecated": false, "description": "", "tags": ["RouteSetting"], "parameters": [], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/LocalRouteSettingItem"}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/task/query-unfinished-tasks2": {"post": {"summary": "/fms-platform-dispatch-management/task/query-unfinished-tasks2", "deprecated": false, "description": "", "tags": ["Task"], "parameters": [{"name": "orderNo", "in": "query", "description": "", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UnFinishedTaskResponseDto2"}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}, "get": {"summary": "/fms-platform-dispatch-management/task/query-unfinished-tasks2", "deprecated": false, "description": "", "tags": ["Task"], "parameters": [{"name": "orderNo", "in": "query", "description": "", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UnFinishedTaskResponseDto2"}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/task/query-unfinished-tasks": {"get": {"summary": "/fms-platform-dispatch-management/task/query-unfinished-tasks", "deprecated": false, "description": "", "tags": ["Task"], "parameters": [{"name": "orderNo", "in": "query", "description": "", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UnFinishedTaskResponseDto"}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/CRMRelation/GetCustomerIsHold": {"get": {"summary": "Get whether the client is in a pause state", "deprecated": false, "description": "use:\r\n- Verify the current status of the customer\r\n- Used for customer status checks before business processing", "tags": ["CRMRelation"], "parameters": [{"name": "CustomerID", "in": "query", "description": "Customer ID", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "boolean"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/CRMRelation/GetCustomerIsAccountOrCreditHold": {"get": {"summary": "/fms-platform-dispatch-management/CRMRelation/GetCustomerIsAccountOrCreditHold", "deprecated": false, "description": "", "tags": ["CRMRelation"], "parameters": [{"name": "CustomerID", "in": "query", "description": "", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BooleanBooleanTuple"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/CRMRelation/GetAllHoldCustomerCode": {"get": {"summary": "/fms-platform-dispatch-management/CRMRelation/GetAllHoldCustomerCode", "deprecated": false, "description": "", "tags": ["CRMRelation"], "parameters": [], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringListStringListTuple"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/get-all": {"get": {"summary": "Query sub status inconsistent order", "deprecated": false, "description": "", "tags": ["FixOrder"], "parameters": [{"name": "orderKey", "in": "query", "description": "", "required": false, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/Diff"}, "properties": {}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/LoadMaster/SearchLoadInstructionLinehaulList": {"post": {"summary": "Load Master Linehaul Search List\r\nforeign", "deprecated": false, "description": "", "tags": ["LoadMaster"], "parameters": [], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/LoadInstructionLinehaulListRequest"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/LoadInstructionLinehaulListResponsePagedResultDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/costmodule/GetTripOrderDataForCostModule": {"get": {"summary": "/fms-platform-dispatch-management/costmodule/GetTripOrderDataForCostModule", "deprecated": false, "description": "", "tags": ["DispatchForCostModule"], "parameters": [{"name": "tripNo", "in": "query", "description": "", "required": false, "schema": {"type": "integer", "format": "int64"}}, {"name": "skipMiles", "in": "query", "description": "", "required": false, "schema": {"type": "boolean"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TripOrderDataForCostModule"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/costmodule/GetEmployeeDriverTripsByDriverCodeAndDate": {"get": {"summary": "/fms-platform-dispatch-management/costmodule/GetEmployeeDriverTripsByDriverCodeAndDate", "deprecated": false, "description": "", "tags": ["DispatchForCostModule"], "parameters": [{"name": "driverCode", "in": "query", "description": "", "required": false, "schema": {"type": "string"}}, {"name": "date", "in": "query", "description": "", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TripOrderDataForCostModule"}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/dispatch-for-cost-module/get-trip-by-tms-id": {"post": {"summary": "Get FMS itinerary information through TMS itinerary ID", "deprecated": false, "description": "", "tags": ["DispatchForCostModule"], "parameters": [], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/GetTripByTmsTripIdRequestDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TripOrderDataForCostModule"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/costmodule/CaculateTripTotalMiles": {"get": {"summary": "/fms-platform-dispatch-management/costmodule/CaculateTripTotalMiles", "deprecated": false, "description": "", "tags": ["DispatchForCostModule"], "parameters": [{"name": "tripNo", "in": "query", "description": "", "required": false, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "number", "format": "double"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/test/route-engine/get-route-one": {"post": {"summary": "/fms-platform-dispatch-management/test/route-engine/get-route-one", "deprecated": false, "description": "", "tags": ["TestRouteEngine"], "parameters": [], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/GetRouteOneInputV2Dto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/GetRouteOneOutputV2Dto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/test/route-engine/get-route-list": {"post": {"summary": "/fms-platform-dispatch-management/test/route-engine/get-route-list", "deprecated": false, "description": "", "tags": ["TestRouteEngine"], "parameters": [], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/GetRouteListInputV2Dto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/GetRouteListOutputV2Dto"}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}}, "components": {"schemas": {"AddressDetail": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "group": {"type": "string", "nullable": true}, "street": {"type": "string", "nullable": true}, "country": {"type": "string", "nullable": true}, "city": {"type": "string", "nullable": true}, "state": {"type": "string", "nullable": true}, "zip": {"type": "string", "nullable": true}, "code": {"type": "string", "nullable": true}, "lat": {"type": "number", "format": "double"}, "lng": {"type": "number", "format": "double"}, "address1": {"type": "string", "nullable": true}, "address2": {"type": "string", "nullable": true}, "address3": {"type": "string", "nullable": true}, "address4": {"type": "string", "nullable": true}}, "additionalProperties": false}, "AddressInfo": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "group": {"type": "string", "nullable": true}, "street": {"type": "string", "nullable": true}, "country": {"type": "string", "nullable": true}, "city": {"type": "string", "nullable": true}, "state": {"type": "string", "nullable": true}, "zip": {"type": "string", "nullable": true}, "code": {"type": "string", "nullable": true}, "lat": {"type": "number", "format": "double"}, "lng": {"type": "number", "format": "double"}, "address1": {"type": "string", "nullable": true}, "address2": {"type": "string", "nullable": true}, "address3": {"type": "string", "nullable": true}, "address4": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BooleanBooleanTuple": {"type": "object", "properties": {"item1": {"type": "boolean"}, "item2": {"type": "boolean"}}, "additionalProperties": false}, "BindPackageDetail": {"type": "object", "properties": {"packageNo": {"type": "string", "nullable": true}, "taskNo": {"type": "string", "nullable": true}, "taskType": {"type": "integer", "format": "int32"}, "orderNo": {"type": "string", "nullable": true}, "totalPallet": {"type": "number", "format": "double"}, "totalWeight": {"type": "number", "format": "double"}, "shipper": {"$ref": "#/components/schemas/AddressDetail"}, "consignee": {"$ref": "#/components/schemas/AddressDetail"}, "addressHash": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "nullable": true}}, "additionalProperties": false}, "LocalDispatchOrderFilterRequest": {"type": "object", "properties": {"task_types": {"type": "array", "items": {"type": "string"}, "nullable": true}, "trackingno_or_pronos": {"type": "array", "items": {"type": "string"}, "nullable": true}, "pu_no": {"type": "array", "items": {"type": "string"}, "nullable": true}, "order_nos": {"type": "array", "items": {"type": "string"}, "nullable": true}, "shipment_types": {"type": "array", "items": {"type": "string"}, "nullable": true}, "service_levels": {"type": "array", "items": {"type": "string"}, "nullable": true}, "bill_to_codes": {"type": "array", "items": {"type": "string"}, "nullable": true}, "route_name": {"type": "string", "nullable": true}, "shpr_trms": {"type": "array", "items": {"type": "string"}, "nullable": true}, "cnse_trms": {"type": "array", "items": {"type": "string"}, "nullable": true}, "svcs_trms": {"type": "array", "items": {"type": "string"}, "nullable": true}, "current_terminal": {"type": "string", "nullable": true}, "shipper_name": {"type": "string", "nullable": true}, "shipper_state": {"type": "string", "nullable": true}, "shipper_city": {"type": "string", "nullable": true}, "shipper_zip": {"type": "string", "nullable": true}, "consignee_name": {"type": "string", "nullable": true}, "consignee_state": {"type": "string", "nullable": true}, "consignee_city": {"type": "string", "nullable": true}, "consignee_zip": {"type": "string", "nullable": true}, "address": {"type": "string", "nullable": true}, "date": {"type": "string", "format": "date-time"}, "start_date": {"type": "string", "format": "date-time", "nullable": true}, "mabd_start": {"type": "string", "format": "date-time", "nullable": true}, "mabd_end": {"type": "string", "format": "date-time", "nullable": true}, "appointment_is_null": {"type": "boolean"}, "tab": {"$ref": "#/components/schemas/LocalDispatchOrderFilterTabEnum"}, "skip": {"type": "integer", "format": "int32"}, "max_result_count": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "LocalDispatchOrderFilterTabEnum": {"enum": [0, 1, 2, 3, 4, 5, 6, 7, 8], "type": "integer", "format": "int32"}, "GetRouteListInputV2Dto": {"type": "object", "properties": {"currentPage": {"type": "integer", "format": "int32"}, "size": {"type": "integer", "format": "int32"}, "routeResultIds": {"type": "array", "items": {"type": "integer", "format": "int64"}, "nullable": true}, "routeResultNameLike": {"type": "string", "nullable": true}}, "additionalProperties": false}, "GetRouteListOutputV2Dto": {"type": "object", "properties": {"routeResultId": {"type": "integer", "format": "int64"}, "routeResultName": {"type": "string", "nullable": true}, "routeResultStatus": {"type": "integer", "format": "int32"}, "routeHolderId": {"type": "integer", "format": "int64"}, "routeType": {"type": "integer", "format": "int32"}, "zoneFrom": {"type": "string", "nullable": true}, "zoneTo": {"type": "string", "nullable": true}, "orgTerminalCode": {"type": "string", "nullable": true}, "destTerminalCode": {"type": "string", "nullable": true}, "maxWeight": {"type": "number", "format": "double"}, "maxPallet": {"type": "number", "format": "double"}, "lastWeight": {"type": "number", "format": "double"}, "lastPallet": {"type": "number", "format": "double"}, "packageCount": {"type": "integer", "format": "int32"}, "packageList": {"type": "array", "items": {"$ref": "#/components/schemas/PackageListItem"}, "nullable": true}, "hotZoneList": {"type": "array", "items": {"$ref": "#/components/schemas/HotZoneDetailDto"}, "nullable": true}}, "additionalProperties": false}, "GetRouteOneInputV2Dto": {"type": "object", "properties": {"routeResultId": {"type": "integer", "format": "int64"}}, "additionalProperties": false}, "LocalRouteSettingItem": {"type": "object", "properties": {"terminal_code": {"type": "string", "nullable": true}, "is_open_auto_dispatch": {"type": "integer", "format": "int32"}, "is_open_run_plan": {"type": "integer", "format": "int32"}, "is_pickup_active": {"type": "integer", "format": "int32"}, "is_delivery_active": {"type": "integer", "format": "int32"}, "is_pickup_delivery_active": {"type": "integer", "format": "int32"}, "is_linehaul_active": {"type": "integer", "format": "int32"}, "is_route_000": {"type": "integer", "format": "int32"}, "is_general_trip": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "GetRouteOneOutputV2Dto": {"type": "object", "properties": {"routeResultId": {"type": "integer", "format": "int64"}, "routeResultName": {"type": "string", "nullable": true}, "shuttleNo": {"type": "string", "nullable": true}, "realTransportDatetime": {"type": "string", "nullable": true}, "routeResultStatus": {"type": "integer", "format": "int32"}, "routeResultType": {"type": "integer", "format": "int32"}, "isActive": {"type": "integer", "format": "int32"}, "routePlantName": {"type": "string", "nullable": true}, "routeType": {"type": "integer", "format": "int32"}, "scheduleTransportDatetime": {"type": "string", "nullable": true}, "orgTerminalCode": {"type": "string", "nullable": true}, "destTerminalCode": {"type": "string", "nullable": true}, "fromPolygonCodes": {"type": "array", "items": {"type": "string"}, "nullable": true}, "toPolygonCodes": {"type": "array", "items": {"type": "string"}, "nullable": true}, "hotZoneList": {"type": "array", "items": {"$ref": "#/components/schemas/HotZoneDetailV2"}, "nullable": true}, "maxWeight": {"type": "number", "format": "double"}, "maxPallet": {"type": "number", "format": "double"}, "lastWeight": {"type": "number", "format": "double"}, "lastPallet": {"type": "number", "format": "double"}, "bindPackageList": {"type": "array", "items": {"$ref": "#/components/schemas/BindPackageDetail"}, "nullable": true}, "fullRouteResultName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "GetTripByTmsTripIdRequestDto": {"type": "object", "properties": {"tms_trip_id": {"type": "string", "nullable": true}}, "additionalProperties": false}, "LoadInstructionLinehaulListOrderFreightCommodityResponse": {"type": "object", "properties": {"code": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "quantity": {"type": "integer", "format": "int32"}, "quantity_uom": {"type": "string", "nullable": true}, "weight": {"type": "number", "format": "double"}, "weight_uom": {"type": "string", "nullable": true}, "volume": {"type": "number", "format": "double"}, "volume_uom": {"type": "string", "nullable": true}, "linear": {"type": "number", "format": "double"}, "linear_uom": {"type": "string", "nullable": true}, "length": {"type": "number", "format": "double"}, "width": {"type": "number", "format": "double"}, "height": {"type": "number", "format": "double"}, "space": {"type": "number", "format": "double"}, "stackable": {"type": "integer", "format": "int32"}, "freight_class": {"type": "number", "format": "double"}, "nmfc": {"type": "string", "nullable": true}, "declared_value": {"type": "number", "format": "double"}, "unit_price": {"type": "number", "format": "double"}, "customer_pro": {"type": "string", "nullable": true}, "inner_pack_qty": {"type": "integer", "format": "int32"}, "inner_pack_uom": {"type": "string", "nullable": true}, "reference": {"type": "string", "nullable": true}, "reference2": {"type": "string", "nullable": true}, "reference3": {"type": "string", "nullable": true}, "reference4": {"type": "string", "nullable": true}, "reference5": {"type": "string", "nullable": true}, "load_no": {"type": "string", "nullable": true}, "po_no": {"type": "string", "nullable": true}, "bol": {"type": "string", "nullable": true}, "bol_no": {"type": "string", "nullable": true}}, "additionalProperties": false}, "LoadInstructionLinehaulListOrderPalletResponse": {"type": "object", "properties": {"pallet_id": {"type": "integer", "format": "int64"}, "sequence": {"type": "integer", "format": "int32"}, "pallets_no": {"type": "string", "nullable": true}, "tracking_no": {"type": "string", "nullable": true}, "length": {"type": "number", "format": "double"}, "width": {"type": "number", "format": "double"}, "height": {"type": "number", "format": "double"}, "weight": {"type": "number", "format": "double"}, "weight_uom": {"type": "string", "nullable": true}, "freight_class": {"type": "number", "format": "double"}, "volume": {"type": "number", "format": "double"}, "volume_uom": {"type": "string", "nullable": true}, "status": {"type": "integer", "format": "int32"}, "stackable": {"type": "integer", "format": "int32"}, "inner_count": {"type": "integer", "format": "int32"}, "is_over_size": {"type": "integer", "format": "int32"}, "is_over_load": {"type": "integer", "format": "int32"}, "linear": {"type": "number", "format": "double"}, "linear_uom": {"type": "string", "nullable": true}, "item_count": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "LoadInstructionLinehaulListOrderResponse": {"type": "object", "properties": {"order_no": {"type": "string", "nullable": true}, "tracking_or_pro_no": {"type": "string", "nullable": true}, "shipper_name": {"type": "string", "nullable": true}, "consignee_name": {"type": "string", "nullable": true}, "total_pallets": {"type": "integer", "format": "int32"}, "total_weight": {"type": "number", "format": "double"}, "pallets": {"type": "array", "items": {"$ref": "#/components/schemas/LoadInstructionLinehaulListOrderPalletResponse"}, "nullable": true}, "freight_commodity": {"type": "array", "items": {"$ref": "#/components/schemas/LoadInstructionLinehaulListOrderFreightCommodityResponse"}, "nullable": true}}, "additionalProperties": false}, "LoadInstructionLinehaulListRequest": {"type": "object", "properties": {"org_terminals": {"type": "array", "items": {"type": "string"}, "nullable": true}, "dest_terminals": {"type": "array", "items": {"type": "string"}, "nullable": true}, "current_terminal": {"type": "string", "nullable": true}, "linehaul_nos": {"type": "array", "items": {"type": "integer", "format": "int64"}, "nullable": true}, "pro_nos": {"type": "array", "items": {"type": "string"}, "nullable": true}, "page_number": {"maximum": 2147483647, "minimum": 0, "type": "integer", "format": "int32"}, "page_size": {"maximum": 2147483647, "minimum": 1, "type": "integer", "format": "int32"}}, "additionalProperties": false}, "LoadInstructionLinehaulListResponse": {"type": "object", "properties": {"task_no": {"type": "integer", "format": "int64"}, "org_terminal": {"type": "string", "nullable": true}, "dest_terminal": {"type": "string", "nullable": true}, "linehaul_status": {"$ref": "#/components/schemas/TaskStatusEnum"}, "trip_no": {"type": "integer", "format": "int64"}, "orders": {"type": "array", "items": {"$ref": "#/components/schemas/LoadInstructionLinehaulListOrderResponse"}, "nullable": true}}, "additionalProperties": false}, "LoadInstructionLinehaulListResponsePagedResultDto": {"type": "object", "properties": {"total_count": {"type": "integer", "format": "int64"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/LoadInstructionLinehaulListResponse"}, "nullable": true}}, "additionalProperties": false}, "HolidaySchedules": {"type": "object", "properties": {"holiday_from": {"type": "string", "nullable": true}, "holiday_name": {"type": "string", "nullable": true}, "holiday_to": {"type": "string", "nullable": true}, "id": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "HotZoneDetailDto": {"type": "object", "properties": {"hot_zone_code": {"type": "string", "nullable": true}, "polygon_code": {"type": "string", "nullable": true}, "terminal_code": {"type": "string", "nullable": true}}, "additionalProperties": false}, "HotZoneDetailV2": {"type": "object", "properties": {"zoneCode": {"type": "string", "nullable": true}, "terminalCode": {"type": "string", "nullable": true}, "polygonCode": {"type": "string", "nullable": true}}, "additionalProperties": false}, "LocalZoneTaskSuggestTripRequest": {"type": "object", "properties": {"task_no": {"type": "integer", "format": "int64"}}, "additionalProperties": false}, "LocalZoneTaskSuggestTripResponse": {"type": "object", "properties": {"match_flag": {"type": "integer", "format": "int32"}, "msg": {"type": "string", "nullable": true}, "zone_trip_list": {"type": "array", "items": {"$ref": "#/components/schemas/ZoneTripObj"}, "nullable": true}}, "additionalProperties": false}, "PackageListItem": {"type": "object", "properties": {"packageNo": {"type": "string", "nullable": true}, "taskNo": {"type": "string", "nullable": true}, "taskType": {"type": "integer", "format": "int32"}, "orderNo": {"type": "string", "nullable": true}, "totalPallet": {"type": "number", "format": "double"}, "totalWeight": {"type": "number", "format": "double"}, "shipper": {"$ref": "#/components/schemas/AddressInfo"}, "consignee": {"$ref": "#/components/schemas/AddressInfo"}, "addressHash": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StringListStringListTuple": {"type": "object", "properties": {"item1": {"type": "array", "items": {"type": "string"}, "nullable": true}, "item2": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "TaskStatusEnum": {"enum": [1, 2, 3, 4, 5, 6, 7, 20, 21, 22, -1], "type": "integer", "format": "int32"}, "TempTaskInReferenceInfoTpf": {"type": "object", "properties": {"sequence": {"type": "integer", "format": "int32"}, "status": {"type": "integer", "format": "int32"}, "status_name": {"type": "string", "nullable": true}, "task_no": {"type": "integer", "format": "int64"}, "trip_no": {"type": "integer", "format": "int64"}, "task_type": {"type": "integer", "format": "int32"}, "task_group_id": {"type": "integer", "format": "int64"}, "created_time": {"type": "string", "nullable": true}, "update_time": {"type": "string", "nullable": true}, "update_user": {"type": "string", "nullable": true}}, "additionalProperties": false}, "TempTaskInfoTpf": {"type": "object", "properties": {"task_no": {"type": "integer", "format": "int64"}, "task_type": {"type": "integer", "format": "int32"}, "task_type_name": {"type": "string", "nullable": true}, "status": {"type": "integer", "format": "int32"}, "status_name": {"type": "string", "nullable": true}, "task_type_group": {"type": "string", "nullable": true}, "shipment_order_no": {"type": "string", "nullable": true}, "created_time": {"type": "string", "nullable": true}, "update_time": {"type": "string", "nullable": true}, "update_user": {"type": "string", "nullable": true}}, "additionalProperties": false}, "TempTaskLinehaulOrderInfoTpf": {"type": "object", "properties": {"task_no": {"type": "integer", "format": "int64"}, "shipment_order_no": {"type": "string", "nullable": true}, "status": {"type": "integer", "format": "int32"}, "status_name": {"type": "string", "nullable": true}, "org_terminal": {"type": "string", "nullable": true}, "dest_terminal": {"type": "string", "nullable": true}, "created_time": {"type": "string", "nullable": true}, "update_time": {"type": "string", "nullable": true}, "update_user": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Schedules": {"type": "object", "properties": {"holiday_schedules": {"type": "array", "items": {"$ref": "#/components/schemas/HolidaySchedules"}, "nullable": true}, "work_schedules": {"type": "array", "items": {"$ref": "#/components/schemas/WorkSchedules"}, "nullable": true}}, "additionalProperties": false}, "UnFinishedTaskResponseDto": {"type": "object", "properties": {"trip_no": {"type": "integer", "format": "int64"}, "task_no": {"type": "integer", "format": "int64"}, "task_type": {"type": "integer", "format": "int32", "nullable": true}, "task_type_name": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UnFinishedTaskResponseDto2": {"type": "object", "properties": {"trip_no": {"type": "integer", "format": "int64"}, "task_no": {"type": "integer", "format": "int64"}, "task_type": {"type": "integer", "format": "int32", "nullable": true}, "data_type": {"type": "string", "nullable": true}, "task_type_name": {"type": "string", "nullable": true}, "origin_task_info_list": {"type": "array", "items": {"$ref": "#/components/schemas/TempTaskInfoTpf"}, "nullable": true}, "linehaul_order_info_list": {"type": "array", "items": {"$ref": "#/components/schemas/TempTaskLinehaulOrderInfoTpf"}, "nullable": true}, "task_reference_list": {"type": "array", "items": {"$ref": "#/components/schemas/TempTaskInReferenceInfoTpf"}, "nullable": true}}, "additionalProperties": false}, "StringFmsSelectListItem": {"type": "object", "properties": {"text": {"type": "string", "nullable": true}, "value": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StringFmsSelectListItemListResultDto": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/StringFmsSelectListItem"}, "nullable": true}}, "additionalProperties": false}, "TerminaAddressInfoResponse": {"type": "object", "properties": {"address": {"type": "string", "nullable": true}, "address1": {"type": "string", "nullable": true}, "address2": {"type": "string", "nullable": true}, "area_locations": {"type": "array", "items": {"type": "string"}, "nullable": true}, "city": {"type": "string", "nullable": true}, "code": {"type": "string", "nullable": true}, "company_code": {"type": "string", "nullable": true}, "company_id": {"type": "integer", "format": "int32"}, "company_name": {"type": "string", "nullable": true}, "contact_name": {"type": "string", "nullable": true}, "docks": {"type": "array", "items": {"type": "string"}, "nullable": true}, "email": {"type": "string", "nullable": true}, "facility_code": {"type": "string", "nullable": true}, "facility_company_code": {"type": "string", "nullable": true}, "facility_company_id": {"type": "string", "nullable": true}, "facility_id": {"type": "string", "nullable": true}, "facility_name": {"type": "string", "nullable": true}, "id": {"type": "integer", "format": "int32"}, "lat": {"type": "number", "format": "double"}, "location_id": {"type": "integer", "format": "int64"}, "lon": {"type": "number", "format": "double"}, "name": {"type": "string", "nullable": true}, "phone": {"type": "string", "nullable": true}, "pro_prefix": {"type": "string", "nullable": true}, "schedules": {"$ref": "#/components/schemas/Schedules"}, "state": {"type": "string", "nullable": true}, "street": {"type": "string", "nullable": true}, "type": {"type": "string", "nullable": true}, "waves": {"type": "array", "items": {"$ref": "#/components/schemas/Waves"}, "nullable": true}, "zip_code": {"type": "string", "nullable": true}}, "additionalProperties": false}, "TerminalAddressResponseDto": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/TerminaAddressInfoResponse"}, "msg": {"type": "string", "nullable": true}, "success": {"type": "boolean"}, "timestamp": {"type": "integer", "format": "int64"}}, "additionalProperties": false}, "TripOrderDataForCostModule": {"type": "object", "properties": {"trip_no": {"type": "integer", "format": "int64"}, "dispatch_date": {"type": "string", "format": "date-time"}, "carrier_code": {"type": "string", "nullable": true}, "carrier_name": {"type": "string", "nullable": true}, "driver_code": {"type": "string", "nullable": true}, "driver_name": {"type": "string", "nullable": true}, "org_terminal": {"type": "string", "nullable": true}, "dest_terminal": {"type": "string", "nullable": true}, "total_miles": {"type": "number", "format": "double"}, "has_linehaul": {"type": "boolean"}, "trip_status": {"type": "integer", "format": "int32"}, "trip_orders": {"type": "array", "items": {"$ref": "#/components/schemas/TripOrderListDto"}, "nullable": true}}, "additionalProperties": false}, "TripOrderListDto": {"type": "object", "properties": {"task_no": {"type": "integer", "format": "int64"}, "tracking_no": {"type": "string", "nullable": true}, "order_no": {"type": "string", "nullable": true}, "task_type": {"type": "integer", "format": "int32"}, "service_level": {"type": "integer", "format": "int32"}, "from_address1": {"type": "string", "nullable": true}, "from_address2": {"type": "string", "nullable": true}, "from_city": {"type": "string", "nullable": true}, "from_state": {"type": "string", "nullable": true}, "from_zipcode": {"type": "string", "nullable": true}, "to_address1": {"type": "string", "nullable": true}, "to_address2": {"type": "string", "nullable": true}, "to_city": {"type": "string", "nullable": true}, "to_state": {"type": "string", "nullable": true}, "to_zipcode": {"type": "string", "nullable": true}, "miles": {"type": "number", "format": "double"}}, "additionalProperties": false}, "Waves": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32", "nullable": true}, "sequence": {"type": "integer", "format": "int32", "nullable": true}, "time_from": {"type": "string", "nullable": true}, "time_to": {"type": "string", "nullable": true}}, "additionalProperties": false}, "WorkSchedules": {"type": "object", "properties": {"day": {"type": "string", "nullable": true}, "id": {"type": "integer", "format": "int32", "nullable": true}, "time_from": {"type": "string", "nullable": true}, "time_to": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ZoneTripObj": {"type": "object", "properties": {"trip_no": {"type": "integer", "format": "int64"}, "route_name": {"type": "string", "nullable": true}, "carrier_name": {"type": "string", "nullable": true}, "driver_name": {"type": "string", "nullable": true}, "pickup_total_pallet": {"type": "integer", "format": "int32"}, "pickup_total_weight": {"type": "number", "format": "double"}, "delivery_total_pallet": {"type": "integer", "format": "int32"}, "delivery_total_weight": {"type": "number", "format": "double"}}, "additionalProperties": false}}}}