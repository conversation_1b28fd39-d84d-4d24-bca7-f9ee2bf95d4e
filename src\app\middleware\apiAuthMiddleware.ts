/**
 * @file 服务端API认证中间件
 * @description 处理API路由的认证，包括普通API和嵌入式API的验证
 * @server-side
 */

import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { verifyToken } from '../../utils/authUtils';
import { verifyEmbedToken } from '../../utils/token';
import { rateLimiterMiddleware } from '../../middleware/rate-limiter';
import { addCorsHeaders } from '../../utils/cors';

// Public routes that don't need authentication
const PUBLIC_ROUTES = [
  '/api/auth/token',
  '/api/auth/refresh',
  '/api/auth/logout',
];

// 嵌入式API路由 - 需要特殊处理
const EMBED_ROUTES = [
  '/api/chat-embed',
  '/api/chat-history',
];

// 获取站点ID
function getSiteId(request: NextRequest): string | null {
  return request.headers.get('x-site-id');
}

/**
 * API Authentication Middleware
 * Handles token verification for all API routes
 * @param req Request object
 * @returns Response object
 */
export async function apiAuthMiddleware(req: NextRequest) {
  const { pathname } = req.nextUrl;

  // 如果是OPTIONS请求，直接放行
  if (req.method === 'OPTIONS') {
    return NextResponse.next();
  }

  // Only protect API routes
  if (!pathname.startsWith('/api/')) {
    return NextResponse.next();
  }

  // Allow public routes to pass through
  if (PUBLIC_ROUTES.some(route => pathname.startsWith(route))) {
    console.log(`[API Auth] Public route access: ${pathname}`);
    return NextResponse.next();
  }

  // 特殊处理嵌入式API路由 - 检查域名
  if (EMBED_ROUTES.some(route => pathname.startsWith(route))) {
    return handleEmbedAuth(req);
  }

  // 处理普通API路由
  return handleNormalAuth(req);
}

/**
 * 处理嵌入式API的认证
 */
async function handleEmbedAuth(req: NextRequest): Promise<NextResponse> {
  // 获取 Authorization header
  const authHeader = req.headers.get('Authorization');
  
  // 如果存在 Authorization header，使用普通API的验证方式
  if (authHeader?.startsWith('Bearer ')) {
    const token = authHeader.substring(7);
    const payload = await verifyToken(token);
    
    if (!payload) {
      return addCorsHeaders(NextResponse.json(
        { error: 'Invalid token' },
        { status: 401 }
      ));
    }
    
    return addCorsHeaders(NextResponse.next());
  }

  const siteId = getSiteId(req);
  if (!siteId) {
    return addCorsHeaders(NextResponse.json(
      { error: 'Missing site ID' },
      { status: 400 }
    ));
  }

  // 如果没有 Authorization header，检查临时 token
  const tempToken = req.headers.get('X-Temp-Token');
  if (!tempToken) {
    return addCorsHeaders(NextResponse.json(
      { error: 'Missing authentication' },
      { status: 401 }
    ));
  }

  const payload = await verifyEmbedToken(tempToken, siteId);
  if (!payload) {
    return addCorsHeaders(NextResponse.json(
      { error: 'Invalid token' },
      { status: 401 }
    ));
  }
  // 从请求头中获取客户端IP
  const clientIP = req.headers.get('x-forwarded-for')?.split(',')[0] || 
                  req.headers.get('x-real-ip') || 
                  'unknown';
  console.log('handleEmbedAuth clientIP ', clientIP, req.headers.get('x-forwarded-for'), req.headers.get('x-real-ip'));
  console.log('handleEmbedAuth headers ', req.headers);
  // 执行限流检查
  const rateLimitResponse = await rateLimiterMiddleware(req, siteId, clientIP);
  if (rateLimitResponse) {
    return addCorsHeaders(rateLimitResponse);
  }

  return addCorsHeaders(NextResponse.next());
}

/**
 * 处理普通API的认证
 */
async function handleNormalAuth(req: NextRequest): Promise<NextResponse> {
  // Get token from request
  let token = null;

  // Check Authorization header
  const authHeader = req.headers.get('Authorization');
  if (authHeader && authHeader.startsWith('Bearer ')) {
    token = authHeader.substring(7);
  }

  // Check cookies if no token in header
  if (!token) {
    token = req.cookies.get('iam_access_token')?.value;
  }

  // Check x-session-token header if no token found yet
  if (!token) {
    token = req.headers.get('x-session-token');
  }

  // If no token found and route requires auth, return 401
  if (!token) {
    console.log(`[API Auth] No token found for protected route: ${req.nextUrl.pathname}`);
    return new NextResponse(
      JSON.stringify({ error: 'Authentication required' }),
      {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }

  // Verify token
  const payload = await verifyToken(token);
  if (!payload) {
    console.log(`[API Auth] Invalid token for protected route: ${req.nextUrl.pathname}`);
    return new NextResponse(
      JSON.stringify({ error: 'Invalid or expired token' }),
      {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }

  // Token is valid, allow request to proceed
  console.log(`[API Auth] Valid token for route: ${req.nextUrl.pathname}, user: ${payload.sub || payload.data?.user_id || 'unknown'}`);
  return NextResponse.next();
}