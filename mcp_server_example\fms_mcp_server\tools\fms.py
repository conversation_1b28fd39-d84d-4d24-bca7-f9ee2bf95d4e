import aiohttp
import json
import logging
import base64
import os
from typing import Dict, Any, List, Union
from pathlib import Path
from config import FMS_BASE_URL
from tools.fms_api_finder import FMSApiFinder
from .base import BaseTool

logger = logging.getLogger(__name__)

# 添加一个辅助函数读取提示词
def get_fms_prompt():
    """读取FMS提示词"""
    try:
        # 尝试从文件中读取提示词
        current_dir = os.path.dirname(os.path.abspath(__file__))
        parent_dir = os.path.dirname(current_dir)
        fms_prompt_path = os.path.join(parent_dir, "prompts", "fms_prompt.py")
        
        if os.path.exists(fms_prompt_path):
            with open(fms_prompt_path, 'r', encoding='utf-8') as f:
                content = f.read()
                # 提取FMS_TOOLS_PROMPT变量的值
                if "FMS_TOOLS_PROMPT = '''" in content:
                    start_index = content.find("FMS_TOOLS_PROMPT = '''") + len("FMS_TOOLS_PROMPT = '''")
                    end_index = content.find("'''", start_index)
                    if end_index > start_index:
                        return content[start_index:end_index]
    except Exception as e:
        logger.error(f"Error reading FMS prompt: {e}")
    
    # 如果读取失败，返回默认提示词
    return """
    ## Guide for FMS Tools
    
    FMS Tools provides functionality for managing factory operations.
    
    Available tools:
    - find_fms_api - Search for FMS API endpoints
    - call_fms_api - Call FMS API endpoints
    """

async def on_request_start(session, trace_config_ctx, params):
    logger.info(f"Starting request:")
    logger.info(f"Sending request: {params.method} {params.url}")
    logger.info(f"Headers: {params.headers}")
    logger.info(f"Params: {params}")
    if hasattr(params, 'data'):
        logger.info(f"Data: {params.data}")
    if hasattr(params, 'json'):
        logger.info(f"JSON: {params.json}")

async def on_request_end(session, trace_config_ctx, params):
    logger.info(f"Ending request: {params.method} {params.url}")
    logger.info(f"Response status: {params.response.status}")


class FMSTools(BaseTool):
    def __init__(self, **kwargs):
        """Initialize FMS Tools"""
        if not FMS_BASE_URL:
            raise ValueError("Missing required FMS_BASE_URL configuration in config.py")
            
        self.base_url = FMS_BASE_URL
        
        # Basic headers
        self.headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json, text/plain, */*',
            'Item-Time-Zone': 'America/Los_Angeles'
        }
        
        # Initialize API finder
        try:
            self.api_finder = FMSApiFinder.get_instance()
            logger.info("Successfully initialized FMS API Finder")
        except Exception as e:
            logger.error(f"Failed to initialize FMS API Finder: {str(e)}")
            self.api_finder = None
            
    async def update_context(self, **context):
        """Update FMS tool context"""
        # 调用父类的update_context
        await super().update_context(**context)
        logger.info(f"Updated FMS context")

    async def call_fms_api(self, path: str, method: str, params: Union[Dict[str, Any], List[Any]] = None, headers: Dict[str, Any] = None) -> Dict[str, Any]:
        """Call FMS API with Fms-token and Company-id headers
        
        Args:
            path: API endpoint path
            method: HTTP method (GET, POST, PUT, DELETE)
            params: Request body parameters, can be either a dictionary or array depending on API requirements
            headers: Required custom headers
        
        Returns:
            Dict containing the API response
        """
        try:
            # Start with base headers
            request_headers = dict(self.headers)
            
            # Apply custom headers if provided (including authorization, tenant, facility)
            if headers and isinstance(headers, dict):
                request_headers.update(headers)
                logger.info("Using custom headers provided by client")
            
            # 验证必要的头信息
            required_headers = ['Fms-token', 'Company-id']
            missing_headers = [h for h in required_headers if h.lower() not in {k.lower() for k in request_headers.keys()}]
            
            if missing_headers:
                logger.error(f"Missing required headers: {', '.join(missing_headers)}")
                return {
                    "success": False,
                    "error": f"Missing required headers: {', '.join(missing_headers)}",
                    "message": "API call requires complete headers with Fms-token and Company-id information",
                    "data": None
                }
            
            # Add default source parameter for db-change-logs/search-by-paging endpoints
            if path.endswith('db-change-logs/search-by-paging') and method.upper() == 'POST':
                params = params or {}
                if 'source' not in params:
                    params['source'] = 'fmsApp'
                    logger.info("Added default source='fmsApp' parameter for db-change-logs API")
            
            url = f"{self.base_url}{path}"
            # Log the exact request details including the serialized JSON
            logger.info(f"Calling FMS API with details:")
            logger.info(f"URL: {url}")
            logger.info(f"Method: {method}")
            
            # Log headers but mask authorization token for security
            safe_headers = {**request_headers}
            if 'Authorization' in safe_headers:
                safe_headers['Authorization'] = 'Bearer [REDACTED]'
            logger.info(f"Headers: {json.dumps(safe_headers, indent=2)}")
            
            if params:
                logger.info(f"Request Body: {json.dumps(params, indent=2)}")
            
            # Create trace config
            trace_config = aiohttp.TraceConfig()
            trace_config.on_request_start.append(on_request_start)
            trace_config.on_request_end.append(on_request_end)
            
            async with aiohttp.ClientSession(trace_configs=[trace_config]) as session:
                method_upper = method.upper()
                request_args = {
                    'method': method_upper,
                    'url': url,
                    'headers': request_headers
                }
                if method_upper == 'GET':
                    # GET 用 params 作为 query string
                    if params:
                        request_args['params'] = params
                else:
                    # 其他方法用 json body
                    request_args['json'] = params
                
                async with session.request(**request_args) as response:
                    # 处理响应
                    response_json = await response.json()
                    logger.debug(f"API Response: {response_json}")
                    
                    # Clean response by removing null values
                    cleaned_response = self._clean_null_values(response_json)
                    
                    # Log cleaned response
                    logger.info(f"FMS API Response Status: {response.status}")
                    logger.info(f"FMS API Response Headers: {dict(response.headers)}")
                    logger.info(f"FMS API Response Body (cleaned): {cleaned_response}")
                    
                    return {
                        "success": response.status < 400,
                        "status_code": response.status,
                        "data": cleaned_response
                    }
                
        except Exception as e:
            logger.error(f"FMS API call failed: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "message": "FMS API call failed",
                "data": None
            }

    def _clean_null_values(self, data: Any) -> Any:
        """Recursively remove null values from the data structure"""
        if isinstance(data, dict):
            return {
                key: self._clean_null_values(value)
                for key, value in data.items()
                if value is not None and value != {} and value != []
            }
        elif isinstance(data, list):
            return [
                self._clean_null_values(item)
                for item in data
                if item is not None and item != {} and item != []
            ]
        else:
            return data

    async def find_fms_api(self, query: str, top_k: int = 5) -> Dict[str, Any]:
        """
        Find the most relevant FMS APIs based on the query

        Args:
            query: User's search query
            top_k: Number of top relevant APIs to return

        Returns:
            Dict: Response in the format expected by Claude AI
        """
        try:
            if not self.api_finder:
                logger.error("FMS API Finder is not initialized")
                raise ValueError("FMS API Finder is not initialized")

            logger.info(f"Searching for FMS APIs with query: '{query}', top_k: {top_k}")
            
            # 检查API文件是否存在
            if hasattr(self.api_finder, 'api_file_paths'):
                for file_path in self.api_finder.api_file_paths:
                    logger.info(f"Checking API file: {file_path}, exists: {os.path.exists(file_path)}")
            
            results = self.api_finder.search_apis(query, top_k=top_k)
            logger.info(f"Search complete, found {len(results)} results")
            
            # Format results for better presentation
            formatted_results = []
            for result in results:
                api_info = {
                    'path': result['path'],
                    'method': result['method'],
                    'similarity': result['similarity'],
                    'summary': result['details']['summary'],
                    'description': result['details']['description'],
                    'parameters': {
                        'headers': result['details']['parameters']['headers'],
                        'path': result['details']['parameters']['path'],
                        'query': result['details']['parameters']['query'],
                        'body': result['details']['parameters']['body']
                    }
                }
                formatted_results.append(api_info)
                logger.info(f"API found: {result['method']} {result['path']} (similarity: {result['similarity']:.4f})")

            # 返回简化的格式，移除额外的嵌套层级
            return {
                "success": True,
                "apis": formatted_results,
                "message": f"Found {len(formatted_results)} relevant APIs"
            }

        except Exception as e:
            logger.error(f"Error finding FMS APIs: {str(e)}")
            logger.exception("Exception details:")
            return {
                "success": False,
                "error": str(e),
                "message": "Failed to find relevant APIs",
                "apis": []
            }

    def get_tools(self) -> list:
        """Return the available FMS tools and their schemas"""
        return [
            {
                "name": "call_fms_api",
                "description": "Call FMS API endpoints. You must first use find_fms_api to discover the correct API path before calling this.",
                "input_schema": {
                    "type": "object",
                    "properties": {
                        "path": {
                            "type": "string",
                            "description": "API path, must be one defined in the documentation"
                        },
                        "method": {
                            "type": "string",
                            "description": "HTTP method (GET, POST, PUT, DELETE)",
                            "enum": ["GET", "POST", "PUT", "DELETE"]
                        },
                        "params": {
                            "oneOf": [
                                {
                                    "type": "object",
                                    "description": "Request parameters as object, for APIs that expect object parameters"
                                },
                                {
                                    "type": "array",
                                    "description": "Request parameters as array, for APIs that expect array parameters"
                                }
                            ],
                            "description": "Request parameters, must match the API documentation format. Can be either an object or array depending on the API requirements."
                        },
                        "headers": {
                            "type": "object",
                            "description": "Required request headers, must for example: {'Authorization': 'Bearer YOUR_TOKEN'}"
                        }
                    },
                    "required": ["path", "method"]
                }
            },
            {
                "name": "find_fms_api",
                "description": "Search for relevant FMS APIs based on the query. Returns API information including path, method, parameters, etc.",
                "input_schema": {
                    "type": "object",
                    "properties": {
                        "query": {
                            "type": "string",
                            "description": "Search query describing the API functionality you're looking for"
                        },
                        "top_k": {
                            "type": "integer",
                            "description": "Number of most relevant APIs to return",
                            "default": 5,
                            "minimum": 1,
                            "maximum": 10
                        }
                    },
                    "required": ["query"]
                }
            }
        ]

    @classmethod
    def get_system_prompt(cls) -> str:
        return get_fms_prompt()