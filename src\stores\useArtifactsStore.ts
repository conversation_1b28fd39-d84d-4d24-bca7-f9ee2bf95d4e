import { create } from 'zustand';

interface Artifact {
  id: string;
  type: string;
  title: string;
  props: Record<string, any>;
  messageId?: string; // 关联的消息ID
  toolName?: string; // 工具名称，用于显示
}

interface ArtifactsState {
  // 当前激活显示在侧边栏的artifacts
  activeArtifacts: Artifact[];
  // 可用但未激活的artifacts（用于历史消息）
  availableArtifacts: Map<string, Artifact[]>; // messageId -> artifacts[]
  
  // 激活一个artifact到侧边栏
  activateArtifact: (artifact: Artifact) => void;
  // 批量激活artifacts（用于新消息自动显示）
  activateArtifacts: (artifacts: Artifact[]) => void;
  // 移除激活的artifact
  removeActiveArtifact: (id: string) => void;
  // 注册可用的artifacts（但不激活）
  registerAvailableArtifacts: (messageId: string, artifacts: Artifact[]) => void;
  // 获取某个消息的可用artifacts
  getAvailableArtifacts: (messageId: string) => Artifact[];
  // 清空所有（切换聊天时使用）
  clearAll: () => void;
}

export const useArtifactsStore = create<ArtifactsState>((set, get) => ({
  activeArtifacts: [],
  availableArtifacts: new Map(),
  
  activateArtifact: (artifact) =>
    set((state) => {
      // 检查是否已经激活
      const existingIndex = state.activeArtifacts.findIndex((a) => a.id === artifact.id);
      if (existingIndex > -1) {
        // 已存在，替换
        const newActiveArtifacts = [...state.activeArtifacts];
        newActiveArtifacts[existingIndex] = artifact;
        return { activeArtifacts: newActiveArtifacts };
      }
      // 新增
      return { activeArtifacts: [...state.activeArtifacts, artifact] };
    }),
    
  activateArtifacts: (artifacts) =>
    set((state) => {
      const newActiveArtifacts = [...state.activeArtifacts];
      artifacts.forEach(artifact => {
        const existingIndex = newActiveArtifacts.findIndex((a) => a.id === artifact.id);
        if (existingIndex > -1) {
          newActiveArtifacts[existingIndex] = artifact;
        } else {
          newActiveArtifacts.push(artifact);
        }
      });
      return { activeArtifacts: newActiveArtifacts };
    }),
    
  removeActiveArtifact: (id) =>
    set((state) => ({
      activeArtifacts: state.activeArtifacts.filter((a) => a.id !== id),
    })),
    
  registerAvailableArtifacts: (messageId, artifacts) =>
    set((state) => {
      const newAvailableArtifacts = new Map(state.availableArtifacts);
      newAvailableArtifacts.set(messageId, artifacts);
      return { availableArtifacts: newAvailableArtifacts };
    }),
    
  getAvailableArtifacts: (messageId) => {
    return get().availableArtifacts.get(messageId) || [];
  },
    
  clearAll: () => set({ activeArtifacts: [], availableArtifacts: new Map() }),
}));

// 导出一个兼容的hook用于现有代码
export const useArtifactsStoreCompat = () => {
  const store = useArtifactsStore();
  return {
    ...store,
    artifacts: store.activeArtifacts,
    addArtifact: store.activateArtifact,
    removeArtifact: store.removeActiveArtifact,
    clearArtifacts: store.clearAll,
  };
};