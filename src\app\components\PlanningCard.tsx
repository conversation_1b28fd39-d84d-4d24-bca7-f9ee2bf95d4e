'use client';

import React, { useState } from 'react';
import ReactMarkdown from 'react-markdown';
import '@/styles/item-design-system.css';

interface PlanningStep {
  title: string;
  description?: string;
  status?: 'pending' | 'in_progress' | 'completed' | 'failed';
}

interface PlanningData {
  summary: string;
  concepts?: string[];
  tools?: string[];
  steps: PlanningStep[];
  parameters?: Record<string, any>;
  challenges?: string[];
}

interface PlanningCardProps {
  planningData: PlanningData;
}

export default function PlanningCard({ planningData }: PlanningCardProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  
  // Collapsed view
  if (!isExpanded) {
    return (
      <div 
        className="item-card bg-item-purple/20 rounded-lg border border-item-purple/30 p-2 text-white shadow-lg cursor-pointer transition-all duration-200 hover:border-item-purple/50 hover:shadow-xl my-2 item-glow-purple-subtle"
        onClick={() => setIsExpanded(true)}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div className="bg-item-bg-hover p-1.5 rounded-lg mr-2.5">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-4 h-4 text-item-purple">
                <path strokeLinecap="round" strokeLinejoin="round" d="M8.25 6.75h12M8.25 12h12m-12 5.25h12M3.75 6.75h.007v.008H3.75V6.75zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zM3.75 12h.007v.008H3.75V12zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm-.375 5.25h.007v.008H3.75v-.008zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0z" />
              </svg>
            </div>
            <div>
              <div className="text-sm font-semibold text-white">Task Plan</div>
              <div className="text-xs text-item-purple-light font-medium">{planningData.summary}</div>
            </div>
          </div>
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-4 h-4 text-item-purple transition-transform duration-200">
            <path strokeLinecap="round" strokeLinejoin="round" d="M19.5 8.25l-7.5 7.5-7.5-7.5" />
          </svg>
        </div>
      </div>
    );
  }
  
  // Get step status color
  const getStatusColor = (status?: string) => {
    switch (status) {
      case 'completed': return 'bg-green-900/20 border-green-500/30 text-green-400';
      case 'in_progress': return 'bg-item-purple/20 border-item-purple/30 text-item-purple-light';
      case 'failed': return 'bg-red-900/20 border-red-500/30 text-red-400';
      case 'pending':
      default: return 'bg-item-bg-hover/50 border-item-gray-700/30 text-white';
    }
  };
  
  // Expanded view
  return (
    <div className="item-card bg-item-purple/20 rounded-lg border border-item-purple/30 p-2 text-white shadow-lg my-2 item-glow-purple-subtle">
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center">
          <div className="bg-item-bg-hover p-1.5 rounded-lg mr-2.5">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-4 h-4 text-item-purple">
              <path strokeLinecap="round" strokeLinejoin="round" d="M8.25 6.75h12M8.25 12h12m-12 5.25h12M3.75 6.75h.007v.008H3.75V6.75zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zM3.75 12h.007v.008H3.75V12zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm-.375 5.25h.007v.008H3.75v-.008zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0z" />
            </svg>
          </div>
          <div>
            <div className="text-sm font-semibold text-white">Task Plan</div>
            <div className="text-xs text-item-purple-light mt-0.5 font-medium">{planningData.summary}</div>
          </div>
        </div>
        <button 
          className="text-item-purple hover:text-item-purple-light p-1.5 rounded-lg hover:bg-item-bg-hover transition-all duration-200"
          onClick={() => setIsExpanded(false)}
        >
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-4 h-4">
            <path strokeLinecap="round" strokeLinejoin="round" d="M4.5 15.75l7.5-7.5 7.5 7.5" />
          </svg>
        </button>
      </div>
      
      {/* Key concepts */}
      {planningData.concepts && planningData.concepts.length > 0 && (
        <div className="mb-3 bg-item-bg-hover/50 p-2 rounded-lg">
          <div className="text-sm font-semibold mb-2 text-item-orange uppercase tracking-wide">Key Concepts</div>
          <div className="flex flex-wrap gap-1.5">
            {planningData.concepts.map((concept, index) => (
              <span 
                key={index} 
                className="px-2.5 py-1 rounded-full bg-item-bg-card border border-item-purple/30 text-sm font-medium text-white hover:border-item-purple/50 transition-colors duration-200"
              >
                {concept}
              </span>
            ))}
          </div>
        </div>
      )}
      
      {/* Tools needed */}
      {planningData.tools && planningData.tools.length > 0 && (
        <div className="mb-3 bg-item-bg-hover/50 p-2 rounded-lg">
          <div className="text-sm font-semibold mb-2 text-item-orange uppercase tracking-wide">Required Tools</div>
          <div className="bg-item-bg-card rounded-lg p-2 border border-item-gray-800/30">
            <code className="text-sm text-item-purple-light font-mono leading-snug">
              {planningData.tools.join(', ')}
            </code>
          </div>
        </div>
      )}
      
      {/* Steps */}
      <div className="mb-3">
        <div className="text-sm font-semibold mb-2.5 text-item-orange uppercase tracking-wide">Execution Steps</div>
        <div className="space-y-2">
          {planningData.steps.map((step, index) => (
            <div key={index} className={`p-2 rounded-lg border ${getStatusColor(step.status)} transition-all duration-200 hover:shadow-md`}>
              <div className="flex items-start">
                <div className="bg-item-purple text-white h-6 w-6 rounded-full flex items-center justify-center text-sm font-bold mr-3 flex-shrink-0 shadow-lg">
                  {index + 1}
                </div>
                <div className="flex-1">
                  <div className="font-semibold text-base text-white">{step.title}</div>
                  {step.description && (
                    <div className="text-sm mt-1.5 text-item-gray-300 leading-snug">
                      <ReactMarkdown>{step.description}</ReactMarkdown>
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
      
      {/* Challenges */}
      {planningData.challenges && planningData.challenges.length > 0 && (
        <div className="bg-item-bg-hover/50 rounded-lg p-2">
          <div className="text-sm font-semibold mb-2 text-item-orange uppercase tracking-wide">Potential Challenges</div>
          <div className="bg-item-bg-card border border-item-gray-800/30 rounded-lg p-2 text-white text-sm">
            <ul className="list-disc pl-5 space-y-1.5">
              {planningData.challenges.map((challenge, index) => (
                <li key={index} className="leading-snug text-item-gray-300">{challenge}</li>
              ))}
            </ul>
          </div>
        </div>
      )}
    </div>
  );
} 