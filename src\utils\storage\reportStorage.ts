import { ChatReport, ReportSearchParams } from '@/chat-report/types';
import { StorageConfig } from './types';
import * as fs from 'fs';
import * as path from 'path';
import { nanoid } from 'nanoid';
import { S3Client, PutObjectCommand, GetObjectCommand, ListObjectsV2Command } from '@aws-sdk/client-s3';

// 报告元数据接口
export interface ReportMetadata {
  id: string;
  latestReportDate?: string; // 保留向后兼容性
  latestReportTimestamp?: string; // 新增：精确到时间的时间戳
  createdAt: string;
  updatedAt: string;
  [key: string]: any; // 允许添加其他元数据
}

// 报告存储接口
export interface ReportStorage {
  saveReport(report: ChatReport): Promise<ChatReport>;
  getReportByDate(date: string): Promise<ChatReport | null>;
  getReportById(id: string): Promise<any | null>; // 用于获取元数据或其他特殊报告
  listAllReports(): Promise<Array<Omit<ChatReport, 'chatAnalyses'>>>;
  searchReports(params: ReportSearchParams): Promise<Array<Omit<ChatReport, 'chatAnalyses'>>>;
  saveMetadata(metadata: ReportMetadata): Promise<ReportMetadata>; // 保存元数据
}

// 本地文件系统报告存储实现
export class LocalReportStorage implements ReportStorage {
  private readonly reportsDir: string;

  constructor(config: StorageConfig) {
    this.reportsDir = config.reports?.dir || path.join(process.cwd(), 'data', 'reports');
    this.ensureReportsDir();
  }

  private ensureReportsDir(): void {
    if (!fs.existsSync(this.reportsDir)) {
      fs.mkdirSync(this.reportsDir, { recursive: true });
    }
  }

  private getReportFilePath(date: string): string {
    return path.join(this.reportsDir, `report-${date}.json`);
  }

  private getMetadataFilePath(id: string): string {
    return path.join(this.reportsDir, `${id}.json`);
  }

  // 保存报告
  async saveReport(report: ChatReport): Promise<ChatReport> {
    this.ensureReportsDir();
    const filePath = this.getReportFilePath(report.date);
    await fs.promises.writeFile(filePath, JSON.stringify(report, null, 2), 'utf8');
    console.log(`报告已保存到 ${filePath}`);
    return report;
  }

  // 根据日期获取报告
  async getReportByDate(date: string): Promise<ChatReport | null> {
    this.ensureReportsDir();
    const filePath = this.getReportFilePath(date);

    if (!fs.existsSync(filePath)) {
      console.log(`未找到日期为 ${date} 的报告，路径: ${filePath}`);
      return null;
    }

    try {
      const reportContent = await fs.promises.readFile(filePath, 'utf8');
      return JSON.parse(reportContent) as ChatReport;
    } catch (error) {
      console.error(`读取日期为 ${date} 的报告时出错:`, error);
      return null;
    }
  }

  // 获取所有报告文件
  private async getAllReportFiles(): Promise<string[]> {
    this.ensureReportsDir();

    try {
      const files = await fs.promises.readdir(this.reportsDir);
      return files.filter(file =>
        file.startsWith('report-') &&
        file.endsWith('.json') &&
        file !== 'report-metadata.json' // 排除元数据文件
      );
    } catch (error) {
      console.error('读取报告目录时出错:', error);
      return [];
    }
  }

  // 列出所有报告（仅元数据，不包含完整内容）
  async listAllReports(): Promise<Array<Omit<ChatReport, 'chatAnalyses'>>> {
    const reportFiles = await this.getAllReportFiles();
    const reports: Array<Omit<ChatReport, 'chatAnalyses'>> = [];

    for (const file of reportFiles) {
      try {
        const content = await fs.promises.readFile(path.join(this.reportsDir, file), 'utf8');
        const fullReport = JSON.parse(content) as ChatReport;

        // 省略详细分析以减小负载大小
        const { chatAnalyses, ...reportMeta } = fullReport;
        reports.push(reportMeta);
      } catch (error) {
        console.error(`读取报告文件 ${file} 时出错:`, error);
      }
    }

    // 按日期降序排序（最新的在前）
    return reports.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
  }

  // 搜索报告
  async searchReports(params: ReportSearchParams): Promise<Array<Omit<ChatReport, 'chatAnalyses'>>> {
    const allReports = await this.listAllReports();

    return allReports.filter(report => {
      // 日期范围过滤
      if (params.fromDate && report.date < params.fromDate) {
        return false;
      }
      if (params.toDate && report.date > params.toDate) {
        return false;
      }

      // 解决状态过滤（如果指定）
      if (params.resolved !== undefined) {
        if (report.totalChats === 0) return true; // 处理没有聊天的边缘情况

        const resolvedPercentage = report.totalResolvedChats / report.totalChats;
        if (params.resolved && resolvedPercentage < 0.5) {
          return false;
        } else if (!params.resolved && resolvedPercentage >= 0.5) {
          return false;
        }
      }

      // 查询文本过滤
      if (params.query) {
        // 我们没有完整的文本，但可以检查日期/ID
        return report.id.includes(params.query) || report.date.includes(params.query);
      }

      return true;
    });
  }

  // 根据ID获取报告或元数据
  async getReportById(id: string): Promise<any | null> {
    this.ensureReportsDir();
    const filePath = this.getMetadataFilePath(id);

    if (!fs.existsSync(filePath)) {
      console.log(`未找到ID为 ${id} 的报告或元数据，路径: ${filePath}`);
      return null;
    }

    try {
      const content = await fs.promises.readFile(filePath, 'utf8');
      return JSON.parse(content);
    } catch (error) {
      console.error(`读取ID为 ${id} 的报告或元数据时出错:`, error);
      return null;
    }
  }

  // 保存元数据
  async saveMetadata(metadata: ReportMetadata): Promise<ReportMetadata> {
    this.ensureReportsDir();
    const filePath = this.getMetadataFilePath(metadata.id);

    try {
      await fs.promises.writeFile(filePath, JSON.stringify(metadata, null, 2), 'utf8');
      console.log(`元数据已保存到 ${filePath}`);
      return metadata;
    } catch (error) {
      console.error(`保存元数据到 ${filePath} 时出错:`, error);
      throw new Error('保存元数据失败');
    }
  }
}

// S3报告存储实现
export class S3ReportStorage implements ReportStorage {
  private readonly s3Client: S3Client;
  private readonly bucketName: string;
  private readonly prefix: string;

  constructor(config: StorageConfig) {
    this.bucketName = config.s3?.bucketName || '';
    this.prefix = (config.reports?.prefix || 'reports/').replace(/\/*$/, '/');

    // 使用与聊天历史相同的S3客户端配置
    const clientConfig = {
      region: config.s3?.region || 'us-west-2',
      credentials: {
        accessKeyId: config.s3?.accessKeyId || '',
        secretAccessKey: config.s3?.secretAccessKey || '',
      }
    };

    this.s3Client = new S3Client(clientConfig);
    console.log(`初始化S3报告存储，桶: ${this.bucketName}，前缀: ${this.prefix}`);
  }

  // 获取报告的S3对象键
  private getReportKey(date: string): string {
    return `${this.prefix}report-${date}.json`;
  }

  // 获取元数据的S3对象键
  private getMetadataKey(id: string): string {
    return `${this.prefix}${id}.json`;
  }

  // 保存报告
  async saveReport(report: ChatReport): Promise<ChatReport> {
    const objectKey = this.getReportKey(report.date);

    try {
      const command = new PutObjectCommand({
        Bucket: this.bucketName,
        Key: objectKey,
        Body: JSON.stringify(report, null, 2),
        ContentType: 'application/json',
      });

      await this.s3Client.send(command);
      console.log(`报告已保存到S3: ${objectKey}`);
      return report;
    } catch (error) {
      console.error('保存报告到S3失败:', error);
      throw new Error('保存报告失败');
    }
  }

  // 根据日期获取报告
  async getReportByDate(date: string): Promise<ChatReport | null> {
    const objectKey = this.getReportKey(date);

    try {
      const command = new GetObjectCommand({
        Bucket: this.bucketName,
        Key: objectKey,
      });

      try {
        const response = await this.s3Client.send(command);
        const rawData = await response.Body?.transformToString();

        if (rawData) {
          return JSON.parse(rawData) as ChatReport;
        }
      } catch (e: any) {
        // 如果对象不存在，返回null
        if (e.name === 'NoSuchKey') {
          console.log(`未找到日期为 ${date} 的报告`);
          return null;
        }
        throw e;
      }
    } catch (error) {
      console.error(`获取日期为 ${date} 的报告时出错:`, error);
      return null;
    }

    return null;
  }

  // 列出所有报告（仅元数据，不包含完整内容）
  async listAllReports(): Promise<Array<Omit<ChatReport, 'chatAnalyses'>>> {
    try {
      const command = new ListObjectsV2Command({
        Bucket: this.bucketName,
        Prefix: this.prefix,
      });

      const response = await this.s3Client.send(command);
      const reports: Array<Omit<ChatReport, 'chatAnalyses'>> = [];

      if (response.Contents && response.Contents.length > 0) {
        for (const item of response.Contents) {
          // 确保是报告文件，且不是元数据文件
          if (item.Key &&
              item.Key.endsWith('.json') &&
              item.Key.includes('report-') &&
              !item.Key.endsWith('report-metadata.json')) {
            try {
              const getCommand = new GetObjectCommand({
                Bucket: this.bucketName,
                Key: item.Key,
              });

              const objectResponse = await this.s3Client.send(getCommand);
              const rawData = await objectResponse.Body?.transformToString();

              if (rawData) {
                const fullReport = JSON.parse(rawData) as ChatReport;
                // 省略详细分析以减小负载大小
                const { chatAnalyses, ...reportMeta } = fullReport;
                reports.push(reportMeta);
              }
            } catch (e) {
              console.error(`从S3获取对象 ${item.Key} 失败:`, e);
            }
          }
        }
      }

      // 按日期降序排序（最新的在前）
      return reports.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
    } catch (error) {
      console.error('列出报告时出错:', error);
      return [];
    }
  }

  // 搜索报告
  async searchReports(params: ReportSearchParams): Promise<Array<Omit<ChatReport, 'chatAnalyses'>>> {
    const allReports = await this.listAllReports();

    return allReports.filter(report => {
      // 日期范围过滤
      if (params.fromDate && report.date < params.fromDate) {
        return false;
      }
      if (params.toDate && report.date > params.toDate) {
        return false;
      }

      // 解决状态过滤（如果指定）
      if (params.resolved !== undefined) {
        if (report.totalChats === 0) return true; // 处理没有聊天的边缘情况

        const resolvedPercentage = report.totalResolvedChats / report.totalChats;
        if (params.resolved && resolvedPercentage < 0.5) {
          return false;
        } else if (!params.resolved && resolvedPercentage >= 0.5) {
          return false;
        }
      }

      // 查询文本过滤
      if (params.query) {
        // 我们没有完整的文本，但可以检查日期/ID
        return report.id.includes(params.query) || report.date.includes(params.query);
      }

      return true;
    });
  }

  // 根据ID获取报告或元数据
  async getReportById(id: string): Promise<any | null> {
    const objectKey = this.getMetadataKey(id);

    try {
      const command = new GetObjectCommand({
        Bucket: this.bucketName,
        Key: objectKey,
      });

      try {
        const response = await this.s3Client.send(command);
        const rawData = await response.Body?.transformToString();

        if (rawData) {
          return JSON.parse(rawData);
        }
      } catch (e: any) {
        // 如果对象不存在，返回null
        if (e.name === 'NoSuchKey') {
          console.log(`未找到ID为 ${id} 的报告或元数据`);
          return null;
        }
        throw e;
      }
    } catch (error) {
      console.error(`获取ID为 ${id} 的报告或元数据时出错:`, error);
      return null;
    }

    return null;
  }

  // 保存元数据
  async saveMetadata(metadata: ReportMetadata): Promise<ReportMetadata> {
    const objectKey = this.getMetadataKey(metadata.id);

    try {
      const command = new PutObjectCommand({
        Bucket: this.bucketName,
        Key: objectKey,
        Body: JSON.stringify(metadata, null, 2),
        ContentType: 'application/json',
      });

      await this.s3Client.send(command);
      console.log(`元数据已保存到S3: ${objectKey}`);
      return metadata;
    } catch (error) {
      console.error('保存元数据到S3失败:', error);
      throw new Error('保存元数据失败');
    }
  }
}

// 创建报告存储实例
export const createReportStorage = (config: StorageConfig): ReportStorage => {
  if (config.type === 's3') {
    return new S3ReportStorage(config);
  } else {
    return new LocalReportStorage(config);
  }
};
