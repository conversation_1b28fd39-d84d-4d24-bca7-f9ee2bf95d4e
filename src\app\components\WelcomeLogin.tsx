'use client';

import React, { useState } from 'react';
import LoginButton from './LoginButton';
import '@/styles/item-design-system.css';

export default function WelcomeLogin() {
  const [isHovered, setIsHovered] = useState(false);
  
  return (
    <div className="flex flex-col items-center justify-center h-screen w-full p-8 text-center bg-item-bg-primary">
      {/* Main content */}
      <div className="relative max-w-md">
        <div className="mb-2 text-xs text-item-gray-400 tracking-wider transition-colors duration-300">
          Cyber Bot v2.0
        </div>
        
        <h1 className={`text-4xl font-bold mb-6 tracking-tight transition-all duration-500 ${
          isHovered 
            ? 'text-item-purple' 
            : 'text-white'
        }`}>
          Welcome to AI Chatbot
        </h1>
        
        <div className="mb-8 max-w-2xl mx-auto">
          <p className="text-item-gray-400 text-sm leading-relaxed">
            Explore advanced AI models and powerful tools through 
            our neural network.
          </p>
          
          <div className="mt-8 flex flex-col items-center gap-4"
               onMouseEnter={() => setIsHovered(true)}
               onMouseLeave={() => setIsHovered(false)}>
            <LoginButton className="px-8 py-3 bg-item-bg-card border border-item-gray-800 w-full max-w-xs transition-all duration-300 text-white rounded-md hover:border-item-purple/30" />
            <div className="text-xs text-item-gray-400">
              Secure authentication powered by IAM (OAUTH2)
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 