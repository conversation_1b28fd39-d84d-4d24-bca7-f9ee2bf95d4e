{"openapi": "3.0.1", "info": {"title": "FMS", "description": "", "version": "1.0.0"}, "paths": {"/fms-platform-order/shipment-order-data-provide/query-package-track/{packageNo}": {"get": {"summary": "internal\r\nQuery the package status", "deprecated": false, "description": "", "tags": ["ShipmentOrderDataProvide"], "parameters": [{"name": "packageNo", "in": "path", "description": "Package number", "required": true, "example": "", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/QueryPackageTrackDto"}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order/shipment-order-data-provide/list": {"get": {"summary": "internal\r\nGet the Packages information corresponding to ShipmentOrderNo", "deprecated": false, "description": "", "tags": ["ShipmentOrderDataProvide"], "parameters": [], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/RpcShipmentOrderListRequest"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/RpcShipmentOrderDto"}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order/shipment-order-data-provide/shipmentorders": {"get": {"summary": "Get the order information corresponding to ShipmentOrderNo", "deprecated": false, "description": "", "tags": ["ShipmentOrderDataProvide"], "parameters": [{"name": "shipmentOrderNos", "in": "query", "description": "Order number list", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ShipmentOrderOutput"}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order/shipment-order-data-provide/workorders": {"get": {"summary": "Get the order information corresponding to WorkOrderNo", "deprecated": false, "description": "", "tags": ["ShipmentOrderDataProvide"], "parameters": [{"name": "workOrderNos", "in": "query", "description": "Work Order Number List", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WorkOrderOutput"}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order/shipment-order-data-provide/get-order-address": {"get": {"summary": "Follow <PERSON><PERSON><PERSON> or <PERSON>cking<PERSON><PERSON> to get the sender information from the order recipient", "deprecated": false, "description": "", "tags": ["ShipmentOrderDataProvide"], "parameters": [{"name": "type", "in": "query", "description": "type", "required": false, "schema": {"type": "integer", "format": "int32"}}, {"name": "orderNo", "in": "query", "description": "Order number", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/OrderAddressDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order/shipment-order-data-provide/get-order-by-tms-id": {"post": {"summary": "Get FMS order information through TMS order ID", "deprecated": false, "description": "", "tags": ["ShipmentOrderDataProvide"], "parameters": [], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/GetOrderByTmsOrderIdRequestDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/OrderInfoResponseDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order/shipment-order-data-provide/get-orders-by-tms-ids": {"post": {"summary": "Get FMS order information in batches through TMS order ID", "deprecated": false, "description": "", "tags": ["ShipmentOrderDataProvide"], "parameters": [], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/GetOrdersByTmsOrderIdsRequestDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/GetOrderByTmsOrderIdResponseDataDto"}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order/files/{shipmentOrderNo}": {"get": {"summary": "internal\r\nQuery all uploaded files corresponding to shipmentorder", "deprecated": false, "description": "", "tags": ["ShipmentOrderFiles"], "parameters": [{"name": "shipmentOrderNo", "in": "path", "description": "Freight Order Number", "required": true, "example": "", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ShipmentOrderFilesDto"}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order/freight/{shipmentOrderNo}": {"get": {"summary": "Query shipping orderNo corresponding shipping fee information", "deprecated": false, "description": "", "tags": ["ShipmentOrderFreight"], "parameters": [{"name": "shipmentOrderNo", "in": "path", "description": "", "required": true, "example": "", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ShipmentOrderActualFreightInfoDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order/freight/v2/{shipmentOrderNo}": {"get": {"summary": "Query shipping orderNo corresponding shipping fee information", "deprecated": false, "description": "", "tags": ["ShipmentOrderFreight"], "parameters": [{"name": "shipmentOrderNo", "in": "path", "description": "", "required": true, "example": "", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ShipmentOrderActualFreightInfoV2DtoListResultDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order/osd/subtype/{osdType}": {"get": {"summary": "internal\r\nLoad different SubType drop-down box data when selecting OSD Type", "deprecated": false, "description": "", "tags": ["ShipmentOrderOSD"], "parameters": [{"name": "osdType", "in": "path", "description": "OSD type", "required": true, "example": "", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringFmsSelectListItemListResultDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order/osd/getosdList/{shipmentOrderNo}": {"get": {"summary": "internal\r\nQuery the Osd record collection corresponding to the shipmentorder", "deprecated": false, "description": "", "tags": ["ShipmentOrderOSD"], "parameters": [{"name": "shipmentOrderNo", "in": "path", "description": "Freight Order Number", "required": true, "example": "", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ShowOSDDto"}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order/osd/list": {"post": {"summary": "internal\r\nQuery the Osd record collection corresponding to the shipmentorder", "deprecated": false, "description": "", "tags": ["ShipmentOrderOSD"], "parameters": [], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/ShipmentOrderOsdListRequest"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ShowOSDDto"}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order/shipment-orders/get-appointment/shipmentorderNo={shipmentOrderNo}&appointmentType={appointmentType}": {"get": {"summary": "internal\r\nQuery appointment information", "deprecated": false, "description": "", "tags": ["ShipmentOrders"], "parameters": [{"name": "shipmentOrderNo", "in": "path", "description": "Order number", "required": true, "example": "", "schema": {"type": "string"}}, {"name": "appointmentType", "in": "path", "description": "Appointment Type", "required": true, "example": 0, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ShipmentOrderShipperAppointmentDtoNew"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order/shipment-orders/query": {"post": {"summary": "foreign\r\nShipment Order List Page - Pagination Query", "deprecated": false, "description": "", "tags": ["ShipmentOrders"], "parameters": [], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/ShipmentOrderGetListRequest"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ShipmentOrderListItemDtoPagedResultDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order/shipment-orders/getcustomersinfo": {"get": {"summary": "Query CRM Customer Information", "deprecated": false, "description": "", "tags": ["ShipmentOrders"], "parameters": [{"name": "CustomerName", "in": "query", "description": "", "required": false, "schema": {"type": "string"}}, {"name": "PrintName", "in": "query", "description": "", "required": false, "schema": {"type": "string"}}, {"name": "CustomerCode", "in": "query", "description": "", "required": false, "schema": {"type": "string"}}, {"name": "Sorting", "in": "query", "description": "", "required": false, "schema": {"type": "string"}}, {"name": "PageNumber", "in": "query", "description": "", "required": false, "schema": {"maximum": 2147483647, "minimum": 0, "type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "description": "", "required": false, "schema": {"maximum": 2147483647, "minimum": 1, "type": "integer", "format": "int32"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CustomerInfoDtoPagedResultDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order/shipment-orders/get-err-rowdatas": {"get": {"summary": "/fms-platform-order/shipment-orders/get-err-rowdatas", "deprecated": false, "description": "", "tags": ["ShipmentOrders"], "parameters": [], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"type": "string"}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order/shipment-orders/get-err-rowdatas/details": {"post": {"summary": "/fms-platform-order/shipment-orders/get-err-rowdatas/details", "deprecated": false, "description": "", "tags": ["ShipmentOrders"], "parameters": [], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/GetErrRowdatasRequest"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"type": "string"}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order/shipment-orders/search": {"post": {"summary": "foreign\r\nQuery orders based on order number", "deprecated": false, "description": "", "tags": ["ShipmentOrders"], "parameters": [], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/RpcGetOrdersRequest"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/RpcGetOrdersResponse"}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order/shipment-orders-pallet-weight/search": {"post": {"summary": "foreign\r\nQuery pallet and weight according to the order number", "deprecated": false, "description": "", "tags": ["ShipmentOrders"], "parameters": [], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/RpcGetOrdersRequest"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/RpcShipmentOrderPalletWeightDto"}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}}, "components": {"schemas": {"AppointmentCarrierInfo": {"type": "object", "properties": {"contractor_or_carrier_name": {"type": "string", "nullable": true}, "contractor_or_carrier_code": {"type": "string", "nullable": true}}, "additionalProperties": false}, "GetErrRowdatasRequest": {"type": "object", "properties": {"keys": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "CustomerInfoDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "customer_name": {"type": "string", "nullable": true}, "customer_code": {"type": "string", "nullable": true}, "print_name": {"type": "string", "nullable": true}, "bill_to_only": {"type": "boolean"}, "type": {"type": "integer", "format": "int32"}, "telnet_id": {"type": "string", "nullable": true}, "company": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CustomerInfoDtoPagedResultDto": {"type": "object", "properties": {"total_count": {"type": "integer", "format": "int64"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/CustomerInfoDto"}, "nullable": true}}, "additionalProperties": false}, "OrderAddressDto": {"type": "object", "properties": {"order_no": {"type": "string", "nullable": true}, "tracking_no": {"type": "string", "nullable": true}, "shipper_name": {"type": "string", "nullable": true}, "consignee_name": {"type": "string", "nullable": true}, "shipper_zip_code": {"type": "string", "nullable": true}, "consignee_zip_code": {"type": "string", "nullable": true}, "shipper_city": {"type": "string", "nullable": true}, "consignee_city": {"type": "string", "nullable": true}}, "additionalProperties": false}, "GetOrderByTmsOrderIdRequestDto": {"type": "object", "properties": {"tms_order_id": {"type": "string", "nullable": true}}, "additionalProperties": false}, "GetOrderByTmsOrderIdResponseDataDto": {"type": "object", "properties": {"order_id": {"type": "integer", "format": "int64"}, "order_no": {"type": "string", "nullable": true}, "pro_no": {"type": "string", "nullable": true}, "tms_order_id": {"type": "string", "nullable": true}}, "additionalProperties": false}, "GetOrdersByTmsOrderIdsRequestDto": {"type": "object", "properties": {"tms_order_ids": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "RpcOrderEstimateFreightDto": {"type": "object", "properties": {"code": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "quantity": {"type": "integer", "format": "int32"}, "quantity_uom": {"type": "string", "nullable": true}, "weight": {"type": "number", "format": "double"}, "weight_uom": {"type": "string", "nullable": true}, "volume": {"type": "number", "format": "double"}, "volume_uom": {"type": "string", "nullable": true}, "linear": {"type": "number", "format": "double"}, "linear_uom": {"type": "string", "nullable": true}, "length": {"type": "number", "format": "double"}, "width": {"type": "number", "format": "double"}, "height": {"type": "number", "format": "double"}, "space": {"type": "number", "format": "double"}, "stackable": {"type": "integer", "format": "int32"}, "freight_class": {"type": "number", "format": "double"}, "nmfc": {"type": "string", "nullable": true}, "declared_value": {"type": "number", "format": "double"}, "unit_price": {"type": "number", "format": "double"}, "customer_pro": {"type": "string", "nullable": true}, "inner_pack_qty": {"type": "integer", "format": "int32"}, "inner_pack_uom": {"type": "string", "nullable": true}, "reference": {"type": "string", "nullable": true}, "reference2": {"type": "string", "nullable": true}, "reference3": {"type": "string", "nullable": true}, "reference4": {"type": "string", "nullable": true}, "reference5": {"type": "string", "nullable": true}, "load_no": {"type": "string", "nullable": true}, "po_no": {"type": "string", "nullable": true}, "bol": {"type": "string", "nullable": true}, "bol_no": {"type": "string", "nullable": true}}, "additionalProperties": false}, "QueryPackageTrackDto": {"type": "object", "properties": {"status": {"$ref": "#/components/schemas/ShipmentOrderStatusEnum"}, "sub_status": {"$ref": "#/components/schemas/ShipmentOrderSubStatusEnum"}, "status_desc": {"$ref": "#/components/schemas/ShipmentOrderStatusEnum"}, "sub_status_desc": {"$ref": "#/components/schemas/ShipmentOrderSubStatusEnum"}, "package_no": {"type": "string", "nullable": true}, "tracking_no": {"type": "string", "nullable": true}, "pallet_no": {"type": "string", "nullable": true}, "shipment_type": {"$ref": "#/components/schemas/ShipmentTypeEnum"}, "event": {"type": "string", "nullable": true}, "title": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "address1": {"type": "string", "nullable": true}, "address2": {"type": "string", "nullable": true}, "city": {"type": "string", "nullable": true}, "state": {"type": "string", "nullable": true}, "country": {"type": "string", "nullable": true}, "zipcode": {"type": "string", "nullable": true}, "longitude": {"type": "number", "format": "double"}, "latitude": {"type": "number", "format": "double"}, "driver": {"type": "string", "nullable": true}, "carrier": {"type": "string", "nullable": true}, "package_status": {"$ref": "#/components/schemas/ShipmentOrderPackageStatusEnum"}, "package_status_desc": {"$ref": "#/components/schemas/ShipmentOrderPackageStatusEnum"}, "actual_arrive_time": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "ShipmentOrderOutput": {"type": "object", "additionalProperties": false, "properties": {}}, "OrderInfoResponseDto": {"type": "object", "properties": {"order_id": {"type": "integer", "format": "int64"}, "tms_order_id": {"type": "string", "nullable": true}, "order_no": {"type": "string", "nullable": true}, "order_type": {"type": "integer", "format": "int32"}, "order_status": {"type": "integer", "format": "int32"}, "pro_no": {"type": "string", "nullable": true}, "pieces": {"type": "integer", "format": "int32"}, "weight": {"type": "number", "format": "double"}, "volume": {"type": "number", "format": "double"}, "pallet_count": {"type": "integer", "format": "int32"}, "create_time": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "RpcGetOrderAccServiceResponse": {"type": "object", "properties": {"acc_code": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "id": {"type": "integer", "format": "int32"}, "quantity": {"type": "integer", "format": "int32"}, "uom": {"type": "string", "nullable": true}}, "additionalProperties": false}, "RpcGetOrderAddress": {"type": "object", "properties": {"address1": {"type": "string", "nullable": true}, "address2": {"type": "string", "nullable": true}, "city": {"type": "string", "nullable": true}, "close_time": {"type": "string", "nullable": true}, "contact": {"type": "string", "nullable": true}, "country": {"type": "string", "nullable": true}, "deliverd_by": {"type": "integer", "format": "int32"}, "deliverd_location": {"type": "string", "nullable": true}, "deliverd_time": {"type": "string", "format": "date-time"}, "delivery_max_times": {"type": "integer", "format": "int32"}, "desired_delivery_date": {"type": "string", "format": "date-time"}, "email": {"type": "string", "nullable": true}, "latitude": {"type": "number", "format": "double"}, "location_id": {"type": "integer", "format": "int64"}, "location_type": {"type": "integer", "format": "int32"}, "location_name": {"type": "string", "nullable": true}, "longitude": {"type": "number", "format": "double"}, "mabd": {"type": "string", "format": "date-time"}, "note": {"type": "string", "nullable": true}, "open_time": {"type": "string", "nullable": true}, "phone": {"type": "string", "nullable": true}, "pod_name": {"type": "string", "nullable": true}, "polygon_code": {"type": "string", "nullable": true}, "polygon_id": {"type": "string", "nullable": true}, "request_pickup_date": {"type": "string", "format": "date-time"}, "request_pickup_time_begin": {"type": "string", "format": "date-time"}, "request_pickup_time_end": {"type": "string", "format": "date-time"}, "state": {"type": "string", "nullable": true}, "terminal": {"type": "string", "nullable": true}, "terminal_address1": {"type": "string", "nullable": true}, "terminal_address2": {"type": "string", "nullable": true}, "terminal_city": {"type": "string", "nullable": true}, "terminal_lat": {"type": "number", "format": "double"}, "terminal_lng": {"type": "number", "format": "double"}, "terminal_state": {"type": "string", "nullable": true}, "terminal_zipcode": {"type": "string", "nullable": true}, "zip_code": {"type": "string", "nullable": true}}, "additionalProperties": false}, "RpcGetOrderBill": {"type": "object", "properties": {"address1": {"type": "string", "nullable": true}, "address2": {"type": "string", "nullable": true}, "bill_to_account": {"type": "string", "nullable": true}, "bill_to_name": {"type": "string", "nullable": true}, "city": {"type": "string", "nullable": true}, "close_time": {"type": "string", "nullable": true}, "contact": {"type": "string", "nullable": true}, "country": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "freight_term": {"type": "string", "nullable": true}, "latitude": {"type": "number", "format": "double"}, "location_id": {"type": "integer", "format": "int64"}, "location_type": {"type": "integer", "format": "int32"}, "longitude": {"type": "number", "format": "double"}, "open_time": {"type": "string", "nullable": true}, "phone": {"type": "string", "nullable": true}, "quote_amount": {"type": "number", "format": "double", "nullable": true}, "quote_id": {"type": "string", "nullable": true}, "state": {"type": "string", "nullable": true}, "zip_code": {"type": "string", "nullable": true}}, "additionalProperties": false}, "RpcGetOrderPackageResponse": {"type": "object", "properties": {"freight_class": {"type": "number", "format": "double"}, "height": {"type": "number", "format": "double"}, "inner_count": {"type": "integer", "format": "int32"}, "is_over_load": {"type": "integer", "format": "int32"}, "is_over_size": {"type": "integer", "format": "int32"}, "item_count": {"type": "integer", "format": "int32"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/RpcGetOrderProduct"}, "nullable": true}, "length": {"type": "number", "format": "double"}, "linear": {"type": "number", "format": "double"}, "linear_uom": {"type": "string", "nullable": true}, "package_no": {"type": "string", "nullable": true}, "sequence": {"type": "integer", "format": "int32"}, "stackable": {"type": "integer", "format": "int32"}, "status": {"type": "integer", "format": "int32"}, "tracking_no": {"type": "string", "nullable": true}, "volume": {"type": "number", "format": "double"}, "volume_uom": {"type": "string", "nullable": true}, "weight": {"type": "number", "format": "double"}, "weight_uom": {"type": "string", "nullable": true}, "width": {"type": "number", "format": "double"}}, "additionalProperties": false}, "RpcGetOrderPalletResponse": {"type": "object", "properties": {"pallet_id": {"type": "integer", "format": "int64"}, "sequence": {"type": "integer", "format": "int32"}, "pallets_no": {"type": "string", "nullable": true}, "tracking_no": {"type": "string", "nullable": true}, "length": {"type": "number", "format": "double"}, "width": {"type": "number", "format": "double"}, "height": {"type": "number", "format": "double"}, "weight": {"type": "number", "format": "double"}, "weight_uom": {"type": "string", "nullable": true}, "freight_class": {"type": "number", "format": "double"}, "volume": {"type": "number", "format": "double"}, "volume_uom": {"type": "string", "nullable": true}, "status": {"type": "integer", "format": "int32"}, "stackable": {"type": "integer", "format": "int32"}, "inner_count": {"type": "integer", "format": "int32"}, "is_over_size": {"type": "integer", "format": "int32"}, "is_over_load": {"type": "integer", "format": "int32"}, "linear": {"type": "number", "format": "double"}, "linear_uom": {"type": "string", "nullable": true}, "item_count": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "RpcGetOrderProduct": {"type": "object", "properties": {"code": {"type": "string", "nullable": true}, "customer_pro": {"type": "string", "nullable": true}, "declared_value": {"type": "number", "format": "double"}, "description": {"type": "string", "nullable": true}, "freight_class": {"type": "number", "format": "double"}, "height": {"type": "number", "format": "double"}, "length": {"type": "number", "format": "double"}, "linear_uom": {"type": "string", "nullable": true}, "lot_no": {"type": "string", "nullable": true}, "nmfc": {"type": "string", "nullable": true}, "package_no": {"type": "string", "nullable": true}, "quantity": {"type": "integer", "format": "int32"}, "quantity_uom": {"type": "string", "nullable": true}, "shipment_order_no": {"type": "string", "nullable": true}, "sku": {"type": "string", "nullable": true}, "stackable": {"type": "integer", "format": "int32"}, "volume": {"type": "number", "format": "double"}, "volume_uom": {"type": "string", "nullable": true}, "weight": {"type": "number", "format": "double"}, "weight_uom": {"type": "string", "nullable": true}, "width": {"type": "number", "format": "double"}}, "additionalProperties": false}, "ShipmentOrderCreateFromEnum": {"enum": [1, 2, 3], "type": "integer", "format": "int32"}, "RpcGetOrdersRequest": {"type": "object", "properties": {"order_nos": {"type": "array", "items": {"type": "string"}, "nullable": true}, "is_package_and_pallet_no": {"type": "boolean"}}, "additionalProperties": false}, "RpcGetOrdersResponse": {"type": "object", "properties": {"acc_service_list": {"type": "array", "items": {"$ref": "#/components/schemas/RpcGetOrderAccServiceResponse"}, "nullable": true}, "actual_create_time": {"type": "string", "format": "date-time"}, "billing_to": {"$ref": "#/components/schemas/RpcGetOrderBill"}, "bol": {"type": "string", "nullable": true}, "business_client": {"type": "string", "nullable": true}, "company_code": {"type": "string", "nullable": true}, "consignee_address": {"$ref": "#/components/schemas/RpcGetOrderAddress"}, "consignee_terminal": {"type": "string", "nullable": true}, "create_from": {"type": "string", "nullable": true}, "current_location": {"type": "string", "nullable": true}, "current_transit_dest_terminal": {"type": "string", "nullable": true}, "current_transit_org_terminal": {"type": "string", "nullable": true}, "current_trip": {"type": "string", "nullable": true}, "dest_terminal": {"type": "string", "nullable": true}, "dpls": {"type": "integer", "format": "int32"}, "facility_id": {"type": "string", "nullable": true}, "foreign_master_order_id": {"type": "string", "nullable": true}, "foreign_order_id": {"type": "string", "nullable": true}, "freight_term": {"type": "string", "nullable": true}, "id": {"type": "integer", "format": "int64"}, "insurance_price": {"type": "number", "format": "double"}, "is_hold": {"type": "integer", "format": "int32"}, "is_need_delivery": {"type": "boolean"}, "is_need_linehaul": {"type": "boolean"}, "is_need_pickup": {"type": "boolean"}, "is_need_service": {"type": "boolean"}, "load_date": {"type": "string", "format": "date-time"}, "load_no": {"type": "string", "nullable": true}, "master_order_no": {"type": "string", "nullable": true}, "next_trip": {"type": "string", "nullable": true}, "note": {"type": "string", "nullable": true}, "order_no": {"type": "string", "nullable": true}, "order_status": {"type": "integer", "format": "int32"}, "order_sub_status": {"type": "integer", "format": "int32"}, "original_terminal": {"type": "string", "nullable": true}, "package_quantity": {"type": "integer", "format": "int32"}, "packages": {"type": "array", "items": {"$ref": "#/components/schemas/RpcGetOrderPackageResponse"}, "nullable": true}, "pallets": {"type": "array", "items": {"$ref": "#/components/schemas/RpcGetOrderPalletResponse"}, "nullable": true}, "po_no": {"type": "string", "nullable": true}, "post_date": {"type": "string", "format": "date-time"}, "pro_no": {"type": "string", "nullable": true}, "rate": {"type": "number", "format": "double"}, "reference": {"type": "string", "nullable": true}, "reference2": {"type": "string", "nullable": true}, "rma": {"type": "string", "nullable": true}, "service_level": {"type": "integer", "format": "int32"}, "service_terminal": {"type": "string", "nullable": true}, "shipments_type": {"type": "integer", "format": "int32"}, "shipper_address": {"$ref": "#/components/schemas/RpcGetOrderAddress"}, "shipper_terminal": {"type": "string", "nullable": true}, "tracking_no": {"type": "string", "nullable": true}, "estimate_freights": {"type": "array", "items": {"$ref": "#/components/schemas/RpcOrderEstimateFreightDto"}, "nullable": true}, "reference5": {"type": "string", "nullable": true}, "invoice_pro": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ShipmentOrderSourceTypeEnum": {"enum": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], "type": "integer", "format": "int32"}, "RpcShipmentOrderPalletWeightDto": {"type": "object", "properties": {"order_no": {"type": "string", "nullable": true}, "total_pallets": {"type": "integer", "format": "int32"}, "total_inner_pack_qty": {"type": "integer", "format": "int32"}, "total_weights": {"type": "number", "format": "double"}, "pallet_nos": {"type": "array", "items": {"type": "string"}, "nullable": true}, "package_nos": {"type": "array", "items": {"type": "string"}, "nullable": true}, "tms_revenue": {"type": "number", "format": "double"}, "total_spaces": {"type": "number", "format": "double"}, "rated_weight": {"type": "number", "format": "double"}}, "additionalProperties": false}, "ServiceLevelEnum": {"enum": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 30], "type": "integer", "format": "int32"}, "ShipmentTypeEnum": {"enum": [1, 2, 3, 4], "type": "integer", "format": "int32"}, "WorkOrderOutput": {"type": "object", "properties": {"order_no": {"type": "string", "nullable": true}, "order_status": {"$ref": "#/components/schemas/WorkOrderStatusEnum"}, "order_sub_status": {"$ref": "#/components/schemas/WorkOrderSubStatusEnum"}, "business_client": {"type": "string", "nullable": true}, "service_level": {"$ref": "#/components/schemas/ServiceLevelEnum"}, "reference": {"type": "string", "nullable": true}, "work_order_type": {"$ref": "#/components/schemas/WorkOrderTypeEnum"}, "master_order_no": {"type": "string", "nullable": true}, "rma": {"type": "string", "nullable": true}, "so_no": {"type": "string", "nullable": true}, "po_no": {"type": "string", "nullable": true}, "order_created_time": {"type": "string", "format": "date-time", "nullable": true}, "desired_service_date": {"type": "string", "format": "date-time", "nullable": true}, "service_appointment": {"type": "string", "format": "date-time", "nullable": true}, "service_complete_time": {"type": "string", "format": "date-time", "readOnly": true, "nullable": true}, "processing_days": {"$ref": "#/components/schemas/ProcessingDays"}, "hold": {"type": "boolean"}, "sales_order": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ProcessingDays": {"type": "object", "additionalProperties": false, "properties": {}}, "RecordStatus": {"enum": [0, 1], "type": "integer", "format": "int32"}, "RpcAccService": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "acc_code": {"type": "string", "nullable": true}, "quantity": {"type": "integer", "format": "int32"}, "description": {"type": "string", "nullable": true}, "uom": {"type": "string", "nullable": true}}, "additionalProperties": false}, "RpcBillingDto": {"type": "object", "properties": {"freight_term": {"type": "string", "nullable": true}, "bill_to_account": {"type": "string", "nullable": true}, "bill_to_name": {"type": "string", "nullable": true}, "quote_id": {"type": "string", "nullable": true}, "quote_amount": {"type": "number", "format": "double", "nullable": true}, "address1": {"type": "string", "nullable": true}, "address2": {"type": "string", "nullable": true}, "country": {"type": "string", "nullable": true}, "state": {"type": "string", "nullable": true}, "city": {"type": "string", "nullable": true}, "zip_code": {"type": "string", "nullable": true}, "location_id": {"type": "integer", "format": "int64"}, "location_name": {"type": "string", "nullable": true}, "contact": {"type": "string", "nullable": true}, "phone": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "latitude": {"type": "number", "format": "double"}, "longitude": {"type": "number", "format": "double"}, "open_time": {"type": "string", "nullable": true}, "close_time": {"type": "string", "nullable": true}, "location_type": {"type": "integer", "format": "int32"}, "order_key": {"type": "integer", "format": "int64"}, "location_code": {"type": "string", "nullable": true}}, "additionalProperties": false}, "RpcOrderConsigneeAddress": {"type": "object", "properties": {"mabd": {"type": "string", "format": "date-time"}, "delivery_max_times": {"type": "integer", "format": "int32"}, "desired_delivery_date": {"type": "string", "format": "date-time"}, "pod_name": {"type": "string", "nullable": true}, "deliverd_by": {"type": "string", "nullable": true}, "deliverd_name": {"type": "string", "nullable": true}, "deliverd_time": {"type": "string", "format": "date-time"}, "deliverd_location": {"type": "string", "nullable": true}, "terminal": {"type": "string", "nullable": true}, "terminal_address1": {"type": "string", "nullable": true}, "terminal_address2": {"type": "string", "nullable": true}, "terminal_city": {"type": "string", "nullable": true}, "terminal_state": {"type": "string", "nullable": true}, "terminal_zipcode": {"type": "string", "nullable": true}, "terminal_lat": {"type": "number", "format": "double"}, "terminal_lng": {"type": "number", "format": "double"}, "polygon_id": {"type": "string", "nullable": true}, "polygon_code": {"type": "string", "nullable": true}, "note": {"type": "string", "nullable": true}, "appointment_date": {"type": "string", "format": "date-time"}, "appointment_time_from": {"type": "string", "format": "date-time"}, "appointment_time_to": {"type": "string", "format": "date-time"}, "requre_appt": {"type": "integer", "format": "int32"}, "appointment_no": {"type": "string", "nullable": true}, "puZoneCode": {"type": "string", "nullable": true}, "address1": {"type": "string", "nullable": true}, "address2": {"type": "string", "nullable": true}, "country": {"type": "string", "nullable": true}, "state": {"type": "string", "nullable": true}, "city": {"type": "string", "nullable": true}, "zip_code": {"type": "string", "nullable": true}, "location_id": {"type": "integer", "format": "int64"}, "location_name": {"type": "string", "nullable": true}, "contact": {"type": "string", "nullable": true}, "phone": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "latitude": {"type": "number", "format": "double"}, "longitude": {"type": "number", "format": "double"}, "open_time": {"type": "string", "nullable": true}, "close_time": {"type": "string", "nullable": true}, "location_type": {"type": "integer", "format": "int32"}, "order_key": {"type": "integer", "format": "int64"}, "location_code": {"type": "string", "nullable": true}}, "additionalProperties": false}, "RpcOrderItem": {"type": "object", "properties": {"package_id": {"type": "integer", "format": "int64"}, "code": {"type": "string", "nullable": true}, "customer_pro": {"type": "string", "nullable": true}, "declared_value": {"type": "number", "format": "double"}, "description": {"type": "string", "nullable": true}, "freight_class": {"type": "number", "format": "double"}, "height": {"type": "number", "format": "double"}, "length": {"type": "number", "format": "double"}, "nmfc": {"type": "string", "nullable": true}, "package_no": {"type": "string", "nullable": true}, "quantity": {"type": "integer", "format": "int32"}, "quantity_uom": {"type": "string", "nullable": true}, "shipment_order_no": {"type": "string", "nullable": true}, "linear_uom": {"type": "string", "nullable": true}, "stackable": {"type": "integer", "format": "int32"}, "volume": {"type": "number", "format": "double"}, "volume_uom": {"type": "string", "nullable": true}, "weight": {"type": "number", "format": "double"}, "weight_uom": {"type": "string", "nullable": true}, "width": {"type": "number", "format": "double"}, "sku": {"type": "string", "nullable": true}, "lot_no": {"type": "string", "nullable": true}}, "additionalProperties": false}, "RpcOrderPackages": {"type": "object", "properties": {"package_id": {"type": "integer", "format": "int64"}, "sequence": {"type": "integer", "format": "int32"}, "package_no": {"type": "string", "nullable": true}, "tracking_no": {"type": "string", "nullable": true}, "length": {"type": "number", "format": "double"}, "width": {"type": "number", "format": "double"}, "height": {"type": "number", "format": "double"}, "weight": {"type": "number", "format": "double"}, "weight_uom": {"type": "string", "nullable": true}, "freight_class": {"type": "number", "format": "double"}, "volume": {"type": "number", "format": "double"}, "volume_uom": {"type": "string", "nullable": true}, "status": {"type": "integer", "format": "int32"}, "stackable": {"type": "integer", "format": "int32"}, "inner_count": {"type": "integer", "format": "int32"}, "is_over_size": {"type": "integer", "format": "int32"}, "is_over_load": {"type": "integer", "format": "int32"}, "linear": {"type": "number", "format": "double"}, "linear_uom": {"type": "string", "nullable": true}, "item_count": {"type": "integer", "format": "int32"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/RpcOrderItem"}, "nullable": true}}, "additionalProperties": false}, "ShipmentOrderOsdListRequest": {"type": "object", "properties": {"package_id": {"type": "integer", "format": "int64"}, "order_no": {"type": "string", "nullable": true}}, "additionalProperties": false}, "RpcOrderPallets": {"type": "object", "properties": {"pallet_id": {"type": "integer", "format": "int64"}, "sequence": {"type": "integer", "format": "int32"}, "pallets_no": {"type": "string", "nullable": true}, "tracking_no": {"type": "string", "nullable": true}, "length": {"type": "number", "format": "double"}, "width": {"type": "number", "format": "double"}, "height": {"type": "number", "format": "double"}, "weight": {"type": "number", "format": "double"}, "weight_uom": {"type": "string", "nullable": true}, "freight_class": {"type": "number", "format": "double"}, "volume": {"type": "number", "format": "double"}, "volume_uom": {"type": "string", "nullable": true}, "status": {"type": "integer", "format": "int32"}, "stackable": {"type": "integer", "format": "int32"}, "inner_count": {"type": "integer", "format": "int32"}, "is_over_size": {"type": "integer", "format": "int32"}, "is_over_load": {"type": "integer", "format": "int32"}, "linear": {"type": "number", "format": "double"}, "linear_uom": {"type": "string", "nullable": true}, "item_count": {"type": "integer", "format": "int32"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/RpcOrderItem"}, "nullable": true}, "dock_location_name": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ShipmentOrderPackageStatusEnum": {"enum": [10, 12, 21, 22, 23, 24, 25, 30, 32, 34, 40, 42, 43, 45, 50, 51, 52, 55, 71, 72, 73, 74, 75, 76, 77, 78], "type": "integer", "format": "int32"}, "RpcOrderShipperAddress": {"type": "object", "properties": {"request_pickup_date": {"type": "string", "format": "date-time"}, "request_pickup_time_begin": {"type": "string", "format": "date-time"}, "request_pickup_time_end": {"type": "string", "format": "date-time"}, "terminal": {"type": "string", "nullable": true}, "terminal_address1": {"type": "string", "nullable": true}, "terminal_address2": {"type": "string", "nullable": true}, "terminal_city": {"type": "string", "nullable": true}, "terminal_state": {"type": "string", "nullable": true}, "terminal_zipcode": {"type": "string", "nullable": true}, "terminal_lat": {"type": "number", "format": "double"}, "terminal_lng": {"type": "number", "format": "double"}, "polygon_id": {"type": "string", "nullable": true}, "polygon_code": {"type": "string", "nullable": true}, "note": {"type": "string", "nullable": true}, "appointment_date": {"type": "string", "format": "date-time"}, "appointment_time_from": {"type": "string", "format": "date-time"}, "appointment_time_to": {"type": "string", "format": "date-time"}, "appointment_no": {"type": "string", "nullable": true}, "puZoneCode": {"type": "string", "nullable": true}, "address1": {"type": "string", "nullable": true}, "address2": {"type": "string", "nullable": true}, "country": {"type": "string", "nullable": true}, "state": {"type": "string", "nullable": true}, "city": {"type": "string", "nullable": true}, "zip_code": {"type": "string", "nullable": true}, "location_id": {"type": "integer", "format": "int64"}, "location_name": {"type": "string", "nullable": true}, "contact": {"type": "string", "nullable": true}, "phone": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "latitude": {"type": "number", "format": "double"}, "longitude": {"type": "number", "format": "double"}, "open_time": {"type": "string", "nullable": true}, "close_time": {"type": "string", "nullable": true}, "location_type": {"type": "integer", "format": "int32"}, "order_key": {"type": "integer", "format": "int64"}, "location_code": {"type": "string", "nullable": true}}, "additionalProperties": false}, "RpcShipmentOrderDto": {"type": "object", "properties": {"shipment_order_id": {"type": "integer", "format": "int64"}, "shipment_order_no": {"type": "string", "nullable": true}, "order_no": {"type": "string", "nullable": true}, "note": {"type": "string", "nullable": true}, "order_status": {"type": "integer", "format": "int32"}, "order_sub_status": {"type": "integer", "format": "int32"}, "business_client": {"type": "string", "nullable": true}, "facility_id": {"type": "string", "nullable": true}, "master_order_no": {"type": "string", "nullable": true}, "company_id": {"type": "integer", "format": "int32"}, "company_code": {"type": "string", "nullable": true}, "shipments_type": {"type": "integer", "format": "int32"}, "shipment_type": {"type": "integer", "format": "int32"}, "service_level": {"type": "integer", "format": "int32"}, "shipper_terminal": {"type": "string", "nullable": true}, "consignee_terminal": {"type": "string", "nullable": true}, "org_terminal": {"type": "string", "nullable": true}, "dest_terminal": {"type": "string", "nullable": true}, "package_quantity": {"type": "integer", "format": "int32"}, "acc_service_list": {"type": "array", "items": {"$ref": "#/components/schemas/RpcAccService"}, "nullable": true}, "shipper_address": {"$ref": "#/components/schemas/RpcOrderShipperAddress"}, "consignee_address": {"$ref": "#/components/schemas/RpcOrderConsigneeAddress"}, "is_need_delivery": {"type": "boolean"}, "is_need_pickup": {"type": "boolean"}, "is_need_linehaul": {"type": "boolean"}, "is_need_service": {"type": "boolean"}, "billing_to": {"$ref": "#/components/schemas/RpcBillingDto"}, "packages": {"type": "array", "items": {"$ref": "#/components/schemas/RpcOrderPackages"}, "nullable": true}, "pallets": {"type": "array", "items": {"$ref": "#/components/schemas/RpcOrderPallets"}, "nullable": true}, "bol": {"type": "string", "nullable": true}, "bol_no": {"type": "string", "nullable": true}, "load_no": {"type": "string", "nullable": true}, "load_date": {"type": "string", "format": "date-time"}, "post_date": {"type": "string", "format": "date-time"}, "rate": {"type": "number", "format": "double"}, "dpls": {"type": "integer", "format": "int32"}, "po_no": {"type": "string", "nullable": true}, "rma": {"type": "string", "nullable": true}, "insurance_price": {"type": "number", "format": "double"}, "current_trip": {"type": "string", "nullable": true}, "next_trip": {"type": "string", "nullable": true}, "current_transit_org_terminal": {"type": "string", "nullable": true}, "current_transit_dest_terminal": {"type": "string", "nullable": true}, "tracking_no": {"type": "string", "nullable": true}, "dispatched_order_no": {"type": "string", "nullable": true}, "freight_term": {"type": "string", "nullable": true}, "create_from": {"$ref": "#/components/schemas/ShipmentOrderCreateFromEnum"}, "actual_create_time": {"type": "string", "format": "date-time"}, "service_terminal": {"type": "string", "nullable": true}, "is_hold": {"type": "integer", "format": "int32"}, "foreign_master_order_id": {"type": "string", "nullable": true}, "reference": {"type": "string", "nullable": true}, "reference2": {"type": "string", "nullable": true}, "pro_no": {"type": "string", "nullable": true}, "current_location": {"type": "string", "nullable": true}, "invoice_pro": {"type": "string", "nullable": true}, "invoice_pro_prefix": {"type": "string", "nullable": true}, "estimate_freights": {"type": "array", "items": {"$ref": "#/components/schemas/RpcOrderEstimateFreightDto"}, "nullable": true}, "total_weight": {"type": "number", "format": "double"}, "total_volume": {"type": "number", "format": "double"}, "source": {"$ref": "#/components/schemas/ShipmentOrderSourceTypeEnum"}, "reference3": {"type": "string", "nullable": true}, "reference4": {"type": "string", "nullable": true}, "reference5": {"type": "string", "nullable": true}, "reference6": {"type": "string", "nullable": true}}, "additionalProperties": false}, "RpcShipmentOrderListRequest": {"type": "object", "properties": {"order_nos": {"type": "array", "items": {"type": "string"}, "nullable": true}, "tracking_or_pro_nos": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "ShipmentOrderActualFreightInfoDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "package_no_or_pallet_no": {"type": "string", "nullable": true}, "show_pkg_or_pallet": {"type": "integer", "format": "int32"}, "current_location": {"type": "string", "nullable": true}, "exception": {"type": "string", "nullable": true}, "pick_up_quantity": {"type": "integer", "format": "int32"}, "quantity_uom": {"type": "string", "nullable": true}, "length": {"type": "number", "format": "double"}, "width": {"type": "number", "format": "double"}, "height": {"type": "number", "format": "double"}, "weight": {"type": "number", "format": "double"}, "delivery_qty": {"type": "integer", "format": "int32"}, "dim_weight": {"type": "number", "format": "double"}, "class": {"type": "number", "format": "double"}, "shipment_type": {"type": "integer", "format": "int32"}, "is_del": {"type": "boolean"}, "dock_location_name": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ShipmentOrderActualFreightInfoV2Dto": {"type": "object", "properties": {"order_no": {"type": "string", "nullable": true}, "tracking_no": {"type": "string", "nullable": true}, "pickup_qty": {"type": "integer", "format": "int32"}, "delivered_qty": {"type": "integer", "format": "int32"}, "uom": {"type": "string", "nullable": true}, "current_location": {"type": "string", "nullable": true}, "exception": {"type": "string", "nullable": true}, "length": {"type": "number", "format": "double"}, "width": {"type": "number", "format": "double"}, "height": {"type": "number", "format": "double"}, "weight": {"type": "number", "format": "double"}, "dim_weight": {"type": "number", "format": "double"}, "package_class": {"type": "number", "format": "double"}, "last_update": {"type": "string", "nullable": true}, "last_update_date": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "ShipmentOrderActualFreightInfoV2DtoListResultDto": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/ShipmentOrderActualFreightInfoV2Dto"}, "nullable": true}}, "additionalProperties": false}, "ShipmentOrderConsigneeEntity": {"type": "object", "properties": {"address1": {"type": "string", "nullable": true}, "address2": {"type": "string", "nullable": true}, "city": {"type": "string", "nullable": true}, "close_time": {"type": "string", "nullable": true}, "country": {"type": "string", "nullable": true}, "delivered_by": {"type": "string", "nullable": true}, "delivered_location": {"type": "string", "nullable": true}, "delivered_time": {"type": "string", "format": "date-time"}, "appointment_date": {"type": "string", "format": "date-time"}, "appointment_time_from": {"type": "string", "format": "date-time"}, "appointment_time_to": {"type": "string", "format": "date-time"}, "delivery_max_times": {"type": "integer", "format": "int32"}, "desired_delivery_date": {"type": "string", "format": "date-time"}, "latitude": {"type": "number", "format": "double"}, "location_id": {"type": "integer", "format": "int32"}, "location_name": {"type": "string", "nullable": true}, "location_type": {"type": "integer", "format": "int32"}, "longitude": {"type": "number", "format": "double"}, "mabd": {"type": "string", "format": "date-time"}, "mabd_from": {"type": "string", "format": "date-time"}, "mabd_to": {"type": "string", "format": "date-time"}, "master_order_no": {"type": "string", "nullable": true}, "note": {"type": "string", "nullable": true}, "open_time": {"type": "string", "nullable": true}, "pod_name": {"type": "string", "nullable": true}, "primary_contact_email": {"type": "string", "nullable": true}, "primary_contact_name": {"type": "string", "nullable": true}, "primary_contact_phone": {"type": "string", "nullable": true}, "secondary_contact_email": {"type": "string", "nullable": true}, "secondary_contact_name": {"type": "string", "nullable": true}, "secondary_contact_phone": {"type": "string", "nullable": true}, "state": {"type": "string", "nullable": true}, "zipcode": {"type": "string", "nullable": true}, "terminal": {"type": "string", "nullable": true}, "terminal_address1": {"type": "string", "nullable": true}, "terminal_address2": {"type": "string", "nullable": true}, "terminal_city": {"type": "string", "nullable": true}, "terminal_lng": {"type": "number", "format": "double"}, "terminal_state": {"type": "string", "nullable": true}, "terminal_lat": {"type": "number", "format": "double"}, "terminal_zipcode": {"type": "string", "nullable": true}, "validation_result": {"type": "string", "nullable": true}, "polygon_id": {"type": "string", "nullable": true}, "polygon_code": {"type": "string", "nullable": true}, "terminal_location_id": {"type": "integer", "format": "int64"}, "contact_id": {"type": "string", "nullable": true}, "require_appt": {"type": "integer", "format": "int32"}, "appointment_no": {"type": "string", "nullable": true}, "pu_zone_code": {"type": "string", "nullable": true}, "location_code": {"type": "string", "nullable": true}, "order_no": {"type": "string", "nullable": true}, "order_key": {"type": "integer", "format": "int64"}}, "additionalProperties": false}, "ShipmentOrderFilesDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "shipment_order_no": {"type": "string", "nullable": true}, "file_type": {"type": "string", "nullable": true}, "file_url": {"type": "string", "nullable": true}, "rotation_angle": {"type": "integer", "format": "int32"}, "preview_url": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ShipmentOrderGetListRequest": {"type": "object", "properties": {"bill_to_accounts": {"type": "array", "items": {"type": "string"}, "nullable": true}, "bols": {"type": "array", "items": {"type": "string"}, "nullable": true}, "po_nos": {"type": "array", "items": {"type": "string"}, "nullable": true}, "business_client": {"type": "string", "nullable": true}, "company_codes": {"type": "array", "items": {"type": "string"}, "nullable": true}, "consignee_city": {"type": "string", "nullable": true}, "consignee_name": {"type": "string", "nullable": true}, "consignee_state": {"type": "array", "items": {"type": "string"}, "nullable": true}, "consignee_terminals": {"type": "array", "items": {"type": "string"}, "nullable": true}, "consignee_zip_codes": {"type": "array", "items": {"type": "string"}, "nullable": true}, "current_locations": {"type": "array", "items": {"type": "string"}, "nullable": true}, "customer_references": {"type": "array", "items": {"type": "string"}, "nullable": true}, "delayed": {"type": "boolean", "nullable": true}, "delivery_appointment": {"type": "array", "items": {"type": "string", "format": "date-time"}, "nullable": true}, "delivery_date": {"type": "array", "items": {"type": "string", "format": "date-time"}, "nullable": true}, "desired_delivery_date": {"type": "array", "items": {"type": "string", "format": "date-time"}, "nullable": true}, "exception": {"type": "boolean", "nullable": true}, "lh_eta_date": {"type": "array", "items": {"type": "string", "format": "date-time"}, "nullable": true}, "lh_etd_date": {"type": "array", "items": {"type": "string", "format": "date-time"}, "nullable": true}, "lhs": {"type": "array", "items": {"type": "string"}, "nullable": true}, "master_order_ids": {"type": "array", "items": {"type": "integer", "format": "int64"}, "nullable": true}, "order_nos": {"type": "array", "items": {"type": "string"}, "nullable": true}, "origin_states": {"type": "array", "items": {"type": "string"}, "nullable": true}, "origin_zip_codes": {"type": "array", "items": {"type": "string"}, "nullable": true}, "pickup_appointment": {"type": "array", "items": {"type": "string", "format": "date-time"}, "nullable": true}, "pickup_complete_date": {"type": "array", "items": {"type": "string", "format": "date-time"}, "nullable": true}, "record_status": {"$ref": "#/components/schemas/RecordStatus"}, "request_pickup_date": {"type": "array", "items": {"type": "string", "format": "date-time"}, "nullable": true}, "route_plan": {"type": "string", "nullable": true}, "service_levels": {"type": "array", "items": {"$ref": "#/components/schemas/ServiceLevelEnum"}, "nullable": true}, "service_terminals": {"type": "array", "items": {"type": "string"}, "nullable": true}, "shipment_types": {"type": "array", "items": {"$ref": "#/components/schemas/ShipmentTypeEnum"}, "nullable": true}, "shipper_city": {"type": "string", "nullable": true}, "shipper_name": {"type": "string", "nullable": true}, "shipper_terminals": {"type": "array", "items": {"type": "string"}, "nullable": true}, "status": {"type": "array", "items": {"$ref": "#/components/schemas/ShipmentOrderStatusEnum"}, "nullable": true}, "sub_status": {"type": "array", "items": {"$ref": "#/components/schemas/ShipmentOrderSubStatusEnum"}, "nullable": true}, "tracking_nos": {"type": "array", "items": {"type": "string"}, "nullable": true}, "trips": {"type": "array", "items": {"type": "string"}, "nullable": true}, "pu_nos": {"type": "array", "items": {"type": "string"}, "nullable": true}, "sorting": {"type": "string", "nullable": true}, "page_number": {"maximum": 2147483647, "minimum": 0, "type": "integer", "format": "int32"}, "page_size": {"maximum": 2147483647, "minimum": 1, "type": "integer", "format": "int32"}}, "additionalProperties": false}, "ShipmentOrderShipperEntity": {"type": "object", "properties": {"address1": {"type": "string", "nullable": true}, "address2": {"type": "string", "nullable": true}, "city": {"type": "string", "nullable": true}, "close_time": {"type": "string", "nullable": true}, "country": {"type": "string", "nullable": true}, "latitude": {"type": "number", "format": "double"}, "location_id": {"type": "integer", "format": "int32"}, "location_name": {"type": "string", "nullable": true}, "location_type": {"type": "integer", "format": "int32"}, "longitude": {"type": "number", "format": "double"}, "master_order_no": {"type": "string", "nullable": true}, "note": {"type": "string", "nullable": true}, "open_time": {"type": "string", "nullable": true}, "primary_contact_email": {"type": "string", "nullable": true}, "primary_contact_name": {"type": "string", "nullable": true}, "primary_contact_phone": {"type": "string", "nullable": true}, "request_pickup_date": {"type": "string", "format": "date-time"}, "request_pickup_time_begin": {"type": "string", "format": "date-time"}, "request_pickup_time_end": {"type": "string", "format": "date-time"}, "secondary_contact_email": {"type": "string", "nullable": true}, "secondary_contact_name": {"type": "string", "nullable": true}, "secondary_contact_phone": {"type": "string", "nullable": true}, "state": {"type": "string", "nullable": true}, "zipcode": {"type": "string", "nullable": true}, "terminal": {"type": "string", "nullable": true}, "terminal_address1": {"type": "string", "nullable": true}, "terminal_address2": {"type": "string", "nullable": true}, "terminal_city": {"type": "string", "nullable": true}, "terminal_zipcode": {"type": "string", "nullable": true}, "terminal_state": {"type": "string", "nullable": true}, "terminal_lng": {"type": "number", "format": "double"}, "terminal_lat": {"type": "number", "format": "double"}, "validation_result": {"type": "string", "nullable": true}, "polygon_id": {"type": "string", "nullable": true}, "polygon_code": {"type": "string", "nullable": true}, "terminal_location_id": {"type": "integer", "format": "int64"}, "appointment_date": {"type": "string", "format": "date-time"}, "appointment_time_from": {"type": "string", "format": "date-time"}, "appointment_time_to": {"type": "string", "format": "date-time"}, "contact_id": {"type": "string", "nullable": true}, "appointment_no": {"type": "string", "nullable": true}, "pu_zone_code": {"type": "string", "nullable": true}, "location_code": {"type": "string", "nullable": true}, "order_no": {"type": "string", "nullable": true}, "order_key": {"type": "integer", "format": "int64"}}, "additionalProperties": false}, "ShipmentOrderListItemDto": {"type": "object", "properties": {"actual_transit_days": {"type": "integer", "format": "int32", "readOnly": true}, "company_id": {"type": "integer", "format": "int64"}, "consignee_terminal": {"type": "string", "nullable": true}, "current_location": {"type": "string", "nullable": true}, "delayed": {"type": "boolean", "nullable": true}, "delivery_date": {"type": "string", "format": "date-time"}, "pickup_complete_date": {"type": "string", "format": "date-time"}, "exception": {"type": "string", "nullable": true}, "expected_transit_days": {"type": "integer", "format": "int32"}, "order_no": {"type": "string", "nullable": true}, "order_status": {"$ref": "#/components/schemas/ShipmentOrderStatusEnum"}, "service_level": {"$ref": "#/components/schemas/ServiceLevelEnum"}, "service_terminal": {"type": "string", "nullable": true}, "shipment_order_id": {"type": "integer", "format": "int64"}, "shipment_type": {"$ref": "#/components/schemas/ShipmentTypeEnum"}, "shipper_terminal": {"type": "string", "nullable": true}, "tracking_no": {"type": "string", "nullable": true}, "reference5": {"type": "string", "nullable": true}, "update_time": {"type": "string", "format": "date-time", "nullable": true}, "bill_to_name": {"type": "string", "nullable": true}, "shipper": {"$ref": "#/components/schemas/ShipmentOrderShipperEntity"}, "consignee": {"$ref": "#/components/schemas/ShipmentOrderConsigneeEntity"}}, "additionalProperties": false}, "ShipmentOrderListItemDtoPagedResultDto": {"type": "object", "properties": {"total_count": {"type": "integer", "format": "int64"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/ShipmentOrderListItemDto"}, "nullable": true}}, "additionalProperties": false}, "ShipmentOrderShipperAppointmentDtoNew": {"type": "object", "properties": {"tracking_no": {"type": "string", "nullable": true}, "shipment_order_no": {"type": "string", "nullable": true}, "appointment_time_from": {"type": "string", "nullable": true}, "appointment_time_to": {"type": "string", "nullable": true}, "appointment_date": {"type": "string", "format": "date-time", "nullable": true}, "confirmed_by_shipper": {"type": "integer", "format": "int32"}, "confirmed_by_contractor": {"type": "integer", "format": "int32"}, "open_time": {"type": "string", "nullable": true}, "close_time": {"type": "string", "nullable": true}, "shipper_name": {"type": "string", "nullable": true}, "address": {"type": "string", "nullable": true}, "request_pickup_date": {"type": "string", "format": "date-time", "nullable": true}, "reference": {"type": "string", "nullable": true}, "sales_order": {"type": "string", "nullable": true}, "purchase_order": {"type": "string", "nullable": true}, "load": {"type": "string", "nullable": true}, "bol": {"type": "string", "nullable": true}, "master_order": {"type": "string", "nullable": true}, "work_order": {"type": "string", "nullable": true}, "total_freight": {"type": "string", "nullable": true}, "order_status": {"type": "string", "nullable": true}, "appointtment_status": {"$ref": "#/components/schemas/ShipmentOrderShipperAppointmentStatusDto"}, "shipment_order_appointment_status": {"type": "integer", "format": "int32"}, "carriers": {"type": "array", "items": {"$ref": "#/components/schemas/AppointmentCarrierInfo"}, "nullable": true}, "shipment_order_id": {"type": "integer", "format": "int64"}, "tripno_or_taskno": {"type": "integer", "format": "int64"}}, "additionalProperties": false}, "ShipmentOrderShipperAppointmentStatusDto": {"type": "object", "properties": {"appointment_id": {"type": "integer", "format": "int64"}, "create_time": {"type": "string", "format": "date-time", "nullable": true}, "order_status": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "ShipmentOrderStatusEnum": {"enum": ["New", "Pickup Confirmed", "Out For Pickup", "Pickup Complete", "Pending Linehaul", "Line<PERSON>ul", "Pending Delivery", "Out For Delivery", "Delivered", "Partial Delivered", "Pending Service", "Delivered With Exception", "Delivery Failed", "Consignee Refusal", "Cancelled", "Incomplete", "Return To Shipper"], "type": "string"}, "ShipmentOrderSubStatusEnum": {"enum": ["NotValid", "New", "Package Ready", "Pending Pickup", "Pickup Dispatched", "Out For Pickup", "Departed Pickup Location", "Pickup Complete", "DirectlyPickupOffload", "Pending Linehaul", "<PERSON><PERSON><PERSON> Dispatched", "Linehaul Loaded", "Linehaul In Transit", "In Transit", "Complete", "Pending Dispatch", "Dispatched", "Out For Delivery", "Delivery", "Partial Delivered", "Pending Service", "Delivered with Exception", "Delivery Failed", "Consignee Refusal", "Cancelled", "Incomplete", "Return To Shipper"], "type": "string"}, "ShowOSDDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "osd_code": {"type": "string", "nullable": true}, "shipment_order_no": {"type": "string", "nullable": true}, "tracking_no": {"type": "string", "nullable": true}, "osd_type": {"type": "string", "nullable": true}, "sub_type": {"type": "string", "nullable": true}, "report_user": {"type": "string", "nullable": true}, "report_time": {"type": "string", "format": "date-time"}, "osd_note": {"type": "string", "nullable": true}, "osd_files": {"type": "string", "nullable": true}, "pallet_no": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StringFmsSelectListItem": {"type": "object", "properties": {"text": {"type": "string", "nullable": true}, "value": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StringFmsSelectListItemListResultDto": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/StringFmsSelectListItem"}, "nullable": true}}, "additionalProperties": false}, "WorkOrderStatusEnum": {"enum": [1000, 1100, 1200, 1300, 1400, 1500, 1600, 1700, 1800, 1900, 2000], "type": "integer", "format": "int32"}, "WorkOrderSubStatusEnum": {"enum": [1000, 1100, 1210, 1211, 1300, 1400, 1500, 1600, 1700, 1800, 1900, 2000], "type": "integer", "format": "int32"}, "WorkOrderTypeEnum": {"enum": [0, 1], "type": "integer", "format": "int32"}}}}