import type { IClientUserContextManager } from './IClientUserContextManager';
import type { UserContext, SessionContext, WmsUserInfo, Facility } from '@/utils/clientUserContext';

export class WmsClientUserContextManager implements IClientUserContextManager {
  private static wmsInstance: WmsClientUserContextManager;

  public constructor() {
    // 可在此处添加WMS站点的特殊初始化逻辑
  }

  public static getInstance(): WmsClientUserContextManager {
    if (!WmsClientUserContextManager.wmsInstance) {
      WmsClientUserContextManager.wmsInstance = new WmsClientUserContextManager();
    }
    return WmsClientUserContextManager.wmsInstance;
  }

  getAuthToken(): string | null {
    // 从 localStorage 获取 token
    try {
      return localStorage.getItem('token');
    } catch {
      return null;
    }
  }

  setAuthToken(token: string): void {}
  clearAuthToken(): void {}

  getAuthHeaders(): Record<string, string> {
    // 与 DefaultClientUserContextManager 保持一致
    const token = this.getAuthToken();
    console.log('WmsClientUserContextManager getAuthHeaders token', token);
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };
    if (token) {
      headers['Authorization'] = token.startsWith('Bearer ') ? token : `Bearer ${token}`;
    }
    return headers;
  }

  async initUserSession(userId: string, username: string, email?: string, tenant?: string, facility?: string): Promise<UserContext> { return { session: {} as SessionContext, preferences: {} }; }
  async addWmsUserInfo(userId: string, wmsUserInfo: WmsUserInfo): Promise<boolean> { return false; }
  setCurrentFacility(userId: string, facilityId: string): boolean { return false; }
  getUserFacilities(userId: string): Facility[] { return []; }

  getCurrentFacility(userId: string): Facility | undefined {
    // 从 localStorage 的 defaultFacility 获取，json格式
    try {
      const facilityStr = localStorage.getItem('defaultFacility');
      if (!facilityStr) return undefined;
      const facility = JSON.parse(facilityStr);
      return facility || undefined;
    } catch {
      return undefined;
    }
  }

  async getUserContext(userId: string): Promise<UserContext | null> { return null; }
  updateSessionContext(userId: string, updates: Partial<SessionContext>): SessionContext | undefined { return undefined; }
  clearUserSession(userId: string): void {}
  getUserSession(userId: string): SessionContext | undefined { return undefined; }
  updateWmsUserInfo(userId: string, wmsUserInfo: WmsUserInfo): boolean { return false; }
  getWmsUserInfo(): any | null {
    // 返回 { tenantId: ... } 结构
    const tenantId = this.getCurrentTenantId();
    return { "tenantId": tenantId };
  }
  getWmsUserInfoByUserId(userId: string): WmsUserInfo | null { return null; }

  getCurrentTenantId(): string | null {
    // 从 localStorage 的 defaultCompanyId 获取
    try {
      return localStorage.getItem('defaultCompanyId');
    } catch {
      return null;
    }
  }

  getCurrentFacilityId(): string | null {
    // 从 localStorage 的 defaultFacility.id 获取
    try {
      const facilityStr = localStorage.getItem('defaultFacility');
      if (!facilityStr) return null;
      const facility = JSON.parse(facilityStr);
      return facility?.id || null;
    } catch {
      return null;
    }
  }

  getIAMUserInfo(): any | null { return null; }
  getUserRoleNames(): string[] { return []; }
  getBiToken(): string | null { return null; }
} 