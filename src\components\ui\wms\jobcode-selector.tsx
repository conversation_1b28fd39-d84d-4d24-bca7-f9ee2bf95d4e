'use client';

import * as React from 'react';
import { useState, useEffect, useRef } from 'react';
import { Tag, Search, Loader2 } from 'lucide-react';
// @ts-ignore 忽略类型检查
import { debounce } from 'lodash';
import { wmsApi } from '@/utils/wmsApiClient';

import { Input } from '@/components/ui/input';
import { cn } from '@/lib/utils';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
  getCyberAgentPortalContainer,
} from "@/components/ui/select";

interface JobCodeOption {
  id: string;
  jobCode: string;
  description?: string;
}

interface JobCodeSelectorProps {
  value?: string;
  onChange: (value: string, jobCodeData?: JobCodeOption) => void;
  placeholder?: string;
  disabled?: boolean;
  required?: boolean;
  customerId?: string;
  apiHeaders?: Record<string, string>;
  defaultValue?: string;
}

// 定义WMS API响应类型
interface WmsApiResponse {
  success: boolean;
  msg?: string;
  data?: any[] | {
    list?: any[];
    [key: string]: any;
  };
  [key: string]: any;
}

export function JobCodeSelector({
  value,
  onChange,
  placeholder = 'Select job code',
  disabled = false,
  required = false,
  customerId,
  apiHeaders = {},
  defaultValue
}: JobCodeSelectorProps) {
  // Fixed API path
  const API_PATH = 'wms-bam/billing/get-account-items';
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [options, setOptions] = useState<JobCodeOption[]>([]);
  const [selectedJobCode, setSelectedJobCode] = useState<JobCodeOption | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const searchInputRef = useRef<HTMLInputElement>(null);
  const initialLoadRef = useRef(false);

  // 初始化时如果有defaultValue但没有value，则加载defaultValue对应的JobCode
  useEffect(() => {
    if (!initialLoadRef.current && defaultValue && !value) {
      initialLoadRef.current = true;
      fetchJobCodeByCode(defaultValue).then(jobCode => {
        if (jobCode) {
          onChange(jobCode.jobCode, jobCode);
        }
      });
    }
  }, [defaultValue, value]);

  // 当value变化时，如果已有选中的JobCode与新value不同，清除已选JobCode数据
  useEffect(() => {
    if (value !== selectedJobCode?.jobCode) {
      setSelectedJobCode(null);
    }
  }, [value]);

  // 如果有value但没有selectedJobCode，尝试获取JobCode数据
  useEffect(() => {
    if (value && !selectedJobCode && !disabled) {
      fetchJobCodeByCode(value);
    }
  }, [value, selectedJobCode, disabled, customerId]);

  // 当下拉框打开时自动聚焦搜索框
  useEffect(() => {
    if (open && searchInputRef.current) {
      setTimeout(() => {
        searchInputRef.current?.focus();
      }, 100);
    }
  }, [open]);

  // 提取JobCode数据的函数
  const extractJobCodeData = (jobCode: any): JobCodeOption => {
    return {
      id: jobCode.AccountItem || '',
      jobCode: jobCode.AccountItem || 'Unknown Job Code',
      description: jobCode.Description || ''
    };
  };

  // 解析API响应的函数
  const parseApiResponse = (response: any): any[] => {
    // 检查response.data是数组的情况
    if (Array.isArray(response.data)) {
      return response.data;
    }

    // 检查response.data.list的情况
    if (response.data && Array.isArray(response.data.list)) {
      return response.data.list;
    }

    // 检查response.data是对象但包含data字段的情况
    if (response.data && response.data.data && Array.isArray(response.data.data)) {
      return response.data.data;
    }

    // 直接检查response是否包含data字段为数组的情况
    if (response.data && Array.isArray(response.data)) {
      return response.data;
    }

    return [];
  };

  // 通过Code获取JobCode信息
  const fetchJobCodeByCode = async (jobCodeValue: string): Promise<JobCodeOption | null> => {
    try {
      setLoading(true);

      const searchParams: any = {
        jobCodeLike: jobCodeValue,
        currentPage: 1,
        pageSize: 20
      };

      // 如果有客户ID，添加到搜索参数
      if (customerId) {
        searchParams.customerId = customerId;
      }

      const response = await wmsApi.post<WmsApiResponse>(API_PATH, searchParams);

      if (response.success) {
        const jobCodeList = parseApiResponse(response);

        // 尝试精确匹配 jobCode
        let jobCode = jobCodeList.find((jc: any) => {
          return jc.jobCode && jc.jobCode.toString() === jobCodeValue.toString();
        });

        // 如果没有精确匹配，尝试模糊匹配
        if (!jobCode && jobCodeList.length > 0) {
          jobCode = jobCodeList[0]; // 取第一个结果
        }

        if (jobCode) {
          const jobCodeOption = extractJobCodeData(jobCode);
          setSelectedJobCode(jobCodeOption);
          return jobCodeOption;
        }
      }
    } catch (error) {
      // 错误处理
    } finally {
      setLoading(false);
    }
    return null;
  };

  // 搜索JobCode的函数
  const searchJobCodes = async (query: string, id?: string) => {
    // 使用传入的id参数，如果没有则使用props中的customerId
    const effectiveCustomerId = id !== undefined ? id : customerId;

    try {
      setLoading(true);

      const searchParams: any = {
        currentPage: 1,
        pageSize: 20,
        type: 'JobCode'
      };

      // 如果有搜索关键字，添加到搜索参数
      if (query.trim()) {
        searchParams.jobCodeLike = query;
      }

      // 如果有客户ID，添加到搜索参数
      if (effectiveCustomerId) {
        searchParams.customerId = effectiveCustomerId;
      }

      const response = await wmsApi.post<WmsApiResponse>(API_PATH, searchParams);

      if (response.success) {
        const jobCodeList = parseApiResponse(response);

        if (jobCodeList.length > 0) {
          const jobCodes = jobCodeList.map((jobCode: any) => extractJobCodeData(jobCode));
          setOptions(jobCodes);
        } else {
          setOptions([]);
        }
      } else {
        setOptions([]);
      }
    } catch (error) {
      setOptions([]);
    } finally {
      setLoading(false);
    }
  };

  // 防抖搜索
  const debouncedSearch = useRef(
    debounce((query: string, id?: string) => {
      searchJobCodes(query, id);
    }, 300)
  ).current;

  // 处理搜索输入变化
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchQuery(value);
    debouncedSearch(value, customerId);
  };

  // 处理JobCode选择
  const handleJobCodeSelect = (jobCodeId: string) => {
    const jobCode = options.find(opt => opt.id === jobCodeId);
    if (jobCode) {
      setSelectedJobCode(jobCode);
      // 传递 jobCode 而不是 id 作为值
      onChange(jobCode.jobCode, jobCode);
      // 重置搜索状态
      setSearchQuery('');
      setOptions([]);
      setOpen(false);
    }
  };

  // 打开下拉框时触发搜索
  const handleOpenChange = (isOpen: boolean) => {
    setOpen(isOpen);

    // 如果关闭下拉框，清空搜索和结果
    if (!isOpen) {
      setSearchQuery('');
      setOptions([]);
    }
    // 如果打开下拉框，执行搜索（无论是否有搜索关键字或客户ID）
    else if (isOpen) {
      debouncedSearch(searchQuery, customerId);
    }
  };

  // 格式化价格显示
  const formatPrice = (price?: number, currency?: string) => {
    if (price === undefined || price === null) return '';
    return `${price} ${currency || 'USD'}`;
  };

  return (
    <div className="relative w-full">
      <Select
        value={selectedJobCode?.id || ''}
        onValueChange={handleJobCodeSelect}
        open={open}
        onOpenChange={handleOpenChange}
        disabled={disabled}
      >
        <SelectTrigger
          className={cn(
            "w-full h-10 rounded-md bg-transparent",
            "border border-item-gray-700/50",
            "hover:border-item-purple focus:border-item-purple",
            "flex items-center justify-between px-3 py-2 text-sm text-white",
            "focus:outline-none focus:ring-1 focus:ring-item-purple",
            "transition-all duration-200",
            "data-[placeholder]:text-item-gray-400"
          )}
        >
          <SelectValue placeholder={placeholder}>
            {loading ? (
              <div className="flex items-center">
                <Loader2 className="mr-2 h-4 w-4 animate-spin text-item-gray-400" />
                <span>Loading...</span>
              </div>
            ) : selectedJobCode ? (
              <div className="flex items-center">
                <Tag className="mr-2 h-4 w-4 text-item-gray-400" />
                <span>{selectedJobCode.jobCode}</span>
              </div>
            ) : null}
          </SelectValue>
        </SelectTrigger>
        <SelectContent
          className={cn(
            "border border-item-gray-700/70 bg-item-bg-card text-white",
            "rounded-md shadow-lg min-w-[300px] overflow-hidden",
            "animate-in fade-in-80 zoom-in-95"
          )}
          portalContainer={getCyberAgentPortalContainer()}
        >
          <div className="flex items-center px-3 py-2 border-b border-item-gray-700/50">
            <Search className="mr-2 h-4 w-4 shrink-0 text-item-gray-400" />
            <Input
              ref={searchInputRef}
              className="h-8 border-0 p-0 bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0 text-white placeholder:text-item-gray-400"
              placeholder="Search job codes..."
              value={searchQuery}
              onChange={handleSearchChange}
            />
          </div>

          <div className="max-h-[320px] overflow-auto scrollbar-thin scrollbar-thumb-item-gray-600 scrollbar-track-transparent">
            {loading ? (
              <div className="flex items-center justify-center py-6">
                <Loader2 className="h-6 w-6 animate-spin text-item-gray-400" />
              </div>
            ) : options.length > 0 ? (
              <SelectGroup>
                <SelectLabel className="px-3 text-xs font-medium text-item-gray-400">Job Codes</SelectLabel>
                {options.map((jobCode) => (
                  <SelectItem
                    key={jobCode.id}
                    value={jobCode.id}
                    className={cn(
                      "py-2 px-3 cursor-pointer text-white",
                      "focus:bg-item-purple focus:text-white",
                      "data-[highlighted]:bg-item-purple data-[highlighted]:text-white"
                    )}
                  >
                    <div className="flex items-center">
                      <Tag className="mr-2 h-4 w-4 text-item-gray-400" />
                      <div>
                        <div className="font-medium">{jobCode.jobCode}</div>
                        {jobCode.description && (
                          <div className="text-xs text-item-gray-400">{jobCode.description}</div>
                        )}
                      </div>
                    </div>
                  </SelectItem>
                ))}
              </SelectGroup>
            ) : searchQuery || customerId ? (
              <div className="py-6 text-center text-sm text-item-gray-400">
                No job codes found
              </div>
            ) : (
              <div className="py-6 text-center text-sm text-item-gray-400">
                {customerId ? 'Loading job codes...' : 'Type to search job codes'}
              </div>
            )}
          </div>
        </SelectContent>
      </Select>
    </div>
  );
}
