# Item Design System Implementation

## 概览

基于 item 品牌设计系统，我们为 CyberBot 独立站点创建了一个现代化的 Dark 模式设计方案。这个设计保持了 item 的设计理念，同时适配了暗色主题的需求。

## 设计特点

### 1. 配色方案
- **主色调**: 紫色 (#8B5CF6) - 继承自 item 的品牌色，调整为更适合暗色背景
- **强调色**: 橙色 (#F97316) - 保持原始的 item 橙色
- **背景层次**: 
  - 主背景: #0A0A0B (深黑)
  - 次级背景: #141416 (深灰)
  - 卡片背景: #1C1C1F (炭灰)
  - 悬停状态: #242427

### 2. 设计元素
- **圆角**: 使用更大的圆角 (rounded-lg) 创造现代感
- **阴影**: 深色阴影增强层次感
- **动画**: 平滑的过渡效果 (200ms cubic-bezier)
- **渐变**: 品牌渐变色用于标题和强调元素

### 3. 组件样式

#### 按钮
- 主按钮: 紫色渐变背景，悬停时有轻微上移效果
- 次级按钮: 透明背景配紫色边框，悬停时填充紫色

#### 卡片
- 使用 `item-card` 类创建统一的卡片样式
- 悬停时有边框高亮和阴影加深效果

#### 输入框
- 深色背景配合细边框
- 聚焦时显示紫色边框和光晕效果

### 4. 交互细节
- **Hover 效果**: 所有可交互元素都有平滑的悬停过渡
- **Focus 状态**: 紫色聚焦环保证可访问性
- **动画**: 使用 `item-animate-in` 类添加淡入动画

## 已更新的组件

1. **Chat.tsx**
   - 顶部导航栏采用分层背景
   - 模型选择器使用卡片样式
   - 音频下载菜单使用 item 设计语言
   - 欢迎界面使用渐变文字效果

2. **配置文件**
   - `tailwind.config.js`: 添加 item 色彩系统
   - `item-design-system.css`: 定义 CSS 变量和实用类

## 下一步计划

1. 更新 ChatInput 组件样式
2. 更新 ChatMessage 组件样式
3. 集成 Satoshi 字体或类似的几何无衬线字体
4. 为其他页面应用统一的设计系统

## 使用指南

### CSS 类
```css
.item-card          /* 卡片容器 */
.item-button-primary /* 主按钮 */
.item-button-secondary /* 次级按钮 */
.item-input         /* 输入框 */
.item-animate-in    /* 淡入动画 */
.item-scrollbar     /* 自定义滚动条 */
.item-glass         /* 玻璃拟态效果 */
.item-glow-purple   /* 紫色光晕 */
.item-glow-orange   /* 橙色光晕 */
```

### Tailwind 类
```css
bg-item-bg-primary    /* 主背景色 */
bg-item-bg-secondary  /* 次级背景色 */
bg-item-bg-card       /* 卡片背景色 */
bg-item-bg-hover      /* 悬停背景色 */
text-item-purple      /* 紫色文字 */
text-item-orange      /* 橙色文字 */
border-item-gray-800  /* 边框颜色 */
```

## 设计原则

1. **一致性**: 所有组件使用统一的颜色、圆角和间距
2. **层次感**: 通过背景色和阴影创建清晰的视觉层次
3. **可访问性**: 保持足够的对比度和清晰的交互状态
4. **现代感**: 使用渐变、动画和玻璃拟态等现代设计元素
5. **品牌识别**: 保持 item 的紫色和橙色品牌色彩

## 注意事项

- 嵌入版本保持原有设计不变
- 所有新样式仅应用于独立站点
- 保持与现有功能的兼容性
- 确保在不同屏幕尺寸下的响应式表现