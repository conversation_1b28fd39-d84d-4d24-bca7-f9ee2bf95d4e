{"openapi": "3.0.1", "info": {"title": "Inventory-Service", "description": "", "version": "1.0.0"}, "tags": [], "paths": {"/inventory-bam/inventory/search-by-paging": {"post": {"summary": "Search Inventory By Paging", "deprecated": false, "description": "", "tags": [], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/InventoryQuery", "description": ""}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/R%C2%ABPageResult%C2%ABInventoryDto%C2%BB%C2%BB"}, "examples": {"1": {"summary": "成功示例", "value": {"code": 0, "msg": "", "success": false, "data": {"list": [{"id": 0, "customerId": "", "titleId": "", "itemId": "", "qty": 0, "uomId": "", "uom": "", "sn": "", "baseQty": 0, "secondaryQty": 0, "secondaryUomId": "", "status": "", "preStatus": "", "type": "", "mode": "", "channel": "", "lotNo": "", "expirationDate": "", "mfgDate": "", "shelfLifeDays": 0, "lpId": "", "locationId": "", "receiptId": "", "orderId": "", "adjustmentId": "", "originalLPId": "", "originalBaseQty": 0, "dynTxtPropertyValue01": "", "dynTxtPropertyValue02": "", "dynTxtPropertyValue03": "", "dynTxtPropertyValue04": "", "dynTxtPropertyValue05": "", "dynTxtPropertyValue06": "", "dynTxtPropertyValue07": "", "dynTxtPropertyValue08": "", "dynTxtPropertyValue09": "", "dynTxtPropertyValue10": "", "dynTxtPropertyValue11": "", "dynTxtPropertyValue12": "", "dynTxtPropertyValue13": "", "dynTxtPropertyValue14": "", "dynTxtPropertyValue15": "", "dynTxtPropertyValue16": "", "dynTxtPropertyValue17": "", "dynTxtPropertyValue18": "", "dynTxtPropertyValue19": "", "dynTxtPropertyValue20": "", "dynDatePropertyValue01": "", "dynDatePropertyValue02": "", "dynDatePropertyValue03": "", "dynDatePropertyValue04": "", "dynDatePropertyValue05": "", "dynDatePropertyValue06": "", "dynDatePropertyValue07": "", "dynDatePropertyValue08": "", "dynDatePropertyValue09": "", "dynDatePropertyValue10": "", "profiles": [{"fieldKey": "", "stringFieldVal": "", "stringListFieldVal": [""], "timeFieldVal": "", "boolFieldVal": false, "numberFieldVal": 0}], "receivedTime": "", "receivedBy": "", "adjustOutTime": "", "adjustOutBy": "", "shippedTime": "", "shippedBy": ""}], "totalCount": 0, "currentPage": 0, "pageSize": 0, "totalPage": 0}}}}}}, "headers": {}}}, "security": []}}, "/wms-bam/inventory/search-sum-by-paging": {"post": {"summary": "Search Inventory By Paging", "deprecated": false, "description": "", "tags": [], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/InventoryQuery", "description": ""}}}}, "responses": {}, "security": []}}, "/inventory-app/inventory-lock/search/by/paging": {"post": {"summary": "Search Inventory Lock By Paging", "deprecated": false, "description": "", "tags": [], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/InventoryLockQuery", "description": ""}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RPageResultInventoryLockDto", "description": ""}, "examples": {"1": {"summary": "成功示例", "value": {"code": 0, "msg": "", "success": false, "data": {"list": [{"id": 0, "orderId": "", "orderItemLineId": "", "itemId": "", "uomId": "", "qty": 0, "baseQty": 0, "titleId": "", "customerId": "", "lotNo": "", "status": "", "goodsType": "", "createdTime": "", "createdBy": "", "updatedTime": "", "updatedBy": ""}], "totalCount": 0, "currentPage": 0, "pageSize": 0, "totalPage": 0}}}}}}, "headers": {}}}, "security": []}}, "/inventory-bam/lp/search-by-paging": {"post": {"summary": "Search LP By Paging", "deprecated": false, "description": "", "tags": [], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LpQuery", "description": ""}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/R%C2%ABPageResult%C2%ABLpDto%C2%BB%C2%BB"}, "examples": {"1": {"summary": "成功示例", "value": {"code": 0, "msg": "", "success": false, "data": {"list": [{"id": "", "code": "", "locationId": "", "type": "", "hlpCategory": "", "confId": "", "orderId": "", "taskId": "", "trackingNo": "", "cartonNo": "", "palletNo": "", "equipmentId": "", "seq": 0, "status": "", "parentId": "", "consolidateLp": "", "weight": 0, "weightUnit": "", "length": 0, "width": 0, "height": 0, "linearUnit": "", "createdBy": "", "createdTime": "", "updatedBy": "", "updatedTime": ""}], "totalCount": 0, "currentPage": 0, "pageSize": 0, "totalPage": 0}}}}}}, "headers": {}}}, "security": []}}, "/wms/location/search-by-paging": {"post": {"summary": "Search Location By Paging", "deprecated": false, "description": "", "tags": [], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LocationSearch", "description": ""}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RPageResultLocationDto"}, "examples": {"1": {"summary": "成功示例", "value": {"code": 0, "msg": "", "success": false, "data": {"list": [{"id": "", "name": "", "akaName": "", "floor": 0, "parentId": "", "type": "", "maxSize": 0, "length": 0, "width": 0, "height": 0, "linearUnit": "", "status": "", "spaceStatus": "", "dockStatus": "", "hlpId": "", "supportPickType": "", "category": "", "sequence": 0, "capacityType": "", "capacity": 0, "customerIds": [""], "tag": "", "aisle": "", "bay": "", "section": "", "level": 0, "slot": "", "enableLocationHLP": false, "disallowToMixItemOnSameLocation": false, "putAwaySuggestionWeight": 0, "createdTime": "", "updatedTime": "", "createdBy": "", "updatedBy": ""}], "totalCount": 0, "currentPage": 0, "pageSize": 0, "totalPage": 0}}}}}}, "headers": {}}}, "security": []}}}, "components": {"schemas": {"InventoryDto": {"type": "object", "properties": {"id": {"type": "integer", "description": "", "nullable": true}, "customerId": {"type": "string", "description": "", "nullable": true}, "titleId": {"type": "string", "description": "", "nullable": true}, "itemId": {"type": "string", "description": "", "nullable": true}, "qty": {"type": "number", "description": "", "nullable": true}, "uomId": {"type": "string", "description": "", "nullable": true}, "uom": {"type": "string", "description": "", "nullable": true}, "sn": {"type": "string", "description": "", "nullable": true}, "baseQty": {"type": "number", "description": "", "nullable": true}, "secondaryQty": {"type": "number", "description": "", "nullable": true}, "secondaryUomId": {"type": "string", "description": "", "nullable": true}, "status": {"type": "string", "description": "", "enum": ["OPEN", "DAMAGE", "ONHOLD", "ADJUSTOUT", "RECEIVING", "PICKED", "PACKED", "LOADED", "SHIPPED"], "nullable": true}, "preStatus": {"type": "string", "description": "", "enum": ["OPEN", "DAMAGE", "ONHOLD", "ADJUSTOUT", "RECEIVING", "PICKED", "PACKED", "LOADED", "SHIPPED"], "nullable": true}, "type": {"type": "string", "description": "", "nullable": true}, "mode": {"type": "string", "description": "", "enum": ["WMS", "OMS", "RMS", "ERP", "ASSET"], "nullable": true}, "channel": {"type": "string", "description": "", "enum": ["RECEIVE", "ADJUSTIN"], "nullable": true}, "lotNo": {"type": "string", "description": "", "nullable": true}, "expirationDate": {"type": "string", "description": "", "nullable": true}, "mfgDate": {"type": "string", "description": "", "nullable": true}, "shelfLifeDays": {"type": "integer", "description": "", "nullable": true}, "lpId": {"type": "string", "description": "", "nullable": true}, "locationId": {"type": "string", "description": "", "nullable": true}, "receiptId": {"type": "string", "description": "", "nullable": true}, "orderId": {"type": "string", "description": "", "nullable": true}, "adjustmentId": {"type": "string", "description": "", "nullable": true}, "originalLPId": {"type": "string", "description": "", "nullable": true}, "originalBaseQty": {"type": "number", "description": "", "nullable": true}, "dynTxtPropertyValue01": {"type": "string", "description": "", "nullable": true}, "dynTxtPropertyValue02": {"type": "string", "description": "", "nullable": true}, "dynTxtPropertyValue03": {"type": "string", "description": "", "nullable": true}, "dynTxtPropertyValue04": {"type": "string", "description": "", "nullable": true}, "dynTxtPropertyValue05": {"type": "string", "description": "", "nullable": true}, "dynTxtPropertyValue06": {"type": "string", "description": "", "nullable": true}, "dynTxtPropertyValue07": {"type": "string", "description": "", "nullable": true}, "dynTxtPropertyValue08": {"type": "string", "description": "", "nullable": true}, "dynTxtPropertyValue09": {"type": "string", "description": "", "nullable": true}, "dynTxtPropertyValue10": {"type": "string", "description": "", "nullable": true}, "dynTxtPropertyValue11": {"type": "string", "description": "", "nullable": true}, "dynTxtPropertyValue12": {"type": "string", "description": "", "nullable": true}, "dynTxtPropertyValue13": {"type": "string", "description": "", "nullable": true}, "dynTxtPropertyValue14": {"type": "string", "description": "", "nullable": true}, "dynTxtPropertyValue15": {"type": "string", "description": "", "nullable": true}, "dynTxtPropertyValue16": {"type": "string", "description": "", "nullable": true}, "dynTxtPropertyValue17": {"type": "string", "description": "", "nullable": true}, "dynTxtPropertyValue18": {"type": "string", "description": "", "nullable": true}, "dynTxtPropertyValue19": {"type": "string", "description": "", "nullable": true}, "dynTxtPropertyValue20": {"type": "string", "description": "", "nullable": true}, "dynDatePropertyValue01": {"type": "string", "description": "", "nullable": true}, "dynDatePropertyValue02": {"type": "string", "description": "", "nullable": true}, "dynDatePropertyValue03": {"type": "string", "description": "", "nullable": true}, "dynDatePropertyValue04": {"type": "string", "description": "", "nullable": true}, "dynDatePropertyValue05": {"type": "string", "description": "", "nullable": true}, "dynDatePropertyValue06": {"type": "string", "description": "", "nullable": true}, "dynDatePropertyValue07": {"type": "string", "description": "", "nullable": true}, "dynDatePropertyValue08": {"type": "string", "description": "", "nullable": true}, "dynDatePropertyValue09": {"type": "string", "description": "", "nullable": true}, "dynDatePropertyValue10": {"type": "string", "description": "", "nullable": true}, "profiles": {"type": "array", "items": {"$ref": "#/components/schemas/InventoryProfile", "description": "com.item.inventory.domain.inventory.model.entity.InventoryProfile"}, "description": "", "nullable": true}, "receivedTime": {"type": "string", "description": "", "nullable": true}, "receivedBy": {"type": "string", "description": "", "nullable": true}, "adjustOutTime": {"type": "string", "description": "", "nullable": true}, "adjustOutBy": {"type": "string", "description": "", "nullable": true}, "shippedTime": {"type": "string", "description": "", "nullable": true}, "shippedBy": {"type": "string", "description": "", "nullable": true}}}, "InventoryProfile": {"type": "object", "properties": {"fieldKey": {"type": "string", "description": "", "nullable": true}, "stringFieldVal": {"type": "string", "description": "", "nullable": true}, "stringListFieldVal": {"type": "array", "items": {"type": "string"}, "description": "", "nullable": true}, "timeFieldVal": {"type": "string", "description": "", "nullable": true}, "boolFieldVal": {"type": "boolean", "description": "", "nullable": true}, "numberFieldVal": {"type": "number", "description": "", "nullable": true}}}, "InventoryQuery": {"type": "object", "properties": {"currentPage": {"type": "integer", "description": "", "nullable": true}, "pageSize": {"type": "integer", "description": "", "nullable": true}, "sortingFields": {"type": "array", "items": {"$ref": "#/components/schemas/SortingField", "description": "com.item.xms.persistence.query.SortingField"}, "description": "", "nullable": true}, "id": {"type": "string", "description": "", "nullable": true}, "customerId": {"type": "string", "description": "", "nullable": true}, "customerIds": {"type": "array", "items": {"type": "string"}, "description": "", "nullable": true}, "titleId": {"type": "string", "description": "", "nullable": true}, "titleIds": {"type": "array", "items": {"type": "string"}, "description": "", "nullable": true}, "itemId": {"type": "string", "description": "", "nullable": true}, "itemIds": {"type": "array", "items": {"type": "string"}, "description": "", "nullable": true}, "qtyGt": {"type": "number", "description": "", "nullable": true}, "sn": {"type": "string", "description": "", "nullable": true}, "sns": {"type": "array", "items": {"type": "string"}, "description": "", "nullable": true}, "status": {"type": "string", "description": "", "enum": ["OPEN", "DAMAGE", "ONHOLD", "ADJUSTOUT", "RECEIVING", "PICKED", "PACKED", "LOADED", "SHIPPED"], "nullable": true}, "statuses": {"type": "array", "items": {"type": "string", "enum": ["OPEN", "DAMAGE", "ONHOLD", "ADJUSTOUT", "RECEIVING", "PICKED", "PACKED", "LOADED", "SHIPPED"]}, "description": "", "nullable": true}, "excludeStatuses": {"type": "array", "items": {"type": "string"}, "description": "", "nullable": true}, "type": {"type": "string", "description": "", "nullable": true}, "types": {"type": "array", "items": {"type": "string"}, "description": "", "nullable": true}, "mode": {"type": "string", "description": "", "enum": ["WMS", "OMS", "RMS", "ERP", "ASSET"], "nullable": true}, "channel": {"type": "string", "description": "", "enum": ["RECEIVE", "ADJUSTIN"], "nullable": true}, "lotNo": {"type": "string", "description": "", "nullable": true}, "lpId": {"type": "string", "description": "", "nullable": true}, "lpIds": {"type": "array", "items": {"type": "string"}, "description": "", "nullable": true}, "locationId": {"type": "string", "description": "", "nullable": true}, "locationIds": {"type": "array", "items": {"type": "string"}, "description": "", "nullable": true}, "receiptId": {"type": "string", "description": "", "nullable": true}, "receiptIds": {"type": "array", "items": {"type": "string"}, "description": "", "nullable": true}, "orderId": {"type": "string", "description": "", "nullable": true}, "orderIds": {"type": "array", "items": {"type": "string"}, "description": "", "nullable": true}, "adjustmentId": {"type": "string", "description": "", "nullable": true}, "adjustmentIds": {"type": "array", "items": {"type": "string"}, "description": "", "nullable": true}, "receivedTimeFrom": {"type": "string", "description": "", "nullable": true}, "receivedTimeTo": {"type": "string", "description": "", "nullable": true}, "shippedTimeFrom": {"type": "string", "description": "", "nullable": true}, "shippedTimeTo": {"type": "string", "description": "", "nullable": true}, "createdTimeFrom": {"type": "string", "description": "", "nullable": true}, "createdTimeTo": {"type": "string", "description": "", "nullable": true}, "dynTxtPropertyValue01": {"type": "string", "description": "", "nullable": true}, "dynTxtPropertyValue01s": {"type": "array", "items": {"type": "string"}, "description": "", "nullable": true}, "dynTxtPropertyValue02": {"type": "string", "description": "", "nullable": true}, "dynTxtPropertyValue02s": {"type": "array", "items": {"type": "string"}, "description": "", "nullable": true}, "dynTxtPropertyValue03": {"type": "string", "description": "", "nullable": true}, "dynTxtPropertyValue03s": {"type": "array", "items": {"type": "string"}, "description": "", "nullable": true}, "dynTxtPropertyValue04": {"type": "string", "description": "", "nullable": true}, "dynTxtPropertyValue04s": {"type": "array", "items": {"type": "string"}, "description": "", "nullable": true}, "dynTxtPropertyValue05": {"type": "string", "description": "", "nullable": true}, "dynTxtPropertyValue05s": {"type": "array", "items": {"type": "string"}, "description": "", "nullable": true}, "dynTxtPropertyValue06": {"type": "string", "description": "", "nullable": true}, "dynTxtPropertyValue06s": {"type": "array", "items": {"type": "string"}, "description": "", "nullable": true}, "dynTxtPropertyValue07": {"type": "string", "description": "", "nullable": true}, "dynTxtPropertyValue07s": {"type": "array", "items": {"type": "string"}, "description": "", "nullable": true}, "dynTxtPropertyValue08": {"type": "string", "description": "", "nullable": true}, "dynTxtPropertyValue08s": {"type": "array", "items": {"type": "string"}, "description": "", "nullable": true}, "dynTxtPropertyValue09": {"type": "string", "description": "", "nullable": true}, "dynTxtPropertyValue09s": {"type": "array", "items": {"type": "string"}, "description": "", "nullable": true}, "dynTxtPropertyValue10": {"type": "string", "description": "", "nullable": true}, "dynTxtPropertyValue10s": {"type": "array", "items": {"type": "string"}, "description": "", "nullable": true}, "dynTxtPropertyValue11": {"type": "string", "description": "", "nullable": true}, "dynTxtPropertyValue11s": {"type": "array", "items": {"type": "string"}, "description": "", "nullable": true}, "dynTxtPropertyValue12": {"type": "string", "description": "", "nullable": true}, "dynTxtPropertyValue12s": {"type": "array", "items": {"type": "string"}, "description": "", "nullable": true}, "dynTxtPropertyValue13": {"type": "string", "description": "", "nullable": true}, "dynTxtPropertyValue13s": {"type": "array", "items": {"type": "string"}, "description": "", "nullable": true}, "dynTxtPropertyValue14": {"type": "string", "description": "", "nullable": true}, "dynTxtPropertyValue14s": {"type": "array", "items": {"type": "string"}, "description": "", "nullable": true}, "dynTxtPropertyValue15": {"type": "string", "description": "", "nullable": true}, "dynTxtPropertyValue15s": {"type": "array", "items": {"type": "string"}, "description": "", "nullable": true}, "dynTxtPropertyValue16": {"type": "string", "description": "", "nullable": true}, "dynTxtPropertyValue16s": {"type": "array", "items": {"type": "string"}, "description": "", "nullable": true}, "dynTxtPropertyValue17": {"type": "string", "description": "", "nullable": true}, "dynTxtPropertyValue17s": {"type": "array", "items": {"type": "string"}, "description": "", "nullable": true}, "dynTxtPropertyValue18": {"type": "string", "description": "", "nullable": true}, "dynTxtPropertyValue18s": {"type": "array", "items": {"type": "string"}, "description": "", "nullable": true}, "dynTxtPropertyValue19": {"type": "string", "description": "", "nullable": true}, "dynTxtPropertyValue19s": {"type": "array", "items": {"type": "string"}, "description": "", "nullable": true}, "dynTxtPropertyValue20": {"type": "string", "description": "", "nullable": true}, "dynTxtPropertyValue20s": {"type": "array", "items": {"type": "string"}, "description": "", "nullable": true}, "includeInnerLp": {"type": "boolean", "description": "", "nullable": true}}}, "SortingField": {"type": "object", "properties": {"field": {"type": "string", "description": ""}, "orderBy": {"type": "string", "description": "", "enum": ["NONE", "ASC", "DESC"]}}}, "PageResult«InventoryDto»": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/components/schemas/InventoryDto", "description": "com.item.inventory.application.inventory.dto.InventoryDto"}, "description": "", "nullable": true}, "totalCount": {"type": "integer", "description": "", "nullable": true}, "currentPage": {"type": "integer", "description": "", "nullable": true}, "pageSize": {"type": "integer", "description": "", "nullable": true}, "totalPage": {"type": "integer", "description": "", "nullable": true}}}, "R«PageResult«InventoryDto»»": {"type": "object", "properties": {"code": {"type": "integer", "description": "", "nullable": true}, "msg": {"type": "string", "description": "", "nullable": true}, "success": {"type": "boolean", "description": "", "nullable": true}, "data": {"$ref": "#/components/schemas/PageResult%C2%ABInventoryDto%C2%BB", "description": ""}}}, "InventoryLockQuery": {"type": "object", "properties": {"currentPage": {"type": "integer", "description": ""}, "pageSize": {"type": "integer", "description": ""}, "sortingFields": {"type": "array", "items": {"$ref": "#/components/schemas/SortingField", "description": "com.item.xms.persistence.query.SortingField"}, "description": ""}, "id": {"type": "string", "description": ""}, "orderId": {"type": "string", "description": ""}, "orderIds": {"type": "array", "items": {"type": "string"}, "description": ""}, "orderItemLineId": {"type": "string", "description": ""}, "orderItemLineIds": {"type": "array", "items": {"type": "string"}, "description": ""}, "itemId": {"type": "string", "description": ""}, "itemIds": {"type": "array", "items": {"type": "string"}, "description": ""}, "uomId": {"type": "string", "description": ""}, "uomIds": {"type": "array", "items": {"type": "string"}, "description": ""}, "titleId": {"type": "string", "description": ""}, "titleIds": {"type": "array", "items": {"type": "string"}, "description": ""}, "customerId": {"type": "string", "description": ""}, "customerIds": {"type": "array", "items": {"type": "string"}, "description": ""}, "status": {"type": "string", "description": "", "enum": ["ACTIVE", "CLOSED", "INACTIVE"]}, "statuses": {"type": "array", "items": {"type": "string", "enum": ["ACTIVE", "CLOSED", "INACTIVE"]}, "description": ""}, "lotNo": {"type": "string", "description": ""}, "createdTimeFrom": {"type": "string", "description": ""}, "supplierId": {"type": "string", "description": ""}, "supplierIds": {"type": "array", "items": {"type": "string"}, "description": ""}, "receiveTaskId": {"type": "string", "description": ""}, "receiveTaskIds": {"type": "array", "items": {"type": "string"}, "description": ""}, "putAwayTaskId": {"type": "string", "description": ""}, "putAwayTaskIds": {"type": "array", "items": {"type": "string"}, "description": ""}, "pickTaskId": {"type": "string", "description": ""}, "pickTaskIds": {"type": "array", "items": {"type": "string"}, "description": ""}, "packTaskId": {"type": "string", "description": ""}, "packTaskIds": {"type": "array", "items": {"type": "string"}, "description": ""}, "loadTaskId": {"type": "string", "description": ""}, "loadTaskIds": {"type": "array", "items": {"type": "string"}, "description": ""}, "createdTimeTo": {"type": "string", "description": ""}, "dynTxtPropertyValue01": {"type": "string", "description": ""}, "dynTxtPropertyValue01s": {"type": "array", "items": {"type": "string"}, "description": ""}, "dynTxtPropertyValue02": {"type": "string", "description": ""}, "dynTxtPropertyValue02s": {"type": "array", "items": {"type": "string"}, "description": ""}, "dynTxtPropertyValue03": {"type": "string", "description": ""}, "dynTxtPropertyValue03s": {"type": "array", "items": {"type": "string"}, "description": ""}, "dynTxtPropertyValue04": {"type": "string", "description": ""}, "dynTxtPropertyValue04s": {"type": "array", "items": {"type": "string"}, "description": ""}, "dynTxtPropertyValue05": {"type": "string", "description": ""}, "dynTxtPropertyValue05s": {"type": "array", "items": {"type": "string"}, "description": ""}, "dynTxtPropertyValue06": {"type": "string", "description": ""}, "dynTxtPropertyValue06s": {"type": "array", "items": {"type": "string"}, "description": ""}, "dynTxtPropertyValue07": {"type": "string", "description": ""}, "dynTxtPropertyValue07s": {"type": "array", "items": {"type": "string"}, "description": ""}, "dynTxtPropertyValue08": {"type": "string", "description": ""}, "dynTxtPropertyValue08s": {"type": "array", "items": {"type": "string"}, "description": ""}, "dynTxtPropertyValue09": {"type": "string", "description": ""}, "dynTxtPropertyValue09s": {"type": "array", "items": {"type": "string"}, "description": ""}, "dynTxtPropertyValue10": {"type": "string", "description": ""}, "dynTxtPropertyValue10s": {"type": "array", "items": {"type": "string"}, "description": ""}, "dynTxtPropertyValue11": {"type": "string", "description": ""}, "dynTxtPropertyValue11s": {"type": "array", "items": {"type": "string"}, "description": ""}, "dynTxtPropertyValue12": {"type": "string", "description": ""}, "dynTxtPropertyValue12s": {"type": "array", "items": {"type": "string"}, "description": ""}, "dynTxtPropertyValue13": {"type": "string", "description": ""}, "dynTxtPropertyValue13s": {"type": "array", "items": {"type": "string"}, "description": ""}, "dynTxtPropertyValue14": {"type": "string", "description": ""}, "dynTxtPropertyValue14s": {"type": "array", "items": {"type": "string"}, "description": ""}, "dynTxtPropertyValue15": {"type": "string", "description": ""}, "dynTxtPropertyValue15s": {"type": "array", "items": {"type": "string"}, "description": ""}, "dynTxtPropertyValue16": {"type": "string", "description": ""}, "dynTxtPropertyValue16s": {"type": "array", "items": {"type": "string"}, "description": ""}, "dynTxtPropertyValue17": {"type": "string", "description": ""}, "dynTxtPropertyValue17s": {"type": "array", "items": {"type": "string"}, "description": ""}, "dynTxtPropertyValue18": {"type": "string", "description": ""}, "dynTxtPropertyValue18s": {"type": "array", "items": {"type": "string"}, "description": ""}, "dynTxtPropertyValue19": {"type": "string", "description": ""}, "dynTxtPropertyValue19s": {"type": "array", "items": {"type": "string"}, "description": ""}, "dynTxtPropertyValue20": {"type": "string", "description": ""}, "dynTxtPropertyValue20s": {"type": "array", "items": {"type": "string"}, "description": ""}}}, "RPageResultInventoryLockDto": {"type": "object", "properties": {"code": {"type": "integer", "description": ""}, "msg": {"type": "string", "description": ""}, "success": {"type": "boolean", "description": ""}, "data": {"$ref": "#/components/schemas/PageResultInventoryLockDto", "description": ""}}}, "PageResultInventoryLockDto": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/components/schemas/InventoryLockDto", "description": "com.item.inventory.application.inventorylock.dto.InventoryLockDto"}, "description": ""}, "totalCount": {"type": "integer", "description": ""}, "currentPage": {"type": "integer", "description": ""}, "pageSize": {"type": "integer", "description": ""}, "totalPage": {"type": "integer", "description": ""}}}, "InventoryLockDto": {"type": "object", "properties": {"id": {"type": "integer", "description": ""}, "orderId": {"type": "string", "description": ""}, "orderItemLineId": {"type": "string", "description": ""}, "itemId": {"type": "string", "description": ""}, "uomId": {"type": "string", "description": ""}, "qty": {"type": "number", "description": ""}, "baseQty": {"type": "number", "description": ""}, "titleId": {"type": "string", "description": ""}, "customerId": {"type": "string", "description": ""}, "lotNo": {"type": "string", "description": ""}, "status": {"type": "string", "description": "", "enum": ["ACTIVE", "CLOSED", "INACTIVE"]}, "goodsType": {"type": "string", "description": ""}, "supplierId": {"type": "string", "description": ""}, "receiveTaskId": {"type": "string", "description": ""}, "putAwayTaskId": {"type": "string", "description": ""}, "pickTaskId": {"type": "string", "description": ""}, "packTaskId": {"type": "string", "description": ""}, "loadTaskId": {"type": "string", "description": ""}, "dynTxtPropertyValue01": {"type": "string", "description": ""}, "dynTxtPropertyValue02": {"type": "string", "description": ""}, "dynTxtPropertyValue03": {"type": "string", "description": ""}, "dynTxtPropertyValue04": {"type": "string", "description": ""}, "dynTxtPropertyValue05": {"type": "string", "description": ""}, "dynTxtPropertyValue06": {"type": "string", "description": ""}, "dynTxtPropertyValue07": {"type": "string", "description": ""}, "dynTxtPropertyValue08": {"type": "string", "description": ""}, "dynTxtPropertyValue09": {"type": "string", "description": ""}, "dynTxtPropertyValue10": {"type": "string", "description": ""}, "dynTxtPropertyValue11": {"type": "string", "description": ""}, "dynTxtPropertyValue12": {"type": "string", "description": ""}, "dynTxtPropertyValue13": {"type": "string", "description": ""}, "dynTxtPropertyValue14": {"type": "string", "description": ""}, "dynTxtPropertyValue15": {"type": "string", "description": ""}, "dynTxtPropertyValue16": {"type": "string", "description": ""}, "dynTxtPropertyValue17": {"type": "string", "description": ""}, "dynTxtPropertyValue18": {"type": "string", "description": ""}, "dynTxtPropertyValue19": {"type": "string", "description": ""}, "dynTxtPropertyValue20": {"type": "string", "description": ""}, "dynDatePropertyValue01": {"type": "string", "description": ""}, "dynDatePropertyValue02": {"type": "string", "description": ""}, "dynDatePropertyValue03": {"type": "string", "description": ""}, "dynDatePropertyValue04": {"type": "string", "description": ""}, "dynDatePropertyValue05": {"type": "string", "description": ""}, "dynDatePropertyValue06": {"type": "string", "description": ""}, "dynDatePropertyValue07": {"type": "string", "description": ""}, "dynDatePropertyValue08": {"type": "string", "description": ""}, "dynDatePropertyValue09": {"type": "string", "description": ""}, "dynDatePropertyValue10": {"type": "string", "description": ""}, "createdTime": {"type": "string", "description": ""}, "createdBy": {"type": "string", "description": ""}, "updatedTime": {"type": "string", "description": ""}, "updatedBy": {"type": "string", "description": ""}}}, "LpQuery": {"type": "object", "properties": {"currentPage": {"type": "integer", "description": "", "nullable": true}, "pageSize": {"type": "integer", "description": "", "nullable": true}, "sortingFields": {"type": "array", "items": {"$ref": "#/components/schemas/SortingField", "description": "com.item.xms.persistence.query.SortingField"}, "description": "", "nullable": true}, "id": {"type": "string", "description": "", "nullable": true}, "ids": {"type": "array", "items": {"type": "string"}, "description": "", "nullable": true}, "code": {"type": "string", "description": "", "nullable": true}, "locationId": {"type": "string", "description": "", "nullable": true}, "locationIds": {"type": "array", "items": {"type": "string"}, "description": "", "nullable": true}, "originalReceiveLocationId": {"type": "string", "description": "", "nullable": true}, "type": {"type": "string", "description": "", "enum": ["ILP", "HLP", "CLP", "SLP", "TLP", "RLP"], "nullable": true}, "hlpCategory": {"type": "string", "description": "", "enum": ["ORDER_ALIAS", "TASK_ALIAS", "TRACK", "NG_NO_ALIAS", "EQUIPMENT_ALIAS", "LOCATION_ALIAS", "CARTON_ALIAS"], "nullable": true}, "goodsType": {"type": "string", "description": "", "nullable": true}, "confId": {"type": "string", "description": "", "nullable": true}, "packagingTypeSpecId": {"type": "string", "description": "", "nullable": true}, "packagingTypeProductId": {"type": "string", "description": "", "nullable": true}, "orderId": {"type": "string", "description": "", "nullable": true}, "taskId": {"type": "string", "description": "", "nullable": true}, "lpReceiptId": {"type": "string", "description": "", "nullable": true}, "lpItemSpecId": {"type": "string", "description": "", "nullable": true}, "lpTrackingNo": {"type": "string", "description": "", "nullable": true}, "toteId": {"type": "string", "description": "", "nullable": true}, "seq": {"type": "integer", "description": "", "nullable": true}, "cartonQty": {"type": "integer", "description": "", "nullable": true}, "status": {"type": "string", "description": "", "enum": ["NEW", "RECEIVING", "IN_STOCK", "PICKED", "STAGED", "STAGE_TO_LOAD", "LOADED", "PACKED", "SHIPPED", "ON_HOLD", "QUARANTINE", "EXCEPTIONAL"], "nullable": true}, "createdBy": {"type": "string", "description": "", "nullable": true}, "createdTime": {"type": "string", "description": "", "nullable": true}, "updatedBy": {"type": "string", "description": "", "nullable": true}, "updatedTime": {"type": "string", "description": "", "nullable": true}, "parentId": {"type": "string", "description": "", "nullable": true}, "parentIds": {"type": "array", "items": {"type": "string"}, "description": "", "nullable": true}, "consolidateLp": {"type": "string", "description": "", "nullable": true}, "palletQty": {"type": "integer", "description": "", "nullable": true}, "palletNo": {"type": "string", "description": "", "nullable": true}, "isPickToOrder": {"type": "boolean", "description": "", "nullable": true}, "snMismatch": {"type": "string", "description": "", "nullable": true}, "equipmentId": {"type": "string", "description": "", "nullable": true}, "allowToAdd": {"type": "boolean", "description": "", "nullable": true}, "isPacked": {"type": "boolean", "description": "", "nullable": true}, "palletHeight": {"type": "number", "description": "", "nullable": true}, "weight": {"type": "number", "description": "", "nullable": true}, "weightUnit": {"type": "string", "description": "", "enum": ["G", "OZ", "KG", "POUND", "LBS", "LB"], "nullable": true}, "length": {"type": "number", "description": "", "nullable": true}, "width": {"type": "number", "description": "", "nullable": true}, "height": {"type": "number", "description": "", "nullable": true}, "linearUnit": {"type": "string", "description": "", "enum": ["CM", "INCH", "FEET", "M"], "nullable": true}, "destination": {"type": "string", "description": "", "nullable": true}, "plateNo": {"type": "string", "description": "", "nullable": true}, "thirdPartyLpNo": {"type": "string", "description": "", "nullable": true}, "cartonPackageSpecId": {"type": "string", "description": "", "nullable": true}}}, "LpDto": {"type": "object", "properties": {"id": {"type": "string", "description": "", "nullable": true}, "code": {"type": "string", "description": "", "nullable": true}, "locationId": {"type": "string", "description": "", "nullable": true}, "type": {"type": "string", "description": "", "enum": ["ILP", "HLP", "CLP", "SLP", "TLP", "RLP"], "nullable": true}, "hlpCategory": {"type": "string", "description": "", "enum": ["ORDER_ALIAS", "TASK_ALIAS", "TRACK", "NG_NO_ALIAS", "EQUIPMENT_ALIAS", "LOCATION_ALIAS", "CARTON_ALIAS"], "nullable": true}, "confId": {"type": "string", "description": "", "nullable": true}, "orderId": {"type": "string", "description": "", "nullable": true}, "taskId": {"type": "string", "description": "", "nullable": true}, "trackingNo": {"type": "string", "description": "", "nullable": true}, "cartonNo": {"type": "string", "description": "", "nullable": true}, "palletNo": {"type": "string", "description": "", "nullable": true}, "equipmentId": {"type": "string", "description": "", "nullable": true}, "seq": {"type": "integer", "description": "", "nullable": true}, "status": {"type": "string", "description": "", "enum": ["NEW", "RECEIVING", "IN_STOCK", "PICKED", "STAGED", "STAGE_TO_LOAD", "LOADED", "PACKED", "SHIPPED", "ON_HOLD", "QUARANTINE", "EXCEPTIONAL"], "nullable": true}, "parentId": {"type": "string", "description": "", "nullable": true}, "consolidateLp": {"type": "string", "description": "", "nullable": true}, "weight": {"type": "number", "description": "", "nullable": true}, "weightUnit": {"type": "string", "description": "", "enum": ["G", "OZ", "KG", "POUND", "LBS", "LB"], "nullable": true}, "length": {"type": "number", "description": "", "nullable": true}, "width": {"type": "number", "description": "", "nullable": true}, "height": {"type": "number", "description": "", "nullable": true}, "linearUnit": {"type": "string", "description": "", "enum": ["CM", "INCH", "FEET", "M"], "nullable": true}, "createdBy": {"type": "string", "description": "", "nullable": true}, "createdTime": {"type": "string", "description": "", "nullable": true}, "updatedBy": {"type": "string", "description": "", "nullable": true}, "updatedTime": {"type": "string", "description": "", "nullable": true}}}, "R«PageResult«LpDto»»": {"type": "object", "properties": {"code": {"type": "integer", "description": "", "nullable": true}, "msg": {"type": "string", "description": "", "nullable": true}, "success": {"type": "boolean", "description": "", "nullable": true}, "data": {"$ref": "#/components/schemas/PageResult%C2%ABLpDto%C2%BB", "description": ""}}}, "PageResult«LpDto»": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/components/schemas/LpDto", "description": "com.item.inventory.application.lp.dto.LpDto"}, "description": "", "nullable": true}, "totalCount": {"type": "integer", "description": "", "nullable": true}, "currentPage": {"type": "integer", "description": "", "nullable": true}, "pageSize": {"type": "integer", "description": "", "nullable": true}, "totalPage": {"type": "integer", "description": "", "nullable": true}}}, "LocationSearch": {"type": "object", "properties": {"currentPage": {"type": "integer", "description": ""}, "pageSize": {"type": "integer", "description": ""}, "sortingFields": {"type": "array", "items": {"$ref": "#/components/schemas/SortingField", "description": "com.item.xms.persistence.query.SortingField"}, "description": ""}, "ids": {"type": "array", "items": {"type": "string"}, "description": ""}, "locationIds": {"type": "array", "items": {"type": "string"}, "description": ""}, "type": {"type": "string", "description": "", "enum": ["LOCATION", "PICK", "STAGING", "DOCK", "AIR_ROB", "REWORK"]}, "types": {"type": "array", "items": {"type": "string", "enum": ["LOCATION", "PICK", "STAGING", "DOCK", "AIR_ROB", "REWORK"]}, "description": ""}, "excludeTypes": {"type": "array", "items": {"type": "string"}, "description": ""}, "status": {"type": "string", "description": "", "enum": ["USABLE", "DISABLED", "DELETE", "MERGED", "MIXTURE"]}, "statuses": {"type": "array", "items": {"type": "string", "enum": ["USABLE", "DISABLED", "DELETE", "MERGED", "MIXTURE"]}, "description": ""}, "excludeStatuses": {"type": "array", "items": {"type": "string"}, "description": ""}, "name": {"type": "string", "description": ""}, "names": {"type": "array", "items": {"type": "string"}, "description": ""}, "exactlyNames": {"type": "array", "items": {"type": "string"}, "description": ""}, "stack": {"type": "string", "description": "", "enum": ["ONE_HEIGHT", "TWO_HEIGHT", "TWO_HALF_HEIGHT", "THREE_HALF_HEIGHT", "THREE_HEIGHT", "FOUR_HEIGHT", "FOUR_HALF_HEIGHT", "FIVE_HEIGHT", "SIX_HEIGHT", "RACK"]}, "stacks": {"type": "array", "items": {"type": "string", "enum": ["ONE_HEIGHT", "TWO_HEIGHT", "TWO_HALF_HEIGHT", "THREE_HALF_HEIGHT", "THREE_HEIGHT", "FOUR_HEIGHT", "FOUR_HALF_HEIGHT", "FIVE_HEIGHT", "SIX_HEIGHT", "RACK"]}, "description": ""}, "companyId": {"type": "string", "description": ""}, "regexName": {"type": "string", "description": ""}, "parentIds": {"type": "array", "items": {"type": "string"}, "description": ""}, "parentId": {"type": "string", "description": ""}, "floorAbove": {"type": "integer", "description": ""}, "floorBelow": {"type": "integer", "description": ""}, "checkingNo": {"type": "string", "description": ""}, "excludeLocationId": {"type": "string", "description": ""}, "excludeLocationIds": {"type": "array", "items": {"type": "string"}, "description": ""}, "locationGroupId": {"type": "string", "description": ""}, "locationGroupIds": {"type": "array", "items": {"type": "string"}, "description": ""}, "supportPickType": {"type": "string", "description": "", "enum": ["PALLET_PICK", "CASE_PICK", "PIECE_PICK", "NONE"]}, "category": {"type": "string", "description": "", "enum": ["YARD", "WAREHOUSE", "DOCK"]}, "categories": {"type": "array", "items": {"type": "string", "enum": ["YARD", "WAREHOUSE", "DOCK"]}, "description": ""}, "entryId": {"type": "string", "description": ""}, "entryIds": {"type": "array", "items": {"type": "string"}, "description": ""}, "hlpId": {"type": "string", "description": ""}, "hlpIds": {"type": "array", "items": {"type": "string"}, "description": ""}, "sequences": {"type": "array", "items": {"type": "integer"}, "description": ""}, "updatedTime": {"type": "string", "description": ""}, "createdTime": {"type": "string", "description": ""}, "customerId": {"type": "string", "description": ""}, "limit": {"type": "integer", "description": ""}, "autoUpdateOccupancyFlag": {"type": "boolean", "description": ""}, "spaceStatus": {"type": "string", "description": "", "enum": ["EMPTY", "OCCUPIED", "FULL", "RESERVED"]}, "spaceStatuses": {"type": "array", "items": {"type": "string", "enum": ["EMPTY", "OCCUPIED", "FULL", "RESERVED"]}, "description": ""}, "excludeSpaceStatuses": {"type": "array", "items": {"type": "string"}, "description": ""}, "dockStatus": {"type": "string", "description": "", "enum": ["AVAILABLE", "RESERVED", "OCCUPIED"]}, "zone": {"type": "string", "description": ""}, "zones": {"type": "array", "items": {"type": "string"}, "description": ""}, "aisle": {"type": "string", "description": ""}, "aisles": {"type": "array", "items": {"type": "string"}, "description": ""}, "aisleFrom": {"type": "string", "description": ""}, "aisleTo": {"type": "string", "description": ""}, "bay": {"type": "string", "description": ""}, "bays": {"type": "array", "items": {"type": "string"}, "description": ""}, "bayFrom": {"type": "string", "description": ""}, "bayTo": {"type": "string", "description": ""}, "level": {"type": "integer", "description": ""}, "levelFrom": {"type": "integer", "description": ""}, "levelTo": {"type": "integer", "description": ""}, "slot": {"type": "string", "description": ""}, "keyword": {"type": "string", "description": ""}, "twoSigns": {"type": "boolean", "description": ""}, "pickToLightId": {"type": "string", "description": ""}, "dynTxtPropertyValue01": {"type": "string", "description": "动态文本字段查询 - 只保留精确匹配"}, "dynTxtPropertyValue02": {"type": "string", "description": ""}, "dynTxtPropertyValue03": {"type": "string", "description": ""}, "dynTxtPropertyValue04": {"type": "string", "description": ""}, "dynTxtPropertyValue05": {"type": "string", "description": ""}, "dynTxtPropertyValue06": {"type": "string", "description": ""}, "dynTxtPropertyValue07": {"type": "string", "description": ""}, "dynTxtPropertyValue08": {"type": "string", "description": ""}, "dynTxtPropertyValue09": {"type": "string", "description": ""}, "dynTxtPropertyValue10": {"type": "string", "description": ""}, "dynDatePropertyValue01From": {"type": "string", "description": "动态时间字段查询 - 只保留大小比较"}, "dynDatePropertyValue01To": {"type": "string", "description": ""}, "dynDatePropertyValue02From": {"type": "string", "description": ""}, "dynDatePropertyValue02To": {"type": "string", "description": ""}, "dynDatePropertyValue03From": {"type": "string", "description": ""}, "dynDatePropertyValue03To": {"type": "string", "description": ""}, "dynDatePropertyValue04From": {"type": "string", "description": ""}, "dynDatePropertyValue04To": {"type": "string", "description": ""}, "dynDatePropertyValue05From": {"type": "string", "description": ""}, "dynDatePropertyValue05To": {"type": "string", "description": ""}}}, "RPageResultLocationDto": {"type": "object", "properties": {"code": {"type": "integer", "description": ""}, "msg": {"type": "string", "description": ""}, "success": {"type": "boolean", "description": ""}, "data": {"$ref": "#/components/schemas/PageResultLocationDto", "description": ""}}}, "PageResultLocationDto": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/components/schemas/LocationDto", "description": "com.item.inventory.application.location.dto.LocationDto"}, "description": ""}, "totalCount": {"type": "integer", "description": ""}, "currentPage": {"type": "integer", "description": ""}, "pageSize": {"type": "integer", "description": ""}, "totalPage": {"type": "integer", "description": ""}}}, "LocationDto": {"type": "object", "properties": {"id": {"type": "string", "description": ""}, "name": {"type": "string", "description": ""}, "akaName": {"type": "string", "description": ""}, "floor": {"type": "integer", "description": ""}, "parentId": {"type": "string", "description": ""}, "type": {"type": "string", "description": "", "enum": ["LOCATION", "PICK", "STAGING", "DOCK", "AIR_ROB", "REWORK"]}, "maxSize": {"type": "number", "description": ""}, "length": {"type": "number", "description": ""}, "width": {"type": "number", "description": ""}, "height": {"type": "number", "description": ""}, "linearUnit": {"type": "string", "description": "", "enum": ["CM", "INCH", "FEET", "M"]}, "status": {"type": "string", "description": "", "enum": ["USABLE", "DISABLED", "DELETE", "MERGED", "MIXTURE"]}, "spaceStatus": {"type": "string", "description": "", "enum": ["EMPTY", "OCCUPIED", "FULL", "RESERVED"]}, "dockStatus": {"type": "string", "description": "", "enum": ["AVAILABLE", "RESERVED", "OCCUPIED"]}, "hlpId": {"type": "string", "description": ""}, "supportPickType": {"type": "string", "description": "", "enum": ["PALLET_PICK", "CASE_PICK", "PIECE_PICK", "NONE"]}, "category": {"type": "string", "description": "", "enum": ["YARD", "WAREHOUSE", "DOCK"]}, "sequence": {"type": "integer", "description": ""}, "capacityType": {"type": "string", "description": "", "enum": ["PALLET", "VOLUME", "RACK", "SMALL", "LARGE"]}, "capacity": {"type": "number", "description": ""}, "customerIds": {"type": "array", "items": {"type": "string"}, "description": ""}, "tag": {"type": "string", "description": ""}, "aisle": {"type": "string", "description": ""}, "bay": {"type": "string", "description": ""}, "section": {"type": "string", "description": ""}, "level": {"type": "integer", "description": ""}, "slot": {"type": "string", "description": ""}, "enableLocationHLP": {"type": "boolean", "description": ""}, "disallowToMixItemOnSameLocation": {"type": "boolean", "description": ""}, "putAwaySuggestionWeight": {"type": "number", "description": ""}, "lastCountDate": {"type": "string", "description": ""}, "features": {"type": "array", "items": {"type": "string"}, "description": ""}, "dynTxtPropertyValue01": {"type": "string", "description": ""}, "dynTxtPropertyValue02": {"type": "string", "description": ""}, "dynTxtPropertyValue03": {"type": "string", "description": ""}, "dynTxtPropertyValue04": {"type": "string", "description": ""}, "dynTxtPropertyValue05": {"type": "string", "description": ""}, "dynTxtPropertyValue06": {"type": "string", "description": ""}, "dynTxtPropertyValue07": {"type": "string", "description": ""}, "dynTxtPropertyValue08": {"type": "string", "description": ""}, "dynTxtPropertyValue09": {"type": "string", "description": ""}, "dynTxtPropertyValue10": {"type": "string", "description": ""}, "dynDatePropertyValue01": {"type": "string", "description": ""}, "dynDatePropertyValue02": {"type": "string", "description": ""}, "dynDatePropertyValue03": {"type": "string", "description": ""}, "dynDatePropertyValue04": {"type": "string", "description": ""}, "dynDatePropertyValue05": {"type": "string", "description": ""}, "createdTime": {"type": "string", "description": ""}, "updatedTime": {"type": "string", "description": ""}, "createdBy": {"type": "string", "description": ""}, "updatedBy": {"type": "string", "description": ""}, "inventoryStatus": {"type": "string", "description": "", "enum": ["OPEN", "DAMAGE", "ONHOLD", "ADJUSTOUT", "RECEIVING", "PICKED", "PACKED", "LOADED", "SHIPPED"]}, "entryId": {"type": "string", "description": ""}}}}, "securitySchemes": {}}, "servers": []}