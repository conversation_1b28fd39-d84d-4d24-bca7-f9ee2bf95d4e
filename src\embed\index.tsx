'use client';

import React from 'react';
import { createRoot, type Root } from 'react-dom/client';
import EmbeddedChat from './EmbeddedChat';
// Import CSS file, webpack will convert it to a string
import styles from './embed-chat.css';
import dayPickerStyles from 'react-day-picker/dist/style.css';
import { setSite } from '../utils/SiteContext';
import { initClientUserContextManager } from '../utils/clientUserContext';
// Global state
let currentTheme: 'light' | 'dark' = 'dark';
let rootElement: HTMLDivElement | null = null;
let containerElement: HTMLDivElement | null = null;
let shadowRoot: ShadowRoot | null = null;
let styleElement: HTMLStyleElement | null = null;
let isMinimized = true;
let reactRoot: Root | null = null;
let isDragging = false;
let dragStartPosition = { x: 0, y: 0 };
let containerStartPosition = { x: 0, y: 0 };
let chatIconElement: HTMLDivElement | null = null;
let currentPosition: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left' = 'bottom-right';

// 添加获取租户ID和设施ID的接口和实现
interface ContextDataProvider {
  getTenantId(): string | null;
  getFacilityId(): string | null;
}

class LocalStorageContextDataProvider implements ContextDataProvider {
  private tenantIdKeys = ['defaultCompanyId', 'tenantId', 'companyId','company_id'];
  private facilityIdKeys = ['defaultFacility', 'facility', 'facilityId','facility_id'];

  getTenantId(): string | null {
    try {
      for (const key of this.tenantIdKeys) {
        const value = localStorage.getItem(key);
        if (value) return value;
      }
      return null;
    } catch (e) {
      console.error('Error getting tenant ID:', e);
      return null;
    }
  }

  getFacilityId(): string | null {
    try {
      for (const key of this.facilityIdKeys) {
        const value = localStorage.getItem(key);
        if (!value) continue;
        // 尝试解析 JSON，如果失败则直接返回字符串
        try {
          const facility = JSON.parse(value);
          if (facility && facility.id) return facility.id;
        } catch {
          // 不是 JSON，直接返回字符串
          return value;
        }
      }
      return null;
    } catch (e) {
      console.error('Error getting facility ID:', e);
      return null;
    }
  }
}

// 实例化上下文数据提供者
const contextDataProvider: ContextDataProvider = new LocalStorageContextDataProvider();

function getTenantId(): string | null {
  return contextDataProvider.getTenantId();
}

function getFacilityId(): string | null {
  return contextDataProvider.getFacilityId();
}

// ================= ConfigLoader 抽象与实现 =================
interface SiteConfig {
  siteId: string;
  theme?: 'light' | 'dark';
  [key: string]: any;
}

interface ConfigLoader {
  loadConfig(siteId: string): Promise<SiteConfig>;
}

// 默认实现：本地静态配置，未来可远程拉取
class StaticConfigLoader implements ConfigLoader {
  private staticConfigs: Record<string, SiteConfig> = {
    'default': { siteId: 'default',  theme: 'dark' },
    'wms': { siteId: 'wms',  theme: 'dark' },
    // 可扩展更多站点配置
  };
  async loadConfig(siteId: string): Promise<SiteConfig> {
    return this.staticConfigs[siteId] || this.staticConfigs['default'];
  }
}

// ========== 主流程组合 ==========
const configLoader: ConfigLoader = new StaticConfigLoader();

// Main API object
const CyberAgent = {
  /**
   * Initialize and render chat component
   */
  init(options: {
    theme?: 'light' | 'dark';
    position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';
    minimized?: boolean;
    siteId?: string;
    welcomeSuggestions?: string[];
    apiUrl?: string;
    hideToolUsage?: boolean;
    historyCacheTime?: number;
    publicKey?: string;
  } = {}) {
    // If already initialized, do not repeat initialization
    if (containerElement) return;

    const { 
      theme = 'dark', 
      position = 'bottom-right', 
      minimized = true, 
      siteId = 'default', 
      welcomeSuggestions, 
      apiUrl, 
      hideToolUsage, 
      historyCacheTime,
      publicKey 
    } = options;
    
    
    currentTheme = theme;
    isMinimized = minimized;
    currentPosition = position;

    // 设置站点上下文
    setSite(siteId);
    initClientUserContextManager();

    // Create container
    containerElement = document.createElement('div');

    // Set styles based on position
    let positionStyle = '';
    switch (position) {
      case 'bottom-right':
        positionStyle = 'bottom: 20px; right: 20px;';
        break;
      case 'bottom-left':
        positionStyle = 'bottom: 20px; left: 20px;';
        break;
      case 'top-right':
        positionStyle = 'top: 20px; right: 20px;';
        break;
      case 'top-left':
        positionStyle = 'top: 20px; left: 20px;';
        break;
    }

    containerElement.style.cssText = `
      position: fixed;
      ${positionStyle}
      width: 350px;
      height: 500px;
      z-index: 9999;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      transition: all 0.3s ease;
      resize: both;
      min-width: 300px;
      min-height: 400px;
      max-width: 90vw;
      max-height: 90vh;
      background-color: transparent;
    `;

    document.body.appendChild(containerElement);

    // Create Shadow DOM - use closed mode for security and isolation
    shadowRoot = containerElement.attachShadow({ mode: 'closed' });

    // Create style element
    styleElement = document.createElement('style');

    // Add styles from external CSS file
    // webpack's css-loader will convert CSS file to a string
    // Add additional global scrollbar styles to ensure they apply to all elements in the Shadow DOM
    const scrollbarStyles = `
      /* Ensure scrollbar styles apply to all elements */
      * {
        scrollbar-width: thin;
        scrollbar-color: #334155 #1e293b;
      }
    `;
    styleElement.textContent = dayPickerStyles + styles + scrollbarStyles;
    shadowRoot.appendChild(styleElement);

    // Create root element
    rootElement = document.createElement('div');
    rootElement.className = 'cyber-agent-root';
    rootElement.style.width = '100%';
    rootElement.style.height = '100%';
    shadowRoot.appendChild(rootElement);

    // 新增：创建弹层 Portal 容器
    const portalContainer = document.createElement('div');
    portalContainer.id = 'cyberagent-portal-root';
    shadowRoot.appendChild(portalContainer);
    // 便于 React 组件访问
    (window as any).__cyberagent_portal_container = portalContainer;


    // 加载站点配置并渲染
    configLoader.loadConfig(siteId).then((config) => {
      // 合并配置，优先使用传入的参数
      const mergedConfig = {
        ...config,
        ...(hideToolUsage !== undefined && { hideToolUsage }),
        ...(historyCacheTime !== undefined && { historyCacheTime })
      };
      // 透传 config
      this.render(null, mergedConfig, siteId, welcomeSuggestions, apiUrl, publicKey);
    });

    // Create chat icon element but don't append it yet
    chatIconElement = document.createElement('div');
    chatIconElement.className = 'chat-icon';
    chatIconElement.innerHTML = `
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
      </svg>
    `;
    chatIconElement.style.cssText = `
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
      color: white;
      cursor: pointer;
    `;
    
    // Add click event to maximize when icon is clicked
    chatIconElement.addEventListener('click', () => {
      this.maximize();
    });

    // Apply minimized state if needed
    if (isMinimized) {
      this.minimize();
    } else {
      this.maximize();
    }

    // Trigger ready event
    window.dispatchEvent(new CustomEvent('cyber-agent-ready'));

    return this;
  },

  /**
   * Render chat component
   */
  render(token?: string | null, config?: SiteConfig, siteId?: string, welcomeSuggestions?: string[], apiUrl?: string, publicKey?: string) {
    if (!rootElement) return;
    // 获取租户ID和设施ID
    const tenantId = getTenantId();
    const facilityId = getFacilityId();

    // If React root hasn't been created yet, create one
    if (!reactRoot) {
      reactRoot = createRoot(rootElement);
      
      // Render the component using modern React API
    reactRoot.render(
      <EmbeddedChat
        token={token || undefined}
        domain={window.location.hostname}
        theme={currentTheme}
        config={config}
        tenantId={tenantId || undefined}
        facilityId={facilityId || undefined}
        siteId={siteId || (config?.siteId ? config.siteId : 'default')}
        welcomeSuggestions={welcomeSuggestions}
        apiUrl={apiUrl}
        hideToolUsage={config?.hideToolUsage || false}
        publicKey={publicKey}
        onError={(error) => {
          console.error('Embedded chat error:', error);
        }}
        onMinimize={() => {
          if (isMinimized) {
            this.maximize();
          } else {
            this.minimize();
          }
      }}
      />
    );
    } else {
      // Update the container class but don't re-render the React component
      if (containerElement) {
        if (isMinimized) {
          containerElement.classList.add('minimized');
        } else {
          containerElement.classList.remove('minimized');
        }
      }
    }

    // Add drag functionality
    this.setupDraggable();
  },

  /**
   * Create drag area - use chat header as drag handle
   */
  setupDraggable() {
    if (!containerElement || !shadowRoot) return;

    // Create drag area - use chat header as drag handle
    const handleMouseDown = (e: MouseEvent) => {
      // Don't start dragging if minimized
      if (isMinimized) {
        // Handle single click to maximize when minimized
        this.maximize();
        return;
      }

      // If clicked on toggle button, don't start dragging
      if (e.target instanceof Element &&
          (e.target.closest('button') ||
           e.target.tagName === 'BUTTON' ||
           e.target.tagName === 'SVG' ||
           e.target.tagName === 'PATH')) {
        return;
      }

      // Get current container position
      const rect = containerElement!.getBoundingClientRect();
      containerStartPosition = {
        x: rect.left,
        y: rect.top
      };

      // Record mouse start position
      dragStartPosition = {
        x: e.clientX,
        y: e.clientY
      };

      isDragging = true;

      // Add dragging styles
      containerElement!.style.transition = 'none';
      containerElement!.style.opacity = '0.9';
      containerElement!.classList.add('dragging');

      // Prevent default behavior and bubbling
      e.preventDefault();
      e.stopPropagation();
    };

    const handleMouseMove = (e: MouseEvent) => {
      if (!isDragging || !containerElement) return;

      // Calculate new position
      const newLeft = containerStartPosition.x + (e.clientX - dragStartPosition.x);
      const newTop = containerStartPosition.y + (e.clientY - dragStartPosition.y);

      // Set new position
      containerElement.style.left = `${newLeft}px`;
      containerElement.style.top = `${newTop}px`;
      containerElement.style.right = 'auto';
      containerElement.style.bottom = 'auto';

      // Make sure the position is fixed
      containerElement.style.position = 'fixed';

      // Add dragging class to container
      containerElement.classList.add('dragging');

      // Prevent default behavior and bubbling
      e.preventDefault();
      e.stopPropagation();
    };

    const handleMouseUp = () => {
      if (!isDragging || !containerElement) return;

      isDragging = false;

      // Restore normal styles
      containerElement.style.transition = 'all 0.3s ease';
      containerElement.style.opacity = '1';

      // Remove dragging class
      containerElement.classList.remove('dragging');
    };

    // Add event listener to header area of shadow DOM
    shadowRoot.addEventListener('mousedown', (e: Event) => {
      // Start dragging if clicked on header area (but not when minimized)
      if (e instanceof MouseEvent && e.target instanceof Element) {
        if (isMinimized) {
          // Handle click on minimized chat to maximize
          this.maximize();
        } else if (e.target.closest('.chat-header') ||
           e.target.className === 'chat-header' ||
           (typeof e.target.className === 'string' && e.target.className.includes('chat-title'))) {
          handleMouseDown(e);
        }
      }
    });

    // Add global event listener
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  },

  /**
   * Minimize chat
   */
  minimize() {
    if (!containerElement || !shadowRoot || !rootElement || !chatIconElement) return;

    isMinimized = true;

    // Set styles for minimized state
    containerElement.style.width = '50px';
    containerElement.style.height = '50px';
    containerElement.style.minWidth = '50px';
    containerElement.style.minHeight = '50px';
    containerElement.style.resize = 'none';
    containerElement.style.borderRadius = '50%'; // Make it circular
    containerElement.style.overflow = 'hidden';
    containerElement.style.backgroundColor = '#1e293b'; // Dark slate color that matches the theme
    containerElement.style.position = 'fixed'; // Ensure position remains fixed
    containerElement.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.3)';
    containerElement.style.cursor = 'pointer'; // Change cursor on hover
    
    // Set minimized position based on the position prop
    containerElement.style.top = '';
    containerElement.style.right = '';
    containerElement.style.bottom = '';
    containerElement.style.left = '';

    switch (currentPosition) {
      case 'bottom-right':
        containerElement.style.bottom = '20px';
        containerElement.style.right = '20px';
        break;
      case 'bottom-left':
        containerElement.style.bottom = '20px';
        containerElement.style.left = '20px';
        break;
      case 'top-right':
        containerElement.style.top = '20px';
        containerElement.style.right = '20px';
        break;
      case 'top-left':
        containerElement.style.top = '20px';
        containerElement.style.left = '20px';
        break;
      default:
        containerElement.style.bottom = '20px';
        containerElement.style.right = '20px';
    }

    // Add minimized class
    containerElement.classList.add('minimized');
    
    // Hide the main chat content
    rootElement.style.display = 'none';
    
    // Show the chat icon
    if (!shadowRoot.contains(chatIconElement)) {
      shadowRoot.appendChild(chatIconElement);
    }
    chatIconElement.style.display = 'flex';

    // Dispatch event
    window.dispatchEvent(new CustomEvent('cyber-agent-state-change', {
      detail: { isMinimized: true }
    }));
  },

  /**
   * Maximize chat
   */
  maximize() {
    if (!containerElement || !shadowRoot || !rootElement || !chatIconElement) return;

    isMinimized = false;

    // Restore styles for maximized state
    containerElement.style.width = '350px';
    containerElement.style.height = '500px';
    containerElement.style.minWidth = '300px';
    containerElement.style.minHeight = '400px';
    containerElement.style.maxWidth = '90vw';
    containerElement.style.maxHeight = '90vh';
    containerElement.style.resize = 'both';
    containerElement.style.borderRadius = '8px';
    containerElement.style.overflow = 'hidden';
    containerElement.style.backgroundColor = '#111827'; // Same as --bg-color-dark in CSS
    containerElement.style.position = 'fixed'; // Ensure position remains fixed
    containerElement.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
    containerElement.style.cursor = 'default';

    // Remove minimized class
    containerElement.classList.remove('minimized');

    // Hide the chat icon
    chatIconElement.style.display = 'none';
    
    // Show the main chat content
    rootElement.style.display = 'block';

    // Remove notification indicator if it exists
    const indicator = shadowRoot.querySelector('.notification-indicator');
    if (indicator && indicator.parentElement) {
      indicator.parentElement.removeChild(indicator);
    }

    // Dispatch event
    window.dispatchEvent(new CustomEvent('cyber-agent-state-change', {
      detail: { isMinimized: false }
    }));
  },

  /**
   * Toggle chat window state
   */
  toggle() {
    if (isMinimized) {
      this.maximize();
    } else {
      this.minimize();
    }
  },

  /**
   * Set theme
   */
  setTheme(theme: 'light' | 'dark') {
    currentTheme = theme;
    this.render();
  },

  /**
   * Get current theme
   */
  getTheme() {
    return currentTheme;
  },

  /**
   * Get current position
   */
  getPosition() {
    if (!containerElement) return 'bottom-right';

    // Determine current position from styles
    if (containerElement.style.bottom && containerElement.style.right) return 'bottom-right';
    if (containerElement.style.bottom && containerElement.style.left) return 'bottom-left';
    if (containerElement.style.top && containerElement.style.right) return 'top-right';
    if (containerElement.style.top && containerElement.style.left) return 'top-left';

    return 'bottom-right';
  },

  /**
   * Set position
   */
  setPosition(position: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left') {
    if (!containerElement) return;

    // Reset position properties
    containerElement.style.top = '';
    containerElement.style.bottom = '';
    containerElement.style.left = '';
    containerElement.style.right = '';

    // Ensure position is fixed
    containerElement.style.position = 'fixed';

    // Set position based on parameter
    switch (position) {
      case 'bottom-right':
        containerElement.style.bottom = '20px';
        containerElement.style.right = '20px';
        break;
      case 'bottom-left':
        containerElement.style.bottom = '20px';
        containerElement.style.left = '20px';
        break;
      case 'top-right':
        containerElement.style.top = '20px';
        containerElement.style.right = '20px';
        break;
      case 'top-left':
        containerElement.style.top = '20px';
        containerElement.style.left = '20px';
        break;
    }

    // Dispatch event
    window.dispatchEvent(new CustomEvent('cyber-agent-position-change', { detail: { position } }));
  },

  /**
   * Check if chat is minimized
   */
  isMinimized() {
    return isMinimized;
  },

  /**
   * Simulate receiving a message (for testing purposes)
   */
  simulateMessage(message: { role: 'user' | 'assistant', content: string }) {
    if (!shadowRoot) return;

    // Find the embedded chat component in the shadow DOM
    const chatComponent = shadowRoot.querySelector('.embedded-chat');
    if (!chatComponent) return;

    // Create a custom event to simulate receiving a message
    const event = new CustomEvent('cyber-agent-new-message', {
      detail: { message },
      bubbles: true,
      composed: true
    });

    // Dispatch event on the chat component
    chatComponent.dispatchEvent(event);

    // If chat is minimized and message is from assistant, show notification indicator
    if (isMinimized && message.role === 'assistant') {
      // Create notification indicator if it doesn't exist
      let indicator = shadowRoot.querySelector('.notification-indicator');
      if (!indicator) {
        indicator = document.createElement('div');
        indicator.className = 'notification-indicator';
        chatComponent.appendChild(indicator);
      }

      console.log('Notification indicator added for new message');
    }

    return true;
  },

  /**
   * Reset the chat state
   */
  reset() {
    if (!rootElement) return;

    // Re-render the component to reset state
    this.render();

    // Remove notification indicator if it exists
    if (shadowRoot) {
      const indicator = shadowRoot.querySelector('.notification-indicator');
      if (indicator && indicator.parentElement) {
        indicator.parentElement.removeChild(indicator);
      }
    }

    console.log('Chat state reset');
  },

  /**
   * Unmount chat component
   */
  destroy() {
    // Use modern React API to unmount component
    if (reactRoot) {
      reactRoot.unmount();
      reactRoot = null;
    }

    if (containerElement && containerElement.parentNode) {
      containerElement.parentNode.removeChild(containerElement);
    }

    rootElement = null;
    containerElement = null;
    shadowRoot = null;
    styleElement = null;
  },
};

// 读取全局注入的配置
const EMBED_SITE_CONFIG = (typeof __SITE_CONFIG__ !== 'undefined') ? __SITE_CONFIG__ : {};

// Auto initialization (can be manually initialized via window.CyberAgent.init())
document.addEventListener('DOMContentLoaded', () => {
  // Auto initialization
  CyberAgent.init({
    theme: EMBED_SITE_CONFIG.theme,
    position: EMBED_SITE_CONFIG.position,
    siteId: EMBED_SITE_CONFIG.siteId,
    welcomeSuggestions: EMBED_SITE_CONFIG.welcomeSuggestions,
    apiUrl: EMBED_SITE_CONFIG.apiUrl,
    hideToolUsage: EMBED_SITE_CONFIG.hideToolUsage,
    historyCacheTime: EMBED_SITE_CONFIG.historyCacheTime,
    publicKey: EMBED_SITE_CONFIG.publicKey
  });
});

// Expose to global
if (typeof window !== 'undefined') {
  (window as any).CyberAgent = CyberAgent;
}

// 声明全局变量（由webpack注入）
declare const __SITE_CONFIG__: any;

export default CyberAgent;
