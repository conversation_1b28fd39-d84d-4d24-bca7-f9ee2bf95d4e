'use client';

import React, { createContext, useState, useContext, ReactNode } from 'react';

interface SuccessContextType {
  success: string | null;
  setSuccess: (message: string | null) => void;
  clearSuccess: () => void;
}

const SuccessContext = createContext<SuccessContextType | undefined>(undefined);

export function SuccessProvider({ children }: { children: ReactNode }) {
  const [success, setSuccessState] = useState<string | null>(null);
  
  const setSuccess = (message: string | null) => {
    setSuccessState(message);
  };
  
  const clearSuccess = () => {
    setSuccessState(null);
  };
  
  return (
    <SuccessContext.Provider value={{ success, setSuccess, clearSuccess }}>
      {children}
    </SuccessContext.Provider>
  );
}

export function useSuccess() {
  const context = useContext(SuccessContext);
  if (context === undefined) {
    throw new Error('useSuccess must be used within a SuccessProvider');
  }
  return context;
}
