'use client';

import { clientUserContextManager } from '@/utils/clientUserContext';
import {
  UserRole,
  IAM_ROLE_MAPPING,
  PROTECTED_ROUTES,
  getUserRolesByIamRoles as baseGetUserRolesByIamRoles,
  hasPermission as baseHasPermission
} from '@/utils/permissionConfigBase';

// 重新导出基础配置
export { UserRole, IAM_ROLE_MAPPING, PROTECTED_ROUTES };

// 检查用户是否有访问特定路由的权限
export const hasPermission = (
  userRoles: string[] | undefined,
  pathname: string
): boolean => {
  // 使用基础配置中的 hasPermission 函数
  return baseHasPermission(userRoles, pathname, PROTECTED_ROUTES);
};

// 从 IAM 角色名称获取系统角色
export const getUserRolesByIamRoles = (iamRoles: string[] | undefined): UserRole[] => {
  // 使用基础配置中的 getUserRolesByIamRoles 函数
  return baseGetUserRolesByIamRoles(iamRoles);
};

// 从用户信息中获取角色名称
export const getRoleNamesFromUserInfo = (): string[] => {
  // 使用 clientUserContextManager 获取角色名称
  return clientUserContextManager.getUserRoleNames();
};

// 从 IAM 用户信息中获取用户角色
export const getUserRolesByUserId = (userId: string | null): UserRole[] => {
  if (!userId) return [];

  try {
    // 直接从 IAM 用户信息中获取角色名称
    const roleNames = getRoleNamesFromUserInfo();
    if (roleNames.length > 0) {
      return getUserRolesByIamRoles(roleNames);
    }
  } catch (error) {
    console.error('获取用户角色失败:', error);
  }

  // 默认只返回基本用户角色
  return [UserRole.USER];
};
