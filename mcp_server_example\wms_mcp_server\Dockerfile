FROM python:3.11-slim

WORKDIR /app

# 安装依赖
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用程序代码
COPY . .

# 设置默认环境变量
ENV WMS_BASE_URL=https://api.example.com/wms
ENV CHATBOT_CLIENT_ID=default_client_id
ENV CHATBOT_CLIENT_SECRET=default_client_secret
ENV IAM_URL=https://id.example.com

# 暴露端口
EXPOSE 8000

# 启动应用
CMD ["python", "mcp_server/wms_mcp_server.py", "--host", "0.0.0.0", "--port", "8000", "--transport", "sse"] 