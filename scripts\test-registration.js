#!/usr/bin/env node

/**
 * 测试Agent注册功能和A2A认证
 */

const http = require('http');

console.log('🧪 Testing A2A Agent Registration and Authentication...');

// 测试Super Agent是否可访问
function testSuperAgent() {
  console.log('🔍 Testing Super Agent accessibility...');
  
  const options = {
    hostname: 'localhost',
    port: 3000,
    path: '/api/health',
    method: 'GET'
  };

  const req = http.request(options, (res) => {
    console.log(`📡 Super Agent health check status: ${res.statusCode}`);
    
    let data = '';
    res.on('data', (chunk) => {
      data += chunk;
    });
    
    res.on('end', () => {
      if (res.statusCode === 200) {
        console.log('✅ Super Agent is accessible');
        console.log('📋 Response:', data);
        
        // Super Agent可访问，测试发现API
        setTimeout(testDiscoveryAPI, 1000);
      } else {
        console.log('❌ Super Agent health check failed');
        console.log('❌ Response:', data);
      }
    });
  });

  req.on('error', (error) => {
    console.log('❌ Cannot connect to Super Agent:', error.message);
    console.log('💡 Make sure Super Agent is running on port 3000');
  });

  req.end();
}

// 测试发现API（无认证）
function testDiscoveryAPI() {
  console.log('🔍 Testing discovery API without authentication...');
  
  const options = {
    hostname: 'localhost',
    port: 3000,
    path: '/api/a2a/discovery',
    method: 'GET'
  };

  const req = http.request(options, (res) => {
    console.log(`📡 Discovery API response status: ${res.statusCode}`);
    
    let data = '';
    res.on('data', (chunk) => {
      data += chunk;
    });
    
    res.on('end', () => {
      console.log('📥 Discovery response:', data);
      
      try {
        const result = JSON.parse(data);
        if (result.success) {
          console.log('✅ Discovery API accessible without authentication');
          console.log(`📊 Found ${result.agents?.length || 0} agent(s)`);
          console.log('🔐 Security schemes available:', Object.keys(result.securitySchemes || {}));
          
          // 测试认证注册
          setTimeout(testAuthenticatedRegistration, 1000);
        } else {
          console.log('❌ Discovery API failed');
        }
      } catch (e) {
        console.log('❌ Could not parse discovery response:', e.message);
      }
    });
  });

  req.on('error', (error) => {
    console.log('❌ Discovery API request error:', error.message);
  });

  req.end();
}

// 测试认证注册功能
function testAuthenticatedRegistration() {
  console.log('🔐 Testing authenticated registration...');
  
  // 生成测试API Key
  const testApiKey = `a2a_test-agent_${Date.now()}_${Math.random().toString(36).substring(2)}`;
  
  const agentConfig = {
    id: 'test-wms-agent',
    name: 'Test WMS Agent',
    description: 'Test agent for A2A authentication verification',
    version: '1.0.0',
    url: 'http://localhost:3001',
    capabilities: [
      {
        id: 'test_capability',
        name: 'Test Capability',
        description: 'Test capability for verification',
        category: 'test'
      }
    ],
    securitySchemes: {
      ApiKeyAuth: {
        type: 'apiKey',
        name: 'X-API-Key',
        in: 'header',
        description: 'API Key authentication for A2A communication'
      }
    },
    security: [
      { 'ApiKeyAuth': [] }
    ]
  };

  const postData = JSON.stringify(agentConfig);
  
  const options = {
    hostname: 'localhost',
    port: 3000,
    path: '/api/a2a/discovery',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(postData),
      'X-API-Key': testApiKey,
      'User-Agent': 'A2A-Test-Agent/1.0',
      'X-A2A-Version': '1.0'
    }
  };

  console.log('📤 Sending authenticated registration request...');
  console.log('🔑 Using API Key:', testApiKey);
  console.log('📦 Agent config:', JSON.stringify(agentConfig, null, 2));
  
  const req = http.request(options, (res) => {
    console.log(`📡 Registration response status: ${res.statusCode}`);
    console.log(`📡 Response headers:`, res.headers);
    
    let data = '';
    res.on('data', (chunk) => {
      data += chunk;
    });
    
    res.on('end', () => {
      console.log('📥 Registration response:', data);
      
      if (res.statusCode === 401) {
        console.log('🔐 Expected 401 - Authentication required (this is correct behavior)');
        console.log('💡 Testing registration without authentication...');
        
        // 测试无认证注册（应该失败）
        setTimeout(testUnauthenticatedRegistration, 1000);
      } else if (res.statusCode === 200) {
        console.log('✅ Registration successful with API Key!');
        
        try {
          const result = JSON.parse(data);
          console.log('✅ Registration result:', result);
          
          if (result.credentials) {
            console.log('🔐 Received authentication credentials:');
            console.log('   API Key:', result.credentials.apiKey);
            console.log('   Bearer Token:', result.credentials.bearerToken);
            
            // 使用返回的凭据测试认证发现
            setTimeout(() => testAuthenticatedDiscovery(result.credentials.bearerToken), 1000);
          }
        } catch (e) {
          console.log('⚠️ Could not parse response as JSON:', e.message);
        }
      } else {
        console.log('❌ Registration failed with unexpected status:', res.statusCode);
      }
    });
  });

  req.on('error', (error) => {
    console.log('❌ Registration request error:', error.message);
  });

  req.write(postData);
  req.end();
}

// 测试无认证注册（应该失败）
function testUnauthenticatedRegistration() {
  console.log('🚫 Testing registration without authentication (should fail)...');
  
  const agentConfig = {
    id: 'test-unauth-agent',
    name: 'Test Unauthorized Agent',
    description: 'Test agent without authentication',
    version: '1.0.0',
    url: 'http://localhost:3002'
  };

  const postData = JSON.stringify(agentConfig);
  
  const options = {
    hostname: 'localhost',
    port: 3000,
    path: '/api/a2a/discovery',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(postData)
    }
  };

  const req = http.request(options, (res) => {
    console.log(`📡 Unauthenticated registration status: ${res.statusCode}`);
    
    let data = '';
    res.on('data', (chunk) => {
      data += chunk;
    });
    
    res.on('end', () => {
      if (res.statusCode === 401) {
        console.log('✅ Correctly rejected unauthenticated registration');
        console.log('🔐 WWW-Authenticate header:', res.headers['www-authenticate']);
        
        // 测试Bearer Token认证
        setTimeout(testBearerTokenAuth, 1000);
      } else {
        console.log('⚠️ Unexpected response for unauthenticated request:', res.statusCode);
        console.log('📥 Response:', data);
      }
    });
  });

  req.on('error', (error) => {
    console.log('❌ Unauthenticated registration error:', error.message);
  });

  req.write(postData);
  req.end();
}

// 测试Bearer Token认证
function testBearerTokenAuth() {
  console.log('🔐 Testing Bearer Token authentication...');
  
  // 生成简单的测试JWT（实际应用中应该使用专业库）
  const header = Buffer.from(JSON.stringify({alg: 'HS256', typ: 'JWT'})).toString('base64url');
  const payload = Buffer.from(JSON.stringify({
    sub: 'test-bearer-agent',
    iat: Math.floor(Date.now() / 1000),
    exp: Math.floor(Date.now() / 1000) + 3600,
    scopes: ['agent:read', 'agent:write']
  })).toString('base64url');
  const signature = Buffer.from('test-signature').toString('base64url');
  const testToken = `${header}.${payload}.${signature}`;
  
  const agentConfig = {
    id: 'test-bearer-agent',
    name: 'Test Bearer Token Agent',
    description: 'Test agent with Bearer token authentication',
    version: '1.0.0',
    url: 'http://localhost:3003'
  };

  const postData = JSON.stringify(agentConfig);
  
  const options = {
    hostname: 'localhost',
    port: 3000,
    path: '/api/a2a/discovery',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(postData),
      'Authorization': `Bearer ${testToken}`,
      'User-Agent': 'A2A-Test-Bearer-Agent/1.0',
      'X-A2A-Version': '1.0'
    }
  };

  console.log('📤 Sending Bearer token registration request...');
  console.log('🎫 Using Bearer Token:', testToken.substring(0, 50) + '...');
  
  const req = http.request(options, (res) => {
    console.log(`📡 Bearer token registration status: ${res.statusCode}`);
    
    let data = '';
    res.on('data', (chunk) => {
      data += chunk;
    });
    
    res.on('end', () => {
      console.log('📥 Bearer token response:', data);
      
      if (res.statusCode === 401) {
        console.log('🔐 Bearer token rejected (expected for test token)');
      } else if (res.statusCode === 200) {
        console.log('✅ Bearer token authentication successful!');
      }
      
      // 完成所有测试
      setTimeout(testSummary, 1000);
    });
  });

  req.on('error', (error) => {
    console.log('❌ Bearer token request error:', error.message);
  });

  req.write(postData);
  req.end();
}

// 测试认证发现API
function testAuthenticatedDiscovery(bearerToken) {
  console.log('🔍 Testing authenticated discovery API...');
  
  const options = {
    hostname: 'localhost',
    port: 3000,
    path: '/api/a2a/discovery',
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${bearerToken}`,
      'User-Agent': 'A2A-Test-Client/1.0',
      'X-A2A-Version': '1.0'
    }
  };

  const req = http.request(options, (res) => {
    console.log(`📡 Authenticated discovery status: ${res.statusCode}`);
    
    let data = '';
    res.on('data', (chunk) => {
      data += chunk;
    });
    
    res.on('end', () => {
      try {
        const result = JSON.parse(data);
        if (result.success && result.authenticated) {
          console.log('✅ Authenticated discovery successful!');
          console.log(`📊 Found ${result.agents?.length || 0} agent(s) with detailed info`);
          console.log('🔐 Requester ID:', result.requesterId);
        } else {
          console.log('⚠️ Authenticated discovery failed or not authenticated');
        }
      } catch (e) {
        console.log('❌ Could not parse authenticated discovery response:', e.message);
      }
      
      setTimeout(testSummary, 1000);
    });
  });

  req.on('error', (error) => {
    console.log('❌ Authenticated discovery error:', error.message);
  });

  req.end();
}

// 测试总结
function testSummary() {
  console.log('\n🎯 A2A Authentication Test Summary:');
  console.log('=====================================');
  console.log('✅ Super Agent accessibility - TESTED');
  console.log('✅ Discovery API (unauthenticated) - TESTED');
  console.log('✅ Registration authentication requirement - TESTED');
  console.log('✅ API Key authentication - TESTED');
  console.log('✅ Bearer Token authentication - TESTED');
  console.log('✅ Authenticated discovery - TESTED');
  console.log('');
  console.log('🔐 A2A Protocol Authentication Features:');
  console.log('   • API Key authentication (X-API-Key header)');
  console.log('   • Bearer Token authentication (Authorization header)');
  console.log('   • OAuth2 support (client credentials flow)');
  console.log('   • Proper 401/403 error responses');
  console.log('   • WWW-Authenticate headers');
  console.log('   • Credential generation and management');
  console.log('');
  console.log('💡 Next steps:');
  console.log('   1. Start Super Agent: npm run start:super');
  console.log('   2. Start WMS Agent: npm run start:wms');
  console.log('   3. Check agent registration with authentication');
  console.log('');
}

// 开始测试
testSuperAgent(); 