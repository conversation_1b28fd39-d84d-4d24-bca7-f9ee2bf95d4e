import React from 'react';

export default function ClockCardEmbed({ toolInvocation }: { toolInvocation: any }) {
  const result = toolInvocation.result || {};
  
  // Extract clock data
  const time = result.time || '';
  const date = result.date || '';
  const timezone = result.timezone || 'Unknown timezone';
  const hourFormat = result.hourFormat || '24';
  
  // Format the display text
  const displayText = `${date} ${time} (${timezone})`;
  
  return (
    <div className="embed-card clock-tool p-2 my-1 bg-blue-800/20 rounded border-l-2 border-blue-600/30 border-t border-r border-b border-blue-700/20">
      <div className="flex items-center" style={{ whiteSpace: 'nowrap', width: '100%' }}>
        <div className="mr-2 text-blue-400 flex-shrink-0">
          <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <circle cx="12" cy="12" r="10"></circle>
            <polyline points="12 6 12 12 16 14"></polyline>
          </svg>
        </div>
        <div className="text-xs text-blue-300 flex-shrink-0 mr-2">Clock:</div>
        <div className="text-xs text-blue-400 overflow-hidden text-ellipsis" style={{ textOverflow: 'ellipsis', overflow: 'hidden', whiteSpace: 'nowrap' }}>
          {displayText}
        </div>
      </div>
    </div>
  );
} 