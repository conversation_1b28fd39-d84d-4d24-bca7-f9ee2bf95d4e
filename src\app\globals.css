@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 236, 236, 241; /* text_primary: #ECECF1 */
  --background-rgb: 32, 33, 35;   /* background_primary: #202123 */
  --highlight-rgb: 126, 95, 247;    /* accent_focus: #7E5FF7 */
  --secondary-text-rgb: 162, 162, 173; /* text_secondary: #A2A2AD */
  --heading-text-rgb: 236, 236, 241;   /* text_primary: #ECECF1 */
}

body {
  background-color: rgb(var(--background-rgb));
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  line-height: 1.7;
  letter-spacing: 0.015em;
}

@layer base {
  h1, h2, h3, h4, h5, h6 {
    color: rgb(var(--heading-text-rgb));
    font-weight: 600;
    line-height: 1.3;
  }

  p {
    margin-bottom: 0.75rem;
  }

  a {
    color: rgb(var(--highlight-rgb));
    text-decoration: none;
  }

  a:hover {
    text-decoration: underline;
  }

  code {
    font-family: 'Fira Code', Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace;
    font-size: 0.9em;
  }
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
  }
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .text-readable {
    @apply text-gray-300 leading-relaxed tracking-wide;
  }

  .text-readable-secondary {
    @apply text-gray-400 leading-relaxed;
  }

  .text-heading {
    @apply text-gray-200 font-semibold leading-snug;
  }
}

@keyframes pulse {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 1; }
}

/* 声浪动画 */
@keyframes soundWave {
  0%, 100% { 
    transform: scaleY(0.3);
    opacity: 0.6;
  }
  25% { 
    transform: scaleY(1.2);
    opacity: 1;
  }
  50% { 
    transform: scaleY(0.8);
    opacity: 0.8;
  }
  75% { 
    transform: scaleY(1.5);
    opacity: 1;
  }
}

.sound-wave-bar {
  animation: soundWave 1s ease-in-out infinite;
  transform-origin: center bottom;
}

.sound-wave-bar:nth-child(1) { animation-delay: 0ms; animation-duration: 800ms; }
.sound-wave-bar:nth-child(2) { animation-delay: 100ms; animation-duration: 600ms; }
.sound-wave-bar:nth-child(3) { animation-delay: 200ms; animation-duration: 900ms; }
.sound-wave-bar:nth-child(4) { animation-delay: 300ms; animation-duration: 700ms; }
.sound-wave-bar:nth-child(5) { animation-delay: 400ms; animation-duration: 650ms; }
.sound-wave-bar:nth-child(6) { animation-delay: 500ms; animation-duration: 750ms; }

/* 断开连接倒计时动画 */
@keyframes countdown {
  from {
    stroke-dashoffset: 113;
  }
  to {
    stroke-dashoffset: 0;
  }
}

/* Custom scrollbar for the new theme */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #202123; /* background_primary */
}

::-webkit-scrollbar-thumb {
  background: #565869; /* border_primary */
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #7E5FF7; /* accent_focus */
}

/* react-day-picker 样式已移至组件内 */

/* 防止AI回答抖动的样式 */
.messages-scroll-container {
  scroll-behavior: smooth;
  overscroll-behavior-y: contain;
}

.message-container {
  contain: layout;
  will-change: contents;
  overflow-anchor: none;
}

.ai-message-wrapper {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
  transition: height 0.1s ease-out;
}

/* 阅读优化 */
/* 已移除宽度限制 */

.prose p {
  @apply text-gray-300;
}

.prose strong {
  @apply text-[#ECECF1] font-semibold;
}

.prose blockquote {
  @apply border-l-4 border-[#565869] pl-4 py-1 my-2 text-[#A2A2AD] italic;
}

/* 代码块优化 */
.prose pre {
  @apply bg-[#343541]/70 rounded-md p-3 text-sm my-3;
}

.prose code {
  @apply text-[#BEB2F9] bg-[#343541] px-1.5 py-0.5 rounded text-sm; /* Lighter purple for code */
}

/* 表格优化 */
.prose table {
  @apply border-collapse border border-[#565869] my-3 w-full;
}

.prose thead {
  @apply bg-[#343541];
}

.prose tbody {
  @apply bg-[#202123]/50;
}

.prose th {
  @apply px-4 py-2 text-left font-medium text-[#ECECF1];
}

.prose td {
  @apply px-4 py-2 border-r border-[#565869] last:border-r-0 text-[#A2A2AD];
}

/* 列表优化 */
.prose ul {
  @apply list-disc pl-5 space-y-1 my-3;
}

.prose ol {
  @apply list-decimal pl-5 space-y-1 my-3;
}

.prose li {
  @apply pl-1 text-[#A2A2AD];
}



@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}