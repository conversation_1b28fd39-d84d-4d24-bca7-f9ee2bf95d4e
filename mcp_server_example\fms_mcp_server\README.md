# FMS MCP 服务器

基于MCP (Model Context Protocol) 的工厂管理系统工具服务器，为Claude、GPT等大模型提供工厂管理系统API调用功能。

## 概述

该服务器实现了MCP协议，提供以下功能：

- **工具 (Tools)**：
  - `find_fms_api`: 搜索匹配的FMS API接口
  - `call_fms_api`: 调用指定的FMS API

- **资源 (Resources)**：
  - `fms://api-docs`: FMS API文档摘要
  - `fms://tools-guide`: FMS工具使用指南

- **提示词 (Prompts)**：
  - `fms_guide`: FMS工具使用指南提示词

## 安装与配置

### 环境要求

- Python 3.9+
- pip 或 uv 包管理器

### 安装依赖

```bash
# 使用pip
pip install -r requirements.txt

# 或使用uv
uv pip install -r requirements.txt
```

### 配置

1. 创建配置文件：

```bash
# 复制示例配置文件
cp config.py.example config.py
```

2. 编辑config.py，设置以下参数：
   - `FMS_BASE_URL`: FMS API基础URL
   - `CHATBOT_CLIENT_ID`: API客户端ID
   - `CHATBOT_CLIENT_SECRET`: API客户端密钥
   - `IAM_URL`: IAM服务URL

## 使用方法

### 直接启动服务器

```bash
# 使用默认配置启动（SSE传输协议）
python mcp_server/fms_mcp_server.py

# 指定传输协议
python mcp_server/fms_mcp_server.py --transport stdio
python mcp_server/fms_mcp_server.py --transport sse

# 指定主机和端口（仅SSE模式）
python mcp_server/fms_mcp_server.py --host 0.0.0.0 --port 8000
```

### 使用MCP CLI工具

```bash
# 开发模式（带交互式检查器）
mcp dev mcp_server/fms_mcp_server.py

# 安装到Claude Desktop
mcp install mcp_server/fms_mcp_server.py --name "FMS工具"

# 使用环境变量
mcp install mcp_server/fms_mcp_server.py -v FMS_BASE_URL=https://api.example.com/fms
```

### 在Docker中运行

```bash
# 构建镜像
docker build -t fms-mcp-server .

# 运行容器
docker run -p 8000:8000 -e FMS_BASE_URL=https://api.example.com/fms fms-mcp-server
```

## 开发说明

### 项目结构

```
fms_mcp_server/
├── mcp_server/              # MCP服务器代码
│   └── fms_mcp_server.py    # 主服务器代码
├── prompts/                 # 提示词模板
│   └── fms_prompt.py        # FMS工具提示词
├── tools/                   # 工具实现
│   ├── __init__.py
│   ├── base.py              # 基础工具类
│   ├── fms.py               # FMS工具实现
│   └── fms_api_finder.py    # FMS API查找工具
├── config.py                # 配置文件
├── requirements.txt         # 项目依赖
└── README.md                # 项目说明
```

### 添加新工具

1. 在`tools/`目录下创建新的工具实现
2. 在`mcp_server/fms_mcp_server.py`中添加工具注册
3. 添加相应的提示词和资源

## 故障排除

1. 如果启动时报错"Failed to import FMS tools"，检查：
   - `tools/`目录是否存在
   - `tools/__init__.py`是否正确导入FMSTools

2. 如果API调用失败，检查：
   - 配置文件中的认证信息是否正确
   - 网络连接是否正常
   - API路径是否正确

## 许可证

MIT 