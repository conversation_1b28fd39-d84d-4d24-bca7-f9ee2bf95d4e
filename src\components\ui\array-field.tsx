'use client';

import * as React from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { DatePicker } from '@/components/ui/date-picker';
import { DateTimePicker } from '@/components/ui/datetime-picker';
import { TimePicker } from '@/components/ui/time-picker';
import { CustomSelect } from '@/components/ui/custom-select';
import { UserSelector } from '@/components/ui/wms/user-selector';
import { MultipleUserSelector } from '@/components/ui/wms/multiple-user-selector';
import { CustomerSelector } from '@/components/ui/wms/customer-selector';
import { TitleSelector } from '@/components/ui/wms/title-selector';
import { GeneralProjectSelector } from '@/components/ui/wms/general-project-selector';
import { JobCodeSelector } from '@/components/ui/wms/jobcode-selector';
import { ItemMasterSelector } from '@/components/ui/wms/itemmaster-selector';
import { ItemMasterUomSelector } from '@/components/ui/wms/itemmaster-uom-selector';
import { TemplateFieldType } from '@/tools/types';
import { Trash2, PlusCircle } from 'lucide-react';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { cn } from '@/lib/utils';

interface TemplateField {
  name: string;
  label: string;
  type: string;
  required?: boolean;
  description?: string;
  options?: { label: string; value: string }[];
  apiUrl?: string;
  apiPath?: string;
  apiHeaders?: Record<string, string>;
  dependsOn?: string; // Field name that this field depends on
  defaultValue?: any;
  arrayItemFields?: TemplateField[];
}

interface ArrayFieldProps {
  field: TemplateField;
  value: any[];
  onChange: (value: any[]) => void;
  className?: string;
  parentFormValues?: Record<string, any>; // 添加父表单值
}

// 解析依赖值的辅助函数
const resolveDependencyValue = (
  field: TemplateField,
  itemValue: any,
  parentFormValues: Record<string, any>
): string | undefined => {
  if (!field.dependsOn) return undefined;

  console.log('resolveDependencyValue called with:', {
    dependsOn: field.dependsOn,
    itemValue,
    parentFormValues
  });

  // 检查依赖字段名称是否包含点号，表示可能是父表单中的嵌套字段
  if (field.dependsOn.includes('.')) {
    // 例如 "taskCmd.customerId" 这样的路径
    const parts = field.dependsOn.split('.');
    let value: any = parentFormValues;

    // 遍历路径获取值
    for (const part of parts) {
      console.log(`Accessing part "${part}" of path "${field.dependsOn}", current value:`, value);
      if (!value || typeof value !== 'object') {
        console.log(`Cannot access "${part}" in "${field.dependsOn}", value is not an object or is undefined`);

        // 特殊处理：如果是 "taskCmd.customerId"，尝试直接从父表单中获取 "customerId"
        if (field.dependsOn === "taskCmd.customerId" && parentFormValues.customerId) {
          console.log(`Special case: Found "customerId" directly in parent form:`, parentFormValues.customerId);
          return parentFormValues.customerId;
        }

        return undefined;
      }
      value = value[part];
    }

    console.log(`Resolved nested path "${field.dependsOn}" to value:`, value);
    return value as string;
  }

  // 首先检查当前项中是否有依赖值
  if (itemValue && itemValue[field.dependsOn] !== undefined) {
    console.log(`Found dependency value in item: ${itemValue[field.dependsOn]}`);
    return itemValue[field.dependsOn];
  }

  // 然后检查父表单中是否有依赖值
  if (parentFormValues && parentFormValues[field.dependsOn] !== undefined) {
    console.log(`Found dependency value in parent form: ${parentFormValues[field.dependsOn]}`);
    return parentFormValues[field.dependsOn];
  }

  // 特殊处理：如果依赖字段是 "taskCmd.customerId"，尝试查找任何可能的客户ID字段
  if (field.dependsOn === "taskCmd.customerId" || field.dependsOn === "customerId") {
    // 尝试在父表单中查找任何可能的客户ID字段
    const possibleCustomerIdFields = ["customerId", "customer_id", "customerID", "customer", "taskCmd.customerId"];

    for (const fieldName of possibleCustomerIdFields) {
      if (parentFormValues[fieldName] !== undefined) {
        console.log(`Found alternative customer ID field "${fieldName}" with value:`, parentFormValues[fieldName]);
        return parentFormValues[fieldName];
      }
    }

    // 尝试在请求参数中查找客户ID
    if (parentFormValues.currentPage !== undefined && parentFormValues.pageSize !== undefined) {
      // 这可能是一个API请求参数对象
      console.log("Found API request parameters, checking for customerId");
      if (parentFormValues.customerId) {
        console.log("Found customerId in API parameters:", parentFormValues.customerId);
        return parentFormValues.customerId;
      }
    }
  }

  console.log(`Could not resolve dependency value for "${field.dependsOn}"`);
  return undefined;
};

export function ArrayField({ field, value = [], onChange, className, parentFormValues = {} }: ArrayFieldProps) {
  // Ensure value is always an array
  const arrayValue = Array.isArray(value) ? value : [];

  // 存储上一次的父表单值，用于检测变化
  const prevParentFormValues = React.useRef(parentFormValues);

  // 监听父表单值的变化，重置依赖字段
  React.useEffect(() => {
    // 检查父表单中的值是否发生变化
    const prev = prevParentFormValues.current;

    // 如果数组为空，不需要检查
    if (arrayValue.length === 0) {
      prevParentFormValues.current = parentFormValues;
      return;
    }

    // 检查是否有需要重置的字段
    let needsUpdate = false;
    const updatedArrayValue = [...arrayValue];

    // 遍历所有数组项
    updatedArrayValue.forEach((item, itemIndex) => {
      // 如果有数组项字段，检查它们的依赖关系
      if (field.arrayItemFields) {
        field.arrayItemFields.forEach(itemField => {
          if (itemField.dependsOn) {
            // 检查依赖是否指向父表单中的字段
            const isParentDependency = !item[itemField.dependsOn] &&
              (itemField.dependsOn.includes('.') || parentFormValues[itemField.dependsOn] !== undefined);

            if (isParentDependency) {
              // 获取当前和之前的依赖值
              const currentValue = resolveDependencyValue(itemField, item, parentFormValues);
              const previousValue = resolveDependencyValue(itemField, item, prev);

              // 如果依赖值发生变化，重置该字段
              if (currentValue !== previousValue) {
                updatedArrayValue[itemIndex] = {
                  ...updatedArrayValue[itemIndex],
                  [itemField.name]: '' // 重置字段值
                };
                needsUpdate = true;
              }
            }
          }
        });
      }
    });

    // 如果有字段需要更新，调用 onChange
    if (needsUpdate) {
      onChange(updatedArrayValue);
    }

    // 更新上一次的父表单值
    prevParentFormValues.current = parentFormValues;
  }, [parentFormValues, field.arrayItemFields, arrayValue, onChange]);

  // Add a new item to the array
  const handleAddItem = () => {
    const newItem: Record<string, any> = {};

    // Initialize with default values
    if (field.arrayItemFields) {
      field.arrayItemFields.forEach((itemField) => {
        if (itemField.defaultValue !== undefined) {
          // 处理日期和日期时间类型的默认值，将字符串转换为 Date 对象
          if (itemField.type === TemplateFieldType.DATE || itemField.type === TemplateFieldType.DATETIME || itemField.type === TemplateFieldType.TIME) {
            try {
              // 检查是否为字符串类型的日期
              if (typeof itemField.defaultValue === 'string') {
                newItem[itemField.name] = new Date(itemField.defaultValue);
                console.log(`Converted array item ${itemField.type} string value to Date:`,
                  itemField.defaultValue, newItem[itemField.name]);
              } else {
                // 如果已经是 Date 对象则直接使用
                newItem[itemField.name] = itemField.defaultValue;
              }
            } catch (e) {
              console.error(`Error converting array item ${itemField.type} default value:`, e);
              newItem[itemField.name] = undefined;
            }
          } else {
            // 其他类型直接赋值
            newItem[itemField.name] = itemField.defaultValue;
          }
        } else {
          switch (itemField.type) {
            case TemplateFieldType.CHECKBOX:
              newItem[itemField.name] = false;
              break;
            case TemplateFieldType.DATE:
            case TemplateFieldType.DATETIME:
            case TemplateFieldType.TIME:
              newItem[itemField.name] = undefined;
              break;
            case TemplateFieldType.MULTIPLE_USER_SELECTOR:
              newItem[itemField.name] = [];
              break;
            default:
              newItem[itemField.name] = '';
          }
        }

        // 如果字段有依赖关系，并且依赖指向父表单中的字段，预先设置依赖关系
        if (itemField.dependsOn && !itemField.dependsOn.includes('.') &&
            parentFormValues[itemField.dependsOn] !== undefined) {
          // 将父表单中的依赖值复制到新数组项中
          newItem[itemField.dependsOn] = parentFormValues[itemField.dependsOn];
        }
      });
    }

    onChange([...arrayValue, newItem]);
  };

  // Remove an item from the array
  const handleRemoveItem = (index: number) => {
    const newValue = [...arrayValue];
    newValue.splice(index, 1);
    onChange(newValue);
  };

  // Update a field in an array item
  const handleItemFieldChange = (index: number, fieldName: string, fieldValue: any, itemData?: any) => {
    const newValue = [...arrayValue];
    if (!newValue[index]) {
      newValue[index] = {};
    }

    // 更新当前字段的值
    newValue[index][fieldName] = fieldValue;

    // 如果提供了项目数据，保存到数组项的元数据中
    if (itemData) {
      // 确保有一个元数据对象
      if (!newValue[index].__metadata) {
        newValue[index].__metadata = {};
      }

      // 保存标签映射
      newValue[index].__metadata[fieldName] = {
        value: fieldValue,
        label: itemData.name || itemData.label || String(fieldValue),
        data: itemData
      };

      console.log(`Saved label mapping for ${fieldName} in array item ${index}:`,
        itemData.name || itemData.label || String(fieldValue));
    }

    // 检查是否有依赖于此字段的其他字段，如果有，则重置它们的值
    if (field.arrayItemFields) {
      field.arrayItemFields.forEach(itemField => {
        if (itemField.dependsOn === fieldName) {
          // 重置依赖字段的值
          newValue[index][itemField.name] = '';
          // 同时清除依赖字段的元数据
          if (newValue[index].__metadata && newValue[index].__metadata[itemField.name]) {
            delete newValue[index].__metadata[itemField.name];
          }
          console.log(`Reset dependent field ${itemField.name} in array item ${index} because ${fieldName} changed`);
        }
      });
    }

    onChange(newValue);
  };

  // Render a field based on its type
  const renderField = (itemField: TemplateField, itemIndex: number, itemValue: any) => {
    const fieldValue = itemValue[itemField.name];

    switch (itemField.type) {
      case TemplateFieldType.TEXT:
        return (
          <Input
            value={fieldValue || ''}
            onChange={(e) => handleItemFieldChange(itemIndex, itemField.name, e.target.value)}
            className="bg-item-bg-card border-item-gray-700 text-white focus:ring-1 focus:ring-item-purple focus:border-item-purple transition-all duration-200"
            required={itemField.required}
          />
        );
      case TemplateFieldType.TEXTAREA:
        return (
          <textarea
            value={fieldValue || ''}
            onChange={(e) => handleItemFieldChange(itemIndex, itemField.name, e.target.value)}
            className="w-full bg-item-bg-card border border-item-gray-700 rounded-md p-2 text-white focus:ring-1 focus:ring-item-purple focus:border-item-purple transition"
            rows={2}
            required={itemField.required}
          />
        );
      case TemplateFieldType.NUMBER:
        return (
          <Input
            type="number"
            value={fieldValue || ''}
            onChange={(e) => handleItemFieldChange(itemIndex, itemField.name, e.target.value)}
            className="bg-item-bg-card border-item-gray-700 text-white focus:ring-1 focus:ring-item-purple focus:border-item-purple transition-all duration-200"
            required={itemField.required}
          />
        );
      case TemplateFieldType.SELECT:
        return (
          <CustomSelect
            value={fieldValue || ''}
            onChange={(value, optionData) => handleItemFieldChange(itemIndex, itemField.name, value, optionData)}
            options={itemField.options || []}
            placeholder={`Select ${itemField.label}`}
            className="bg-gray-800 border-gray-700"
          />
        );
      case TemplateFieldType.USER_SELECTOR:
        return (
          <UserSelector
            value={fieldValue || ''}
            onChange={(value, userData) => handleItemFieldChange(itemIndex, itemField.name, value, userData)}
            placeholder={`Select ${itemField.label}`}
            apiHeaders={itemField.apiHeaders}
            required={itemField.required}
          />
        );
      case TemplateFieldType.MULTIPLE_USER_SELECTOR:
        return (
          <MultipleUserSelector
            value={fieldValue || []}
            onChange={(value, userData) => handleItemFieldChange(itemIndex, itemField.name, value, userData)}
            placeholder={`Select ${itemField.label}`}
            apiHeaders={itemField.apiHeaders}
            required={itemField.required}
            defaultValue={itemField.defaultValue}
          />
        );
      case TemplateFieldType.CUSTOMER_SELECTOR:
        return (
          <CustomerSelector
            value={fieldValue || ''}
            onChange={(value, customerData) => handleItemFieldChange(itemIndex, itemField.name, value, customerData)}
            placeholder={`Select ${itemField.label}`}
            apiHeaders={itemField.apiHeaders}
            required={itemField.required}
          />
        );
      case TemplateFieldType.TITLE_SELECTOR:
        return (
          <TitleSelector
            value={fieldValue || ''}
            onChange={(value, titleData) => handleItemFieldChange(itemIndex, itemField.name, value, titleData)}
            placeholder={`Select ${itemField.label}`}
            apiHeaders={itemField.apiHeaders}
            required={itemField.required}
          />
        );
      case TemplateFieldType.GENERAL_PROJECT_SELECTOR:
        return (
          <GeneralProjectSelector
            value={fieldValue || ''}
            onChange={(value, projectData) => handleItemFieldChange(itemIndex, itemField.name, value, projectData)}
            placeholder={`Select ${itemField.label}`}
            apiHeaders={itemField.apiHeaders}
            required={itemField.required}
          />
        );
      case TemplateFieldType.JOBCODE_SELECTOR:
        return (
          <JobCodeSelector
            value={fieldValue || ''}
            onChange={(value, jobcodeData) => handleItemFieldChange(itemIndex, itemField.name, value, jobcodeData)}
            placeholder={`Select ${itemField.label}`}
            customerId={(() => {
              const resolvedValue = resolveDependencyValue(itemField, itemValue, parentFormValues);
              console.log(`Resolving customerId for jobCode in item ${itemIndex}:`, {
                dependsOn: itemField.dependsOn,
                resolvedValue,
                parentFormValues
              });
              return resolvedValue;
            })()}
            apiHeaders={itemField.apiHeaders}
            required={itemField.required}
          />
        );
      case TemplateFieldType.ITEMMASTER_SELECTOR:
        return (
          <ItemMasterSelector
            value={fieldValue || ''}
            onChange={(value, itemData) => handleItemFieldChange(itemIndex, itemField.name, value, itemData)}
            placeholder={`Select ${itemField.label}`}
            customerId={(() => {
              const resolvedValue = resolveDependencyValue(itemField, itemValue, parentFormValues);
              console.log(`Resolving customerId for itemMaster in item ${itemIndex}:`, {
                dependsOn: itemField.dependsOn,
                resolvedValue,
                parentFormValues
              });
              return resolvedValue;
            })()}
            apiHeaders={itemField.apiHeaders}
            required={itemField.required}
          />
        );
      case TemplateFieldType.ITEMMASTER_UOM_SELECTOR:
        return (
          <ItemMasterUomSelector
            value={fieldValue || ''}
            onChange={(value, uomData) => handleItemFieldChange(itemIndex, itemField.name, value, uomData)}
            placeholder={`Select ${itemField.label}`}
            itemId={(() => {
              const resolvedValue = resolveDependencyValue(itemField, itemValue, parentFormValues);
              console.log(`Resolving itemId for itemMasterUom in item ${itemIndex}:`, {
                dependsOn: itemField.dependsOn,
                resolvedValue,
                parentFormValues
              });
              return resolvedValue;
            })()}
            apiHeaders={itemField.apiHeaders}
            required={itemField.required}
          />
        );
      case TemplateFieldType.DATE:
        return (
          <DatePicker
            date={fieldValue}
            setDate={(date) => handleItemFieldChange(itemIndex, itemField.name, date)}
            placeholder={`Select ${itemField.label}`}
          />
        );
      case TemplateFieldType.DATETIME:
        return (
          <DateTimePicker
            date={fieldValue}
            setDate={(date) => handleItemFieldChange(itemIndex, itemField.name, date)}
            placeholder={`Select ${itemField.label}`}
          />
        );
      case TemplateFieldType.TIME:
        return (
          <TimePicker
            time={fieldValue}
            setTime={(time) => handleItemFieldChange(itemIndex, itemField.name, time)}
            placeholder={`Select ${itemField.label}`}
          />
        );
      case TemplateFieldType.SWITCH:
        return (
          <div className="flex items-center justify-between space-x-2">
            <div className="space-y-0.5">
              <Label
                htmlFor={`${itemIndex}-${itemField.name}`}
                className="text-xs font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 text-item-gray-300"
              >
                {itemField.label}
                {itemField.required && <span className="text-item-orange ml-1">*</span>}
              </Label>
              {itemField.description && (
                <p className="text-item-gray-400 text-xs">{itemField.description}</p>
              )}
            </div>
            <Switch
              id={`${itemIndex}-${itemField.name}`}
              checked={!!fieldValue}
              onCheckedChange={(checked) => handleItemFieldChange(itemIndex, itemField.name, checked === true)}
            />
          </div>
        );
      default:
        return (
          <Input
            value={fieldValue || ''}
            onChange={(e) => handleItemFieldChange(itemIndex, itemField.name, e.target.value)}
            className="bg-item-bg-card border-item-gray-700 text-white focus:ring-1 focus:ring-item-purple focus:border-item-purple transition-all duration-200"
          />
        );
    }
  };

  return (
    <div className={cn("bg-item-bg-card/50 p-3 rounded-lg border border-item-gray-700/60", className)}>
      {/* Array items */}
      {arrayValue.length > 0 && arrayValue.map((item, index) => (
        <div key={index} className="p-4 border border-item-gray-700 rounded-lg bg-item-bg-card/50 space-y-4">
          <div className="flex justify-between items-center mb-2">
            <h4 className="text-item-purple text-sm">{field.label} #{index + 1}</h4>
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={() => handleRemoveItem(index)}
              className="bg-item-bg-hover text-white hover:bg-item-gray-700 border-none rounded px-2 py-1 text-xs transition-all duration-200"
            >
              <Trash2 className="h-3 w-3 mr-1" />
              Remove
            </Button>
          </div>

          {/* Render fields for this array item */}
          {field.arrayItemFields?.map((itemField) => (
            <div key={itemField.name} className="space-y-1">
              <label className="text-item-gray-300 text-xs block">
                {itemField.label}
                {itemField.required && <span className="text-item-orange ml-1">*</span>}
              </label>

              {renderField(itemField, index, item)}

              {itemField.description && (
                <p className="text-item-gray-400 text-xs">{itemField.description}</p>
              )}
            </div>
          ))}
        </div>
      ))}

      {/* Description text - moved above the button for better UX */}
      {field.description && (
        <p className="text-item-gray-400 text-xs mb-2">{field.description}</p>
      )}

      {/* Add button - simplified to match screenshot */}
      <div className="flex justify-start mt-2">
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={handleAddItem}
          className="bg-item-purple hover:bg-item-purple/80 text-white border-none rounded-md py-2 px-4 flex items-center text-sm max-w-full transition-all duration-200 shadow-lg hover:shadow-xl"
          title={`Add ${field.label}`}
        >
          <PlusCircle className="mr-2 h-4 w-4 flex-shrink-0" />
          <span className="truncate min-w-0">{`Add ${field.label}`}</span>
        </Button>
      </div>
    </div>
  );
}
