'use client';

import * as React from 'react';
import { Check, ChevronsUpDown } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';

// US States data - matching address-input.tsx format
const US_STATES = [
  { label: 'Alabama', value: 'AL' },
  { label: 'Alaska', value: 'AK' },
  { label: 'Arizona', value: 'AZ' },
  { label: 'Arkansas', value: 'AR' },
  { label: 'California', value: 'CA' },
  { label: 'Colorado', value: 'CO' },
  { label: 'Connecticut', value: 'CT' },
  { label: 'Delaware', value: 'DE' },
  { label: 'Florida', value: 'FL' },
  { label: 'Georgia', value: 'GA' },
  { label: 'Hawaii', value: 'HI' },
  { label: 'Idaho', value: 'ID' },
  { label: 'Illinois', value: 'IL' },
  { label: 'Indiana', value: 'IN' },
  { label: 'Iowa', value: 'IA' },
  { label: 'Kansas', value: 'KS' },
  { label: 'Kentucky', value: 'KY' },
  { label: 'Louisiana', value: 'LA' },
  { label: 'Maine', value: 'ME' },
  { label: 'Maryland', value: 'MD' },
  { label: 'Massachusetts', value: 'MA' },
  { label: 'Michigan', value: 'MI' },
  { label: 'Minnesota', value: 'MN' },
  { label: 'Mississippi', value: 'MS' },
  { label: 'Missouri', value: 'MO' },
  { label: 'Montana', value: 'MT' },
  { label: 'Nebraska', value: 'NE' },
  { label: 'Nevada', value: 'NV' },
  { label: 'New Hampshire', value: 'NH' },
  { label: 'New Jersey', value: 'NJ' },
  { label: 'New Mexico', value: 'NM' },
  { label: 'New York', value: 'NY' },
  { label: 'North Carolina', value: 'NC' },
  { label: 'North Dakota', value: 'ND' },
  { label: 'Ohio', value: 'OH' },
  { label: 'Oklahoma', value: 'OK' },
  { label: 'Oregon', value: 'OR' },
  { label: 'Pennsylvania', value: 'PA' },
  { label: 'Rhode Island', value: 'RI' },
  { label: 'South Carolina', value: 'SC' },
  { label: 'South Dakota', value: 'SD' },
  { label: 'Tennessee', value: 'TN' },
  { label: 'Texas', value: 'TX' },
  { label: 'Utah', value: 'UT' },
  { label: 'Vermont', value: 'VT' },
  { label: 'Virginia', value: 'VA' },
  { label: 'Washington', value: 'WA' },
  { label: 'West Virginia', value: 'WV' },
  { label: 'Wisconsin', value: 'WI' },
  { label: 'Wyoming', value: 'WY' },
  { label: 'District of Columbia', value: 'DC' }
];

interface StateData {
  code: string;
  name: string;
}

interface StateSelectorProps {
  value?: string;
  onChange?: (value: string, stateData?: StateData) => void;
  placeholder?: string;
  required?: boolean;
  defaultValue?: string;
  className?: string;
}

export function StateSelector({
  value = '',
  onChange,
  placeholder = 'Select state',
  required = false,
  defaultValue,
  className,
}: StateSelectorProps) {
  const [open, setOpen] = React.useState(false);
  const [searchTerm, setSearchTerm] = React.useState('');
  const hasSetDefault = React.useRef(false);

  // Set default value on mount
  React.useEffect(() => {
    if (defaultValue && !value && onChange && !hasSetDefault.current) {
      const defaultState = US_STATES.find(
        state => state.value === defaultValue || state.label === defaultValue
      );
      if (defaultState) {
        const stateData = { code: defaultState.value, name: defaultState.label };
        onChange(defaultState.value, stateData);
        hasSetDefault.current = true;
      }
    }
  }, [defaultValue, value, onChange]);

  // Find selected state
  const selectedState = US_STATES.find(state => state.value === value);
  
  // Filter states based on search term
  const filteredStates = US_STATES.filter(state =>
    state.label.toLowerCase().includes(searchTerm.toLowerCase()) ||
    state.value.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleSelect = (selectedValue: string) => {
    const selectedState = US_STATES.find(state => state.value === selectedValue);
    if (selectedState && onChange) {
      const stateData = { code: selectedState.value, name: selectedState.label };
      onChange(selectedValue, stateData);
    }
    setOpen(false);
    setSearchTerm('');
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn(
            "w-full justify-between bg-item-bg-card border-item-purple/30 text-white hover:bg-item-bg-hover hover:text-white transition-all duration-200",
            !selectedState && "text-item-gray-500",
            className
          )}
        >
          {selectedState ? selectedState.label : placeholder}
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-full p-0 bg-item-bg-card border-item-purple/30">
        <div className="p-2">
          <input
            type="text"
            placeholder="Search states..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full px-3 py-2 bg-item-bg-input text-white border border-item-gray-700 rounded text-sm focus:ring-1 focus:ring-item-purple focus:border-item-purple transition-all duration-200"
          />
        </div>
        <div className="max-h-64 overflow-auto">
          {filteredStates.length === 0 ? (
            <div className="text-item-gray-400 py-6 text-center text-sm">
              No state found.
            </div>
          ) : (
            filteredStates.map((state) => (
              <div
                key={state.value}
                onClick={() => handleSelect(state.value)}
                className="flex items-center justify-between px-3 py-2 text-white hover:bg-item-bg-hover cursor-pointer transition-all duration-200"
              >
                <div className="flex items-center">
                  <Check
                    className={cn(
                      "mr-2 h-4 w-4 text-item-purple",
                      selectedState?.value === state.value ? "opacity-100" : "opacity-0"
                    )}
                  />
                  <span>{state.label}</span>
                </div>
                <span className="text-xs text-item-gray-400">{state.value}</span>
              </div>
            ))
          )}
        </div>
      </PopoverContent>
    </Popover>
  );
} 