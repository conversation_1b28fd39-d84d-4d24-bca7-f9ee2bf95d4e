@tailwind base;
@tailwind components;
@tailwind utilities;

/* Embedded chat component styles - based on Chat.tsx */
:host {
  /* 深色主题变量 */
  --primary-color: #4f46e5; /* Indigo */
  --bg-color-dark: #111827; /* Dark background */
  --text-color-dark: #e2e8f0; /* Text color */
  --border-color-dark: #1e293b; /* Border color */
  --message-bg-user-dark: #334155; /* User message background - more subtle slate color */
  --message-bg-assistant-dark: #1e293b; /* Assistant message background */
  --header-bg-dark: #0f172a; /* Header background */
  --user-avatar-bg-dark: #334155; /* User avatar background - matching message */
  --ai-icon-bg-dark: #4b5563; /* More subtle gray for AI icon background */
  --ai-icon-border-dark: rgba(75, 85, 99, 0.4); /* Border color for AI icon */
  
  /* 亮色主题变量 */
  --bg-color-light: #f8fafc; /* Light background */
  --text-color-light: #334155; /* Text color */
  --border-color-light: #e2e8f0; /* Border color */
  --message-bg-user-light: #e2e8f0; /* User message background */
  --message-bg-assistant-light: #f1f5f9; /* Assistant message background */
  --header-bg-light: #f1f5f9; /* Header background */
  --user-avatar-bg-light: #94a3b8; /* User avatar background */
  --ai-icon-bg-light: #6366f1; /* AI icon background */
  --ai-icon-border-light: rgba(99, 102, 241, 0.4); /* Border color for AI icon */

  /* Item 设计系统日历变量 */
  --rdp-accent-color: #8B5CF6; /* item-purple */
  --rdp-accent-background-color: rgba(139, 92, 246, 0.2);
  --rdp-day-width: 40px;
  --rdp-day-height: 40px;
  --rdp-selected-border: 2px solid #F97316; /* item-orange */
  --rdp-today-color: #8B5CF6; /* item-purple */

  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  font-size: 14px;
  line-height: 1.5;
}

/* Main container - 深色主题默认样式 */
.embedded-chat {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  background-color: var(--bg-color-dark);
  color: var(--text-color-dark);
  border: 1px solid var(--border-color-dark);
  position: relative;
  resize: both;
  min-width: 300px;
  min-height: 400px;
  max-width: 100%;
  max-height: 100%;
  transition: box-shadow 0.3s ease, width 0.3s ease, height 0.3s ease, border-radius 0.3s ease;
}

/* 亮色主题容器样式 */
.embedded-chat.light {
  background-color: var(--bg-color-light);
  color: var(--text-color-light);
  border: 1px solid var(--border-color-light);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Dragging state - applied to the container */
.dragging {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.5) !important;
  opacity: 0.9 !important;
  cursor: move !important;
  z-index: 10000 !important;
}

/* Resize handle */
.embedded-chat::after {
  content: '';
  position: absolute;
  right: 0;
  bottom: 0;
  width: 15px;
  height: 15px;
  cursor: nwse-resize;
  background: linear-gradient(135deg, transparent 50%, rgba(255, 255, 255, 0.5) 50%);
  border-bottom-right-radius: 8px;
}

/* 亮色主题调整拉伸手柄 */
.embedded-chat.light::after {
  background: rgba(0, 0, 0, 0.1);
}

/* Minimized state */
.embedded-chat.minimized {
  width: 50px !important;
  height: 50px !important;
  border-radius: 50%;
  overflow: hidden;
  resize: none;
  min-width: 50px;
  min-height: 50px;
  background-color: #1e293b;
  cursor: default;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px solid rgba(30, 41, 59, 0.8);
}

/* 亮色主题最小化状态 */
.embedded-chat.light.minimized {
  background-color: #f1f5f9;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(226, 232, 240, 0.8);
}

/* Chat header */
.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: var(--header-bg-dark);
  color: white;
  font-weight: 600;
  border-bottom: 1px solid var(--border-color-dark);
}

/* 亮色主题头部 */
.embedded-chat.light .chat-header {
  background-color: var(--header-bg-light);
  color: var(--text-color-light);
  border-bottom: 1px solid var(--border-color-light);
}

/* Maximize button in minimized state */
.maximize-button {
  display: none; /* Hide the maximize button completely */
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: rgba(30, 41, 59, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  padding: 0;
  z-index: 20;
  transition: all 0.2s ease;
}

.maximize-button:hover {
  background-color: var(--primary-color);
  transform: scale(1.1);
}

/* Hide chat title when minimized */
.embedded-chat.minimized .chat-title {
  display: none;
}

.chat-title {
  font-weight: 600;
  display: flex;
  align-items: center;
}

/* Toggle button */
.toggle-button {
  background: none;
  border: none;
  cursor: pointer;
  color: white;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.2s;
}

/* 亮色主题切换按钮 */
.embedded-chat.light .toggle-button {
  color: var(--text-color-light);
}

/* Theme toggle button */
.theme-toggle-button {
  background: none;
  border: none;
  cursor: pointer;
  color: white;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.2s;
}

/* 亮色主题的主题切换按钮 */
.embedded-chat.light .theme-toggle-button {
  color: var(--text-color-light);
}

.theme-toggle-button:hover {
  transform: scale(1.1);
}

/* Minimized state: make toggle button larger and center it */
.embedded-chat.minimized .toggle-button {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: transparent;
  color: white;
}

.embedded-chat.minimized .toggle-button svg {
  width: 28px;
  height: 28px;
}

.toggle-button:hover {
  transform: scale(1.1);
}

.toggle-button svg {
  transition: transform 0.2s;
}

.toggle-button:hover svg {
  transform: scale(1.2);
}

/* Notification indicator for minimized state */
.notification-indicator {
  position: absolute;
  top: 6px;
  right: 6px;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #ef4444; /* Red notification dot */
  border: 1px solid #111827;
  animation: pulse 2s infinite;
  z-index: 10;
}

@keyframes pulse {
  0% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7);
  }

  70% {
    transform: scale(1);
    box-shadow: 0 0 0 6px rgba(239, 68, 68, 0);
  }

  100% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0);
  }
}

/* Hover effect for minimized state */
.embedded-chat.minimized:hover {
  transform: scale(1.05);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.3);
  border-color: #4f46e5; /* 使用变量中定义的主色 */
  border-width: 2px;
  cursor: default; /* Ensure cursor remains as default even on hover */
  background-color: #2c3a4e; /* 悬停时背景色略微变亮 */
}

/* 调整悬停时图标效果 */
.embedded-chat.minimized:hover::before {
  filter: drop-shadow(0 0 2px rgba(255, 255, 255, 0.2));
  transform: translate(-50%, -50%) scale(1.05);
}

/* Custom scrollbar styles - applied globally to shadow DOM elements */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.embedded-chat.dark ::-webkit-scrollbar,
.dark ::-webkit-scrollbar,
.chat-messages::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.embedded-chat.dark ::-webkit-scrollbar-track,
.dark ::-webkit-scrollbar-track,
.chat-messages::-webkit-scrollbar-track {
  background: #1e293b;
  border-radius: 4px;
}

.embedded-chat.dark ::-webkit-scrollbar-thumb,
.dark ::-webkit-scrollbar-thumb,
.chat-messages::-webkit-scrollbar-thumb {
  background: #334155;
  border-radius: 4px;
}

.embedded-chat.dark ::-webkit-scrollbar-thumb:hover,
.dark ::-webkit-scrollbar-thumb:hover,
.chat-messages::-webkit-scrollbar-thumb:hover {
  background: #475569;
}

.embedded-chat.light ::-webkit-scrollbar,
.light ::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.embedded-chat.light ::-webkit-scrollbar-track,
.light ::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

.embedded-chat.light ::-webkit-scrollbar-thumb,
.light ::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

.embedded-chat.light ::-webkit-scrollbar-thumb:hover,
.light ::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Chat messages area */
.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  background-color: var(--bg-color-dark);
  scrollbar-width: thin !important; /* Firefox */
  scrollbar-color: #334155 #1e293b !important; /* Firefox */

  /* Ensure Webkit scrollbar styles */
  &::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  &::-webkit-scrollbar-track {
    background: #1e293b;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: #334155;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: #475569;
  }
}

/* 亮色主题下的聊天信息区域 */
.embedded-chat.light .chat-messages {
  background-color: var(--bg-color-light);
}

/* Welcome message */
.welcome-message {
  text-align: center;
  padding: 12px;
  color: #94a3b8;
}

.welcome-title {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 12px;
  color: #e2e8f0;
}

.welcome-description {
  margin-bottom: 20px;
  color: #94a3b8;
}

.welcome-suggestions {
  margin-top: 16px;
}

.welcome-suggestions h3 {
  font-size: 16px;
  margin-bottom: 12px;
  color: #e2e8f0;
}

.welcome-suggestions ul {
  list-style-type: disc;
  padding-left: 20px;
  text-align: left;
}

.welcome-suggestions li {
  margin-bottom: 8px;
  color: #94a3b8;
}

/* 亮色主题下的欢迎标题 */
.embedded-chat.light .welcome-title {
  color: var(--text-color-light);
}

/* 亮色主题下的欢迎描述 */
.embedded-chat.light .welcome-description {
  color: #64748b;
}

/* 亮色主题下的欢迎建议标题 */
.embedded-chat.light .welcome-suggestions h3 {
  color: var(--text-color-light);
}

/* 亮色主题下的欢迎建议列表 */
.embedded-chat.light .welcome-suggestions li {
  color: #64748b;
}

/* Message row */
.message-row {
  display: flex;
  align-items: flex-start;
  gap: 8px; /* Space between avatar and message */
  width: 100%;
  margin-bottom: 12px; /* Space between messages */
  transition: opacity 0.2s ease; /* Smooth transition for hover effects */
  position: relative; /* For proper positioning of elements */
}

.message-row.justify-end {
  flex-direction: row-reverse;
  justify-content: flex-end; /* Align user messages to the right */
}

/* Subtle hover effect */
.message-row:hover {
  opacity: 1;
}

/* Avatar styles */
.user-avatar, .assistant-avatar {
  flex-shrink: 0;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 4px; /* Align with first line of text */
}

.user-avatar {
  background-color: var(--user-avatar-bg-dark);
  color: white;
}

/* 亮色主题用户头像 */
.embedded-chat.light .user-avatar {
  background-color: var(--user-avatar-bg-light);
  color: white;
}

.assistant-avatar {
  background-color: var(--ai-icon-bg-dark);
  color: white;
  border: 1px solid var(--ai-icon-border-dark);
}

/* 亮色主题助手头像 */
.embedded-chat.light .assistant-avatar {
  background-color: var(--ai-icon-bg-light);
  border: 1px solid var(--ai-icon-border-light);
}

/* Message styles */
.message {
  max-width: 80%;
  padding: 8px 12px;
  border-radius: 10px; /* Slightly less rounded */
  word-break: break-word;
  line-height: 1.5;
  transition: all 0.2s ease; /* Smooth transition for hover effects */
}

.user-message {
  background-color: var(--message-bg-user-dark);
  color: #e2e8f0; /* Slightly softer than pure white */
  border: 1px solid rgba(71, 85, 105, 0.4); /* Subtle border */
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1); /* Soft shadow for depth */
  margin-left: auto; /* Push user messages to the right */
}

/* 亮色主题用户消息 */
.embedded-chat.light .user-message {
  background-color: var(--message-bg-user-light);
  color: var(--text-color-light);
  border: 1px solid rgba(148, 163, 184, 0.4);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.assistant-message {
  background-color: var(--message-bg-assistant-dark);
  color: var(--text-color-dark);
  border: 1px solid rgba(30, 41, 59, 0.5);
  margin-right: auto; /* Push assistant messages to the left */
}

/* 亮色主题助手消息 */
.embedded-chat.light .assistant-message {
  background-color: var(--message-bg-assistant-light);
  color: var(--text-color-light);
  border: 1px solid rgba(226, 232, 240, 0.5);
}

/* Message content */
.message-content {
  white-space: normal;
  font-size: 0.95rem; /* Slightly smaller font */
  line-height: 1.5;
}

/* Keep pre-wrap only in code blocks */
.message-content pre,
.message-content code {
  white-space: pre-wrap;
}

/* Add subtle hover effect to messages */
.message:hover {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
}

/* List styles in message content */
.message-content ul {
  list-style-type: disc;
  padding-left: 1.5rem;
  margin: 0.5rem 0;
}

.message-content ol {
  list-style-type: decimal;
  padding-left: 1.5rem;
  margin: 0.5rem 0;
}

.message-content li {
  margin-bottom: 0.25rem;
}

/* Tool call styles */
.tool-call-indicator {
  background-color: rgba(30, 41, 59, 0.5);
  padding: 0.5rem;
  border-radius: 0.25rem;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
  color: #94a3b8;
}

/* Tool card styles */
.prose {
  color: #e2e8f0;
  font-size: 0.875rem;
  line-height: 1.5;
}

.prose pre {
  background-color: rgba(15, 23, 42, 0.5);
  border-radius: 0.375rem;
  padding: 0.75rem;
  overflow-x: auto;
  margin: 0.5rem 0;
  font-family: 'Menlo', 'Monaco', 'Courier New', monospace;
  font-size: 0.8125rem;
  color: #e2e8f0;
}

.prose code {
  font-family: 'Menlo', 'Monaco', 'Courier New', monospace;
  background-color: rgba(15, 23, 42, 0.5);
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-size: 0.9em;
}

.prose a {
  color: #38bdf8;
  text-decoration: none;
}

.prose a:hover {
  text-decoration: underline;
  color: #7dd3fc;
}

.prose p {
  margin-bottom: 0.75rem;
}

.prose h1, .prose h2, .prose h3, .prose h4 {
  margin-top: 1.5rem;
  margin-bottom: 0.75rem;
  font-weight: 600;
  color: #f8fafc;
}

.prose h1 {
  font-size: 1.5rem;
}

.prose h2 {
  font-size: 1.25rem;
}

.prose h3 {
  font-size: 1.125rem;
}

.prose h4 {
  font-size: 1rem;
}

/* Tool card styles */
.bg-slate-800\/30 {
  background-color: rgba(30, 41, 59, 0.3);
}

.border-slate-600\/30 {
  border-color: rgba(71, 85, 105, 0.3);
}

.border-slate-700\/30 {
  border-color: rgba(51, 65, 85, 0.3);
}

.border-l-4 {
  border-left-width: 4px;
}

.rounded-lg {
  border-radius: 0.5rem;
}

.shadow-md {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.text-readable {
  color: #e2e8f0;
}

.text-gray-300 {
  color: #cbd5e1;
}

.text-gray-400 {
  color: #94a3b8;
}

.text-gray-500 {
  color: #64748b;
}

.text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}

.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.font-normal {
  font-weight: 400;
}

.font-medium {
  font-weight: 500;
}

.font-semibold {
  font-weight: 600;
}

.tracking-wide {
  letter-spacing: 0.025em;
}

.leading-relaxed {
  line-height: 1.625;
}

.bg-gray-800\/60 {
  background-color: rgba(31, 41, 55, 0.6);
}

.bg-gray-700\/40 {
  background-color: rgba(55, 65, 81, 0.4);
}

.bg-gray-700\/20 {
  background-color: rgba(55, 65, 81, 0.2);
}

.bg-gray-800\/30 {
  background-color: rgba(31, 41, 55, 0.3);
}

.bg-red-900\/20 {
  background-color: rgba(127, 29, 29, 0.2);
}

.border-gray-700\/30 {
  border-color: rgba(55, 65, 81, 0.3);
}

.border-red-800\/20 {
  border-color: rgba(153, 27, 27, 0.2);
}

.overflow-x-auto {
  overflow-x: auto;
}

.overflow-auto {
  overflow: auto;
}

.max-h-40 {
  max-height: 10rem;
}

.max-h-60 {
  max-height: 15rem;
}

.whitespace-pre-wrap {
  white-space: pre-wrap;
}

.break-words {
  overflow-wrap: break-word;
}

.font-mono {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
}

.space-y-2 > * + * {
  margin-top: 0.5rem;
}

.grid {
  display: grid;
}

.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.gap-1 {
  gap: 0.25rem;
}

.gap-y-2 {
  row-gap: 0.5rem;
}

.pl-2 {
  padding-left: 0.5rem;
}

.text-red-400 {
  color: #f87171;
}

.text-cyan-300 {
  color: #67e8f9;
}

.text-cyan-400 {
  color: #22d3ee;
}

.bg-gradient-to-br {
  /* Gradient replaced with solid color */
  background-color: rgba(30, 58, 138, 0.4);
}

.border-cyan-800\/50 {
  border-color: rgba(21, 94, 117, 0.5);
}

.text-cyan-100 {
  color: #cffafe;
}

.text-cyan-200 {
  color: #a5f3fc;
}

.text-cyan-300 {
  color: #67e8f9;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: .5;
  }
}

.bg-cyan-800\/50 {
  background-color: rgba(21, 94, 117, 0.5);
}

.bg-cyan-800\/30 {
  background-color: rgba(21, 94, 117, 0.3);
}

.bg-blue-900\/30 {
  background-color: rgba(30, 58, 138, 0.3);
}

.bg-black\/50 {
  background-color: rgba(0, 0, 0, 0.5);
}

.bg-gray-950\/50 {
  background-color: rgba(3, 7, 18, 0.5);
}

.bg-red-950\/50 {
  background-color: rgba(69, 10, 10, 0.5);
}

.border-red-800\/30 {
  border-color: rgba(153, 27, 27, 0.3);
}

.border-red-700\/50 {
  border-color: rgba(185, 28, 28, 0.5);
}

.text-red-300 {
  color: #fca5a5;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.items-start {
  align-items: flex-start;
}

.justify-between {
  justify-content: space-between;
}

.flex-1 {
  flex: 1 1 0%;
}

.flex-shrink-0 {
  flex-shrink: 0;
}

.cursor-pointer {
  cursor: pointer;
}

.mb-1 {
  margin-bottom: 0.25rem;
}

.mb-2 {
  margin-bottom: 0.5rem;
}

.mb-3 {
  margin-bottom: 0.75rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mb-6 {
  margin-bottom: 1.5rem;
}

.mt-1 {
  margin-top: 0.25rem;
}

.mt-2 {
  margin-top: 0.5rem;
}

.mt-3 {
  margin-top: 0.75rem;
}

.mr-2 {
  margin-right: 0.5rem;
}

.ml-2 {
  margin-left: 0.5rem;
}

.ml-3 {
  margin-left: 0.75rem;
}

.p-1 {
  padding: 0.25rem;
}

.p-2 {
  padding: 0.5rem;
}

.p-3 {
  padding: 0.75rem;
}

.p-4 {
  padding: 1rem;
}

.px-1 {
  padding-left: 0.25rem;
  padding-right: 0.25rem;
}

.px-2 {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}

.px-3 {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}

.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}

.py-0\.5 {
  padding-top: 0.125rem;
  padding-bottom: 0.125rem;
}

.py-1 {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}

.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.py-3 {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}

.pl-1 {
  padding-left: 0.25rem;
}

.pl-3 {
  padding-left: 0.75rem;
}

.pl-4 {
  padding-left: 1rem;
}

.pr-3 {
  padding-right: 0.75rem;
}

.pr-4 {
  padding-right: 1rem;
}

.pt-2 {
  padding-top: 0.5rem;
}

.rounded {
  border-radius: 0.25rem;
}

.rounded-md {
  border-radius: 0.375rem;
}

.rounded-xl {
  border-radius: 0.75rem;
}

.rounded-full {
  border-radius: 9999px;
}

.border {
  border-width: 1px;
}

.border-t {
  border-top-width: 1px;
}

.border-r {
  border-right-width: 1px;
}

.border-b {
  border-bottom-width: 1px;
}

.border-gray-700 {
  border-color: rgb(55, 65, 81);
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}

.w-3 {
  width: 0.75rem;
}

.w-3\.5 {
  width: 0.875rem;
}

.w-4 {
  width: 1rem;
}

.w-5 {
  width: 1.25rem;
}

.w-8 {
  width: 2rem;
}

.w-10 {
  width: 2.5rem;
}

.h-3 {
  height: 0.75rem;
}

.h-3\.5 {
  height: 0.875rem;
}

.h-4 {
  height: 1rem;
}

.h-5 {
  height: 1.25rem;
}

.h-8 {
  height: 2rem;
}

.h-10 {
  height: 2.5rem;
}

.shadow-lg {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.hover\:text-white:hover {
  color: #ffffff;
}

.hover\:text-gray-300:hover {
  color: #cbd5e1;
}

.hover\:text-red-300:hover {
  color: #fca5a5;
}

.hover\:underline:hover {
  text-decoration: underline;
}

.hover\:text-cyan-300:hover {
  color: #67e8f9;
}

.hover\:bg-blue-600:hover {
  background-color: rgb(37, 99, 235);
}

.transition-colors {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

/* User avatar */
.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: var(--user-avatar-bg-dark);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #e2e8f0; /* Matching text color */
  font-weight: bold;
  font-size: 12px;
  flex-shrink: 0;
  border: 1px solid rgba(71, 85, 105, 0.3); /* Subtle border */
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05); /* Very soft shadow */
}

/* 亮色主题用户头像 */
.embedded-chat.light .user-avatar {
  background-color: var(--user-avatar-bg-light);
  color: white;
}

/* Assistant avatar */
.assistant-avatar {
  background-color: var(--ai-icon-bg-dark);
  color: white;
  border: 1px solid var(--ai-icon-border-dark);
}

/* 亮色主题助手头像 */
.embedded-chat.light .assistant-avatar {
  background-color: var(--ai-icon-bg-light);
  border: 1px solid var(--ai-icon-border-light);
}

/* Avatar icons styling */
.user-avatar svg, .assistant-avatar svg {
  stroke: currentColor;
  stroke-opacity: 0.85; /* Slightly more transparent */
  stroke-width: 1.5px;
  color: #e2e8f0; /* Light color for better contrast */
  width: 16px;
  height: 16px;
  transition: all 0.2s ease; /* Smooth transition */
}

/* Subtle hover effect for icons */
.assistant-avatar:hover svg {
  stroke-opacity: 1;
  transform: scale(1.05); /* Slightly larger on hover */
}

/* Override all SVG stroke attributes in the embedded chat */
.embedded-chat svg[stroke="white"] {
  stroke: currentColor !important;
  stroke-width: 1.5px !important;
}

/* Add robot icon to all assistant avatars */
.assistant-avatar svg {
  display: none;
}

.assistant-avatar::before {
  content: '';
  display: block;
  width: 16px;
  height: 16px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='%23e2e8f0' viewBox='0 0 16 16'%3E%3Cpath d='M6 12.5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 0 1h-3a.5.5 0 0 1-.5-.5M3 8.062C3 6.76 4.235 5.765 5.53 5.886a26.6 26.6 0 0 0 4.94 0C11.765 5.765 13 6.76 13 8.062v1.157a.93.93 0 0 1-.765.935c-.845.147-2.34.346-4.235.346s-3.39-.2-4.235-.346A.93.93 0 0 1 3 9.219zm4.542-.827a.25.25 0 0 0-.217.068l-.92.9a25 25 0 0 1-1.871-.183.25.25 0 0 0-.068.495c.55.076 1.232.149 2.02.193a.25.25 0 0 0 .189-.071l.754-.736.847 1.71a.25.25 0 0 0 .404.062l.932-.97a25 25 0 0 0 1.922-.188.25.25 0 0 0-.068-.495c-.538.074-1.207.145-1.98.189a.25.25 0 0 0-.166.076l-.754.785-.842-1.7a.25.25 0 0 0-.182-.135'/%3E%3Cpath d='M8.5 1.866a1 1 0 1 0-1 0V3h-2A4.5 4.5 0 0 0 1 7.5V8a1 1 0 0 0-1 1v2a1 1 0 0 0 1 1v1a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2v-1a1 1 0 0 0 1-1V9a1 1 0 0 0-1-1v-.5A4.5 4.5 0 0 0 10.5 3h-2zM14 7.5V13a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V7.5A3.5 3.5 0 0 1 5.5 4h5A3.5 3.5 0 0 1 14 7.5'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}

/* Thinking indicator */
.thinking-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 8px 12px;
  border-radius: 10px; /* Match message border radius */
  background-color: var(--message-bg-assistant-dark);
  border: 1px solid rgba(30, 41, 59, 0.5); /* Match assistant message border */
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1); /* Soft shadow for depth */
  margin-top: 10px; /* Add top margin to align with avatar */
}

.dot {
  width: 6px; /* Slightly smaller dots */
  height: 6px;
  border-radius: 50%;
  background-color: #94a3b8;
  animation: pulse 1.5s infinite ease-in-out;
}

.dot:nth-child(2) {
  animation-delay: 0.2s;
}

.dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(0.8);
    opacity: 0.4; /* More subtle opacity */
  }
  50% {
    transform: scale(1.1); /* Less dramatic scaling */
    opacity: 0.8; /* More subtle opacity */
  }
}

/* 亮色主题下的思考指示器 */
.embedded-chat.light .thinking-indicator {
  background-color: rgba(241, 245, 249, 0.8);
}

.embedded-chat.light .dot {
  background-color: #6366f1;
}

/* Tool call indicator */
.tool-call-indicator {
  font-size: 0.9em;
  color: #3b82f6;
  background-color: rgba(59, 130, 246, 0.1);
  padding: 4px 8px;
  border-radius: 4px;
  margin-bottom: 8px;
  display: inline-block;
  font-weight: 500;
}

/* Error message */
.error-message {
  color: #ef4444;
  padding: 8px 12px;
  border-radius: 8px;
  background-color: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.2);
  margin: 8px 0;
}

/* Chat input area */
.chat-input {
  display: flex;
  padding: 12px;
  background-color: var(--bg-color-dark);
  border-top: 1px solid var(--border-color-dark);
}

.chat-input input {
  flex: 1;
  padding: 10px 14px;
  border-radius: 20px;
  border: 1px solid #334155;
  background-color: #1e293b;
  color: #e2e8f0;
  outline: none;
  font-size: 14px;
}

.chat-input input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.3);
}

.chat-input button {
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  margin-left: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s;
}

.chat-input button:hover {
  background-color: #4338ca;
}

.chat-input button:disabled {
  background-color: #64748b;
  cursor: not-allowed;
}

/* Code block styles */
.message-content pre {
  background-color: #0f172a;
  border-radius: 6px;
  padding: 12px;
  overflow-x: auto;
  margin: 8px 0;
  font-family: 'Menlo', 'Monaco', 'Courier New', monospace;
  font-size: 13px;
  color: #e2e8f0;
}

.message-content code {
  font-family: 'Menlo', 'Monaco', 'Courier New', monospace;
  background-color: rgba(15, 23, 42, 0.5);
  padding: 2px 4px;
  border-radius: 4px;
  font-size: 0.9em;
}

/* Table styles */
.message-content table {
  border-collapse: collapse;
  width: 100%;
  margin: 8px 0;
  font-size: 13px;
}

.message-content th,
.message-content td {
  border: 1px solid #334155;
  padding: 6px 8px;
  text-align: left;
}

.message-content th {
  background-color: #1e293b;
}

/* Helper classes */
.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.justify-end {
  justify-content: flex-end;
}

.mr-2 {
  margin-right: 0.5rem;
}

.focus\\:ring-2:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus\\:ring-blue-500:focus {
  --tw-ring-color: #3b82f6;
}

.hover\\:bg-blue-600:hover {
  background-color: #2563eb;
}

.transition-colors {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

/* Add styles for tool cards and collapsible sections */
.embed-card {
  transition: all 0.2s ease-in-out;
  border-radius: 0.25rem;
  overflow: hidden;
  background-color: rgba(31, 41, 55, 0.2) !important;
  border: 1px solid rgba(55, 65, 81, 0.15);
  margin: 3px 0;
}

.embed-card:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.embed-card.finish-task {
  border-left: 2px solid rgba(16, 185, 129, 0.3);
}

.embed-card.sequential-thinking {
  border-left: 2px solid rgba(99, 102, 241, 0.3);
}

.embed-card.memory-tool {
  border-left: 2px solid rgba(245, 158, 11, 0.3);
}

.embed-card.error {
  border-left: 2px solid rgba(239, 68, 68, 0.3);
}

.embed-card.wms-api {
  border-left: 2px solid rgba(59, 130, 246, 0.3);
}

/* Simplified styling for sequential thinking steps */
.sequential-thinking .text-xs.bg-gray-800\/50 {
  background-color: rgba(31, 41, 55, 0.3);
  border: 1px solid rgba(75, 85, 99, 0.1);
  color: #d1d5db;
}

/* Smaller, more subtle styling for number circles */
.sequential-thinking .w-5.h-5.rounded-full.bg-indigo-500\/70 {
  width: 16px !important;
  height: 16px !important;
  background-color: rgba(79, 70, 229, 0.3);
  border: 1px solid rgba(139, 92, 246, 0.1);
  box-shadow: none;
}

/* Use less space for scrollbars */
.sequential-thinking .max-h-80.overflow-y-auto::-webkit-scrollbar {
  width: 3px;
}

.sequential-thinking .max-h-80.overflow-y-auto::-webkit-scrollbar-track {
  background: rgba(30, 41, 59, 0.1);
}

.sequential-thinking .max-h-80.overflow-y-auto::-webkit-scrollbar-thumb {
  background-color: rgba(99, 102, 241, 0.2);
}

/* More subtle text colors */
.text-gray-200 {
  color: #9ca3af !important;
}

.text-gray-300 {
  color: #9ca3af !important;
}

.text-gray-400 {
  color: #6b7280 !important;
}

.text-indigo-400 {
  color: #818cf8 !important;
  opacity: 0.7;
}

.text-green-400 {
  color: #34d399 !important;
  opacity: 0.7;
}

.text-red-400 {
  color: #f87171 !important;
  opacity: 0.7;
}

.text-blue-400 {
  color: #60a5fa !important;
  opacity: 0.7;
}

/* Simpler tool card styling */
.tool-card {
  background-color: rgba(31, 41, 55, 0.2) !important;
  border: 1px solid rgba(55, 65, 81, 0.15);
  margin: 3px 0;
}

/* More subtle reasoning block */
.reasoning {
  background-color: rgba(30, 58, 138, 0.05) !important;
  border-left: 1px solid rgba(59, 130, 246, 0.2) !important;
}

/* Fix button styling to match theme */
button.text-gray-300,
button.text-gray-400 {
  background-color: rgba(31, 41, 55, 0.3) !important;
  border: 1px solid rgba(75, 85, 99, 0.2) !important;
  color: #9ca3af !important;
  font-size: 11px;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

button.text-gray-300:hover,
button.text-gray-400:hover {
  background-color: rgba(55, 65, 81, 0.4) !important;
  color: #d1d5db !important;
}

/* 亮色主题下的聊天输入区域 */
.embedded-chat.light .chat-input {
  border-top: 1px solid var(--border-color-light);
  background-color: var(--bg-color-light);
}

.embedded-chat.light .chat-input input {
  background-color: #f1f5f9;
  color: var(--text-color-light);
  border: 1px solid #e2e8f0;
}

.embedded-chat.light .chat-input input:focus {
  outline: none;
  border-color: #6366f1;
  box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.2);
}

.embedded-chat.light .chat-input button {
  background-color: #6366f1;
}

/* 恢复最小化状态的样式 */
/* Remove resize handle in minimized state */
.embedded-chat.minimized::after {
  display: none;
}

/* Show robot icon in minimized state */
.embedded-chat.minimized::before {
  content: '';
  display: block;
  width: 26px;
  height: 26px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' fill='%23ffffff' viewBox='0 0 16 16'%3E%3Cpath d='M6 12.5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 0 1h-3a.5.5 0 0 1-.5-.5M3 8.062C3 6.76 4.235 5.765 5.53 5.886a26.6 26.6 0 0 0 4.94 0C11.765 5.765 13 6.76 13 8.062v1.157a.93.93 0 0 1-.765.935c-.845.147-2.34.346-4.235.346s-3.39-.2-4.235-.346A.93.93 0 0 1 3 9.219zm4.542-.827a.25.25 0 0 0-.217.068l-.92.9a25 25 0 0 1-1.871-.183.25.25 0 0 0-.068.495c.55.076 1.232.149 2.02.193a.25.25 0 0 0 .189-.071l.754-.736.847 1.71a.25.25 0 0 0 .404.062l.932-.97a25 25 0 0 0 1.922-.188.25.25 0 0 0-.068-.495c-.538.074-1.207.145-1.98.189a.25.25 0 0 0-.166.076l-.754.785-.842-1.7a.25.25 0 0 0-.182-.135'/%3E%3Cpath d='M8.5 1.866a1 1 0 1 0-1 0V3h-2A4.5 4.5 0 0 0 1 7.5V8a1 1 0 0 0-1 1v2a1 1 0 0 0 1 1v1a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2v-1a1 1 0 0 0 1-1V9a1 1 0 0 0-1-1v-.5A4.5 4.5 0 0 0 10.5 3h-2zM14 7.5V13a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V7.5A3.5 3.5 0 0 1 5.5 4h5A3.5 3.5 0 0 1 14 7.5'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  margin-top: 0;
  pointer-events: none;
  filter: drop-shadow(0 0 1px rgba(0, 0, 0, 0.5));
}

/* 亮色主题下的最小化状态机器人图标 */
.embedded-chat.light.minimized::before {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' fill='%23334155' viewBox='0 0 16 16'%3E%3Cpath d='M6 12.5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 0 1h-3a.5.5 0 0 1-.5-.5M3 8.062C3 6.76 4.235 5.765 5.53 5.886a26.6 26.6 0 0 0 4.94 0C11.765 5.765 13 6.76 13 8.062v1.157a.93.93 0 0 1-.765.935c-.845.147-2.34.346-4.235.346s-3.39-.2-4.235-.346A.93.93 0 0 1 3 9.219zm4.542-.827a.25.25 0 0 0-.217.068l-.92.9a25 25 0 0 1-1.871-.183.25.25 0 0 0-.068.495c.55.076 1.232.149 2.02.193a.25.25 0 0 0 .189-.071l.754-.736.847 1.71a.25.25 0 0 0 .404.062l.932-.97a25 25 0 0 0 1.922-.188.25.25 0 0 0-.068-.495c-.538.074-1.207.145-1.98.189a.25.25 0 0 0-.166.076l-.754.785-.842-1.7a.25.25 0 0 0-.182-.135'/%3E%3Cpath d='M8.5 1.866a1 1 0 1 0-1 0V3h-2A4.5 4.5 0 0 0 1 7.5V8a1 1 0 0 0-1 1v2a1 1 0 0 0 1 1v1a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2v-1a1 1 0 0 0 1-1V9a1 1 0 0 0-1-1v-.5A4.5 4.5 0 0 0 10.5 3h-2zM14 7.5V13a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V7.5A3.5 3.5 0 0 1 5.5 4h5A3.5 3.5 0 0 1 14 7.5'/%3E%3C/svg%3E");
  filter: drop-shadow(0 0 1px rgba(0, 0, 0, 0.2));
}

/* Special styling for header in minimized state */
.embedded-chat.minimized .chat-header {
  height: 100%;
  padding: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: transparent;
  border: none;
  position: relative;
}

/* Position classes - these are applied to the container, not the embedded chat */
/* The embedded chat itself uses relative positioning and fills its container */

/* 覆盖 DayPicker 选中和今天的样式，使用 Item 设计系统 */

/* 设置日历日期按钮的字体颜色 - Item Purple Light */
.cyber-agent-root .rdp-day_button,
.rdp-day_button {
  color: #C4B5FD !important; /* item-purple-light */
}

/* 保持选中日期的白色字体 */
.cyber-agent-root .rdp-day_selected .rdp-day_button,
.rdp-day_selected .rdp-day_button {
  color: #fff !important;
}

/* 选中日期样式 - 使用 Item 紫色到橙色渐变 */
.cyber-agent-root .rdp-day_selected,
.rdp-day_selected {
  background: #8B5CF6 !important;
  border: 2px solid #F97316 !important; /* item-orange border */
  color: #fff !important;
}

/* 当天日期样式 - Item Purple */
.cyber-agent-root .rdp-day_today:not(.rdp-day_selected),
.rdp-day_today:not(.rdp-day_selected) {
  border: 2px solid #8B5CF6 !important; /* item-purple border */
  color: #8B5CF6 !important;
  background-color: rgba(139, 92, 246, 0.15) !important;
}

/* 当天且被选中的日期样式 */
.cyber-agent-root .rdp-day_today.rdp-day_selected,
.rdp-day_today.rdp-day_selected {
  background: #8B5CF6 !important;
  border: 2px solid #F97316 !important; /* item-orange border */
  box-sizing: border-box !important;
  color: #fff !important;
}

/* 当天且被选中的日期按钮样式 */
.cyber-agent-root .rdp-day_today.rdp-day_selected .rdp-day_button,
.rdp-day_today.rdp-day_selected .rdp-day_button {
  color: #fff !important;
  font-weight: 600 !important;
}
