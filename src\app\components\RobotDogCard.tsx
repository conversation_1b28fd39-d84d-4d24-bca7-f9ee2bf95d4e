'use client';

import React from 'react';
import ArtifactToolCard from './ArtifactToolCard';

interface RobotDogCardProps {
  toolInvocation: any;
}

export default function RobotDogCard({ toolInvocation }: RobotDogCardProps) {
  const args = toolInvocation?.args || {};
  const result = toolInvocation?.result || {};
  const dog_id = args.dog_id || 'rbt_dog';
  const facility_id = args.facility_id || 'yard-25';
  const spot = args.spot;
  
  // 获取操作描述
  const getOperationDescription = () => {
    if (args.type === 'route' && spot) {
      return `Route ${spot}`;
    }
    return 'Patrol';
  };

  const robotDogIcon = (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-4 h-4">
      <path strokeLinecap="round" strokeLinejoin="round" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 01.553-.894L9 2l6 3 6-3v13l-6 3-6-3z" />
    </svg>
  );

  const title = `Robot Dog: ${dog_id} • ${getOperationDescription()}`;

  return (
    <ArtifactToolCard
      toolInvocation={toolInvocation}
      title={title}
      icon={robotDogIcon}
      borderColor="border-item-orange/30"
      iconBgColor="bg-item-orange/20"
      iconTextColor="text-item-orange"
    />
  );
} 