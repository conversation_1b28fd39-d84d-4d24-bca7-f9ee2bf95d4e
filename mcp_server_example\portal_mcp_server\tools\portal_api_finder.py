from llama_index.core import Document, VectorStoreIndex, Settings
from llama_index.embeddings.huggingface import HuggingFaceEmbedding
from typing import List, Dict, Any, Set, Tuple
import json
from pathlib import Path
from urllib.parse import unquote
import logging
import os
import re

logger = logging.getLogger(__name__)

class PortalApiFinder:
    _instances = {}
    
    def __new__(cls, model_name: str = "all-MiniLM-L6-v2", api_file_paths: List[str] = None):
        instance_key = model_name
        if instance_key not in cls._instances:
            cls._instances[instance_key] = super(PortalApiFinder, cls).__new__(cls)
        return cls._instances[instance_key]
        
    def __init__(self, model_name: str = "all-MiniLM-L6-v2", api_file_paths: List[str] = None):
        instance_key = model_name
        if hasattr(self, '_initialized') and self._initialized.get(instance_key, False):
            return
                
        logger.info(f"Initializing PortalApiFinder with model: {model_name}...")
        if api_file_paths is None:
            docs_dir = Path(__file__).parent.parent / 'documents'
            api_file_paths = [
                str(docs_dir / 'client-portal.json'),
                # 可以根据需要添加更多 Portal 相关的 API 文件
                # str(docs_dir / 'portal-user.json'),
                # str(docs_dir / 'portal-order.json'),
                # str(docs_dir / 'portal-inventory.json'),
            ]
            
        # 重要：禁用 LLM
        Settings.llm = None
        Settings.chunk_size = 16384
        Settings.embed_model = HuggingFaceEmbedding(
                model_name=model_name
            )
            
        self.api_file_paths = api_file_paths
        self.api_data = self._load_api_files()
        self.schemas = self._preprocess_schemas()
        self.documents = self._create_documents()
        self.index = self._create_index()
        
        # 记录所有API路径，用于调试和验证
        self.all_api_paths = set()
        for path in self.api_data.get('paths', {}).keys():
            self.all_api_paths.add(path)
        logger.info(f"Total APIs loaded: {len(self.all_api_paths)}")
            
        if not hasattr(self, '_initialized'):
            self._initialized = {}
        self._initialized[instance_key] = True
        logger.info(f"PortalApiFinder initialized successfully with model: {model_name}")

    @classmethod
    def get_instance(cls, model_name: str = "all-MiniLM-L6-v2", api_file_paths: List[str] = None) -> 'PortalApiFinder':
        """Get or create a singleton instance of PortalApiFinder"""
        return cls(model_name, api_file_paths)

    def _preprocess_schemas(self) -> Dict:
        """预处理所有 schemas，建立引用映射"""
        schemas = {}
        raw_schemas = self.api_data.get('components', {}).get('schemas', {})
        
        # 处理所有可能的编码形式
        for key, value in raw_schemas.items():
            # 存储原始 key
            schemas[key] = value
            # 存储 URL 解码后的 key
            decoded_key = unquote(key)
            if decoded_key != key:
                schemas[decoded_key] = value
            # 存储编码后的 key
            encoded_key = key.replace("<", "%C2%AB").replace(">", "%C2%BB")
            if encoded_key != key:
                schemas[encoded_key] = value
                
        return schemas

    def _resolve_schema_ref(self, schema: Dict, depth: int = 0) -> Dict:
        """Resolve $ref in schema and replace with actual fields"""
        if depth > 10:  # 防止循环引用
            return {"type": "object", "description": "Max depth reached"}
            
        if not isinstance(schema, dict):
            return schema
            
        if '$ref' in schema:
            ref_path = schema['$ref'].split('/')
            ref_name = ref_path[-1]
            
            # 尝试不同形式的引用名称
            if ref_name not in self.schemas:
                # URL 解码尝试
                decoded_name = unquote(ref_name)
                if decoded_name in self.schemas:
                    ref_name = decoded_name
                else:
                    # 如果找不到引用，返回基本结构
                    return {
                        "type": "object",
                        "description": f"Unresolved reference: {ref_name}"
                    }
            
            ref_schema = self.schemas[ref_name]
            return self._resolve_schema_ref(ref_schema, depth + 1)
            
        resolved_schema = {}
        for key, value in schema.items():
            if key == 'properties':
                resolved_schema[key] = {
                    prop_name: self._resolve_schema_ref(prop_schema, depth + 1)
                    for prop_name, prop_schema in value.items()
                }
            elif isinstance(value, dict):
                resolved_schema[key] = self._resolve_schema_ref(value, depth + 1)
            elif isinstance(value, list):
                resolved_schema[key] = [
                    self._resolve_schema_ref(item, depth + 1) if isinstance(item, dict) else item
                    for item in value
                ]
            else:
                resolved_schema[key] = value
                
        return resolved_schema

    def _load_api_files(self) -> Dict[str, Any]:
        """Load and merge multiple API files"""
        merged_data = {
            'paths': {},
            'components': {'schemas': {}}
        }
        
        total_paths = 0
        
        for file_path in self.api_file_paths:
            try:
                logger.info(f"Loading API file: {file_path}")
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    
                    # 检查并记录文件内容
                    path_count = len(data.get('paths', {}))
                    schema_count = len(data.get('components', {}).get('schemas', {}))
                    logger.info(f"  - Found {path_count} API paths and {schema_count} schemas in {file_path}")
                    
                    # 记录一些API路径示例
                    if path_count > 0:
                        sample_paths = list(data.get('paths', {}).keys())[:3]
                        logger.info(f"  - Sample paths: {sample_paths}")
                    
                    # Merge paths
                    if 'paths' in data:
                        merged_data['paths'].update(data['paths'])
                        total_paths += path_count
                        
                    # Merge schemas
                    if 'components' in data and 'schemas' in data['components']:
                        merged_data['components']['schemas'].update(
                            data['components']['schemas']
                        )
            except Exception as e:
                logger.error(f"Error loading API file {file_path}: {str(e)}")
                continue
        
        logger.info(f"Total API paths loaded: {total_paths}")  
        return merged_data

    def _extract_path_components(self, path: str) -> Tuple[Set[str], Set[str]]:
        """从API路径中提取组件和语义信息"""
        path_lower = path.lower()
        
        # 分割路径并清理
        segments = [s for s in path_lower.split('/') if s and not s.startswith('{')]
        
        # 提取单词（处理连字符和下划线）
        words = set()
        for segment in segments:
            # 分割连字符和下划线
            sub_words = re.split(r'[-_]', segment)
            words.update(sub_words)
        
        # 提取复合概念（保持连字符和下划线的组合）
        compounds = set(segments)
        
        return words, compounds

    def _infer_semantic_info(self, path: str, method: str, api_details: Dict) -> Dict[str, Any]:
        """推断API的语义信息"""
        words, compounds = self._extract_path_components(path)
        
        # 操作类型推断
        operation_indicators = {
            'search': ['search', 'find', 'query', 'list', 'get'],
            'create': ['create', 'add', 'insert', 'new'],
            'update': ['update', 'modify', 'edit', 'change'],
            'delete': ['delete', 'remove', 'cancel']
        }
        
        detected_operation = None
        # 首先从路径词汇推断
        for op_type, indicators in operation_indicators.items():
            if any(indicator in words for indicator in indicators):
                detected_operation = op_type
                break
        
        # 如果路径没有明确指示，从HTTP方法推断
        if not detected_operation:
            method_mapping = {
                'get': 'search',
                'post': 'create',  # 默认，但可能被路径覆盖
                'put': 'update',
                'delete': 'delete'
            }
            detected_operation = method_mapping.get(method.lower())
            
            # 特殊处理：POST + search路径 = search操作
            if method.lower() == 'post' and any(word in words for word in ['search', 'find', 'query']):
                detected_operation = 'search'
        
        # 实体类型推断（Portal相关的实体）
        portal_entities = [
            'outbound_order',         # 出库订单
            'outbound_order_item',   # 出库订单明细
            'inventory_status',      # 库存状态
            'report',                # 报表
            'customer',              # 客户
            'warehouse'              # 仓库
        ]
        
        detected_entities = []
        for entity in portal_entities:
            if entity in words:
                detected_entities.append(entity)
        
        # 检测复合实体
        for compound in compounds:
            if any(entity in compound for entity in portal_entities):
                detected_entities.append(compound)
        
        return {
            'operation': detected_operation,
            'entities': detected_entities,
            'words': words,
            'compounds': compounds
        }

    def _create_documents(self) -> List[Document]:
        documents = []
        
        for path, methods in self.api_data.get('paths', {}).items():
            for method, details in methods.items():
                # 推断语义信息
                semantic_info = self._infer_semantic_info(path, method, details)
                
                # 构建文档内容
                content = f"""
                API Path: {path}
                Method: {method.upper()}
                Summary: {details.get('summary', '')}
                """
                
                # 添加语义描述
                if semantic_info['operation']:
                    op_descriptions = {
                        'search': 'search, find, retrieve, query, or list information',
                        'create': 'create, add, or insert new data',
                        'update': 'update, modify, or change existing data',
                        'delete': 'delete, remove, or cancel data'
                    }
                    content += f"\nThis API is used to {op_descriptions.get(semantic_info['operation'], semantic_info['operation'])}. "
                
                if semantic_info['entities']:
                    entities_text = ', '.join(semantic_info['entities'])
                    content += f"\nThis API works with: {entities_text}. "
                
                # 添加路径词汇以提高搜索匹配
                if semantic_info['words']:
                    words_text = ' '.join(semantic_info['words'])
                    content += f"\nPath components: {words_text}. "
                
                # 添加详细信息
                content += f"\nDescription: {details.get('description', '')}"
                content += f"\nOperation ID: {details.get('operationId', '')}"
                content += f"\nTags: {', '.join(details.get('tags', []))}"
                
                # 添加参数信息
                parameters_text = ""
                for param in details.get('parameters', []):
                    parameters_text += f"{param.get('name', '')}: {param.get('description', '')}\n"
                
                if parameters_text:
                    content += f"\nParameters:\n{parameters_text}"
                
                # 添加请求体信息
                if 'requestBody' in details and 'content' in details['requestBody']:
                    for content_type, content_details in details['requestBody']['content'].items():
                        if 'schema' in content_details:
                            schema = content_details['schema']
                            if 'properties' in schema:
                                properties = schema['properties']
                                body_text = "\nRequest Body Fields:\n"
                                for prop_name, prop_details in properties.items():
                                    body_text += f"{prop_name}: {prop_details.get('description', '')}\n"
                                content += body_text
                
                metadata = {
                    'path': path,
                    'method': method.upper(),
                    'summary': details.get('summary', ''),
                    'operation': semantic_info['operation'],
                    'entities': semantic_info['entities'],
                    'words': list(semantic_info['words']),
                    'compounds': list(semantic_info['compounds'])
                }
                
                documents.append(Document(
                    text=content,
                    metadata=metadata,
                    excluded_llm_metadata_keys=['path', 'method', 'summary', 'operation', 'entities', 'words', 'compounds']
                ))
                
        return documents
        
    def _create_index(self) -> VectorStoreIndex:
        return VectorStoreIndex.from_documents(
            self.documents,
            show_progress=True
        )

    def _calculate_relevance_score(self, query: str, metadata: Dict, base_similarity: float) -> float:
        """计算API与查询的相关性分数，使用加权平均而非简单累加"""
        query_lower = query.lower()
        query_words = set(re.split(r'[-_\s]+', query_lower))
        
        # 基础分数（向量相似度）
        base_score = base_similarity
        
        # 计算各种匹配的权重分数
        scores = []
        weights = []
        
        # 1. 向量相似度（权重最高）
        scores.append(base_score)
        weights.append(0.5)
        
        # 2. 词汇匹配分数
        api_words = set(metadata.get('words', []))
        word_matches = len(query_words.intersection(api_words))
        if len(query_words) > 0:
            word_match_ratio = word_matches / len(query_words)
            scores.append(word_match_ratio)
            weights.append(0.2)
        
        # 3. 复合词匹配分数
        api_compounds = set(metadata.get('compounds', []))
        compound_matches = 0
        for compound in api_compounds:
            if any(word in compound for word in query_words):
                compound_matches += 1
        if len(api_compounds) > 0:
            compound_match_ratio = min(compound_matches / len(api_compounds), 1.0)
            scores.append(compound_match_ratio)
            weights.append(0.15)
        
        # 4. 实体匹配分数
        api_entities = set(metadata.get('entities', []))
        entity_matches = sum(1 for entity in api_entities if entity in query_lower)
        if len(api_entities) > 0:
            entity_match_ratio = min(entity_matches / len(api_entities), 1.0)
            scores.append(entity_match_ratio)
            weights.append(0.1)
        
        # 5. 操作匹配分数
        operation_keywords = {
            'search': ['find', 'search', 'get', 'query', 'list'],
            'create': ['create', 'add', 'insert', 'new'],
            'update': ['update', 'modify', 'edit', 'change'],
            'delete': ['delete', 'remove', 'cancel']
        }
        
        api_operation = metadata.get('operation')
        if api_operation:
            operation_words = operation_keywords.get(api_operation, [])
            operation_match = 1.0 if any(word in query_words for word in operation_words) else 0.0
            scores.append(operation_match)
            weights.append(0.05)
        
        # 计算加权平均分数
        if len(scores) > 0 and len(weights) > 0:
            # 归一化权重
            total_weight = sum(weights)
            normalized_weights = [w / total_weight for w in weights]
            
            # 计算加权平均
            weighted_score = sum(s * w for s, w in zip(scores, normalized_weights))
            return weighted_score
        
        return base_score
        
    def search_apis(self, query: str, top_k: int = 10) -> List[Dict]:
        """Search APIs using embedding similarity and return detailed API information"""
        try:
            # 向量检索
            retriever = self.index.as_retriever(similarity_top_k=max(top_k * 2, 20))
            vector_nodes = retriever.retrieve(query)
            
            # 处理结果并计算增强相关性分数
            results = []
            processed_paths = set()
            
            for node in vector_nodes:
                try:
                    metadata = node.metadata
                    path = metadata['path']
                    method = metadata['method'].lower()
                    
                    # 避免重复
                    path_method_key = f"{path}:{method}"
                    if path_method_key in processed_paths:
                        continue
                    processed_paths.add(path_method_key)
                    
                    # 跳过相似度过低的结果
                    if node.score is not None and node.score < 0.2:
                        continue
                        
                    # 计算增强相关性分数
                    enhanced_score = self._calculate_relevance_score(
                        query, metadata, node.score or 0.0
                    )
                    
                    api_details = self.api_data['paths'][path][method]
                    result = self._format_api_result(path, method, api_details, enhanced_score)
                    result['matched_by'] = 'vector_search'
                    
                    results.append(result)
                    
                except Exception as e:
                    logger.warning(f"Error processing API result: {str(e)}")
                    continue
            
            # 按相关性分数排序
            results.sort(key=lambda x: -x['similarity'])
            
            return results[:top_k]
            
        except Exception as e:
            logger.error(f"Error searching APIs: {str(e)}")
            return []

    def _format_api_result(self, path: str, method: str, api_details: Dict, similarity: float) -> Dict:
        """格式化API结果"""
        result = {
            'path': path,
            'method': method.upper(),
            'similarity': similarity if similarity is not None else 0.0,
            'details': {
                'summary': api_details.get('summary', ''),
                'description': api_details.get('description', ''),
                'tags': api_details.get('tags', []),
                'parameters': {
                    'headers': [],
                    'path': [],
                    'query': [],
                    'body': None
                },
                'responses': {}
            }
        }
        
        # 处理参数
        for param in api_details.get('parameters', []):
            param_info = {
                'name': param.get('name'),
                'description': param.get('description', ''),
                'required': param.get('required', False),
                'schema': self._resolve_schema_ref(param.get('schema', {})),
                'example': param.get('example'),
            }
            
            param_location = param.get('in', '')
            if param_location == 'header':
                result['details']['parameters']['headers'].append(param_info)
            elif param_location == 'path':
                result['details']['parameters']['path'].append(param_info)
            elif param_location == 'query':
                result['details']['parameters']['query'].append(param_info)
        
        # 处理请求体
        if 'requestBody' in api_details:
            request_body = api_details['requestBody']
            content_type = next(iter(request_body.get('content', {})), None)
            if content_type:
                result['details']['parameters']['body'] = {
                    'content_type': content_type,
                    'required': request_body.get('required', False),
                    'description': request_body.get('description', ''),
                    'schema': self._resolve_schema_ref(request_body['content'][content_type].get('schema', {})),
                }
        
        return result

    def get_api_details(self, path: str, method: str = None) -> Dict:
        if path not in self.api_data.get('paths', {}):
            return {}
            
        if method:
            method = method.lower()
            return self.api_data['paths'][path].get(method, {})
            
        return self.api_data['paths'][path] 
    
    def get_available_paths(self) -> List[str]:
        """返回所有可用的API路径，用于调试"""
        return sorted(list(self.all_api_paths))


if __name__ == "__main__":
    api_finder = PortalApiFinder()
    
    # 打印API文件加载情况
    print(f"\nLoaded API files: {len(api_finder.api_file_paths)}")
    for file_path in api_finder.api_file_paths:
        print(f"- {file_path}")
    
    # 打印总API路径数量
    print(f"\nTotal API paths: {len(api_finder.all_api_paths)}")
    
    # 查找带user的API路径
    print("\nSample API paths with 'user':")
    user_apis = []
    for path in api_finder.get_available_paths():
        if "user" in path.lower():
            user_apis.append(path)
    
    # 只显示部分结果以避免过多输出
    for path in user_apis[:5]:
        print(f"- {path}")
    if len(user_apis) > 5:
        print(f"... and {len(user_apis) - 5} more")
    
    # 运行搜索测试
    test_queries = ["find user", "search order", "query product", "get inventory", "create customer", "update supplier", "delete warehouse", "list payment", "search notification", "find report"]
    
    for query in test_queries:
        print(f"\n\nTesting search for '{query}':")
        results = api_finder.search_apis(query)
        
        print(f"Found {len(results)} results")
        
        # 输出前5个结果
        for i, result in enumerate(results[:5]):
            print(f"\n{i+1}. Path: {result['path']}")
            print(f"   Method: {result['method']}")
            print(f"   Similarity: {result['similarity']:.2f}")
            print(f"   Matched by: {result.get('matched_by', 'unknown')}")
            print(f"   Summary: {result['details']['summary']}")
        
        if len(results) > 5:
            print(f"\n... and {len(results) - 5} more results")
    
    print("\nDone!") 