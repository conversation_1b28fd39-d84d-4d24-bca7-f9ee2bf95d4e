import aiohttp
import json
import logging
import base64
import os
from typing import Dict, Any, List, Union
from pathlib import Path
from config import TMS_BASE_URL, TMS_PHP_BASE_URL, TMS_API_KEY, TMS_TRACKING_URL_PREFIX
from tools.tms_api_finder import TMSApiFinder
from .base import BaseTool

logger = logging.getLogger(__name__)

# 添加一个辅助函数读取提示词
def get_tms_prompt():
    """读取TMS提示词"""
    parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    tms_prompt_path = os.path.join(parent_dir, "prompts", "tms_prompt.py")
    
    if os.path.exists(tms_prompt_path):
        with open(tms_prompt_path, 'r', encoding='utf-8') as f:
            content = f.read()
            # 提取TMS_TOOLS_PROMPT变量的值
            if "TMS_TOOLS_PROMPT = '''" in content:
                start_index = content.find("TMS_TOOLS_PROMPT = '''") + len("TMS_TOOLS_PROMPT = '''")
                end_index = content.find("'''", start_index)
                return content[start_index:end_index]
    logger.error(f"Error reading TMS prompt: {e}")
    return """## Guide for TMS Tools

TMS Tools provides functionality for managing transport operations.

Available tools:
- find_tms_api - Search for TMS API endpoints
- call_tms_api - Call TMS API endpoints
"""

async def on_request_start(session, trace_config_ctx, params):
    logger.info(f"Starting request:")
    logger.info(f"Sending request: {params.method} {params.url}")
    logger.info(f"Headers: {params.headers}")
    logger.info(f"Params: {params}")
    if hasattr(params, 'data'):
        logger.info(f"Data: {params.data}")
    if hasattr(params, 'json'):
        logger.info(f"JSON: {params.json}")

async def on_request_end(session, trace_config_ctx, params):
    logger.info(f"Ending request: {params.method} {params.url}")
    logger.info(f"Response status: {params.response.status}")


class TMSTools(BaseTool):
    def __init__(self, company_id: int = "23", **kwargs):
        """Initialize TMS Tools"""
        if not TMS_BASE_URL:
            raise ValueError("Missing required TMS_BASE_URL configuration in config.py")
            
        self.base_url = TMS_BASE_URL
        self.session = None
        self.api_finder = None
        self.company_id = company_id
        self.php_base_url = TMS_PHP_BASE_URL
        self.api_key = TMS_API_KEY

        self.user_id = None
        self.user_token = None

        # Basic headers
        self.headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'X-API-KEY': self.api_key
        }
        try:
            self.api_finder = TMSApiFinder.get_instance()
            logger.info("Successfully initialized TMS API Finder")
        except Exception as e:
            logger.error(f"Failed to initialize TMS API Finder: {str(e)}")
            
    def update_context(self, context: Dict[str, Any]):
        """Update TMS tool context"""
        self.context = context
        logger.info(f"Updated TMS context")
        
    async def call_tms_api(self, path: str, method: str, params: Dict = None, company_id: str = None, headers: Dict[str, Any] = None) -> Dict[str, Any]:
        """Call TMS API with authentication"""
        # Update company_id if provided for this call
        current_company_id = company_id or self.company_id
        
        # Add company_id to params for tracking APIs
        if '/cpv2/tracking/' in path:
            params = params or {}
            params['company_id'] = current_company_id
            
        # 根据 API 路径选择请求格式和基础 URL
        is_php_api = path.endswith('.php')
        base_url = self.php_base_url if is_php_api else self.base_url
        url = f"{base_url}{path}"
        
        # Log the exact request details
        logger.info(f"Calling TMS API with details:")
        logger.info(f"URL: {url}")
        logger.info(f"Method: {method}")
        logger.info(f"Headers: {json.dumps(self.headers, indent=2)}")
        if params:
            logger.info(f"Request Body: {json.dumps(params, indent=2)}")
        
        # Create trace config
        trace_config = aiohttp.TraceConfig()
        trace_config.on_request_start.append(on_request_start)
        trace_config.on_request_end.append(on_request_end)
        
        if is_php_api:  # 移除 or is_quote_api，询价功能已迁移
            # 对于 PHP API，使用 form-data 格式
            headers = {
                'Accept': 'application/json',
                'X-API-KEY': self.api_key
            }
            
            # 如果有用户认证信息，添加到请求参数中
            request_params = params or {}
            if self.user_id and self.user_token:
                request_params['UserID'] = self.user_id
                request_params['UserToken'] = self.user_token
        else:
            # 对于其他 API，使用 JSON 格式
            headers = self.headers
            request_params = params

        async with aiohttp.ClientSession(trace_configs=[trace_config]) as session:
            try:
                if is_php_api:  
                    # 对于 PHP API，使用 form-data 格式发送请求
                    async with session.request(
                        method,
                        url,
                        headers=headers,
                        data=request_params  # 使用包含了用户认证的参数
                    ) as response:
                        response_json = await response.json()
                        logger.debug(f"API Response: {response_json}")
                        
                        # Log response
                        logger.info(f"TMS API Response Status: {response.status}")
                        logger.info(f"TMS API Response Headers: {dict(response.headers)}")
                        logger.info(f"TMS API Response Body: {response_json}")
                        return {
                            "content": [{
                                "content": json.dumps({
                                    "success": True,
                                    "status_code": response.status,
                                    "data": response_json
                                })
                            }]
                        }
                else:
                    # 对于其他 API，GET 用 params，其他用 json
                    request_args = {
                        "method": method,
                        "url": url,
                        "headers": headers
                    }
                    if method.upper() == "GET":
                        request_args["params"] = request_params
                    else:
                        request_args["json"] = request_params
                    async with session.request(**request_args) as response:
                        response_json = await response.json()
                        logger.debug(f"API Response: {response_json}")
                        
                        # Log response
                        logger.info(f"TMS API Response Status: {response.status}")
                        logger.info(f"TMS API Response Headers: {dict(response.headers)}")
                        logger.info(f"TMS API Response Body: {response_json}")
                        # 检查是否为 apinew/cpv2/tracking/getOrders 接口且返回正常
                        if ('apinew/cpv2/tracking/getOrders' in path and response.status == 200 and 'data' in response_json and 'data' in response_json['data']):
                            for order in response_json['data']['data']:
                                if isinstance(order, dict):
                                    order_id = order.get('order_id')
                                    if order_id:
                                        tracking_url = f"{TMS_TRACKING_URL_PREFIX}/client-portal/order-details/{order_id}?from=chatbot"
                                        order['trackingUrl'] = tracking_url

                        logger.info(f"TMS API Response Body after add trackingUrl: {response_json}")
                        return {
                            "content": [{
                                "content": json.dumps({
                                    "success": True,
                                    "status_code": response.status,
                                    "data": response_json
                                })
                            }]
                        }
            
            except Exception as e:
                logger.error(f"TMS API call failed: {str(e)}")
                return {
                    "content": [{
                        "content": json.dumps({
                            "success": False,
                            "error": str(e),
                            "message": "TMS API call failed"
                        })
                    }]
                }
            
    def _clean_response(self, data: Any) -> Any:
        """清理响应数据，移除敏感信息"""
        if isinstance(data, dict):
            return {k: self._clean_response(v) for k, v in data.items() 
                   if k not in ['password', 'token', 'secret']}
        elif isinstance(data, list):
            return [self._clean_response(item) for item in data]
        return data

    async def find_tms_api(self, query: str, top_k: int = 5) -> Dict[str, Any]:
        """
        Find the most relevant TMS APIs based on the query

        Args:
            query: User's search query
            top_k: Number of top relevant APIs to return

        Returns:
            Dict: Response in the format expected by Claude AI
        """
        try:
            if not self.api_finder:
                logger.error("TMS API Finder is not initialized")
                raise ValueError("TMS API Finder is not initialized")

            logger.info(f"Searching for TMS APIs with query: '{query}', top_k: {top_k}")
            
            # 检查API文件是否存在
            if hasattr(self.api_finder, 'api_file_paths'):
                for file_path in self.api_finder.api_file_paths:
                    logger.info(f"Checking API file: {file_path}, exists: {os.path.exists(file_path)}")
            
            results = self.api_finder.search_apis(query, top_k=top_k)
            logger.info(f"Search complete, found {len(results)} results")
            
            # Format results for better presentation
            formatted_results = []
            for result in results:
                api_info = {
                    'path': result['path'],
                    'method': result['method'],
                    'similarity': result['similarity'],
                    'summary': result['details']['summary'],
                    'description': result['details']['description'],
                    'parameters': {
                        'headers': result['details']['parameters']['headers'],
                        'path': result['details']['parameters']['path'],
                        'query': result['details']['parameters']['query'],
                        'body': result['details']['parameters']['body']
                    }
                }
                formatted_results.append(api_info)
                logger.info(f"API found: {result['method']} {result['path']} (similarity: {result['similarity']:.4f})")

            # 返回简化的格式，移除额外的嵌套层级
            return {
                "success": True,
                "apis": formatted_results,
                "message": f"Found {len(formatted_results)} relevant APIs"
            }

        except Exception as e:
            logger.error(f"Error finding TMS APIs: {str(e)}")
            logger.exception("Exception details:")
            return {
                "success": False,
                "error": str(e),
                "message": "Failed to find relevant APIs",
                "apis": []
            }

    def get_tools(self) -> list:
        """Return the available TMS tools and their schemas"""
        return [
            {
                "name": "call_tms_api",
                "description": "Call TMS API endpoints. You must first use find_tms_api to discover the correct API path before calling this.",
                "input_schema": {
                    "type": "object",
                    "properties": {
                        "path": {
                            "type": "string",
                            "description": "API path, must be one defined in the documentation"
                        },
                        "method": {
                            "type": "string",
                            "description": "HTTP method (GET, POST, PUT, DELETE)",
                            "enum": ["GET", "POST", "PUT", "DELETE"]
                        },
                        "params": {
                            "oneOf": [
                                {
                                    "type": "object",
                                    "description": "Request parameters as object, for APIs that expect object parameters"
                                },
                                {
                                    "type": "array",
                                    "description": "Request parameters as array, for APIs that expect array parameters"
                                }
                            ],
                            "description": "Request parameters, must match the API documentation format. Can be either an object or array depending on the API requirements."
                        },
                        "headers": {
                            "type": "object",
                            "description": "Required request headers, must for example: {'Authorization': 'Bearer YOUR_TOKEN'}"
                        }
                    },
                    "required": ["path", "method"]
                }
            },
            {
                "name": "find_tms_api",
                "description": "Search for relevant TMS APIs based on the query. Returns API information including path, method, parameters, etc.",
                "input_schema": {
                    "type": "object",
                    "properties": {
                        "query": {
                            "type": "string",
                            "description": "Search query describing the API functionality you're looking for"
                        },
                        "top_k": {
                            "type": "integer",
                            "description": "Number of most relevant APIs to return",
                            "default": 5,
                            "minimum": 1,
                            "maximum": 10
                        }
                    },
                    "required": ["query"]
                }
            }
        ]

    @classmethod
    def get_system_prompt(cls) -> str:
        return get_tms_prompt()