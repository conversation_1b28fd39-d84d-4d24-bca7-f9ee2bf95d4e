'use client';

/**
 * 客户端用户上下文管理
 *
 * 这个模块实现了客户端用户上下文管理，包含:
 * - 短期会话数据 (内存存储，登出时清除)
 */

/**
 * 客户端内存会话存储
 * 管理客户端内存中的会话数据 (登出时清除)
 */
class ClientSessionStore {
  private store: Map<string, SessionContext>;

  constructor() {
    this.store = new Map();
  }

  /**
   * 获取会话数据
   */
  get(userId: string): SessionContext | undefined {
    return this.store.get(userId);
  }

  /**
   * 设置会话数据
   */
  set(userId: string, session: SessionContext): void {
    this.store.set(userId, {
      ...session,
      lastActivity: Date.now()
    });
  }

  /**
   * 清除用户会话
   */
  clearUserSession(userId: string): void {
    this.store.delete(userId);
  }

  /**
   * 更新会话的特定字段
   */
  update(userId: string, updates: Partial<SessionContext>): SessionContext | undefined {
    const currentSession = this.store.get(userId);
    if (!currentSession) return undefined;

    const updatedSession = {
      ...currentSession,
      ...updates,
      lastActivity: Date.now()
    };

    this.store.set(userId, updatedSession);
    return updatedSession;
  }
}

import type { IClientUserContextManager } from './IClientUserContextManager';
import type { UserContext, SessionContext, WmsUserInfo, Facility, UserPreferences } from '../clientUserContext';
export class DefaultClientUserContextManager implements IClientUserContextManager {
  private static instance: DefaultClientUserContextManager;
  private sessionStore: ClientSessionStore;

  // 本地存储键
  private readonly STORAGE_KEYS = {
    ACCESS_TOKEN: 'iam_access_token',
    WMS_USER_INFO: 'wms_user_info',
    CURRENT_FACILITY: 'wms_current_facility',
    IAM_USER_INFO: 'iam_user_info'
  };

  protected constructor() {
    this.sessionStore = new ClientSessionStore();
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): DefaultClientUserContextManager {
    if (!DefaultClientUserContextManager.instance) {
      DefaultClientUserContextManager.instance = new DefaultClientUserContextManager();
    }
    return DefaultClientUserContextManager.instance;
  }

  /**
   * 获取授权令牌
   * 集中管理令牌获取逻辑
   */
  getAuthToken(): string | null {
    return localStorage.getItem(this.STORAGE_KEYS.ACCESS_TOKEN);
  }

  /**
   * 设置授权令牌
   */
  setAuthToken(token: string): void {
    localStorage.setItem(this.STORAGE_KEYS.ACCESS_TOKEN, token);
  }

  /**
   * 清除授权令牌
   */
  clearAuthToken(): void {
    localStorage.removeItem(this.STORAGE_KEYS.ACCESS_TOKEN);
  }

  /**
   * 获取标准的带认证的请求头
   * 用于API请求时统一添加认证信息
   */
  getAuthHeaders(): Record<string, string> {
    const token = this.getAuthToken();

    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };

    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    return headers;
  }

  /**
   * 初始化用户会话
   * 在用户登录时调用
   */
  async initUserSession(
    userId: string,
    username: string,
    email?: string,
    tenant?: string,
    facility?: string
  ): Promise<UserContext> {
    // 首先清除可能存在的旧会话
    this.clearUserSession(userId);

    // 创建会话上下文
    const sessionContext: SessionContext = {
      userId,
      username,
      email,
      currentTenant: tenant,
      currentFacility: facility,
      lastActivity: Date.now()
    };

    // 保存到会话存储
    this.sessionStore.set(userId, sessionContext);

    // 尝试从localStorage恢复WMS用户信息
    const storedWmsInfo = this.restoreWmsUserInfoFromStorage(userId);
    if (storedWmsInfo) {

      this.sessionStore.update(userId, {
        wmsUserInfo: storedWmsInfo,
        currentTenant: storedWmsInfo.tenantId,
        currentFacility: storedWmsInfo.currentFacility?.id
      });
    }

    // 客户端不直接管理用户偏好，返回空对象
    const preferences: UserPreferences = {};

    return {
      session: sessionContext,
      preferences
    };
  }

  /**
   * 添加WMS用户信息到会话
   */
  async addWmsUserInfo(userId: string, wmsUserInfo: WmsUserInfo): Promise<boolean> {
    try {
      // 获取会话数据
      const sessionData = this.sessionStore.get(userId);
      if (!sessionData) {
        console.error('Unable to add WMS user info: user session does not exist');
        return false;
      }

      console.log('添加WMS用户信息:', {
        userId,
        facilitiesCount: wmsUserInfo.facilities.length,
        hasCurrentFacility: !!wmsUserInfo.currentFacility
      });

      // 更新会话数据
      const updatedSession = this.sessionStore.update(userId, {
        wmsUserInfo,
        currentTenant: wmsUserInfo.tenantId,
        currentFacility: wmsUserInfo.currentFacility?.id
      });

      // 保存到localStorage以便新标签页恢复
      this.saveWmsUserInfoToStorage(wmsUserInfo);

      return !!updatedSession;
    } catch (error) {
      console.error('添加WMS用户信息失败:', error);
      return false;
    }
  }

  /**
   * 设置当前使用的设施
   */
  setCurrentFacility(userId: string, facilityId: string): boolean {
    try {
      // 获取会话数据
      const sessionData = this.sessionStore.get(userId);
      if (!sessionData || !sessionData.wmsUserInfo) {
        console.error('无法设置当前设施：用户会话不存在或WMS信息不存在');
        return false;
      }

      console.log('设置当前设施:', {
        userId,
        facilityId,
        availableFacilities: sessionData.wmsUserInfo.facilities.length
      });

      // 查找设施
      const facility = sessionData.wmsUserInfo.facilities.find(f => f.id === facilityId);
      if (!facility) {
        console.error('无法设置当前设施：设施不存在:', facilityId);
        console.log('可用设施:', sessionData.wmsUserInfo.facilities);
        return false;
      }

      // 更新WMS用户信息中的当前设施
      const updatedWmsUserInfo = {
        ...sessionData.wmsUserInfo,
        currentFacility: facility
      };

      // 更新会话数据
      const updatedSession = this.sessionStore.update(userId, {
        wmsUserInfo: updatedWmsUserInfo,
        currentFacility: facilityId
      });

      // 更新localStorage中的WMS用户信息
      this.saveWmsUserInfoToStorage(updatedWmsUserInfo);

      // 清除缓存，确保下次调用时获取最新值
      const GLOBAL_FACILITY_KEY = '_global_facility_id_';
      const GLOBAL_TENANT_KEY = '_global_tenant_id_';
      this.sessionStore.clearUserSession(GLOBAL_FACILITY_KEY);
      this.sessionStore.clearUserSession(GLOBAL_TENANT_KEY);

      console.log('成功设置当前设施:', facility.name);

      return !!updatedSession;
    } catch (error) {
      console.error('设置当前设施失败:', error);
      return false;
    }
  }

  /**
   * 获取当前用户的所有设施
   */
  getUserFacilities(userId: string): Facility[] {
    const sessionData = this.sessionStore.get(userId);
    if (!sessionData || !sessionData.wmsUserInfo) {
      console.warn('获取用户设施失败: 会话不存在或WMS信息不存在');

      // 尝试从localStorage恢复
      const storedWmsInfo = this.restoreWmsUserInfoFromStorage(userId);
      if (storedWmsInfo) {
        // 如果在会话中不存在但在localStorage中存在，则恢复到会话
        if (sessionData) {
          this.sessionStore.update(userId, {
            wmsUserInfo: storedWmsInfo,
            currentTenant: storedWmsInfo.tenantId,
            currentFacility: storedWmsInfo.currentFacility?.id
          });
          console.log('已从localStorage恢复WMS用户信息');
          return storedWmsInfo.facilities || [];
        }
      }

      return [];
    }

    const facilities = sessionData.wmsUserInfo.facilities || [];
    console.log(`获取用户设施: 用户${userId}有${facilities.length}个设施`);
    return facilities;
  }

  /**
   * 获取用户的当前设施
   */
  getCurrentFacility(userId: string): Facility | undefined {
    const sessionData = this.sessionStore.get(userId);
    if (!sessionData || !sessionData.wmsUserInfo) {
      console.warn('获取当前设施失败: 会话不存在或WMS信息不存在');

      // 尝试从localStorage恢复
      const storedWmsInfo = this.restoreWmsUserInfoFromStorage(userId);
      if (storedWmsInfo && storedWmsInfo.currentFacility) {
        // 如果在会话中不存在但在localStorage中存在，则恢复到会话
        if (sessionData) {
          this.sessionStore.update(userId, {
            wmsUserInfo: storedWmsInfo,
            currentTenant: storedWmsInfo.tenantId,
            currentFacility: storedWmsInfo.currentFacility.id
          });
          console.log('已从localStorage恢复当前设施:', storedWmsInfo.currentFacility.name);
          return storedWmsInfo.currentFacility;
        }
      } else {
        // 尝试单独从localStorage恢复当前设施ID
        try {
          const currentFacilityId = localStorage.getItem(this.STORAGE_KEYS.CURRENT_FACILITY);
          if (currentFacilityId && sessionData && sessionData.wmsUserInfo) {
            const facility = sessionData.wmsUserInfo.facilities.find((f: Facility) => f.id === currentFacilityId);
            if (facility) {
              const updatedWmsUserInfo = {
                ...sessionData.wmsUserInfo,
                currentFacility: facility
              };
              this.sessionStore.update(userId, {
                wmsUserInfo: updatedWmsUserInfo,
                currentFacility: facility.id
              });
              console.log('已从localStorage恢复当前设施ID:', facility.name);
              return facility;
            }
          }
        } catch (error) {
          console.error('恢复当前设施ID失败:', error);
        }
      }

      return undefined;
    }

    const facility = sessionData.wmsUserInfo.currentFacility;
    if (facility) {
      console.log(`获取当前设施: 用户${userId}的当前设施是${facility.name}`);
    } else {
      console.warn(`获取当前设施: 用户${userId}没有当前设施`);
    }

    return facility;
  }

  /**
   * 获取用户上下文
   */
  async getUserContext(userId: string): Promise<UserContext | null> {
    // 获取会话数据
    const sessionData = this.sessionStore.get(userId);
    if (!sessionData) {
      return null; // 用户未登录或会话已过期
    }

    // 更新最后活动时间
    this.sessionStore.update(userId, { lastActivity: Date.now() });

    // 客户端不直接管理用户偏好，返回空对象
    const preferences: UserPreferences = {};

    return {
      session: sessionData,
      preferences
    };
  }

  /**
   * 更新会话上下文
   */
  updateSessionContext(userId: string, updates: Partial<SessionContext>): SessionContext | undefined {
    return this.sessionStore.update(userId, updates);
  }

  /**
   * 清除用户会话
   */
  clearUserSession(userId: string): void {
    this.sessionStore.clearUserSession(userId);
  }

  /**
   * 从localStorage恢复WMS用户信息
   * 在新标签页打开时调用
   */
  private restoreWmsUserInfoFromStorage(userId: string): WmsUserInfo | null {
    try {
      console.log(`尝试从localStorage恢复用户${userId}的WMS信息`);

      // 检查是否有WMS用户信息
      const storedData = localStorage.getItem(this.STORAGE_KEYS.WMS_USER_INFO);
      if (!storedData) {
        console.log('localStorage中没有找到WMS用户信息');

        // 检查是否有当前设施信息，如果有，可能需要单独获取WMS数据
        const currentFacilityId = localStorage.getItem(this.STORAGE_KEYS.CURRENT_FACILITY);
        if (currentFacilityId) {
          console.log(`找到当前设施ID: ${currentFacilityId}，但缺少完整WMS用户信息`);
        }

        return null;
      }

      // 解析存储的数据
      const wmsUserData = JSON.parse(storedData);
      console.log('从localStorage解析到WMS数据:', {
        dataId: wmsUserData.id,
        userId,
        hasFacilities: Array.isArray(wmsUserData.facilities) && wmsUserData.facilities.length > 0,
        facilitiesCount: Array.isArray(wmsUserData.facilities) ? wmsUserData.facilities.length : 0,
        hasCurrentFacility: !!wmsUserData.currentFacility
      });

      // 更灵活的用户ID匹配 - 支持字符串ID和数字ID的兼容
      // 不再检查精确匹配，因为ID可能在不同上下文中有不同的表示形式（数字或字符串）
      // 主要目标是使用户能够保留他们的设施选择，而不是因为ID格式不同而重置
      // 我们假设localStorage中的数据属于当前用户，因为它们在同一个浏览器会话中

      // 验证设施数据的完整性
      if (!Array.isArray(wmsUserData.facilities) || wmsUserData.facilities.length === 0) {
        console.warn('从localStorage恢复的WMS用户信息缺少设施数据');
        return null;
      }

      console.log(`成功从localStorage恢复WMS用户信息，包含${wmsUserData.facilities.length}个设施`);

      // 确保currentFacility存在，如果设置了但没有匹配的设施，尝试重新匹配
      if (!wmsUserData.currentFacility && wmsUserData.facilities.length > 0) {
        const currentFacilityId = localStorage.getItem(this.STORAGE_KEYS.CURRENT_FACILITY);
        if (currentFacilityId) {
          const facility = wmsUserData.facilities.find((f: Facility) => f.id === currentFacilityId);
          if (facility) {
            console.log(`根据存储的设施ID匹配到当前设施: ${facility.name}`);
            wmsUserData.currentFacility = facility;
          }
        }

        // 如果仍然没有当前设施，使用第一个
        if (!wmsUserData.currentFacility) {
          wmsUserData.currentFacility = wmsUserData.facilities[0];
          console.log(`未找到当前设施，使用第一个设施: ${wmsUserData.currentFacility.name}`);
        }
      }

      // 确保wmsUserData.id与当前userId保持一致，以保证后续操作正常
      wmsUserData.id = userId;

      return wmsUserData;
    } catch (error) {
      console.error('从localStorage恢复WMS用户信息失败:', error);
      // 如果解析失败，清除可能已损坏的数据
      localStorage.removeItem(this.STORAGE_KEYS.WMS_USER_INFO);
      return null;
    }
  }

  /**
   * 保存WMS用户信息到localStorage
   */
  private saveWmsUserInfoToStorage(wmsUserInfo: WmsUserInfo): void {
    try {
      // 确保数据有效
      if (!wmsUserInfo || !wmsUserInfo.id) {
        console.error('尝试保存无效的WMS用户信息');
        return;
      }

      // 确保设施数据完整
      if (!Array.isArray(wmsUserInfo.facilities)) {
        console.error('WMS用户信息缺少设施数组');
        return;
      }

      // 创建简化的设施对象以避免可能的循环引用
      const simplifiedFacilities = wmsUserInfo.facilities.map(facility => ({
        id: facility.id,
        name: facility.name,
        code: facility.code
      }));

      // 对当前设施也执行相同的操作
      let simplifiedCurrentFacility = null;
      if (wmsUserInfo.currentFacility) {
        simplifiedCurrentFacility = {
          id: wmsUserInfo.currentFacility.id,
          name: wmsUserInfo.currentFacility.name,
          code: wmsUserInfo.currentFacility.code
        };
      }

      // 确保存储的数据不包含过大的对象，使用简化的设施对象
      const wmsUserInfoForStorage = {
        // 确保始终存储字符串形式的ID，防止字符串/数字类型不匹配
        id: String(wmsUserInfo.id),
        username: wmsUserInfo.username,
        fullName: wmsUserInfo.fullName,
        email: wmsUserInfo.email,
        tenantId: wmsUserInfo.tenantId,
        facilities: simplifiedFacilities,
        currentFacility: simplifiedCurrentFacility,
        roles: wmsUserInfo.roles
      };

      // 尝试序列化以验证对象
      const jsonString = JSON.stringify(wmsUserInfoForStorage);

      // 保存到localStorage
      localStorage.setItem(this.STORAGE_KEYS.WMS_USER_INFO, jsonString);
      console.log(`WMS用户信息已保存到localStorage，包含${simplifiedFacilities.length}个设施`);

      // 同时保存当前设施ID
      if (wmsUserInfo.currentFacility) {
        localStorage.setItem(this.STORAGE_KEYS.CURRENT_FACILITY, wmsUserInfo.currentFacility.id);
        console.log('当前设施已保存到localStorage:', wmsUserInfo.currentFacility.name);
      }
    } catch (error) {
      console.error('保存WMS用户信息到localStorage失败:', error);

      // 尝试识别常见的序列化错误
      if (error instanceof TypeError && error.message.includes('circular')) {
        console.error('检测到循环引用。请确保对象没有循环引用');
      }

      try {
        // 尝试保存最小的必要信息 - 只有设施ID和名称
        if (wmsUserInfo && wmsUserInfo.facilities) {
          const minimalData = {
            id: String(wmsUserInfo.id), // 确保始终存储字符串ID
            facilities: wmsUserInfo.facilities.map(f => ({ id: f.id, name: f.name, code: f.code })),
            currentFacility: wmsUserInfo.currentFacility ?
              { id: wmsUserInfo.currentFacility.id, name: wmsUserInfo.currentFacility.name, code: wmsUserInfo.currentFacility.code } :
              null
          };
          localStorage.setItem(this.STORAGE_KEYS.WMS_USER_INFO, JSON.stringify(minimalData));
          console.log('保存了简化版的WMS用户信息');
        }
      } catch (fallbackError) {
        console.error('保存简化的WMS用户数据也失败:', fallbackError);
      }
    }
  }

  /**
   * 获取用户会话数据
   */
  getUserSession(userId: string): SessionContext | undefined {
    return this.sessionStore.get(userId);
  }

  /**
   * 更新WMS用户信息
   */
  updateWmsUserInfo(userId: string, wmsUserInfo: WmsUserInfo): boolean {
    try {
      // 获取会话数据
      const sessionData = this.sessionStore.get(userId);
      if (!sessionData) {
        console.error('无法更新WMS用户信息：用户会话不存在');
        return false;
      }

      console.log('更新WMS用户信息:', {
        userId,
        facilitiesCount: wmsUserInfo.facilities.length,
        hasCurrentFacility: !!wmsUserInfo.currentFacility
      });

      // 更新会话数据
      const updatedSession = this.sessionStore.update(userId, {
        wmsUserInfo,
        currentTenant: wmsUserInfo.tenantId,
        currentFacility: wmsUserInfo.currentFacility?.id
      });

      // 清除全局缓存，确保下次调用时获取最新值
      const GLOBAL_FACILITY_KEY = '_global_facility_id_';
      const GLOBAL_TENANT_KEY = '_global_tenant_id_';
      const GLOBAL_WMS_KEY = '_global_wms_data_';
      this.sessionStore.clearUserSession(GLOBAL_FACILITY_KEY);
      this.sessionStore.clearUserSession(GLOBAL_TENANT_KEY);
      this.sessionStore.clearUserSession(GLOBAL_WMS_KEY);

      console.log('已更新WMS用户信息并清除全局缓存');

      return !!updatedSession;
    } catch (error) {
      console.error('更新WMS用户信息失败:', error);
      return false;
    }
  }

  /**
   * 获取WMS用户信息
   * 优先从会话存储中获取，没有则从localStorage读取并缓存到会话存储
   * 不依赖于会话数据，可在任何地方直接调用
   */
  getWmsUserInfo(): any | null {
    try {
      // 创建一个特殊的会话键来存储全局WMS数据
      const GLOBAL_WMS_KEY = '_global_wms_data_';

      // 首先尝试从会话存储中获取
      const sessionData = this.sessionStore.get(GLOBAL_WMS_KEY);
      if (sessionData && sessionData.wmsUserInfo) {
        // 如果会话存储中有数据，直接返回
        return sessionData.wmsUserInfo;
      }

      // 如果会话存储中没有，从localStorage获取
      const storedData = localStorage.getItem(this.STORAGE_KEYS.WMS_USER_INFO);
      if (!storedData) {
        console.log('localStorage中没有找到WMS用户信息');
        return null;
      }

      // 解析存储的数据
      const wmsUserData = JSON.parse(storedData);

      // 减少日志输出，只在开发环境下输出
      if (process.env.NODE_ENV === 'development') {
        console.log('从localStorage获取WMS数据成功，已缓存到会话存储', {
          hasFacilities: Array.isArray(wmsUserData.facilities) && wmsUserData.facilities.length > 0,
          facilitiesCount: Array.isArray(wmsUserData.facilities) ? wmsUserData.facilities.length : 0,
          hasCurrentFacility: !!wmsUserData.currentFacility,
          tenantId: wmsUserData.tenantId
        });
      }

      // 将数据存入会话存储以便后续使用
      this.sessionStore.set(GLOBAL_WMS_KEY, {
        userId: GLOBAL_WMS_KEY,
        username: 'global',
        lastActivity: Date.now(),
        wmsUserInfo: wmsUserData
      });

      return wmsUserData;
    } catch (error) {
      console.error('获取WMS用户信息失败:', error);
      return null;
    }
  }

  /**
   * 获取指定用户的WMS用户信息，优先从会话中获取，没有则从localStorage恢复
   */
  getWmsUserInfoByUserId(userId: string): WmsUserInfo | null {
    // 优先从会话中获取
    const sessionData = this.sessionStore.get(userId);
    if (sessionData && sessionData.wmsUserInfo) {
      return sessionData.wmsUserInfo;
    }

    // 如果会话中没有，尝试从localStorage恢复
    return this.restoreWmsUserInfoFromStorage(userId);
  }

  /**
   * 获取当前租户ID
   * 优先从会话存储中获取，没有则从WMS用户信息中获取并缓存到会话存储
   */
  getCurrentTenantId(): string | null {
    // 创建一个特殊的会话键来存储当前租户ID
    const GLOBAL_TENANT_KEY = '_global_tenant_id_';

    // 首先尝试从会话存储中获取
    const sessionData = this.sessionStore.get(GLOBAL_TENANT_KEY);
    if (sessionData && sessionData.currentTenant) {
      return sessionData.currentTenant;
    }

    // 从WMS用户信息中获取
    const wmsUserInfo = this.getWmsUserInfo();
    const tenantId = wmsUserInfo?.tenantId || null;

    if (tenantId) {
      // 缓存到会话存储
      this.sessionStore.set(GLOBAL_TENANT_KEY, {
        userId: GLOBAL_TENANT_KEY,
        username: 'global',
        lastActivity: Date.now(),
        currentTenant: tenantId
      });
    }

    return tenantId;
  }

  /**
   * 获取当前设施ID
   * 优先从会话存储中获取，没有则从localStorage读取并缓存到会话存储
   */
  getCurrentFacilityId(): string | null {
    // 创建一个特殊的会话键来存储当前设施ID
    const GLOBAL_FACILITY_KEY = '_global_facility_id_';

    // 首先尝试从会话存储中获取
    const sessionData = this.sessionStore.get(GLOBAL_FACILITY_KEY);
    if (sessionData && sessionData.currentFacility) {
      return sessionData.currentFacility;
    }

    
    // 先从WMS用户信息中获取
    const wmsUserInfo = this.getWmsUserInfo();
    if (wmsUserInfo?.currentFacility?.id) {
      // 缓存到会话存储
      this.sessionStore.set(GLOBAL_FACILITY_KEY, {
        userId: GLOBAL_FACILITY_KEY,
        username: 'global',
        lastActivity: Date.now(),
        currentFacility: wmsUserInfo.currentFacility.id
      });

      return wmsUserInfo.currentFacility.id;
    }

    // 如果WMS用户信息中没有，则从单独存储的当前设施ID获取
    const facilityId = localStorage.getItem(this.STORAGE_KEYS.CURRENT_FACILITY);

    if (facilityId) {
      // 缓存到会话存储
      this.sessionStore.set(GLOBAL_FACILITY_KEY, {
        userId: GLOBAL_FACILITY_KEY,
        username: 'global',
        lastActivity: Date.now(),
        currentFacility: facilityId
      });
    }

    return facilityId;
  }

  /**
   * 获取IAM用户信息
   * 优先从会话存储中获取，没有则从localStorage读取并缓存到会话存储
   * 包含用户角色信息
   */
  getIAMUserInfo(): any | null {
    try {
      // 创建一个特殊的会话键来存储全局IAM数据
      const GLOBAL_IAM_KEY = '_global_iam_data_';

      // 首先尝试从会话存储中获取
      const sessionData = this.sessionStore.get(GLOBAL_IAM_KEY);
      if (sessionData && sessionData.iamUserInfo) {
        // 如果会话存储中有数据，直接返回
        return sessionData.iamUserInfo;
      }

      // 如果会话存储中没有，从localStorage获取
      const storedData = localStorage.getItem(this.STORAGE_KEYS.IAM_USER_INFO);
      if (!storedData) {
        return null;
      }

      // 解析存储的数据
      const iamUserData = JSON.parse(storedData);

      // 将数据存入会话存储以便后续使用
      this.sessionStore.set(GLOBAL_IAM_KEY, {
        userId: GLOBAL_IAM_KEY,
        username: 'global',
        lastActivity: Date.now(),
        iamUserInfo: iamUserData
      });

      return iamUserData;
    } catch (error) {
      console.error('获取IAM用户信息失败:', error);
      return null;
    }
  }

  /**
   * 获取用户角色名称
   * 从IAM用户信息中获取角色名称
   */
  getUserRoleNames(): string[] {
    try {
      const iamUserInfo = this.getIAMUserInfo();
      if (!iamUserInfo || !Array.isArray(iamUserInfo.userRoles)) {
        return [];
      }

      // 从userRoles中提取角色名称
      const roleNames = iamUserInfo.userRoles.map((role: any) => {
        if (typeof role === 'string') return role;
        if (role && typeof role === 'object' && role.name) return role.name;
        return null;
      }).filter(Boolean);

      return roleNames;
    } catch (error) {
      console.error('获取用户角色名称失败:', error);
      return [];
    }
  }

  getBiToken(): string | null {
    const biUserInfo = localStorage.getItem('bi_user_info');
    if (biUserInfo) {
      const parsed = JSON.parse(biUserInfo);
      return parsed.idToken;
    }
    return null;
  }
}