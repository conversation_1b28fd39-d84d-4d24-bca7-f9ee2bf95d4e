import { NextRequest, NextResponse } from 'next/server';
import { getUserIdFromRequest } from '@/utils/authUtils';
import { S3Storage, LocalFileStorage, StorageConfig, getStorageConfig } from '@/utils/storage';

// 获取聊天历史索引
export async function GET(req: NextRequest) {
  try {
    console.log('GET 请求: 获取聊天历史索引');
    
    // 获取当前用户ID
    const userId = await getUserIdFromRequest(req);
    
    // 如果未登录，返回错误
    if (!userId) {
      console.log('未获取到用户ID，返回401错误');
      return NextResponse.json(
        { error: '用户未登录或会话已过期' },
        { status: 401 }
      );
    }
    
    console.log('使用用户ID:', userId);
    
    // 获取存储配置和实例
    const config = getStorageConfig();
    let storage;
    
    console.log('存储类型:', config.type);
    
    if (config.type === 's3') {
      console.log('创建S3存储实例');
      storage = new S3Storage(config);
    } else {
      console.log('创建本地文件存储实例');
      storage = new LocalFileStorage(config);
    }
    
    // 获取用户的聊天历史索引
    console.log('开始获取用户聊天历史索引...');
    try {
      const chatIndexItems = await storage.getUserChatHistoryIndex(userId);
      console.log(`成功获取到 ${chatIndexItems.length} 条聊天索引`);
      return NextResponse.json(chatIndexItems);
    } catch (indexError) {
      console.error('获取聊天历史索引API异常:', indexError);
      throw indexError;
    }
  } catch (error) {
    console.error('获取聊天历史索引失败:', error);
    return NextResponse.json(
      { error: '获取聊天历史索引失败', details: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    );
  }
} 