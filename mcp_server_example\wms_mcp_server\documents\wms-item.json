{"openapi": "3.0.1", "info": {"title": "Item-Master", "description": "", "version": "1.0.0"}, "tags": [], "paths": {"/wms-bam/item/search-by-paging": {"post": {"summary": "Search Item By Paging", "deprecated": false, "description": "", "tags": [], "parameters": [{"name": "X-Tenant-id", "in": "header", "description": "", "example": "CO1", "schema": {"type": "string", "default": "CO1"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ItemQuery", "description": ""}, "example": {"currentPage": 1, "pageSize": 30}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RPageResultItemDto", "description": ""}, "examples": {"1": {"summary": "成功示例", "value": {"code": 0, "msg": "", "success": false, "data": {"data": [{}], "totalCount": 0, "currentPage": 0, "pageSize": 0, "totalPage": 0}}}, "2": {"summary": "成功示例", "value": {"code": 0, "msg": "", "success": false, "data": {"list": [{}], "totalCount": 0, "currentPage": 0, "pageSize": 0, "totalPage": 0}}}}}}, "headers": {}}}, "security": []}}, "/wms-bam/item-uom/search": {"post": {"summary": "Search Item UOM", "deprecated": false, "description": "", "tags": [], "parameters": [{"name": "X-Tenant-id", "in": "header", "description": "", "example": "CO1", "schema": {"type": "string", "default": "CO1"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ItemUomQuery", "description": ""}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RListItemUomDto", "description": ""}, "examples": {"1": {"summary": "成功示例", "value": {"code": 0, "msg": "", "success": false, "data": [{"id": 0, "itemId": "", "uomId": "", "name": "", "dualUomType": "", "qty": 0, "baseQty": 0, "insideUomId": 0, "isDefaultUom": false, "isBaseUom": false, "length": 0, "width": 0, "height": 0, "linearUom": "", "weight": 0, "weightUom": "", "channel": "", "status": "", "isOversizePackage": false, "volume": 0, "volumeUom": "", "createdBy": "", "createdTime": "", "updatedBy": "", "updatedTime": "", "billingUOM": ""}]}}}}}, "headers": {}}}, "security": []}}}, "components": {"schemas": {"ItemDto": {"type": "object", "properties": {"tags": {"type": "array", "items": {"type": "string", "enum": ["MATERIAL", "PRODUCT", "ASSET", "SAMPLE"]}, "description": "", "title": "标签集合"}, "variantPropertyIds": {"type": "array", "items": {"type": "integer"}, "description": ""}, "hasSerialNumber": {"type": "boolean", "description": "", "default": false, "title": "有序列号"}, "hasRFID": {"type": "boolean", "description": "", "title": "是否是RFID"}, "requireCollectSnOnShipping": {"type": "boolean", "description": "", "default": false, "title": "发货时需要采集序列号"}, "isKitting": {"type": "boolean", "description": "", "default": false}, "isSpu": {"type": "boolean", "description": ""}, "enableDualUom": {"type": "boolean", "description": "", "title": "启用双计量单位"}, "kitItemFulfillBy": {"type": "string", "description": "", "enum": ["BY_KIT", "BY_COMPONENT"], "title": "套件项目履行方式"}, "grade": {"type": "string", "description": "", "title": "等级"}, "billingGrade": {"type": "string", "description": "", "title": "计费等级"}, "freightClass": {"type": "string", "description": "", "title": "货运分类"}, "countLevel": {"type": "string", "description": "", "title": "计数级别"}, "nmfc": {"type": "string", "description": "", "title": "NMFC"}, "commodityCode": {"type": "string", "description": "", "title": "商品代码"}, "commodityDescription": {"type": "string", "description": "", "title": "商品描述"}, "serialNoLength": {"type": "number", "description": "", "title": "序列号长度"}, "serialNoValidationRule": {"type": "string", "description": ""}, "validationInboundSerialNo": {"type": "boolean", "description": "", "default": false, "title": "入库序列号验证"}, "validationOutboundSerialNo": {"type": "boolean", "description": "", "default": false, "title": "出库序列号验证"}, "labels": {"type": "array", "items": {"type": "string"}, "description": "", "title": "标识集合"}, "isHazardousMaterial": {"type": "boolean", "description": "", "default": false, "title": "是否为危险品"}, "countryOfOrigin": {"type": "string", "description": ""}, "shippingRule": {"type": "string", "description": "", "enum": ["FIFO", "FEFO", "LIFO", "LSFO", "LEFO"], "title": "运输规则"}, "storageDays": {"type": "integer", "description": ""}, "shipAllowDays": {"type": "integer", "description": "", "title": "允许运输天数"}, "isUnknownItem": {"type": "boolean", "description": "", "title": "是否为未知物品"}, "billingMaterial": {"type": "string", "description": "", "title": "计费材料"}, "id": {"type": "string", "description": "ITEM-XXX", "title": "内部ID"}, "name": {"type": "string", "description": "", "title": "商品编码/名称"}, "code": {"type": "string", "description": ""}, "casePackQty": {"type": "number", "description": ""}, "description": {"type": "string", "description": "", "title": "描述"}, "customerId": {"type": "string", "description": "", "title": "客户ID"}, "supplierIds": {"type": "array", "items": {"type": "string"}, "description": "", "title": "供应商ID集合"}, "brandId": {"type": "string", "description": "", "title": "品牌ID"}, "groupIds": {"type": "array", "items": {"type": "integer"}, "description": "", "title": "分组ID集合"}, "status": {"type": "string", "description": "", "enum": ["ACTIVE", "INACTIVE", "PENDING", "ORDER_ONLY", "DRAFT", "ARCHIVED", "DISCONTINUE"], "title": "状态"}, "fileIds": {"type": "array", "items": {"type": "string"}, "description": "", "title": "文件ID集合"}, "requireCollectLotNoOnReceive": {"type": "boolean", "description": "", "default": false, "title": "收货时需要采集批次号"}, "requireCollectExpirationDateOnReceive": {"type": "boolean", "description": "", "default": false, "title": "收货时需要采集到期日期"}, "requireCollectMfgDateOnReceive": {"type": "boolean", "description": "", "default": false, "title": "收货时需要采集生产日期"}, "requireCollectShelfLifeDaysOnReceive": {"type": "boolean", "description": "", "default": false, "title": "收货时需要采集保质期天数"}, "requireCollectSnOnReceive": {"type": "boolean", "description": "", "title": "收货时需要采集序列号"}, "channel": {"type": "string", "description": ""}, "source": {"type": "string", "description": ""}, "itemCondition": {"type": "string", "description": ""}, "color": {"type": "string", "description": ""}, "ingredient": {"type": "string", "description": ""}, "expirationDate": {"type": "string", "description": ""}, "hasLotNo": {"type": "boolean", "description": ""}, "requireCollectLotNoOnShipping": {"type": "boolean", "description": ""}, "createdBy": {"type": "string", "description": "", "title": "创建者"}, "createdTime": {"type": "string", "description": ""}, "updatedBy": {"type": "string", "description": "", "title": "更新者"}, "updatedTime": {"type": "string", "description": ""}, "itemHazardId": {"type": "string", "description": "", "title": "物品危险品ID"}, "shortDescription": {"type": "string", "description": "", "title": "简短描述"}, "abbreviation": {"type": "string", "description": "", "title": "缩写"}, "upcCode": {"type": "string", "description": "", "title": "UPC代码"}, "upcCodeCase": {"type": "string", "description": "", "title": "UPC箱码"}, "eanCode": {"type": "string", "description": "", "title": "EAN代码"}, "allowOverWriteItem": {"type": "boolean", "description": "", "default": false, "title": "允许覆盖物品"}, "allowMixedPackagingForSmallParcel": {"type": "boolean", "description": "", "default": false, "title": "允许小包裹混合包装"}, "stack": {"type": "string", "description": "", "title": "堆叠"}, "requireDeductMaterialInventory": {"type": "boolean", "description": "", "title": "需要扣减材料库存"}, "smallParcelPackaging": {"type": "string", "description": "", "title": "小包裹包装"}, "packagingMaterial": {"type": "string", "description": "", "pattern": "^ITEM-[0-9]+", "title": "包装材料"}, "hasAttachment": {"type": "boolean", "description": "", "title": "有附件"}, "categoryId": {"type": "integer", "description": "", "title": "分类ID"}, "imageFileIds": {"type": "array", "items": {"type": "string"}, "description": "", "title": "图片文件ID集合"}, "parentId": {"type": "string", "description": "", "title": "父ID"}, "carrier": {"type": "string", "description": ""}, "deliveryService": {"type": "string", "description": ""}, "dynTxtPropertyValue01": {"type": "string", "description": "", "title": "动态文本属性值"}, "dynTxtPropertyValue02": {"type": "string", "description": "", "title": "动态文本属性值"}, "dynTxtPropertyValue03": {"type": "string", "description": "", "title": "动态文本属性值"}, "dynTxtPropertyValue04": {"type": "string", "description": "", "title": "动态文本属性值"}, "dynTxtPropertyValue05": {"type": "string", "description": "", "title": "动态文本属性值"}, "dynTxtPropertyValue06": {"type": "string", "description": "", "title": "动态文本属性值"}, "dynTxtPropertyValue07": {"type": "string", "description": "", "title": "动态文本属性值"}, "dynTxtPropertyValue08": {"type": "string", "description": "", "title": "动态文本属性值"}, "dynTxtPropertyValue09": {"type": "string", "description": "", "title": "动态文本属性值"}, "dynTxtPropertyValue10": {"type": "string", "description": "", "title": "动态文本属性值"}, "dynTxtPropertyValue11": {"type": "string", "description": "", "title": "动态文本属性值"}, "dynTxtPropertyValue12": {"type": "string", "description": "", "title": "动态文本属性值"}, "dynTxtPropertyValue13": {"type": "string", "description": "", "title": "动态文本属性值"}, "dynTxtPropertyValue14": {"type": "string", "description": "", "title": "动态文本属性值"}, "dynTxtPropertyValue15": {"type": "string", "description": "", "title": "动态文本属性值"}, "dynTxtPropertyValue16": {"type": "string", "description": "", "title": "动态文本属性值"}, "dynTxtPropertyValue17": {"type": "string", "description": "", "title": "动态文本属性值"}, "dynTxtPropertyValue18": {"type": "string", "description": "", "title": "动态文本属性值"}, "dynTxtPropertyValue19": {"type": "string", "description": "", "title": "动态文本属性值"}, "dynTxtPropertyValue20": {"type": "string", "description": "", "title": "动态文本属性值"}}}, "SortingField": {"type": "object", "properties": {"field": {"type": "string", "description": ""}, "orderBy": {"type": "string", "description": "", "enum": ["NONE", "ASC", "DESC"]}}}, "ItemQuery": {"type": "object", "properties": {"currentPage": {"type": "integer", "description": ""}, "pageSize": {"type": "integer", "description": ""}, "sortingFields": {"type": "array", "items": {"$ref": "#/components/schemas/SortingField", "description": "com.item.xms.persistence.query.SortingField"}, "description": ""}, "isKitting": {"type": "boolean", "description": ""}, "isSpu": {"type": "boolean", "description": ""}, "tags": {"type": "array", "items": {"type": "string", "enum": ["MATERIAL", "PRODUCT", "ASSET", "SAMPLE"]}, "description": ""}, "labels": {"type": "array", "items": {"type": "string"}, "description": ""}, "isHazardousMaterial": {"type": "boolean", "description": ""}, "countryOfOrigin": {"type": "string", "description": ""}, "shippingRule": {"type": "string", "description": "", "enum": ["FIFO", "FEFO", "LIFO", "LSFO", "LEFO"]}, "isUnknownItem": {"type": "boolean", "description": ""}, "billingMaterials": {"type": "array", "items": {"type": "string"}, "description": ""}, "ids": {"type": "array", "items": {"type": "string"}, "description": ""}, "id": {"type": "string", "description": ""}, "parentId": {"type": "string", "description": ""}, "carrier": {"type": "string", "description": ""}, "deliveryService": {"type": "string", "description": ""}, "parentIdIsNull": {"type": "boolean", "description": ""}, "excludeId": {"type": "string", "description": ""}, "name": {"type": "string", "description": ""}, "code": {"type": "string", "description": ""}, "eqCode": {"type": "string", "description": ""}, "codes": {"type": "array", "items": {"type": "string"}, "description": ""}, "eqName": {"type": "string", "description": ""}, "names": {"type": "array", "items": {"type": "string"}, "description": ""}, "customerIds": {"type": "array", "items": {"type": "string"}, "description": ""}, "customerId": {"type": "string", "description": ""}, "supplierIds": {"type": "array", "items": {"type": "string"}, "description": ""}, "countLevel": {"type": "string", "description": ""}, "brandId": {"type": "string", "description": ""}, "groupIds": {"type": "array", "items": {"type": "integer"}, "description": ""}, "variantPropertyIds": {"type": "array", "items": {"type": "integer"}, "description": ""}, "scenario": {"type": "string", "description": "", "enum": ["AUTO_COMPLETE", "ID"]}, "status": {"type": "string", "description": "", "enum": ["ACTIVE", "INACTIVE", "PENDING", "ORDER_ONLY", "DRAFT", "ARCHIVED", "DISCONTINUE"]}, "source": {"type": "string", "description": ""}, "channel": {"type": "string", "description": ""}, "itemCondition": {"type": "string", "description": ""}, "enableDualUom": {"type": "boolean", "description": ""}, "kitItemFulfillBy": {"type": "string", "description": "", "enum": ["BY_KIT", "BY_COMPONENT"]}, "statuses": {"type": "array", "items": {"type": "string"}, "description": ""}, "columns": {"type": "array", "items": {"type": "string"}, "description": ""}, "nullFields": {"type": "array", "items": {"type": "string"}, "description": ""}, "keyword": {"type": "string", "description": ""}, "itemUpcStatuses": {"type": "array", "items": {"type": "string"}, "description": ""}, "commodityCodes": {"type": "array", "items": {"type": "string"}, "description": ""}, "upcCode": {"type": "string", "description": ""}, "upcCodes": {"type": "array", "items": {"type": "string"}, "description": ""}, "eanCode": {"type": "string", "description": ""}, "isbnCode": {"type": "string", "description": ""}, "requireCollectSnOnShipping": {"type": "boolean", "description": ""}, "abbreviation": {"type": "string", "description": ""}, "upcCodeCase": {"type": "string", "description": ""}, "description": {"type": "string", "description": ""}, "shipAllowDaysGt": {"type": "integer", "description": ""}, "hasAttachment": {"type": "boolean", "description": ""}, "hasSerialNumber": {"type": "boolean", "description": ""}, "hasRFID": {"type": "boolean", "description": ""}, "createdTimeFrom": {"type": "string", "description": ""}, "createdTimeTo": {"type": "string", "description": ""}, "updatedTimeFrom": {"type": "string", "description": ""}, "updatedTimeTo": {"type": "string", "description": ""}, "categoryId": {"type": "integer", "description": ""}, "categoryIds": {"type": "array", "items": {"type": "integer"}, "description": ""}, "dynTxtPropertyValue01": {"type": "string", "description": ""}, "dynTxtPropertyValue02": {"type": "string", "description": ""}, "dynTxtPropertyValue03": {"type": "string", "description": ""}, "dynTxtPropertyValue04": {"type": "string", "description": ""}, "dynTxtPropertyValue05": {"type": "string", "description": ""}, "dynTxtPropertyValue06": {"type": "string", "description": ""}, "dynTxtPropertyValue07": {"type": "string", "description": ""}, "dynTxtPropertyValue08": {"type": "string", "description": ""}, "dynTxtPropertyValue09": {"type": "string", "description": ""}, "dynTxtPropertyValue10": {"type": "string", "description": ""}, "dynTxtPropertyValue11": {"type": "string", "description": ""}, "dynTxtPropertyValue12": {"type": "string", "description": ""}, "dynTxtPropertyValue13": {"type": "string", "description": ""}, "dynTxtPropertyValue14": {"type": "string", "description": ""}, "dynTxtPropertyValue15": {"type": "string", "description": ""}, "dynTxtPropertyValue16": {"type": "string", "description": ""}, "dynTxtPropertyValue17": {"type": "string", "description": ""}, "dynTxtPropertyValue18": {"type": "string", "description": ""}, "dynTxtPropertyValue19": {"type": "string", "description": ""}, "dynTxtPropertyValue20": {"type": "string", "description": ""}, "orIds": {"type": "array", "items": {"type": "string"}, "description": ""}, "orEanCode": {"type": "string", "description": ""}, "orUpcCode": {"type": "string", "description": ""}, "orIsbnCode": {"type": "string", "description": ""}}}, "PageResultItemDto": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/components/schemas/ItemDto", "description": "Created by fanson on 2024/5/24."}, "description": ""}, "totalCount": {"type": "integer", "description": ""}, "currentPage": {"type": "integer", "description": ""}, "pageSize": {"type": "integer", "description": ""}, "totalPage": {"type": "integer", "description": ""}}}, "RPageResultItemDto": {"type": "object", "properties": {"code": {"type": "integer", "description": ""}, "msg": {"type": "string", "description": ""}, "success": {"type": "boolean", "description": ""}, "data": {"$ref": "#/components/schemas/PageResultItemDto", "description": ""}}}, "ItemUomQuery": {"type": "object", "properties": {"currentPage": {"type": "integer", "description": ""}, "pageSize": {"type": "integer", "description": ""}, "sortingFields": {"type": "array", "items": {"$ref": "#/components/schemas/SortingField", "description": "com.item.xms.persistence.query.SortingField"}, "description": ""}, "itemIds": {"type": "array", "items": {"type": "string"}, "description": ""}, "name": {"type": "string", "description": ""}, "dualUomType": {"type": "string", "description": "", "enum": ["PRIMARY", "SECONDARY"]}, "ids": {"type": "array", "items": {"type": "integer"}, "description": ""}, "id": {"type": "integer", "description": ""}, "excludeId": {"type": "integer", "description": ""}, "itemId": {"type": "string", "description": ""}, "insideUomId": {"type": "integer", "description": ""}, "uomId": {"type": "string", "description": ""}, "uomIds": {"type": "array", "items": {"type": "string"}, "description": ""}, "isBaseUom": {"type": "boolean", "description": ""}, "isDefaultUom": {"type": "boolean", "description": ""}, "status": {"type": "string", "description": "", "enum": ["ENABLE", "DISABLE"]}, "createdTimeFrom": {"type": "string", "description": ""}, "createdTimeTo": {"type": "string", "description": ""}, "updatedTimeFrom": {"type": "string", "description": ""}, "updatedTimeTo": {"type": "string", "description": ""}, "scenario": {"type": "string", "description": "", "enum": ["AUTO_COMPLETE", "ID"]}, "isOversizePackage": {"type": "boolean", "description": ""}}}, "ItemUomDto": {"type": "object", "properties": {"id": {"type": "integer", "description": ""}, "itemId": {"type": "string", "description": ""}, "uomId": {"type": "string", "description": ""}, "name": {"type": "string", "description": ""}, "dualUomType": {"type": "string", "description": "", "enum": ["PRIMARY", "SECONDARY"]}, "qty": {"type": "number", "description": "", "minimum": 1e-05}, "baseQty": {"type": "number", "description": "", "minimum": 1e-05}, "insideUomId": {"type": "integer", "description": ""}, "isDefaultUom": {"type": "boolean", "description": ""}, "isBaseUom": {"type": "boolean", "description": "", "default": false}, "length": {"type": "number", "description": ""}, "width": {"type": "number", "description": ""}, "height": {"type": "number", "description": ""}, "linearUom": {"type": "string", "description": ""}, "weight": {"type": "number", "description": ""}, "weightUom": {"type": "string", "description": ""}, "channel": {"type": "string", "description": ""}, "status": {"type": "string", "description": "", "enum": ["ENABLE", "DISABLE"]}, "isOversizePackage": {"type": "boolean", "description": ""}, "volume": {"type": "number", "description": ""}, "volumeUom": {"type": "string", "description": ""}, "createdBy": {"type": "string", "description": ""}, "createdTime": {"type": "string", "description": ""}, "updatedBy": {"type": "string", "description": ""}, "updatedTime": {"type": "string", "description": ""}, "billingUOM": {"type": "string", "description": ""}}}, "RListItemUomDto": {"type": "object", "properties": {"code": {"type": "integer", "description": ""}, "msg": {"type": "string", "description": ""}, "success": {"type": "boolean", "description": ""}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/ItemUomDto", "description": "Created by fanson on 2024/6/3."}, "description": ""}}}}, "securitySchemes": {}}, "servers": []}