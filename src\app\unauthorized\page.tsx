'use client';

import React from 'react';
import Link from 'next/link';
import { Shield, AlertTriangle } from 'lucide-react';

export default function UnauthorizedPage() {
  return (
    <div className="min-h-screen bg-gray-950 bg-[radial-gradient(ellipse_at_center,_var(--tw-gradient-stops))] from-gray-900 via-gray-950 to-black text-cyan-100 flex flex-col items-center justify-center p-4">
      {/* Decorative overlay elements */}
      <div className="absolute top-0 left-0 right-0 h-20 bg-gradient-to-b from-cyan-900/20 to-transparent pointer-events-none z-10"></div>
      <div className="absolute bottom-0 left-0 right-0 h-20 bg-gradient-to-t from-blue-900/20 to-transparent pointer-events-none z-10"></div>

      <div className="bg-gray-800/80 backdrop-blur-sm p-8 rounded-xl border border-blue-700/30 shadow-lg max-w-md w-full relative overflow-hidden">
        <div className="absolute top-0 right-0 w-40 h-40 bg-blue-600/10 rounded-full -translate-y-1/2 translate-x-1/2 blur-2xl pointer-events-none"></div>

        <div className="flex items-center mb-6">
          <div className="bg-blue-900/30 p-3 rounded-lg mr-4">
            <Shield className="h-8 w-8 text-blue-400" />
          </div>
          <h1 className="text-2xl font-bold text-blue-400 font-mono">Access Denied</h1>
        </div>

        <div className="bg-gray-900/50 rounded-lg p-4 mb-6 border border-gray-700/50">
          <div className="flex items-start">
            <AlertTriangle className="h-5 w-5 text-amber-400 mr-2 mt-0.5 flex-shrink-0" />
            <p className="text-gray-300">
              You don't have permission to access this page. If you believe this is an error, please contact your system administrator for appropriate access.
            </p>
          </div>
        </div>

        <div className="flex flex-col sm:flex-row gap-3">
          <Link
            href="/"
            className="inline-block bg-teal-800/80 hover:bg-teal-700/80 text-cyan-100 font-medium py-2 px-4 rounded transition-colors text-center border border-teal-700/30 font-mono"
          >
            Return to Home
          </Link>

          <button
            onClick={() => window.history.back()}
            className="inline-block bg-gray-700/50 hover:bg-gray-600/50 text-gray-200 font-medium py-2 px-4 rounded transition-colors border border-gray-600/30 font-mono"
          >
            Go Back
          </button>
        </div>
      </div>
    </div>
  );
}
