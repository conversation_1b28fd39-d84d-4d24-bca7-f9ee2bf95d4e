'use client';

import React, { useState } from 'react';
import '@/styles/item-design-system.css';

interface KnowledgeCardProps {
  toolInvocation: any;
}

export default function KnowledgeCard({ toolInvocation }: KnowledgeCardProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  // 解析知识库ID
  let kbIds: string[] = [];
  if (toolInvocation?.args?.kbId) {
    if (Array.isArray(toolInvocation.args.kbId)) {
      kbIds = toolInvocation.args.kbId;
    } else if (typeof toolInvocation.args.kbId === 'string') {
      kbIds = [toolInvocation.args.kbId];
    }
  }
  
  const uniqueKBs = Array.from(new Set(kbIds));
  const kbText = uniqueKBs.length > 0 ? uniqueKBs.join(', ') : 'Unknown';

  // 解析引用文档 - 处理不同的数据结构
  let sourcesArr: any[] = [];
  let docList: string[] = [];
  
  if (toolInvocation?.result) {
    if (Array.isArray(toolInvocation.result)) {
      sourcesArr = toolInvocation.result;
      docList = sourcesArr.map((item: any) => item.source_document || item.source || item.document).filter(Boolean);
    } else if (typeof toolInvocation.result === 'string') {
      // 如果result是字符串，尝试解析JSON
      try {
        const parsed = JSON.parse(toolInvocation.result);
        if (Array.isArray(parsed)) {
          sourcesArr = parsed;
          docList = sourcesArr.map((item: any) => item.source_document || item.source || item.document).filter(Boolean);
        }
      } catch (e) {
        // 如果不是JSON，就当作纯文本处理
        console.log('Result is plain text:', toolInvocation.result);
      }
    }
  }
  
  const uniqueDocs = Array.from(new Set(docList));
  const docCount = uniqueDocs.length;

  // 获取查询内容
  const queryMessage = toolInvocation?.args?.message || 'Unknown query';

  // 工具调用处理
  if (toolInvocation && toolInvocation.state !== 'result') {
    return (
      <div className="item-card bg-indigo-600/20 rounded-lg border border-indigo-500/30 p-2 text-white shadow-lg my-2 item-glow-indigo-subtle">
        <div className="flex items-center">
          <div className="bg-item-bg-hover p-1.5 rounded-lg mr-2.5">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
            </svg>
          </div>
          <div className="flex-1 text-sm text-white font-medium pr-2 leading-snug">
            Searching knowledge base "{kbText}"...
          </div>
        </div>
      </div>
    );
  }

  // 折叠视图
  if (!isExpanded) {
    return (
      <div className="item-card bg-indigo-600/20 rounded-lg border border-indigo-500/30 p-2 text-white shadow-lg my-2 cursor-pointer transition-all duration-200 hover:border-indigo-500/50 hover:shadow-xl item-glow-indigo-subtle">
        <div className="flex justify-between" onClick={() => setIsExpanded(true)}>
          <div className="flex items-center flex-1">
            <div className="bg-item-bg-hover p-1.5 rounded-lg mr-2.5">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="currentColor" className="text-indigo-400">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
              </svg>
            </div>
            <div className="text-sm text-item-gray-500 font-medium pr-2 leading-snug flex-1 truncate">
              Knowledge Base: {kbText} • {docCount > 0 ? `${docCount} document${docCount !== 1 ? 's' : ''} referenced` : 'No documents found'}
            </div>
          </div>
          <button className="text-indigo-400 hover:text-indigo-300 ml-2 flex-shrink-0 transition-colors duration-200">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-4 h-4">
              <path strokeLinecap="round" strokeLinejoin="round" d="M19.5 8.25l-7.5 7.5-7.5-7.5" />
            </svg>
          </button>
        </div>
      </div>
    );
  }

  // 展开视图
  return (
    <div className="item-card bg-indigo-600/20 rounded-lg border border-indigo-500/30 p-2 text-white shadow-lg my-2 item-glow-indigo-subtle">
      <div className="flex justify-between cursor-pointer" onClick={() => setIsExpanded(!isExpanded)}>
        <div className="flex items-center flex-1">
          <div className="bg-item-bg-hover p-1.5 rounded-lg mr-2.5">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="currentColor" className="text-indigo-400">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
            </svg>
          </div>
          <div className="text-sm text-white font-semibold pr-2 leading-snug flex-1 truncate">
            Knowledge Base Query: "{queryMessage}"
          </div>
        </div>
        <button className="text-indigo-400 hover:text-indigo-300 hover:bg-item-bg-hover p-1.5 rounded-lg ml-2 flex-shrink-0 transition-all duration-200">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-4 h-4">
            <path strokeLinecap="round" strokeLinejoin="round" d="M4.5 15.75l7.5-7.5 7.5 7.5" />
          </svg>
        </button>
      </div>

      <div className="mt-3 pt-2.5 border-t border-item-gray-800/40">
        <div className="text-xs text-indigo-400 mb-2 font-medium uppercase tracking-wide">
          Knowledge Base: {kbText} • Found {docCount} reference{docCount !== 1 ? 's' : ''}
        </div>
        
        {uniqueDocs.length > 0 ? (
          <div className="space-y-1.5">
            <div className="text-xs text-indigo-400 mb-2 font-semibold uppercase tracking-wide">Referenced Documents:</div>
            {uniqueDocs.map((doc, index) => (
              <div key={index} className="bg-item-bg-card rounded-lg p-2 border border-item-gray-800/30 hover:border-indigo-500/30 transition-colors duration-200">
                <div className="text-sm text-white font-medium">
                  {doc}
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-sm bg-item-bg-card p-2 rounded-lg border border-item-gray-800/30 text-white">
            No documents found for this query
          </div>
        )}
      </div>
      
      <div className="text-xs text-item-gray-400 mt-2.5 text-right font-medium">
        Knowledge Base Results
      </div>
    </div>
  );
} 