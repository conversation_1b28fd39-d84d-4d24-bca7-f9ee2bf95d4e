/**
 * Request Context Management
 * Provides utilities for managing request context during API calls
 */

/**
 * Request context type definition
 */
export interface RequestContext {
  // Authentication token or header
  authorization?: string;
  // Tenant ID for multi-tenant features
  tenantId?: string;
  // Facility ID for warehouse-specific features
  facilityId?: string;
  // User ID if available
  userId: string;
  // Creation timestamp
  createdAt: number;
  // Portal token
  portalToken?: string;
  // FMS token
  fmsToken?: string;
  // Customer code
  customerCode?: string;
  // UF customers
  ufCustomers?: string;
  // UT customers
  utCustomers?: string;
  // BI token
  biToken?: string;
  // Timezone information
  timezone?: string;
}

// Map to store request contexts by request ID
const requestContextStore = new Map<string, RequestContext>();

/**
 * Set request context for a specific request ID
 * @param requestId Unique request identifier
 * @param context Context information
 */
export function setRequestContext(requestId: string, context: Omit<RequestContext, 'createdAt'>) {
  requestContextStore.set(requestId, {
    ...context,
    createdAt: Date.now()
  });
}

/**
 * Get request context for a specific request ID
 * @param requestId Unique request identifier
 * @returns Request context or undefined if not found
 */
export function getRequestContext(requestId: string): RequestContext | undefined {

  return requestContextStore.get(requestId);
}

/**
 * Clear request context for a specific request ID
 * @param requestId Unique request identifier
 */
export function clearRequestContext(requestId: string) {
  requestContextStore.delete(requestId);
}

/**
 * Registers a callback to be executed when the process is about to exit
 * to clean up any remaining request contexts
 */
function registerCleanupHook() {
  process.on('beforeExit', () => {
    if (requestContextStore.size > 0) {
      console.log(`[RequestContext] Cleaning up ${requestContextStore.size} remaining contexts before exit`);
      requestContextStore.clear();
    }
  });
}

// Register the cleanup hook
registerCleanupHook();

/**
 * Format a tool call ID with request ID prefix
 * This allows tools to retrieve the request context when called
 * @param requestId The request ID to use as prefix
 * @param baseToolId The original tool call ID
 * @returns Formatted tool call ID with request ID prefix
 */
export function formatToolCallId(requestId: string, baseToolId: string): string {
  // 使用_call_作为请求ID和工具调用ID的分隔符，以便于提取
  return `${requestId}_call_${baseToolId}`;
}

/**
 * Extract request ID from a tool call ID
 * @param toolCallId Tool call ID possibly prefixed with request ID
 * @returns The extracted request ID or 'default' if not found
 */
export function extractRequestIdFromToolCallId(toolCallId: string): string {
  // 修改逻辑，考虑请求ID本身可能包含下划线的情况
  // 格式应该是 requestId_call_XXXXX，所以我们查找第一个"_call_"位置
  const callMarkerIndex = toolCallId.indexOf('_call_');

  if (callMarkerIndex !== -1) {
    // 返回"_call_"之前的部分作为请求ID
    return toolCallId.substring(0, callMarkerIndex);
  }

  // 如果没有找到"_call_"标记，则尝试旧的分割方法，但可能不可靠
  const parts = toolCallId.split('_');
  return parts.length > 1 ? parts[0] : 'default';
}

/**
 * Get user ID from tool call ID
 * First extracts request ID, then gets user ID from request context
 * @param toolCallId Tool call ID
 * @returns User ID or undefined
 */
export function getUserIdFromRequestContext(toolCallId: string): string | undefined {
  // Extract request ID from tool call ID
  const requestId = extractRequestIdFromToolCallId(toolCallId);
  console.log(`[RequestContext] Extracted request ID: ${requestId} from tool call ID: ${toolCallId}`);

  // Get user ID from request context
  const context = getRequestContext(requestId);
  console.log(`[RequestContext] Context for request ID ${requestId}:`, context);

  // Return user ID, or undefined if not found
  return context?.userId;
}