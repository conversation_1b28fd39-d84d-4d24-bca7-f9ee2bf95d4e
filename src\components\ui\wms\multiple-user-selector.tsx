'use client';

import * as React from 'react';
import { useState, useEffect, useRef } from 'react';
import { User, Search, Loader2, X } from 'lucide-react';
// @ts-ignore 忽略类型检查
import { debounce } from 'lodash';
import { wmsApi } from '@/utils/wmsApiClient';

import { Input } from '@/components/ui/input';
import { cn } from '@/lib/utils';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
  getCyberAgentPortalContainer,
} from "@/components/ui/select";

interface UserOption {
  id: string;
  name: string;
  email?: string;
}

interface MultipleUserSelectorProps {
  value?: string[];
  onChange: (value: string[], userData?: UserOption[]) => void;
  placeholder?: string;
  disabled?: boolean;
  required?: boolean;
  apiHeaders?: Record<string, string>;
  defaultValue?: string[];
  maxSelections?: number;
}

// 定义WMS API响应类型
interface WmsApiResponse {
  success: boolean;
  msg?: string;
  data?: {
    list?: any[];
    [key: string]: any;
  };
  [key: string]: any;
}

export function MultipleUserSelector({
  value = [],
  onChange,
  placeholder = 'Select users',
  disabled = false,
  required = false,
  apiHeaders = {},
  defaultValue = [],
  maxSelections
}: MultipleUserSelectorProps) {
  // Fixed API path
  const API_PATH = 'wms-bam/user/facility/search-by-paging';
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [options, setOptions] = useState<UserOption[]>([]);
  const [selectedUsers, setSelectedUsers] = useState<UserOption[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const searchInputRef = useRef<HTMLInputElement>(null);
  const initialLoadRef = useRef(false);

  // 检查字符串是否为纯数字（userId）
  const isNumericUserId = (value: string): boolean => {
    return /^\d+$/.test(value.trim());
  };

  // 通过名字搜索用户并自动选择（如果唯一匹配）
  const searchAndSelectUserByName = async (userName: string): Promise<UserOption | null> => {
    try {
      setLoading(true);
      console.log('Searching user by name:', userName);
      
      // 拆分用户名为firstName和lastName
      const nameParts = userName.trim().split(/\s+/);
      let searchParams: any = { pageSize: 10, currentPage: 1 };
      
      if (nameParts.length === 1) {
        // 只有一个词，可能是firstName或lastName
        searchParams.firstName = nameParts[0];
      } else if (nameParts.length >= 2) {
        // 多个词，第一个当作firstName，其余当作lastName
        searchParams.firstName = nameParts[0];
        searchParams.lastName = nameParts.slice(1).join(' ');
      }
      
      console.log('Search params:', searchParams);
      const response = await wmsApi.post<WmsApiResponse>(API_PATH, searchParams);

      console.log('Search user by name response:', response);

      if (response.success && response.data && response.data.list) {
        const userList = response.data.list;
        const users = userList.map((user: any) => ({
          id: user.userId,
          name: formatUserName(user),
          email: user.email
        }));

        // 尝试精确匹配
        const exactMatch = users.find((user: UserOption) => 
          user.name.toLowerCase() === userName.toLowerCase()
        );

        if (exactMatch) {
          console.log('Found exact match:', exactMatch);
          return exactMatch;
        }

        // 如果没有精确匹配，检查是否只有一个结果
        if (users.length === 1) {
          console.log('Found single match:', users[0]);
          return users[0];
        }

        // 如果有多个匹配，尝试部分匹配
        const partialMatches = users.filter((user: UserOption) =>
          user.name.toLowerCase().includes(userName.toLowerCase()) ||
          userName.toLowerCase().includes(user.name.toLowerCase())
        );

        if (partialMatches.length === 1) {
          console.log('Found single partial match:', partialMatches[0]);
          return partialMatches[0];
        }

        console.warn('Multiple matches found for name:', userName, 'matches:', users.length);
      } else {
        console.warn('No users found for name:', userName);
      }
    } catch (error) {
      console.error('Error searching user by name:', error);
    } finally {
      setLoading(false);
    }
    return null;
  };

  // 初始化时处理defaultValue
  useEffect(() => {
    console.log('defaultValue', defaultValue);
    console.log('Value', value);

    if (!initialLoadRef.current && defaultValue.length > 0 && value.length === 0) {
      initialLoadRef.current = true;
      
      const loadDefaultUsers = async () => {
        const users: UserOption[] = [];
        
        for (const defaultVal of defaultValue) {
          let user: UserOption | null = null;
          
          if (isNumericUserId(defaultVal)) {
            // 如果defaultValue是数字，按ID获取用户
            user = await fetchUserById(defaultVal);
          } else {
            // 如果defaultValue不是数字，当作用户名搜索
            user = await searchAndSelectUserByName(defaultVal);
          }
          
          if (user && !users.find(u => u.id === user.id)) {
            users.push(user);
          }
        }
        
        if (users.length > 0) {
          setSelectedUsers(users);
          onChange(users.map(u => u.id), users);
        }
      };
      
      loadDefaultUsers();
    }
  }, [defaultValue, value]);

  // If the external value changes, update the internal selected users
  useEffect(() => {
    if (value.length > 0) {
      const loadUsers = async () => {
        const users: UserOption[] = [];
        
        for (const val of value) {
          // 检查是否已经存在
          const existingUser = selectedUsers.find(u => u.id === val);
          if (existingUser) {
            users.push(existingUser);
            continue;
          }
          
          let user: UserOption | null = null;
          const isNumericId = /^\d+$/.test(val);
          
          if (isNumericId) {
            // The value is a numeric ID, fetch the user directly.
            user = await fetchUserById(val);
          } else {
            // The value is a name string, search for the user by name.
            user = await searchAndSelectUserByName(val);
          }
          
          if (user) {
            users.push(user);
          }
        }
        
        setSelectedUsers(users);
      };
      
      loadUsers();
    } else if (value.length === 0) {
      // The value was cleared, so reset the component's state.
      setSelectedUsers([]);
    }
  }, [value]);

  // 当下拉框打开时自动聚焦搜索框
  useEffect(() => {
    if (open && searchInputRef.current) {
      setTimeout(() => {
        searchInputRef.current?.focus();
      }, 100);
    }
  }, [open]);

  // 处理用户姓名显示的函数
  const formatUserName = (user: any): string => {
    // 1. 优先使用fullName，如果不为空且不是"undefined undefined"
    if (user.fullName && user.fullName !== "undefined undefined" && !user.fullName.includes("undefined")) {
      return user.fullName;
    }

    // 2. 如果firstName和lastName都有值，则组合它们
    if (user.firstName && user.lastName &&
        user.firstName !== "undefined" && user.lastName !== "undefined") {
      return `${user.firstName} ${user.lastName}`;
    }

    // 3. 如果firstName和lastName都是空的，则使用userName
    return user.userName || `User ${user.userId}`;
  };

  // 通过ID获取用户信息
  const fetchUserById = async (userId: string): Promise<UserOption | null> => {
    console.log('Fetching user by ID:', userId);
    if (!/^\d+$/.test(userId)) {
      console.warn('fetchUserById called with non-numeric value:', userId);
      return null;
    }
    try {
      setLoading(true);
      const response = await wmsApi.post<WmsApiResponse>(API_PATH, { userId });

      if (response.success && response.data && response.data.list && response.data.list.length > 0) {
        const user = response.data.list[0];
        const userOption: UserOption = {
          id: user.userId,
          name: formatUserName(user),
          email: user.email
        };
        return userOption;
      } else {
        console.warn('User not found with ID:', userId);
      }
    } catch (error) {
      console.error('Error fetching user by ID:', error);
    } finally {
      setLoading(false);
    }
    return null;
  };

  // 搜索用户的函数
  const searchUsers = async (query: string) => {
    if (!query.trim()) {
      setOptions([]);
      return;
    }

    try {
      setLoading(true);
      console.log('Searching users with query:', query);
      const response = await wmsApi.post<WmsApiResponse>(API_PATH, { name: query });

      console.log('User search API response:', response);

      if (response.success && response.data && response.data.list) {
        const userList = response.data.list;
        const users = userList.map((user: any) => ({
          id: user.userId,
          name: formatUserName(user),
          email: user.email
        }));

        console.log('Mapped users:', users);
        setOptions(users);
      } else {
        console.warn('No users found or API error:', response.msg);
        setOptions([]);
      }
    } catch (error) {
      console.error('Error searching users:', error);
      setOptions([]);
    } finally {
      setLoading(false);
    }
  };

  // 防抖搜索
  const debouncedSearch = useRef(
    debounce((query: string) => {
      searchUsers(query);
    }, 300)
  ).current;

  // 处理搜索输入变化
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchQuery(value);
    debouncedSearch(value);
  };

  // 处理用户选择
  const handleUserSelect = (userId: string) => {
    const user = options.find(opt => opt.id === userId);
    if (user) {
      // 检查用户是否已经被选中
      if (selectedUsers.find(u => u.id === userId)) {
        return;
      }
      
      // 检查是否达到最大选择数量
      if (maxSelections && selectedUsers.length >= maxSelections) {
        return;
      }
      
      const newSelectedUsers = [...selectedUsers, user];
      setSelectedUsers(newSelectedUsers);
      onChange(newSelectedUsers.map(u => u.id), newSelectedUsers);
      
      // 重置搜索状态
      setSearchQuery('');
      setOptions([]);
    }
  };

  // 移除选中的用户
  const removeUser = (userId: string) => {
    console.log('removeUser called with userId:', userId);
    console.log('Current selectedUsers:', selectedUsers);
    const newSelectedUsers = selectedUsers.filter(u => u.id !== userId);
    console.log('New selectedUsers after filter:', newSelectedUsers);
    setSelectedUsers(newSelectedUsers);
    onChange(newSelectedUsers.map(u => u.id), newSelectedUsers);
  };

  // 打开下拉框时触发搜索
  const handleOpenChange = (isOpen: boolean) => {
    setOpen(isOpen);

    // 如果关闭下拉框，清空搜索和结果
    if (!isOpen) {
      setSearchQuery('');
      setOptions([]);
    }
    // 如果打开下拉框且有搜索关键字，执行搜索
    else if (isOpen && searchQuery) {
      debouncedSearch(searchQuery);
    }
  };

  // 过滤掉已选中的用户
  const availableOptions = options.filter(option => 
    !selectedUsers.find(selected => selected.id === option.id)
  );

  return (
    <div className="relative w-full">
      <Select
        value=""
        onValueChange={handleUserSelect}
        open={open}
        onOpenChange={handleOpenChange}
        disabled={disabled}
      >
        <SelectTrigger
          className={cn(
            "w-full min-h-10 rounded-md bg-transparent",
            "border border-item-gray-700/50",
            "hover:border-item-purple focus:border-item-purple",
            "flex items-center justify-between px-3 py-2 text-sm text-white",
            "focus:outline-none focus:ring-1 focus:ring-item-purple",
            "transition-all duration-200",
            "data-[placeholder]:text-item-gray-400"
          )}
        >
          <div className="flex flex-wrap gap-1 flex-1">
            {selectedUsers.length > 0 ? (
              selectedUsers.map((user) => (
                <div
                  key={user.id}
                  className="flex items-center bg-item-purple/20 rounded-md px-2 py-1 text-xs"
                  onClick={(e) => {
                    // 阻止整个标签的点击事件冒泡
                    e.stopPropagation();
                  }}
                >
                  <User className="mr-1 h-3 w-3 text-item-gray-400" />
                  <span className="mr-1">{user.name}</span>
                  <button
                    type="button"
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      console.log('Remove user clicked:', user.id);
                      removeUser(user.id);
                    }}
                    onMouseDown={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                    }}
                    onPointerDown={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                    }}
                    className="hover:text-red-400 transition-colors flex items-center justify-center ml-1"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </div>
              ))
            ) : (
              <span className="text-item-gray-400">{placeholder}</span>
            )}
          </div>
          {loading && (
            <Loader2 className="h-4 w-4 animate-spin text-item-gray-400 ml-2" />
          )}
        </SelectTrigger>
        <SelectContent
          className={cn(
            "border border-item-gray-700/70 bg-item-bg-card text-white",
            "rounded-md shadow-lg min-w-[300px] overflow-hidden",
            "animate-in fade-in-80 zoom-in-95"
          )}
          portalContainer={getCyberAgentPortalContainer()}
        >
          <div className="flex items-center px-3 py-2 border-b border-item-gray-700/50">
            <Search className="mr-2 h-4 w-4 shrink-0 text-item-gray-400" />
            <Input
              ref={searchInputRef}
              className="h-8 border-0 p-0 bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0 text-white placeholder:text-item-gray-400"
              placeholder="Search users..."
              value={searchQuery}
              onChange={handleSearchChange}
            />
          </div>

          <div className="max-h-[320px] overflow-auto scrollbar-thin scrollbar-thumb-item-gray-600 scrollbar-track-transparent">
            {maxSelections && selectedUsers.length >= maxSelections ? (
              <div className="py-6 text-center text-sm text-item-gray-400">
                Maximum {maxSelections} users selected
              </div>
            ) : loading ? (
              <div className="flex items-center justify-center py-6">
                <Loader2 className="h-6 w-6 animate-spin text-item-gray-400" />
              </div>
            ) : availableOptions.length > 0 ? (
              <SelectGroup>
                <SelectLabel className="px-3 text-xs font-medium text-item-gray-400">Available Users</SelectLabel>
                {availableOptions.map((user) => (
                  <SelectItem
                    key={user.id}
                    value={user.id}
                    className={cn(
                      "py-2 px-3 cursor-pointer text-white",
                      "focus:bg-item-purple focus:text-white",
                      "data-[highlighted]:bg-item-purple data-[highlighted]:text-white"
                    )}
                  >
                    <div className="flex items-center">
                      <User className="mr-2 h-4 w-4 text-item-gray-400" />
                      <div>
                        <div>{user.name}</div>
                        {user.email && (
                          <div className="text-xs text-item-gray-400">{user.email}</div>
                        )}
                      </div>
                    </div>
                  </SelectItem>
                ))}
              </SelectGroup>
            ) : searchQuery ? (
              <div className="py-6 text-center text-sm text-item-gray-400">
                No users found
              </div>
            ) : (
              <div className="py-6 text-center text-sm text-item-gray-400">
                Type to search users
              </div>
            )}
          </div>
        </SelectContent>
      </Select>
    </div>
  );
}