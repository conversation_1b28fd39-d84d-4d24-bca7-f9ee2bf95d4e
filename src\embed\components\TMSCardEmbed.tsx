import React from 'react';

export default function TMSCardEmbed({ toolInvocation }: { toolInvocation: any }) {
  // Extract the result data and arguments
  const result = toolInvocation.result || {};
  const args = toolInvocation.args || {};
  
  // Get search parameters
  const searchText = args.searchText || '';
  const page = args.page || 1;
  
  // Determine tool type and display label
  const toolName = toolInvocation.toolName || '';
  const displayLabel = toolName === 'tmsQuoteTool' ? 'TMS Quote:' : 'TMS Tracking:';
  
  // Determine if the operation was successful
  const isSuccess = result.success === true;
  const hasError = result.success === false;
  
  // Get result information
  let statusText = '';
  let baseUrl = result.baseUrl || 'TMS';
  
  if (isSuccess) {
    if (toolName === 'tmsQuoteTool') {
      // Handle TMS Quote response
      // The quote data is stored in result.quote_data
      if (result.quote_data) {
        // Check if quote_data has quote_amount (indicating a successful quote)
        if (result.quote_data.quote_amount) {
          const amount = result.quote_data.quote_amount;
          const quoteId = result.quote_data.quote_id || '';
          statusText = `Quote: $${amount}${quoteId ? ` (ID: ${quoteId})` : ''}`;
        } else if (result.quote_data.quotes && Array.isArray(result.quote_data.quotes)) {
          // Handle array of quotes format
          const quoteCount = result.quote_data.quotes.length;
          statusText = quoteCount > 0 
            ? `Found ${quoteCount} quote${quoteCount !== 1 ? 's' : ''}` 
            : 'No quotes found';
        } else if (result.quote_data.quote) {
          // Handle single quote object format
          statusText = 'Quote generated';
        } else {
          // Check if quote_data has any quote-related fields
          const hasQuoteFields = result.quote_data.quote_id || 
                                 result.quote_data.quote_lines || 
                                 result.quote_data.terminal_days;
          statusText = hasQuoteFields ? 'Quote generated' : 'No quotes found';
        }
      } else {
        statusText = 'No quotes found';
      }
    } else {
      // Handle TMS Tracking response (original logic)
      let orderCount = 0;
      // Parse the TMS API response structure
      // The actual structure is: result.data.data.data (array of orders)
      if (result.data && result.data.data && result.data.data.data) {
        const orders = result.data.data.data;
        if (Array.isArray(orders)) {
          orderCount = orders.length;
        }
      } else if (result.data && result.data.data && result.data.data.total) {
        // Fallback to total count if available
        orderCount = result.data.data.total;
      }
      
      statusText = orderCount > 0 
        ? `Found ${orderCount} order${orderCount !== 1 ? 's' : ''}` 
        : 'No orders found';
    }
  } else if (hasError) {
    statusText = `Error: ${result.error || 'Unknown error'}`;
  } else {
    statusText = 'Processing...';
  }
  
  return (
    <div className="embed-card tms-tracking p-2 my-1 bg-blue-800/20 rounded border-l-2 border-blue-600/30 border-t border-r border-b border-blue-700/20">
      <div className="flex items-center" style={{ whiteSpace: 'nowrap', width: '100%' }}>
        <div className="mr-2 text-blue-400 flex-shrink-0">
          <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4" />
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 7l6 6 2-2 6-6" />
          </svg>
        </div>
        <div className="text-xs text-blue-300 flex-shrink-0 mr-2">
          {displayLabel}
        </div>
        <div className="text-xs text-blue-400 overflow-hidden text-ellipsis mr-2" style={{ textOverflow: 'ellipsis', overflow: 'hidden', whiteSpace: 'nowrap' }}>
          {searchText}
        </div>
        {page > 1 && (
          <div className="text-xs text-blue-500 flex-shrink-0 mr-2">
            (Page {page})
          </div>
        )}
        <div className="text-xs text-gray-400 overflow-hidden text-ellipsis" style={{ textOverflow: 'ellipsis', overflow: 'hidden', whiteSpace: 'nowrap' }}>
          {statusText}
        </div>
      </div>
    </div>
  );
} 