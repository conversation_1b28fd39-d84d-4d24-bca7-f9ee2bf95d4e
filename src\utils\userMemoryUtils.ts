/**
 * 用户记忆工具函数
 * 用于处理用户信息的记忆保存
 */

import api from '@/utils/apiClient';
import { WmsUserInfo } from '@/utils/clientUserContext';

/**
 * 将设施信息转换为自然语言描述并保存为记忆
 * 单独调用add memory API，设置infer参数为false，确保整条信息都被保存
 *
 * @param userInfo WMS用户信息对象
 * @returns 保存结果
 */
export async function saveFacilitiesAsMemory(userInfo: WmsUserInfo): Promise<boolean> {
  try {
    if (!userInfo || !userInfo.facilities || userInfo.facilities.length === 0) {
      console.error('[UserMemoryUtils] 无法保存设施信息记忆: 设施信息为空');
      return false;
    }

    console.log('[UserMemoryUtils] 开始将设施信息转换为自然语言并保存为记忆...');

    // 获取默认设施ID
    // 注意：WmsUserInfo类型可能没有定义profile属性，但实际对象可能有
    const defaultFacilityId = (userInfo as any).profile?.defaultFacilityId ||
                             (userInfo.currentFacility ? userInfo.currentFacility.id : null);

    // 构建设施列表的自然语言描述
    let facilitiesText = "Please keep this content in one memory id:  Available warehouse facilities: ";
    facilitiesText += userInfo.facilities.map(facility =>
      `${facility.name} (${facility.id})`
    ).join(", ");

    // 如果有默认设施，添加默认设施信息
    if (defaultFacilityId) {
      const defaultFacility = userInfo.facilities.find(f => f.id === defaultFacilityId);
      if (defaultFacility) {
        facilitiesText += `\n\nDefault facility: ${defaultFacility.name} (${defaultFacility.id})`;
      }
    }
    
    console.log('[UserMemoryUtils] 设施信息自然语言描述:', facilitiesText);

    try {
      // 通过API保存记忆，设置infer=false
      console.log('[UserMemoryUtils] 调用API保存设施信息记忆...');
      const response = await api.post('/api/memory', {
        type: 'user',
        content: facilitiesText,
        //infer: false,  // 设置infer=false，确保整条信息都被保存
        metadata: {
          source: 'facilities_info',
          timestamp: new Date().toISOString(),
        }
      });


      // 检查响应状态
      if (response.status >= 200 && response.status < 300) {
        return true;
      } else {
      
        if (response.data) {
          console.error('[UserMemoryUtils] 响应数据:', JSON.stringify(response.data));
        }
        return false;
      }
    } catch (error: any) {
      console.error('[UserMemoryUtils] 调用记忆API保存设施信息时发生异常:', error);
      return false;
    }
  } catch (error) {
    console.error('[UserMemoryUtils] 处理设施信息记忆时出错:', error);
    return false;
  }
}

/**
 * 将用户信息保存为记忆
 * 保存WMS用户信息，包含用户基本信息和设施信息
 * 通过API调用保存，而不是直接调用服务
 *
 * @param userInfo WMS用户信息对象
 * @returns 保存结果
 */
export async function saveUserInfoAsMemory(userInfo: WmsUserInfo): Promise<boolean> {
  try {
    if (!userInfo) {
      console.error('[UserMemoryUtils] 无法保存WMS用户信息记忆: 用户信息为空');
      return false;
    }

    console.log('[UserMemoryUtils] 开始保存WMS用户信息为记忆...');

    // 创建一个不包含profile节点的WMS用户信息副本
    // 同时确保设施信息被正确保留
    const wmsUserInfoToSave = {
      id: userInfo.id,
      username: userInfo.username,
      fullName: userInfo.fullName,
      email: userInfo.email,
      tenantId: userInfo.tenantId,
      roles: userInfo.roles
    };

    const content =

       "Please keep my profile: " + JSON.stringify(wmsUserInfoToSave, null, 2);



    try {
      // 通过API保存记忆
      console.log('[UserMemoryUtils] 调用API保存记忆...');
      const response = await api.post('/api/memory', {
        type: 'user',
        content: content,
        metadata: {
          source: 'wms_user_info',
          timestamp: new Date().toISOString(),
          logLevel: 'DEBUG'  // 添加logLevel参数，与testMem0.js保持一致
        }
      });


      if (response && response.data) {
        console.log('[UserMemoryUtils] WMS用户信息已成功保存为记忆');
        return true;
      } else {
        console.error('[UserMemoryUtils] 保存WMS用户信息记忆失败:', response?.error);
        // 如果响应中有更详细的错误信息，记录下来
        if (response?.data) {
          console.error('[UserMemoryUtils] 错误详情:', JSON.stringify(response.data));
        }
        return false;
      }
    } catch (error: any) {
      console.error('[UserMemoryUtils] 调用记忆API时发生异常:', error);
      // 尝试提取更多错误信息
      if (error && error.response) {
        console.error('[UserMemoryUtils] 响应状态:', error.response.status);
        console.error('[UserMemoryUtils] 响应数据:', JSON.stringify(error.response.data));
      }
      return false;
    }
  } catch (error) {
    console.error('[UserMemoryUtils] 保存用户信息记忆时出错:', error);
    return false;
  }
}
