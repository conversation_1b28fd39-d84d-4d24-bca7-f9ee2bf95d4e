/**
 * User Context Management System
 * 
 * 提供统一的用户上下文管理接口，根据运行环境动态导入客户端或服务端实现
 */

// 导出类型定义
export type { 
  SessionContext, 
  UserPreferences, 
  UserContext 
} from './serverUserContext';

// 动态导入正确的实现
// 注意：在实际导入时，这些模块是静态分析的，所以不会引起客户端包含服务端代码
let userContextManager: any;

// 仅在服务端执行的代码
if (typeof window === 'undefined') {
  // 服务端环境
  const { serverUserContextManager } = require('./serverUserContext');
  userContextManager = serverUserContextManager;
} else {
  // 客户端环境
  const { clientUserContextManager } = require('./clientUserContext');
  userContextManager = clientUserContextManager;
}

// 导出统一接口
export { userContextManager }; 