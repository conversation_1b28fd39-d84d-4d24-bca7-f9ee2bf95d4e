import { NextRequest, NextResponse } from 'next/server';
import { getUserIdFromRequest } from '@/utils/authUtils';
import { getReportByDate, searchReports } from '@/chat-report/reportStorage';
import { getChatHistory } from '@/utils/storage';
import JSZip from 'jszip';
import { format } from 'date-fns';

type ReportSearchParams = {
  fromDate?: string;
  toDate?: string;
  query?: string;
  resolved?: boolean;
};

export async function GET(req: NextRequest) {
  try {
    // 获取当前用户ID（用于权限验证）
    const currentUserId = await getUserIdFromRequest(req);
    if (!currentUserId) {
      return NextResponse.json(
        { error: '用户未登录或会话已过期' },
        { status: 401 }
      );
    }

    // 获取查询参数
    const url = new URL(req.url);
    const searchParams: ReportSearchParams = {
      fromDate: url.searchParams.get('fromDate') || undefined,
      toDate: url.searchParams.get('toDate') || undefined,
      query: url.searchParams.get('query') || undefined,
    };

    const resolvedParam = url.searchParams.get('resolved');
    if (resolvedParam !== null) {
      searchParams.resolved = resolvedParam === 'true';
    }

    // 使用与查询接口相同的搜索逻辑获取报告
    const reports = await searchReports(searchParams);

    // 创建zip文件
    const zip = new JSZip();

    // 处理每个报告
    for (const report of reports) {
      // 获取完整的报告数据（包含chatAnalyses）
      const fullReport = await getReportByDate(report.date);
      if (!fullReport) continue;

      // 为每个报告创建独立的文件夹
      const reportDate = format(new Date(report.date), 'yyyyMMdd');
      const reportFolder = zip.folder(`report_${reportDate}`);
      if (!reportFolder) {
        console.warn(`无法创建报告文件夹: report_${reportDate}`);
        continue;
      }

      // 在报告文件夹中创建聊天历史文件夹
      const chatHistoryFolder = reportFolder.folder('chat-history');
      if (!chatHistoryFolder) {
        console.warn(`无法创建聊天历史文件夹: report_${reportDate}/chat-history`);
        continue;
      }

      // 添加报告JSON文件到报告文件夹
      const reportFileName = `report.json`;
      reportFolder.file(reportFileName, JSON.stringify(fullReport, null, 2));

      // 获取并添加关联的聊天历史到聊天历史文件夹
      for (const chatId of fullReport.chatIds) {
        const userId = fullReport.chatUserMap[chatId];
        if (!userId) continue;

        const chatHistory = await getChatHistory(chatId, userId);
        if (chatHistory) {
          const chatFileName = `${chatId}.json`;
          chatHistoryFolder.file(chatFileName, JSON.stringify(chatHistory, null, 2));
        }
      }
    }

    // 生成zip文件
    const zipContent = await zip.generateAsync({ type: 'nodebuffer' });

    // 设置响应头
    const headers = new Headers();
    headers.set('Content-Type', 'application/zip');
    headers.set('Content-Disposition', `attachment; filename="chat_reports_${format(new Date(), 'yyyyMMdd')}.zip"`);

    return new NextResponse(zipContent, {
      status: 200,
      headers
    });

  } catch (error) {
    console.error('导出报告失败:', error);
    return NextResponse.json(
      { error: '导出报告失败' },
      { status: 500 }
    );
  }
} 