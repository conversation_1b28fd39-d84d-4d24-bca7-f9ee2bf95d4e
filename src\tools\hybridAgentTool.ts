import { tool } from '@openai/agents/realtime';
import api from '@/utils/apiClient';
import { clientUserContextManager } from '@/utils/clientUserContext';
import { log } from '@/utils/logger';

// Global message storage - simple and direct solution
declare global {
  interface Window {
    __CHAT_MESSAGES__: any[];
  }
}

/**
 * Process hybrid stream response and forward data while accumulating results
 */
async function processHybridStream(body: any, toolCallId: string, taskDescription: string): Promise<string> {
  const makeStreamRequest = async (authHeaders: Record<string, string>) => {
    const response = await fetch('/api/hybrid-agent', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Tenant-Id': clientUserContextManager.getCurrentTenantId() || '',
        'X-Facility-Id': clientUserContextManager.getCurrentFacilityId() || '',
        ...authHeaders,
      },
      body: JSON.stringify({
        ...body,
        parallel_tool_calls: false
      })
    });

    return response;
  };

  try {
    // Get auth headers
    let authHeaders = clientUserContextManager.getAuthHeaders();
    
    // Make stream request
    let response = await makeStreamRequest(authHeaders);

    // Retry once if 401 error
    if (response.status === 401) {
      log.warn('Authentication failed, retrying', undefined, 'hybrid-agent');
      authHeaders = clientUserContextManager.getAuthHeaders();
      response = await makeStreamRequest(authHeaders);
      
      if (response.status === 401) {
        throw new Error('Authentication failed after retry');
      }
    }

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    if (!response.body) {
      throw new Error('No response body');
    }

    log.info('Stream response received, processing started', { toolCallId }, 'hybrid-agent');

    // Notify frontend to start listening for stream events
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('hybrid-agent-tool-call', {
        detail: { toolCallId, taskDescription }
      }));
    }

    // Read stream data, forward to frontend and accumulate final result
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let fullContent = '';
    let finalAssistantMessage = '';
    let chunkCount = 0;

    while (true) {
      const { done, value } = await reader.read();
      
      if (done) {
        log.info('Stream processing completed', { 
          toolCallId, 
          totalChunks: chunkCount, 
          resultLength: finalAssistantMessage.length 
        }, 'hybrid-agent');
        
        // Send completion event
        if (typeof window !== 'undefined') {
          window.dispatchEvent(new CustomEvent(`hybrid-agent-${toolCallId}`, {
            detail: { type: 'completed' }
          }));
        }
        break;
      }

      chunkCount++;
      const chunk = decoder.decode(value);
      
      // Forward raw data chunk to frontend
      if (typeof window !== 'undefined') {
        window.dispatchEvent(new CustomEvent(`hybrid-agent-${toolCallId}`, {
          detail: { 
            type: 'chunk',
            data: chunk
          }
        }));
      }

      // Parse data chunks to accumulate final result - Vercel AI SDK format
      const lines = chunk.split('\n');
      
      for (let i = 0; i < lines.length; i++) {
        const line = lines[i].trim();
        if (!line) continue;
        
        // Vercel AI SDK format: prefix:JSON_content
        const colonIndex = line.indexOf(':');
        if (colonIndex === -1) continue;
        
        const prefix = line.substring(0, colonIndex);
        const rawContent = line.substring(colonIndex + 1);
        
        try {
          // 0: Text delta - format is 0:"text_content"
          if (prefix === '0' && rawContent.startsWith('"') && rawContent.endsWith('"')) {
            const textContent = rawContent.slice(1, -1); // Remove quotes
            finalAssistantMessage += textContent;
          }
          
          // e: Step end
          else if (prefix === 'e') {
            const parsed = JSON.parse(rawContent);
            if (parsed.finishReason === 'stop') {
              fullContent = finalAssistantMessage;
            }
          }
          
          // d: Message end
          else if (prefix === 'd') {
            const parsed = JSON.parse(rawContent);
            if (parsed.finishReason === 'stop') {
              fullContent = finalAssistantMessage;
            }
          }
          
        } catch (parseError) {
          // Silently ignore parsing errors for non-critical data
          continue;
        }
      }
    }

    reader.releaseLock();
    
    // Return accumulated final result
    const result = fullContent || finalAssistantMessage || 'Task completed successfully.';
    log.info('Stream processing finished successfully', { 
      toolCallId, 
      resultLength: result.length 
    }, 'hybrid-agent');
    return result;
    
  } catch (error) {
    log.error('Stream processing failed', error, 'hybrid-agent');
    
    // Send error event to frontend
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent(`hybrid-agent-${toolCallId}`, {
        detail: { 
          type: 'error',
          error: error instanceof Error ? error.message : String(error)
        }
      }));
    }
    
    throw error;
  }
}
/**
 * HybridAgent tool definition for OpenAI Realtime API
 * Uses single stream request to forward data and return results
 */
export const hybridAgentTool = tool({
  name: 'hybridAgentTool',
  description: 'Determines the next response for complex tasks that require intelligent processing, tool calling, and high-quality responses using a hybrid agent with access to all available tools.',
  parameters: {
    type: 'object',
    properties: {
      taskDescription: {
        type: 'string',
        description: 'Brief description of the task or query to be processed by the hybrid agent.',
      },
    },
    required: ['taskDescription'],
    additionalProperties: false,
  },
  execute: async (input, details) => {
    const { taskDescription } = input as {
      taskDescription: string;
    };

    const addBreadcrumb = (details?.context as any)?.addTranscriptBreadcrumb as
      | ((title: string, data?: any) => void)
      | undefined;

    // Generate unique tool call ID
    const toolCallId = `hybrid-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

    // Get latest chat messages from global state
    const messages: any[] = (typeof window !== 'undefined' && window.__CHAT_MESSAGES__) || [];
    log.info('Processing hybrid agent request', { 
      toolCallId, 
      taskDescription: taskDescription.substring(0, 100) + '...',
      messagesCount: messages.length 
    }, 'hybrid-agent');

    // Build request body - pass original messages directly like Chat.tsx does to /api/chat
    const body: any = {
      // Use original messages directly, hybrid-agent API has complete system prompts
      messages: [
        ...messages,
        // Add current user task as latest message
        {
          role: 'user',
          content: taskDescription
        }
      ],
      // Add model and tools configuration
      model: 'gpt-4.1-2025-04-14',
      tools: true
    };

    try {
      if (addBreadcrumb) {
        addBreadcrumb('[hybridAgent] Processing request', { 
          taskDescription, 
          originalMessagesCount: messages.length,
          totalMessagesCount: body.messages.length
        });
      }

      // Process stream request, forward data and get final result
      const finalText = await processHybridStream(body, toolCallId, taskDescription);

      if (addBreadcrumb) {
        addBreadcrumb('[hybridAgent] Response generated', { response: finalText });
      }

      log.info('Hybrid agent request completed successfully', { 
        toolCallId, 
        responseLength: finalText.length 
      }, 'hybrid-agent');

      return { nextResponse: finalText, toolCallId };
    } catch (error) {
      log.error('Hybrid agent request failed', error, 'hybrid-agent');
      return { error: 'I encountered an error while processing your request. Please try again.' };
    }
  },
}); 