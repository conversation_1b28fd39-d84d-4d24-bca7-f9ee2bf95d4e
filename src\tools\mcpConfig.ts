/**
 * MCP服务器配置类型定义
 */
export interface McpServerConfig {
  // 服务器URL
  url?: string;
  // 命令行启动配置（如果需要本地启动）
  command?: string;
  args?: string[];
  // 环境变量
  env?: Record<string, string>;
  // 其他配置选项
  options?: Record<string, any>;
  // 服务器描述
  description?: string;
}

/**
 * MCP服务器配置集合
 */
export interface McpConfig {
  // 默认服务器ID
  defaultServer?: string;
  // 服务器配置映射
  mcpServers: Record<string, McpServerConfig>;
}

// 导入用于文件系统操作的模块
import * as fs from 'fs';
import * as path from 'path';

// 缓存配置对象
let cachedConfig: McpConfig | null = null;
// 配置加载时间戳
let configLoadTimestamp: number = 0;
// 默认配置缓存有效期为28天，可通过环境变量配置
const CONFIG_CACHE_TTL = parseInt(process.env.MCP_CACHE_TTL || '2419200000'); // 28天

/**
 * 从配置文件加载MCP配置
 */
function loadConfigFromFile(): McpConfig | null {
  try {
    // 尝试从根目录读取mcp.json
    const configPath = path.join(process.cwd(), 'mcp.json');
    
    if (fs.existsSync(configPath)) {
      const configContent = fs.readFileSync(configPath, 'utf8');
      const config = JSON.parse(configContent);
      console.log('[MCP Config] 从文件加载配置成功:', configPath);
      return config;
    }
    
    console.log('[MCP Config] 配置文件不存在');
    return null;
  } catch (error) {
    console.error('[MCP Config] 加载配置文件时出错:', error);
    return null;
  }
}

/**
 * 从环境变量或配置文件获取MCP配置
 */
export function getMcpConfig(): McpConfig {
  const now = Date.now();
  
  // 检查缓存是否有效
  if (
    cachedConfig && 
    configLoadTimestamp && 
    now - configLoadTimestamp < CONFIG_CACHE_TTL
  ) {
    return cachedConfig;
  }
  
  // 缓存过期或不存在，重新加载
  console.log('[MCP Config] 配置缓存过期或不存在，重新加载配置');
  
  // 尝试从环境变量获取JSON配置
  const configStr = process.env.MCP_CONFIG;
  if (configStr) {
    try {
      const parsedConfig = JSON.parse(configStr) as McpConfig;
      cachedConfig = parsedConfig;
      configLoadTimestamp = now;
      console.log('[MCP Config] 从环境变量加载配置成功');
      return cachedConfig;
    } catch (error) {
      console.error('[MCP Config] 解析环境变量MCP_CONFIG时出错:', error);
    }
  }
  
  // 尝试从文件加载配置
  const fileConfig = loadConfigFromFile();
  if (fileConfig !== null) {
    cachedConfig = fileConfig;
    configLoadTimestamp = now;
    return cachedConfig;
  }
  
  // 没有找到配置，返回空配置
  console.warn('[MCP Config] 未找到有效配置，请确保 mcp.json 文件存在或已设置 MCP_CONFIG 环境变量');
  cachedConfig = {
    defaultServer: '',
    mcpServers: {}
  };
  configLoadTimestamp = now;
  return cachedConfig;
}

/**
 * 强制重新加载MCP配置
 * 可用于在系统运行时动态更新配置
 */
export function reloadMcpConfig(): McpConfig {
  console.log('[MCP Config] 强制重新加载MCP配置...');
  
  // 清除缓存
  cachedConfig = null;
  configLoadTimestamp = 0;
  
  // 重新加载配置
  return getMcpConfig();
}

/**
 * 获取指定MCP服务器的URL
 * @param serverId 服务器ID，为空则使用默认服务器
 * @returns 服务器URL
 */
export function getMcpServerUrl(serverId?: string): string {
  const config = getMcpConfig();
  
  // 确定要使用的服务器ID
  const targetServerId = serverId || config.defaultServer || Object.keys(config.mcpServers)[0];
  
  // 获取对应服务器配置
  const serverConfig = config.mcpServers[targetServerId];
  if (!serverConfig) {
    throw new Error(`MCP server "${targetServerId}" not configured`);
  }
  
  // 优先使用特定的环境变量
  const envVarName = `MCP_SERVER_URL_${targetServerId.toUpperCase().replace(/-/g, '_')}`;
  const urlFromEnv = process.env[envVarName];
  if (urlFromEnv) {
    return urlFromEnv;
  }
  
  // 其次使用配置中的URL
  if (serverConfig.url) {
    return serverConfig.url;
  }
  
  // 没有找到URL配置
  throw new Error(`URL for MCP server "${targetServerId}" is not defined`);
}

/**
 * 获取所有配置的MCP服务器ID列表
 */
export function getAllMcpServerIds(): string[] {
  const config = getMcpConfig();
  return Object.keys(config.mcpServers);
}

/**
 * 获取当前激活的MCP服务器ID
 */
export function getActiveMcpServerId(): string {
  // 首先检查是否有服务器ID被显式设置
  const serverId = process.env.MCP_ACTIVE_SERVER;
  if (serverId) {
    return serverId;
  }
  
  // 否则使用默认服务器
  const config = getMcpConfig();
  return config.defaultServer || Object.keys(config.mcpServers)[0];
}

/**
 * 设置活动MCP服务器ID
 * @param serverId 要设置为活动的服务器ID
 */
export function setActiveMcpServerId(serverId: string): void {
  // 在服务器端设置环境变量
  if (typeof process !== 'undefined' && process.env) {
    process.env.MCP_ACTIVE_SERVER = serverId;
  }
  
  // 在客户端可以使用其他方式存储这个设置，例如localStorage
  if (typeof window !== 'undefined' && window.localStorage) {
    window.localStorage.setItem('MCP_ACTIVE_SERVER', serverId);
  }
  
  console.log(`[MCP Config] 已将活动服务器设置为: ${serverId}`);
} 