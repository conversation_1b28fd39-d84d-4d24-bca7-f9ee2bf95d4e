'use client';

import * as React from 'react';
import { Check, ChevronsUpDown, X } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Badge } from '@/components/ui/badge';

// TMS Accessorial charges data
const ACCESSORIAL_OPTIONS = [
  { ID: "107", code: "HOLDL", name: "DELIVERIES: HOLIDAYS" },
  { ID: "106", code: "HOLPU", name: "PICKUPS: HOLIDAYS" },
  { ID: "105", code: "CFSPD", name: "PICKUPS OR DELIVERIES: CFS WHSE" },
  { ID: "104", code: "PWPND", name: "PICKUPS OR DELIVERIES: PIER, WHARF" },
  { ID: "103", code: "INSDP", name: "INSIDE PICKUP" },
  { ID: "102", code: "EXBIP", name: "<PERSON><PERSON><PERSON>UP - <PERSON><PERSON>HIBITION SITES" },
  { ID: "101", code: "<PERSON>VE<PERSON>", name: "EXCESSIVE LENGTH SHIPMENTS" },
  { ID: "100", code: "IBOND", name: "IN BOND FREIGHT - UNITED STATES" },
  { ID: "96", code: "AMZSU", name: "AMAZON SURCHARGE" },
  { ID: "89", code: "LIMTP", name: "PICKUPS: RESTRICTED/LIMITED ACCESS" },
  { ID: "84", code: "NOTIF", name: "NOTIFY PRIOR TO DELIVERY (NO APPT)" },
  { ID: "76", code: "LABOR", name: "EXTRA LABOR" },
  { ID: "71", code: "RESDP", name: "PICKUPS: RESIDENTIAL/NON-COMMERCIAL" },
  { ID: "70", code: "CNSTD", name: "DELIVERIES: CONSTRUCTION, UTILITY SITES" },
  { ID: "65", code: "PLTZD", name: "PALLETIZING" },
  { ID: "60", code: "AFHRD", name: "DELIVERIES: AFTER BUSINESS HOUR" },
  { ID: "58", code: "AFHRP", name: "PICKUPS: AFTER BUSINESS HOUR" },
  { ID: "52", code: "BLNDS", name: "BLIND SHIPMENTS" },
  { ID: "51", code: "ASSIT", name: "DRIVER ASSIST LOADING/UNLOADING" },
  { ID: "50", code: "STSEG", name: "SORTING OR SEGREGATING" },
  { ID: "49", code: "LIMTD", name: "DELIVERIES: RESTRICTED/LIMITED ACCESS" },
  { ID: "46", code: "ISLND", name: "ISLAND DELIVERY" },
  { ID: "41", code: "EXBID", name: "DELIVERIES - EXHIBITION SITES" },
  { ID: "40", code: "CNSTP", name: "PICKUPS: CONSTRUCTION, UTILITY SITES" },
  { ID: "38", code: "WKNDL", name: "DELIVERIES: SATURDAYS, SUNDAY" },
  { ID: "34", code: "APPTD", name: "APPOINTMENT REQUIRED" },
  { ID: "32", code: "EXVAL", name: "EXCESSIVE VALUE/INSURANCE" },
  { ID: "31", code: "CODCG", name: "C.O.D. SHIPMENTS" },
  { ID: "30", code: "WKNPU", name: "PICKUPS: SATURDAYS, SUNDAY" },
  { ID: "29", code: "RESDD", name: "DELIVERIES: RESIDENTIAL/NON-COMMERCIAL" },
  { ID: "28", code: "LGPUP", name: "LIFTGATE PICKUP" },
  { ID: "27", code: "LGDEL", name: "LIFTGATE DELIVERY" },
  { ID: "26", code: "INSDD", name: "INSIDE DELIVERY" }
];

interface AccessorialOption {
  ID: string;
  code: string;
  name: string;
}

interface AccessorialMultipleSelectorProps {
  value?: AccessorialOption[];
  onChange?: (value: AccessorialOption[]) => void;
  placeholder?: string;
  required?: boolean;
  defaultValue?: AccessorialOption[];
  className?: string;
  disabled?: boolean;
}

export function AccessorialMultipleSelector({
  value = [],
  onChange,
  placeholder = 'Select accessorial charges',
  required = false,
  defaultValue,
  className,
  disabled = false,
}: AccessorialMultipleSelectorProps) {
  const [open, setOpen] = React.useState(false);
  const [searchTerm, setSearchTerm] = React.useState('');
  const hasSetDefault = React.useRef(false);

  // Set default value on mount
  React.useEffect(() => {
    if (defaultValue && defaultValue.length > 0 && value.length === 0 && onChange && !hasSetDefault.current) {
      onChange(defaultValue);
      hasSetDefault.current = true;
    }
  }, [defaultValue, value, onChange]);

  // Filter options based on search term
  const filteredOptions = ACCESSORIAL_OPTIONS.filter(option =>
    option.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    option.code.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleSelect = (option: AccessorialOption) => {
    if (!onChange || disabled) return;

    const isSelected = value.some(item => item.code === option.code);
    
    if (isSelected) {
      // Remove from selection
      const newValue = value.filter(item => item.code !== option.code);
      onChange(newValue);
    } else {
      // Add to selection
      const newValue = [...value, option];
      onChange(newValue);
    }
  };

  const handleRemove = (optionToRemove: AccessorialOption) => {
    if (!onChange || disabled) return;
    const newValue = value.filter(item => item.code !== optionToRemove.code);
    onChange(newValue);
  };

  const handleClearAll = () => {
    if (onChange && !disabled) {
      onChange([]);
    }
  };

  return (
    <div className={cn("w-full", className)}>
      {/* Selected items display */}
      {value.length > 0 && (
        <div className="flex flex-wrap items-center gap-2 mb-2">
          {value.map((option) => (
            <Badge
              key={option.code}
              variant="secondary"
              className={cn(
                "bg-item-bg-card text-item-gray-200 hover:bg-item-purple-600 text-xs font-normal transition-all duration-200",
                disabled && "opacity-50 cursor-not-allowed"
              )}
            >
              {option.name}
              {!disabled && (
                <button
                  type="button"
                  onClick={() => handleRemove(option)}
                  className="ml-2 hover:bg-item-purple-500 rounded-full p-0.5 transition-all duration-200"
                >
                  <X className="h-3 w-3" />
                </button>
              )}
            </Badge>
          ))}
          {!disabled && (
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={handleClearAll}
              className="h-6 px-2 text-xs text-item-gray-400 hover:text-item-purple-300 hover:bg-transparent transition-all duration-200"
            >
              Clear all
            </Button>
          )}
        </div>
      )}

      {/* Selector */}
      <Popover open={open} onOpenChange={disabled ? undefined : setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            disabled={disabled}
            className={cn(
              "w-full justify-between bg-item-bg-card border-item-gray-700 text-white hover:bg-item-purple-700 hover:text-white transition-all duration-200",
              value.length === 0 && "text-item-gray-400",
              disabled && "opacity-50 cursor-not-allowed hover:bg-item-bg-card hover:text-white",
              className
            )}
            title={value.length > 0 
              ? `${value.length} accessorial charge${value.length > 1 ? 's' : ''} selected`
              : placeholder
            }
          >
            <span className="truncate min-w-0">
              {value.length > 0 
                ? `${value.length} accessorial charge${value.length > 1 ? 's' : ''} selected`
                : placeholder
              }
            </span>
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-full p-0 bg-item-bg-card border-item-gray-700" style={{ width: 'var(--radix-popover-trigger-width)' }}>
          <div className="p-2">
            <input
              type="text"
              placeholder="Search accessorial charges..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full px-3 py-2 bg-item-bg-card text-white border border-item-gray-600 rounded text-sm transition-all duration-200 focus:border-item-purple-500"
            />
          </div>
          <div className="max-h-64 overflow-auto">
            {filteredOptions.length === 0 ? (
              <div className="text-item-gray-400 py-6 text-center text-sm">
                No accessorial charges found.
              </div>
            ) : (
              filteredOptions.map((option) => {
                const isSelected = value.some(item => item.code === option.code);
                return (
                  <div
                    key={option.code}
                    onClick={() => !disabled && handleSelect(option)}
                    className={cn(
                      "flex items-center justify-between px-3 py-2 text-white hover:bg-item-purple-700 transition-all duration-200",
                      disabled ? "cursor-not-allowed opacity-50" : "cursor-pointer"
                    )}
                  >
                    <div className="flex items-center flex-1">
                      <Check
                        className={cn(
                          "mr-2 h-4 w-4",
                          isSelected ? "opacity-100" : "opacity-0"
                        )}
                      />
                      <div className="flex flex-col flex-1">
                        <span className="text-sm">{option.name}</span>
                        <span className="text-xs text-item-gray-400 font-mono">{option.code}</span>
                      </div>
                    </div>
                  </div>
                );
              })
            )}
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
} 