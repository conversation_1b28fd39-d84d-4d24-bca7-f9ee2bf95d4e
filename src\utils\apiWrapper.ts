import { NextRequest, NextResponse } from 'next/server';
import { setRequestContext, clearRequestContext } from './requestContext';
import { nanoid } from 'nanoid';
import { getUserIdFromRequest } from './authUtils';

type ApiHandler = (req: NextRequest, requestId: string) => Promise<NextResponse | Response>;

/**
 * 创建处理请求上下文的高阶函数
 * @param handler API处理函数
 * @returns 包装后的处理函数
 */
export function withRequestContext(handler: ApiHandler): (req: NextRequest) => Promise<NextResponse | Response> {
  return async (req: NextRequest) => {
    // 生成唯一的请求ID
    const requestId = nanoid();

    // 获取请求头信息
    const authHeader = req.headers.get('Authorization');
    const tenantIdHeader = req.headers.get('x-tenant-id');
    const facilityIdHeader = req.headers.get('x-facility-id');
    const fmsTokenHeader = req.headers.get('fms-token');
    const portalTokenHeader = req.headers.get('portal-token');
    const customerCodeHeader = req.headers.get('customer-code');
    const ufCustomersHeader = req.headers.get('uf-customers');
    const utCustomersHeader = req.headers.get('ut-customers');
    const biTokenHeader = req.headers.get('bi-token');
    const timezoneHeader = req.headers.get('x-timezone');

    console.log("Headers:", authHeader, tenantIdHeader, facilityIdHeader, ufCustomersHeader, utCustomersHeader, biTokenHeader, timezoneHeader);

    try {
      // Try to get user ID (if authenticated)
      const userId = await getUserIdFromRequest(req) || 'anonymous';
      console.log(`[RequestContext] Got user ID for request ${requestId}: ${userId}`);

      // Initialize request context
      const ufCustomers = ufCustomersHeader || '[]';
      const utCustomers = utCustomersHeader || '[]';
      const context = {
        authorization: authHeader || undefined,
        fmsToken: fmsTokenHeader || undefined,
        portalToken: portalTokenHeader || undefined,
        tenantId: tenantIdHeader || undefined,
        facilityId: facilityIdHeader || undefined,
        userId,
        customerCode: customerCodeHeader || undefined,
        ufCustomers,
        utCustomers,
        biToken: biTokenHeader || undefined,
        timezone: timezoneHeader || undefined
      };

      setRequestContext(requestId, context);

      console.log(`[RequestContext] Created context for request ${requestId}, path: ${req.nextUrl.pathname}`);
      console.log(`[RequestContext] Context details:`, JSON.stringify(context));

      // 直接将原始请求和requestId传递给处理函数
      const response = await handler(req, requestId);


      return response;

    } catch (error) {

      return NextResponse.json(
        { error: 'Internal Server Error', details: error instanceof Error ? error.message : String(error) },
        { status: 500 }
      );
    }
  };
}