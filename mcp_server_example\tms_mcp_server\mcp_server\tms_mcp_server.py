import asyncio
import logging
import os
import sys
import traceback
from typing import Dict, Any, Optional, List, Union
from contextlib import asynccontextmanager
from collections.abc import AsyncIterator
from dataclasses import dataclass

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
)
logger = logging.getLogger("tms_mcp_server")

# Add parent directory to PATH to import tools
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
print(f"Current working directory: {os.getcwd()}")
print(f"Added to path: {parent_dir}")
sys.path.append(parent_dir)

# Try to import FastMCP and TMS tools
try:
    from fastmcp import FastMCP, Context
    logger.info("Successfully imported MCP FastMCP")
except ImportError as e:
    # If modules not found, try to install them
    logger.warning(f"Failed to import required modules: {e}, trying to install...")
    try:
        import subprocess
        subprocess.check_call([sys.executable, "-m", "pip", "install", "mcp[cli]"])
        from fastmcp import FastMCP, Context
        logger.info("Successfully installed and imported MCP")
    except Exception as install_error:
        logger.error(f"Failed to install MCP: {install_error}")
        traceback.print_exc()
        sys.exit(1)

# Ensure config file exists
if not os.path.exists(os.path.join(parent_dir, "config.py")):
    try:
        with open(os.path.join(parent_dir, "config.py"), "w", encoding="utf-8") as f:
            f.write("""import os
from dotenv import load_dotenv

# Try to load environment variables from .env file (if exists)
try:
    load_dotenv()
except ImportError:
    pass

# TMS Configuration
TMS_BASE_URL = os.getenv("TMS_BASE_URL", "https://tms-staging.item.com/api")
""")
        logger.info("Created default config.py file")
    except Exception as config_error:
        logger.error(f"Failed to create config.py: {config_error}")

# Try to import TMS tools
try:
    from tools.tms import TMSTools
    logger.info("Successfully imported TMS tools")
except ImportError as e:
    logger.error(f"Failed to import TMS tools: {e}")
    sys.exit(1)

# Define TMSContext type for storing global TMS tools instance
@dataclass
class TMSContext:
    tms_tools: Optional[TMSTools] = None

# Create server lifecycle manager
@asynccontextmanager
async def tms_lifespan(server: FastMCP) -> AsyncIterator[TMSContext]:
    """Initialize TMS tools instance and clean up resources when server stops"""
    logger.info("Starting TMS MCP Server lifecycle")
    
    # Initialize TMS tools
    try:
        logger.info("Initializing TMS tools")
        tms_tools = TMSTools()
        logger.info("TMS tools initialized successfully")
        
        # Create context object
        context = TMSContext(tms_tools=tms_tools)
        
        # Add context directly to server application state for access in tool functions
        if hasattr(server, 'app'):
            if not hasattr(server.app, 'state'):
                server.app.state = type('State', (object,), {})
            server.app.state.tms_context = context
        
        yield context
    except Exception as e:
        logger.error(f"Failed to initialize TMS tools: {e}")
        traceback.print_exc()
        # Continue even if initialization fails to avoid server startup failure
        yield TMSContext(tms_tools=None)
    finally:
        # Clean up resources when server shuts down
        logger.info("Shutting down TMS MCP Server")

# Create FastMCP instance with specified name and lifecycle manager
mcp = FastMCP(
    name="TMS MCP Server", 
    version="1.0.0",
    description="Transport Management System MCP Server",
    lifespan=tms_lifespan,
    dependencies=["aiohttp", "dotenv"]  # Add dependencies for easier installation
)

@mcp.tool()
async def find_tms_api(query: str, top_k: int = 5, ctx: Context = None) -> Dict[str, Any]:
    """
    Search for matching TMS API endpoints
    
    Args:
        query: Search query text
        top_k: Number of results to return
        ctx: MCP context object
        
    Returns:
        List of matching APIs
    """
    logger.info(f"Calling find_tms_api with query: {query}, top_k: {top_k}")
    
    # Get TMS tools instance - try different context attribute names
    tms_tools = None
    if ctx:
        # Check ctx itself and possible nested locations
        if hasattr(ctx, 'app') and hasattr(ctx.app, 'state') and hasattr(ctx.app.state, 'tms_context'):
            tms_tools = ctx.app.state.tms_context.tms_tools
        elif hasattr(ctx, 'lifespan_ctx'):
            tms_tools = getattr(ctx.lifespan_ctx, 'tms_tools', None)
        elif hasattr(ctx, 'tms_context'):
            tms_tools = ctx.tms_context.tms_tools
        elif hasattr(ctx, 'tms_tools'):
            tms_tools = ctx.tms_tools
        # Log context structure for debugging
        logger.debug(f"Context structure: {dir(ctx)}")
    
    # If cannot get from context, create a new instance
    if not tms_tools:
        logger.warning("Could not get TMS tools from context, creating a new instance")
        try:
            tms_tools = TMSTools()
        except Exception as e:
            error_msg = f"Failed to create TMS tools: {e}"
            logger.error(error_msg)
            raise Exception(error_msg)
    
    try:
        # Call find_tms_api method of TMS tools
        result = await tms_tools.find_tms_api(query=query, top_k=top_k)
        # Log processing, avoid string type errors
        if isinstance(result, dict):
            apis_count = len(result.get('apis', []))
            logger.info(f"find_tms_api found {apis_count} APIs")
        else:
            logger.info(f"find_tms_api completed successfully, result type: {type(result).__name__}")
        return result
    except Exception as e:
        logger.error(f"Error in find_tms_api execution: {e}")
        traceback.print_exc()
        raise

@mcp.tool()
async def call_tms_api(
    path: str, 
    method: str, 
    params: Union[Dict[str, Any], List[Any]] = {}, 
    headers: Dict[str, Any] = None,
    ctx: Context = None
) -> Dict[str, Any]:
    """
    Call a specific TMS API
    
    Args:
        path: API path
        method: HTTP method (GET, POST, PUT, DELETE)
        params: API parameters, can be either a dictionary or array depending on API requirements
        headers: Request headers
        ctx: MCP context object
        
    Returns:
        API call result
    """
    logger.info(f"Calling call_tms_api with path: {path}, method: {method}, params: {params}")
    
    # Validate headers must contain necessary information
    if not headers or not isinstance(headers, dict):
        logger.error("Missing required headers for API call")
        return {
            "success": False,
            "error": "Missing headers",
            "message": "API call requires headers with TMS-token, Company-id"
        }
    
    # Get TMS tools instance - try different context attribute names
    tms_tools = None
    if ctx:
        # Check ctx itself and possible nested locations
        if hasattr(ctx, 'app') and hasattr(ctx.app, 'state') and hasattr(ctx.app.state, 'tms_context'):
            tms_tools = ctx.app.state.tms_context.tms_tools
        elif hasattr(ctx, 'lifespan_ctx'):
            tms_tools = getattr(ctx.lifespan_ctx, 'tms_tools', None)
        elif hasattr(ctx, 'tms_context'):
            tms_tools = ctx.tms_context.tms_tools
        elif hasattr(ctx, 'tms_tools'):
            tms_tools = ctx.tms_tools
    
    # If cannot get from context, create a new instance
    if not tms_tools:
        logger.warning("Could not get TMS tools from context, creating a new instance")
        try:
            tms_tools = TMSTools()
        except Exception as e:
            error_msg = f"Failed to create TMS tools: {e}"
            logger.error(error_msg)
            raise Exception(error_msg)
    
    try:
        # Call call_tms_api method of TMS tools
        result = await tms_tools.call_tms_api(
            path=path,
            method=method,
            params=params,
            headers=headers
        )
        # Safely log results
        logger.info(f"call_tms_api completed successfully, result type: {type(result).__name__}")
        if isinstance(result, dict):
            status = "Success" if result.get('success') else "Failure"
            logger.info(f"call_tms_api call {status}, status_code: {result.get('status')}")
        return result
    except Exception as e:
        logger.error(f"Error in call_tms_api execution: {e}")
        traceback.print_exc()
        raise

# Add TMS tools guide prompt
@mcp.prompt()
def tms_guide() -> str:
    """TMS tools usage guide prompt"""
    try:
        # Try to read prompt from file
        tms_prompt_path = os.path.join(parent_dir, "prompts", "tms_prompt.py")
        logger.info(f"Reading TMS prompt from: {tms_prompt_path}")
        
        if os.path.exists(tms_prompt_path):
            try:
                with open(tms_prompt_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    # Extract TMS_TOOLS_PROMPT variable value
                    if "TMS_TOOLS_PROMPT = '''" in content:
                        start_index = content.find("TMS_TOOLS_PROMPT = '''") + len("TMS_TOOLS_PROMPT = '''")
                        end_index = content.find("'''", start_index)
                        if end_index > start_index:
                            prompt_content = content[start_index:end_index]
                            logger.info(f"Successfully extracted TMS prompt content, length: {len(prompt_content)}")
                            return prompt_content
            except Exception as read_error:
                logger.error(f"Error reading prompt file: {read_error}")
    except Exception as e:
        logger.error(f"Error creating prompt: {e}")
    
    # If reading fails or file not found, return default prompt
    return """
    ## Guide for TMS Tools
    
    TMS Tools provides functionality for managing transport operations.
    
    Available tools:
    
    1. find_tms_api - Search for relevant TMS API endpoints
       Usage: Use this tool to find API endpoints that match your query
       
    2. call_tms_api - Call a specific TMS API endpoint
       Usage: After finding the right API, use this to execute the call
       
    When using these tools, follow these steps:
    1. First, use find_tms_api to discover the appropriate API endpoint
    2. Review the results to find the best matching API
    3. Use call_tms_api with the exact path and parameters from the API details
    
    Example workflow:
    1. find_tms_api(query="query transport list")
    2. Review results to find transport API
    3. call_tms_api(path="/transport/search", method="POST", params={"transportId": "123"})
    """

# Add resource node providing TMS API documentation
@mcp.resource("tms://api-docs")
def get_tms_api_docs() -> str:
    """Get TMS API documentation summary"""
    return """
    # TMS API Documentation Summary
    
    TMS system provides the following main API categories:
    
    ## Transport Related
    - /transport/search - Search transports
    - /transport/{id} - Get transport details
    - /transport/create - Create transport
    
    ## Order Related
    - /order/search - Search orders
    - /order/{id} - Get order details
    - /order/commit - Commit order
    
    ## Route Related
    - /route/search - Search routes
    - /route/{id} - Get route details
    
    Use the find_tms_api tool to search for more detailed APIs.
    """

# Add resource node providing TMS tools usage guide
@mcp.resource("tms://tools-guide")
def get_tms_tools_guide() -> str:
    """Get TMS tools usage guide"""
    return """
    # TMS Tools Usage Guide
    
    ## find_tms_api
    
    Used to search for matching TMS API endpoints.
    
    ```python
    await find_tms_api(query="search transports", top_k=5)
    ```
    
    Parameters:
    - query: Search query text
    - top_k: Number of results to return
    
    Returns:
    List of matching APIs including path, method, parameters, etc.
    
    ## call_tms_api
    
    Used to call a specific TMS API.
    
    ```python
    await call_tms_api(
        path="/transport/search",
        method="POST",
        params={"transportId": "12345"},
        headers={"Authorization": "Bearer YOUR_TOKEN"}
    )
    ```
    
    Parameters:
    - path: API path
    - method: HTTP method (GET, POST, PUT, DELETE)
    - params: API parameters
    - headers: Custom request headers, including authorization token (e.g., {"Authorization": "Bearer YOUR_TOKEN"})
    
    Returns:
    API call result
    """

if __name__ == "__main__":
    # Parse command line arguments
    import argparse
    parser = argparse.ArgumentParser(description="TMS MCP Server")
    parser.add_argument("--host", default="127.0.0.1", help="Bind socket to this host")
    parser.add_argument("--port", type=int, default=8003, help="Bind socket to this port")
    parser.add_argument("--transport", choices=["stdio", "sse"], default="sse", help="Transport protocol (stdio or sse)")
    args = parser.parse_args()
    
    logger.info(f"Starting TMS MCP server with {args.transport} transport")
    
    # 根据传输模式选择不同的启动方式
    if args.transport == "sse":
        # 使用 starlette 和 uvicorn 运行 SSE 服务器
        import uvicorn
        from starlette.applications import Starlette
        from starlette.routing import Mount
        
        # 创建一个 Starlette 应用并挂载 MCP 的 SSE 应用
        app = Starlette(routes=[Mount("/", app=mcp.sse_app())])
        
        # 使用 uvicorn 运行
        uvicorn.run(
            app, 
            host=args.host, 
            port=args.port,
            timeout_keep_alive=300,  # 保持连接超时
            timeout_graceful_shutdown=300  # 优雅关闭超时
        )
    else:
        # 对于 stdio 模式，直接使用 run 方法
        mcp.run(transport="stdio") 