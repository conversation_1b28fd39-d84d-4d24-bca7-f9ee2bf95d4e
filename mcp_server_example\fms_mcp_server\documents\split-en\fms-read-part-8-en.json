{"openapi": "3.0.1", "info": {"title": "FMS", "description": "", "version": "1.0.0"}, "paths": {"/fms-platform-dispatch-management/dispatching-orders/search": {"post": {"summary": "Dispatch Dashboard page query Order", "deprecated": false, "description": "", "tags": ["DispatchingOrders"], "parameters": [], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/GetDispatchOrderRequest"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/DispatchDashboardCardDtoPagedResultDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/compare/{orderKey}": {"get": {"summary": "Query sub status inconsistent order", "deprecated": false, "description": "", "tags": ["FixOrder"], "parameters": [{"name": "orderKey", "in": "path", "description": "", "required": true, "example": 0, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/Diff"}, "properties": {}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/not-match": {"get": {"summary": "Query sub status inconsistent order", "deprecated": false, "description": "", "tags": ["FixOrder"], "parameters": [], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DOEntity"}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/Home/HealthCheck": {"get": {"summary": "AI: Health check endpoint, allowing anonymous access", "deprecated": false, "description": "", "tags": ["Home"], "parameters": [], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "string"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/Home/Ding": {"get": {"summary": "AI: Simple echo endpoint that allows anonymous access", "deprecated": false, "description": "", "tags": ["Home"], "parameters": [{"name": "dong", "in": "query", "description": "Enter string", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "string"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/route-engine-dispatch/linehaul/list": {"post": {"summary": "AI: Get the total list of <PERSON><PERSON>ul Dispatch", "deprecated": false, "description": "", "tags": ["LinehaulDispatch"], "parameters": [], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/LinehaulDispatchTotalInputDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/LinehaulDispatchTotalListOutputDto"}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/route-engine-dispatch/linehaul/status": {"get": {"summary": "AI: Get task status", "deprecated": false, "description": "", "tags": ["LinehaulDispatch"], "parameters": [], "responses": {"200": {"description": "Success", "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/route-engine-dispatch/linehaul/loadstatus": {"get": {"summary": "AI: Get loading status", "deprecated": false, "description": "", "tags": ["LinehaulDispatch"], "parameters": [], "responses": {"200": {"description": "Success", "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/route-engine-dispatch/linehaul/get-linehaul": {"post": {"summary": "AI: Get Linehaul Task List", "deprecated": false, "description": "", "tags": ["LinehaulDispatch"], "parameters": [], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/GetLinehaulListInputDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/GetLinehaulListOutputDto"}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/get-dispatch/all": {"post": {"summary": "/fms-platform-dispatch-management/get-dispatch/all", "deprecated": false, "description": "", "tags": ["OrderGetDispatch"], "parameters": [], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/Fix1Request"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/LhTripTaskRpcDto"}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/linear-feet": {"get": {"summary": "Order matrix query", "deprecated": false, "description": "", "tags": ["Orders"], "parameters": [], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/OrderMatrixTab"}, "properties": {}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/fix/orders/linehual-tasks": {"get": {"summary": "AI: Order list page - pagination query", "deprecated": false, "description": "", "tags": ["Orders"], "parameters": [], "responses": {"200": {"description": "Success", "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/linear-feet2": {"get": {"summary": "Order matrix query", "deprecated": false, "description": "", "tags": ["Orders"], "parameters": [], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/OrderMatrixTab"}, "properties": {}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/lh/orders/search": {"post": {"summary": "Orders of Linehaul Dispatch menu", "deprecated": false, "description": "", "tags": ["Orders"], "parameters": [], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/OrderGetPagedListRequest"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/OrderListItemDtoPagedResultDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/samples/2": {"get": {"summary": "/samples/2", "deprecated": false, "description": "", "tags": ["<PERSON><PERSON>"], "parameters": [], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/OrderListItemDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/TaskAppointment/GetTaskActiveAppointment": {"get": {"summary": "Get reservation details", "deprecated": false, "description": "", "tags": ["TaskAppointment"], "parameters": [{"name": "orderNo", "in": "query", "description": "", "required": false, "schema": {"type": "string"}}, {"name": "appointmentType", "in": "query", "description": "1：Pickup 2:Delivery 3:Service 4:DeliveryAndService", "required": false, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TaskAppointmentDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/TaskAppointment/GetPendingAppointmentServiceTaskByShipmentOrderNo": {"get": {"summary": "Get the ServiceTaskNo that can be booked for the current Delivery Task", "deprecated": false, "description": "", "tags": ["TaskAppointment"], "parameters": [{"name": "shipmentOrderNo", "in": "query", "description": "", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "integer", "format": "int64"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/TaskAppointment/GetAppointmentInfo": {"get": {"summary": "Get reservation information", "deprecated": false, "description": "", "tags": ["TaskAppointment"], "parameters": [{"name": "order_no", "in": "query", "description": "", "required": false, "schema": {"type": "string"}}, {"name": "appointment_type", "in": "query", "description": "", "required": false, "schema": {"$ref": "#/components/schemas/TaskAppointmentTypeEnum"}}, {"name": "all_select", "in": "query", "description": "", "required": false, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TaskAppointmentInfo"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/TaskAppointment/GetAppointmentInfoList": {"post": {"summary": "Internal batch acquisition appointment", "deprecated": false, "description": "", "tags": ["TaskAppointment"], "parameters": [], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/AppointmentSelectDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TaskAppointmentInfo"}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/task/get-task-list": {"post": {"summary": "get-task-list", "deprecated": false, "description": "Query task information, query according to the task list, the incoming parameters are do#, pro#, task#, etc., and the outgoing parameters are basic task information.", "tags": ["TaskList"], "parameters": [{"name": "Content-Type", "in": "header", "description": "", "required": false, "example": "application/json", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TaskGetListRequestDto"}, "example": ""}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TaskOutPutDtoPagedResultDto"}}}, "headers": {}}}, "security": [{"apikey-header-fms-token": []}]}}}, "components": {"schemas": {"CarrierTypeEnum": {"enum": [2, 6, 7, 8], "type": "integer", "format": "int32"}, "AppointmentAdditionsServiceInformation": {"type": "object", "properties": {"business_client": {"type": "string", "nullable": true}, "service_level": {"$ref": "#/components/schemas/ServiceLevelEnum"}, "rma": {"type": "string", "nullable": true}}, "additionalProperties": false}, "AppointmentAdditionsServiceProduct": {"type": "object", "properties": {"item_code": {"type": "string", "nullable": true}, "item_name": {"type": "string", "nullable": true}, "item_type": {"type": "string", "nullable": true}, "item_quantity": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "AppointmentSelectDto": {"type": "object", "properties": {"order_keys": {"type": "array", "items": {"type": "integer", "format": "int64"}, "nullable": true}}, "additionalProperties": false}, "DispatchDashboardCardDto": {"type": "object", "properties": {"dispatch_type": {"type": "integer", "format": "int32"}, "task_no": {"type": "integer", "format": "int64"}, "tracking_no": {"type": "string", "nullable": true}, "task_dst_terminal": {"type": "string", "nullable": true}, "task_org_terminal": {"type": "string", "nullable": true}, "task_status": {"type": "integer", "format": "int32"}, "task_type": {"type": "integer", "format": "int32"}, "task_type_group": {"type": "string", "nullable": true}, "order_no": {"type": "string", "nullable": true}, "order_status": {"type": "string", "nullable": true}, "service_level": {"type": "string", "nullable": true}, "order_current_location": {"type": "string", "nullable": true}, "shipment_type": {"type": "string", "nullable": true}, "package_count": {"type": "integer", "format": "int32"}, "weight": {"type": "number", "format": "double"}, "weight_uom": {"type": "string", "nullable": true}, "shipper_name": {"type": "string", "nullable": true}, "consignee_name": {"type": "string", "nullable": true}, "customer_name": {"type": "string", "nullable": true}, "from_lat": {"type": "number", "format": "double"}, "from_lng": {"type": "number", "format": "double"}, "to_lat": {"type": "number", "format": "double"}, "to_lng": {"type": "number", "format": "double"}, "city": {"type": "string", "nullable": true}, "state": {"type": "string", "nullable": true}, "bill_to": {"type": "string", "nullable": true}, "eta": {"type": "string", "format": "date-time"}, "etd": {"type": "string", "format": "date-time"}, "appointment_date": {"type": "string", "format": "date-time"}, "assign_status": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "DispatchDashboardCardDtoPagedResultDto": {"type": "object", "properties": {"total_count": {"type": "integer", "format": "int64"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/DispatchDashboardCardDto"}, "nullable": true}}, "additionalProperties": false}, "GetLinehaulListInputDto": {"type": "object", "properties": {"linehaul_no": {"type": "array", "items": {"type": "integer", "format": "int64"}, "nullable": true}, "org_terminal": {"type": "array", "items": {"type": "string"}, "nullable": true}, "dst_terminal": {"type": "array", "items": {"type": "string"}, "nullable": true}, "delivery_appt_date": {"type": "string", "nullable": true}, "date_type": {"type": "integer", "format": "int32", "nullable": true}, "from_date": {"type": "string", "nullable": true}, "to_date": {"type": "string", "nullable": true}}, "additionalProperties": false}, "GetLinehaulListOrderOutputDto": {"type": "object", "properties": {"order_no": {"type": "string", "nullable": true}, "tracking_no": {"type": "string", "nullable": true}, "shipper_name": {"type": "string", "nullable": true}, "consignee_name": {"type": "string", "nullable": true}, "org_terminal": {"type": "string", "nullable": true}, "dst_terminal": {"type": "string", "nullable": true}, "bill_to_code": {"type": "string", "nullable": true}, "bill_to_name": {"type": "string", "nullable": true}, "consignee_terminal": {"type": "string", "nullable": true}, "consignee_state": {"type": "string", "nullable": true}, "consignee_zipcode": {"type": "string", "nullable": true}, "pallets": {"type": "integer", "format": "int32"}, "weight": {"type": "string", "nullable": true}, "is_dispatch_trip": {"type": "integer", "format": "int32"}, "consignee_city": {"type": "string", "nullable": true}, "eta": {"type": "string", "format": "date-time"}, "est_rev": {"type": "number", "format": "double"}}, "additionalProperties": false}, "GetLinehaulListOutputDto": {"type": "object", "properties": {"is_dispatch_trip": {"type": "integer", "format": "int32"}, "linehaul_no": {"type": "integer", "format": "int64"}, "org_terminal": {"type": "string", "nullable": true}, "dst_terminal": {"type": "string", "nullable": true}, "delivery_appt_date": {"type": "string", "nullable": true}, "appointment_time_from": {"type": "string", "nullable": true}, "appointment_time_to": {"type": "string", "nullable": true}, "pallets": {"type": "integer", "format": "int32"}, "weight": {"type": "string", "nullable": true}, "create_date": {"type": "string", "nullable": true}, "dst_terminal_street": {"type": "string", "nullable": true}, "dst_terminal_city": {"type": "string", "nullable": true}, "est_depature_date": {"type": "string", "nullable": true}, "est_depature_time_from": {"type": "string", "nullable": true}, "est_depature_time_to": {"type": "string", "nullable": true}, "est_arrival_date": {"type": "string", "nullable": true}, "est_arrival_time_from": {"type": "string", "nullable": true}, "est_arrival_time_to": {"type": "string", "nullable": true}, "order_list": {"type": "array", "items": {"$ref": "#/components/schemas/GetLinehaulListOrderOutputDto"}, "nullable": true}, "trip_no": {"type": "integer", "format": "int64"}, "linehaul_status_text": {"type": "string", "nullable": true}}, "additionalProperties": false}, "GetDispatchOrderRequest": {"type": "object", "properties": {"dispatch_type": {"type": "integer", "format": "int32"}, "sorting": {"type": "string", "nullable": true}, "order_nos": {"type": "array", "items": {"type": "string"}, "nullable": true}, "trackingno_or_pronos": {"type": "array", "items": {"type": "string"}, "nullable": true}, "status": {"type": "array", "items": {"type": "string"}, "nullable": true}, "task_types": {"type": "array", "items": {"type": "string"}, "nullable": true}, "shpr_trms": {"type": "array", "items": {"type": "string"}, "nullable": true}, "cnse_trms": {"type": "array", "items": {"type": "string"}, "nullable": true}, "svcs_trms": {"type": "array", "items": {"type": "string"}, "nullable": true}, "shipment_types": {"type": "array", "items": {"type": "string"}, "nullable": true}, "shipper_state": {"type": "string", "nullable": true}, "shipper_city": {"type": "string", "nullable": true}, "shipper_names": {"type": "array", "items": {"type": "string"}, "nullable": true}, "service_levels": {"type": "array", "items": {"type": "string"}, "nullable": true}, "consignee_state": {"type": "string", "nullable": true}, "consignee_city": {"type": "string", "nullable": true}, "consignee_names": {"type": "array", "items": {"type": "string"}, "nullable": true}, "skip": {"type": "integer", "format": "int32"}, "max_result_count": {"type": "integer", "format": "int32"}, "pickup_appointment_from": {"type": "string", "format": "date-time", "nullable": true}, "pickup_appointment_to": {"type": "string", "format": "date-time", "nullable": true}, "delivery_appointment_from": {"type": "string", "format": "date-time", "nullable": true}, "delivery_appointment_to": {"type": "string", "format": "date-time", "nullable": true}, "have_pickup_appointment": {"type": "boolean"}, "delivery_appointment_is_null": {"type": "boolean"}}, "additionalProperties": false}, "DOEntity": {"type": "object", "properties": {"order_key": {"type": "integer", "format": "int64"}, "order_sub_status": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "Fix1Request": {"type": "object", "properties": {"order_keys": {"type": "array", "items": {"type": "integer", "format": "int64"}, "nullable": true}, "is_remove_route_log": {"type": "boolean"}}, "additionalProperties": false}, "LinehaulDispatchTotalInputDto": {"type": "object", "properties": {"current_terminal_code": {"type": "string", "nullable": true}, "est_departure_date_from": {"type": "string", "nullable": true}, "est_departure_date_to": {"type": "string", "nullable": true}, "est_arrival_date_from": {"type": "string", "nullable": true}, "est_arrival_date_to": {"type": "string", "nullable": true}, "trip_date_from": {"type": "string", "nullable": true}, "trip_date_to": {"type": "string", "nullable": true}, "lh_create_date_from": {"type": "string", "nullable": true}, "lh_create_date_to": {"type": "string", "nullable": true}, "org_terminal": {"type": "array", "items": {"type": "string"}, "nullable": true}, "dst_terminal": {"type": "array", "items": {"type": "string"}, "nullable": true}, "linehaul_status": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}, "load_status": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}, "actual_departure_date_from": {"type": "string", "nullable": true}, "actual_departure_date_to": {"type": "string", "nullable": true}, "actual_arrival_date_from": {"type": "string", "nullable": true}, "actual_arrival_date_to": {"type": "string", "nullable": true}, "lh_nos": {"type": "array", "items": {"type": "integer", "format": "int64"}, "nullable": true}, "tracking_nos": {"type": "array", "items": {"type": "string"}, "nullable": true}, "do_nos": {"type": "array", "items": {"type": "string"}, "nullable": true}, "trip_nos": {"type": "array", "items": {"type": "integer", "format": "int64"}, "nullable": true}, "route_ids": {"type": "array", "items": {"type": "string"}, "nullable": true}, "carrier_codes": {"type": "array", "items": {"type": "string"}, "nullable": true}, "driver_codes": {"type": "array", "items": {"type": "string"}, "nullable": true}, "is_inbound": {"type": "integer", "format": "int32", "nullable": true}, "is_outbound": {"type": "integer", "format": "int32", "nullable": true}, "tab": {"type": "string", "nullable": true}, "page_number": {"maximum": 2147483647, "minimum": 0, "type": "integer", "format": "int32"}, "page_size": {"maximum": 2147483647, "minimum": 1, "type": "integer", "format": "int32"}}, "additionalProperties": false}, "LinehaulDispatchTotalListOutputDto": {"type": "object", "properties": {"lh_no": {"type": "string", "nullable": true}, "lh_status": {"type": "integer", "format": "int32"}, "load_status": {"type": "integer", "format": "int32"}, "trip_no": {"type": "string", "nullable": true}, "route_name": {"type": "string", "nullable": true}, "org_terminal": {"type": "string", "nullable": true}, "dest_terminal": {"type": "string", "nullable": true}, "est_departure_date_from": {"type": "string", "nullable": true}, "est_departure_date_to": {"type": "string", "nullable": true}, "est_arrival_date_from": {"type": "string", "nullable": true}, "est_arrival_date_to": {"type": "string", "nullable": true}, "driver_code": {"type": "string", "nullable": true}, "driver_name": {"type": "string", "nullable": true}, "carrier_code": {"type": "string", "nullable": true}, "carrier_name": {"type": "string", "nullable": true}, "trip_date": {"type": "string", "nullable": true}, "pallet": {"type": "string", "nullable": true}, "weight": {"type": "string", "nullable": true}, "last_update_by": {"type": "string", "nullable": true}, "lh_create_date": {"type": "string", "nullable": true}}, "additionalProperties": false}, "OrderGetPagedListRequest": {"type": "object", "properties": {"matrix_enabled": {"type": "integer", "format": "int32"}, "max_result_count": {"type": "integer", "format": "int32"}, "skip": {"type": "integer", "format": "int32"}, "sorting": {"type": "string", "nullable": true}, "bill_to_name": {"type": "string", "nullable": true}, "consignee_state": {"type": "string", "nullable": true}, "consignee_terminals": {"type": "array", "items": {"type": "string"}, "nullable": true}, "current_terminal": {"type": "string", "nullable": true}, "delivery_appt_date": {"type": "array", "items": {"type": "string", "format": "date-time"}, "nullable": true}, "offloaded": {"type": "string", "nullable": true}, "order_nos": {"type": "array", "items": {"type": "string"}, "nullable": true}, "order_type": {"type": "array", "items": {"type": "string"}, "nullable": true}, "pickup_date": {"type": "array", "items": {"type": "string", "format": "date-time"}, "nullable": true}, "prono_or_trackingnos": {"type": "array", "items": {"type": "string"}, "nullable": true}, "routeds": {"type": "integer", "format": "int32", "nullable": true}, "service_terminals": {"type": "array", "items": {"type": "string"}, "nullable": true}, "shipper_terminals": {"type": "array", "items": {"type": "string"}, "nullable": true}, "tab": {"type": "string", "nullable": true}, "pu_nos": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "LhTripTaskRpcDto": {"type": "object", "properties": {"lh_eta_from": {"type": "string", "format": "date-time", "nullable": true}, "lh_etd_from": {"type": "string", "format": "date-time", "nullable": true}, "lh_task_org_terminal": {"type": "string", "nullable": true}, "lh_task_dst_terminal": {"type": "string", "nullable": true}, "lh_trip_dst_terminal": {"type": "string", "nullable": true}, "order_key": {"type": "integer", "format": "int64"}, "task_no": {"type": "integer", "format": "int64", "nullable": true}, "trip_no": {"type": "integer", "format": "int64", "nullable": true}, "lh_task_status": {"type": "integer", "format": "int32"}, "is_load": {"type": "integer", "format": "int32"}, "load_status": {"type": "integer", "format": "int32"}, "task_type": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "OrderListItemDto": {"type": "object", "properties": {"bill_to": {"type": "string", "nullable": true}, "consignee_state": {"type": "string", "nullable": true}, "consignee_terminal": {"type": "string", "nullable": true}, "consignee_zip": {"type": "string", "nullable": true}, "ctn": {"type": "integer", "format": "int32"}, "delivery_appt_date": {"type": "string", "format": "date-time", "nullable": true}, "delivery_appt_date_from": {"type": "string", "format": "date-time", "nullable": true}, "delivery_appt_date_to": {"type": "string", "format": "date-time", "nullable": true}, "exception": {"type": "string", "nullable": true}, "hold": {"type": "string"}, "is_load": {"type": "integer", "format": "int32"}, "load_status": {"$ref": "#/components/schemas/TaskLoadStatusEnum"}, "offloaded": {"type": "string"}, "order_no": {"type": "string", "nullable": true}, "order_status": {"$ref": "#/components/schemas/ShipmentOrderStatusEnum"}, "order_sub_status": {"$ref": "#/components/schemas/ShipmentOrderSubStatusEnum"}, "order_type": {"type": "string", "nullable": true}, "pallet_ids": {"type": "array", "items": {"type": "string"}, "nullable": true}, "plt": {"type": "integer", "format": "int32"}, "pro_no": {"type": "string", "nullable": true}, "pu_date": {"type": "string", "format": "date-time", "nullable": true}, "routed": {"type": "string", "nullable": true}, "service_terminal": {"type": "string", "nullable": true}, "shipper_name": {"type": "string", "nullable": true}, "shipper_terminal": {"type": "string", "nullable": true}, "tracking_no": {"type": "string", "nullable": true}, "weight": {"type": "number", "format": "double"}, "wt": {"type": "number", "format": "double"}}, "additionalProperties": false}, "OrderListItemDtoPagedResultDto": {"type": "object", "properties": {"total_count": {"type": "integer", "format": "int64"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/OrderListItemDto"}, "nullable": true}}, "additionalProperties": false}, "TaskAppointmentDto": {"type": "object", "properties": {"appointment_id": {"type": "integer", "format": "int64"}, "appointment_date": {"type": "string", "format": "date-time"}, "appointment_no": {"type": "string", "nullable": true}, "third_appointment_no": {"type": "string", "nullable": true}, "task_no": {"type": "integer", "format": "int64"}, "tracking_no": {"type": "string", "nullable": true}, "pro_no": {"type": "string", "nullable": true}, "shipment_order_no": {"type": "string", "nullable": true}, "appointment_time_from": {"type": "string", "format": "date-time", "nullable": true}, "appointment_time_to": {"type": "string", "format": "date-time", "nullable": true}, "confirmed_by_shipper": {"type": "integer", "format": "int32"}, "confirmed_by_contractor": {"type": "integer", "format": "int32"}, "carrier_code": {"type": "string", "nullable": true}, "carrier_name": {"type": "string", "nullable": true}, "carrier_type": {"$ref": "#/components/schemas/CarrierTypeEnum"}, "status_display_name": {"type": "string", "nullable": true}, "open_time": {"type": "string", "nullable": true}, "close_time": {"type": "string", "nullable": true}, "address": {"type": "string", "nullable": true}, "reference": {"type": "string", "nullable": true}, "sales_order": {"type": "string", "nullable": true}, "purchase_order": {"type": "string", "nullable": true}, "load": {"type": "string", "nullable": true}, "bol": {"type": "string", "nullable": true}, "master_order": {"type": "string", "nullable": true}, "work_order_no": {"type": "string", "nullable": true}, "total_freight": {"type": "string", "nullable": true}, "shipment_order_id": {"type": "integer", "format": "int64"}, "desire_date": {"type": "string", "format": "date-time", "nullable": true}, "mabd": {"type": "string", "format": "date-time", "nullable": true}, "contact": {"type": "string", "nullable": true}, "is_open_job": {"type": "integer", "format": "int32"}, "can_cancel": {"type": "boolean"}, "can_reschedule": {"type": "boolean"}, "create_time": {"type": "string", "format": "date-time", "nullable": true}, "note": {"type": "string", "nullable": true}, "quessionaires": {"type": "array", "items": {"$ref": "#/components/schemas/TaskAppointmentQuessionaire"}, "nullable": true}, "service_information": {"$ref": "#/components/schemas/AppointmentAdditionsServiceInformation"}, "service_products": {"type": "array", "items": {"$ref": "#/components/schemas/AppointmentAdditionsServiceProduct"}, "nullable": true}, "mabd_from": {"type": "string", "format": "date-time", "nullable": true}, "mabd_to": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "TaskAppointmentTypeEnum": {"enum": [0, 1, 2, 3, 4], "type": "integer", "format": "int32"}, "TaskAppointmentInfo": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "appointment_date": {"type": "string", "format": "date-time", "nullable": true}, "time_from": {"type": "string", "format": "date-time", "nullable": true}, "time_to": {"type": "string", "format": "date-time", "nullable": true}, "consignee_appointment_date": {"type": "string", "format": "date-time", "nullable": true}, "consignee_time_from": {"type": "string", "format": "date-time", "nullable": true}, "consignee_time_to": {"type": "string", "format": "date-time", "nullable": true}, "shipment_order_key": {"type": "integer", "format": "int64"}, "appointment_no": {"type": "string", "nullable": true}, "appointment_type": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "TaskAppointmentQuessionaire": {"type": "object", "properties": {"question": {"type": "string", "nullable": true}, "answer": {"type": "string", "nullable": true}}, "additionalProperties": false}, "TaskLoadStatusEnum": {"enum": [1, 2, 3, 4, 5, 6, 7], "type": "integer", "format": "int32"}, "TaskTypeEnum": {"enum": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "type": "integer", "format": "int32"}, "ServiceLevelEnum": {"enum": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 30], "type": "integer", "format": "int32"}, "TaskGetListRequestDto": {"type": "object", "properties": {"task_nos": {"type": "array", "items": {"type": "string"}, "nullable": true}, "order_nos": {"type": "array", "items": {"type": "string"}, "nullable": true}, "pro_nos": {"type": "array", "items": {"type": "string"}, "nullable": true}, "trip_nos": {"type": "array", "items": {"type": "string"}, "nullable": true}, "task_type": {"type": "array", "items": {"$ref": "#/components/schemas/TaskTypeEnum"}, "nullable": true}, "task_status": {"type": "array", "items": {"$ref": "#/components/schemas/TaskStatusEnum"}, "nullable": true}, "load_status": {"type": "array", "items": {"$ref": "#/components/schemas/TaskLoadStatusEnum"}, "nullable": true}, "tms_task_nos": {"type": "array", "items": {"type": "integer", "format": "int64"}, "nullable": true}, "sorting": {"type": "string", "nullable": true}, "page_number": {"maximum": 2147483647, "minimum": 0, "type": "integer", "format": "int32"}, "page_size": {"maximum": 2147483647, "minimum": 1, "type": "integer", "format": "int32"}}, "additionalProperties": false}, "TaskOutPutDto": {"type": "object", "properties": {"task_id": {"type": "integer", "format": "int64"}, "task_no": {"type": "integer", "format": "int64"}, "task_type": {"type": "string", "nullable": true}, "order_no": {"type": "string", "nullable": true}, "shipment_order_no": {"type": "string", "nullable": true}, "trip_no": {"type": "string", "nullable": true}, "dispatch_date": {"type": "string", "nullable": true}, "pro_no": {"type": "string", "nullable": true}, "task_status": {"type": "string", "nullable": true}, "load_status": {"type": "string", "nullable": true}, "notes": {"type": "string", "nullable": true}, "carrier_name": {"type": "string", "nullable": true}, "carrier_code": {"type": "string", "nullable": true}, "driver_name": {"type": "string", "nullable": true}, "driver_code": {"type": "string", "nullable": true}, "pallet": {"type": "string", "nullable": true}, "weight": {"type": "string", "nullable": true}, "appointment": {"type": "string", "nullable": true}, "order_appointment_pickup": {"type": "string", "nullable": true}, "order_appointment_delivery": {"type": "string", "nullable": true}, "etd": {"type": "string", "nullable": true}, "eta": {"type": "string", "nullable": true}, "atd": {"type": "string", "nullable": true}, "ata": {"type": "string", "nullable": true}, "create_time": {"type": "string", "nullable": true}, "update_time": {"type": "string", "nullable": true}, "polygon_code": {"type": "string", "nullable": true}, "tms_stop_task_ids": {"type": "array", "items": {"type": "integer", "format": "int64"}, "nullable": true}}, "additionalProperties": false}, "TaskOutPutDtoPagedResultDto": {"type": "object", "properties": {"total_count": {"type": "integer", "format": "int64"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/TaskOutPutDto"}, "nullable": true}}, "additionalProperties": false}, "TaskStatusEnum": {"enum": [1, 2, 3, 4, 5, 6, 7, 20, 21, 22, -1], "type": "integer", "format": "int32"}, "ShipmentOrderStatusEnum": {"enum": ["New", "Pickup Confirmed", "Out For Pickup", "Pickup Complete", "Pending Linehaul", "Line<PERSON>ul", "Pending Delivery", "Out For Delivery", "Delivered", "Partial Delivered", "Pending Service", "Delivered With Exception", "Delivery Failed", "Consignee Refusal", "Cancelled", "Incomplete", "Return To Shipper"], "type": "string"}, "ShipmentOrderSubStatusEnum": {"enum": ["NotValid", "New", "Package Ready", "Pending Pickup", "Pickup Dispatched", "Out For Pickup", "Departed Pickup Location", "Pickup Complete", "DirectlyPickupOffload", "Pending Linehaul", "<PERSON><PERSON><PERSON> Dispatched", "Linehaul Loaded", "Linehaul In Transit", "In Transit", "Complete", "Pending Dispatch", "Dispatched", "Out For Delivery", "Delivery", "Partial Delivered", "Pending Service", "Delivered with Exception", "Delivery Failed", "Consignee Refusal", "Cancelled", "Incomplete", "Return To Shipper"], "type": "string"}}}}