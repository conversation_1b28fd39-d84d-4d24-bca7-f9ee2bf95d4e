import { NextRequest, NextResponse } from 'next/server';
import { getUserIdFromRequest } from '@/utils/authUtils';
import { getReportByDate, searchReports, saveReport } from '@/chat-report/reportStorage';
import { getChatHistoryList, getAllUserIds } from '@/utils/storage';
import { format, parseISO, isValid } from 'date-fns';

interface FixReportRequest {
  fromDate?: string;
  toDate?: string;
}

export async function POST(req: NextRequest) {
  try {
    // 获取当前用户ID（用于权限验证）
    const currentUserId = await getUserIdFromRequest(req);
    if (!currentUserId) {
      return NextResponse.json(
        { error: '用户未登录或会话已过期' },
        { status: 401 }
      );
    }

    // 获取请求体
    const body: FixReportRequest = await req.json();
    const { fromDate, toDate } = body;

    // 验证日期格式
    if (fromDate && !isValid(parseISO(fromDate))) {
      return NextResponse.json(
        { error: '开始日期格式无效' },
        { status: 400 }
      );
    }
    if (toDate && !isValid(parseISO(toDate))) {
      return NextResponse.json(
        { error: '结束日期格式无效' },
        { status: 400 }
      );
    }

    // 构建搜索参数
    const searchParams = {
      fromDate,
      toDate,
    };

    // 获取需要修复的报告列表
    const reports = await searchReports(searchParams);
    let fixedCount = 0;
    let skippedCount = 0;
    let errorCount = 0;

    // 处理每个报告
    for (const report of reports) {
      try {
        // 获取完整的报告数据
        const fullReport = await getReportByDate(report.date);
        if (!fullReport) {
          console.warn(`报告不存在: ${report.date}`);
          skippedCount++;
          continue;
        }

        // 检查是否需要修复：chatIds 不为空且 chatUserMap 为空时
        if (fullReport.chatIds && 
            fullReport.chatIds.length > 0 && 
            (!fullReport.chatUserMap || Object.keys(fullReport.chatUserMap).length === 0)) {
          // 遍历所有 userId 目录，查找 chatId 和 userId
          const chatIds = new Set<string>(fullReport.chatIds);
          const chatUserMap: Record<string, string> = {};

          const userIds = await getAllUserIds();
          for (const userId of userIds) {
            const chatHistories = await getChatHistoryList(userId);
            for (const chatHistory of chatHistories) {
              if (chatIds.has(chatHistory.id)) {
                chatUserMap[chatHistory.id] = userId;
              }
            }
          }

          // 更新报告数据
          const updatedReport = {
            ...fullReport,
            chatUserMap,  // 只更新 chatUserMap
            updatedAt: new Date().toISOString()
          };

          // 保存更新后的报告
          await saveReport(updatedReport);
          fixedCount++;
        } else {
          skippedCount++;
        }
      } catch (error) {
        console.error(`修复报告失败: ${report.date}`, error);
        errorCount++;
      }
    }

    return NextResponse.json({
      message: '报告修复完成',
      stats: {
        total: reports.length,
        fixed: fixedCount,
        skipped: skippedCount,
        errors: errorCount
      }
    });

  } catch (error) {
    console.error('修复报告失败:', error);
    return NextResponse.json(
      { error: '修复报告失败' },
      { status: 500 }
    );
  }
} 