'use client';

import React, { ReactNode } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { useRouter } from 'next/navigation';
import '@/styles/item-design-system.css';

interface ProtectedRouteProps {
  children: ReactNode;
  fallback?: ReactNode;
}

export default function ProtectedRoute({
  children,
  fallback
}: ProtectedRouteProps) {
  const { isAuthenticated, isLoading } = useAuth();
  const router = useRouter();

  // 如果正在加载，显示加载状态
  if (isLoading) {
    return (
      <div className="min-h-screen bg-item-bg-primary flex items-center justify-center text-white">
        <div className="text-center">
          <div className="w-16 h-16 border-t-4 border-item-purple border-solid rounded-full animate-spin mb-6"></div>
          <p className="text-item-gray-400 text-xl font-medium">Loading...</p>
        </div>
      </div>
    );
  }

  // 如果未认证，显示回退组件或重定向到登录页面
  if (!isAuthenticated) {
    // 如果提供了回退组件，显示它
    if (fallback) {
      return <>{fallback}</>;
    }

    // 否则，显示默认的未授权提示
    return (
      <div className="min-h-screen bg-item-bg-primary flex items-center justify-center text-white">
        <div className="text-center bg-item-bg-card backdrop-blur-sm p-8 rounded-xl border border-item-gray-800 shadow-lg">
          <div className="text-4xl font-bold text-red-400 mb-4">
            Login Required
          </div>
          <p className="text-item-gray-400 mb-8">
            You need to login to access this page
          </p>
          <button
            onClick={() => router.push('/')}
            className="px-6 py-3 bg-item-purple hover:bg-item-purple-dark text-white rounded-md transition-colors"
          >
            Back to Home
          </button>
        </div>
      </div>
    );
  }

  // 已认证，显示子组件
  return <>{children}</>;
}