// 简单检查.env.local环境变量
const fs = require('fs');
const path = require('path');

console.log('===== 环境变量检查 =====');

// 检查.env.local文件是否存在
const envPath = path.resolve(process.cwd(), '.env.local');
if (fs.existsSync(envPath)) {
  console.log(`✅ .env.local文件存在: ${envPath}`);
  
  // 读取文件内容
  const content = fs.readFileSync(envPath, 'utf8');
  
  // 检查是否包含OPENAI_API_KEY
  if (content.includes('OPENAI_API_KEY')) {
    console.log('✅ 文件包含OPENAI_API_KEY设置');
  } else {
    console.log('❌ 文件不包含OPENAI_API_KEY设置');
  }
  
  // 手动加载环境变量
  require('dotenv').config({ path: envPath });
  
  // 检查是否已加载到process.env
  if (process.env.OPENAI_API_KEY) {
    console.log('✅ OPENAI_API_KEY已加载到环境变量');
    console.log(`   长度: ${process.env.OPENAI_API_KEY.length} 字符`);
    console.log(`   前缀: ${process.env.OPENAI_API_KEY.substring(0, 5)}...`);
  } else {
    console.log('❌ OPENAI_API_KEY未加载到环境变量');
  }
} else {
  console.log(`❌ .env.local文件不存在: ${envPath}`);
}

console.log('\n===== 当前目录信息 =====');
console.log('当前工作目录:', process.cwd());
console.log('脚本位置:', __filename);
console.log('脚本目录:', __dirname);

console.log('\n===== 环境变量值 =====');
console.log('NODE_ENV:', process.env.NODE_ENV || '未设置');
console.log('DB_HOST:', process.env.DB_HOST || '未设置'); 