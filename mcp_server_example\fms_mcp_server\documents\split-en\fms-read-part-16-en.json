{"openapi": "3.0.1", "info": {"title": "FMS", "description": "", "version": "1.0.0"}, "paths": {"/fms-platform-dispatch/my-trips": {"get": {"summary": "My Trip page, get list data (directly read the library) by wjg", "deprecated": false, "description": "", "tags": ["Trips"], "parameters": [{"name": "<PERSON><PERSON>", "in": "query", "description": "", "required": false, "schema": {"type": "integer", "format": "int32"}}, {"name": "MaxResoultCount", "in": "query", "description": "", "required": false, "schema": {"type": "integer", "format": "int32"}}, {"name": "Status", "in": "query", "description": "", "required": false, "schema": {"$ref": "#/components/schemas/TripInfoEnum"}}, {"name": "Filter", "in": "query", "description": "", "required": false, "schema": {"$ref": "#/components/schemas/SearchBy"}}, {"name": "Timing", "in": "query", "description": "", "required": false, "schema": {"$ref": "#/components/schemas/EventTiming"}}, {"name": "DateRange", "in": "query", "description": "", "required": false, "schema": {"type": "array", "items": {"type": "string", "format": "date-time"}}}, {"name": "Keyword", "in": "query", "description": "", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MyTripListItemDtoPagedResultDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch/my-stops": {"get": {"summary": "My Stop page, get list data (directly read the library) by wjg", "deprecated": false, "description": "", "tags": ["Trips"], "parameters": [{"name": "tripNo", "in": "query", "description": "", "required": false, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MyStopListItemDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch/get-stopList": {"get": {"summary": "trip StopList, only get the stop of trip Get list data by wjg", "deprecated": false, "description": "", "tags": ["Trips"], "parameters": [{"name": "tripNo", "in": "query", "description": "", "required": false, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DptStopDto"}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch/get-stopTaskList": {"get": {"summary": "trip TaskList, only get the task of stop to get list data by wjg", "deprecated": false, "description": "", "tags": ["Trips"], "parameters": [{"name": "tripNo", "in": "query", "description": "", "required": false, "schema": {"type": "integer", "format": "int64"}}, {"name": "stopsNo", "in": "query", "description": "", "required": false, "schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DptTaskDto"}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch/get-terminalpackages": {"get": {"summary": "GetTerminalPackagesByStopId  by wjg", "deprecated": false, "description": "", "tags": ["Trips"], "parameters": [{"name": "TripNo", "in": "query", "description": "", "required": false, "schema": {"type": "integer", "format": "int64"}}, {"name": "StopNo", "in": "query", "description": "", "required": false, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TerminaTask"}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch/my-trips/types": {"get": {"summary": "My Trip page, Type drop-down box data", "deprecated": false, "description": "", "tags": ["Trips"], "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Int32FmsSelectListItemListResultDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch/my-trips/status": {"get": {"summary": "My Trip page, Status drop-down box data", "deprecated": false, "description": "", "tags": ["Trips"], "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Int32FmsSelectListItemListResultDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch/my-trips/date-ranges": {"get": {"summary": "My Trip page, Date Range drop-down box data", "deprecated": false, "description": "", "tags": ["Trips"], "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Int32FmsSelectListItemListResultDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order-account/AccountPayable/GetOrder": {"get": {"summary": "AI: Get order information", "deprecated": false, "description": "", "tags": ["Account<PERSON><PERSON><PERSON>"], "parameters": [{"name": "orderNo", "in": "query", "description": "Order number", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PushBNPOrderRPCRequest"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order-account/AccountPayable/ManualPushBNPOrder": {"get": {"summary": "Manually push order to bnp", "deprecated": false, "description": "", "tags": ["Account<PERSON><PERSON><PERSON>"], "parameters": [{"name": "orderNo", "in": "query", "description": "", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "boolean"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order-account/AccountPayable/GetTrip": {"get": {"summary": "AI: Get Trip information", "deprecated": false, "description": "", "tags": ["Account<PERSON><PERSON><PERSON>"], "parameters": [{"name": "tripNo", "in": "query", "description": "Trip number", "required": false, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PushBNPTripRPCRequest"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order-account/AccountPayable/ManualPushBNPTrip": {"get": {"summary": "Manually push <PERSON> to bnp", "deprecated": false, "description": "", "tags": ["Account<PERSON><PERSON><PERSON>"], "parameters": [{"name": "tripNo", "in": "query", "description": "", "required": false, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "boolean"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order-account/invoice/manual-push-order": {"get": {"summary": "Manually push order to Invoice", "deprecated": false, "description": "", "tags": ["Invoice"], "parameters": [{"name": "orderNo", "in": "query", "description": "", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "boolean"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order-account/order-profit-loss/rating": {"get": {"summary": "AI: rating list query under order pnl", "deprecated": false, "description": "", "tags": ["OrderProfitAndLoss"], "parameters": [{"name": "orderNo", "in": "query", "description": "Order number", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ShipmentOrdersProfitLostRatingDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order-account/order-profit-loss/carrier-pay": {"get": {"summary": "AI: order pnl carrier/driver pay list query below", "deprecated": false, "description": "", "tags": ["OrderProfitAndLoss"], "parameters": [{"name": "orderNo", "in": "query", "description": "Order number", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CarrierDriverPayDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order-account/order-profit-loss/profit-and-loss": {"get": {"summary": "AI: order pnl Profit and Loss report", "deprecated": false, "description": "", "tags": ["OrderProfitAndLoss"], "parameters": [{"name": "orderNo", "in": "query", "description": "Order number", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProfitLossDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order-account/order-profit-loss/check-role": {"get": {"summary": "AI: order pnl check user permissions", "deprecated": false, "description": "", "tags": ["OrderProfitAndLoss"], "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "boolean"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order-account/order-profit-loss/shipper-bol": {"get": {"summary": "Upload pdf", "deprecated": false, "description": "", "tags": ["OrderProfitAndLoss"], "parameters": [{"name": "orderNo", "in": "query", "description": "", "required": false, "schema": {"type": "string"}}, {"name": "tripNo", "in": "query", "description": "", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UploadResultDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order-account/OrderProfitAndLoss/GetShipmentOrderPdf": {"get": {"summary": "Get PDF file information", "deprecated": false, "description": "", "tags": ["OrderProfitAndLoss"], "parameters": [{"name": "orderNo", "in": "query", "description": "", "required": false, "schema": {"type": "string"}}, {"name": "tripNo", "in": "query", "description": "", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FileInfoDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order-account/trip-carrier-pay/{tripNo}": {"get": {"summary": "AI: Get data from TripCarrierPay", "deprecated": false, "description": "", "tags": ["TripCarrierPay"], "parameters": [{"name": "tripNo", "in": "path", "description": "Trip number", "required": true, "example": 0, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CarrierPayResponseDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}}, "components": {"schemas": {"CarrierDriverPayDetail": {"type": "object", "properties": {"order_no": {"type": "string", "nullable": true}, "order_key": {"type": "integer", "format": "int64"}, "carrier_amount": {"type": "number", "format": "double", "nullable": true}, "carrier_code": {"type": "string", "nullable": true}, "carrier_name": {"type": "string", "nullable": true}, "carrier_pro_no": {"type": "string", "nullable": true}, "carrier_type": {"type": "string", "nullable": true}, "task_type_name": {"type": "string", "nullable": true}, "task_type": {"type": "integer", "format": "int32", "nullable": true}, "carrier_quote_no": {"type": "string", "nullable": true}, "trip_no": {"type": "string", "nullable": true}, "source": {"type": "string", "nullable": true}, "transaction_id": {"type": "string", "nullable": true}, "status": {"type": "integer", "format": "int32"}, "id": {"type": "integer", "format": "int64"}, "created_time": {"type": "string", "format": "date-time", "nullable": true}, "created_by": {"type": "integer", "format": "int64", "nullable": true}, "update_by": {"type": "integer", "format": "int64", "nullable": true}, "update_time": {"type": "string", "format": "date-time", "nullable": true}, "is_del": {"type": "boolean"}, "create_user": {"type": "string", "nullable": true}, "update_user": {"type": "string", "nullable": true}, "actual_update_time": {"type": "string", "format": "date-time"}, "actual_create_time": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "CarrierDriverPayDto": {"type": "object", "properties": {"carrier_driver_pay_list": {"type": "array", "items": {"$ref": "#/components/schemas/CarrierDriverPayDetail"}, "nullable": true}, "total": {"type": "number", "format": "double", "nullable": true}}, "additionalProperties": false}, "CarrierPayResponseDto": {"type": "object", "properties": {"trip_carrier_pay_response": {"$ref": "#/components/schemas/TripCarrierPayResponseDto"}, "trip_carrier_pay_charge_response": {"type": "array", "items": {"$ref": "#/components/schemas/TripCarrierPayChargeResponseDto"}, "nullable": true}}, "additionalProperties": false}, "FileInfoDto": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "url": {"type": "string", "nullable": true}, "size": {"type": "integer", "format": "int64"}, "source_url": {"type": "string", "nullable": true}}, "additionalProperties": false}, "DptStopDto": {"type": "object", "properties": {"stop_sequence": {"type": "integer", "format": "int32"}, "stop_no": {"type": "integer", "format": "int64"}, "sequence": {"type": "integer", "format": "int32"}, "stop_type": {"type": "integer", "format": "int32"}, "stop_status": {"type": "integer", "format": "int32"}, "check_in_time": {"type": "string", "format": "date-time"}, "check_out_time": {"type": "string", "format": "date-time"}, "address1": {"type": "string", "nullable": true}, "address2": {"type": "string", "nullable": true}, "zip_code": {"type": "string", "nullable": true}, "city": {"type": "string", "nullable": true}, "state": {"type": "string", "nullable": true}, "country": {"type": "string", "nullable": true}, "latitude": {"type": "number", "format": "double"}, "longitude": {"type": "number", "format": "double"}, "location_id": {"type": "integer", "format": "int32"}, "stop_type_desc": {"type": "string", "nullable": true}, "stop_terminal_type": {"type": "integer", "format": "int32"}, "from_name": {"type": "string", "nullable": true}, "from_phone": {"type": "string", "nullable": true}, "to_name": {"type": "string", "nullable": true}, "to_phone": {"type": "string", "nullable": true}, "stop_type_state": {"type": "integer", "format": "int32"}, "relevance_stop_no": {"type": "string", "nullable": true}, "is_revenue_terminal": {"type": "boolean"}, "appointment_time_from": {"type": "string", "format": "date-time"}, "appointment_time_to": {"type": "string", "format": "date-time"}, "actual_update_time": {"type": "string", "format": "date-time"}, "tasks": {"type": "array", "items": {"$ref": "#/components/schemas/DptTaskDto"}, "nullable": true}, "delivery_task": {"$ref": "#/components/schemas/TripTaskDto"}, "pickup_task": {"$ref": "#/components/schemas/TripTaskDto"}, "service_task": {"$ref": "#/components/schemas/TripTaskDto"}, "p_with_d_task": {"$ref": "#/components/schemas/TripTaskDto"}, "stop": {"$ref": "#/components/schemas/TripStopDto"}, "pallet_qty_p": {"type": "number", "format": "double"}, "carton_qty_p": {"type": "number", "format": "double"}, "weight_p": {"type": "number", "format": "double"}, "pallet_qty_d": {"type": "number", "format": "double"}, "carton_qty_d": {"type": "number", "format": "double"}, "weight_d": {"type": "number", "format": "double"}, "pallet_qty_s": {"type": "number", "format": "double"}, "carton_qty_s": {"type": "number", "format": "double"}, "weight_s": {"type": "number", "format": "double"}, "is_tcl": {"type": "boolean"}, "is_ltl": {"type": "boolean"}, "is_ftl": {"type": "boolean"}, "is_lh": {"type": "boolean"}}, "additionalProperties": false}, "HttpStatusCode": {"enum": [100, 101, 102, 103, 200, 201, 202, 203, 204, 205, 206, 207, 208, 226, 300, 301, 302, 303, 304, 305, 306, 307, 308, 400, 401, 402, 403, 404, 405, 406, 407, 408, 409, 410, 411, 412, 413, 414, 415, 416, 417, 421, 422, 423, 424, 426, 428, 429, 431, 451, 500, 501, 502, 503, 504, 505, 506, 507, 508, 510, 511], "type": "integer", "format": "int32"}, "DptTaskDto": {"type": "object", "properties": {"stop_no": {"type": "integer", "format": "int64"}, "task_no": {"type": "integer", "format": "int64"}, "task_template_id": {"type": "integer", "format": "int32"}, "task_type": {"type": "integer", "format": "int32"}, "task_type_group": {"type": "string", "nullable": true}, "shipment_type": {"type": "integer", "format": "int32"}, "task_status": {"type": "integer", "format": "int32"}, "appointment_date": {"type": "string", "format": "date-time"}, "appointment_time_from": {"type": "string", "format": "date-time"}, "appointment_time_to": {"type": "string", "format": "date-time"}, "actual_update_time": {"type": "string", "format": "date-time"}, "from_name": {"type": "string", "nullable": true}, "from_phone": {"type": "string", "nullable": true}, "to_name": {"type": "string", "nullable": true}, "to_phone": {"type": "string", "nullable": true}, "pro_number": {"type": "string", "nullable": true}, "pallets": {"type": "integer", "format": "int32"}, "cartons": {"type": "integer", "format": "int32"}, "pu_number": {"type": "string", "nullable": true}, "bol": {"type": "string", "nullable": true}, "reference2": {"type": "string", "nullable": true}, "is_empty_pro": {"type": "boolean"}, "pro_pre_fix": {"type": "string", "nullable": true}, "invoice_pro": {"type": "string", "nullable": true}, "tracking_no": {"type": "string", "nullable": true}, "shipper_note": {"type": "string", "nullable": true}, "consignee_note": {"type": "string", "nullable": true}, "pallet_qty": {"type": "number", "format": "double"}, "carton_qty": {"type": "number", "format": "double"}, "weight": {"type": "number", "format": "double"}, "from_address1": {"type": "string", "nullable": true}, "from_address2": {"type": "string", "nullable": true}, "from_city": {"type": "string", "nullable": true}, "from_state": {"type": "string", "nullable": true}, "from_zip_code": {"type": "string", "nullable": true}, "to_address1": {"type": "string", "nullable": true}, "to_address2": {"type": "string", "nullable": true}, "to_city": {"type": "string", "nullable": true}, "to_state": {"type": "string", "nullable": true}, "to_zip_code": {"type": "string", "nullable": true}, "order_service_level": {"type": "integer", "format": "int32"}, "required_appointment": {"type": "integer", "format": "int32"}, "task_reference_type": {"type": "integer", "format": "int32"}, "task_step": {"type": "array", "items": {"$ref": "#/components/schemas/DptTaskSteps"}, "nullable": true}, "packages_item": {"type": "array", "items": {"$ref": "#/components/schemas/DptTaskPackages"}, "nullable": true}}, "additionalProperties": false}, "DptTaskPackages": {"type": "object", "properties": {"task_no": {"type": "integer", "format": "int64"}, "stop_sequence": {"type": "integer", "format": "int32"}, "package_no": {"type": "string", "nullable": true}, "tracking_no": {"type": "string", "nullable": true}, "package_sequence": {"type": "integer", "format": "int32"}, "package_status": {"type": "integer", "format": "int32"}, "package_type": {"type": "string", "nullable": true}, "weight_uom": {"type": "string", "nullable": true}, "from_name": {"type": "string", "nullable": true}, "from_phone": {"type": "string", "nullable": true}, "to_name": {"type": "string", "nullable": true}, "to_phone": {"type": "string", "nullable": true}, "order_service_level": {"type": "integer", "format": "int32"}, "task_type": {"type": "integer", "format": "int32"}, "pallet_qty": {"type": "integer", "format": "int32"}, "carton_qty": {"type": "integer", "format": "int32"}, "weight": {"type": "number", "format": "double"}, "actual_update_time": {"type": "string", "format": "date-time"}, "shipment_type": {"type": "integer", "format": "int32"}, "finish_step": {"type": "array", "items": {"$ref": "#/components/schemas/TaskSteps"}, "nullable": true}}, "additionalProperties": false}, "DptTaskStepActions": {"type": "object", "properties": {"step_code": {"type": "string", "nullable": true}, "action_code": {"type": "string", "nullable": true}, "action_name": {"type": "string", "nullable": true}, "is_required": {"type": "integer", "format": "int32"}, "operate_parameters": {"type": "string", "nullable": true}, "reference_action_type": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "ManifestDto": {"type": "object", "properties": {"class_id": {"type": "integer", "format": "int32"}, "weight": {"type": "string", "nullable": true}, "lengths": {"type": "string", "nullable": true}, "widths": {"type": "string", "nullable": true}, "heights": {"type": "string", "nullable": true}, "pallet_qty": {"type": "integer", "format": "int32"}, "volume": {"type": "string", "nullable": true}, "carton": {"type": "integer", "format": "int32"}, "linear": {"type": "string", "nullable": true}, "value": {"type": "string", "nullable": true}, "space": {"type": "string", "nullable": true}, "code": {"type": "string", "nullable": true}, "interval_time": {"type": "string", "nullable": true}}, "additionalProperties": false}, "DptTaskSteps": {"type": "object", "properties": {"sequence": {"type": "integer", "format": "int32"}, "step_code": {"type": "string", "nullable": true}, "step_name": {"type": "string", "nullable": true}, "step_desc": {"type": "string", "nullable": true}, "act_together": {"type": "integer", "format": "int32"}, "dpt_task_step_actions": {"type": "array", "items": {"$ref": "#/components/schemas/DptTaskStepActions"}, "nullable": true}}, "additionalProperties": false}, "EventTiming": {"enum": [1, 2, 3, 4], "type": "integer", "format": "int32"}, "OrderApDto": {"type": "object", "properties": {"tms_ap_id": {"type": "string", "nullable": true}, "carrier_id": {"type": "string", "nullable": true}, "carrier_pro": {"type": "string", "nullable": true}, "tms_ap_export_id": {"type": "string", "nullable": true}, "tms_ap_invoice_id": {"type": "string", "nullable": true}}, "additionalProperties": false}, "OrderQuoteDto": {"type": "object", "properties": {"desc": {"type": "string", "nullable": true}, "amount": {"type": "string", "nullable": true}, "type": {"type": "string", "nullable": true}, "quote_id": {"type": "integer", "format": "int64"}, "order_id": {"type": "integer", "format": "int64"}, "code": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ProfitLossDetails": {"type": "object", "properties": {"over_view": {"type": "string", "nullable": true}, "internal_note": {"type": "string", "nullable": true}, "type": {"type": "integer", "format": "int32", "nullable": true}, "amount": {"type": "number", "format": "double", "nullable": true}, "misc": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ProfitLossDto": {"type": "object", "properties": {"over_view": {"type": "string", "nullable": true}, "internal_note": {"type": "string", "nullable": true}, "type": {"type": "string", "nullable": true}, "rating_total": {"type": "number", "format": "double", "nullable": true}, "profit_loss_details": {"type": "array", "items": {"$ref": "#/components/schemas/ProfitLossDetails"}, "nullable": true}, "net_profit": {"type": "number", "format": "double"}}, "additionalProperties": false}, "ProfitLostRatingDetail": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "nullable": true}, "rating_amount": {"type": "number", "format": "double", "nullable": true}, "client_explanation": {"type": "string", "nullable": true}, "internal_note": {"type": "string", "nullable": true}, "type": {"type": "string", "nullable": true}, "source": {"$ref": "#/components/schemas/ShipmentOrderRateSource"}, "is_del": {"type": "boolean"}}, "additionalProperties": false}, "PushBNPOrderFile": {"type": "object", "properties": {"tms_order_id": {"type": "integer", "format": "int32"}, "file_id": {"type": "integer", "format": "int32"}, "file_url": {"type": "string", "nullable": true}, "task_type": {"type": "string", "nullable": true}, "file_name": {"type": "string", "nullable": true}}, "additionalProperties": false}, "PushBNPOrderRPCRequest": {"type": "object", "properties": {"clientid": {"type": "string", "nullable": true}, "request_source": {"type": "string", "nullable": true}, "pu": {"type": "integer", "format": "int64"}, "order_id": {"type": "string", "nullable": true}, "pro": {"type": "integer", "format": "int64"}, "billto_id": {"type": "string", "nullable": true}, "ship_date": {"type": "string", "nullable": true}, "pickup_date": {"type": "string", "nullable": true}, "delivery_date": {"type": "string", "nullable": true}, "weights": {"type": "string", "nullable": true}, "pallets": {"type": "string", "nullable": true}, "pieces": {"type": "string", "nullable": true}, "po": {"type": "string", "nullable": true}, "ctrl_terminal": {"type": "string", "nullable": true}, "svcs_terminal": {"type": "string", "nullable": true}, "shipper_id": {"type": "integer", "format": "int32"}, "shipper_name": {"type": "string", "nullable": true}, "shipper_street": {"type": "string", "nullable": true}, "shipper_street2": {"type": "string", "nullable": true}, "shipper_city": {"type": "string", "nullable": true}, "shipper_zip": {"type": "string", "nullable": true}, "shipper_state": {"type": "string", "nullable": true}, "shipper_country": {"type": "string", "nullable": true}, "shipper_lat": {"type": "number", "format": "double"}, "shipper_lng": {"type": "number", "format": "double"}, "consignee_id": {"type": "integer", "format": "int32"}, "consignee_name": {"type": "string", "nullable": true}, "consignee_street": {"type": "string", "nullable": true}, "consignee_street2": {"type": "string", "nullable": true}, "consignee_city": {"type": "string", "nullable": true}, "consignee_state": {"type": "string", "nullable": true}, "consignee_zip": {"type": "string", "nullable": true}, "consignee_country": {"type": "string", "nullable": true}, "consignee_lat": {"type": "number", "format": "double"}, "consignee_lng": {"type": "number", "format": "double"}, "is_cancel": {"type": "integer", "format": "int32"}, "rate_type": {"type": "string", "nullable": true}, "revenue": {"type": "string", "nullable": true}, "invoice_file": {"type": "string", "nullable": true}, "invoice_no": {"type": "string", "nullable": true}, "quote_amount": {"type": "string", "nullable": true}, "rev_id": {"type": "integer", "format": "int32"}, "rev_code": {"type": "string", "nullable": true}, "invoice_date": {"type": "string", "nullable": true}, "invoice_due_date": {"type": "string", "nullable": true}, "ar_status": {"type": "string", "nullable": true}, "order_stage_id": {"type": "integer", "format": "int64"}, "order_stage": {"type": "string", "nullable": true}, "order_ap": {"type": "array", "items": {"$ref": "#/components/schemas/OrderApDto"}, "nullable": true}, "manifest": {"type": "array", "items": {"$ref": "#/components/schemas/ManifestDto"}, "nullable": true}, "order_quote": {"type": "array", "items": {"$ref": "#/components/schemas/OrderQuoteDto"}, "nullable": true}}, "additionalProperties": false}, "MyStopListItemDto": {"type": "object", "properties": {"trip_no": {"type": "integer", "format": "int64"}, "status": {"type": "integer", "format": "int32"}, "carrier_name": {"type": "string", "nullable": true}, "driver_name": {"type": "string", "nullable": true}, "optimize_status": {"type": "integer", "format": "int32"}, "full_file_host": {"type": "string", "nullable": true}, "stops": {"type": "array", "items": {"$ref": "#/components/schemas/DptStopDto"}, "nullable": true}, "is_tcl": {"type": "boolean"}, "is_ltl": {"type": "boolean"}, "is_ftl": {"type": "boolean"}, "is_lh": {"type": "boolean"}}, "additionalProperties": false}, "PushBNPStop": {"type": "object", "properties": {"stop_id": {"type": "integer", "format": "int64"}, "stop_sequence": {"type": "integer", "format": "int32"}, "trailer_no_vehicle_id": {"type": "string", "nullable": true}, "trailer_no_vehicle_name": {"type": "string", "nullable": true}, "stop_location_id": {"type": "integer", "format": "int32"}, "stop_location_name": {"type": "string", "nullable": true}, "stop_location_code": {"type": "string", "nullable": true}, "stop_location_street": {"type": "string", "nullable": true}, "stop_location_street2": {"type": "string", "nullable": true}, "stop_location_zip": {"type": "string", "nullable": true}, "stop_location_city": {"type": "string", "nullable": true}, "stop_location_state": {"type": "string", "nullable": true}, "stop_location_country": {"type": "string", "nullable": true}, "stop_location_lat": {"type": "number", "format": "double"}, "stop_location_lng": {"type": "number", "format": "double"}, "stop_tasks": {"type": "array", "items": {"$ref": "#/components/schemas/PushBNPTask"}, "nullable": true}}, "additionalProperties": false}, "MyTripListItemDto": {"type": "object", "properties": {"status": {"$ref": "#/components/schemas/TripStatusEnum"}, "trip_no": {"type": "integer", "format": "int64", "nullable": true}, "dispatch_terminal": {"type": "string", "nullable": true}, "dst_terminal": {"type": "string", "nullable": true}, "duration": {"type": "string", "nullable": true}, "total_distance": {"type": "number", "format": "double"}, "delivery_task": {"$ref": "#/components/schemas/TripTaskDto"}, "pickup_task": {"$ref": "#/components/schemas/TripTaskDto"}, "service_task": {"$ref": "#/components/schemas/TripTaskDto"}, "p_with_d_task": {"$ref": "#/components/schemas/TripTaskDto"}, "lh_task": {"$ref": "#/components/schemas/TripTaskDto"}, "stop": {"$ref": "#/components/schemas/TripStopDto"}, "trip_name": {"type": "string", "nullable": true}, "dispatch_date": {"type": "string", "format": "date-time"}, "start_time": {"type": "string", "format": "date-time"}, "is_offline": {"type": "integer", "format": "int32"}, "companion": {"type": "string", "nullable": true}, "is_add_companion": {"type": "boolean"}, "tractor": {"type": "string", "nullable": true}, "trailer": {"type": "string", "nullable": true}, "is_tcl": {"type": "boolean"}, "is_ltl": {"type": "boolean"}, "is_ftl": {"type": "boolean"}, "is_lh": {"type": "boolean"}, "is_first_org_terminal": {"type": "boolean"}, "actual_update_time": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "PushBNPTask": {"type": "object", "properties": {"stop_task_id": {"type": "integer", "format": "int64"}, "stop_tasks_sequence": {"type": "integer", "format": "int32"}, "task_master_id": {"type": "integer", "format": "int32"}, "task_master_name": {"type": "string", "nullable": true}, "task_group_id": {"type": "integer", "format": "int32"}, "task_group_status": {"type": "string", "nullable": true}, "task_group_stage": {"type": "string", "nullable": true}, "order_id": {"type": "string", "nullable": true}, "linehaul_id": {"type": "integer", "format": "int64"}, "pu_id": {"type": "string", "nullable": true}, "task_group_sequence": {"type": "integer", "format": "int32"}, "dryrun_id": {"type": "integer", "format": "int32"}, "stop_task_status": {"type": "integer", "format": "int32"}, "stop_task_details": {"type": "array", "items": {"$ref": "#/components/schemas/PushBNPOrderFile"}, "nullable": true}}, "additionalProperties": false}, "MyTripListItemDtoPagedResultDto": {"type": "object", "properties": {"total_count": {"type": "integer", "format": "int64"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/MyTripListItemDto"}, "nullable": true}}, "additionalProperties": false}, "PushBNPTripRPCRequest": {"type": "object", "properties": {"trip_id": {"type": "integer", "format": "int64"}, "clientid": {"type": "string", "nullable": true}, "request_source": {"type": "string", "nullable": true}, "trip_statusid": {"type": "integer", "format": "int32"}, "trip_status": {"type": "string", "nullable": true}, "origin_terminal": {"type": "string", "nullable": true}, "dest_terminal": {"type": "string", "nullable": true}, "start_date": {"type": "string", "format": "date-time"}, "dispatch_date": {"type": "string", "format": "date-time"}, "complete_date": {"type": "string", "format": "date-time"}, "dispatcher": {"type": "string", "nullable": true}, "carrier_id": {"type": "string", "nullable": true}, "driver_id": {"type": "string", "nullable": true}, "driver_type": {"type": "string", "nullable": true}, "tractor_no_vehicle_id": {"type": "string", "nullable": true}, "tractor_no_vehicle_name": {"type": "string", "nullable": true}, "quote_amount": {"type": "number", "format": "double"}, "trip_pro_qty": {"type": "integer", "format": "int32"}, "trip_order_ids": {"type": "string", "nullable": true}, "stops": {"type": "array", "items": {"$ref": "#/components/schemas/PushBNPStop"}, "nullable": true}}, "additionalProperties": false}, "RatingStatusEnum": {"enum": [1, 2, 3, 4], "type": "integer", "format": "int32"}, "ShipmentOrderRateSource": {"enum": [1, 2], "type": "integer", "format": "int32"}, "ShipmentOrdersProfitLostRatingDto": {"type": "object", "properties": {"rating_details": {"type": "array", "items": {"$ref": "#/components/schemas/ProfitLostRatingDetail"}, "nullable": true}, "total": {"type": "number", "format": "double"}, "status": {"$ref": "#/components/schemas/RatingStatusEnum"}, "invoice_no": {"type": "string", "nullable": true}}, "additionalProperties": false}, "TripCarrierPayChargeResponseDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "trip_no": {"type": "integer", "format": "int64"}, "charge_type": {"type": "integer", "format": "int32"}, "charge_type_name": {"type": "string", "nullable": true}, "days": {"type": "integer", "format": "int32"}, "amount": {"type": "number", "format": "double"}, "created_time": {"type": "string", "format": "date-time", "nullable": true}, "created_by": {"type": "integer", "format": "int64", "nullable": true}, "update_by": {"type": "integer", "format": "int64", "nullable": true}, "update_time": {"type": "string", "format": "date-time", "nullable": true}, "is_del": {"type": "boolean"}, "create_user": {"type": "string", "nullable": true}, "update_user": {"type": "string", "nullable": true}, "actual_update_time": {"type": "string", "format": "date-time"}, "actual_create_time": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "SearchBy": {"enum": [1, 2, 3, 4], "type": "integer", "format": "int32"}, "TripCarrierPayResponseDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "trip_no": {"type": "integer", "format": "int64"}, "carrier_pro_no": {"type": "string", "nullable": true}, "carrier_quote_no": {"type": "string", "nullable": true}, "carrier_pay": {"type": "number", "format": "double"}, "created_time": {"type": "string", "format": "date-time", "nullable": true}, "created_by": {"type": "integer", "format": "int64", "nullable": true}, "update_by": {"type": "integer", "format": "int64", "nullable": true}, "update_time": {"type": "string", "format": "date-time", "nullable": true}, "is_del": {"type": "boolean"}, "create_user": {"type": "string", "nullable": true}, "update_user": {"type": "string", "nullable": true}, "actual_update_time": {"type": "string", "format": "date-time"}, "actual_create_time": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "UploadResultDto": {"type": "object", "properties": {"http_status_code": {"$ref": "#/components/schemas/HttpStatusCode"}, "message": {"type": "string", "nullable": true}, "file_info": {"$ref": "#/components/schemas/FileInfoDto"}}, "additionalProperties": false}, "TaskStepActions": {"type": "object", "properties": {"step_code": {"type": "string", "nullable": true}, "step_name": {"type": "string", "nullable": true}, "action_code": {"type": "string", "nullable": true}, "action_name": {"type": "string", "nullable": true}, "action_data": {"type": "string", "nullable": true}, "notes": {"type": "string", "nullable": true}, "full_file_url": {"type": "string", "nullable": true}}, "additionalProperties": false}, "TaskSteps": {"type": "object", "properties": {"step_sequence": {"type": "integer", "format": "int32"}, "step_code": {"type": "string", "nullable": true}, "step_name": {"type": "string", "nullable": true}, "notes": {"type": "string", "nullable": true}, "start_time": {"type": "string", "format": "date-time"}, "ent_time": {"type": "string", "format": "date-time"}, "is_complete": {"type": "integer", "format": "int32"}, "is_exception": {"type": "integer", "format": "int32"}, "exception_reason": {"type": "string", "nullable": true}, "exception_data": {"type": "string", "nullable": true}, "full_file_url": {"type": "string", "nullable": true}, "task_step_actions": {"type": "array", "items": {"$ref": "#/components/schemas/TaskStepActions"}, "nullable": true}}, "additionalProperties": false}, "TerminaTask": {"type": "object", "properties": {"stop_no": {"type": "integer", "format": "int64"}, "task_no": {"type": "integer", "format": "int64"}, "task_template_id": {"type": "integer", "format": "int32"}, "task_type": {"type": "integer", "format": "int32"}, "task_type_group": {"type": "string", "nullable": true}, "shipment_type": {"type": "integer", "format": "int32"}, "status": {"type": "integer", "format": "int32"}, "order_service_level": {"type": "integer", "format": "int32"}, "required_appointment": {"type": "integer", "format": "int32"}, "packages_item": {"type": "array", "items": {"$ref": "#/components/schemas/TerminaTaskPackages"}, "nullable": true}}, "additionalProperties": false}, "TerminaTaskPackages": {"type": "object", "properties": {"task_no": {"type": "integer", "format": "int64"}, "stop_sequence": {"type": "integer", "format": "int32"}, "package_no": {"type": "string", "nullable": true}, "shipment_type": {"type": "integer", "format": "int32"}, "tracking_no": {"type": "string", "nullable": true}, "package_sequence": {"type": "integer", "format": "int32"}, "package_type": {"type": "string", "nullable": true}, "weight_uom": {"type": "string", "nullable": true}, "from_name": {"type": "string", "nullable": true}, "from_phone": {"type": "string", "nullable": true}, "to_name": {"type": "string", "nullable": true}, "to_phone": {"type": "string", "nullable": true}, "order_service_level": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "TripStopDto": {"type": "object", "properties": {"total": {"type": "integer", "format": "int32"}, "completed": {"type": "integer", "format": "int32"}, "dry_run": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "TripTaskDto": {"type": "object", "properties": {"total": {"type": "integer", "format": "int32"}, "completed": {"type": "integer", "format": "int32"}, "dry_run": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "Int32FmsSelectListItem": {"type": "object", "properties": {"text": {"type": "string", "nullable": true}, "value": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "Int32FmsSelectListItemListResultDto": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/Int32FmsSelectListItem"}, "nullable": true}}, "additionalProperties": false}, "TripStatusEnum": {"enum": ["Pre Plan", "Dispatched", "Check In", "In Progress", "Canceled", "Complete", "Reviewed"], "type": "string"}, "TripInfoEnum": {"enum": [2, 3, 4, 5, 6, 10], "type": "integer", "format": "int32"}}}}