# 🎯 多 Agent 系统方案设计 (v2.0)

## 📋 **核心设计理念**

**问题陈述**：当前的单一 Agent 架构需要在一个上下文中加载所有领域的工具和提示词，导致上下文过长（预估超过 37,000 tokens）。这严重影响了大型语言模型（LLM）的性能，导致响应速度变慢、质量下降，甚至超出模型的能力上限。

**核心解决方案**：将现有的单一 Agent 升级为 **Super Agent**（协调者），并引入多个**专业化 Agent**。Super Agent 负责处理通用任务，并通过**工具委托机制**将专业任务分发给拥有精准上下文的专业 Agent。为了实现开放性和互操作性，Agent 间的通信将严格遵守 **Google A2A (Agent-to-Agent) 协议规范草案**。

---

## 🏗️ **整体架构**

```mermaid
graph TD
    subgraph "你的应用 (Super Agent Platform)"
        UI[前端 UI]
        API_CHAT[<b>/api/chat</b><br>为UI服务]
        API_A2A[<b>/api/a2a/delegate</b><br>为外部Agent服务 (A2A标准)]
        Core[核心 Agent 逻辑<br>(Agent Manager)]
        
        subgraph "内部专业 Agent"
            WA[WMS Agent]
            FA[FMS Agent]
        end
        
        Manifest[<b>/.well-known/agent.json</b><br>本应用的Agent名片]
    end
    
    subgraph "外部 Agent (其他团队)"
        EA[Team A Agent<br>独立的 A2A 兼容应用]
        EA_Manifest{[/.well-known/agent.json]}
    end
    
    UI --> API_CHAT
    API_CHAT --> Core
    
    EA -- "1. 发现" --> Manifest
    Core -- "2. 委托" --> EA
    
    EA -- "3. 委托" --> API_A2A
    API_A2A -- "适配" --> Core
    
    Core --> WA & FA
```

---

## 🔧 **核心组件详解**

### **1. Super Agent (`/api/chat/route.ts`)**
- **职责**：作为系统的智能入口，是**协调者**和**通用问题处理者**。
- **上下文**：保持轻量级（预估 **~6K tokens**），只包含通用提示词和核心工具（包括委托工具）。
- **面向对象**：**只服务于前端 UI**。其 API 格式保持简单，无需改动。

### **2. 内部专业 Agent (Internal Specialized Agents)**
- **定义**：在同一个应用内部，通过类（`class WMSAgent extends BaseAgent`）实例化的、专门处理特定领域任务的 Agent。
- **上下文**：每个 Agent 只加载自己领域的专业工具和系统提示词，保持高效的上下文长度（预估 **~13K tokens**）。

### **3. A2A 兼容外部 Agent (A2A-Compatible External Agents)**
- **定义**：由其他团队开发的、完全遵守 Google A2A 协议的独立 Agent 应用。
- **交互方式**：我们的系统通过其 `/.well-known/agent.json` 进行发现，并通过其标准化的委托端点进行通信。

### **4. Agent Manager**
- **职责**：作为 Agent 的注册中心和调度中心。
- **能力**：
  - 统一管理内部和外部 Agent。
  - 从一个"种子列表"开始，**自动发现**并注册外部的 A2A Agent。
  - 对外部 Agent 进行**健康监控**，实现容错。

---

## 🌐 **协议与标准化：采纳 Google A2A 协议**

为确保系统的开放性和互操作性，我们将严格遵循 Google A2A 协议草案的核心规范。

### **1. Agent 发现 (Discovery)**
- 每个 Agent（包括我们的 Super Agent）都必须提供一个标准的"名片"文件。
- **路径**：`/.well-known/agent.json`
- **内容**：描述 Agent 的 ID、名称、能力、以及最重要的——**通信端点**。

### **2. 通信协议 (Communication Protocol)**
- **标准**：**JSON-RPC 2.0 over HTTP**。
- **优点**：这是一个成熟的远程过程调用（RPC）标准，比简单的 RESTful API 更严谨，明确定义了请求（`method`, `params`, `id`）和响应（`result`, `error`, `id`）的结构。

### **3. 任务委托 (Task Delegation)**
- **方法 (Method)**：核心的委托方法是 `CreateTask`。
- **载荷 (Payload)**：请求体需要封装在一个 `params` 对象中，其核心是 `task` 对象。
- **关键参数**：`task.inputs.conversation_history.value`，用于**传递完整的对话 `messages` 列表**。

**`CreateTask` 请求体规范示例:**
```json
{
  "jsonrpc": "2.0",
  "method": "CreateTask",
  "params": {
    "task": {
      "title": "A brief, human-readable description of the task.",
      "inputs": {
        "conversation_history": {
          "type": "conversation",
          "value": [
            { "role": "user", "content": "The user's message" }
          ]
        }
      }
    }
  },
  "id": "request-id-12345"
}
```

---

## 🔌 **API 架构分离：UI vs. A2A**

为保证系统稳定性和关注点分离，我们将设立两个不同的 API 端点：

1.  **`/api/chat/route.ts` (现有，不对外暴露)**
    - **用途**：专门服务于我们自己的前端 UI。
    - **格式**：继续使用当前简单的 JSON 格式。
    - **状态**：**无需任何改造**，保持稳定。

2.  **`/api/a2a/delegate/route.ts` (新增)**
    - **用途**：专门用于接收来自其他外部 Agent 的、符合 A2A 规范的委托请求。
    - **格式**：严格处理 JSON-RPC 2.0 和 `CreateTask` 方法。
    - **功能**：作为"适配器"，将收到的 A2A 标准请求转换为我们内部 `AgentExecutionContext`，然后交由核心逻辑处理。

---

## 🔄 **实施策略 (渐进式演进)**

- **阶段一：MVP - 内部委托与 A2A 兼容**
  - **目标**：验证核心委托机制，并让 Super Agent 对外符合 A2A 规范。
  - **任务**：
    1.  改造 `route.ts`，实现 `SuperAgent` 和第一个内部 `WMSAgent`。
    2.  创建 `/public/.well-known/agent.json` 文件。
    3.  创建 `/api/a2a/delegate` 端点，使其能接收标准 A2A 请求。

- **阶段二：实现外部 Agent 发现与交互**
  - **目标**：让 Super Agent 能够主动与外部 Agent 通信。
  - **任务**：
    1.  创建 `A2ACompatibleAgent` 类，实现 A2A 发现和 `CreateTask` 调用逻辑。
    2.  在 `AgentManager` 中添加从 `config/a2a-discovery-seeds.json` 加载和管理外部 Agent 的功能。

- **阶段三：完善生态与安全**
  - **目标**：构建完整的内部 Agent 矩阵，并增强安全性。
  - **任务**：
    1.  实现 `FMSAgent`, `TMSAgent` 等所有内部 Agent。
    2.  在 A2A 通信层添加 OAuth 2.0 等认证机制。

---

## ✅ **方案优势总结**

1.  **解决核心痛点**：通过将上下文分散到专业 Agent，从根本上解决了上下文过长的问题。
2.  **符合业界标准**：采纳 Google A2A 协议，使我们的系统具备了**高度的互操作性**和**开放性**。
3.  **架构清晰**：通过分离 UI API 和 A2A API，保证了系统的**稳定性和安全性**。
4.  **高度可扩展性**：可以方便地添加新的内部 Agent，也可以无缝地将任何 A2A 兼容的外部 Agent 接入我们的生态。
5.  **风险可控**：实施策略清晰，可分阶段进行，且对现有核心功能**零侵入**。

这个方案将使你的项目从一个单一的聊天机器人，演进为一个**符合行业标准、具备高度智能、专业能力和无限扩展潜力的多 Agent 平台**。 