# 依赖
/node_modules
/mcp_server_example/wms_mcp_server/mcpenv
/mcp_server_example/browser_mcp_server/browservenv
/.pnp
.pnp.js

# 测试
/coverage

# Next.js
/.next/
/out/

# 生产环境
/build

# 杂项
.DS_Store
*.pem

# 调试
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 本地环境文件\
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Vercel
.vercel

# 聊天历史记录
/src/chat-history

# TypeScript增量编译缓存
tsconfig.tsbuildinfo
*.tsbuildinfo
.vscode/

data/reports
.next-*/

.cursor
memory.db
public/embed-bundle.js
vector_store.db

mcp_server_example/*/__pycache__/
mcp_server_example/*/fmsenv/
mcp_server_example/*/tools/__pycache__/