import React from 'react';
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>eader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Trash2, Edit, Clock } from 'lucide-react';
import '@/styles/item-design-system.css';

interface MemoryCardProps {
  id: string;
  content: string;
  type: 'personal' | 'knowledge';
  score?: number;
  timestamp?: string;
  onEdit?: (id: string) => void;
  onDelete?: (id: string) => void;
}

export function MemoryCard({
  id,
  content,
  type,
  score,
  timestamp,
  onEdit,
  onDelete
}: MemoryCardProps) {
  // Format timestamp if provided
  const formattedTime = timestamp ? new Date(timestamp).toLocaleString() : undefined;

  // Format score to percentage if provided
  const formattedScore = score ? `${Math.round(score * 100)}%` : undefined;

  return (
    <Card className="w-full mb-4 border-l-4 hover:shadow-md transition-shadow bg-item-bg-card border-item-gray-800"
      style={{ borderLeftColor: type === 'personal' ? 'var(--item-purple)' : 'var(--item-gray-400)' }}>
      <CardHeader className="pb-2">
        <div className="flex justify-between items-center">
          <Badge variant={type === 'personal' ? 'default' : 'secondary'} className={type === 'personal' ? 'bg-item-purple hover:bg-item-purple-dark' : 'bg-item-gray-700 hover:bg-item-gray-700/80'}>
            {type === 'personal' ? 'Personal Memory' : 'System Knowledge'}
          </Badge>
          {formattedScore && (
            <Badge variant="outline" className="ml-2">
              Match: {formattedScore}
            </Badge>
          )}
        </div>
        <CardTitle className="text-base mt-2">{content}</CardTitle>
      </CardHeader>

      <CardFooter className="pt-2 flex justify-between items-center text-sm text-muted-foreground">
        <div className="flex items-center">
          {formattedTime && (
            <span className="flex items-center text-xs">
              <Clock className="h-3 w-3 mr-1" />
              {formattedTime}
            </span>
          )}
        </div>

        <div className="flex gap-2">
          {onEdit && (
            <Button
              variant="ghost"
              size="sm"
              className="h-8 px-2 text-blue-600"
              onClick={() => onEdit(id)}
            >
              <Edit className="h-4 w-4 mr-1" />
              Update
            </Button>
          )}

          {onDelete && (
            <Button
              variant="ghost"
              size="sm"
              className="h-8 px-2 text-red-600"
              onClick={() => onDelete(id)}
            >
              <Trash2 className="h-4 w-4 mr-1" />
              Delete
            </Button>
          )}
        </div>
      </CardFooter>
    </Card>
  );
}
