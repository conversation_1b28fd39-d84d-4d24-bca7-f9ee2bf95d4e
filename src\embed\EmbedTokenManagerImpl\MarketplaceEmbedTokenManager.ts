import { EmbedTokenManager } from '../EmbedTokenManager';

export class MarketplaceEmbedTokenManager implements EmbedTokenManager {
  getRefreshToken(): string | null {
    return null;
  }

  getCurrentToken(): string | null {
    return null;
  }

  async refreshToken(): Promise<string | null> {
    return null;
  }

  updateClientToken(token: string): void {
    // 空实现
  }

  removeToken(): void {
    // 空实现
  }
} 