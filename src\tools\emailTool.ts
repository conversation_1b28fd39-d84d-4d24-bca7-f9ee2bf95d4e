import { tool } from 'ai';
import { z } from 'zod';
// 需要先安装 nodemailer: npm install nodemailer
import nodemailer from 'nodemailer';

/**
 * Email tool
 * Implements the same parameter descriptions as email.py
 */
export const emailTool = tool({
  description: 'Send email to specified recipients. IMPORTANT: You must first compose the complete email body with all necessary content before calling this tool. Always include \'to_addresses\', \'subject\', and \'body\' parameters. Format the body properly with paragraphs, line breaks, and clear structure for readability.',
  parameters: z.object({
    to_addresses: z.array(z.string()).describe("List of recipient email addresses (e.g., ['<EMAIL>'])"),
    subject: z.string().describe('Email subject - should be clear and concise'),
    body: z.string().describe('Email content - REQUIRED. Must be fully composed with all necessary information before calling this tool. Format with proper paragraphs and structure.'),
    cc_addresses: z.array(z.string()).optional().describe('List of CC recipient email addresses'),
    bcc_addresses: z.array(z.string()).optional().describe('List of BCC recipient email addresses'),
    is_html: z.boolean().optional().describe('Whether the email body is HTML content'),
  }),
  execute: async ({ to_addresses, subject, body, cc_addresses = [], bcc_addresses = [], is_html = false }) => {
    // SMTP config (should be set via environment variables or config file)
    const smtpConfig = {
      host: process.env.SMTP_HOST || 'smtp.example.com',
      port: Number(process.env.SMTP_PORT) || 587,
      secure: false, // true for 465, false for other ports
      auth: {
        user: process.env.SMTP_USER || '<EMAIL>',
        pass: process.env.SMTP_PASS || 'password',
      },
    };
    const from = process.env.SMTP_FROM || smtpConfig.auth.user;
    try {
      const transporter = nodemailer.createTransport(smtpConfig);
      
      const info = await transporter.sendMail({
        from,
        to: to_addresses,
        cc: cc_addresses.length > 0 ? cc_addresses : undefined,
        bcc: bcc_addresses.length > 0 ? bcc_addresses : undefined,
        subject,
        [is_html ? 'html' : 'text']: body
      });
      return {
        success: true,
        message: 'Email sent successfully',
        details: {
          to: to_addresses, cc: cc_addresses, bcc: bcc_addresses, subject, messageId: info.messageId
        }
      };
    } catch (e: any) {
      return {
        success: false,
        error: e.message || String(e),
        message: 'Failed to send email'
      };
    }
  }
}); 