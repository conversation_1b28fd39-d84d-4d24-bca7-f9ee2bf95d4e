'use client';

import React, { useState } from 'react';
import '@/styles/item-design-system.css';

interface WmsApiResultItem {
  path: string;
  method: string;
  similarity: number;
  summary: string;
  description: string;
  parameters?: {
    headers?: any[];
    path?: any[];
    query?: any[];
    body?: any;
  };
}

interface WmsApiSearchResult {
  success: boolean;
  apis?: WmsApiResultItem[];
  message?: string;
  error?: string;
}

interface WmsApiCallResult {
  success: boolean;
  status_code?: number;
  data?: any;
  error?: string;
  message?: string;
}

interface WMSCardProps {
  toolInvocation?: any;
  type?: 'search' | 'call';
  result?: any;
  searchQuery?: string;
  apiPath?: string;
  apiMethod?: string;
}

export default function WMSCard({
  toolInvocation,
  type: propType,
  result: propResult,
  searchQuery: propSearchQuery,
  apiPath: propApiPath,
  apiMethod: propApiMethod
}: WMSCardProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  // Keep debug switch but default off
  const [showDebug, setShowDebug] = useState(false);
  
  // State variable to save API search results
  const [savedApiResults, setSavedApiResults] = useState<any>(null);
  
  // Extract data from tool invocation or direct properties
  let type: 'search' | 'call' | undefined, 
      rawResult: any = null,
      result: any, 
      searchQuery: string | undefined, 
      apiPath: string | undefined, 
      apiMethod: string | undefined,
      apiParams: any = null;
  
  if (toolInvocation) {
    if (toolInvocation.state === 'result') {
      // Determine type based on tool name
      type = toolInvocation.toolName === 'find_wms_api' ? 'search' : 'call';
      
      // Try to get results from different possible locations
      if (toolInvocation.result?.result) {
        rawResult = toolInvocation.result.result;
      } else if (toolInvocation.result) {
        // If result exists but result.result doesn't, use result directly
        rawResult = toolInvocation.result;
      } else if (toolInvocation.apis) {
        // Try to get apis from top level
        rawResult = {
          success: true,
          apis: toolInvocation.apis,
          message: `Found ${toolInvocation.apis.length} APIs`
        };
      }
      
      // Save API results directly if they exist
      if ('apis' in toolInvocation && Array.isArray(toolInvocation.apis)) {
        const apiResults = {
          success: true,
          apis: toolInvocation.apis,
          message: `Found ${toolInvocation.apis.length} relevant APIs`
        };
        setSavedApiResults(apiResults);
      }
      
      if (type === 'search') {
        searchQuery = toolInvocation.args?.query;
      } else {
        apiPath = toolInvocation.args?.path;
        apiMethod = toolInvocation.args?.method;
        apiParams = toolInvocation.args?.params;
      }
    } else if (toolInvocation.state === 'call') {
      // Determine call type
      type = toolInvocation.toolName === 'find_wms_api' ? 'search' : 'call';
      
      if (type === 'search') {
        searchQuery = toolInvocation.args?.query;
      } else {
        apiPath = toolInvocation.args?.path;
        apiMethod = toolInvocation.args?.method;
      }
    }
  } else {
    // Use directly passed properties
    type = propType;
    result = propResult;
    searchQuery = propSearchQuery;
    apiPath = propApiPath;
    apiMethod = propApiMethod;
  }
  
  // Extract actual useful data content
  const extractActualData = (data: any): any => {
    if (!data) {
      return null;
    }
    
    // Directly check if it's already the correct format
    if (data.success !== undefined && data.apis !== undefined) {
      return data;
    }
    
    // Check if it's an object containing type and result fields (typical format for tool return)
    if (data.type && data.result) {
      return extractActualData(data.result);
    }
    
    // Handle possible API response formats
    try {
      // Handle string format JSON
      if (typeof data === 'string') {
        try {
          return JSON.parse(data);
        } catch (e) {
          return { success: false, error: "Data parsing error" };
        }
      }
      
      // Check if it's an object containing text field
      if (data.text) {
        try {
          return JSON.parse(data.text);
        } catch (e) {
          // Error handling
        }
      }
      
      // Check data.content path
      if (data.content) {
        // Handle case when content is string
        if (typeof data.content === 'string') {
          try {
            return JSON.parse(data.content);
          } catch (e) {
            // Error handling
          }
        }
        
        // Handle case when content is array
        if (Array.isArray(data.content) && data.content.length > 0) {
          const contentItem = data.content[0];
          
          // Check text field
          if (contentItem.text) {
            try {
              return JSON.parse(contentItem.text);
            } catch (e) {
              // Error handling
            }
          }
          
          // Check content object
          if (contentItem.content) {
            if (typeof contentItem.content === 'string') {
              try {
                return JSON.parse(contentItem.content);
              } catch (e) {
                // Error handling
              }
            } else if (typeof contentItem.content === 'object') {
              return contentItem.content;
            }
          }
          
          return contentItem;
        }
      }
    } catch (e) {
      return { success: false, error: "Data parsing error" };
    }
    
    // Try to automatically build format
    if (Array.isArray(data)) {
      return {
        success: true,
        apis: data.map(item => ({...item})),
        message: `Found ${data.length} results`
      };
    }
    
    // Return original data
    return data;
  };

  // Actual extract result data
  result = extractActualData(rawResult);
  
  // If there's saved API results but current result is null, use saved results
  if (!result && savedApiResults) {
    result = savedApiResults;
  }
  
  // Tool invocation processing
  if (toolInvocation && toolInvocation.state !== 'result') {
    return (
      <div className="item-card bg-item-bg-card rounded-lg border border-item-gray-800 p-2 text-white shadow-lg my-2 ">
        <div className="flex items-center">
          <div className="bg-item-bg-hover p-1.5 rounded-lg mr-2.5">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-4 h-4 text-item-gray-400">
              <path strokeLinecap="round" strokeLinejoin="round" d="M7.5 21L3 16.5m0 0L7.5 12M3 16.5h13.5m0-13.5L21 7.5m0 0L16.5 12M21 7.5H7.5" />
            </svg>
          </div>
          <div className="flex-1 text-sm text-white font-medium pr-2 leading-snug">
            {type === 'search' 
              ? `Searching for WMS APIs related to "${searchQuery}"...`
              : `Calling WMS API: ${apiMethod} ${apiPath}`
            }
          </div>
        </div>
      </div>
    );
  }
  
  // Get API information summary
  const getApiSummary = () => {
    if (!result) return null;
    
    if (type === 'search') {
      // Search results
      const apiCount = Array.isArray(result.apis) ? result.apis.length : 0;
      return `Found ${apiCount} APIs`;
    } else {
      // Call results
      try {
        if (typeof result === 'object') {
          // Try to get result count
          if (Array.isArray(result)) {
            return `Returned ${result.length} records`;
          } else if (result.totalCount !== undefined) {
            return `Total ${result.totalCount} records`;
          } else if (result.data && Array.isArray(result.data)) {
            return `Returned ${result.data.length} records`;
          } else if (Object.keys(result).length > 0) {
            return `Returned ${Object.keys(result).length} fields`;
          }
        }
        return 'Call successful';
      } catch (e) {
        return 'Result parsing error';
      }
    }
  };
  
  // Folded view
  if (!isExpanded) {
    return (
      <div className="item-card bg-item-bg-card rounded-lg border border-item-gray-800 p-2 text-white shadow-lg my-2 cursor-pointer hover:border-item-gray-700 hover:shadow-xl transition-all duration-200 ">
        <div className="flex justify-between" onClick={() => setIsExpanded(true)}>
          <div className="flex items-center flex-1">
            <div className="bg-item-bg-hover p-1.5 rounded-lg mr-2.5">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-4 h-4 text-item-gray-400">
                <path strokeLinecap="round" strokeLinejoin="round" d="M7.5 21L3 16.5m0 0L7.5 12M3 16.5h13.5m0-13.5L21 7.5m0 0L16.5 12M21 7.5H7.5" />
              </svg>
            </div>
            <div className="text-sm text-item-gray-500 font-medium pr-2 leading-snug">
              {type === 'search' 
                ? `WMS API Search: "${searchQuery}"`
                : `WMS API Call: ${apiMethod} ${apiPath}`
              }
            </div>
          </div>
          <button className="text-item-gray-400 hover:text-item-gray-300 ml-2 flex-shrink-0 transition-colors duration-200">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-4 h-4">
              <path strokeLinecap="round" strokeLinejoin="round" d="M19.5 8.25l-7.5 7.5-7.5-7.5" />
            </svg>
          </button>
        </div>
      </div>
    );
  }
  
  // Full view
  return (
    <div className="item-card bg-item-bg-card rounded-lg border border-item-gray-800 p-2 text-white shadow-lg my-2 ">
      <div className="flex justify-between cursor-pointer" onClick={() => setIsExpanded(!isExpanded)}>
        <div className="flex items-center flex-1">
          <div className="bg-item-bg-hover p-1.5 rounded-lg mr-2.5">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-4 h-4 text-item-gray-400">
              <path strokeLinecap="round" strokeLinejoin="round" d="M7.5 21L3 16.5m0 0L7.5 12M3 16.5h13.5m0-13.5L21 7.5m0 0L16.5 12M21 7.5H7.5" />
            </svg>
          </div>
          <div className="text-sm text-white font-semibold pr-2 leading-snug">
            {type === 'search' 
              ? `WMS API Search: "${searchQuery}"`
              : `WMS API Call: ${apiMethod} ${apiPath}`
            }
          </div>
        </div>
        <button className="text-item-gray-400 hover:text-item-gray-300 hover:bg-item-bg-hover p-1.5 rounded-lg ml-2 flex-shrink-0 transition-all duration-200">
          {isExpanded ? (
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-4 h-4">
              <path strokeLinecap="round" strokeLinejoin="round" d="M4.5 15.75l7.5-7.5 7.5 7.5" />
            </svg>
          ) : (
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-4 h-4">
              <path strokeLinecap="round" strokeLinejoin="round" d="M19.5 8.25l-7.5 7.5-7.5-7.5" />
            </svg>
          )}
        </button>
      </div>
      
      {/* Only show debug view in development environment and when manually enabled */}
      {showDebug && (
        <div className="mt-3 pt-2.5 border-t border-item-gray-800/40">
          <div className="text-xs text-item-gray-400 mb-1.5 font-semibold uppercase tracking-wide">Raw data:</div>
          <pre className="text-xs overflow-auto max-h-40 font-mono whitespace-pre-wrap break-words text-item-gray-300 bg-item-bg-card p-2 rounded-lg border border-item-gray-800/30">
            {JSON.stringify({rawResult, result, type}, null, 2)}
          </pre>
        </div>
      )}
      
      {/* Search results */}
      {type === 'search' && result && (
        <div>
          {result.success ? (
            <div>
              <div className="text-xs text-item-gray-400 mb-2 font-medium uppercase tracking-wide">
                Found {Array.isArray(result.apis) ? result.apis.length : 0} relevant APIs
              </div>
              
              {Array.isArray(result.apis) && result.apis.length > 0 ? (
                <div className="space-y-2">
                  {result.apis.map((api: any, index: number) => (
                    <div key={index} className="bg-item-bg-card rounded-lg p-2 border border-item-gray-800/30 hover:border-item-gray-800 transition-colors duration-200">
                      <div className="flex justify-between items-start">
                        <div className="font-semibold text-sm text-white">{api.method} {api.path}</div>
                        <div className="text-xs px-3 py-1 rounded-full bg-item-bg-hover text-item-gray-400 font-medium">
                          {api.similarity ? `Similarity: ${(api.similarity * 100).toFixed(1)}%` : 'Unknown similarity'}
                        </div>
                      </div>
                      <div className="text-xs text-item-gray-300 mt-1.5">
                        {api.summary || api.description || 'No description available'}
                      </div>
                      
                      {/* Display API parameters */}
                      {api.parameters && (
                        <div className="mt-1.5">
                          <div className="text-xs text-item-gray-400 mb-1.5 font-medium uppercase tracking-wide">Parameters:</div>
                          
                          {/* Path parameters */}
                          {api.parameters.path && api.parameters.path.length > 0 && (
                            <div className="mb-1">
                              <div className="text-xs text-item-gray-400 font-medium">Path parameters:</div>
                              <div className="grid grid-cols-2 gap-0.5 pl-2">
                                {api.parameters.path.map((param: any, pIdx: number) => (
                                  <div key={`path-${pIdx}`} className="text-xs">
                                    <span className="text-white font-medium">{param.name}</span>
                                    {param.required && <span className="text-red-400 ml-1">*</span>}
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}
                          
                          {/* Query parameters */}
                          {api.parameters.query && api.parameters.query.length > 0 && (
                            <div className="mb-1">
                              <div className="text-xs text-item-gray-400 font-medium">Query parameters:</div>
                              <div className="grid grid-cols-2 gap-0.5 pl-2">
                                {api.parameters.query.map((param: any, pIdx: number) => (
                                  <div key={`query-${pIdx}`} className="text-xs">
                                    <span className="text-white font-medium">{param.name}</span>
                                    {param.required && <span className="text-red-400 ml-1">*</span>}
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-sm bg-item-bg-card p-2 rounded-lg border border-item-gray-800/30 text-white">
                  No APIs found for "{searchQuery}"
                </div>
              )}
            </div>
          ) : (
            <div className="text-sm bg-red-900/20 p-2 rounded-lg border border-red-500/30 text-white">
              API search failed: {result.error || 'Unknown error'}
            </div>
          )}
        </div>
      )}
      
      {/* API call results */}
      {type === 'call' && (
        <div>
          <div className="text-xs text-item-gray-400 mb-2 flex justify-between font-medium">
            <div>{getApiSummary()}</div>
            <div>Endpoint: {apiPath}</div>
          </div>
          
          {/* Add API call parameters display */}
          {apiParams && (
            <div className="mb-3 bg-item-bg-card rounded-lg p-2 border border-item-gray-800/30">
              <div className="text-xs text-item-gray-400 mb-1.5 font-semibold uppercase tracking-wide">Request Parameters:</div>
              <pre className="text-xs overflow-auto max-h-20 font-mono whitespace-pre-wrap break-words text-white">
                {typeof apiParams === 'string' ? apiParams : JSON.stringify(apiParams, null, 2)}
              </pre>
            </div>
          )}
          
          {result && result.success !== false ? (
            <div className="bg-item-bg-card rounded-lg p-2 border border-item-gray-800/30">
              <div className="text-xs text-item-gray-400 mb-1.5 font-semibold uppercase tracking-wide">Response:</div>
              <pre className="text-xs overflow-auto max-h-60 font-mono whitespace-pre-wrap break-words text-white">
                {typeof result === 'string' ? result : JSON.stringify(result, null, 2)}
              </pre>
            </div>
          ) : (
            <div className="text-sm bg-red-900/20 p-2 rounded-lg border border-red-500/30 text-white">
              API call failed: {(result && result.error) ? result.error : 'Unknown error'}
            </div>
          )}
        </div>
      )}
      
      <div className="text-xs text-item-gray-400 mt-2.5 text-right font-medium">
        WMS API Results
      </div>
    </div>
  );
} 