import { tool } from 'ai';
import { z } from 'zod';
import axios from 'axios';

/**
 * Knowledge Base Tool
 * Implemented using Vercel AI SDK's tool function
 * Query relevant knowledge base information through third-party API
 */
export const kbTool = tool({
  description: 'a tool used for querying information in the knowledge base',
  parameters: z.object({
    kbId: z.string().describe('Knowledge base ID'),
    message: z.string().describe('Query message'),
    top_k: z.number().optional().default(20).describe('Number of documents to retrieve (optional, default: 20)'),
    use_semantic_ranker: z.boolean().optional().default(true).describe('Whether to use semantic ranker (optional, default: true)'),
    files: z.array(z.string()).optional().describe('Array of file names to restrict search (optional)'),
    session_id: z.string().optional().describe('Session ID for the chat (optional)'),
    history: z.array(z.string()).optional().describe('Chat history (optional)')
  }),
  execute: async ({ kbId, message, top_k, use_semantic_ranker, files, session_id, history }) => {
    console.log('KbTool.execute called with args:', JSON.stringify({ kbId, message, top_k, use_semantic_ranker, files, session_id, history }));
    
    try {
      // Get KB_BASE_URL from environment variables
      const kbBaseUrl = process.env.KB_BASE_URL;
      
      if (!kbBaseUrl) {
        throw new Error('KB_BASE_URL environment variable is not set');
      }
      
      // Build request URL
      const url = `${kbBaseUrl}/api/knowledge/chat/v2`;
      console.log('KbTool.execute url', url);

      // Construct request body, including all parameters
      const requestBody: any = {
        kb_id: kbId,
        query: message
      };
      if (typeof top_k !== 'undefined') requestBody.top_k = top_k;
      if (typeof use_semantic_ranker !== 'undefined') requestBody.use_semantic_ranker = use_semantic_ranker;
      if (typeof files !== 'undefined') requestBody.files = files;
      if (typeof session_id !== 'undefined') requestBody.session_id = session_id;
      if (typeof history !== 'undefined') requestBody.history = history;
      
      // Send POST request
      const response = await axios.post(url, requestBody, {
        headers: {
          'Content-Type': 'application/json'
        }
      });
      
      console.log('Knowledge base query response:', response.data);
      
      // Extract generated_text from response
      const generatedText = response.data.sources || 'No relevant information found';
      
      return generatedText;
    } catch (error) {
      console.error('Failed to query knowledge base:', error);
      return `Knowledge base query failed: ${error instanceof Error ? error.message : String(error)}`;
    }
  }
}); 