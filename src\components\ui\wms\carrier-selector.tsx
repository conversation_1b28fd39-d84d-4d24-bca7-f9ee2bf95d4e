'use client';

import * as React from 'react';
import { useState, useEffect, useRef } from 'react';
import { Truck, Search, Loader2 } from 'lucide-react';
// @ts-ignore 忽略类型检查
import { debounce } from 'lodash';
import { wmsApi } from '@/utils/wmsApiClient';

import { Input } from '@/components/ui/input';
import { cn } from '@/lib/utils';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
  getCyberAgentPortalContainer,
} from "@/components/ui/select";

interface CarrierOption {
  id: string;
  name: string;
  code?: string;
  status?: string;
}

interface CarrierSelectorProps {
  value?: string;
  onChange: (value: string, carrierData?: CarrierOption) => void;
  placeholder?: string;
  disabled?: boolean;
  required?: boolean;
  facilityId?: string;
  apiHeaders?: Record<string, string>;
  defaultValue?: string;
}

// 定义WMS API响应类型
interface WmsApiResponse {
  success: boolean;
  msg?: string;
  data?: any[] | {
    list?: any[];
    [key: string]: any;
  };
  [key: string]: any;
}

export function CarrierSelector({
  value,
  onChange,
  placeholder = 'Select carrier',
  disabled = false,
  required = false,
  apiHeaders = {},
  defaultValue
}: CarrierSelectorProps) {
  // Fixed API path for carriers
  const API_PATH = 'mdm/carrier/search-by-paging';
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [options, setOptions] = useState<CarrierOption[]>([]);
  const [selectedCarrier, setSelectedCarrier] = useState<CarrierOption | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const searchInputRef = useRef<HTMLInputElement>(null);
  const initialLoadRef = useRef(false);

  // Initialize with defaultValue if provided and value is not set
  useEffect(() => {
    if (!initialLoadRef.current && defaultValue && !value) {
      initialLoadRef.current = true;
      fetchCarrierById(defaultValue).then(carrier => {
        if (carrier) {
          onChange(carrier.id, carrier);
        }
      });
    }
  }, [defaultValue, value]);

  // 当value变化时，如果已有选中的承运商ID与新value不同，清除已选承运商数据
  useEffect(() => {
    if (value !== selectedCarrier?.id) {
      setSelectedCarrier(null);
    }
  }, [value]);

  // 如果有value但没有selectedCarrier，尝试获取承运商数据
  useEffect(() => {
    if (value && !selectedCarrier && !disabled) {
      fetchCarrierById(value);
    }
  }, [value, selectedCarrier, disabled]);

  // 当下拉框打开时自动聚焦搜索框
  useEffect(() => {
    if (open && searchInputRef.current) {
      setTimeout(() => {
        searchInputRef.current?.focus();
      }, 100);
    }
  }, [open]);

  // 提取承运商数据的函数
  const extractCarrierData = (carrier: any): CarrierOption => {
    return {
      id: carrier.id || '',
      name: carrier.name || carrier.fullName || 'Unknown',
      code: carrier.code || '',
      status: carrier.status || ''
    };
  };

  // 解析API响应的函数
  const parseApiResponse = (response: any): any[] => {
    // 检查response.data是数组的情况
    if (Array.isArray(response.data)) {
      return response.data;
    }

    // 检查response.data.list的情况
    if (response.data && Array.isArray(response.data.list)) {
      return response.data.list;
    }

    // 检查response.data是对象但包含data字段的情况
    if (response.data && response.data.data && Array.isArray(response.data.data)) {
      return response.data.data;
    }

    // 直接检查response是否包含data字段为数组的情况
    if (response.data && Array.isArray(response.data)) {
      return response.data;
    }

    console.warn('Could not extract carrier list from API response:', response);
    return [];
  };

  // 通过ID获取承运商信息
  const fetchCarrierById = async (carrierId: string): Promise<CarrierOption | null> => {
    try {
      setLoading(true);
      console.log('Fetching carrier by ID:', carrierId);
      const response = await wmsApi.post<WmsApiResponse>(API_PATH, {
        id: carrierId
      });

      console.log('Fetch carrier by ID response:', response);

      if (response.success) {
        const carrierList = parseApiResponse(response);
        console.log('Parsed carrier list:', carrierList);

        const carrier = carrierList.find((c: any) => {
          return c.id && c.id.toString() === carrierId.toString();
        });

        if (carrier) {
          const carrierOption = extractCarrierData(carrier);
          console.log('Found carrier:', carrierOption);
          setSelectedCarrier(carrierOption);
          return carrierOption;
        } else {
          console.warn('Carrier not found with ID:', carrierId);
        }
      } else {
        console.error('Error fetching carrier data:', response.msg);
      }
    } catch (error) {
      console.error('Error fetching carrier data:', error);
    } finally {
      setLoading(false);
    }
    return null;
  };

  // 搜索承运商的函数
  const searchCarriers = async (query: string) => {
    if (!query.trim()) {
      setOptions([]);
      return;
    }

    try {
      setLoading(true);
      console.log('Searching carriers with query:', query);

      const response = await wmsApi.post<WmsApiResponse>(API_PATH, {
        currentPage: 1,
        pageSize: 20,
        name: query,
        status: "ACTIVE",
      });

      console.log('Carrier search API response:', response);

      if (response.success) {
        const carrierList = parseApiResponse(response);
        console.log('Parsed carrier list for search:', carrierList);

        if (carrierList.length > 0) {
          const carriers = carrierList.map((carrier: any) => extractCarrierData(carrier));
          console.log('Mapped carriers:', carriers);
          setOptions(carriers);
        } else {
          console.warn('No carriers found in the response');
          setOptions([]);
        }
      } else {
        console.warn('No carriers found or API error:', response.msg);
        setOptions([]);
      }
    } catch (error) {
      console.error('Error searching carriers:', error);
      setOptions([]);
    } finally {
      setLoading(false);
    }
  };

  // 防抖搜索
  const debouncedSearch = useRef(
    debounce((query: string) => {
      searchCarriers(query);
    }, 300)
  ).current;

  // 处理搜索输入变化
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchQuery(value);
    debouncedSearch(value);
  };

  // 处理承运商选择
  const handleCarrierSelect = (carrierId: string) => {
    const carrier = options.find(opt => opt.id === carrierId);
    if (carrier) {
      setSelectedCarrier(carrier);
      onChange(carrier.id, carrier);
      // 重置搜索状态
      setSearchQuery('');
      setOptions([]);
      setOpen(false);
    }
  };

  // 打开下拉框时触发搜索
  const handleOpenChange = (isOpen: boolean) => {
    setOpen(isOpen);

    // 如果关闭下拉框，清空搜索和结果
    if (!isOpen) {
      setSearchQuery('');
      setOptions([]);
    }
    // 如果打开下拉框且有搜索关键字，执行搜索
    else if (isOpen && searchQuery) {
      debouncedSearch(searchQuery);
    }
  };

  return (
    <div className="relative w-full">
      <Select
        value={value}
        onValueChange={handleCarrierSelect}
        open={open}
        onOpenChange={handleOpenChange}
        disabled={disabled}
      >
        <SelectTrigger
          className={cn(
            "w-full h-10 rounded-md bg-transparent",
            "border border-item-gray-700/50",
            "hover:border-item-purple focus:border-item-purple",
            "flex items-center justify-between px-3 py-2 text-sm text-white",
            "focus:outline-none focus:ring-1 focus:ring-item-purple",
            "transition-all duration-200",
            "data-[placeholder]:text-item-gray-400"
          )}
        >
          <SelectValue placeholder={placeholder}>
            {loading ? (
              <div className="flex items-center">
                <Loader2 className="mr-2 h-4 w-4 animate-spin text-item-gray-400" />
                <span>Loading...</span>
              </div>
            ) : selectedCarrier ? (
              <div className="flex items-center">
                <Truck className="mr-2 h-4 w-4 text-item-gray-400" />
                <span>{selectedCarrier.name}</span>
              </div>
            ) : null}
          </SelectValue>
        </SelectTrigger>
        <SelectContent
          className={cn(
            "border border-item-gray-700/70 bg-item-bg-card text-white",
            "rounded-md shadow-lg min-w-[300px] overflow-hidden",
            "animate-in fade-in-80 zoom-in-95"
          )}
          portalContainer={getCyberAgentPortalContainer()}
        >
          <div className="flex items-center px-3 py-2 border-b border-item-gray-700/50">
            <Search className="mr-2 h-4 w-4 shrink-0 text-item-gray-400" />
            <Input
              ref={searchInputRef}
              className="h-8 border-0 p-0 bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0 text-white placeholder:text-item-gray-400"
              placeholder="Search carriers..."
              value={searchQuery}
              onChange={handleSearchChange}
            />
          </div>

          <div className="max-h-[320px] overflow-auto scrollbar-thin scrollbar-thumb-item-gray-600 scrollbar-track-transparent">
            {loading ? (
              <div className="flex items-center justify-center py-6">
                <Loader2 className="h-6 w-6 animate-spin text-item-gray-400" />
              </div>
            ) : options.length > 0 ? (
              <SelectGroup>
                <SelectLabel className="px-3 text-xs font-medium text-item-gray-400">Carriers</SelectLabel>
                {options.map((carrier) => (
                  <SelectItem
                    key={carrier.id}
                    value={carrier.id}
                    className={cn(
                      "py-2 px-3 cursor-pointer text-white",
                      "focus:bg-item-purple focus:text-white",
                      "data-[highlighted]:bg-item-purple data-[highlighted]:text-white"
                    )}
                  >
                    <div className="flex items-center">
                      <Truck className="mr-2 h-4 w-4 text-item-gray-400" />
                      <div>
                        <div>{carrier.name}</div>
                        {carrier.code && (
                          <div className="text-xs text-item-gray-400">{carrier.code}</div>
                        )}
                      </div>
                    </div>
                  </SelectItem>
                ))}
              </SelectGroup>
            ) : searchQuery ? (
              <div className="py-6 text-center text-sm text-item-gray-400">
                No carriers found
              </div>
            ) : (
              <div className="py-6 text-center text-sm text-item-gray-400">
                Type to search carriers
              </div>
            )}
          </div>
        </SelectContent>
      </Select>
    </div>
  );
} 