{"openapi": "3.0.1", "info": {"title": "FMS", "description": "", "version": "1.0.0"}, "paths": {"/fms-platform-dispatch-management/TripDetail/GetTripStatistics": {"get": {"summary": "AI: Get itinerary statistics", "deprecated": false, "description": "", "tags": ["TripDetail"], "parameters": [{"name": "tripNo", "in": "query", "description": "Itinerary number", "required": false, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TdTripStatisticOutput"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/TripDetail/GetStopList": {"get": {"summary": "SearchStopsByTrip", "deprecated": false, "description": "Query the stop information under trip, enter trip#, and return the information as stop information under trip Detail.", "tags": ["TripDetail"], "parameters": [{"name": "tripNo", "in": "query", "description": "Itinerary number", "required": false, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StopListOutput"}}}, "headers": {}}}, "security": [{"basic": []}]}}, "/fms-platform-dispatch-management/TripDetail/GetTaskList": {"get": {"summary": "SearchTasksByTrip", "deprecated": false, "description": "Query the task information under the trip, enter trip#, and return the task information of this trip whose information is trip Detail.", "tags": ["TripDetail"], "parameters": [{"name": "tripNo", "in": "query", "description": "Itinerary number", "required": false, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TdTaskOutput"}}}}, "headers": {}}}, "security": [{"apikey-header-fms-token": []}]}}, "/fms-platform-dispatch-management/TripDetail/SearchStopList": {"post": {"summary": "AI: Search for a stop list", "deprecated": false, "description": "", "tags": ["TripDetail"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchStopTaskInput"}, "example": ""}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StopListOutput"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/TripDetail/SearchTaskList": {"post": {"summary": "AI: Search task list", "deprecated": false, "description": "", "tags": ["TripDetail"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchStopTaskInput"}, "example": ""}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TdTaskOutput"}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/TripDetail/GetPendingShipmentOrderList": {"post": {"summary": "AI: Get a list of pending freight orders", "deprecated": false, "description": "", "tags": ["TripDetail"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PendingOrderTaskInput"}, "example": ""}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PendingShipmentOrderListOutput"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/TripDetail/GetPendingWorkingOrderList": {"post": {"summary": "AI: Get a list of pending work orders", "deprecated": false, "description": "", "tags": ["TripDetail"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PendingOrderTaskInput"}, "example": ""}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PendingWorkingOrderListOutput"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/TripDetail/GetStopStatusList": {"get": {"summary": "AI: Get the status list of docks", "deprecated": false, "description": "", "tags": ["TripDetail"], "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StringSByteKeyValuePair"}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/TripDetail/GetTaskStatusList": {"get": {"summary": "AI: Get the task status list", "deprecated": false, "description": "", "tags": ["TripDetail"], "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StringSByteKeyValuePair"}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/TripDetail/GetSignatureTypeList": {"get": {"summary": "AI: Get a list of signature types", "deprecated": false, "description": "", "tags": ["TripDetail"], "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StringSByteKeyValuePair"}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/TripDetail/GetTaskDryrunReasonList": {"get": {"summary": "AI: Get a list of reasons for running tasks", "deprecated": false, "description": "", "tags": ["TripDetail"], "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StringStringKeyValuePair"}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/TripDetail/GetTaskCompleteWithExceptionList": {"get": {"summary": "AI: Get the task completed and record the exception list", "deprecated": false, "description": "", "tags": ["TripDetail"], "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StringStringListKeyValuePair"}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/TripDetail/TaskReasonList": {"post": {"summary": "AI: Get a list of tasks", "deprecated": false, "description": "", "tags": ["TripDetail"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReasonInput"}, "example": ""}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ExceptionReasonOutput"}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/TripDetail/TaskEndTerminalComplete": {"get": {"summary": "AI: End Terminal modify state to Complete state", "deprecated": false, "description": "", "tags": ["TripDetail"], "parameters": [{"name": "tripNo", "in": "query", "description": "Itinerary number", "required": false, "schema": {"type": "integer", "format": "int64"}}, {"name": "StopNo", "in": "query", "description": "Stop number", "required": false, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "boolean"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/TripDetail/TripPdfReport": {"get": {"summary": "Get pdf", "deprecated": false, "description": "", "tags": ["TripDetail"], "parameters": [{"name": "tripNo", "in": "query", "description": "", "required": false, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/TripDetail/TripPdfStopMannifest": {"get": {"summary": "Get pdf stop manifest", "deprecated": false, "description": "", "tags": ["TripDetail"], "parameters": [{"name": "tripNo", "in": "query", "description": "", "required": false, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/TripDetail/TripPdfStopMannifestData": {"get": {"summary": "Get pdf stop manifest data test", "deprecated": false, "description": "", "tags": ["TripDetail"], "parameters": [{"name": "tripNo", "in": "query", "description": "", "required": false, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/TripDetail/LoadSheetToPdf": {"get": {"summary": "/fms-platform-dispatch-management/TripDetail/LoadSheetToPdf", "deprecated": false, "description": "", "tags": ["TripDetail"], "parameters": [{"name": "tripNo", "in": "query", "description": "", "required": false, "schema": {"minimum": 1, "type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/TripDetail/GetDstTripTerminalByTask": {"get": {"summary": "/fms-platform-dispatch-management/TripDetail/GetDstTripTerminalByTask", "deprecated": false, "description": "", "tags": ["TripDetail"], "parameters": [{"name": "TripNo", "in": "query", "description": "", "required": false, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"type": "string"}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/TripDetail/CheckTaskList": {"get": {"summary": "Check the task list - determine whether wms can be synchronized", "deprecated": false, "description": "", "tags": ["TripDetail"], "parameters": [{"name": "tripNo", "in": "query", "description": "Itinerary number", "required": false, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CheckTaskListResponse"}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}}, "components": {"schemas": {"ExceptionReasonOutput": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "reason_enum": {"type": "string", "nullable": true}, "shipment_type": {"type": "string", "nullable": true}, "order_type": {"type": "string", "nullable": true}, "stop_dim": {"type": "string", "nullable": true}, "package_dim": {"type": "string", "nullable": true}, "task_dry_run": {"type": "string", "nullable": true}, "task_refuse": {"type": "string", "nullable": true}, "task_complete_exc": {"type": "string", "nullable": true}, "service_app_stage": {"type": "string", "nullable": true}, "osd": {"type": "string", "nullable": true}, "show_pickup_or_refuse": {"type": "string", "nullable": true}, "package_status": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CheckTaskListResponse": {"type": "object", "properties": {"task_no": {"type": "string", "nullable": true}, "do": {"type": "string", "nullable": true}, "order_pro": {"type": "string", "nullable": true}, "wms_order_dn": {"type": "string", "nullable": true}, "wms_status": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StringStringKeyValuePair": {"type": "object", "properties": {"key": {"type": "string", "nullable": true}, "value": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StringStringListKeyValuePair": {"type": "object", "properties": {"key": {"type": "string", "nullable": true}, "value": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "PackageExcepetion": {"type": "object", "properties": {"package_no": {"type": "string", "nullable": true}, "expection_reason": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ReasonInput": {"type": "object", "properties": {"shipment_type": {"$ref": "#/components/schemas/ShipmentTypeEnum"}, "task_type": {"$ref": "#/components/schemas/TaskTypeEnum"}, "button_type": {"type": "string", "nullable": true}}, "additionalProperties": false}, "PalletExcepetion": {"type": "object", "properties": {"pallet_no": {"type": "string", "nullable": true}, "expection_reason": {"type": "string", "nullable": true}}, "additionalProperties": false}, "PendingOrderTaskInput": {"type": "object", "properties": {"trip_no": {"type": "integer", "format": "int64"}, "shipment_order_type": {"type": "string", "nullable": true}, "page_number": {"type": "integer", "format": "int32"}, "page_size": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "PendingShipmentOrderListOutput": {"type": "object", "properties": {"total": {"type": "integer", "format": "int64"}, "all_task_outputs": {"type": "array", "items": {"$ref": "#/components/schemas/TdTaskOutput"}, "nullable": true}, "pending_pickup_task_outputs": {"type": "array", "items": {"$ref": "#/components/schemas/TdTaskOutput"}, "nullable": true}, "pending_delivery_task_outputs": {"type": "array", "items": {"$ref": "#/components/schemas/TdTaskOutput"}, "nullable": true}}, "additionalProperties": false}, "PendingWorkingOrderListOutput": {"type": "object", "properties": {"total": {"type": "integer", "format": "int64"}, "task_outputs": {"type": "array", "items": {"$ref": "#/components/schemas/TdTaskOutput"}, "nullable": true}}, "additionalProperties": false}, "ShipmentOrderOutput": {"type": "object", "additionalProperties": false, "properties": {}}, "SearchOrderInput": {"type": "object", "properties": {"shipment_order": {"type": "string", "nullable": true}, "work_order": {"type": "string", "nullable": true}, "pro": {"type": "string", "nullable": true}, "tracking_billing_ref": {"type": "string", "nullable": true}, "linehaul": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SearchShipperConsigneeInput": {"type": "object", "properties": {"location": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "phone": {"type": "string", "nullable": true}}, "additionalProperties": false}, "TaskTypeEnum": {"enum": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "type": "integer", "format": "int32"}, "SearchStopTaskInput": {"type": "object", "properties": {"trip_no": {"type": "integer", "format": "int64"}, "order_info": {"$ref": "#/components/schemas/SearchOrderInput"}, "shipper_info": {"$ref": "#/components/schemas/SearchShipperConsigneeInput"}, "consignee_info": {"$ref": "#/components/schemas/SearchShipperConsigneeInput"}, "stop_status_list": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}, "task_status_list": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}, "signature_type_list": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}, "appointment_from": {"type": "string", "format": "date-time", "nullable": true}, "appointment_to": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "ServiceLevelEnum": {"enum": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 30], "type": "integer", "format": "int32"}, "ShipmentTypeEnum": {"enum": [1, 2, 3, 4], "type": "integer", "format": "int32"}, "WorkOrderOutput": {"type": "object", "properties": {"order_no": {"type": "string", "nullable": true}, "order_status": {"$ref": "#/components/schemas/WorkOrderStatusEnum"}, "order_sub_status": {"$ref": "#/components/schemas/WorkOrderSubStatusEnum"}, "business_client": {"type": "string", "nullable": true}, "service_level": {"$ref": "#/components/schemas/ServiceLevelEnum"}, "reference": {"type": "string", "nullable": true}, "work_order_type": {"$ref": "#/components/schemas/WorkOrderTypeEnum"}, "master_order_no": {"type": "string", "nullable": true}, "rma": {"type": "string", "nullable": true}, "so_no": {"type": "string", "nullable": true}, "po_no": {"type": "string", "nullable": true}, "order_created_time": {"type": "string", "format": "date-time", "nullable": true}, "desired_service_date": {"type": "string", "format": "date-time", "nullable": true}, "service_appointment": {"type": "string", "format": "date-time", "nullable": true}, "service_complete_time": {"type": "string", "format": "date-time", "readOnly": true, "nullable": true}, "processing_days": {"$ref": "#/components/schemas/ProcessingDays"}, "hold": {"type": "boolean"}, "sales_order": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StopListOutput": {"type": "object", "properties": {"stop_list": {"type": "array", "items": {"$ref": "#/components/schemas/TdStopOutput"}, "nullable": true}}, "additionalProperties": false}, "StringSByteKeyValuePair": {"type": "object", "properties": {"key": {"type": "string", "nullable": true}, "value": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "ProcessingDays": {"type": "object", "additionalProperties": false, "properties": {}}, "TaskSignatureTypeEnum": {"enum": [0, 1, 2, 3], "type": "integer", "format": "int32"}, "TdAdditionOutput": {"type": "object", "properties": {"business_clinet": {"type": "string", "nullable": true}, "desire_service_date": {"type": "string", "format": "date-time"}, "dst_polygon_id": {"type": "string", "nullable": true}, "dst_terminal": {"type": "string", "nullable": true}, "from_address1": {"type": "string", "nullable": true}, "from_address2": {"type": "string", "nullable": true}, "from_city": {"type": "string", "nullable": true}, "from_lat": {"type": "number", "format": "double"}, "from_lng": {"type": "number", "format": "double"}, "from_loaction_id": {"type": "string", "nullable": true}, "from_name": {"type": "string", "nullable": true}, "from_phone": {"type": "string", "nullable": true}, "from_email": {"type": "string", "nullable": true}, "from_state": {"type": "string", "nullable": true}, "from_zipcode": {"type": "string", "nullable": true}, "org_polygon_id": {"type": "string", "nullable": true}, "org_terminal": {"type": "string", "nullable": true}, "packages": {"type": "integer", "format": "int32"}, "task_no": {"type": "integer", "format": "int64"}, "to_address1": {"type": "string", "nullable": true}, "to_address2": {"type": "string", "nullable": true}, "to_city": {"type": "string", "nullable": true}, "to_lat": {"type": "number", "format": "double"}, "to_lng": {"type": "number", "format": "double"}, "to_location_id": {"type": "string", "nullable": true}, "to_name": {"type": "string", "nullable": true}, "to_phone": {"type": "string", "nullable": true}, "to_email": {"type": "string", "nullable": true}, "to_state": {"type": "string", "nullable": true}, "to_zipcode": {"type": "string", "nullable": true}, "volume": {"type": "number", "format": "double"}, "weight": {"type": "number", "format": "double"}, "exception_reason": {"type": "string", "nullable": true}, "exception_data": {"type": "string", "nullable": true}, "id": {"type": "integer", "format": "int64"}, "created_time": {"type": "string", "format": "date-time", "nullable": true}, "created_by": {"type": "integer", "format": "int64", "nullable": true}, "create_user": {"type": "string", "nullable": true}, "update_time": {"type": "string", "format": "date-time", "nullable": true}, "update_by": {"type": "integer", "format": "int64", "nullable": true}, "update_user": {"type": "string", "nullable": true}, "is_del": {"type": "boolean"}, "actual_create_time": {"type": "string", "format": "date-time"}, "actual_update_time": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "TdAppointmentOutput": {"type": "object", "properties": {"appointment_date": {"type": "string", "format": "date-time"}, "appointment_no": {"type": "string", "nullable": true}, "appointment_type": {"type": "integer", "format": "int32"}, "appointment_type_text": {"type": "string", "readOnly": true, "nullable": true}, "carrier_code": {"type": "string", "nullable": true}, "carrier_name": {"type": "string", "nullable": true}, "customer_confirmed": {"type": "integer", "format": "int32"}, "driver_code": {"type": "string", "nullable": true}, "driver_name": {"type": "string", "nullable": true}, "service_confirmed": {"type": "integer", "format": "int32"}, "status": {"type": "integer", "format": "int32"}, "status_text": {"type": "string", "readOnly": true, "nullable": true}, "task_no": {"type": "integer", "format": "int64"}, "time_from": {"type": "string", "nullable": true}, "time_to": {"type": "string", "nullable": true}, "trip_no": {"type": "string", "nullable": true}, "id": {"type": "integer", "format": "int64"}, "created_time": {"type": "string", "format": "date-time", "nullable": true}, "created_by": {"type": "integer", "format": "int64", "nullable": true}, "create_user": {"type": "string", "nullable": true}, "update_time": {"type": "string", "format": "date-time", "nullable": true}, "update_by": {"type": "integer", "format": "int64", "nullable": true}, "update_user": {"type": "string", "nullable": true}, "is_del": {"type": "boolean"}, "actual_create_time": {"type": "string", "format": "date-time"}, "actual_update_time": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "TdPackageOutput": {"type": "object", "properties": {"height": {"type": "number", "format": "double"}, "length": {"type": "number", "format": "double"}, "package_class": {"type": "number", "format": "double"}, "package_no": {"type": "string", "nullable": true}, "sequence": {"type": "integer", "format": "int32"}, "task_no": {"type": "integer", "format": "int64"}, "tracking_no": {"type": "string", "nullable": true}, "volume": {"type": "number", "format": "double"}, "volume_uom": {"type": "string", "nullable": true}, "weight": {"type": "number", "format": "double"}, "weight_uom": {"type": "string", "nullable": true}, "width": {"type": "number", "format": "double"}, "id": {"type": "integer", "format": "int64"}, "created_time": {"type": "string", "format": "date-time", "nullable": true}, "created_by": {"type": "integer", "format": "int64", "nullable": true}, "create_user": {"type": "string", "nullable": true}, "update_time": {"type": "string", "format": "date-time", "nullable": true}, "update_by": {"type": "integer", "format": "int64", "nullable": true}, "update_user": {"type": "string", "nullable": true}, "is_del": {"type": "boolean"}, "actual_create_time": {"type": "string", "format": "date-time"}, "actual_update_time": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "TdPalletOutput": {"type": "object", "properties": {"task_no": {"type": "integer", "format": "int64"}, "sequence": {"type": "integer", "format": "int32"}, "pallet_no": {"type": "string", "nullable": true}, "shipment_order_no": {"type": "string", "nullable": true}, "tracking_no": {"type": "string", "nullable": true}, "item_count": {"type": "integer", "format": "int32"}, "linear_uom": {"type": "string", "nullable": true}, "length": {"type": "number", "format": "double"}, "width": {"type": "number", "format": "double"}, "height": {"type": "number", "format": "double"}, "weight": {"type": "number", "format": "double"}, "weight_uom": {"type": "string", "nullable": true}, "id": {"type": "integer", "format": "int64"}, "created_time": {"type": "string", "format": "date-time", "nullable": true}, "created_by": {"type": "integer", "format": "int64", "nullable": true}, "create_user": {"type": "string", "nullable": true}, "update_time": {"type": "string", "format": "date-time", "nullable": true}, "update_by": {"type": "integer", "format": "int64", "nullable": true}, "update_user": {"type": "string", "nullable": true}, "is_del": {"type": "boolean"}, "actual_create_time": {"type": "string", "format": "date-time"}, "actual_update_time": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "TdReferenceOutput": {"type": "object", "properties": {"billing_ref": {"type": "string", "nullable": true}, "sequence": {"type": "integer", "format": "int32"}, "status": {"type": "integer", "format": "int32"}, "status_text": {"type": "string", "readOnly": true, "nullable": true}, "stop_no": {"type": "integer", "format": "int64"}, "task_no": {"type": "integer", "format": "int64"}, "tracking_no": {"type": "string", "nullable": true}, "trip_no": {"type": "integer", "format": "int64"}, "stop_type": {"type": "integer", "format": "int32"}, "stop_type_text": {"type": "string", "readOnly": true, "nullable": true}, "task_type": {"type": "integer", "format": "int32"}, "task_type_text": {"type": "string", "readOnly": true, "nullable": true}, "id": {"type": "integer", "format": "int64"}, "created_time": {"type": "string", "format": "date-time", "nullable": true}, "created_by": {"type": "integer", "format": "int64", "nullable": true}, "create_user": {"type": "string", "nullable": true}, "update_time": {"type": "string", "format": "date-time", "nullable": true}, "update_by": {"type": "integer", "format": "int64", "nullable": true}, "update_user": {"type": "string", "nullable": true}, "is_del": {"type": "boolean"}, "actual_create_time": {"type": "string", "format": "date-time"}, "actual_update_time": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "TdStopOutput": {"type": "object", "properties": {"task_statistic_output": {"$ref": "#/components/schemas/TdStopTaskStatisticOutput"}, "first_terminal_stop": {"type": "boolean"}, "last_terminal_stop": {"type": "boolean"}, "terminal_stop_checkin_text": {"type": "string", "nullable": true}, "is_show": {"type": "boolean"}, "reference_outputs": {"type": "array", "items": {"$ref": "#/components/schemas/TdReferenceOutput"}, "nullable": true}, "task_outputs": {"type": "array", "items": {"$ref": "#/components/schemas/TdTaskOutput"}, "nullable": true}, "address1": {"type": "string", "nullable": true}, "address2": {"type": "string", "nullable": true}, "check_in_time": {"type": "string", "format": "date-time"}, "check_out_time": {"type": "string", "format": "date-time"}, "current_time": {"type": "string", "format": "date-time"}, "time_elapsed": {"type": "string", "readOnly": true, "nullable": true}, "city": {"type": "string", "nullable": true}, "country": {"type": "string", "nullable": true}, "create_way": {"type": "integer", "format": "int32"}, "latitude": {"type": "number", "format": "double"}, "location_id": {"type": "integer", "format": "int32"}, "stop_type_desc": {"type": "string", "nullable": true}, "longitude": {"type": "number", "format": "double"}, "optimize_status": {"type": "integer", "format": "int32"}, "optimize_status_text": {"type": "string", "readOnly": true, "nullable": true}, "sequence": {"type": "integer", "format": "int32"}, "reorder": {"type": "integer", "format": "int32"}, "state": {"type": "string", "nullable": true}, "stop_no": {"type": "integer", "format": "int64"}, "stop_status": {"type": "integer", "format": "int32"}, "stop_status_text": {"type": "string", "readOnly": true, "nullable": true}, "stop_type": {"type": "integer", "format": "int32"}, "stop_type_text": {"type": "string", "readOnly": true, "nullable": true}, "trip_no": {"type": "string", "nullable": true}, "zipcode": {"type": "string", "nullable": true}, "tractor": {"type": "string", "nullable": true}, "trailer": {"type": "string", "nullable": true}, "exception_reason": {"type": "string", "nullable": true}, "stop_terminal_type": {"type": "integer", "format": "int32"}, "stop_type_info": {"type": "string", "nullable": true}, "id": {"type": "integer", "format": "int64"}, "created_time": {"type": "string", "format": "date-time", "nullable": true}, "created_by": {"type": "integer", "format": "int64", "nullable": true}, "create_user": {"type": "string", "nullable": true}, "update_time": {"type": "string", "format": "date-time", "nullable": true}, "update_by": {"type": "integer", "format": "int64", "nullable": true}, "update_user": {"type": "string", "nullable": true}, "is_del": {"type": "boolean"}, "actual_create_time": {"type": "string", "format": "date-time"}, "actual_update_time": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "TdStopTaskStatisticOutput": {"type": "object", "properties": {"delivery_task_count": {"type": "integer", "format": "int32"}, "service_task_count": {"type": "integer", "format": "int32"}, "pickup_task_count": {"type": "integer", "format": "int32"}, "linehaul_task_count": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "TdTaskOutput": {"type": "object", "properties": {"button_show": {"$ref": "#/components/schemas/TdTaskOutputButtonShow"}, "is_show": {"type": "boolean"}, "addition_output": {"$ref": "#/components/schemas/TdAdditionOutput"}, "appointment_output": {"$ref": "#/components/schemas/TdAppointmentOutput"}, "package_outputs": {"type": "array", "items": {"$ref": "#/components/schemas/TdPackageOutput"}, "nullable": true}, "pallet_outputs": {"type": "array", "items": {"$ref": "#/components/schemas/TdPalletOutput"}, "nullable": true}, "reference_outputs": {"type": "array", "items": {"$ref": "#/components/schemas/TdReferenceOutput"}, "nullable": true}, "general_stop_output": {"$ref": "#/components/schemas/TdStopOutput"}, "task_sequence": {"type": "integer", "format": "int32", "readOnly": true}, "shipment_order_output": {"$ref": "#/components/schemas/ShipmentOrderOutput"}, "work_order_output": {"$ref": "#/components/schemas/WorkOrderOutput"}, "assign_status": {"type": "integer", "format": "int32"}, "assign_status_text": {"type": "string", "readOnly": true, "nullable": true}, "carrier_code": {"type": "string", "nullable": true}, "carrier_name": {"type": "string", "nullable": true}, "driver_code": {"type": "string", "nullable": true}, "driver_name": {"type": "string", "nullable": true}, "master_order_no": {"type": "string", "nullable": true}, "trip_no": {"type": "integer", "format": "int64"}, "notes": {"type": "string", "nullable": true}, "order_service_level": {"type": "integer", "format": "int32"}, "packages": {"type": "integer", "format": "int32", "readOnly": true}, "required_appointment": {"type": "integer", "format": "int32"}, "appointment_date": {"type": "string", "format": "date-time"}, "appointment_date_text": {"type": "string", "readOnly": true, "nullable": true}, "appointment_time_from": {"type": "string", "format": "date-time"}, "appointment_time_to": {"type": "string", "format": "date-time"}, "shipment_order_no": {"type": "string", "nullable": true}, "shipment_type": {"type": "integer", "format": "int32"}, "shipment_type_text": {"type": "string", "readOnly": true, "nullable": true}, "status": {"type": "integer", "format": "int32"}, "status_text": {"type": "string", "readOnly": true, "nullable": true}, "task_no": {"type": "integer", "format": "int64"}, "task_signature_type": {"$ref": "#/components/schemas/TaskSignatureTypeEnum"}, "task_signature_type_text": {"type": "string", "readOnly": true, "nullable": true}, "task_template_id": {"type": "integer", "format": "int32"}, "task_type": {"type": "integer", "format": "int32"}, "task_type_text": {"type": "string", "readOnly": true, "nullable": true}, "task_type_group": {"type": "string", "nullable": true}, "tracking_no": {"type": "string", "nullable": true}, "volume": {"type": "number", "format": "double", "readOnly": true}, "weight": {"type": "number", "format": "double", "readOnly": true}, "work_order_no": {"type": "string", "nullable": true}, "order_no": {"type": "string", "nullable": true}, "exception_reason": {"type": "string", "nullable": true}, "package_excepetions": {"type": "array", "items": {"$ref": "#/components/schemas/PackageExcepetion"}, "nullable": true}, "pallet_excepetions": {"type": "array", "items": {"$ref": "#/components/schemas/PalletExcepetion"}, "nullable": true}, "contact_info": {"type": "string", "nullable": true}, "pallet_nos": {"type": "array", "items": {"type": "string"}, "nullable": true}, "package_nos": {"type": "array", "items": {"type": "string"}, "nullable": true}, "pro_pre_fix": {"type": "string", "nullable": true}, "invoice_pro": {"type": "string", "nullable": true}, "special_send_dock": {"type": "integer", "format": "int32"}, "tms_revenue": {"type": "number", "format": "double"}, "id": {"type": "integer", "format": "int64"}, "created_time": {"type": "string", "format": "date-time", "nullable": true}, "created_by": {"type": "integer", "format": "int64", "nullable": true}, "create_user": {"type": "string", "nullable": true}, "update_time": {"type": "string", "format": "date-time", "nullable": true}, "update_by": {"type": "integer", "format": "int64", "nullable": true}, "update_user": {"type": "string", "nullable": true}, "is_del": {"type": "boolean"}, "actual_create_time": {"type": "string", "format": "date-time"}, "actual_update_time": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "TdTaskOutputButtonShow": {"type": "object", "properties": {"complete_show": {"type": "boolean"}, "complete_cancel_show": {"type": "boolean"}, "undispatch_show": {"type": "boolean"}, "dryrun_show": {"type": "boolean"}, "dryrun_cancel_show": {"type": "boolean"}, "transfer_show": {"type": "boolean"}, "task_refused_show": {"type": "boolean"}, "tas_refused_cancel_show": {"type": "boolean"}, "task_complete_with_exception_show": {"type": "boolean"}, "task_partial_complete_show": {"type": "boolean"}, "task_service_failed": {"type": "boolean"}}, "additionalProperties": false}, "TdTripStatisticItemOutput": {"type": "object", "properties": {"complete_count": {"type": "integer", "format": "int32"}, "dry_run_count": {"type": "integer", "format": "int32"}, "total_count": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "TdTripStatisticOutput": {"type": "object", "properties": {"delivery_tasks": {"$ref": "#/components/schemas/TdTripStatisticItemOutput"}, "service_tasks": {"$ref": "#/components/schemas/TdTripStatisticItemOutput"}, "pickup_tasks": {"$ref": "#/components/schemas/TdTripStatisticItemOutput"}, "linehaul_tasks": {"$ref": "#/components/schemas/TdTripStatisticItemOutput"}}, "additionalProperties": false}, "WorkOrderStatusEnum": {"enum": [1000, 1100, 1200, 1300, 1400, 1500, 1600, 1700, 1800, 1900, 2000], "type": "integer", "format": "int32"}, "WorkOrderSubStatusEnum": {"enum": [1000, 1100, 1210, 1211, 1300, 1400, 1500, 1600, 1700, 1800, 1900, 2000], "type": "integer", "format": "int32"}, "WorkOrderTypeEnum": {"enum": [0, 1], "type": "integer", "format": "int32"}}}}