import asyncio
import logging
import os
import sys
import traceback
from typing import Dict, Any, Optional, List, Union
from contextlib import asynccontextmanager
from collections.abc import AsyncIterator
from dataclasses import dataclass

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
)
logger = logging.getLogger("fms_mcp_server")

# Add parent directory to PATH to import tools
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
print(f"Current working directory: {os.getcwd()}")
print(f"Added to path: {parent_dir}")
sys.path.append(parent_dir)

# Try to import FastMCP and FMS tools
try:
    from fastmcp import FastMCP, Context
    logger.info("Successfully imported MCP FastMCP")
except ImportError as e:
    # If modules not found, try to install them
    logger.warning(f"Failed to import required modules: {e}, trying to install...")
    try:
        import subprocess
        subprocess.check_call([sys.executable, "-m", "pip", "install", "mcp[cli]"])
        from fastmcp import FastMCP, Context
        logger.info("Successfully installed and imported MCP")
    except Exception as install_error:
        logger.error(f"Failed to install MCP: {install_error}")
        traceback.print_exc()
        sys.exit(1)

# Ensure config file exists
if not os.path.exists(os.path.join(parent_dir, "config.py")):
    try:
        with open(os.path.join(parent_dir, "config.py"), "w", encoding="utf-8") as f:
            f.write("""import os
from dotenv import load_dotenv

# Try to load environment variables from .env file (if exists)
try:
    load_dotenv()
except ImportError:
    pass

# FMS Configuration
FMS_BASE_URL = os.getenv("FMS_BASE_URL", "https://fms-staging.item.com/api")
""")
        logger.info("Created default config.py file")
    except Exception as config_error:
        logger.error(f"Failed to create config.py: {config_error}")

# Try to import FMS tools
try:
    from tools.fms import FMSTools
    logger.info("Successfully imported FMS tools")
except ImportError as e:
    logger.error(f"Failed to import FMS tools: {e}")
    sys.exit(1)

# Define FMSContext type for storing global FMS tools instance
@dataclass
class FMSContext:
    fms_tools: Optional[FMSTools] = None

# Create server lifecycle manager
@asynccontextmanager
async def fms_lifespan(server: FastMCP) -> AsyncIterator[FMSContext]:
    """Initialize FMS tools instance and clean up resources when server stops"""
    logger.info("Starting FMS MCP Server lifecycle")
    
    # Initialize FMS tools
    try:
        logger.info("Initializing FMS tools")
        fms_tools = FMSTools()
        logger.info("FMS tools initialized successfully")
        
        # Create context object
        context = FMSContext(fms_tools=fms_tools)
        
        # Add context directly to server application state for access in tool functions
        if hasattr(server, 'app'):
            if not hasattr(server.app, 'state'):
                server.app.state = type('State', (object,), {})
            server.app.state.fms_context = context
        
        yield context
    except Exception as e:
        logger.error(f"Failed to initialize FMS tools: {e}")
        traceback.print_exc()
        # Continue even if initialization fails to avoid server startup failure
        yield FMSContext(fms_tools=None)
    finally:
        # Clean up resources when server shuts down
        logger.info("Shutting down FMS MCP Server")

# Create FastMCP instance with specified name and lifecycle manager
mcp = FastMCP(
    name="FMS MCP Server", 
    version="1.0.0",
    description="Factory Management System MCP Server",
    lifespan=fms_lifespan,
    dependencies=["aiohttp", "dotenv"]  # Add dependencies for easier installation
)

@mcp.tool()
async def find_fms_api(query: str, top_k: int = 5, ctx: Context = None) -> Dict[str, Any]:
    """
    Search for matching FMS API endpoints
    
    Args:
        query: Search query text
        top_k: Number of results to return
        ctx: MCP context object
        
    Returns:
        List of matching APIs
    """
    logger.info(f"Calling find_fms_api with query: {query}, top_k: {top_k}")
    
    # Get FMS tools instance - try different context attribute names
    fms_tools = None
    if ctx:
        # Check ctx itself and possible nested locations
        if hasattr(ctx, 'app') and hasattr(ctx.app, 'state') and hasattr(ctx.app.state, 'fms_context'):
            fms_tools = ctx.app.state.fms_context.fms_tools
        elif hasattr(ctx, 'lifespan_ctx'):
            fms_tools = getattr(ctx.lifespan_ctx, 'fms_tools', None)
        elif hasattr(ctx, 'fms_context'):
            fms_tools = ctx.fms_context.fms_tools
        elif hasattr(ctx, 'fms_tools'):
            fms_tools = ctx.fms_tools
        # Log context structure for debugging
        logger.debug(f"Context structure: {dir(ctx)}")
    
    # If cannot get from context, create a new instance
    if not fms_tools:
        logger.warning("Could not get FMS tools from context, creating a new instance")
        try:
            fms_tools = FMSTools()
        except Exception as e:
            error_msg = f"Failed to create FMS tools: {e}"
            logger.error(error_msg)
            raise Exception(error_msg)
    
    try:
        # Call find_fms_api method of FMS tools
        result = await fms_tools.find_fms_api(query=query, top_k=top_k)
        # Log processing, avoid string type errors
        if isinstance(result, dict):
            apis_count = len(result.get('apis', []))
            logger.info(f"find_fms_api found {apis_count} APIs")
        else:
            logger.info(f"find_fms_api completed successfully, result type: {type(result).__name__}")
        return result
    except Exception as e:
        logger.error(f"Error in find_fms_api execution: {e}")
        traceback.print_exc()
        raise

@mcp.tool()
async def call_fms_api(
    path: str, 
    method: str, 
    params: Union[Dict[str, Any], List[Any]] = {}, 
    headers: Dict[str, Any] = None,
    ctx: Context = None
) -> Dict[str, Any]:
    """
    Call a specific FMS API
    
    Args:
        path: API path
        method: HTTP method (GET, POST, PUT, DELETE)
        params: API parameters, can be either a dictionary or array depending on API requirements
        headers: Request headers
        ctx: MCP context object
        
    Returns:
        API call result
    """
    logger.info(f"Calling call_fms_api with path: {path}, method: {method}, params: {params}")
    
    # Validate headers must contain necessary information
    if not headers or not isinstance(headers, dict):
        logger.error("Missing required headers for API call")
        return {
            "success": False,
            "error": "Missing headers",
            "message": "API call requires headers with Fms-token, Company-id"
        }
    
    # Check if headers include tenant and facility information
    # Get from requestContext first, then from headers parameter
    company_id = headers.get('Company-id', '')
    fms_token = headers.get('fms-token', '')
    
    # Log passed company_id and fms_token
    logger.info(f"Using company_id: {company_id}, fms_token: {fms_token}")
    
    # Get FMS tools instance - try different context attribute names
    fms_tools = None
    if ctx:
        # Check ctx itself and possible nested locations
        if hasattr(ctx, 'app') and hasattr(ctx.app, 'state') and hasattr(ctx.app.state, 'fms_context'):
            fms_tools = ctx.app.state.fms_context.fms_tools
        elif hasattr(ctx, 'lifespan_ctx'):
            fms_tools = getattr(ctx.lifespan_ctx, 'fms_tools', None)
        elif hasattr(ctx, 'fms_context'):
            fms_tools = ctx.fms_context.fms_tools
        elif hasattr(ctx, 'fms_tools'):
            fms_tools = ctx.fms_tools
    
    # If cannot get from context, create a new instance
    if not fms_tools:
        logger.warning("Could not get FMS tools from context, creating a new instance")
        try:
            fms_tools = FMSTools()
        except Exception as e:
            error_msg = f"Failed to create FMS tools: {e}"
            logger.error(error_msg)
            raise Exception(error_msg)
    
    try:
        # Call call_fms_api method of FMS tools
        result = await fms_tools.call_fms_api(
            path=path,
            method=method,
            params=params,
            headers=headers
        )
        # Safely log results
        logger.info(f"call_fms_api completed successfully, result type: {type(result).__name__}")
        if isinstance(result, dict):
            status = "Success" if result.get('success') else "Failure"
            logger.info(f"call_fms_api call {status}, status_code: {result.get('status_code')}")
        return result
    except Exception as e:
        logger.error(f"Error in call_fms_api execution: {e}")
        traceback.print_exc()
        raise

# Add FMS tools guide prompt
@mcp.prompt()
def fms_guide() -> str:
    """FMS tools usage guide prompt"""
    try:
        # Try to read prompt from file
        fms_prompt_path = os.path.join(parent_dir, "prompts", "fms_prompt.py")
        logger.info(f"Reading FMS prompt from: {fms_prompt_path}")
        
        if os.path.exists(fms_prompt_path):
            try:
                with open(fms_prompt_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    # Extract FMS_TOOLS_PROMPT variable value
                    if "FMS_TOOLS_PROMPT = '''" in content:
                        start_index = content.find("FMS_TOOLS_PROMPT = '''") + len("FMS_TOOLS_PROMPT = '''")
                        end_index = content.find("'''", start_index)
                        if end_index > start_index:
                            prompt_content = content[start_index:end_index]
                            logger.info(f"Successfully extracted FMS prompt content, length: {len(prompt_content)}")
                            return prompt_content
            except Exception as read_error:
                logger.error(f"Error reading prompt file: {read_error}")
    except Exception as e:
        logger.error(f"Error creating prompt: {e}")
    
    # If reading fails or file not found, return default prompt
    return """
    ## Guide for FMS Tools
    
    FMS Tools provides functionality for managing factory operations.
    
    Available tools:
    
    1. find_fms_api - Search for relevant FMS API endpoints
       Usage: Use this tool to find API endpoints that match your query
       
    2. call_fms_api - Call a specific FMS API endpoint
       Usage: After finding the right API, use this to execute the call
       
    When using these tools, follow these steps:
    1. First, use find_fms_api to discover the appropriate API endpoint
    2. Review the results to find the best matching API
    3. Use call_fms_api with the exact path and parameters from the API details
    
    Example workflow:
    1. find_fms_api(query="query inventory")
    2. Review results to find inventory API
    3. call_fms_api(path="/inventory/search", method="POST", params={"itemId": "123"})
    """

# Add resource node providing FMS API documentation
@mcp.resource("fms://api-docs")
def get_fms_api_docs() -> str:
    """Get FMS API documentation summary"""
    return """
    # FMS API Documentation Summary
    
    FMS system provides the following main API categories:
    
    ## Inventory Related
    - /inventory/search - Search inventory
    - /inventory/search-sum-by-paging - Paginated inventory summary search
    - /inventory/count - Inventory count
    
    ## Order Related
    - /order/search - Search orders
    - /order/{id} - Get order details
    - /order/commit - Commit order
    
    ## Receipt Related
    - /receipt/search - Search receipts
    - /receipt/{id} - Get receipt details
    
    Use the find_fms_api tool to search for more detailed APIs.
    """

# Add resource node providing FMS tools usage guide
@mcp.resource("fms://tools-guide")
def get_fms_tools_guide() -> str:
    """Get FMS tools usage guide"""
    return """
    # FMS Tools Usage Guide
    
    ## find_fms_api
    
    Used to search for matching FMS API endpoints.
    
    ```python
    await find_fms_api(query="search orders", top_k=5)
    ```
    
    Parameters:
    - query: Search query text
    - top_k: Number of results to return
    
    Returns:
    List of matching APIs including path, method, parameters, etc.
    
    ## call_fms_api
    
    Used to call a specific FMS API.
    
    ```python
    await call_fms_api(
        path="/order/search",
        method="POST",
        params={"orderId": "DN-12345"},
        headers={"Authorization": "Bearer YOUR_TOKEN"}
    )
    ```
    
    Parameters:
    - path: API path
    - method: HTTP method (GET, POST, PUT, DELETE)
    - params: API parameters
    - headers: Custom request headers, including authorization token (e.g., {"Authorization": "Bearer YOUR_TOKEN"})
    
    Returns:
    API call result
    """

if __name__ == "__main__":
    # Parse command line arguments
    import argparse
    parser = argparse.ArgumentParser(description="FMS MCP Server")
    parser.add_argument("--host", default="127.0.0.1", help="Bind socket to this host")
    parser.add_argument("--port", type=int, default=8001, help="Bind socket to this port")
    parser.add_argument("--transport", choices=["stdio", "sse"], default="sse", help="Transport protocol (stdio or sse)")
    args = parser.parse_args()
    
    logger.info(f"Starting FMS MCP server with {args.transport} transport")
    
    # 根据传输模式选择不同的启动方式
    if args.transport == "sse":
        # 使用 starlette 和 uvicorn 运行 SSE 服务器
        import uvicorn
        from starlette.applications import Starlette
        from starlette.routing import Mount
        
        # 创建一个 Starlette 应用并挂载 MCP 的 SSE 应用
        app = Starlette(routes=[Mount("/", app=mcp.sse_app())])
        
        # 使用 uvicorn 运行
        uvicorn.run(
            app, 
            host=args.host, 
            port=args.port,
            timeout_keep_alive=300,  # 保持连接超时
            timeout_graceful_shutdown=300  # 优雅关闭超时
        )
    else:
        # 对于 stdio 模式，直接使用 run 方法
        mcp.run(transport="stdio") 