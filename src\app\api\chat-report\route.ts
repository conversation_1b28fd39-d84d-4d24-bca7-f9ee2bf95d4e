import { NextRequest, NextResponse } from 'next/server';
import {
  getReportByDate,
  listAllReports,
  searchReports
} from '@/chat-report/reportStorage';
import { manualTriggerDailyReport } from '@/chat-report/scheduler';
import { ReportSearchParams } from '@/chat-report/types';
import { getUserIdFromRequest } from '@/utils/authUtils';

// GET - List all reports or get a specific report by date
export async function GET(req: NextRequest) {
  try {
    // Get the current user ID
    const userId = await getUserIdFromRequest(req);

    // If not logged in, return error
    if (!userId) {
      return NextResponse.json(
        { error: 'User not logged in or session expired' },
        { status: 401 }
      );
    }

    const url = new URL(req.url);
    const date = url.searchParams.get('date');

    // If a date is specified, get that specific report
    if (date) {
      const report = await getReportByDate(date);

      if (!report) {
        return NextResponse.json(
          { error: `No report found for date ${date}` },
          { status: 404 }
        );
      }

      return NextResponse.json(report);
    }

    // Otherwise parse search params
    const searchParams: ReportSearchParams = {
      fromDate: url.searchParams.get('fromDate') || undefined,
      toDate: url.searchParams.get('toDate') || undefined,
      query: url.searchParams.get('query') || undefined,
    };

    const resolvedParam = url.searchParams.get('resolved');
    if (resolvedParam !== null) {
      searchParams.resolved = resolvedParam === 'true';
    }

    // Search reports with provided params
    if (Object.keys(searchParams).some(key => searchParams[key as keyof ReportSearchParams] !== undefined)) {
      const reports = await searchReports(searchParams);
      return NextResponse.json(reports);
    }

    // If no search params, return all reports
    const reports = await listAllReports();
    return NextResponse.json(reports);
  } catch (error) {
    console.error('Error fetching reports:', error);
    return NextResponse.json(
      { error: 'Error fetching reports' },
      { status: 500 }
    );
  }
}

// POST - Generate a new report for a specific date
export async function POST(req: NextRequest) {
  try {
    // Get the current user ID
    const userId = await getUserIdFromRequest(req);

    // If not logged in, return error
    if (!userId) {
      return NextResponse.json(
        { error: 'User not logged in or session expired' },
        { status: 401 }
      );
    }

    // Admin permission check could be added here

    const { date } = await req.json();

    // Generate a new report for the provided date or today
    try {
      console.log(`Attempting to generate report for date: ${date || 'today'}`);
      await manualTriggerDailyReport(date);
      console.log(`Report generation completed for date: ${date || 'today'}`);
      return NextResponse.json({ success: true, message: `Report for ${date || 'today'} generated successfully` });
    } catch (err) {
      console.error(`Error in manual report generation: ${err}`);
      return NextResponse.json({
        success: false,
        message: `Error generating report: ${err instanceof Error ? err.message : String(err)}`
      }, { status: 500 });
    }
  } catch (error) {
    console.error('Error generating report:', error);
    return NextResponse.json(
      { error: 'Error generating report' },
      { status: 500 }
    );
  }
}