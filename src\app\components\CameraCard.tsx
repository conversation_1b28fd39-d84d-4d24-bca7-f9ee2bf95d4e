'use client';

import React from 'react';
import ArtifactToolCard from './ArtifactToolCard';

interface CameraCardProps {
  toolInvocation: any;
}

export default function CameraCard({ toolInvocation }: CameraCardProps) {
  const args = toolInvocation?.args || {};
  const result = toolInvocation?.result || {};
  const yard = args.yard || 'cubework-ca-fontana-production';
  const cameras = args.cameras;
  
  // 获取仓库友好名称
  const getFacilityName = () => {
    return yard === 'cubework-ca-fontana-production' ? 'Fontana' : 'Valleyview';
  };

  // 获取摄像头描述 - 支持多摄像头
  const getCameraDescription = () => {
    // Handle cameras array
    if (cameras && cameras.length > 0) {
      if (cameras.length === 1) {
        const camera = cameras[0];
        const locationMap: Record<string, string> = {
          '**************': 'Spot 1',
          '**************': 'Spot 2', 
          '**************': 'Spot 3',
          '***************': 'Spot 1'
        };
        const location = locationMap[camera.ip] || camera.camera;
        return location;
      } else {
        // Multiple cameras
        return `${cameras.length} Cameras`;
      }
    }
    
    // Default case
    return 'Default Camera';
  };

  const cameraIcon = (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-4 h-4">
      <path strokeLinecap="round" strokeLinejoin="round" d="M15.75 10.5l4.72-4.72a.75.75 0 011.28.53v11.38a.75.75 0 01-1.28.53l-4.72-4.72M4.5 18.75h9a2.25 2.25 0 002.25-2.25v-9a2.25 2.25 0 00-2.25-2.25h-9A2.25 2.25 0 002.25 7.5v9a2.25 2.25 0 002.25 2.25z" />
    </svg>
  );

  const title = `Camera: ${getFacilityName()} • ${getCameraDescription()}`;

  return (
    <ArtifactToolCard
      toolInvocation={toolInvocation}
      title={title}
      icon={cameraIcon}
      borderColor="border-item-blue/30"
      iconBgColor="bg-item-blue/20"
      iconTextColor="text-item-blue"
    />
  );
}