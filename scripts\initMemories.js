// 导入 mem0ai 和 dotenv
const { Memory } = require('mem0ai/oss');
const fs = require('fs');
const path = require('path');

// 读取 .env.local 文件
const envPath = path.join(__dirname, '..', '.env.local');
const envContent = fs.readFileSync(envPath, 'utf8');

// 解析环境变量
const envVars = {};
envContent.split('\n').forEach(line => {
  const match = line.match(/^([^#=]+)=(.*)$/);
  if (match) {
    const key = match[1].trim();
    const value = match[2].trim();
    envVars[key] = value;
    process.env[key] = value;
  }
});

console.log('已加载环境变量，OpenAI API 密钥长度:', process.env.OPENAI_API_KEY?.length || 0);

// 记忆类型枚举
const MemoryType = {
  USER: 'user',
  SYSTEM: 'system'
};

// 创建记忆实例
function createMemory(collectionName) {
  return new Memory({
    version: 'v2', // 升级到 v2 版本
    embedder: {
      provider: 'openai',
      config: {
        apiKey: process.env.OPENAI_API_KEY || '',
        model: 'text-embedding-3-small',
      },
    },
    vectorStore: {
      provider: 'memory', // 使用默认内存向量存储
      config: {
        collectionName,
        dimension: 1536,
      },
    },
    llm: {
      provider: 'openai',
      config: {
        apiKey: process.env.OPENAI_API_KEY || '',
        model: 'gpt-4o-mini', // 使用较小模型以降低成本
      },
    },
  });
}

async function initSystemMemories() {
  console.log('[InitSystemMemories] 开始初始化系统记忆');

  // 创建系统记忆实例
  const systemMemory = createMemory('system_memories');

  const systemKnowledge = [
    "AI助手可以帮助回答问题、提供信息和协助操作。",
    "用户可以上传图片和文档进行分析。",
    "系统支持对数据和操作进行自然语言查询。",
    "助手可以帮助排除故障并提供最佳实践指导。",
    "系统可以记住用户偏好和过去的交互，以提供个性化体验。",
    "用户可以使用搜索功能查找历史记录和报告。",
    "系统支持多种工具和集成，包括仓库管理和运输管理功能。",
    "用户可以通过聊天界面上传图片并获取分析结果。",
    "系统可以处理多种语言的查询，包括中文和英文。",
    "用户可以查看和管理自己的聊天历史记录。",
    "系统会自动保存聊天记录，以便用户可以随时查看。",
    "用户可以选择不同的AI模型进行对话，包括OpenAI、Claude和Google模型。",
    "系统支持通过API集成第三方服务和工具。",
    "用户可以通过聊天界面查询天气信息。",
    "系统可以帮助用户解决技术问题和回答常见问题。"
  ];

  for (const knowledge of systemKnowledge) {
    await systemMemory.add(knowledge, {
      userId: 'system',
      metadata: {
        category: 'capabilities',
        importance: 8,
        source: 'initialization',
        timestamp: new Date().toISOString()
      },
      version: "v2" // 指定使用 v2 版本
    });
  }

  console.log(`[InitSystemMemories] 成功初始化 ${systemKnowledge.length} 条系统记忆`);
}

async function main() {
  try {
    await initSystemMemories();
    console.log('[InitSystemMemories] 所有系统记忆初始化成功');
    process.exit(0);
  } catch (error) {
    console.error('[InitSystemMemories] 初始化系统记忆失败:', error);
    process.exit(1);
  }
}

main();
