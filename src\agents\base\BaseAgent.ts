import { Message as VercelChatMessage, streamText } from 'ai';
import { CoreTool } from 'ai';
import { z } from 'zod';
import { clockTool } from '@/tools/clockTool';
import { weatherTool } from '@/tools/weatherTool';
import { requireUserInputTool } from '@/tools/requireUserInputTool';

// Agent执行上下文
export interface AgentExecutionContext {
  messages: VercelChatMessage[];
  model?: string;
  userId: string;
  requestId: string;
  timezone?: string;
  dataStream?: any; // DataStreamWriter from createDataStreamResponse
}

// Agent配置
export interface AgentConfig {
  id: string;
  name: string;
  description: string;
  // 静态配置工具列表
  tools: string[];
  // 系统提示词（可以是字符串或动态加载函数）
  systemPrompt: string | (() => Promise<string>);
  // 支持的模型
  supportedModels?: string[];
}

// Agent工具接口
export interface AgentTool {
  name: string;
  tool: CoreTool;
}

// Agent基类
export abstract class BaseAgent {
  protected config: AgentConfig;
  protected cachedTools: Map<string, CoreTool> = new Map();
  
  constructor(config: AgentConfig) {
    this.config = config;
  }

  get id(): string {
    return this.config.id;
  }

  get name(): string {
    return this.config.name;
  }

  get description(): string {
    return this.config.description;
  }

  // 获取系统提示词
  protected async getSystemPrompt(): Promise<string> {
    if (typeof this.config.systemPrompt === 'function') {
      return await this.config.systemPrompt();
    }
    return this.config.systemPrompt;
  }

  // 获取基础工具 - 所有Agent都会拥有的通用工具
  protected getBaseTools(): Record<string, CoreTool> {
    return {
      clockTool,
      weatherTool,
      requireUserInputTool,
      // 可以在这里添加更多通用工具
      // 比如: finishTaskTool, 等等
    };
  }

  // 加载专业工具 - 子类必须实现
  protected abstract loadSpecializedTools(): Promise<Record<string, CoreTool>>;

  // 获取工具（带缓存）
  protected async getTools(): Promise<Record<string, CoreTool>> {
    if (this.cachedTools.size === 0) {
      console.log(`[${this.config.id}] Loading tools...`);
      
      // 1. 获取基础工具（所有Agent都有）
      const baseTools = this.getBaseTools();
      
      // 2. 获取专业工具（由子类提供）
      const specializedTools = await this.loadSpecializedTools();
      
      // 3. 合并所有工具
      const allTools = { ...baseTools, ...specializedTools };
      
      // 4. 只加载配置中指定的工具 + 自动添加的基础工具
      const toolsToLoad = new Set([...Object.keys(baseTools), ...this.config.tools]);
      
      for (const toolName of toolsToLoad) {
        if (allTools[toolName]) {
          this.cachedTools.set(toolName, allTools[toolName]);
          const isBaseTool = baseTools[toolName] ? ' (base)' : ' (specialized)';
          console.log(`[${this.config.id}] Loaded tool: ${toolName}${isBaseTool}`);
        } else {
          console.warn(`[${this.config.id}] Tool not found: ${toolName}`);
        }
      }
      
      console.log(`[${this.config.id}] Loaded ${this.cachedTools.size} tools (${Object.keys(baseTools).length} base + ${this.config.tools.length} specialized)`);
    }
    
    return Object.fromEntries(this.cachedTools);
  }
  
  // 公开getTools方法，供外部使用
  async getToolsPublic(): Promise<Record<string, CoreTool>> {
    return await this.getTools();
  }


  // 执行任务 - 返回流式响应
  async execute(context: AgentExecutionContext, modelInstance: any): Promise<any> {
    console.log(`[${this.config.id}] Executing with model: ${context.model}`);
    
    // 获取系统提示词
    const systemPrompt = await this.getSystemPrompt();
    
    // 获取工具
    const tools = await this.getTools();
    
    // 包装工具以传递context（包含requestId和dataStream）
    const wrappedTools = this.wrapToolsWithContext(tools, context);
    
    // 将系统提示词作为最后一条消息
    const messagesWithSystemPrompt = [
      ...context.messages, 
      { 
        role: 'system' as const, 
        content: systemPrompt 
      },
    ];
    
    // 使用streamText返回流式响应
    const result = await streamText({
      model: modelInstance,
      messages: messagesWithSystemPrompt,
      temperature: 0.1,
      tools: wrappedTools,
      maxSteps: 30,
      headers: context.timezone ? { 'X-Timezone': context.timezone } : undefined,
      onFinish: async (result) => {
        console.log(`[${this.config.id}] Finished. Tokens: ${result.usage.totalTokens}`);
      },
      onError: (error) => {
        console.error(`[${this.config.id}] Error:`, error);
        console.error(`[${this.config.id}] Error details:`, {
          name: error?.name,
          message: error?.message,
          stack: error?.stack,
          cause: error?.cause,
          fullError: JSON.stringify(error, null, 2)
        });
      }
    });
    
    return result;
  }
  
  // 包装工具以传递requestId和context - 支持多层Agent架构
  protected wrapToolsWithContext(tools: Record<string, CoreTool>, context: AgentExecutionContext): Record<string, CoreTool> {
    const { formatToolCallId, extractRequestIdFromToolCallId } = require('../../utils/requestContext');
    
    const wrappedTools: Record<string, CoreTool> = {};
    
    for (const [toolName, tool] of Object.entries(tools)) {
      // 检查工具是否有 execute 方法
      if ('execute' in tool && typeof (tool as any).execute === 'function') {
        // 有 execute 方法的工具，进行包装
        wrappedTools[toolName] = {
          ...tool,
          execute: async (args: any, options: any) => {
            // 检查toolCallId是否已经包含requestId，避免重复包装
            const existingRequestId = extractRequestIdFromToolCallId(options.toolCallId);
            let finalToolCallId = options.toolCallId;
            
            // 只有当toolCallId还没有包含正确的requestId时才包装
            if (existingRequestId === 'default' || existingRequestId !== context.requestId) {
              // 如果已经有requestId但不匹配，则提取原始toolCallId
              if (existingRequestId !== 'default') {
                const callMarkerIndex = options.toolCallId.indexOf('_call_');
                if (callMarkerIndex !== -1) {
                  finalToolCallId = options.toolCallId.substring(callMarkerIndex + 6); // 去掉 "_call_" 前缀
                }
              }
              finalToolCallId = formatToolCallId(context.requestId, finalToolCallId);
              console.log(`[${this.config.id}] Wrapping tool ${toolName} with requestId: ${context.requestId} (original: ${options.toolCallId} → final: ${finalToolCallId})`);
            } else {
              console.log(`[${this.config.id}] Tool ${toolName} already has correct requestId: ${existingRequestId}`);
            }
            
            const modifiedOptions = {
              ...options,
              toolCallId: finalToolCallId,
              context // 添加完整的context，包含dataStream
            };
            
            // 调用原始工具
            return await (tool as any).execute(args, modifiedOptions);
          }
        };
      } else {
        // 没有 execute 方法的工具（如 requireUserInputTool），直接使用原始工具
        console.log(`[${this.config.id}] Tool ${toolName} has no execute method, using as-is`);
        wrappedTools[toolName] = tool;
      }
    }
    
    return wrappedTools;
  }
}