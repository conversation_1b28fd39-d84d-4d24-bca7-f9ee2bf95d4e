import { NextRequest } from 'next/server';
import { iamAuth } from './iamAuth';
import { jwtVerify, createRemoteJWKSet } from 'jose';
import axios from 'axios';

// 获取 IAM 基础 URL
const IAM_ENDPOINT = process.env.NEXT_PUBLIC_IAM_ENDPOINT || 'https://id-staging.item.com';

// OAuth2 服务器元数据 URL（从 IAM_ENDPOINT 拼接）
const OAUTH_METADATA_URL = `${IAM_ENDPOINT}/.well-known/oauth-authorization-server`;

// IAM 配置（初始默认值，将被动态更新）
let IAM_CONFIG = {
  JWKS_URL: `${IAM_ENDPOINT}/oauth2/jwks`,
  ISSUER: null as string | null, // 初始为 null，只从元数据中获取，不预设默认值
  AUDIENCE: process.env.NEXT_PUBLIC_IAM_CLIENT_ID || null, // 使用客户端ID作为audience
};

// 元数据是否已加载的标志
let METADATA_LOADED = false;

// JWKS 客户端缓存
let JWKS_CLIENT: any = null;

// 初始化并刷新OAuth2服务器配置
const initOAuthServerConfig = async (): Promise<void> => {
  try {
    console.log(`[Auth] Fetching OAuth server metadata from ${OAUTH_METADATA_URL}`);
    const response = await axios.get(OAUTH_METADATA_URL);
    const metadata = response.data;
    
    // 必须从元数据中获取 issuer
    if (metadata.issuer) {
      IAM_CONFIG.ISSUER = metadata.issuer;
      console.log(`[Auth] Updated issuer: ${IAM_CONFIG.ISSUER}`);
    } else {
      console.error('[Auth] No issuer found in metadata, authentication will fail');
      // 如果元数据中没有 issuer，这是一个严重错误
      // 作为最后的后备方案，使用 IAM_ENDPOINT
      IAM_CONFIG.ISSUER = IAM_ENDPOINT;
      console.log(`[Auth] Using fallback issuer: ${IAM_CONFIG.ISSUER}`);
    }
    
    if (metadata.jwks_uri) {
      IAM_CONFIG.JWKS_URL = metadata.jwks_uri;
      console.log(`[Auth] Updated JWKS URL: ${IAM_CONFIG.JWKS_URL}`);
      
      // 重置JWKS客户端以便下次使用新URL
      JWKS_CLIENT = null;
    } else {
      console.warn('[Auth] No jwks_uri found in metadata, using default');
    }
    
    METADATA_LOADED = true;
    return metadata;
  } catch (error) {
    console.error('[Auth] Failed to fetch OAuth server metadata:', error);
    console.log('[Auth] Using fallback configuration');
    // 如果完全无法获取元数据，作为最后的后备方案，使用 IAM_ENDPOINT
    if (!IAM_CONFIG.ISSUER) {
      IAM_CONFIG.ISSUER = IAM_ENDPOINT;
      console.log(`[Auth] Using fallback issuer: ${IAM_CONFIG.ISSUER}`);
    }
    METADATA_LOADED = true; // 即使失败也标记为已尝试加载
  }
};

// 调用初始化函数
initOAuthServerConfig().catch(err => {
  console.error('[Auth] Error during OAuth config initialization:', err);
  // 如果完全初始化失败，作为最后的后备方案
  if (!IAM_CONFIG.ISSUER) {
    IAM_CONFIG.ISSUER = IAM_ENDPOINT;
    console.log(`[Auth] Using emergency fallback issuer after init failure: ${IAM_CONFIG.ISSUER}`);
  }
  METADATA_LOADED = true; // 即使失败也标记为已尝试加载
});

// 初始化JWKS客户端
const getJwksClient = () => {
  if (!JWKS_CLIENT) {
    JWKS_CLIENT = createRemoteJWKSet(new URL(IAM_CONFIG.JWKS_URL));
  }
  return JWKS_CLIENT;
};

/**
 * Extract access token from request
 * @param req Request object
 * @returns Access token or null
 */
export const getAccessTokenFromRequest = (req: NextRequest): string | null => {
  try {
    // Get from Authorization header
    const authHeader = req.headers.get('Authorization');
    if (authHeader && authHeader.startsWith('Bearer ')) {
      console.log('Token retrieved from Authorization header');
      return authHeader.substring(7); // Part after "Bearer "
    }
    
    // Get from Cookie
    const accessToken = req.cookies.get('iam_access_token')?.value;
    if (accessToken) {
      console.log('Token retrieved from Cookie');
      return accessToken;
    }
    
    // Get sessionToken from request
    const sessionToken = req.headers.get('x-session-token');
    if (sessionToken) {
      console.log('Token retrieved from x-session-token header');
      return sessionToken;
    }
    
    console.log('No token found');
    // Nothing found
    return null;
  } catch (error) {
    console.error('Error extracting access token:', error);
    return null;
  }
};

/**
 * Get user info object from localStorage
 * @returns User info or null
 */
export const getUserInfoFromStorage = (): any => {
  try {
    if (typeof window !== 'undefined') {
      const userInfoStr = localStorage.getItem('iam_user_info');
      if (userInfoStr) {
        return JSON.parse(userInfoStr);
      }
    }
    return null;
  } catch (error) {
    console.error('Failed to get user info from localStorage:', error);
    return null;
  }
};

/**
 * Securely verify JWT token with proper signature validation
 * @param token JWT token to verify
 * @returns Promise resolving to payload or null if invalid
 */
export const verifyToken = async (token: string): Promise<any | null> => {
  try {
    // 确保元数据已加载
    if (!METADATA_LOADED) {
      console.log('[Auth] Waiting for metadata to load before verifying token');
      await new Promise(resolve => {
        const checkInterval = setInterval(() => {
          if (METADATA_LOADED) {
            clearInterval(checkInterval);
            resolve(true);
          }
        }, 100);
      });
    }
    
    // 确保 issuer 存在
    if (!IAM_CONFIG.ISSUER) {
      console.error('[Auth] No issuer configured, token verification will fail');
      return null;
    }
    
    const jwks = getJwksClient();
    
    // 记录验证参数
    console.log('[Auth] Token verification parameters:', {
      issuer: IAM_CONFIG.ISSUER,
      audience: IAM_CONFIG.AUDIENCE,
    });
    
    // 只校验issuer，不校验audience
    const { payload } = await jwtVerify(token, jwks, {
      issuer: IAM_CONFIG.ISSUER,
    });
    
    console.log('Token verified successfully');
    return payload;
  } catch (error) {
    console.error('Token verification failed:', error);
    return null;
  }
};

/**
 * Verify access token and extract user ID with proper security
 * @param token Access token
 * @returns Promise resolving to user ID or null
 */
export const verifyTokenAndGetUserId = async (token: string): Promise<string | null> => {
  try {
    console.log('Starting secure token verification and user ID extraction...');
    
    // Verify token with signature validation
    const payload = await verifyToken(token);
    
    if (!payload) {
      console.log('Token verification failed');
      return null;
    }
    
    // Extract user ID from verified payload
    if (payload.data && payload.data.user_id) {
      console.log('User ID retrieved from token.data.user_id:', payload.data.user_id);
      return payload.data.user_id;
    }
    
    // Try to get user ID from sub field
    if (payload.sub) {
      console.log('User ID retrieved from token.sub:', payload.sub);
      return payload.sub;
    }
    
    console.log('Unable to get user ID from verified token');
    return null;
  } catch (error) {
    console.error('Token verification failed:', error);
    return null;
  }
};

/**
 * Get user ID from request with secure token verification
 * @param req Request object
 * @returns Promise resolving to user ID or null
 */
export const getUserIdFromRequest = async (req: NextRequest): Promise<string | null> => {
  console.log('Starting to get user ID from request with secure verification...');
  
  // Get access token
  const token = getAccessTokenFromRequest(req);
  if (!token) {
    console.log('No access token found');
    return null;
  }
  
  // Verify token and get user ID with proper security
  const userId = await verifyTokenAndGetUserId(token);
  
  if (userId) {
    console.log('Successfully retrieved user ID:', userId);
  } else {
    console.log('Failed to get user ID');
  }
  
  return userId;
}; 