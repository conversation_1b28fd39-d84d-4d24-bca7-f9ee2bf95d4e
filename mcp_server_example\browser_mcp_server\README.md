# Browser MCP Server

A Model Control Protocol (MCP) server that provides browser automation powered by the [browser-use](https://github.com/browser-use/browser-use) library. This server enables AI assistants to navigate the web and perform complex web tasks.

## Features

- **Web Task Executor**: Perform virtually any web task using natural language instructions
- **Browser Automation**: Navigate websites, click buttons, fill forms, and extract data
- **AI-Powered**: Uses GPT-4o to understand and execute complex web tasks

## Requirements

- Python 3.9+
- OpenAI API key (for GPT-4o)
- Playwright (automatically installed)

## Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd browser_mcp_server
```

2. Create a virtual environment:
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. Install the requirements:
```bash
pip install -r requirements.txt
```

4. Install Playwright browsers:
```bash
playwright install
```

## Configuration

Create a `.env` file in the root directory with the following variables:

```
OPENAI_API_KEY=your_openai_api_key_here
BROWSER_HEADLESS=true
```

## Usage

### Starting the Server

Run the server with:

```bash
python run_server.py
```

The server will start on `http://0.0.0.0:8001` by default.

### Integrating with CyberBot

1. Add the browser MCP server configuration to your `mcp.json` file:

```json
{
  "mcpServers": {
    "browser": {
      "url": "http://localhost:8001",
      "description": "Browser Automation MCP Server"
    }
  }
}
```

2. Restart CyberBot to discover the new MCP server and its tool.

### Available Tool

#### perform_web_task

Execute a web task using natural language instructions.

```python
task_result = await perform_web_task(
    task_description="Go to GitHub, search for 'machine learning', filter by Python, and extract the top 5 repository names and descriptions"
)
```

Example tasks:

- Navigate to specific websites
- Search for information
- Fill out and submit forms
- Extract data from webpages
- Compare information across multiple sites
- Follow links and navigate through multiple pages

## Architecture

The Browser MCP Server consists of:

1. **MCP Server**: Based on FastMCP for API integration
2. **Browser Tools**: Implementation of browser-use library functionality
3. **Prompt**: System prompt for guiding AI on tool usage

## Development

### Testing

You can test the server locally by making HTTP requests to the exposed endpoint:

```bash
curl -X POST http://localhost:8001/v1/tools/perform_web_task \
  -H "Content-Type: application/json" \
  -d '{"task_description": "Go to example.com and extract the main heading"}'
```

## Troubleshooting

### Common Issues

1. **Browser installation problems**: Try running `playwright install` manually
2. **API key errors**: Ensure OPENAI_API_KEY is correctly set in your .env file
3. **Connection refused**: Make sure the server is running on the correct port

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- [browser-use](https://github.com/browser-use/browser-use) library for browser automation
- Model Control Protocol (MCP) for standardized AI tool integration 