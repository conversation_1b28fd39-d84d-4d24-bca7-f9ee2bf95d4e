import { EmbedTokenManager } from '../EmbedTokenManager';

export class TmsEmbedTokenManager implements EmbedTokenManager {
  getRefreshToken(): string | null {
    return localStorage.getItem('tms_refresh_token');
  }

  getCurrentToken(): string | null {
    return localStorage.getItem('tms_token');
  }

  removeToken(): void {
    
  }

  async refreshToken(): Promise<string | null> {
    return null;
  }

  updateClientToken(token: string) {
    const value = token.startsWith('Bearer ') ? token : `Bearer ${token}`;
    localStorage.setItem('tms_token', value);
  }
} 