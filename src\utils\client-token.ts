/**
 * @file 客户端Token工具
 * @description 用于前端生成单次请求认证Token的工具函数
 * @client-side
 */

import { TokenPayload } from '../types/site';
import JSEncrypt from 'jsencrypt';

/**
 * 获取客户端IP地址
 * @returns 客户端IP地址
 * @description 由于浏览器安全限制，无法直接获取真实IP，返回默认值
 */
export function getRealIP(): string {
  return 'unknown';
}

/**
 * 生成单次请求的临时Token
 * @param siteId 站点ID
 * @param publicKey 站点公钥
 * @returns 加密后的Token
 */
export function generateRequestToken(siteId: string, publicKey: string): string {
  try {
    // 1. 获取当前域名
    const domain = window.location.host;
    
    // 2. 构建Token载荷
    const payload: TokenPayload = {
      domain,
      timestamp: Date.now(),
      siteId,
      // 添加请求唯一标识，防止重放
      nonce: Math.random().toString(36).substring(2, 15)
    };
    
    // 3. 使用公钥加密
    const encrypt = new JSEncrypt();
    encrypt.setPublicKey(publicKey);
    
    const encrypted = encrypt.encrypt(JSON.stringify(payload));
    if (!encrypted) {
      throw new Error('Encryption failed');
    }
    
    return encrypted;
  } catch (error) {
    console.error('Failed to generate request token:', error);
    throw new Error('Failed to generate authentication token');
  }
}
