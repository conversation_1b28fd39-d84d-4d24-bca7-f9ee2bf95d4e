import {tool} from 'ai';
import {z} from 'zod';
import axios from 'axios';
import {extractRequestIdFromToolCallId, getRequestContext} from '../utils/requestContext';
import { v4 as uuidv4 } from 'uuid';

const biApiBaseUrl = process.env.NEXT_BI_API_BASE_URL || 'https://ir43pcnjei.execute-api.us-west-2.amazonaws.com/chatbipre';

/**
 * 统一的header生成方法
 * @param toolCallId 工具调用ID
 * @param additionalHeaders 额外的headers
 * @returns 包含认证信息的headers对象
 */
const buildHeaders = (toolCallId: string, additionalHeaders: Record<string, string> = {}): Record<string, string> => {
  const requestId = extractRequestIdFromToolCallId(toolCallId);
  const requestContext = getRequestContext(requestId);
  const headers: Record<string, string> = { ...additionalHeaders };
  
  // 优先使用 biToken，如果不存在则使用 authorization
  if (requestContext?.biToken) {
    headers['Authorization'] = requestContext.biToken.replace(/^Bearer\s/, '');
  } else if (requestContext?.authorization) {
    headers['Authorization'] = requestContext.authorization.replace(/^Bearer\s/, '');
  }
  // console.log('bi Headers:', headers);
  return headers;
};

// 获取指定 cube 的描述
/**
 * description：工具的描述，简要说明了它的功能：获取特定 cube 的描述信息。
 *
 * parameters：移除 authToken 参数，由全局 API 客户端处理认证。
 */
export const getCubeDescTool = tool({
  description: 'Get description for a specific cube.',
  parameters: z.object({
    // authToken: z.string().describe('Authorization token (IdToken from getCubeCognitoIdTokenTool)'), // 移除
  }),
  execute: async ({}, options: { toolCallId: string }) => { // 添加 options 参数
    try {
      // const url = `${biApiBaseUrl}/cube/GetCubeDesc`;
      const url = `${biApiBaseUrl}/cube/GetCubeDescForChatItem`;
      console.log('getCubeDescTool url:', url);
      // 获取请求上下文并构建headers
      const headers = buildHeaders(options.toolCallId);
      
      const response = await axios.get(url, {
        headers: headers
      });
      console.log('getCubeDescTool response:', JSON.stringify(response.data));
      return response.data;
    } catch (error) {
      console.error('getCubeDescTool error:', error);
      return `Failed to get cube description: ${error instanceof Error ? error.message : String(error)}`;
    }
  }
});

/**
 * Get Cube Field Enum Query IDs Tool
 */
export const getCubeFieldEnumQueryIdsTool = tool({
  description: 'Get query IDs for fetching enumerated field values within a specified cube.',
  parameters: z.object({
    cube: z.string().describe('The ID of the cube to retrieve enum query IDs for.'),
    // authToken: z.string().describe('Authorization token (IdToken from getCubeCognitoIdTokenTool)'), // 移除
  }),
  execute: async ({cube}, options: { toolCallId: string }) => { // 添加 options 参数
    try {
      const url = `${biApiBaseUrl}/cube/search`;

      const headers = buildHeaders(options.toolCallId, {
        'Content-Type': 'application/json'
      });

      const response = await axios.post(url, {
        cube: cube
      }, {
        headers: headers
      });
      console.log('getCubeFieldEnumQueryIdsTool response:', response.data);
      return response.data;
    } catch (error) {
      console.error('getCubeFieldEnumQueryIdsTool error:', error);
      return `Failed to get cube field enum query IDs: ${error instanceof Error ? error.message : String(error)}`;
    }
  }
});

/**
 * Get Cube Schema Info Tool
 */
export const getCubeSchemaInfoTool = tool({
  description: 'Fetch schema information for a specified cube. Use "all" to get schema for all accessible cubes.',
  parameters: z.object({
    cube: z.string().optional().describe('The ID of the cube (or "all") to get schema for. Defaults to "all" if not provided.'),
    // authToken: z.string().describe('Authorization token (IdToken from getCubeCognitoIdTokenTool)'), // 移除
  }),
  execute: async ({cube}, options: { toolCallId: string }) => { // 添加 options 参数
    try {
      const url = `${biApiBaseUrl}/BIChat/cube/schema`;
      const params = cube ? {cube: cube} : {};

      const headers = buildHeaders(options.toolCallId);

      const response = await axios.get(url, {
        headers: headers,
        params: params
      });
      console.log('getCubeSchemaInfoTool response:', response.data);
      return response.data;
    } catch (error) {
      console.error('getCubeSchemaInfoTool error:', error);
      return `Failed to get cube schema info: ${error instanceof Error ? error.message : String(error)}`;
    }
  }
});

/**
 * Download File (Get S3 Presigned URL) Tool
 */
export const downloadFileTool = tool({
  description: 'Generate a presigned S3 URL to download a CSV file by filename.',
  parameters: z.object({
    file_name: z.string().describe('The name of the file to download.'),
    // authToken: z.string().describe('Authorization token (IdToken from getCubeCognitoIdTokenTool)'), // 移除
  }),
  execute: async ({file_name}, options: { toolCallId: string }) => { // 添加 options 参数
    try {
      const url = `${biApiBaseUrl}/cube/file/grid`;

      const headers = buildHeaders(options.toolCallId);

      const response = await axios.get(url, {
        headers: headers,
        params: {
          file_name: file_name
        }
      });
      console.log('downloadFileTool response:', response.data);
      return response.data;
    } catch (error) {
      console.error('downloadFileTool error:', error);
      return `Failed to download file: ${error instanceof Error ? error.message : String(error)}`;
    }
  }
});

/**
 * Create Dashboard Tool
 */
export const createDashboardTool = tool({
  description: 'Execute a list of SQL queries to create a dashboard and return its URL.',
  parameters: z.object({
    params: z.array(
      z.object({
        dashboard_name: z.string().describe('Name of the dashboard.'),
        query_name: z.string().describe('Name of the query.'),
        query_sql: z.string().describe('SQL query string.'),
        db: z.string().describe('Database name.'),
        visualization_payload: z.object({
          type: z.string().describe('Visualization type, e.g., CHART.'),
          name: z.string().describe('Visualization name.'),
          description: z.string().describe('Visualization description.'),
          options: z.record(z.any()).describe('Visualization options, flexible structure, see API example.')
        }).optional().describe('Optional. Visualization configuration object.')
        // chart_type: z.string().describe('Type of chart (e.g., bar, pie, grid).'),
        // x: z.array(z.string()).describe('Fields used for the X-axis.'),
        // y: z.array(z.string()).describe('Fields used for the Y-axis.'),
        // link_format: z.object({
        //   cube_name: z.string().describe('Cube name for link formatting.'),
        //   tag: z.string().describe('Primary tag.'),
        //   stag: z.string().describe('Sub tag.'),
        //   field: z.string().describe('Field used for linking.'),
        //   ftype: z.string().describe('Field type.'),
        //   algorithm: z.string().describe('Algorithm or field used for linking.'),
        // }).optional().describe('Link format configuration.'),
      })
    ).describe('Array of query parameters to create the dashboard.'),
  }),
  execute: async ({params}: { params: any[] }, options: { toolCallId: string }) => {
    try {
      const url = `${biApiBaseUrl}/BIChat/dashboard/url`;

      const headers = buildHeaders(options.toolCallId, {
        'Content-Type': 'application/json'
      });

      console.log('Creating dashboard with params:', JSON.stringify(params, null, 2));

      const response = await axios.post(url, {
        params: params
      }, {
        headers: headers
      });
      console.log('createDashboardTool response:', response.data);
      
      // 检查响应是否成功
      if (response.data.code === 1000 && response.data.data?.dashboard_url) {
        const dashboardData = response.data.data;
        const dashboardName = params[0]?.dashboard_name || 'BI Dashboard';
        
        // 构建响应数据，包含 artifact
        const result = {
          code: response.data.code,
          message: response.data.message,
          data: dashboardData,
          dashboard_url: dashboardData.dashboard_url,
          dashboard_id: dashboardData.dashboard_id,
          dashboard_name: dashboardName,
          instruct: "Don't show the detail to the user, just tell the user that dashboard artifact is ready",
          // 添加 artifact 对象，指示前端在侧边栏渲染 iframe
          artifact: {
            id: uuidv4(),
            type: 'iframe' as const,
            title: dashboardName,
            props: {
              src: dashboardData.dashboard_url,
              width: '100%',
              height: '100%'
            }
          }
        };
        
        return result;
      } else {
        // 如果响应不成功，返回错误信息
        return {
          code: response.data.code || 500,
          message: response.data.message || 'Failed to create dashboard',
          error: response.data.message || 'Dashboard creation failed'
        };
      }
    } catch (error) {
      console.error('createDashboardTool error:', error);
      if (axios.isAxiosError(error)) {
        console.error('Dashboard creation error details:', {
          status: error.response?.status,
          statusText: error.response?.statusText,
          data: error.response?.data,
          requestData: error.config?.data
        });
        return {
          code: error.response?.status || 500,
          message: `Failed to create dashboard: ${error.response?.data?.detail || error.message}`,
          error: error.response?.data?.detail || error.message
        };
      }
      return {
        code: 500,
        message: `Failed to create dashboard: ${error instanceof Error ? error.message : String(error)}`,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }
});

/**
 * Get Cube Enum Values Tool
 */
export const getCubeEnumValuesTool = tool({
  description: 'Retrieve enumerated values for a specified cube field using a query ID.',
  parameters: z.object({
    query_id: z.string().describe('The query ID associated with the cube field whose enum values are to be retrieved.'),
    // authToken: z.string().describe('Authorization token (IdToken from getCubeCognitoIdTokenTool)'), // 移除
  }),
  execute: async ({query_id}: { query_id: string }, options: { toolCallId: string }) => { // 添加 options 参数, 明确 query_id 类型
    try {
      const url = `${biApiBaseUrl}/cube/search`;

      const headers = buildHeaders(options.toolCallId);

      const response = await axios.get(url, {
        headers: headers,
        params: {
          query_id: query_id
        }
      });
      console.log('getCubeEnumValuesTool response:', response.data);
      return response.data;
    } catch (error) {
      console.error('getCubeEnumValuesTool error:', error);
      return `Failed to get cube enum values: ${error instanceof Error ? error.message : String(error)}`;
    }
  }
});

/**
 * Generate Download Filename Tool
 */
export const generateFileNameTool = tool({
  description: 'Execute a given SQL statement on a specified database and generate a downloadable filename for the result.',
  parameters: z.object({
    sql: z.string().describe('The SQL query to be executed.'),
    db: z.string().describe('The database name where the SQL query should be executed.'),
    // authToken: z.string().describe('Authorization token (IdToken from getCubeCognitoIdTokenTool)'), // 移除
  }),
  execute: async ({sql, db}, options: { toolCallId: string }) => { // 添加 options 参数
    try {
      const url = `${biApiBaseUrl}/cube/file/grid`;

      const headers = buildHeaders(options.toolCallId, {
        'Content-Type': 'application/json'
      });

      const response = await axios.post(url, {
        sql: sql,
        db: db
      }, {
        headers: headers
      });
      console.log('generateFileNameTool response:', response.data);
      return response.data;
    } catch (error) {
      console.error('generateFileNameTool error:', error);
      return `Failed to generate filename: ${error instanceof Error ? error.message : String(error)}`;
    }
  }
});

/**
 * Test Query SQL Tool
 */
export const testQuerySqlTool = tool({
  description: 'Test if a SQL query is valid and can be executed successfully.',
  parameters: z.object({
    sql: z.string().describe('The SQL query to be tested.'),
    db: z.string().describe('The database name where the SQL query should be tested.'),
  }),
  execute: async ({sql, db}, options: { toolCallId: string }) => {
    try {
      const url = `${biApiBaseUrl}/BIChat/test/sql`;

      const headers = buildHeaders(options.toolCallId, {
        'Content-Type': 'application/json'
      });

      const response = await axios.post(url, {
        sql: sql,
        db: db
      }, {
        headers: headers
      });
      console.log('testQuerySqlTool response:', response.data);
      return response.data;
    } catch (error) {
      console.error('testQuerySqlTool error:', error);
      return `Failed to test SQL query: ${error instanceof Error ? error.message : String(error)}`;
    }
  }
});

/**
 * Update Dashboard Tool
 */
export const updateDashboardTool = tool({
  description: 'Update the dashboard with the given JSON payload.',
  parameters: z.object({
    dashboard_id: z.string().describe('The ID of the dashboard to update. When operate is save-as, use the dashboard_id from create_dashboard response, otherwise use 0'),
    operate: z.enum(['save-as', 'add-parameter']).describe('Operation type: save-as or add-parameter'),
    query_name: z.string().describe('Query name to modify when operate is add-parameter, otherwise empty string'),
    query_text: z.string().describe('SQL query text to modify when operate is add-parameter, otherwise empty string'),
    filter_params: z.array(z.object({
      name: z.string().describe('Name of the parameter to add'),
      type: z.enum(['date-range', 'query']).describe('Type of parameter: date-range or query'),
      value: z.union([
        z.string().describe('Date range value (e.g., d_this_year, d_this_month, d_last_month)'),
        z.array(z.string()).describe('Array of values, e.g., ["ALL"]')
      ]),
      title: z.string().describe('Parameter name when type is query, otherwise empty string'),
      global: z.boolean().describe('Always false'),
      locals: z.array(z.string()).describe('Empty array'),
      queryId: z.number().describe('Always 0'),
      multiValuesOptions: z.object({
        prefix: z.string().describe('Always "\'"'),
        suffix: z.string().describe('Always "\'"'),
        separator: z.string().describe('Always ","')
      })
    })).optional().describe('Array of filter parameters when operate is add-parameter, otherwise empty array'),
    filter_params_dropdown_list_query_sql: z.array(z.string()).optional().describe('Array of SQL queries for dropdown list when type is query, otherwise empty array')
  }),
  execute: async ({
                    dashboard_id,
                    operate,
                    query_name,
                    query_text,
                    filter_params,
                    filter_params_dropdown_list_query_sql
                  }, options: { toolCallId: string }) => {
    try {
      const url = `${biApiBaseUrl}/BIChat/dashboard`;

      const headers = buildHeaders(options.toolCallId, {
        'Content-Type': 'application/json'
      });

      const requestBody = {
        dashboard_id,
        operate,
        query_name: operate === 'add-parameter' ? query_name : '',
        query_text: operate === 'add-parameter' ? query_text : '',
        filter_params: operate === 'add-parameter' ? filter_params : [],
        filter_params_dropdown_list_query_sql: operate === 'add-parameter' ? filter_params_dropdown_list_query_sql : []
      };

      console.log('Updating dashboard with params:', JSON.stringify(requestBody, null, 2));

      const response = await axios.put(url, requestBody, {
        headers: headers
      });
      console.log('updateDashboardTool response:', response.data);
      return response.data;
    } catch (error) {
      console.error('updateDashboardTool error:', error);
      if (axios.isAxiosError(error)) {
        console.error('Dashboard update error details:', {
          status: error.response?.status,
          statusText: error.response?.statusText,
          data: error.response?.data,
          requestData: error.config?.data
        });
        return `Failed to update dashboard: ${error.response?.data?.detail || error.message}`;
      }
      return `Failed to update dashboard: ${error instanceof Error ? error.message : String(error)}`;
    }
  }
});
