const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');
const mysql = require('mysql2/promise'); // Using mysql2/promise for async operations

// Load environment variables at the beginning of the file
const dotenv = require('dotenv');
const envPath = path.resolve(process.cwd(), '.env.local');
dotenv.config({ path: envPath });

// Check critical environment variables
console.log('Environment variables check:');
console.log('- OPENAI_API_KEY:', process.env.OPENAI_API_KEY ? 'Set' : 'Not set');
console.log('- DB_HOST:', process.env.DB_HOST || 'Using default value');

// Load database configuration
const dbConfig = require('./db-config');

// Print configuration information at script startup (excluding sensitive information)
console.log('Database connection config:', {
  host: dbConfig.host,
  port: dbConfig.port,
  user: dbConfig.user,
  database: dbConfig.database
});

/**
 * Execute SQL query using direct MySQL connection
 * @param {string} sql SQL query
 * @param {Array} params Query parameters (optional)
 * @returns {Array} Query results
 */
async function executeSql(sql, params = []) {
  let connection;
  try {
    // Create database connection
    connection = await mysql.createConnection(dbConfig);
    
    // Execute query
    const [rows] = await connection.query(sql, params);
    return rows;
  } catch (error) {
    console.error('Failed to execute SQL query:', error);
    throw error;
  } finally {
    // Ensure connection is closed
    if (connection) {
      await connection.end();
    }
  }
}

/**
 * Execute SQL query via file (old method, retained for compatibility with existing code)
 * @param {string} sql SQL query
 * @returns {Array} Query results
 */
function executeSqlWithFile(sql) {
  // Create temporary SQL file
  const tempSqlFile = path.join(__dirname, `temp_sql_${Date.now()}.sql`);
  fs.writeFileSync(tempSqlFile, sql, 'utf8');
  
  try {
    // Execute query
    const output = execSync(`node ${path.join(__dirname, 'cursor-sql.js')} --file ${tempSqlFile}`, { encoding: 'utf8' });
    return JSON.parse(output);
  } finally {
    // Clean up temporary file
    if (fs.existsSync(tempSqlFile)) {
      fs.unlinkSync(tempSqlFile);
    }
  }
}

/**
 * Initialize organization memory system
 * Store organization information from def_organization and def_organization_aka tables into the memory system
 * @param {Object} options - Configuration options
 * @param {boolean} options.testMode - Whether to run in test mode, processing only a small number of records
 * @param {boolean} options.infer - Whether to use LLM to infer memory content, defaults to false
 */
async function initOrganizationMemories(options = {}) {
  const testMode = options.testMode || false;
  const infer = options.infer ?? false; // Default: don't use LLM inference
  
  if (testMode) {
    console.log('⚠️ Test mode enabled, will process only a small number of records');
  }
  console.log(`Starting organization memory initialization... (infer=${infer})`);
  
  try {
    // Use MCP to call MySQL, get organization data
    let orgsQuery = `
      SELECT id, tenantId, status, customerCode, name, fullName, isValid 
      FROM def_organization
      WHERE isValid = 1
    `;
    
    // Limit the number of records in test mode
    if (testMode) {
      orgsQuery += ' LIMIT 5';
    }
    
    // Get organization alias data
    const akasQuery = `
      SELECT orgId, items
      FROM def_organization_aka
    `;
    
    // Call MySQL MCP to execute queries
    console.log('Retrieving organization data from database...');
    const orgsResult = await executeSql(orgsQuery);
    
    console.log('Retrieving alias data from database...');
    const akasResult = await executeSql(akasQuery);
    
    if (!orgsResult || !orgsResult.length) {
      throw new Error('Failed to retrieve organization data');
    }
    
    // Build mapping from organization ID to aliases
    const orgAliasMap = new Map();
    for (const aka of akasResult) {
      try {
        // Ensure items is an object, not a string
        let items = aka.items;
        if (typeof items === 'string') {
          items = JSON.parse(items);
        }
        
        if (items && Array.isArray(items)) {
          const aliases = items
            .filter(item => item.referenceId || item.referenceName)
            .map(item => ({
              referenceId: item.referenceId || null,
              referenceName: item.referenceName || null
            }));
          
          if (aliases.length > 0) {
            orgAliasMap.set(aka.orgId, aliases);
          }
        }
      } catch (error) {
        console.error(`Error processing alias data (orgId: ${aka.orgId}):`, error);
      }
    }
    
    // Create memories for each organization
    console.log(`Starting to process ${orgsResult.length} organizations...`);
    let successCount = 0;
    let errorCount = 0;
    let batchSize = 50;
    let batchMemories = [];
    
    // Preprocess organization data, prepare for batch writing
    for (let i = 0; i < orgsResult.length; i++) {
      const org = orgsResult[i];
      const isLastItem = i === orgsResult.length - 1;
      
      try {
        // Get organization aliases
        const aliases = orgAliasMap.get(org.id) || [];
        
        // Merge all names, including primary name, full name, and aliases, and deduplicate
        const allNames = new Set();
        if (org.name) allNames.add(org.name);
        if (org.fullName && org.fullName !== org.name) allNames.add(org.fullName);
        
        // Add names from the alias table
        for (const alias of aliases) {
          if (alias.referenceId) allNames.add(alias.referenceId);
          if (alias.referenceName) allNames.add(alias.referenceName);
        }
        
        // Remove null and empty strings
        const uniqueNames = Array.from(allNames).filter(Boolean);
        
        // Create memory content - single line format to avoid being split
        const content = `Organization ${org.id} info - Name: ${org.name || 'N/A'}, Full Name: ${org.fullName || 'N/A'}, Customer Code: ${org.customerCode || 'N/A'}, Status: ${org.status || 'N/A'}, Aliases: ${uniqueNames.length > 2 ? uniqueNames.slice(2).join(' | ') : 'None'}`;
        
        // Set memory importance - based on organization status and potential importance
        const importance = org.status === 'ACTIVE' ? 8 : 5;
        
        // Add to batch queue
        batchMemories.push({
          type: 'SYSTEM',
          content,
          userId: 'system',
          metadata: {
            orgId: org.id,
            tenantId: org.tenantId,
            customerCode: org.customerCode,
            importance,
            source: 'organization_data',
            timestamp: new Date().toISOString(),
            type: 'organization',
            title: `Organization: ${org.name || org.fullName || org.customerCode || org.id}`,
            organization: {
              id: org.id,
              name: org.name,
              fullName: org.fullName,
              customerCode: org.customerCode,
              status: org.status,
              names: uniqueNames
            }
          }
        });
        
        // If batch size is reached or it's the last batch, add memories in batch
        if (batchMemories.length >= batchSize || isLastItem) {
          // Write batch memories to temporary file
          const tempFile = path.join(__dirname, 'temp_memories.json');
          fs.writeFileSync(tempFile, JSON.stringify(batchMemories, null, 2));
          
          // Call memory system to add memories in batch
          console.log(`Adding a batch of ${batchMemories.length} memories...`);
          try {
            const { bulkAddMemories } = require('./bulkAddMemories');
            // Set infer parameter to control whether to use LLM inference for organization data
            const result = await bulkAddMemories(tempFile, { infer });
            
            successCount += result.success;
            errorCount += result.error;
            console.log(`Successfully processed ${successCount} organization memories, failed ${errorCount}`);
            
            // If batch has any errors, stop the entire process
            if (result.error > 0) {
              console.error('Batch processing encountered errors, stopping subsequent processing');
              break; // Exit loop
            }
          } catch (error) {
            console.error('Failed to add memories in bulk:', error);
            errorCount += batchMemories.length;
            // Serious error, stop entire process
            break;
          }
          
          // Clear batch queue
          batchMemories = [];
          
          // Delete temporary file
          if (fs.existsSync(tempFile)) {
            fs.unlinkSync(tempFile);
          }
        }
      } catch (error) {
        console.error(`Error processing organization ${org.id}:`, error);
        errorCount++;
      }
    }
    
    console.log(`Organization memory initialization complete!`);
    console.log(`Success: ${successCount}, Failed: ${errorCount}, Total: ${orgsResult.length}`);
    
    return { success: successCount, error: errorCount, total: orgsResult.length };
  } catch (error) {
    console.error('Failed to initialize organization memories:', error);
    throw error;
  }
}

// If this file is executed directly
if (require.main === module) {
  // Check command line arguments
  const testMode = process.argv.includes('--test');
  const infer = process.argv.includes('--infer'); // Default is false, only enabled if --infer is specified
  
  initOrganizationMemories({ testMode, infer })
    .then((result) => {
      console.log('Initialization result:', result);
      process.exit(0);
    })
    .catch((error) => {
      console.error('Initialization failed:', error);
      process.exit(1);
    });
}

module.exports = initOrganizationMemories; 