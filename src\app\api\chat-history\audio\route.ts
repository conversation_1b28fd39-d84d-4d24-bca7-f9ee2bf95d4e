import { NextRequest, NextResponse } from 'next/server';
import { getUserIdFromRequest } from '@/utils/authUtils';
import fs from 'fs';
import path from 'path';
import { getChatHistory } from '@/utils/serverChatHistoryUtils';
import { v4 as uuidv4 } from 'uuid';
import { log } from '@/utils/logger';

// POST request handler - Upload audio file
export async function POST(req: NextRequest) {
  try {
    log.info('Audio upload request received', undefined, 'audio-api');

    // Get current user ID
    const userId = await getUserIdFromRequest(req);
    const siteId = req.headers.get('X-Site-ID') || '';

    // Use siteId as userId if no userId available
    let finalUserId = userId;
    if (!finalUserId) {
      if (siteId) {
        finalUserId = siteId; 
      } else {
        log.warn('No userId or siteId found', undefined, 'audio-api');
        return NextResponse.json(
          { error: 'User not authenticated or session expired' },
          { status: 401 }
        );
      }
    }

    // Parse form data
    const formData = await req.formData();
    const audioFile = formData.get('audio') as File;
    const chatId = formData.get('chatId') as string;
    const originalFileName = formData.get('fileName') as string;

    if (!audioFile || !chatId) {
      log.warn('Missing required parameters', { chatId, hasAudioFile: !!audioFile }, 'audio-api');
      return NextResponse.json(
        { error: 'Missing audio file or chat ID' },
        { status: 400 }
      );
    }

    // Verify chat history ownership
    const chatHistory = await getChatHistory(chatId, finalUserId);
    
    if (!chatHistory) {
      log.warn('Chat history not found', { chatId, userId: finalUserId }, 'audio-api');
      return NextResponse.json(
        { error: 'Chat history not found' },
        { status: 404 }
      );
    }
    
    if (chatHistory.userId && chatHistory.userId !== finalUserId) {
      log.warn('User ID mismatch', { chatHistoryUserId: chatHistory.userId, finalUserId }, 'audio-api');
      return NextResponse.json(
        { error: 'Access denied to this chat history' },
        { status: 403 }
      );
    }

    // Create user audio directory
    const basePath = path.join(process.cwd(), 'src', 'chat-history');
    const userAudioDir = path.join(basePath, finalUserId, 'audio');
    
    if (!fs.existsSync(userAudioDir)) {
      fs.mkdirSync(userAudioDir, { recursive: true });
    }

    // Generate unique filename while keeping original extension
    const fileExtension = path.extname(originalFileName || audioFile.name);
    const uniqueFileName = `${uuidv4()}${fileExtension}`;
    const filePath = path.join(userAudioDir, uniqueFileName);

    // Save audio file
    const arrayBuffer = await audioFile.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);
    
    await fs.promises.writeFile(filePath, buffer);
    log.info('Audio file saved successfully', { 
      fileName: uniqueFileName, 
      size: buffer.length,
      chatId 
    }, 'audio-api');

    // Return audio info for download
    const audioInfo = {
      fileName: uniqueFileName,  // Actual filename on server
      originalFileName: originalFileName || audioFile.name,  // Original filename
      displayName: originalFileName || audioFile.name,  // Friendly display name
      size: buffer.length,
      chatId: chatId,
      createdAt: new Date().toISOString(),
      downloadUrl: `/api/chat-history/${chatId}/audio/${uniqueFileName}`
    };

    return NextResponse.json({
      success: true,
      message: 'Audio file saved successfully',
      audioInfo: audioInfo
    });

  } catch (error: any) {
    log.error('Failed to save audio file', error, 'audio-api');
    return NextResponse.json(
      { error: 'Failed to save audio file', details: error?.message || String(error) },
      { status: 500 }
    );
  }
} 