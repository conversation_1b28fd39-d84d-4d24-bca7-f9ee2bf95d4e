import { isScreenCaptureSupported } from './utils';

export interface ScreenshotOptions {
    selector?: string;
    quality?: number;
    maxWidth?: number;
    maxHeight?: number;
    scale?: number;
}

export class ScreenshotTool {
    private stream: MediaStream | null = null;
    private track: MediaStreamTrack | null = null;
    private imageCapture: any | null = null; // Using any since ImageCapture type is not available
    private initialized: boolean = false;

    // 初始化并请求权限
    async initialize(): Promise<boolean> {
        // 只检查基本的活动状态，忽略静音状态
        if (this.initialized && 
            this.stream && 
            this.track && 
            this.track.readyState === 'live') {
            console.log('Already initialized with active track, reusing existing stream');
            return true;
        }
        
        // 如果之前的资源存在，先清理
        this.cleanup();
        
        try {
            if (!isScreenCaptureSupported()) {
                throw new Error('Screen capture is not supported in this browser');
            }

            // 使用 preferCurrentTab 和 selfBrowserSurface 来自动选择当前标签页
            this.stream = await navigator.mediaDevices.getDisplayMedia({
                video: {
                    displaySurface: 'browser',
                    width: { max: 1920 },
                    height: { max: 1080 },
                    frameRate: { max: 1 }
                },
                audio: false,
                preferCurrentTab: true,
                selfBrowserSurface: 'include'
            } as any);

            this.track = this.stream?.getVideoTracks()[0] ?? null;
            if (!this.track) {
                throw new Error('Failed to get video track');
            }

            // 设置视频轨道约束以优化截图
            await this.track.applyConstraints({
                width: { ideal: window.innerWidth },
                height: { ideal: window.innerHeight }
            });

            // @ts-ignore
            this.imageCapture = new ImageCapture(this.track);
            
            // 只监听 ended 事件，因为这是真正需要关注的状态
            this.track.addEventListener('ended', () => {
                console.log('Screen sharing ended, marking as uninitialized');
                this.initialized = false;
                this.cleanup();  // 在 ended 事件时可以安全地清理资源
            });

            this.initialized = true;
            console.log('Screen capture initialized successfully');
            return true;
        } catch (error) {
            console.error('Failed to initialize:', error);
            this.cleanup();
            this.initialized = false;
            return false;
        }
    }
    
    // 截图方法
    async capture(options: ScreenshotOptions = {}): Promise<{
        success: boolean;
        image?: string;
        size?: number;
        error?: string;
    }> {
        try {
            console.log('Capture called with current state:', {
                initialized: this.initialized,
                hasStream: !!this.stream,
                hasTrack: !!this.track,
                trackState: this.track?.readyState
            });

            // 简化初始化检查，只关注真正重要的状态
            const needsInitialization = !this.initialized || 
                !this.stream || 
                !this.track || 
                this.track.readyState === 'ended';

            if (needsInitialization) {
                console.log('Needs initialization because:', {
                    notInitialized: !this.initialized,
                    noStream: !this.stream,
                    noTrack: !this.track,
                    trackEnded: this.track?.readyState === 'ended'
                });

                const initialized = await this.initialize();
                if (!initialized) {
                    throw new Error('Failed to initialize screen capture');
                }
            }
            
            if (!this.imageCapture) {
                throw new Error('ImageCapture not initialized');
            }

            const {
                selector = null,
                quality = 0.8,
                maxWidth = 1280,
                maxHeight = 800,
                scale = 1.0
            } = options;

            let bitmap = await this.imageCapture.grabFrame();
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            
            if (!ctx) {
                throw new Error('Failed to get canvas context');
            }

            let targetWidth: number;
            let targetHeight: number;
            
            if (selector) {
                const element = document.querySelector(selector);
                if (!element) {
                    throw new Error('Element not found');
                }
                
                const rect = element.getBoundingClientRect();
                targetWidth = Math.min(rect.width * scale, maxWidth);
                targetHeight = Math.min(rect.height * scale, maxHeight);
                
                // 保持宽高比
                if (rect.width > rect.height) {
                    const aspectRatio = rect.width / rect.height;
                    if (targetWidth / targetHeight > aspectRatio) {
                        targetWidth = targetHeight * aspectRatio;
                    } else {
                        targetHeight = targetWidth / aspectRatio;
                    }
                }
                
                canvas.width = targetWidth;
                canvas.height = targetHeight;
                
                ctx.drawImage(bitmap,
                    rect.left * scale, rect.top * scale,
                    rect.width * scale, rect.height * scale,
                    0, 0, targetWidth, targetHeight
                );
            } else {
                // 计算缩放后的尺寸，保持宽高比
                const aspectRatio = bitmap.width / bitmap.height;
                targetWidth = Math.min(bitmap.width, maxWidth);
                targetHeight = Math.min(bitmap.height, maxHeight);

                // 确保宽高比保持一致
                if (targetWidth / targetHeight > aspectRatio) {
                    targetWidth = targetHeight * aspectRatio;
                } else {
                    targetHeight = targetWidth / aspectRatio;
                }
                
                // 向下取整以避免小数像素
                targetWidth = Math.floor(targetWidth);
                targetHeight = Math.floor(targetHeight);
                
                canvas.width = targetWidth;
                canvas.height = targetHeight;
                
                // 使用双线性插值算法
                ctx.imageSmoothingEnabled = true;
                ctx.imageSmoothingQuality = 'high';
                
                // 分步绘制以获得更好的质量
                if (bitmap.width > targetWidth * 2) {
                    // 创建临时画布进行分步放
                    const tempCanvas = document.createElement('canvas');
                    const tempCtx = tempCanvas.getContext('2d');
                    if (!tempCtx) {
                        throw new Error('Failed to get temp canvas context');
                    }

                    let currentWidth = bitmap.width;
                    let currentHeight = bitmap.height;
                    
                    // 每次缩小不超过50%，以保持图像质量
                    while (currentWidth > targetWidth * 2) {
                        currentWidth = Math.max(currentWidth / 2, targetWidth);
                        currentHeight = Math.max(currentHeight / 2, targetHeight);
                        
                        tempCanvas.width = currentWidth;
                        tempCanvas.height = currentHeight;
                        
                        tempCtx.imageSmoothingEnabled = true;
                        tempCtx.imageSmoothingQuality = 'high';
                        tempCtx.drawImage(bitmap, 0, 0, currentWidth, currentHeight);
                        
                        // 更新bitmap为当前缩放后的图像
                        bitmap = await createImageBitmap(tempCanvas);
                    }
                }
                
                // 最终绘制
                ctx.drawImage(bitmap, 0, 0, targetWidth, targetHeight);
            }
            
            // 尝试使用不同的图片格式和质量设置
            let base64Image: string;
            let bytes: number;
            
            base64Image = canvas.toDataURL('image/jpeg', quality);
            bytes = atob(base64Image.split(',')[1]).length;
            
            console.log(`Screenshot size: ${(bytes / 1024).toFixed(2)}KB`);
            console.log(`Resolution: ${targetWidth}x${targetHeight}`);
            
            return {
                success: true,
                image: base64Image,
                size: bytes
            };
            
        } catch (error) {
            console.error('Screenshot failed:', error);
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error'
            };
        }
    }

    // 清理资源
    cleanup() {
        if (this.track) {
            this.track.stop();
            this.track = null;
        }
        if (this.stream) {
            this.stream.getTracks().forEach(track => track.stop());
            this.stream = null;
        }
        this.imageCapture = null;
        this.initialized = false;
    }
} 