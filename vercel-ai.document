appendResponseMessages()
Appends an array of ResponseMessage objects (from the AI response) to an existing array of UI messages. It reuses the existing IDs from the response messages, generates new timestamps, and merges tool-call results with the previous assistant message (if any). This is useful for maintaining a unified message history when working with AI responses in a client-side chat application.

Import
import { appendResponseMessages } from "ai"
API Signature
Parameters
messages:
Message[]
An existing array of UI messages for useChat (usually from state).
responseMessages:
ResponseMessage[]
The new array of AI messages returned from the AI service to be appended. For example, "assistant" messages get added as new items, while tool-call results (role: "tool") are merged with the previous assistant message.
Returns
An updated array of Message objects.

Message[]:
Array
A new array of UI messages with the appended AI response messages (and updated tool-call results for the preceding assistant message).


appendClientMessage()
Appends a client Message object to an existing array of UI messages. If the last message in the array has the same ID as the new message, it will replace the existing message instead of appending. This is useful for maintaining a unified message history in a client-side chat application, especially when updating existing messages.

Import
import { appendClientMessage } from "ai"
API Signature
Parameters
messages:
Message[]
An existing array of UI messages for useChat (usually from state).
message:
Message
The new client message to be appended or used to replace an existing message with the same ID.
Returns
Message[]:
Array
A new array of UI messages with either the appended message or the updated message replacing the previous one with the same ID.



createDataStream
The createDataStream function allows you to stream additional data to the client (see Streaming Data).

Import
import { createDataStream } from "ai"
Example

const stream = createDataStream({
  async execute(dataStream) {
    // Write data
    dataStream.writeData({ value: 'Hello' });

    // Write annotation
    dataStream.writeMessageAnnotation({ type: 'status', value: 'processing' });

    // Merge another stream
    const otherStream = getAnotherStream();
    dataStream.merge(otherStream);
  },
  onError: error => `Custom error: ${error.message}`,
});
API Signature
Parameters
execute:
(dataStream: DataStreamWriter) => Promise<void> | void
A function that receives a DataStreamWriter instance and can use it to write data to the stream.
DataStreamWriter
write:
(data: DataStreamString) => void
Appends a data part to the stream.
writeData:
(value: JSONValue) => void
Appends a data part to the stream.
writeMessageAnnotation:
(value: JSONValue) => void
Appends a message annotation to the stream.
writeSource:
(source: Source) => void
Appends a source part to the stream.
merge:
(stream: ReadableStream<DataStreamString>) => void
Merges the contents of another stream to this stream.
onError:
((error: unknown) => string) | undefined
Error handler that is used by the data stream writer. This is intended for forwarding when merging streams to prevent duplicated error masking.
onError:
(error: unknown) => string
A function that handles errors and returns an error message string. By default, it returns "An error occurred."
Returns
ReadableStream<DataStreamString>

A readable stream that emits formatted data stream parts.



createDataStreamResponse
The createDataStreamResponse function creates a Response object that streams data to the client (see Streaming Data).

Import
import { createDataStreamResponse } from "ai"
Example

const response = createDataStreamResponse({
  status: 200,
  statusText: 'OK',
  headers: {
    'Custom-Header': 'value',
  },
  async execute(dataStream) {
    // Write data
    dataStream.writeData({ value: 'Hello' });

    // Write annotation
    dataStream.writeMessageAnnotation({ type: 'status', value: 'processing' });

    // Merge another stream
    const otherStream = getAnotherStream();
    dataStream.merge(otherStream);
  },
  onError: error => `Custom error: ${error.message}`,
});
API Signature
Parameters
status:
number
The status code for the response.
statusText:
string
The status text for the response.
headers:
Headers | Record<string, string>
Additional headers for the response.
execute:
(dataStream: DataStreamWriter) => Promise<void> | void
A function that receives a DataStreamWriter instance and can use it to write data to the stream.
DataStreamWriter
write:
(data: DataStreamString) => void
Appends a data part to the stream.
writeData:
(value: JSONValue) => void
Appends a data part to the stream.
writeMessageAnnotation:
(value: JSONValue) => void
Appends a message annotation to the stream.
writeSource:
(source: Source) => void
Appends a source part to the stream.
merge:
(stream: ReadableStream<DataStreamString>) => void
Merges the contents of another stream to this stream.
onError:
((error: unknown) => string) | undefined
Error handler that is used by the data stream writer. This is intended for forwarding when merging streams to prevent duplicated error masking.
onError:
(error: unknown) => string
A function that handles errors and returns an error message string. By default, it returns "An error occurred."
Returns
Response

A Response object that streams formatted data stream parts with the specified status, headers, and content.