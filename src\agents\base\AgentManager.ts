import { BaseAgent } from './BaseAgent';
import { AgentExecutionContext } from './BaseAgent';

// Agent管理器 - 负责注册和查找Agent
export class AgentManager {
  private agents: Map<string, BaseAgent> = new Map();
  
  // 注册Agent
  registerAgent(agent: BaseAgent): void {
    console.log(`[AgentManager] Registering agent: ${agent.id}`);
    this.agents.set(agent.id, agent);
  }
  
  // 获取Agent
  getAgent(agentId: string): BaseAgent | undefined {
    return this.agents.get(agentId);
  }
  
  // 获取所有Agent
  getAllAgents(): BaseAgent[] {
    return Array.from(this.agents.values());
  }
  
  // 根据ID获取指定的Agent
  getAgentById(agentId: string): BaseAgent | null {
    return this.agents.get(agentId) || null;
  }
  
  // 获取Agent描述（用于Super Agent的工具选择）
  getAgentDescriptions(): Array<{ id: string; name: string; description: string }> {
    return Array.from(this.agents.values()).map(agent => ({
      id: agent.id,
      name: agent.name,
      description: agent.description
    }));
  }
}

// 全局Agent管理器实例
export const globalAgentManager = new AgentManager();