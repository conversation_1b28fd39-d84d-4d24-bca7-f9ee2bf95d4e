'use client';

import React from 'react';
import { IframeArtifact } from './IframeArtifact';
import { SandboxArtifact } from './SandboxArtifact';

interface Artifact {
  id: string;
  type: string;
  title: string;
  props: Record<string, any>;
}

interface ArtifactRendererProps {
  artifact: Artifact;
}

export function ArtifactRenderer({ artifact }: ArtifactRendererProps) {
  switch (artifact.type) {
    case 'iframe':
      // 确保有必需的 src 属性
      if (!artifact.props?.src) {
        return (
          <div className="p-4 text-center text-red-400">
            Error: iframe artifact missing src property
          </div>
        );
      }
      return <IframeArtifact {...(artifact.props as { src: string; width?: string | number; height?: string | number })} />;
    case 'sandbox':
      // 确保有必需的属性
      if (!artifact.props?.initialUrl || !artifact.props?.sessionId) {
        return (
          <div className="p-4 text-center text-red-400">
            Error: sandbox artifact missing required properties
          </div>
        );
      }
      return <SandboxArtifact {...(artifact.props as { initialUrl: string; sessionId: string })} />;
    // case 'html':
    //   return <HtmlArtifact {...artifact.props} />;
    default:
      return (
        <div className="p-4 text-center text-gray-400">
          Unknown Artifact Type: {artifact.type}
        </div>
      );
  }
}