export interface SiteHeaders {
  [key: string]: Record<string, string>;
}

export const getSiteSpecificHeaders = (siteId: string): Record<string, string> => {

  // 将 siteId 转换为小写进行比较
  const normalizedSiteId = siteId.toLowerCase();
  if(normalizedSiteId === 'fms'){
    return getSiteHeaderForFms();
  }
  if(normalizedSiteId === 'portal'){
    return getSiteHeaderForPortal();
  }
  return {};
};

const getSiteHeaderForPortal = (): Record<string, string> => {
  const headers: Record<string, string> = {"x-tenant-id": "SBFH"};
  
  // 解析 portal-token
  const tokenRaw = localStorage.getItem('item-client-web/token');
  if (tokenRaw) {
    try {
      const parsed = JSON.parse(tokenRaw);
      headers['portal-token'] = parsed.value;
    } catch (e) {
    }
  }

  // 解析 customer_code
  const customerCodeRaw = localStorage.getItem('item-client-web/default_customer_code');
  if (customerCodeRaw) {
    try {
      const parsed = JSON.parse(customerCodeRaw);
      headers['customer-code'] = parsed.value;
    } catch (e) {
      // 解析失败，忽略
    }
  }

  // 解析 uf_customers 和 ut_customers
  const subjectRaw = localStorage.getItem('item-client-web/subject');
  if (subjectRaw) {
    try {
      const parsed = JSON.parse(subjectRaw);
      if (parsed.value) {
        if (parsed.value.uf_customers) {
          headers['uf-customers'] = JSON.stringify(parsed.value.uf_customers);
        }
        if (parsed.value.ut_customers) {
          headers['ut-customers'] = JSON.stringify(parsed.value.ut_customers);
        }
      }
    } catch (e) {
      // 解析失败，忽略
    }
  }
  
  // console.log('getSiteHeaderForPortal headers:', headers);
  return headers;
};

const getSiteHeaderForFms = (): Record<string, string> => {
  const headers: Record<string, string> = {};
  // Try to get token from different possible key names
  const possibleKeys = [
    'token'
  ];

  for (const key of possibleKeys) {
    const token = localStorage.getItem(key);
    if (token) {
      headers['fms-token'] = token;
    }
  }
  // console.log('getSiteHeaderForFms headers:', headers);
  return headers;
};