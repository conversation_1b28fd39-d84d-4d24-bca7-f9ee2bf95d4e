'use client';

import React, { useState, useEffect } from 'react';
import '@/styles/item-design-system.css';

interface ClockCardProps {
  toolInvocation?: any;
  // Direct properties
  time?: string;
  date?: string;
  hours?: number;
  minutes?: number;
  seconds?: number;
  timezone?: string;
  hourFormat?: '12h' | '24h';
}

export default function ClockCard({
  toolInvocation,
  time: propTime,
  date: propDate,
  hours: propHours,
  minutes: propMinutes,
  seconds: propSeconds,
  timezone: propTimezone,
  hourFormat: propHourFormat
}: ClockCardProps) {
  const [currentTime, setCurrentTime] = useState<Date>(new Date());
  const [isExpanded, setIsExpanded] = useState(false);
  
  // Extract data from tool invocation or direct properties
  let time: string | undefined, 
      date: string | undefined, 
      timezone: string | undefined, 
      hourFormat: '12h' | '24h' | undefined,
      hours: number | undefined,
      minutes: number | undefined,
      seconds: number | undefined;
  
  if (toolInvocation) {
    if (toolInvocation.state === 'result') {
      // Complete result
      time = toolInvocation.result?.time;
      date = toolInvocation.result?.date;
      timezone = toolInvocation.result?.timezone || toolInvocation.args?.timezone;
      hourFormat = toolInvocation.result?.hourFormat;
      
      // Explicitly extract numeric time components from result
      if (typeof toolInvocation.result?.hours === 'number') {
        hours = toolInvocation.result.hours;
      }
      if (typeof toolInvocation.result?.minutes === 'number') {
        minutes = toolInvocation.result.minutes;
      }
      if (typeof toolInvocation.result?.seconds === 'number') {
        seconds = toolInvocation.result.seconds;
      }
      
      // If time components are not provided, try to parse from time string
      if (time && (!hours || !minutes || !seconds)) {
        const timeParts = time.split(':').map(Number);
        if (timeParts.length >= 2) {
          if (hours === undefined) hours = timeParts[0];
          if (minutes === undefined) minutes = timeParts[1];
          if (seconds === undefined && timeParts.length > 2) seconds = timeParts[2];
        }
      }
      
      // 确保hourFormat与hours匹配
      if (hours !== undefined) {
        hourFormat = hours >= 12 ? '24h' : '12h';
      }
    } else if (toolInvocation.state === 'call') {
      // Request parameters only
      timezone = toolInvocation.args?.timezone;
    } else {
      // Partial call
      timezone = 'Loading...';
    }
  } else {
    // Use directly passed properties
    time = propTime;
    date = propDate;
    timezone = propTimezone;
    hourFormat = propHourFormat;
    
    // Use directly passed time components
    hours = propHours;
    minutes = propMinutes;
    seconds = propSeconds;
    
    // If time components are not provided, try to parse from time string
    if (time && (!hours || !minutes || !seconds)) {
      const timeParts = time.split(':').map(Number);
      if (timeParts.length >= 2) {
        if (hours === undefined) hours = timeParts[0];
        if (minutes === undefined) minutes = timeParts[1]; 
        if (seconds === undefined && timeParts.length > 2) seconds = timeParts[2];
      }
    }
    
    // 确保hourFormat与hours匹配
    if (hours !== undefined) {
      hourFormat = hours >= 12 ? '24h' : '12h';
    }
  }
  
  // Modify real-time handling logic to ensure minutes value is correctly set
  // If no time data is available, use current time
  if (!time || !date) {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const currentHours = now.getHours();
    const currentMinutes = now.getMinutes();
    const currentSeconds = now.getSeconds();
    
    const hoursStr = String(currentHours).padStart(2, '0');
    const minutesStr = String(currentMinutes).padStart(2, '0');
    const secondsStr = String(currentSeconds).padStart(2, '0');
    
    time = `${hoursStr}:${minutesStr}:${secondsStr}`;
    date = `${year}-${month}-${day}`;
    
    // Explicitly set time components to ensure they have values
    hours = currentHours;
    minutes = currentMinutes;
    seconds = currentSeconds;
    
    // Set hourFormat based on current hours
    hourFormat = currentHours >= 12 ? '24h' : '12h';
  }
  
  // Format as standard YYYY-MM-DD HH:MM:SS
  const formattedDateTime = `${date} ${time}`;
  
  // 获取世界主要时区的时间
  const getTimeInOtherZones = () => {
    // Parse current date and time correctly
    const dateParts = date?.split('-').map(Number) || [];
    const timeParts = time?.split(':').map(Number) || [];
    
    if (dateParts.length !== 3 || timeParts.length < 2) {
      return null; // Return null if there's an issue parsing date or time
    }
    
    const year = dateParts[0];
    const month = dateParts[1] - 1; // Month starts from 0
    const day = dateParts[2];
    const hour = timeParts[0];
    const minute = timeParts[1];
    const second = timeParts[2] || 0;
    
    // Create a Date object, assuming input time is UTC
    const inputDate = new Date(Date.UTC(year, month, day, hour, minute, second));
    
    // Define major time zones
    const timeZones = [
      { name: 'UTC', offset: 0 },
      { name: 'New York (EST/EDT)', offset: -4 }, // -4 in summer, -5 in winter
      { name: 'London (BST/GMT)', offset: 1 },    // +1 in summer, +0 in winter
      { name: 'Beijing', offset: 8 },
      { name: 'Tokyo', offset: 9 },
      { name: 'Sydney', offset: 10 }              // Simplified, Australian time zones are more complex
    ];
    
    // Calculate and return times for each zone
    return timeZones.map(zone => {
      const tzDate = new Date(inputDate.getTime() + (zone.offset * 3600000));
      const tzHours = tzDate.getUTCHours().toString().padStart(2, '0');
      const tzMinutes = tzDate.getUTCMinutes().toString().padStart(2, '0');
      return {
        zone: zone.name,
        time: `${tzHours}:${tzMinutes}`
      };
    });
  };
  
  // Tool invocation in process
  if (toolInvocation && toolInvocation.state !== 'result') {
    return (
      <div className="item-card bg-item-bg-card rounded-lg border border-item-gray-800 p-2 text-white shadow-lg my-2">
        <div className="flex items-center">
          <div className="bg-item-bg-hover p-1.5 rounded-lg mr-2.5">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-4 h-4 text-item-gray-400">
              <path strokeLinecap="round" strokeLinejoin="round" d="M12 6v6h4.5m4.5 0a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <div className="flex-1">
            <div className="h-3 bg-item-gray-700 rounded w-3/4 mb-1.5 animate-pulse"></div>
            <div className="h-2 bg-item-gray-800 rounded w-1/2 animate-pulse"></div>
          </div>
        </div>
        <div className="text-sm text-item-gray-400 mt-1.5 font-medium">
          Retrieving time information for{timezone ? ` ${timezone}` : ''}...
        </div>
      </div>
    );
  }
  
  // Collapsed view
  if (!isExpanded) {
    return (
      <div 
        className="item-card bg-item-bg-card rounded-lg border border-item-gray-800 p-2 text-white shadow-lg my-2 cursor-pointer transition-all duration-200 hover:border-item-gray-700 hover:shadow-xl"
        onClick={() => setIsExpanded(true)}
      >
        <div className="flex justify-between items-center">
          <div className="flex items-center flex-1">
            <div className="bg-item-bg-hover p-1.5 rounded-lg mr-2.5">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-4 h-4 text-item-gray-400">
                <path strokeLinecap="round" strokeLinejoin="round" d="M12 6v6h4.5m4.5 0a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div className="text-sm text-item-gray-500 font-medium pr-2 leading-snug flex-1 truncate">
              <span className="mr-2 font-medium">{formattedDateTime}</span>
              <span className="text-xs text-item-gray-500">({timezone || 'Local Time'})</span>
            </div>
          </div>
          <button className="text-item-gray-400 hover:text-item-gray-400 ml-2 flex-shrink-0 transition-transform duration-200">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-4 h-4">
              <path strokeLinecap="round" strokeLinejoin="round" d="M19.5 8.25l-7.5 7.5-7.5-7.5" />
            </svg>
          </button>
        </div>
      </div>
    );
  }
  
  // 处理24小时和12小时制转换的辅助函数
  const format24hTo12h = (hours: number) => {
    const period = hours >= 12 ? 'PM' : 'AM';
    const hours12 = hours % 12 || 12; // 0应显示为12
    return { hours12, period };
  };

  // 增强formatDate功能，支持多种日期格式
  const formatDate = (dateStr: string) => {
    if (!dateStr) return '';
    
    try {
      let dateObj;
      
      // 尝试解析"YYYY-MM-DD"格式
      if (/^\d{4}-\d{2}-\d{2}$/.test(dateStr)) {
        const [year, month, day] = dateStr.split('-').map(num => parseInt(num, 10));
        dateObj = new Date(year, month - 1, day);
      } 
      // 尝试解析"Monday, April 21, 2025"格式
      else if (/\w+,\s+\w+\s+\d{1,2},\s+\d{4}/.test(dateStr)) {
        dateObj = new Date(dateStr);
      }
      // 尝试从完整日期时间字符串中提取日期部分
      else if (dateStr.includes(' ')) {
        // 假设格式是"YYYY-MM-DD HH:MM:SS"或类似格式，提取日期部分
        const datePart = dateStr.split(' ')[0];
        if (/^\d{4}-\d{2}-\d{2}$/.test(datePart)) {
          const [year, month, day] = datePart.split('-').map(num => parseInt(num, 10));
          dateObj = new Date(year, month - 1, day);
        } else {
          console.warn('Unable to extract date from:', dateStr);
          return dateStr;
        }
      } else {
        console.warn('Unsupported date format:', dateStr);
        return dateStr;
      }
      
      // 检查日期是否有效
      if (!dateObj || isNaN(dateObj.getTime())) {
        console.warn('Invalid date after parsing:', dateStr);
        return dateStr;
      }
      
      const options: Intl.DateTimeFormatOptions = { 
        weekday: 'long', 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric' 
      };
      
      return dateObj.toLocaleDateString('en-US', options);
    } catch (error) {
      console.error('Error formatting date:', error, 'for input:', dateStr);
      return dateStr;
    }
  };
  
  // 增加辅助函数，从标题中解析日期
  const parseMainDateTime = () => {
    if (!formattedDateTime) return { parsedDate: null, parsedTime: null };
    
    try {
      // 如果有形如"Monday, April 21, 2025 20:44:12 (Asia/Shanghai)"的标题格式
      const titleMatch = formattedDateTime.match(/(.+?)(\d{2}:\d{2}:\d{2})/);
      if (titleMatch && titleMatch.length > 2) {
        const dateText = titleMatch[1].trim();
        const timeText = titleMatch[2].trim();
        return { 
          parsedDate: dateText,
          parsedTime: timeText
        };
      }
      
      // 否则尝试基本的分割
      const parts = formattedDateTime.split(' ');
      if (parts.length >= 2) {
        // 假设最后一个部分是时间，其余是日期
        const timeText = parts[parts.length - 1];
        const dateText = parts.slice(0, parts.length - 1).join(' ');
        return {
          parsedDate: dateText,
          parsedTime: timeText
        };
      }
      
      return { parsedDate: null, parsedTime: null };
    } catch (error) {
      console.error('Error parsing main date/time:', error);
      return { parsedDate: null, parsedTime: null };
    }
  };
  
  // Expanded view
  return (
    <div className="item-card bg-item-bg-card rounded-lg border border-item-gray-800 p-2 text-white shadow-lg my-2">
      <div className="flex justify-between cursor-pointer" onClick={() => setIsExpanded(!isExpanded)}>
        <div className="flex items-center flex-1">
          <div className="bg-item-bg-hover p-1.5 rounded-lg mr-2.5">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-4 h-4 text-item-gray-400">
              <path strokeLinecap="round" strokeLinejoin="round" d="M12 6v6h4.5m4.5 0a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <div className="text-sm text-white font-medium leading-snug flex-1 truncate">
            <span className="mr-2 font-semibold">{formattedDateTime}</span>
            <span className="text-xs text-item-gray-400">({timezone || 'Local Time'})</span>
          </div>
        </div>
        <button className="text-item-gray-400 hover:text-item-gray-300 ml-2 flex-shrink-0 transition-all duration-200 hover:bg-item-bg-hover p-1.5 rounded-lg">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-4 h-4">
            <path strokeLinecap="round" strokeLinejoin="round" d="M4.5 15.75l7.5-7.5 7.5 7.5" />
          </svg>
        </button>
      </div>
      
      <div className="mt-3 pt-2.5 border-t border-item-gray-800/40">
        {/* 使用解析到的标题日期 */}
        <div className="text-center mb-3">
          <div className="text-item-gray-400 text-xs mb-1.5 font-medium uppercase tracking-wide">Date</div>
          <div className="text-sm font-semibold text-white">
            {(() => {
              // 解析标题中的日期
              const { parsedDate } = parseMainDateTime();
              // 优先使用解析到的日期，然后是date属性，最后是"No date available"
              if (parsedDate) {
                return formatDate(parsedDate);
              } else if (date) {
                return formatDate(date);
              } else {
                return 'No date available';
              }
            })()}
          </div>
        </div>
        
        {/* 只显示12小时制信息 */}
        <div className="bg-item-bg-hover/50 rounded-lg p-2 mb-3 mx-auto max-w-md">
          <div className="text-xs text-item-gray-400 mb-2 text-center font-medium uppercase tracking-wide">12-hour format</div>
          <div className="flex justify-center space-x-4">
            {hours !== undefined ? (
              <>
                <div className="text-center">
                  <div className="text-2xl font-bold text-white">{String(format24hTo12h(hours).hours12).padStart(2, '0')}</div>
                  <div className="text-xs text-item-gray-400 mt-0.5 font-medium">hours</div>
                </div>
                <div className="text-xl font-light self-center text-item-gray-400">:</div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-white">{minutes !== undefined ? String(minutes).padStart(2, '0') : '--'}</div>
                  <div className="text-xs text-item-gray-400 mt-0.5 font-medium">minutes</div>
                </div>
                <div className="text-xl font-light self-center text-item-gray-400">:</div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-white">{seconds !== undefined ? String(seconds).padStart(2, '0') : '--'}</div>
                  <div className="text-xs text-item-gray-400 mt-0.5 font-medium">seconds</div>
                </div>
                <div className="text-center ml-1.5 self-center">
                  <div className="text-base font-bold bg-item-bg-hover px-2.5 py-0.5 rounded-lg text-white border border-item-gray-700">{format24hTo12h(hours).period}</div>
                </div>
              </>
            ) : (
              <div className="text-center text-item-gray-400 font-medium">No time data available</div>
            )}
          </div>
        </div>
        
        {/* Only display world time zones if data is available */}
        {(() => {
          const timeZones = getTimeInOtherZones();
          return timeZones && timeZones.length > 0 ? (
            <div className="mt-3 border-t border-item-gray-800/40 pt-3">
              <div className="text-xs text-item-gray-400 mb-2 font-medium uppercase tracking-wide">World Time Zones:</div>
              <div className="grid grid-cols-2 gap-1.5 text-xs">
                {timeZones.map((tzInfo, index) => (
                  <div key={index} className="flex justify-between py-1 px-2 bg-item-bg-card/50 rounded-lg border border-item-gray-800/30 hover:border-item-orange/30 transition-colors duration-200">
                    <span className="text-item-gray-400 font-medium">{tzInfo.zone}:</span>
                    <span className="text-white font-semibold">{tzInfo.time}</span>
                  </div>
                ))}
              </div>
            </div>
          ) : null;
        })()}
      </div>
    </div>
  );
} 