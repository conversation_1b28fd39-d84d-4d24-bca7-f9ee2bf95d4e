import { NextRequest, NextResponse } from 'next/server';
import axios from 'axios';
import { verifyToken } from '@/utils/authUtils';

// IAM配置
const IAM_CONFIG = {
  USER_INFO_ENDPOINT: `${process.env.NEXT_PUBLIC_IAM_ENDPOINT}/user-info`
};

// 检查环境变量
if (!process.env.NEXT_PUBLIC_IAM_ENDPOINT) {
  console.error('Missing NEXT_PUBLIC_IAM_ENDPOINT configuration');
}

export async function GET(req: NextRequest) {
  try {
    // 从请求头中获取访问令牌
    const authHeader = req.headers.get('Authorization');
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Invalid or missing authorization header' },
        { status: 401 }
      );
    }
    
    // 提取访问令牌
    const accessToken = authHeader.substring(7); // "Bearer "之后的部分
    
    // 验证令牌
    const payload = await verifyToken(accessToken);
    if (!payload) {
      return NextResponse.json(
        { error: 'Invalid or expired token' },
        { status: 401 }
      );
    }
    
    // 请求IAM用户信息端点
    const response = await axios.get(IAM_CONFIG.USER_INFO_ENDPOINT, {
      headers: {
        'Authorization': `Bearer ${accessToken}`
      }
    });
    
    // 根据IAM文档中的响应格式进行处理
    const { data } = response;
    
    if (data.success) {
      return NextResponse.json(data.data);
    } else {
      return NextResponse.json(
        { error: data.msg || 'Failed to get user info' },
        { status: 400 }
      );
    }
  } catch (error) {
    console.error('User info error:', error);
    
    // 处理特定的错误响应
    if (axios.isAxiosError(error) && error.response) {
      const { status, data } = error.response;
      return NextResponse.json(
        { error: 'Failed to get user info', details: data },
        { status }
      );
    }
    
    // 通用错误处理
    return NextResponse.json(
      { error: 'Failed to get user info' },
      { status: 500 }
    );
  }
} 