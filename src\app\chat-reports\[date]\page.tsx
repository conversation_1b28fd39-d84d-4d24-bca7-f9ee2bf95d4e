"use client";

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { ChatReport, ChatAnalysisResult } from '@/chat-report/types';
import { ChatHistory } from '@/utils/chatHistoryUtils';
import api from '@/utils/apiClient';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { AlertTriangle, Database, Search, MessageSquare, X } from 'lucide-react';
import { withPermission } from '@/components/withPermission';
import { UserRole } from '@/utils/permissionConfig';
import '@/styles/item-design-system.css';

function ReportDetailPageContent() {
  const [report, setReport] = useState<ChatReport | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedChatId, setSelectedChatId] = useState<string | null>(null);
  const [originalChat, setOriginalChat] = useState<ChatHistory | null>(null);
  const [loadingChat, setLoadingChat] = useState(false);
  const [chatError, setChatError] = useState<string | null>(null);
  const [showOriginalChat, setShowOriginalChat] = useState(false);
  const params = useParams<{ date: string }>();
  const router = useRouter();

  const date = params.date;

  // Fetch report data
  useEffect(() => {
    const fetchReport = async () => {
      if (!date) return;

      setLoading(true);
      setError(null);

      try {
        const { data, error, status } = await api.get<ChatReport>(`/api/chat-report?date=${date}`);

        if (error) {
          throw new Error(error);
        }

        if (!data) {
          throw new Error('No data returned from API');
        }

        setReport(data);

        // Auto-select the first chat if available
        if (data.chatIds && data.chatIds.length > 0) {
          setSelectedChatId(data.chatIds[0]);
        }
      } catch (err: any) {
        setError(err.message || 'Failed to load report');
        console.error('Error fetching report:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchReport();
  }, [date]);

  // Handle back button click
  const handleBack = () => {
    router.push('/chat-reports');
  };

  // Get the selected chat analysis
  const selectedChatAnalysis = selectedChatId && report?.chatAnalyses
    ? report.chatAnalyses[selectedChatId]
    : null;

  // Load original chat history
  const loadOriginalChat = async (chatId: string) => {
    if (!chatId || !report) return;  // 确保有report数据

    setLoadingChat(true);
    setChatError(null);
    setOriginalChat(null);

    try {
      // 从报告中获取用户ID
      const userId = report.chatUserMap[chatId];

      // 使用新的API查询聊天历史
      const { data, error } = await api.get<ChatHistory>(
        `/api/chat-history/by-id?chatId=${chatId}&userId=${userId}`
      );

      if (error) {
        throw new Error(error);
      }

      if (!data) {
        throw new Error('No chat data returned');
      }

      setOriginalChat(data);
      setShowOriginalChat(true);
    } catch (err: any) {
      setChatError(err.message || 'Failed to load chat history');
      console.error('Error loading chat history:', err);
    } finally {
      setLoadingChat(false);
    }
  };

  // Close original chat view
  const closeOriginalChat = () => {
    setShowOriginalChat(false);
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <div className="min-h-screen bg-item-bg-primary  text-white">

      <div className="container mx-auto p-4 py-8 relative z-20">
        <button
          onClick={handleBack}
          className="mb-6 flex items-center text-item-purple hover:text-item-purple-light transition font-medium "
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M9.707 14.707a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 1.414L7.414 9H15a1 1 0 110 2H7.414l2.293 2.293a1 1 0 010 1.414z" clipRule="evenodd" />
          </svg>
          Back to Reports
        </button>

        {/* Loading State */}
        {loading && (
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center">
              <div className="inline-block animate-spin rounded-full h-12 w-12 border-4 border-item-purple border-t-transparent mb-4"></div>
              <p className="text-item-gray-300 text-lg ">Loading report...</p>
            </div>
          </div>
        )}

        {/* Error Message */}
        {error && (
          <div className="bg-red-900 border border-red-700 text-red-100 px-6 py-4 rounded-lg mb-6 animate-pulse shadow-lg">
            <div className="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-2 text-red-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
              </svg>
              <span>{error}</span>
            </div>
          </div>
        )}

        {/* Report Header */}
        {report && !loading && (
          <div className="mb-8">
            <h1 className="text-2xl md:text-3xl font-bold mb-6   tracking-tight">Analysis Report for {formatDate(report.date)}</h1>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
              <div className="bg-item-bg-card/80 backdrop-blur-sm p-6 rounded-xl border border-item-gray-800/20 shadow-lg hover:shadow-blue-900/10 transition-all">
                <div className="flex items-center mb-2">
                  <Database className="h-5 w-5 text-item-purple-light mr-2" />
                  <div className="text-sm text-item-purple-light font-semibold tracking-wide">Total Chats</div>
                </div>
                <div className="text-2xl md:text-3xl font-bold text-white">{report.totalChats}</div>
              </div>
              <div className="bg-item-bg-card/80 backdrop-blur-sm p-6 rounded-xl border border-item-gray-800/20 shadow-lg hover:shadow-md transition-all">
                <div className="flex items-center mb-2">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-item-gray-400 mr-2" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                  <div className="text-sm text-item-gray-400 font-semibold tracking-wide">Resolved</div>
                </div>
                <div className="text-2xl md:text-3xl font-bold text-white">{report.totalResolvedChats}</div>
              </div>
              <div className="bg-item-bg-card/80 backdrop-blur-sm p-6 rounded-xl border border-item-gray-800/20 shadow-lg hover:shadow-md transition-all">
                <div className="flex items-center mb-2">
                  <AlertTriangle className="h-5 w-5 text-item-gray-400 mr-2" />
                  <div className="text-sm text-item-gray-400 font-semibold tracking-wide">Unresolved</div>
                </div>
                <div className="text-2xl md:text-3xl font-bold text-white">{report.totalUnresolvedChats}</div>
              </div>
              <div className="bg-item-bg-card/80 backdrop-blur-sm p-6 rounded-xl border border-item-gray-800/20 shadow-lg hover:shadow-md transition-all">
                <div className="flex items-center mb-2">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-item-purple-light mr-2" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                  </svg>
                  <div className="text-sm text-item-purple-light font-semibold tracking-wide">Resolution Rate</div>
                </div>
                <div className="text-2xl md:text-3xl font-bold text-white">
                  {report.totalChats > 0
                    ? Math.round((report.totalResolvedChats / report.totalChats) * 100)
                    : 0}%
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Report Content */}
        {report && !loading && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Chat List */}
            <div className="md:col-span-1">
              <div className="bg-item-bg-card/80 backdrop-blur-sm p-5 rounded-xl border border-item-gray-800/30 shadow-lg">
                <h2 className="text-lg font-semibold mb-4 text-white flex items-center">
                  <MessageSquare className="h-5 w-5 mr-2 text-item-purple" />
                  <span>Conversations</span>
                </h2>
                {report.chatIds.length === 0 ? (
                  <p className="text-item-purple/70 ">No chats available for this day</p>
                ) : (
                  <div className="space-y-2 max-h-[600px] overflow-y-auto pr-2 custom-scrollbar">
                    {report.chatIds.map(chatId => {
                      const analysis = report.chatAnalyses[chatId];
                      const hasDataIssue = analysis.dataRetrievalIssue;
                      const hasParamIssue = analysis.parameterExtractionIssue;
                      const hasEntityIssue = analysis.entityRecognitionIssue;
                      const hasToolIssue = analysis.toolUsageIssue;

                      return (
                        <button
                          key={chatId}
                          onClick={() => setSelectedChatId(chatId)}
                          className={`w-full text-left p-3 rounded-lg transition border  ${
                            selectedChatId === chatId
                              ? 'bg-item-purple/30 border-item-purple/70 shadow-md'
                              : 'bg-item-bg-card/60 border-item-gray-800/30 hover:bg-item-purple/20 hover:border-item-purple/40'
                          }`}
                        >
                          <div className="flex justify-between items-center">
                            <div className="truncate flex-1 text-sm">
                              {hasDataIssue && <span className="text-amber-300 mr-1">⚠️ </span>}
                              {hasParamIssue && <span className="text-orange-300 mr-1">🔍 </span>}
                              {hasEntityIssue && <span className="text-red-300 mr-1">🔤 </span>}
                              {hasToolIssue && <span className="text-purple-300 mr-1">🔧 </span>}
                              {analysis.summary || `Chat ${chatId.substring(0, 8)}`}
                            </div>
                            <div className={`ml-2 px-2.5 py-1 rounded-full text-xs font-medium ${
                              analysis.resolved
                                ? 'bg-item-gray-700/40 text-item-gray-300 border border-item-gray-800/20'
                                : hasDataIssue || hasParamIssue || hasEntityIssue || hasToolIssue
                                  ? 'bg-item-gray-700/40 text-item-gray-300 border border-item-gray-800/20'
                                  : 'bg-item-bg-card/60 text-item-purple-light border border-item-gray-800/20'
                            }`}>
                              {analysis.resolved ? 'Resolved' :
                               hasDataIssue ? 'Data Issue' :
                               hasParamIssue ? 'Param Issue' :
                               hasEntityIssue ? 'Entity Issue' :
                               hasToolIssue ? 'Tool Issue' : 'Unresolved'}
                            </div>
                          </div>
                        </button>
                      );
                    })}
                  </div>
                )}
              </div>
            </div>

            {/* Chat Details */}
            <div className="md:col-span-2">
              {selectedChatAnalysis ? (
                <div className="bg-item-bg-card/80 backdrop-blur-sm border border-item-gray-800/30 rounded-xl p-5 shadow-lg">
                  <div className="flex justify-between items-start mb-4">
                    <h2 className="text-xl font-semibold text-item-purple-light ">Conversation Analysis</h2>
                    <div className="flex space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        className="text-item-purple border-item-gray-800/30 hover:bg-item-purple/30"
                        onClick={() => loadOriginalChat(selectedChatAnalysis.chatId || selectedChatId!)}
                        disabled={loadingChat}
                      >
                        <MessageSquare className="h-4 w-4 mr-1" />
                        View Original Chat
                      </Button>
                    </div>
                  </div>

                    <div className="mb-6">
                      <h3 className="text-md font-semibold mb-3 text-white flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-item-purple" viewBox="0 0 20 20" fill="currentColor">
                          <path d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM14 11a1 1 0 011 1v1h1a1 1 0 110 2h-1v1a1 1 0 11-2 0v-1h-1a1 1 0 110-2h1v-1a1 1 0 011-1z" />
                        </svg>
                        <span>Summary</span>
                      </h3>
                      <div className="bg-item-bg-card/60 p-5 rounded-lg border border-item-gray-800/30 shadow-inner">
                        <p className="text-item-gray-300 leading-relaxed">{selectedChatAnalysis.summary}</p>
                      </div>
                    </div>

                    <div className="mb-6">
                      <h3 className="text-md font-semibold mb-3 text-white flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-item-purple" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                        </svg>
                        <span>Resolution Status</span>
                      </h3>
                      <div className="bg-item-bg-card/60 p-5 rounded-lg border border-item-gray-800/30 shadow-inner">
                        <div className="flex flex-col space-y-4">
                          <div className="flex items-center">
                            <div className={`px-5 py-2.5 rounded-md font-medium text-sm shadow-sm ${
                              selectedChatAnalysis.resolved
                                ? 'bg-item-gray-700/60 text-item-gray-300 border border-item-gray-800/30'
                                : selectedChatAnalysis.dataRetrievalIssue || selectedChatAnalysis.parameterExtractionIssue || selectedChatAnalysis.entityRecognitionIssue || selectedChatAnalysis.toolUsageIssue
                                  ? 'bg-item-gray-700/60 text-item-gray-300 border border-item-gray-800/30'
                                  : 'bg-item-bg-card/60 text-white border border-item-gray-800/20'
                            }`}>
                              <span className="font-semibold">
                                {selectedChatAnalysis.resolved ? 'Resolved' :
                                 selectedChatAnalysis.dataRetrievalIssue ? 'Data Retrieval Issue' :
                                 selectedChatAnalysis.parameterExtractionIssue ? 'Parameter Extraction Issue' :
                                 selectedChatAnalysis.entityRecognitionIssue ? 'Entity Recognition Issue' :
                                 selectedChatAnalysis.toolUsageIssue ? 'Tool Usage Issue' : 'Unresolved'}
                              </span>
                            </div>
                          </div>

                          {!selectedChatAnalysis.resolved && selectedChatAnalysis.unresolvedReason && (
                            <div className="text-item-gray-300 leading-relaxed">
                              <span className="font-semibold text-item-gray-400">Reason:</span> {selectedChatAnalysis.unresolvedReason}
                            </div>
                          )}
                        </div>
                      </div>

                      {(selectedChatAnalysis.dataRetrievalIssue ||
                        selectedChatAnalysis.parameterExtractionIssue ||
                        selectedChatAnalysis.entityRecognitionIssue ||
                        selectedChatAnalysis.toolUsageIssue) &&
                        selectedChatAnalysis.issueDetails && (
                        <div className="mt-4 p-5 bg-item-bg-card/60 border border-item-gray-800/30 rounded-lg shadow-inner">
                          <div className="flex items-start mb-4">
                            <div className="bg-item-gray-700/40 p-1.5 rounded-full mr-3 mt-0.5 flex-shrink-0">
                              <AlertTriangle className="h-5 w-5 text-item-gray-400" />
                            </div>
                            <div>
                              <h4 className="text-item-gray-400 font-semibold mb-3">Issue Details</h4>
                              <p className="text-item-gray-300 leading-relaxed">{selectedChatAnalysis.issueDetails}</p>
                            </div>
                          </div>

                          {selectedChatAnalysis.userActualIntent && (
                            <div className="mt-4 border-t border-item-gray-800/30 pt-4">
                              <h4 className="text-item-purple-light font-semibold mb-2">User's Actual Intent:</h4>
                              <div className="bg-item-purple/10 p-4 rounded-md border border-item-gray-800/20 shadow-inner">
                                <p className="text-item-gray-300 leading-relaxed">{selectedChatAnalysis.userActualIntent}</p>
                              </div>
                            </div>
                          )}

                          {selectedChatAnalysis.failedTools && selectedChatAnalysis.failedTools.length > 0 && (
                            <div className="mt-4 border-t border-item-gray-800/30 pt-4">
                              <h4 className="text-item-purple-light font-semibold mb-2">Failed Tools:</h4>
                              <div className="bg-item-bg-card/80 p-4 rounded-md border border-item-gray-800/20 shadow-inner">
                                <ul className="list-disc list-inside text-item-gray-300 space-y-2 pl-1">
                                  {selectedChatAnalysis.failedTools.map((tool, index) => (
                                    <li key={index} className="leading-relaxed">{tool}</li>
                                  ))}
                                </ul>
                              </div>
                            </div>
                          )}

                          {selectedChatAnalysis.failedParameters && selectedChatAnalysis.failedParameters !== null && (
                            <div className="mt-4 border-t border-item-gray-800/30 pt-4">
                              <h4 className="text-item-purple-light font-semibold mb-2">Failed Parameters:</h4>
                              <div className="bg-item-bg-secondary/80 p-5 rounded-md text-item-gray-300 overflow-x-auto shadow-inner border border-item-gray-800/20">
                                <pre className="whitespace-pre-wrap text-sm">
                                  <code className="language-json">
                                    {typeof selectedChatAnalysis.failedParameters === 'string'
                                      ? selectedChatAnalysis.failedParameters
                                      : JSON.stringify(selectedChatAnalysis.failedParameters, null, 2)}
                                  </code>
                                </pre>
                              </div>
                            </div>
                          )}

                          {selectedChatAnalysis.rawToolCalls && selectedChatAnalysis.rawToolCalls.length > 0 && (
                            <div className="mt-4 border-t border-item-gray-800/30 pt-4">
                              <h4 className="text-item-purple-light font-semibold mb-2">Raw Tool Calls:</h4>
                              <div className="space-y-3">
                                {selectedChatAnalysis.rawToolCalls.map((toolCall, idx) => (
                                  <div key={idx} className="bg-item-bg-secondary/80 p-5 rounded-md text-item-gray-300 overflow-x-auto shadow-inner border border-item-gray-800/20">
                                    <pre className="whitespace-pre-wrap text-sm">
                                      <code className="language-json">
                                        {toolCall}
                                      </code>
                                    </pre>
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}

                          {selectedChatAnalysis.improvementSuggestions && selectedChatAnalysis.improvementSuggestions.length > 0 && (
                            <div className="mt-4 border-t border-item-gray-800/30 pt-4">
                              <h4 className="text-item-purple-light font-semibold mb-2">Improvement Suggestions:</h4>
                              <div className="bg-item-bg-card/80 p-4 rounded-md border border-item-gray-800/20 shadow-inner">
                                <ul className="list-disc list-inside text-item-gray-300 space-y-2 pl-1">
                                  {selectedChatAnalysis.improvementSuggestions.map((suggestion, index) => (
                                    <li key={index} className="leading-relaxed">{suggestion}</li>
                                  ))}
                                </ul>
                              </div>
                            </div>
                          )}
                        </div>
                      )}
                    </div>

                    <div className="mb-6">
                      <h3 className="text-md font-semibold mb-3 text-white flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-item-purple" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                        </svg>
                        <span>Key Insights</span>
                      </h3>
                      <div className="bg-item-bg-card/60 p-5 rounded-lg border border-item-gray-800/30 shadow-inner">
                        {selectedChatAnalysis.keyInsights.length > 0 ? (
                          <ul className="space-y-3">
                            {selectedChatAnalysis.keyInsights.map((insight, index) => (
                              <li key={index} className="flex items-start">
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-item-purple mr-3 flex-shrink-0 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
                                  <path d="M11 3a1 1 0 10-2 0v1a1 1 0 102 0V3zM15.657 5.757a1 1 0 00-1.414-1.414l-.707.707a1 1 0 001.414 1.414l.707-.707zM18 10a1 1 0 01-1 1h-1a1 1 0 110-2h1a1 1 0 011 1zM5.05 6.464A1 1 0 106.464 5.05l-.707-.707a1 1 0 00-1.414 1.414l.707.707zM5 10a1 1 0 01-1 1H3a1 1 0 110-2h1a1 1 0 011 1zM8 16v-1h4v1a2 2 0 11-4 0zM12 14c.015-.34.208-.646.477-.859a4 4 0 10-4.954 0c.27.213.462.519.476.859h4.002z" />
                                </svg>
                                <span className="text-item-gray-300 leading-relaxed">{insight}</span>
                              </li>
                            ))}
                          </ul>
                        ) : (
                          <p className="text-item-gray-400 italic">No key insights available.</p>
                        )}
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <h3 className="text-md font-semibold mb-3 text-white flex items-center">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-item-purple" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z" clipRule="evenodd" />
                          </svg>
                          <span>User Questions</span>
                        </h3>
                        <div className="bg-item-bg-card/60 p-5 rounded-lg border border-item-gray-800/30 shadow-inner">
                          {selectedChatAnalysis.userQuestions.length > 0 ? (
                            <div className="space-y-4 max-h-[300px] overflow-y-auto pr-2 custom-scrollbar">
                              {selectedChatAnalysis.userQuestions.map((question, index) => (
                                <div key={index} className="bg-item-purple/10 p-4 rounded-lg border border-item-gray-800/20 shadow-inner">
                                  <div className="text-xs text-item-purple-light mb-2 font-medium flex items-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                                      <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                                    </svg>
                                    User {index + 1}
                                  </div>
                                  <div className="text-item-gray-300 leading-relaxed">
                                    {question.length > 300 ? question.substring(0, 300) + '...' : question}
                                  </div>
                                </div>
                              ))}
                            </div>
                          ) : (
                            <p className="text-item-gray-400 italic">No user questions available.</p>
                          )}
                        </div>
                      </div>

                      <div>
                        <h3 className="text-md font-semibold mb-3 text-white flex items-center">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-item-purple" viewBox="0 0 20 20" fill="currentColor">
                            <path d="M2 10.5a1.5 1.5 0 113 0v6a1.5 1.5 0 01-3 0v-6zM6 10.333v5.43a2 2 0 001.106 1.79l.05.025A4 4 0 008.943 18h5.416a2 2 0 001.962-1.608l1.2-6A2 2 0 0015.56 8H12V4a2 2 0 00-2-2 1 1 0 00-1 1v.667a4 4 0 01-.8 2.4L6.8 7.933a4 4 0 00-.8 2.4z" />
                          </svg>
                          <span>Agent Responses</span>
                        </h3>
                        <div className="bg-item-bg-card/60 p-5 rounded-lg border border-item-gray-800/30 shadow-inner">
                          {selectedChatAnalysis.agentResponses.length > 0 ? (
                            <div className="space-y-4 max-h-[300px] overflow-y-auto pr-2 custom-scrollbar">
                              {selectedChatAnalysis.agentResponses.map((response, index) => (
                                <div key={index} className="bg-item-bg-card/60 p-4 rounded-lg border border-item-gray-800/30 shadow-inner">
                                  <div className="text-xs text-item-purple-light mb-2 font-medium flex items-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z" clipRule="evenodd" />
                                    </svg>
                                    Agent {index + 1}
                                  </div>
                                  <div className="text-item-gray-300 leading-relaxed">
                                    {response.length > 300 ? response.substring(0, 300) + '...' : response}
                                  </div>
                                </div>
                              ))}
                            </div>
                          ) : (
                            <p className="text-item-gray-400 italic">No agent responses available.</p>
                          )}
                        </div>
                      </div>
                    </div>
                </div>
              ) : (
                <div className="bg-item-bg-card/80 backdrop-blur-sm border border-item-gray-800/30 rounded-xl p-8 text-center h-full flex items-center justify-center shadow-lg">
                  <div>
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto text-item-gray-500 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                    </svg>
                    <p className="text-item-purple/70 text-lg ">Select a conversation from the list to view its analysis</p>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
      {showOriginalChat && (
        <div className="fixed inset-0 bg-black/70 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="bg-item-bg-secondary border border-item-gray-800/30 rounded-xl w-full max-w-4xl max-h-[90vh] flex flex-col shadow-2xl">
            <div className="flex justify-between items-center p-4 border-b border-item-gray-800/30">
              <h3 className="text-xl font-semibold text-white ">Original Chat Conversation</h3>
              <button
                onClick={closeOriginalChat}
                className="text-item-gray-400 hover:text-white transition"
              >
                <X className="h-6 w-6" />
              </button>
            </div>

            <div className="flex-1 overflow-y-auto p-4 custom-scrollbar">
              {loadingChat && (
                <div className="flex items-center justify-center h-40">
                  <div className="inline-block animate-spin rounded-full h-8 w-8 border-4 border-item-purple border-t-transparent mr-2"></div>
                  <p className="text-item-gray-300">Loading chat...</p>
                </div>
              )}

              {chatError && (
                <div className="p-4 bg-red-900/30 border border-red-700/30 rounded-lg text-red-200">
                  Error loading chat: {chatError}
                </div>
              )}

              {originalChat && !loadingChat && !chatError && (
                <div className="bg-item-bg-secondary/80 p-5 rounded-md text-item-gray-300 overflow-x-auto shadow-inner border border-item-gray-800/20">
                  <pre className="whitespace-pre-wrap  text-sm">
                    <code className="language-json">
                      {JSON.stringify(originalChat, null, 2)}
                    </code>
                  </pre>
                </div>
              )}
            </div>

            <div className="p-4 border-t border-item-gray-800/30 flex justify-end">
              <Button
                variant="outline"
                onClick={closeOriginalChat}
                className="text-item-purple border-item-gray-800/30 hover:bg-item-bg-hover"
              >
                Close
              </Button>
            </div>
          </div>
        </div>
      )}

      <style jsx global>{`
        .custom-scrollbar::-webkit-scrollbar {
          width: 6px;
        }
        .custom-scrollbar::-webkit-scrollbar-track {
          background: var(--bg-secondary);
          border-radius: 10px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb {
          background: var(--border-secondary);
          border-radius: 10px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
          background: var(--item-purple);
        }
        /* For Firefox */
        .custom-scrollbar {
          scrollbar-width: thin;
          scrollbar-color: var(--border-secondary) var(--bg-secondary);
        }
      `}</style>
    </div>
  );
}

// 使用权限控制 HOC 包装组件
const ReportDetailPage = withPermission(ReportDetailPageContent, [
  UserRole.ADMIN,
  UserRole.REPORT_VIEWER
]);

export default ReportDetailPage;