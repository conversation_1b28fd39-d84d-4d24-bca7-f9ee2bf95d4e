import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { verifyToken } from '@/utils/authUtils';
import {
  UserRole,
  ROLE_PROTECTED_API_ROUTES,
  getRoleNamesByIds,
  getUserRolesByIamRoles
} from '@/utils/serverPermissionConfig';

/**
 * 从令牌中提取用户角色
 * 使用函数式方法处理角色提取逻辑
 */
const extractUserRoles = async (payload: any, token: string): Promise<UserRole[]> => {
  // 初始化用户角色数组，所有登录用户都有基本用户角色
  const userRoles: UserRole[] = [UserRole.USER];

  // 使用函数式方法处理角色提取和合并
  const mergeRoles = (newRoles: UserRole[]) => {
    newRoles.forEach(role => {
      if (!userRoles.includes(role)) {
        userRoles.push(role);
      }
    });
  };

  // 处理角色名称提取
  const extractRoleNames = (rolesData: any[]): string[] => {
    return rolesData.map((role: any) => {
      if (typeof role === 'string') return role;
      if (role && typeof role === 'object' && role.name) return role.name;
      return null;
    }).filter(Boolean);
  };

  // 1. 从 role_ids 获取角色名称
  if (payload.data?.role_ids && Array.isArray(payload.data.role_ids)) {
    try {
      const roleNames = await getRoleNamesByIds(payload.data.role_ids, token);
      mergeRoles(getUserRolesByIamRoles(roleNames));
    } catch (error) {
      console.error(`Error getting role names:`, error);
    }
  }

  // 2. 检查 user.userRoles 字段 (IAM 用户角色)
  if (payload.data?.user?.userRoles && Array.isArray(payload.data.user.userRoles)) {
    const roleNames = extractRoleNames(payload.data.user.userRoles);
    mergeRoles(getUserRolesByIamRoles(roleNames));
  }

  // 3. 检查 userRoles 字段 (可能直接在 payload.data 中)
  if (payload.data?.userRoles && Array.isArray(payload.data.userRoles)) {
    const roleNames = extractRoleNames(payload.data.userRoles);
    mergeRoles(getUserRolesByIamRoles(roleNames));
  }

  return userRoles;
};

/**
 * 检查用户是否有权限访问路由
 */
const checkRoutePermission = (
  pathname: string,
  userRoles: UserRole[]
): { hasPermission: boolean, requiredRoles?: UserRole[] } => {
  // 检查是否是受保护的 API 路由
  const requiredRoutePath = Object.keys(ROLE_PROTECTED_API_ROUTES).find(route =>
    pathname.startsWith(route)
  );

  if (!requiredRoutePath) {
    return { hasPermission: true };
  }

  const requiredRoles = ROLE_PROTECTED_API_ROUTES[requiredRoutePath];

  // 检查用户是否有所需角色
  const hasPermission = requiredRoles.some(role => userRoles.includes(role));

  return { hasPermission, requiredRoles };
};

/**
 * 角色权限中间件
 * 处理基于用户角色的 API 路由访问控制
 * 假设 token 已经在 apiAuthMiddleware 中验证过
 * @param req 请求对象
 * @returns 响应对象
 */
export async function roleAuthMiddleware(req: NextRequest) {
  const { pathname } = req.nextUrl;

  // 检查是否是受保护的 API 路由
  const isProtectedApiRoute = Object.keys(ROLE_PROTECTED_API_ROUTES).some(route =>
    pathname.startsWith(route)
  );

  // 如果不是受保护的 API 路由，直接放行
  if (!isProtectedApiRoute) {
    return NextResponse.next();
  }

  // 获取令牌 - 假设 apiAuthMiddleware 已经验证过令牌
  // 我们只需要获取令牌来提取用户角色
  let token = req.headers.get('Authorization')?.substring(7) ||
              req.cookies.get('iam_access_token')?.value ||
              req.headers.get('x-session-token') || '';

  // 获取令牌载荷 - 假设令牌已经验证过
  const payload = await verifyToken(token);
  if (!payload) {
    // 这种情况不应该发生，因为 apiAuthMiddleware 应该已经验证过令牌
    // 但为了健壮性，我们仍然处理这种情况
    return new NextResponse(
      JSON.stringify({ error: 'Invalid token in role middleware' }),
      { status: 401, headers: { 'Content-Type': 'application/json' } }
    );
  }

  // 提取用户角色
  const userRoles = await extractUserRoles(payload, token);

  // 检查路由权限
  const { hasPermission, requiredRoles } = checkRoutePermission(pathname, userRoles);

  if (!hasPermission) {
    console.log(`[RoleAuth] Access denied for route ${pathname}. User roles: ${userRoles.join(', ')}, Required roles: ${requiredRoles?.join(', ')}`);
    return new NextResponse(
      JSON.stringify({ error: 'Access denied: insufficient permissions' }),
      { status: 403, headers: { 'Content-Type': 'application/json' } }
    );
  }

  // 用户有权限，允许请求继续
  return NextResponse.next();
}
