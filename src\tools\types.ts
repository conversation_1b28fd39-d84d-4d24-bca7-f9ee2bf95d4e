/**
 * 工具定义
 */
export interface ToolDefinition {
  name: string;
  description: string;
  parameters: {
    type: string;
    properties: Record<string, any>;
    required?: string[];
  };
}

/**
 * 工具调用输出
 */
export interface ToolCallOutput {
  content: any;
  message?: string;
  complete?: boolean;
}

/**
 * 输入类型枚举
 */
export enum InputType {
  TEXT = "text",
  TEMPLATE = "template"
}

/**
 * 模板字段类型枚举
 */
export enum TemplateFieldType {
  TEXT = "text",
  TEXTAREA = "textarea",
  NUMBER = "number",
  SELECT = "select",
  CHECKBOX = "checkbox",
  RADIO = "radio",
  DATE = "date",
  DATETIME = "datetime",
  TIME = "time",
  FILE = "file",
  USER_SELECTOR = "userSelector",
  CUSTOMER_SELECTOR = "customerSelector",
  TITLE_SELECTOR = "titleSelector",
  GENERAL_PROJECT_SELECTOR = "generalProjectSelector",
  JOBCODE_SELECTOR = "jobcodeSelector",
  CARRIER_SELECTOR = "carrierSelector",
  DELIVERY_SERVICE_SELECTOR = "deliveryServiceSelector",
  ITEMMASTER_SELECTOR = "itemmasterSelector",
  ITEMMASTER_UOM_SELECTOR = "itemmasterUomSelector",
  STATE_SELECTOR = "stateSelector",
  SWITCH = "switch",
  ARRAY = "array",
  ADDRESS = "address",
  PORTAL_CUSTOMER_SELECTOR = "portalCustomerSelector",
  PORTAL_INVOICE_SELECTOR = "portalInvoiceSelector",
  ACCESSORIAL_MULTIPLE_SELECTOR = "accessorialMultipleSelector",
  MULTIPLE_USER_SELECTOR = "multipleUserSelector"
}

/**
 * 模板字段验证
 */
export interface TemplateFieldValidation {
  pattern?: string;
  min?: number;
  max?: number;
  message?: string;
}

/**
 * 模板字段选项
 */
export interface TemplateFieldOption {
  label: string;
  value: string;
}

/**
 * 模板字段定义
 */
export interface TemplateField {
  name: string;
  label: string;
  type: TemplateFieldType | string;
  required?: boolean;
  options?: TemplateFieldOption[];
  validation?: TemplateFieldValidation;
  apiPath?: string;
  apiHeaders?: Record<string, string>;
  dependsOn?: string; // Field name that this field depends on
  arrayItemFields?: TemplateField[]; // Fields for array items when type is ARRAY
  defaultValue?: any; // Default value for the field
}

/**
 * 模型提供者
 */
export enum ModelProvider {
  OPENAI = "openai",
  CLAUDE = "claude"
}

/**
 * 聊天消息
 */
export interface ChatMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
  name?: string;
  function_call?: {
    name: string;
    arguments: string;
  };
}

/**
 * 工具调用参数
 */
export interface ToolCallParams {
  name: string;
  arguments: Record<string, any>;
}