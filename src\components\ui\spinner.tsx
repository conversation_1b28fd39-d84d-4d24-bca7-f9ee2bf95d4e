'use client';

import * as React from 'react';
import { cn } from '@/lib/utils';

interface SpinnerProps extends React.HTMLAttributes<HTMLDivElement> {
  size?: 'sm' | 'md' | 'lg';
}

export function Spinner({ size = 'md', className, ...props }: SpinnerProps) {
  const sizeClass = {
    sm: 'h-4 w-4 border-2',
    md: 'h-6 w-6 border-2',
    lg: 'h-8 w-8 border-3',
  }[size];

  return (
    <div
      className={cn(
        'inline-block animate-spin rounded-full border-current border-t-transparent',
        sizeClass,
        className
      )}
      {...props}
    >
      <span className="sr-only">Loading...</span>
    </div>
  );
} 