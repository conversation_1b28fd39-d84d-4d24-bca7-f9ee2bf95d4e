# 语音转录流式更新实现

## 概述

我们实现了基于 Vercel AI SDK v5 之前方法的语音转录流式更新功能，允许 AI 助手的语音回复以增量方式实时显示在聊天界面中。

## 实现方案

### 1. 核心组件修改

#### Chat.tsx
- 添加了 `handleVoiceTranscriptDelta` 函数处理增量更新
- 使用 `currentVoiceTranscriptIdRef` 跟踪当前正在进行的语音转录消息
- 使用 `messagesRef` 避免依赖数组问题
- 实现了消息的增量追加逻辑

#### ChatInput.tsx
- 更新了 `ChatInputProps` 接口，添加增量更新回调
- 传递新的回调函数给 `useRealtimeVoice` hook

#### useRealtimeVoice.ts
- 更新了 `UseRealtimeVoiceOptions` 接口
- 在 `assistant_transcript_delta` 事件中调用增量更新回调
- 在 `assistant_transcript_done` 事件中调用完成回调

### 2. 事件流程

```
OpenAI Realtime API
    ↓ response.audio_transcript.delta
RealtimeClient
    ↓ assistant_transcript_delta
useRealtimeVoice
    ↓ onAssistantTranscriptDelta
ChatInput
    ↓ onVoiceTranscriptDelta
Chat.tsx
    ↓ handleVoiceTranscriptDelta
UI 增量更新
```

### 3. 关键特性

- **增量更新**: 每个语音转录片段都会立即显示
- **消息合并**: 同一次语音回复的所有片段会合并到同一条消息中
- **状态管理**: 使用 ref 避免 React 状态更新的竞态条件
- **兼容性**: 保持与现有语音功能的兼容性

### 4. 调试信息

实现中包含了详细的控制台日志，可以跟踪：
- 增量更新的接收
- 消息 ID 的匹配
- 消息内容的累积
- 新消息的创建

## 使用方法

1. 连接语音功能
2. 开始语音对话
3. AI 回复会以增量方式实时显示
4. 每个语音片段都会立即追加到当前消息中

## 技术细节

- 使用 `voice-transcript-` 前缀标识语音转录消息
- 通过消息 ID 匹配确保增量更新的正确性
- 使用 `appendClientMessage` 确保消息格式的一致性
- 避免了 React 批量更新导致的显示延迟问题 