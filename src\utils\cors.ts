/**
 * @file CORS工具函数
 * @description 处理跨域请求的响应头
 */

import { NextResponse } from 'next/server';

/**
 * 添加CORS头到响应对象
 * @param response 响应对象
 * @returns 添加了CORS头的响应对象
 */
export function addCorsHeaders<T extends globalThis.Response | NextResponse>(response: T): T {
  // 从环境变量获取允许的来源，如果未设置则默认为通配符
  const allowedOrigin = process.env.CORS_ALLOWED_ORIGIN || '*';
  
  // 创建新的响应头
  const newHeaders = new Headers(response.headers);
  
  // 添加CORS头
  newHeaders.set('Access-Control-Allow-Origin', allowedOrigin);
  newHeaders.set('Access-Control-Allow-Methods', 'POST, OPTIONS, GET, DELETE, PATCH');
  newHeaders.set('Access-Control-Allow-Headers', '*');
  newHeaders.set('Access-Control-Allow-Credentials', 'true');
  
  // 对于流式响应，我们需要克隆响应并添加头
  if (response instanceof Response) {
    const { status, statusText, body } = response;
    return new Response(body, {
      status,
      statusText,
      headers: newHeaders
    }) as T;
  } else {
    // 为NextResponse设置新的头
    const { status, body } = response;
    return new NextResponse(body, {
      status,
      headers: newHeaders
    }) as T;
  }
}

/**
 * 创建预检请求的响应
 * @returns 预检请求的响应对象
 */
export function createPreflightResponse(): NextResponse {
  return new NextResponse(null, {
    status: 204,
    headers: {
      'Access-Control-Allow-Origin': process.env.CORS_ALLOWED_ORIGIN || '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS, GET, DELETE, PATCH',
      'Access-Control-Allow-Headers': '*',
      'Access-Control-Allow-Credentials': 'true',
      'Access-Control-Max-Age': '86400'
    }
  });
} 