{"openapi": "3.0.1", "info": {"title": "FMS", "description": "", "version": "1.0.0"}, "paths": {"/fms-platform-dockapp/ShipmentOrderDockApp/QueryTripRelationOrderInfoByOrderNo": {"post": {"summary": "AI: Query Trip-related order information based on order number", "deprecated": false, "description": "", "tags": ["ShipmentOrderDockApp"], "parameters": [], "requestBody": {"content": {"application/json-patch+json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TripRelationOrderInfoRequestDto"}}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TripRelationOrderInfoResponse"}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dockapp/shipmentorder-dockapp/query-inquiryorderinfo": {"post": {"summary": "AI: Query order information", "deprecated": false, "description": "", "tags": ["ShipmentOrderDockApp"], "parameters": [], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/DockAppInquiryOrderRequestDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/DockAppInquiryOrderInfoDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dockapp/ShipmentOrderDockApp/TestMsg": {"get": {"summary": "AI: Test message", "deprecated": false, "description": "", "tags": ["ShipmentOrderDockApp"], "parameters": [], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "boolean"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dockapp/ShipmentOrderDockApp/TestGeneratePalletId": {"get": {"summary": "/fms-platform-dockapp/ShipmentOrderDockApp/TestGeneratePalletId", "deprecated": false, "description": "", "tags": ["ShipmentOrderDockApp"], "parameters": [], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "integer", "format": "int64"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/dispatch_local_tasks/_search": {"post": {"summary": "ES Search", "deprecated": false, "description": "", "tags": [], "parameters": [{"name": "pretty", "in": "query", "description": "", "required": false, "example": "true", "schema": {"type": "string"}}, {"name": "error_trace", "in": "query", "description": "", "required": false, "example": "true", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {}}}}, "headers": {}}}, "security": [{"basic": []}]}}, "/fms-platform-syncdata/home/<USER>": {"get": {"summary": "Unnamed interface", "deprecated": false, "description": "", "tags": [], "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-file/Storage/DownloadInternal": {"get": {"summary": "Unnamed interface", "deprecated": false, "description": "", "tags": [], "parameters": [{"name": "fileName", "in": "query", "description": "", "required": false, "example": "fms_app_trip_bol/fms_app_trip_bol_DO241100003900_1731951516369_0.jpeg", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/api/location/v1/terminal/code/LAX": {"get": {"summary": "Unnamed interface", "deprecated": false, "description": "", "tags": [], "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}}, "components": {"schemas": {"ConsigneeDto": {"type": "object", "properties": {"consignee_name": {"type": "string", "nullable": true}, "address1": {"type": "string", "nullable": true}, "address2": {"type": "string", "nullable": true}, "city": {"type": "string", "nullable": true}, "state": {"type": "string", "nullable": true}, "zipcode": {"type": "string", "nullable": true}}, "additionalProperties": false}, "DockAppInquiryOrderInfoDto": {"type": "object", "properties": {"order_no": {"type": "string", "nullable": true}, "pro_no": {"type": "string", "nullable": true}, "invoice_pro": {"type": "string", "nullable": true}, "reference": {"type": "string", "nullable": true}, "reference2": {"type": "string", "nullable": true}, "bol": {"type": "string", "nullable": true}, "load_no": {"type": "string", "nullable": true}, "pickup_completedate": {"type": "string", "format": "date-time", "nullable": true}, "consignee_zip": {"type": "string", "nullable": true}, "city": {"type": "string", "nullable": true}, "state": {"type": "string", "nullable": true}, "pallet_qty": {"type": "integer", "format": "int32"}, "carton_qty": {"type": "integer", "format": "int32"}, "shipper_terminal": {"type": "string", "nullable": true}, "consignee_terminal": {"type": "string", "nullable": true}, "route_suggestion_terminal": {"type": "string", "nullable": true}, "schedule_out_bound_date": {"type": "string", "format": "date-time", "nullable": true}, "delivery_ploygon": {"type": "string", "nullable": true}, "pallet_infos": {"type": "array", "items": {"$ref": "#/components/schemas/PalletInfo"}, "nullable": true}, "trip_no": {"type": "integer", "format": "int64", "nullable": true}, "shipment_type": {"type": "string", "nullable": true}, "shipper": {"$ref": "#/components/schemas/ShipperDto"}, "consignee": {"$ref": "#/components/schemas/ConsigneeDto"}}, "additionalProperties": false}, "DockAppInquiryOrderRequestDto": {"type": "object", "properties": {"order_no": {"type": "string", "nullable": true}, "pro_no": {"type": "string", "nullable": true}, "invoice_pro": {"type": "string", "nullable": true}, "reference": {"type": "string", "nullable": true}, "reference2": {"type": "string", "nullable": true}, "bol": {"type": "string", "nullable": true}, "load_no": {"type": "string", "nullable": true}, "tracking_no": {"type": "string", "nullable": true}, "key_word": {"type": "string", "nullable": true}}, "additionalProperties": false}, "PalletInfo": {"type": "object", "properties": {"pallet_no": {"type": "string", "nullable": true}, "sequence": {"type": "integer", "format": "int32"}, "weight": {"type": "number", "format": "double"}, "height": {"type": "number", "format": "double"}, "width": {"type": "number", "format": "double"}, "length": {"type": "number", "format": "double"}, "linear_uom": {"type": "string", "nullable": true}, "weight_uom": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ShipperDto": {"type": "object", "properties": {"shipper_name": {"type": "string", "nullable": true}, "address1": {"type": "string", "nullable": true}, "address2": {"type": "string", "nullable": true}, "city": {"type": "string", "nullable": true}, "state": {"type": "string", "nullable": true}, "zipcode": {"type": "string", "nullable": true}}, "additionalProperties": false}, "TripRelationOrderInfoDto": {"type": "object", "properties": {"order_no": {"type": "string", "nullable": true}, "pro_no": {"type": "string", "nullable": true}, "load_sequence": {"type": "integer", "format": "int32", "nullable": true}, "invoice_pro": {"type": "string", "nullable": true}, "reference": {"type": "string", "nullable": true}, "reference2": {"type": "string", "nullable": true}, "bol": {"type": "string", "nullable": true}, "load_no": {"type": "string", "nullable": true}, "pickup_completedate": {"type": "string", "format": "date-time", "nullable": true}, "consignee_zip": {"type": "string", "nullable": true}, "city": {"type": "string", "nullable": true}, "state": {"type": "string", "nullable": true}, "pallet_qty": {"type": "integer", "format": "int32"}, "carton_qty": {"type": "integer", "format": "int32"}, "shipper_terminal": {"type": "string", "nullable": true}, "consignee_terminal": {"type": "string", "nullable": true}, "route_suggestion_terminal": {"type": "string", "nullable": true}, "delivery_ploygon": {"type": "string", "nullable": true}, "schedule_out_bound_date": {"type": "string", "format": "date-time", "nullable": true}, "pallet_infos": {"type": "array", "items": {"$ref": "#/components/schemas/PalletInfo"}, "nullable": true}, "shipment_type": {"type": "string", "nullable": true}, "service_level": {"$ref": "#/components/schemas/ServiceLevelEnum"}, "pallet_qty_uom": {"type": "string", "nullable": true}, "carton_qty_uom": {"type": "string", "nullable": true}, "next_route_name": {"type": "string", "nullable": true}, "customer_name": {"type": "string", "nullable": true}, "bill_to_name": {"type": "string", "nullable": true}, "shipper": {"$ref": "#/components/schemas/ShipperDto"}, "consignee": {"$ref": "#/components/schemas/ConsigneeDto"}}, "additionalProperties": false}, "TripRelationOrderInfoRequestDto": {"type": "object", "properties": {"task_id": {"type": "integer", "format": "int64"}, "task_type": {"type": "string", "nullable": true}, "load_type": {"type": "string", "nullable": true}, "termial_code": {"type": "string", "nullable": true}, "order_keys": {"type": "array", "items": {"type": "integer", "format": "int64"}, "nullable": true}, "load_sequence": {"type": "integer", "format": "int32"}, "task_no": {"type": "string", "nullable": true}}, "additionalProperties": false}, "TripRelationOrderInfoResponse": {"type": "object", "properties": {"order_infos": {"type": "array", "items": {"$ref": "#/components/schemas/TripRelationOrderInfoDto"}, "nullable": true}, "task_id": {"type": "integer", "format": "int64", "nullable": true}}, "additionalProperties": false}, "ServiceLevelEnum": {"enum": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 30], "type": "integer", "format": "int32"}}}}