import { useState, useEffect, useCallback, useRef } from 'react';
import { Message } from '@ai-sdk/react';
import { ChatHistory, generateChatTitle } from '@/utils/chatHistoryUtils';
import { useAuth } from '@/app/contexts/AuthContext';
import { clientUserContextManager } from '@/utils/clientUserContext';
import api from '@/utils/apiClient';

// 定义钩子返回类型
interface UseChatHistoryReturn {
  chatHistories: ChatHistory[];
  loading: boolean;
  error: string | null;
  selectedChatId: string | null;
  loadChatHistories: () => Promise<void>;
  selectChat: (id: string) => Promise<ChatHistory | null>;
  createNewChat: () => void;
  saveCurrentChat: (messages: Message[], model: string) => Promise<ChatHistory>;
  deleteChat: (id: string) => Promise<boolean>;
  updateChatTitle: (id: string, newTitle: string) => Promise<boolean>;
}

/**
 * 提供聊天历史管理功能的自定义钩子
 */
export function useChatHistory(): UseChatHistoryReturn {
  const [chatHistories, setChatHistories] = useState<ChatHistory[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedChatId, setSelectedChatId] = useState<string | null>(null);
  
  // 使用ref来防止重复加载和加载竞争条件
  const isLoadingRef = useRef(false);
  const initialLoadDoneRef = useRef(false);
  
  // 获取当前用户信息
  const { user, isAuthenticated, isLoading: authLoading, tokenData } = useAuth();
  
  // 获取用户ID - 尝试多种来源以确保获取到正确ID
  const currentUserId = user?.id ? String(user.id) : 
                      tokenData?.data?.user_id ? tokenData.data.user_id : 
                      tokenData?.sub ? tokenData.sub : null;
  
  useEffect(() => {
    if (currentUserId) {
      console.log('当前使用的用户ID:', currentUserId);
    } else if (user) {
      console.log('用户已登录但未获取到ID:', user);
    }
  }, [currentUserId, user]);

  // 获取访问令牌
  const getAccessToken = (): string | null => {
    return clientUserContextManager.getAuthToken();
  };

  // 获取请求头
  const getAuthHeaders = () => {
    const headers = clientUserContextManager.getAuthHeaders();
    
    if (currentUserId) {
      // 添加用户ID头，确保服务器知道请求来自哪个用户
      headers['X-User-ID'] = currentUserId;
    }
    
    return headers;
  };

  // 加载所有聊天历史
  const loadChatHistories = useCallback(async () => {
    // 防止并发加载请求
    if (isLoadingRef.current) {
      console.log('已有加载请求正在进行，跳过重复加载');
      return;
    }
    
    try {
      isLoadingRef.current = true;
      setLoading(true);
      setError(null);
      
      // 如果用户未登录或正在加载认证状态，则不加载聊天历史
      if (!isAuthenticated || authLoading) {
        setChatHistories([]);
        setLoading(false);
        isLoadingRef.current = false;
        return;
      }
      
      if (!currentUserId) {
        console.error('Unable to load chat history: user ID not found');
        setError('Unable to load chat history: user ID not found');
        setChatHistories([]);
        setLoading(false);
        isLoadingRef.current = false;
        return;
      }
      
      console.log('正在加载用户聊天历史，用户ID:', currentUserId);
      
      const { data, error: apiError, status } = await api.get<ChatHistory[]>('/api/chat-history', {
        headers: { 'X-User-ID': currentUserId }
      });
      
      // 处理响应
      if (apiError) {
        // 如果是401错误，可能已自动处理过token刷新
        if (status === 401) {
          setChatHistories([]);
          console.log('认证失败，可能需要重新登录');
        }
        throw new Error(apiError);
      }
      
      const histories = data || [];
      console.log(`已加载 ${histories.length} 条聊天历史记录`);
      setChatHistories(histories);
    } catch (err) {
      console.error('加载聊天历史失败:', err);
      setError('加载聊天历史失败');
    } finally {
      setLoading(false);
      isLoadingRef.current = false;
    }
  }, [isAuthenticated, authLoading, currentUserId]);

  // 选择特定聊天
  const selectChat = useCallback(async (id: string): Promise<ChatHistory | null> => {
    // 如果已经选中了该聊天，则不重复加载
    if (id === selectedChatId && chatHistories.some(chat => chat.id === id)) {
      console.log('已经选中该聊天，跳过重复加载:', id);
      return chatHistories.find(chat => chat.id === id) || null;
    }
    
    // 防止并发加载请求
    if (isLoadingRef.current) {
      console.log('已有加载请求正在进行，延迟选择聊天');
      // 等待当前加载完成后再选择
      setTimeout(() => selectChat(id), 100);
      return null;
    }
    
    try {
      isLoadingRef.current = true;
      setLoading(true);
      setError(null);
      
      // 如果用户未登录，则不加载聊天详情
      if (!isAuthenticated || authLoading) {
        isLoadingRef.current = false;
        return null;
      }
      
      if (!currentUserId) {
        console.error('未获取到用户ID，无法选择聊天');
        setError('未获取到用户ID');
        isLoadingRef.current = false;
        return null;
      }
      
      console.log('选择聊天:', id, '用户ID:', currentUserId);
      
      const { data, error: apiError } = await api.get<ChatHistory>(`/api/chat-history?id=${id}`, {
        headers: { 'X-User-ID': currentUserId }
      });
      
      if (apiError) {
        throw new Error(apiError);
      }
      
      if (data) {
        setSelectedChatId(id);
        return data;
      }
      return null;
    } catch (err) {
      console.error('选择聊天失败:', err);
      setError('选择聊天失败');
      return null;
    } finally {
      setLoading(false);
      isLoadingRef.current = false;
    }
  }, [isAuthenticated, authLoading, currentUserId, selectedChatId, chatHistories]);

  // 创建新聊天
  const createNewChat = useCallback(() => {
    console.log("创建新聊天: 完全重置状态");
    
    // 重置选中的聊天ID
    setSelectedChatId(null);
    
    // 确保UI状态也被重置
    setLoading(false);
    setError(null);
  }, []);

  // 保存当前聊天
  const saveCurrentChat = useCallback(async (messages: Message[], model: string): Promise<ChatHistory> => {
    try {
      setLoading(true);
      setError(null);
      
      // 如果用户未登录，则不保存聊天
      if (!isAuthenticated || authLoading) {
        throw new Error('用户未登录或会话已过期');
      }
      
      if (!currentUserId) {
        console.error('未获取到用户ID，无法保存聊天');
        throw new Error('未获取到用户ID');
      }
      
      console.log('保存聊天使用的用户ID:', currentUserId);
      
      // 确定是否是新对话或现有对话
      const isNewChat = !selectedChatId;
      
      // 生成标题
      let title = 'New Chat';
      if (messages.length > 0) {
        const firstUserMessage = messages.find(m => m.role === 'user');
        if (firstUserMessage) {
          const content = typeof firstUserMessage.content === 'string' 
            ? firstUserMessage.content 
            : JSON.stringify(firstUserMessage.content);
          title = generateChatTitle(content);
        }
      }
      
      const now = new Date().toISOString();
      const chatData = {
        id: selectedChatId,
        title,
        messages,
        model,
        createdAt: isNewChat ? now : undefined,
        updatedAt: now,
        userId: currentUserId // 确保始终有用户ID
      };
      
      console.log('正在发送保存聊天请求...');
      
      const { data, error: apiError } = await api.post<ChatHistory>('/api/chat-history', chatData, {
        headers: { 'X-User-ID': currentUserId }
      });
      
      if (apiError) {
        throw new Error(apiError);
      }
      
      if (!data) {
        throw new Error('保存聊天失败：服务器未返回数据');
      }
      
      const savedChat = data;
      console.log('聊天已保存:', savedChat.id, '用户:', savedChat.userId);
      
      // 立即更新选定的聊天ID
      if (isNewChat) {
        console.log("设置新的聊天ID:", savedChat.id);
        // 强制立即更新selectedChatId，确保UI能立即反映更改
        setSelectedChatId(savedChat.id);
      }
      
      // 强制更新本地聊天历史列表
      setChatHistories(prevHistories => {
        let newHistories;
        // 检查是否已存在该聊天
        const exists = prevHistories.some(chat => chat.id === savedChat.id);
        if (exists) {
          // 如果已存在，更新该聊天
          newHistories = prevHistories.map(chat => 
            chat.id === savedChat.id ? savedChat : chat
          );
        } else {
          // 如果不存在，添加到列表开头
          newHistories = [savedChat, ...prevHistories];
        }
        console.log("更新后的聊天历史列表:", newHistories.length, "条记录");
        return newHistories;
      });
      
      return savedChat;
    } catch (err) {
      console.error('保存聊天失败:', err);
      setError('保存聊天失败');
      throw err;
    } finally {
      setLoading(false);
    }
  }, [selectedChatId, isAuthenticated, authLoading, currentUserId]);

  // 删除聊天
  const deleteSelectedChat = useCallback(async (id: string): Promise<boolean> => {
    try {
      setLoading(true);
      setError(null);
      
      // 如果用户未登录，则不删除聊天
      if (!isAuthenticated || authLoading) {
        throw new Error('用户未登录或会话已过期');
      }
      
      if (!currentUserId) {
        console.error('未获取到用户ID，无法删除聊天');
        throw new Error('未获取到用户ID');
      }
      
      console.log('删除聊天:', id, '用户ID:', currentUserId);
      
      const { error: apiError } = await api.delete(`/api/chat-history?id=${id}`, {
        headers: { 'X-User-ID': currentUserId }
      });
      
      if (apiError) {
        throw new Error(apiError);
      }
      
      // 如果正在查看的聊天被删除，清除选择
      if (selectedChatId === id) {
        setSelectedChatId(null);
      }
      
      // 从本地状态中删除聊天
      setChatHistories(prevHistories => 
        prevHistories.filter(chat => chat.id !== id)
      );
      
      return true;
    } catch (err) {
      console.error('删除聊天失败:', err);
      setError('删除聊天失败');
      return false;
    } finally {
      setLoading(false);
    }
  }, [selectedChatId, isAuthenticated, authLoading, currentUserId]);

  // 更新聊天标题
  const updateChatTitle = useCallback(async (id: string, newTitle: string): Promise<boolean> => {
    try {
      setLoading(true);
      setError(null);
      
      // 如果用户未登录，则不更新聊天标题
      if (!isAuthenticated || authLoading) {
        throw new Error('用户未登录或会话已过期');
      }
      
      if (!currentUserId) {
        console.error('未获取到用户ID，无法更新聊天标题');
        throw new Error('未获取到用户ID');
      }
      
      console.log('更新聊天标题:', id, newTitle, '用户ID:', currentUserId);
      
      const { data, error: apiError } = await api.patch<ChatHistory>('/api/chat-history', 
        { id, title: newTitle },
        { headers: { 'X-User-ID': currentUserId } }
      );
      
      if (apiError) {
        throw new Error(apiError);
      }
      
      if (!data) {
        throw new Error('更新聊天标题失败：服务器未返回数据');
      }
      
      const updatedChat = data;
      
      // 更新本地聊天历史列表
      setChatHistories(prevHistories => 
        prevHistories.map(chat => 
          chat.id === id ? updatedChat : chat
        )
      );
      
      return true;
    } catch (err) {
      console.error('更新聊天标题失败:', err);
      setError('更新聊天标题失败');
      return false;
    } finally {
      setLoading(false);
    }
  }, [isAuthenticated, authLoading, currentUserId]);

  // 当认证状态变化时，重新加载聊天历史 - 修复重复加载问题
  useEffect(() => {
    // 只有当认证状态加载完成且用户已登录且初始加载还未完成时，才加载聊天历史
    if (!authLoading && isAuthenticated && currentUserId && !initialLoadDoneRef.current) {
      console.log('用户已登录，加载聊天历史. 用户ID:', currentUserId);
      initialLoadDoneRef.current = true; // 标记初始加载已完成
      loadChatHistories();
    } else if (!authLoading && !isAuthenticated) {
      // 如果用户未登录，清空聊天历史
      console.log('用户未登录，清空聊天历史');
      setChatHistories([]);
      setSelectedChatId(null);
      initialLoadDoneRef.current = false; // 重置初始加载标记
    }
  }, [loadChatHistories, isAuthenticated, authLoading, currentUserId]);

  return {
    chatHistories,
    loading,
    error,
    selectedChatId,
    loadChatHistories,
    selectChat,
    createNewChat,
    saveCurrentChat,
    deleteChat: deleteSelectedChat,
    updateChatTitle
  };
} 