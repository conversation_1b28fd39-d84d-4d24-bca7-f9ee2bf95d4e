{"openapi": "3.0.1", "info": {"title": "WMS", "description": "", "version": "1.0.0"}, "tags": [], "paths": {"/mdm/customer/search-by-paging": {"post": {"summary": "Search Customer by Paging", "deprecated": false, "description": "", "tags": [], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CustomerQuery", "description": ""}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RPageResultCustomerDto", "description": ""}, "examples": {"1": {"summary": "成功示例", "value": {"code": 0, "msg": "", "success": false, "data": {"list": [{"id": "", "orgId": "", "customerCode": "", "name": "", "fullName": "", "createdTime": "", "updatedTime": "", "createdBy": "", "updatedBy": "", "customerSetting": {"masterDataSetting": {"ssccSetting": {"companyPrefix": "", "maxCompanyPrefixLength": 0, "maxSequenceLength": 0, "retailerIds": [""], "uccType": ""}}, "inboundSetting": {"notAllowForceCloseForReceiving": false}, "outboundSetting": {"allowPartialLockInventory": false}}}], "totalCount": 0, "currentPage": 0, "pageSize": 0, "totalPage": 0}}}}}}, "headers": {}}}, "security": []}}, "/mdm/facility/search-by-paging": {"post": {"summary": "Search Facility by Paging", "deprecated": false, "description": "", "tags": [], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FacilityQuery", "description": ""}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RPageResultFacilityDto", "description": ""}, "examples": {"1": {"summary": "成功示例", "value": {"code": 0, "msg": "", "success": false, "data": {"list": [{"id": "", "locationMasterId": "", "facilityCode": "", "name": "", "codes": [""], "accountingCode": "", "facilityNo": "", "type": "", "status": "", "timeZone": "", "address": {"name": "", "country": "", "state": "", "city": "", "zipCode": "", "fax": "", "address1": "", "address2": "", "contact": "", "phone": "", "extension": "", "email": "", "lat": "", "lng": ""}, "facilitySetting": {"defaultTaskAssignments": [{"assiganmentTaskType": "", "userRoleIds": [""], "userTagIds": [""], "customerIds": [""], "userIds": [""]}]}, "isCreatedByExternal": false, "createdTime": "", "createdBy": "", "updatedTime": "", "updatedBy": ""}], "totalCount": 0, "currentPage": 0, "pageSize": 0, "totalPage": 0}}}}}}, "headers": {}}}, "security": []}}, "/mdm/organization/search-by-paging": {"post": {"summary": "searchByPaging", "deprecated": false, "description": "", "tags": [], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrganizationSearch", "description": ""}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RPageResultOrganizationDto"}, "example": {"code": 0, "msg": "", "success": false, "data": {"list": [{"id": "", "crmMasterId": "", "name": "", "fullName": "", "status": "", "customerCode": "", "customerType": "", "category": "", "tags": [""], "accountId": "", "source": "", "isValid": false, "note": "", "contacts": [{"name": "", "email": "", "phone": "", "note": "", "isValid": false, "type": ""}], "activatedFacilityIds": [""], "isCreatedByExternal": false, "createdTime": "", "updatedTime": "", "createdBy": "", "updatedBy": ""}], "totalCount": 0, "currentPage": 0, "pageSize": 0, "totalPage": 0}}}}, "headers": {}}}, "security": [{"oauth21": ["profile", "email", "phone", "openid"]}]}}}, "components": {"schemas": {"MasterDataSetting": {"type": "object", "properties": {"ssccSettings": {"type": "array", "items": {"$ref": "#/components/schemas/SsccSetting", "description": "com.item.mdm.domain.customer.entity.customersetting.SsccSetting"}, "description": ""}, "facilityLPLabelSizeSettings": {"type": "array", "items": {"$ref": "#/components/schemas/FacilityLPLabelSizeSetting", "description": "com.item.mdm.domain.customer.entity.customersetting.FacilityLPLabelSizeSetting"}, "description": ""}, "companyPrefix": {"type": "string", "description": ""}, "holidays": {"type": "array", "items": {"$ref": "#/components/schemas/Holiday", "description": "com.item.mdm.domain.company.entity.Holiday"}, "description": ""}, "defaultSerialNoValidationRegex": {"type": "string", "description": ""}}}, "Holiday": {"type": "object", "properties": {"date": {"type": "string", "description": ""}, "description": {"type": "string", "description": ""}}}, "FacilityLPLabelSizeSetting": {"type": "object", "properties": {"facilityId": {"type": "string", "description": ""}, "lpLabelSize": {"type": "string", "description": "", "enum": ["TOW_ONE", "FOUR_SIX"]}}}, "SsccSetting": {"type": "object", "properties": {"facilityId": {"type": "string", "description": ""}, "companyPrefix": {"type": "string", "description": ""}, "extensionCode": {"type": "string", "description": ""}, "maxCompanyPrefixLength": {"type": "integer", "description": ""}, "maxSequenceLength": {"type": "integer", "description": ""}, "retailerIds": {"type": "array", "items": {"type": "string"}, "description": ""}, "uccType": {"type": "string", "description": ""}}}, "CustomerQuery": {"type": "object", "properties": {"currentPage": {"type": "integer", "description": ""}, "pageSize": {"type": "integer", "description": ""}, "sortingFields": {"type": "array", "items": {"$ref": "#/components/schemas/SortingField", "description": "com.item.xms.persistence.query.SortingField"}, "description": ""}, "id": {"type": "string", "description": ""}, "ids": {"type": "array", "items": {"type": "string"}, "description": ""}, "orgId": {"type": "string", "description": ""}, "orgIds": {"type": "array", "items": {"type": "string"}, "description": ""}, "customerCode": {"type": "string", "description": ""}, "source": {"type": "string", "description": ""}, "customerName": {"type": "string", "description": ""}, "customerFullName": {"type": "string", "description": ""}, "createdTimeFrom": {"type": "string", "description": ""}, "createdTimeTo": {"type": "string", "description": ""}, "updatedTimeFrom": {"type": "string", "description": ""}, "updatedTimeTo": {"type": "string", "description": ""}, "createdBy": {"type": "string", "description": ""}, "updatedBy": {"type": "string", "description": ""}, "autoUpdateExpirationDate": {"type": "boolean", "description": ""}, "holdInventoryBeforeShipAllowDays": {"type": "boolean", "description": ""}, "billingPay": {"type": "boolean", "description": ""}, "billingStack": {"type": "string", "description": "", "enum": ["ITEM", "LOCATION", "ITEM_LOCATION", "LOCATION_ITEM"]}}}, "SortingField": {"type": "object", "properties": {"field": {"type": "string", "description": ""}, "orderBy": {"type": "string", "description": "", "enum": ["NONE", "ASC", "DESC"]}}}, "InboundSetting": {"type": "object", "properties": {"receiptUniqueKeys": {"type": "array", "items": {"type": "string"}, "description": "", "default": "new ArrayList<>(Arrays.asList(\"poNo\"))"}, "putAwayLocationTypes": {"type": "array", "items": {"type": "string", "enum": ["CUSTOMER_VLG", "ITEM_VLG", "ITEM_GROUP_VLG", "TITLE_VLG"]}, "description": ""}, "putAwayStrategies": {"type": "array", "items": {"type": "string", "enum": ["SAME_ITEM", "SAME_LOT_NO", "SAME_EXPIRATION_DATE", "SAME_RN_AND_EXPIRATION_DATE"]}, "description": ""}, "skipPutAwayOnReceive": {"type": "boolean", "description": ""}, "allowedReceivingGoodsTypes": {"type": "array", "items": {"type": "string"}, "description": ""}, "notSendReceiveConfirmationResources": {"type": "array", "items": {"type": "string", "enum": ["OMS", "PUBLIC_API", "MANUAL"]}, "description": ""}, "receiveQtyChecks": {"type": "array", "items": {"$ref": "#/components/schemas/ReceiveQtyCheck", "description": "com.item.mdm.domain.customer.entity.ReceiveQtyCheck"}, "description": ""}, "allowReceiveLocationTypes": {"type": "array", "items": {"type": "string"}, "description": ""}, "enableSnCheckByPreProvidedSnFile": {"type": "boolean", "description": ""}, "allowReceiveSNNotInPreProvidedSnFile": {"type": "boolean", "description": ""}, "enableSNCheckWithoutSupplierAndFacility": {"type": "boolean", "description": ""}, "enableSNCheckWithoutContainer": {"type": "boolean", "description": ""}, "preProvidedSnCheckSupplierFacilities": {"type": "array", "items": {"$ref": "#/components/schemas/SupplierFacilitySetting", "description": "com.item.mdm.domain.customer.entity.customersetting.SupplierFacilitySetting"}, "description": ""}, "allowAutoCloseReceipt": {"type": "boolean", "description": ""}, "putAwayMode": {"type": "string", "description": "", "enum": ["AFTER_RECEIVING", "DURING_RECEIVING"]}, "receiptCloseTiming": {"type": "string", "description": "", "enum": ["AFTER_RECEIVING", "AFTER_PUT_AWAY"]}, "rcCartonLevel": {"type": "string", "description": "", "enum": ["SN", "PALLET"]}, "inventoryStatusControl": {"type": "string", "description": "", "enum": ["AVAILABLE_AFTER_PUT_AWAY", "AVAILABLE_AFTER_RECEIPT_CLOSE"]}, "allowManualPutAwayByRN": {"type": "boolean", "description": ""}, "allowPutAwayWithExceptionReceipt": {"type": "boolean", "description": ""}, "displayStackHighAtPutAwayTask": {"type": "boolean", "description": ""}, "forbidCloseReceiveTaskBeforePutAway": {"type": "boolean", "description": ""}, "allowReceiveMethods": {"type": "array", "items": {"type": "string", "enum": ["RECEIVE_BY_SINGLE_ITEM", "RECEIVE_TO_PICK_LOCATION", "RECEIVE_TO_PUT_AWAY", "RECEIVE_BY_PALLET", "RECEIVE_BY_MIX_ITEM", "RECEIVE_BY_CARTON"]}, "description": ""}, "forceDefaultUOMReceiving": {"type": "boolean", "description": "", "default": false}}}, "SupplierFacilitySetting": {"type": "object", "properties": {"facilityId": {"type": "string", "description": ""}, "supplierId": {"type": "string", "description": ""}}}, "ReceiveQtyCheck": {"type": "object", "properties": {"receiptType": {"type": "string", "description": "", "enum": ["REGULAR_RECEIPT", "TITLE_TRANSFER_RECEIPT", "MIGO_TRANSFER_RECEIPT", "INTERNAL_TRANSFER_RECEIVING", "TRANSLOAD", "MATERIAL_PURCHASE"]}, "allowShortReceive": {"type": "boolean", "description": ""}, "notAllowOverReceive": {"type": "boolean", "description": ""}, "allowPartialReceive": {"type": "boolean", "description": ""}}}, "InventorySetting": {"type": "object", "properties": {"autoUpdateExpirationDate": {"type": "boolean", "description": ""}, "holdInventoryBeforeShipAllowDays": {"type": "boolean", "description": ""}}}, "RPageResultCustomerDto": {"type": "object", "properties": {"code": {"type": "integer", "description": ""}, "msg": {"type": "string", "description": ""}, "success": {"type": "boolean", "description": ""}, "data": {"$ref": "#/components/schemas/PageResultCustomerDto", "description": ""}}}, "PageResultCustomerDto": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/components/schemas/CustomerDto", "description": "com.item.mdm.application.customer.dto.CustomerDto"}, "description": ""}, "totalCount": {"type": "integer", "description": ""}, "currentPage": {"type": "integer", "description": ""}, "pageSize": {"type": "integer", "description": ""}, "totalPage": {"type": "integer", "description": ""}}}, "CustomerDto": {"type": "object", "properties": {"id": {"type": "string", "description": ""}, "orgId": {"type": "string", "description": ""}, "customerCode": {"type": "string", "description": ""}, "name": {"type": "string", "description": ""}, "fullName": {"type": "string", "description": ""}, "createdTime": {"type": "string", "description": ""}, "updatedTime": {"type": "string", "description": ""}, "createdBy": {"type": "string", "description": ""}, "updatedBy": {"type": "string", "description": ""}, "masterDataSetting": {"$ref": "#/components/schemas/MasterDataSetting", "description": ""}, "inboundSetting": {"$ref": "#/components/schemas/InboundSetting", "description": ""}, "outboundSetting": {"description": "", "type": "object", "properties": {}}, "inventorySetting": {"$ref": "#/components/schemas/InventorySetting", "description": ""}, "billingPaySetting": {"$ref": "#/components/schemas/BillingPaySetting", "description": ""}, "dynamicFieldMapping": {"$ref": "#/components/schemas/DynamicFieldMapping", "description": ""}}}, "DynamicFieldMapping": {"type": "object", "properties": {"orderDynamicFieldMapping": {"type": "array", "items": {"$ref": "#/components/schemas/DynamicFieldMappingItem", "description": "com.item.mdm.domain.customer.entity.customersetting.DynamicFieldMappingItem"}, "description": ""}, "orderItemLineDynamicFieldMapping": {"type": "array", "items": {"$ref": "#/components/schemas/DynamicFieldMappingItem", "description": "com.item.mdm.domain.customer.entity.customersetting.DynamicFieldMappingItem"}, "description": ""}, "receiptDynamicFieldMapping": {"type": "array", "items": {"$ref": "#/components/schemas/DynamicFieldMappingItem", "description": "com.item.mdm.domain.customer.entity.customersetting.DynamicFieldMappingItem"}, "description": ""}, "receiptItemLineDynamicFieldMapping": {"type": "array", "items": {"$ref": "#/components/schemas/DynamicFieldMappingItem", "description": "com.item.mdm.domain.customer.entity.customersetting.DynamicFieldMappingItem"}, "description": ""}}}, "DynamicFieldMappingItem": {"type": "object", "properties": {"dynamicField": {"type": "string", "description": ""}, "dynamicName": {"type": "string", "description": ""}}}, "BillingPaySetting": {"type": "object", "properties": {"billingPay": {"type": "boolean", "description": ""}, "billingStack": {"type": "string", "description": "", "enum": ["ITEM", "LOCATION", "ITEM_LOCATION", "LOCATION_ITEM"]}}}, "FacilityQuery": {"type": "object", "properties": {"currentPage": {"type": "integer", "description": ""}, "pageSize": {"type": "integer", "description": ""}, "sortingFields": {"type": "array", "items": {"$ref": "#/components/schemas/SortingField", "description": "com.item.xms.persistence.query.SortingField"}, "description": ""}, "id": {"type": "string", "description": ""}, "ids": {"type": "array", "items": {"type": "string"}, "description": ""}, "tenantId": {"type": "string", "description": ""}, "name": {"type": "string", "description": ""}, "nameRegex": {"type": "string", "description": ""}, "codes": {"type": "array", "items": {"type": "string"}, "description": ""}, "accountingCode": {"type": "string", "description": ""}, "facilityNo": {"type": "string", "description": ""}, "locationMasterId": {"type": "integer", "description": ""}, "locationMasterTerminalId": {"type": "integer", "description": ""}, "locationMasterTerminalCode": {"type": "string", "description": ""}, "locationMasterTerminalCodes": {"type": "array", "items": {"type": "string"}, "description": ""}, "type": {"type": "string", "description": "", "enum": ["PHYSICAL", "VIRTUAL", "STORE"]}, "status": {"type": "string", "description": "", "enum": ["ENABLE", "DISABLE"]}, "timeZone": {"type": "string", "description": ""}, "createdBy": {"type": "string", "description": ""}, "createdTimeFrom": {"type": "string", "description": ""}, "createdTimeTo": {"type": "string", "description": ""}, "updatedBy": {"type": "string", "description": ""}, "updatedTimeFrom": {"type": "string", "description": ""}, "updatedTimeTo": {"type": "string", "description": ""}, "wiseAccessUrl": {"type": "string", "description": ""}}}, "OutboundSetting": {"type": "object", "properties": {"allowPartialLockInventory": {"type": "boolean", "description": ""}, "autoTriggerReplenishInOrderPlan": {"type": "boolean", "description": ""}, "orderUniqueKeys": {"type": "array", "items": {"type": "string"}, "description": "", "default": "List.of(\"referenceNo\")"}, "rateShoppingSetting": {"$ref": "#/components/schemas/RateShoppingSetting", "description": ""}, "pickToWallSetting": {"$ref": "#/components/schemas/PickToWallSetting", "description": ""}, "opportunityPickSetting": {"$ref": "#/components/schemas/OpportunityPickSetting", "description": ""}, "allowPartialShippedForDSOrder": {"type": "boolean", "description": ""}, "allowPartialShippedForRGOrder": {"type": "boolean", "description": ""}, "allowShortShippedForDSOrder": {"type": "boolean", "description": ""}, "allowShortShippedForRGOrder": {"type": "boolean", "description": ""}, "requirePrintLabelBeforeCloseOrder": {"type": "boolean", "description": ""}, "pickStrategyRule": {"$ref": "#/components/schemas/MapListString", "description": ""}, "timeWindow": {"type": "string", "description": "", "enum": ["MONTHLY", "YEARLY", "DAILY", "WEEKLY"]}, "shipAllowDays": {"type": "integer", "description": ""}, "requirePrintLabelBeforeCloseOrderSettings": {"type": "array", "items": {"$ref": "#/components/schemas/RequirePrintLabelBeforeCloseOrderSetting", "description": "com.item.mdm.domain.customer.entity.customersetting.RequirePrintLabelBeforeCloseOrderSetting"}, "description": ""}, "shipmentTrackingType": {"type": "string", "description": "", "enum": ["LP_LEVEL", "ORDER_LEVEL"]}, "datePriorityOfDropShipOrder": {"type": "array", "items": {"type": "string"}, "description": "", "default": "Lists.newArrayList(\"SD\", \"SNL\", \"CT\")"}, "datePriorityOfRegularOrder": {"type": "array", "items": {"type": "string"}, "description": "", "default": "Lists.newArrayList(\"AD\", \"SNL\", \"RDD\")"}, "applyShipNotBeforeOfDropShipOrder": {"type": "boolean", "description": "", "default": true}, "applyShipNotBeforeOfRegularOrder": {"type": "boolean", "description": "", "default": true}, "skipStageSetting": {"$ref": "#/components/schemas/SkipStageSetting", "description": ""}, "allowAutoCC": {"type": "boolean", "description": "", "default": false}, "skipCollectingRFIDForDropshipOrder": {"type": "boolean", "description": "", "default": false}, "allowDuplicateInLPSNCollection": {"type": "boolean", "description": "", "default": false}, "forceUseLPTemplate": {"type": "boolean", "description": "", "default": false}, "enablePickByLotNo": {"type": "boolean", "description": "", "default": false}, "forceCarryOverOrderBOLNoteToLoadNote": {"type": "boolean", "description": "", "default": false}, "defaultShipInfos": {"type": "array", "items": {"$ref": "#/components/schemas/DefaultShipInfo", "description": "com.item.mdm.domain.customer.entity.customersetting.DefaultShipInfo"}, "description": ""}, "retailerShipInfos": {"type": "array", "items": {"$ref": "#/components/schemas/RetailerShipInfo", "description": "com.item.mdm.domain.customer.entity.customersetting.RetailerShipInfo"}, "description": ""}, "rollbackToException": {"type": "boolean", "description": "", "default": false}, "ensurePutBackFinishedBeforeRollbackOrder": {"type": "boolean", "description": "", "default": false}, "route856s": {"type": "array", "items": {"$ref": "#/components/schemas/Route856", "description": "com.item.mdm.domain.customer.entity.customersetting.Route856"}, "description": ""}, "retailerPalletTypeSetting": {"$ref": "#/components/schemas/RetailerPalletTypeSetting", "description": ""}, "bolGenerateOptions": {"type": "array", "items": {"$ref": "#/components/schemas/BolGenerateOption", "description": "com.item.mdm.domain.customer.entity.customersetting.BolGenerateOption"}, "description": ""}}}, "BolGenerateOption": {"type": "object", "properties": {"bolPrintRuleApplyToAllRetailer": {"type": "boolean", "description": ""}, "bolPrintRuleApplyToRetailers": {"type": "array", "items": {"type": "string"}, "description": ""}, "bolPrintLevelOptions": {"type": "array", "items": {"type": "string", "enum": ["PRINT_BOL_BY_LOAD", "PRINT_BOL_BY_SHIP_TO", "PRINT_BOL_BY_ORDER"]}, "description": ""}, "masterBolNoLevel": {"type": "string", "description": "", "enum": ["MASTER_BOL_ON_LOAD_LEVEL", "MASTER_BOL_ON_SHIP_TO_LEVEL"]}, "copyMasterBolToOrderBol": {"type": "boolean", "description": ""}, "bolNumberPrefix": {"type": "string", "description": ""}}}, "RetailerPalletTypeSetting": {"type": "object", "properties": {"suggestPalletTypeOnLoad": {"type": "boolean", "description": ""}, "retailerPalletTypes": {"type": "array", "items": {"$ref": "#/components/schemas/RetailerPalletType", "description": "com.item.mdm.domain.customer.entity.customersetting.RetailerPalletType"}, "description": ""}}}, "RetailerPalletType": {"type": "object", "properties": {"retailerIds": {"type": "array", "items": {"type": "string"}, "description": ""}, "itemIds": {"type": "array", "items": {"type": "string"}, "description": ""}}}, "Route856": {"type": "object", "properties": {"retailerId": {"type": "string", "description": ""}, "shipToStoreNos": {"type": "array", "items": {"type": "string"}, "description": ""}}}, "RetailerShipInfo": {"type": "object", "properties": {"retailerId": {"type": "string", "description": ""}, "shipperNameType": {"type": "string", "description": "", "enum": ["ORDER_SHIP_FROM_NAME", "RETAILER_NAME", "FREE_TEXT"]}, "shipperAddressType": {"type": "string", "description": "", "enum": ["RETAILER_ADDRESS", "FACILITY_ADDRESS", "ORDER_SHIP_FROM_ADDRESS", "OTHERS_ADDRESS"]}, "retailerAddressId": {"type": "integer", "description": ""}, "othersAddressId": {"type": "integer", "description": ""}, "appliedFacilityIds": {"type": "array", "items": {"type": "string"}, "description": ""}, "shipFromName": {"type": "string", "description": ""}}}, "DefaultShipInfo": {"type": "object", "properties": {"appliedFacilityIds": {"type": "array", "items": {"type": "string"}, "description": ""}, "appliedRetailerIds": {"type": "array", "items": {"type": "string"}, "description": ""}, "defaultShipFromName": {"type": "string", "description": ""}, "defaultShipFromCompany": {"type": "string", "description": ""}, "defaultShipFromPhone": {"type": "string", "description": ""}, "applyToDocTypes": {"type": "array", "items": {"type": "string"}, "description": ""}}}, "SkipStageSetting": {"type": "object", "properties": {"enableSkipStage": {"type": "boolean", "description": "", "default": false}, "skipOrderTypes": {"type": "array", "items": {"type": "string"}, "description": ""}}}, "RequirePrintLabelBeforeCloseOrderSetting": {"type": "object", "properties": {"applyToOrderTypes": {"type": "array", "items": {"type": "string", "enum": ["RG", "TT", "MT", "DS", "ITO", "TRANSLOAD"]}, "description": ""}, "applyToSpecialRetailer": {"type": "boolean", "description": ""}, "specialRetailers": {"type": "array", "items": {"type": "string"}, "description": ""}, "requirePrintUccLabel": {"type": "boolean", "description": ""}, "requirePrintPackingList": {"type": "boolean", "description": ""}}}, "MapListString": {"type": "object", "properties": {"key": {"type": "array", "items": {"type": "string"}}}}, "OpportunityPickSetting": {"type": "object", "properties": {"enableOpportunityPick": {"type": "boolean", "description": ""}, "vlgIds": {"type": "array", "items": {"type": "string"}, "description": ""}}}, "PickToWallSetting": {"type": "object", "properties": {"enablePickToWall": {"type": "boolean", "description": ""}, "vlgIds": {"type": "array", "items": {"type": "string"}, "description": ""}}}, "RateShoppingSetting": {"type": "object", "properties": {"allowRateShopping": {"type": "boolean", "description": ""}, "allowRateShoppingRetailerIds": {"type": "array", "items": {"type": "string"}, "description": ""}, "rateShoppingGroups": {"type": "array", "items": {"$ref": "#/components/schemas/RateShoppingGroup", "description": "com.item.mdm.domain.customer.entity.RateShoppingGroup"}, "description": ""}}}, "RateShoppingGroup": {"type": "object", "properties": {"deliveryService": {"type": "string", "description": ""}, "carrierProvider": {"type": "string", "description": ""}, "carrierServiceCode": {"type": "string", "description": ""}, "shipperAccount": {"type": "string", "description": ""}, "applyToFacilities": {"type": "array", "items": {"type": "string"}, "description": ""}}}, "RPageResultFacilityDto": {"type": "object", "properties": {"code": {"type": "integer", "description": ""}, "msg": {"type": "string", "description": ""}, "success": {"type": "boolean", "description": ""}, "data": {"$ref": "#/components/schemas/PageResultFacilityDto", "description": ""}}}, "PageResultFacilityDto": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/components/schemas/FacilityDto", "description": "com.item.mdm.application.facility.dto.FacilityDto"}, "description": ""}, "totalCount": {"type": "integer", "description": ""}, "currentPage": {"type": "integer", "description": ""}, "pageSize": {"type": "integer", "description": ""}, "totalPage": {"type": "integer", "description": ""}}}, "FacilityDto": {"type": "object", "properties": {"id": {"type": "string", "description": ""}, "locationMasterId": {"type": "integer", "description": ""}, "locationMasterTerminalId": {"type": "integer", "description": ""}, "locationMasterTerminalCode": {"type": "string", "description": ""}, "facilityCode": {"type": "string", "description": ""}, "name": {"type": "string", "description": ""}, "codes": {"type": "array", "items": {"type": "string"}, "description": ""}, "accountingCode": {"type": "string", "description": ""}, "facilityNo": {"type": "string", "description": ""}, "type": {"type": "string", "description": "", "enum": ["PHYSICAL", "VIRTUAL", "STORE"]}, "status": {"type": "string", "description": "", "enum": ["ENABLE", "DISABLE"]}, "timeZone": {"type": "string", "description": ""}, "address": {"$ref": "#/components/schemas/Address", "description": ""}, "facilitySetting": {"$ref": "#/components/schemas/FacilitySetting", "description": ""}, "isCreatedByExternal": {"type": "boolean", "description": ""}, "lpPrefix": {"type": "string", "description": ""}, "createdTime": {"type": "string", "description": ""}, "createdBy": {"type": "string", "description": ""}, "updatedTime": {"type": "string", "description": ""}, "updatedBy": {"type": "string", "description": ""}, "wiseAccessUrl": {"type": "string", "description": ""}}}, "FacilitySetting": {"type": "object", "properties": {"defaultTaskAssignments": {"type": "array", "items": {"$ref": "#/components/schemas/DefaultTaskAssignment", "description": ""}, "description": ""}, "lpLabelSize": {"type": "string", "description": "", "enum": ["TOW_ONE", "FOUR_SIX"]}, "enableTaskPool": {"type": "boolean", "description": ""}}}, "DefaultTaskAssignment": {"type": "object", "properties": {"assiganmentTaskType": {"type": "string", "description": "", "enum": ["REPLENISHMENT"]}, "userRoleIds": {"type": "array", "items": {"type": "string"}, "description": ""}, "userTagIds": {"type": "array", "items": {"type": "string"}, "description": ""}, "customerIds": {"type": "array", "items": {"type": "string"}, "description": ""}, "userIds": {"type": "array", "items": {"type": "string"}, "description": ""}}}, "Address": {"type": "object", "properties": {"createdTime": {"type": "string", "description": ""}, "createdBy": {"type": "string", "description": ""}, "updatedTime": {"type": "string", "description": ""}, "updatedBy": {"type": "string", "description": ""}, "tenantId": {"type": "string", "description": ""}, "isolationId": {"type": "string", "description": ""}, "id": {"type": "integer", "description": ""}, "orgId": {"type": "string", "description": ""}, "shorthand": {"type": "string", "description": ""}, "tags": {"type": "array", "items": {"type": "string"}, "description": ""}, "name": {"type": "string", "description": ""}, "country": {"type": "string", "description": ""}, "address1": {"type": "string", "description": ""}, "address2": {"type": "string", "description": ""}, "state": {"type": "string", "description": ""}, "city": {"type": "string", "description": ""}, "zipCode": {"type": "string", "description": ""}, "fax": {"type": "string", "description": ""}, "storeNo": {"type": "string", "description": ""}, "contact": {"type": "string", "description": ""}, "phone": {"type": "string", "description": ""}, "email": {"type": "string", "description": ""}, "extension": {"type": "string", "description": ""}, "crmAddressId": {"type": "string", "description": ""}, "latitude": {"type": "string", "description": ""}, "longitude": {"type": "string", "description": ""}, "referenceNo": {"type": "string", "description": ""}, "batchCode": {"type": "string", "description": ""}, "toHome": {"type": "boolean", "description": ""}, "channel": {"type": "string", "description": "", "enum": ["EDI", "FTP", "INT_FTP", "WEB", "MANUAL", "UPLOAD", "ANDROID", "EXTERNAL_WMS", "CLIENT_PORTAL", "ITEM_MASTER", "DTS", "CONVEYOR_LINE", "PUBLIC_API", "CARRIER_WEB", "EXTERNAL_FMS", "AIR_ROB"]}}}, "Contact": {"type": "object", "properties": {"name": {"type": "string", "description": ""}, "email": {"type": "string", "description": ""}, "phone": {"type": "string", "description": ""}, "note": {"type": "string", "description": ""}, "isValid": {"type": "boolean", "description": ""}, "type": {"type": "string", "description": "", "enum": ["SALES_PRP", "ADMIN", "EDI_IT", "AR", "AP", "CLAIMS", "OWNER", "APPLICANT", "DEBT_COLLECTION", "PRIMARY", "ALERT_EMAIL", "SALES_REP", "OTHERS"]}}}, "OrganizationDto": {"type": "object", "properties": {"id": {"type": "string", "description": ""}, "crmMasterId": {"type": "string", "description": ""}, "name": {"type": "string", "description": ""}, "fullName": {"type": "string", "description": ""}, "status": {"type": "string", "description": "", "enum": ["EXCEPTION", "ACTIVE", "INACTIVE"]}, "customerCode": {"type": "string", "description": ""}, "customerType": {"type": "string", "description": "", "enum": ["COMPANY", "INDIVIDUAL"]}, "category": {"type": "string", "description": "", "enum": ["CUSTOMER", "INTERNAL_CUSTOMER"]}, "tags": {"type": "array", "items": {"type": "string", "enum": ["BRAND", "CUSTOMER", "RETAILER", "TITLE", "SUPPLIER", "HOST_ACCOUNT", "TENANT", "ASSET_OWNER", "MANUFACTURER"]}, "description": ""}, "accountId": {"type": "string", "description": ""}, "source": {"type": "string", "description": ""}, "isValid": {"type": "boolean", "description": ""}, "note": {"type": "string", "description": ""}, "contacts": {"type": "array", "items": {"$ref": "#/components/schemas/Contact", "description": "com.item.mdm.domain.organization.entity.Organization.Contact"}, "description": ""}, "activatedFacilityIds": {"type": "array", "items": {"type": "string"}, "description": ""}, "isCreatedByExternal": {"type": "boolean", "description": ""}, "createdTime": {"type": "string", "description": ""}, "updatedTime": {"type": "string", "description": ""}, "createdBy": {"type": "string", "description": ""}, "updatedBy": {"type": "string", "description": ""}}}, "OrganizationSearch": {"type": "object", "properties": {"currentPage": {"type": "integer", "description": ""}, "pageSize": {"type": "integer", "description": ""}, "sortingFields": {"type": "array", "items": {"$ref": "#/components/schemas/SortingField", "description": "com.item.xms.persistence.query.SortingField"}, "description": ""}, "id": {"type": "string", "description": ""}, "ids": {"type": "array", "items": {"type": "string"}, "description": ""}, "keyword": {"type": "string", "description": ""}, "tags": {"type": "array", "items": {"type": "string", "enum": ["BRAND", "CUSTOMER", "RETAILER", "TITLE", "SUPPLIER", "HOST_ACCOUNT", "TENANT", "ASSET_OWNER", "MANUFACTURER"]}, "description": ""}, "tag": {"type": "string", "description": "", "enum": ["BRAND", "CUSTOMER", "RETAILER", "TITLE", "SUPPLIER", "HOST_ACCOUNT", "TENANT", "ASSET_OWNER", "MANUFACTURER"]}, "nameRegex": {"type": "string", "description": ""}, "name": {"type": "string", "description": ""}, "status": {"type": "string", "description": "", "enum": ["EXCEPTION", "ACTIVE", "INACTIVE"]}, "statuses": {"type": "array", "items": {"type": "string", "enum": ["EXCEPTION", "ACTIVE", "INACTIVE"]}, "description": ""}, "excludeStatus": {"type": "string", "description": "", "enum": ["EXCEPTION", "ACTIVE", "INACTIVE"]}, "names": {"type": "array", "items": {"type": "string"}, "description": ""}, "customerCodeRegex": {"type": "string", "description": ""}, "customerCode": {"type": "string", "description": ""}, "customerCodes": {"type": "array", "items": {"type": "string"}, "description": ""}, "activatedFacilityIds": {"type": "array", "items": {"type": "string"}, "description": ""}}}, "RPageResultOrganizationDto": {"type": "object", "properties": {"code": {"type": "integer", "description": ""}, "msg": {"type": "string", "description": ""}, "success": {"type": "boolean", "description": ""}, "data": {"$ref": "#/components/schemas/PageResultOrganizationDto", "description": ""}}}, "PageResultOrganizationDto": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/components/schemas/OrganizationDto", "description": "<p></p>"}, "description": ""}, "totalCount": {"type": "integer", "description": ""}, "currentPage": {"type": "integer", "description": ""}, "pageSize": {"type": "integer", "description": ""}, "totalPage": {"type": "integer", "description": ""}}}}, "securitySchemes": {}}, "servers": []}