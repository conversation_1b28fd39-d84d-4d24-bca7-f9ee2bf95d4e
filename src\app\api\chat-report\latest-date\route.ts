import { NextRequest, NextResponse } from 'next/server';
import { getUserIdFromRequest } from '@/utils/authUtils';
import { listAllReports } from '@/chat-report/reportStorage';

// GET - Get the latest report date
export async function GET(req: NextRequest) {
  try {
    // Get the current user ID
    const userId = await getUserIdFromRequest(req);
    
    // If not logged in, return error
    if (!userId) {
      return NextResponse.json(
        { error: 'User not logged in or session expired' },
        { status: 401 }
      );
    }
    
    // Get all reports and find the latest date
    const reports = await listAllReports();
    
    let latestDate: string | null = null;
    if (reports.length > 0) {
      // Sort reports by date and get the latest one
      const sortedReports = [...reports].sort((a, b) => a.date.localeCompare(b.date));
      latestDate = sortedReports[sortedReports.length - 1].date;
    }
    
    return NextResponse.json({ latestDate });
  } catch (error) {
    console.error('Error getting latest report date:', error);
    return NextResponse.json(
      { error: 'Error getting latest report date' },
      { status: 500 }
    );
  }
}
