import { BaseTool } from './baseTool';
// import { CommonTools } from './commonTools'; // 注释掉其他工具
// import { WMSTools } from './wmsTools'; // 暂时注释掉 WMS 工具
import { WeatherTool } from './weatherTool'; // 引入天气工具
import { ToolDefinition, ToolCallParams, ToolCallOutput } from './types';

/**
 * 工具注册器
 * 负责管理所有工具类及其工具函数
 */
export class ToolRegistry {
  private static instance: ToolRegistry;
  private toolClasses: Map<string, BaseTool> = new Map();
  private toolDefinitions: Map<string, ToolDefinition> = new Map();
  private toolImplementations: Map<string, Function> = new Map();
  
  /**
   * 获取单例实例
   */
  public static getInstance(socket?: WebSocket): ToolRegistry {
    if (!ToolRegistry.instance) {
      ToolRegistry.instance = new ToolRegistry(socket);
    }
    return ToolRegistry.instance;
  }
  
  /**
   * 构造函数
   */
  private constructor(socket?: WebSocket) {
    console.log('ToolRegistry: Initializing...');
    
    // 只注册天气工具
    // this.registerToolClass(new CommonTools(socket)); // 注释掉其他工具
    // this.registerToolClass(new WMSTools()); // 暂时注释掉 WMS 工具
    this.registerToolClass(new WeatherTool()); // 注册天气工具
    
    console.log('ToolRegistry: Initialized with tools:', Array.from(this.toolClasses.keys()));
  }
  
  /**
   * 注册工具类
   */
  public registerToolClass(toolClass: BaseTool): void {
    const className = toolClass.constructor.name;
    this.toolClasses.set(className, toolClass);
    
    // 注册该类提供的所有工具
    const tools = toolClass.getTools();
    for (const tool of tools) {
      this.toolDefinitions.set(tool.name, tool);
      
      // 创建工具实现映射
      const toolName = tool.name;
      if (toolName === 'getWeather' && toolClass instanceof WeatherTool) {
        this.toolImplementations.set(toolName, async (location: string, unit?: 'celsius' | 'fahrenheit') => {
          return await (toolClass as WeatherTool).getWeather(location, unit);
        });
        console.log(`注册了工具: ${toolName}`);
      } else {
        console.warn(`工具 ${toolName} 没有对应的实现函数`);
      }
    }
  }
  
  /**
   * 获取所有工具定义
   */
  public getAllTools(): ToolDefinition[] {
    return Array.from(this.toolDefinitions.values());
  }
  
  /**
   * 获取特定工具定义
   */
  public getToolDefinition(name: string): ToolDefinition | undefined {
    return this.toolDefinitions.get(name);
  }
  
  /**
   * 更新工具上下文
   */
  public async updateToolsContext(context: Record<string, any> = {}): Promise<void> {
    for (const toolClass of Array.from(this.toolClasses.values())) {
      await toolClass.updateContext(context);
    }
  }
  
  /**
   * 执行工具
   */
  public async callTool(toolCall: ToolCallParams): Promise<ToolCallOutput> {
    const { name, arguments: args } = toolCall;
    
    try {
      console.log(`执行工具 ${name}，参数:`, args);
      const implementation = this.toolImplementations.get(name);
      if (!implementation) {
        throw new Error(`Tool ${name} not found or not registered`);
      }
      
      // 将参数对象展开为位置参数
      const toolDefinition = this.toolDefinitions.get(name);
      if (!toolDefinition) {
        throw new Error(`Tool definition not found for ${name}`);
      }
      
      if (name === 'getWeather') {
        const { location, unit } = args;
        return await implementation(location, unit);
      }
      
              throw new Error(`Unsupported tool: ${name}`);
    } catch (error) {
      console.error(`执行工具 ${name} 失败:`, error);
      return {
        content: {
          success: false,
          error: error instanceof Error ? error.message : String(error)
        }
      };
    }
  }
  
  /**
   * 获取系统提示
   */
  public getSystemPrompts(): string[] {
    const prompts: string[] = [];
    
    // 直接添加天气工具的系统提示
    const weatherPrompt = WeatherTool.getSystemPrompt();
    if (weatherPrompt) {
      prompts.push(weatherPrompt);
    }
    
    return prompts;
  }
  
  /**
   * 获取用于OpenAI的工具定义
   */
  public getOpenAITools(): any[] {
    return this.getAllTools().map(tool => ({
      type: 'function',
      function: {
        name: tool.name,
        description: tool.description,
        parameters: tool.parameters
      }
    }));
  }
  
  /**
   * 获取用于Claude的工具定义
   */
  public getClaudeTools(): any[] {
    return this.getAllTools().map(tool => ({
      name: tool.name,
      description: tool.description,
      input_schema: tool.parameters
    }));
  }
} 