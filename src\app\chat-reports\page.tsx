"use client";

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { ChatReport } from '@/chat-report/types';
import { DatePicker } from '@/components/ui/date-picker';
import { CustomSelect } from '@/components/ui/custom-select';
// import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Search, Plus, Loader2, Download, Wrench } from 'lucide-react';
import api from '@/utils/apiClient';
import { useSuccess } from '../contexts/SuccessContext';
import { useError } from '../contexts/ErrorContext';
import { withPermission } from '@/components/withPermission';
import { UserRole } from '@/utils/permissionConfig';

// 聊天报告页面组件
function ChatReportsPageContent() {
  const [reports, setReports] = useState<Omit<ChatReport, 'chatAnalyses'>[]>([]);
  const [loading, setLoading] = useState(true);
  const [isSearching, setIsSearching] = useState(false);
  const [searchParams, setSearchParams] = useState({
    fromDate: '',
    toDate: '',
    resolved: '',
  });
  const [isGenerating, setIsGenerating] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const [isFixing, setIsFixing] = useState(false);
  const router = useRouter();

  // Use global error and success message contexts
  const { setError } = useError();
  const { setSuccess } = useSuccess();



  // Fetch reports function
  const fetchReports = async () => {
    // Don't start a new search if already searching
    if (isSearching) return;

    setIsSearching(true);
    setLoading(true);
    setError(null);

    try {
      // Build search URL with params
      const params = new URLSearchParams();
      if (searchParams.fromDate) params.append('fromDate', searchParams.fromDate);
      if (searchParams.toDate) params.append('toDate', searchParams.toDate);
      if (searchParams.resolved) params.append('resolved', searchParams.resolved);

      // Use apiClient instead of fetch directly
      const { data, error } = await api.get<Omit<ChatReport, 'chatAnalyses'>[]>(`/api/chat-report?${params.toString()}`);

      if (error) {
        throw new Error(error);
      }

      // Filter out invalid date reports
      const validReports = (data || []).filter(report => {
        try {
          const date = new Date(report.date);
          return !isNaN(date.getTime()) && report.date !== 'Invalid Date';
        } catch {
          return false;
        }
      });

      setReports(validReports);
    } catch (err: any) {
      setError(err.message || 'Failed to load reports');
      console.error('Error fetching reports:', err);
    } finally {
      setLoading(false);
      setIsSearching(false);
    }
  };

  // Fetch reports on initial load
  useEffect(() => {
    fetchReports();
  }, []);

  // Handle search form submission
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    fetchReports();
  };

  // 不再使用，由其他处理函数替代
  // const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
  //   const { name, value } = e.target;
  //   console.log(`Input changed: ${name} = '${value}'`);
  //
  //   // Ensure empty strings are handled correctly
  //   if (value === '' && (name === 'fromDate' || name === 'toDate')) {
  //     console.log(`Clearing date field: ${name}`);
  //   }
  //
  //   setSearchParams(prev => ({ ...prev, [name]: value }));
  // };

  // Handle date picker changes
  const handleDateChange = (name: string, date: string | undefined) => {
    console.log(`handleDateChange called: ${name} = ${date}`);
    setSearchParams(prev => ({ ...prev, [name]: date || '' }));
  };

  // Handle select changes
  const handleSelectChange = (name: string, value: string) => {
    setSearchParams(prev => ({ ...prev, [name]: value }));
  };

  // Handle keyboard events for Enter key
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      fetchReports();
    }
  };

  // Generate reports for all missing dates
  const handleGenerateReports = async () => {
    try {
      setIsGenerating(true);
      setError(null);

      // Call the new API endpoint to generate reports for all missing dates
      const { data, error } = await api.post<any>('/api/chat-report/generate-missing', {});

      if (error) {
        throw new Error(error);
      }

      // If no missing reports were found
      if (data && data.message && data.message.includes('No missing reports found')) {
        // Generate today's report as a fallback
        const today = new Date().toISOString().split('T')[0];
        const todayResult = await api.post('/api/chat-report', { date: today });

        if (todayResult.error) {
          throw new Error(todayResult.error);
        }

        // Backend automatically tracks the latest report date
        setSuccess(`Today's report (${today}) has been regenerated successfully.`);
      } else {
        // Set success message with the number of reports generated
        const successCount = data && data.results ?
          data.results.filter((r: any) => r.status === 'success').length : 0;

        // Backend automatically tracks the latest report date during generation
        setSuccess(`Generated ${successCount} new reports successfully.`);
      }

      // Fetch updated reports list
      await fetchReports();
    } catch (err: any) {
      setError(err.message || 'Failed to generate reports');
      console.error('Error generating reports:', err);
    } finally {
      setIsGenerating(false);
    }
  };

  // View a specific report
  const handleViewReport = (date: string) => {
    router.push(`/chat-reports/${date}`);
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    try {
      // Check if the date is valid
      const date = new Date(dateString);
      if (isNaN(date.getTime()) || dateString === 'Invalid Date') {
        return 'Invalid Date';
      }

      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    } catch (error) {
      console.error(`Error formatting date: ${dateString}`, error);
      return 'Invalid Date';
    }
  };

  // 导出报告
  const handleExport = async () => {
    try {
      setIsExporting(true);
      setError(null);

      // 构建导出URL
      const params = new URLSearchParams();
      if (searchParams.fromDate) params.append('fromDate', searchParams.fromDate);
      if (searchParams.toDate) params.append('toDate', searchParams.toDate);
      if (searchParams.resolved) params.append('resolved', searchParams.resolved);

      // 使用 api 客户端发送请求
      const { data, error, headers } = await api.get(`/api/chat-report/export?${params.toString()}`, {
        responseType: 'blob',
        headers: {
          'Accept': 'application/zip',
        },
      });

      if (error) {
        throw new Error(error);
      }

      if (!data) {
        throw new Error('导出数据为空');
      }

      // 获取文件名
      const contentDisposition = headers?.get('Content-Disposition');
      let filename = 'chat_reports.zip';
      if (contentDisposition) {
        const matches = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/.exec(contentDisposition);
        if (matches != null && matches[1]) {
          filename = matches[1].replace(/['"]/g, '');
        }
      }

      // 下载文件
      const url = window.URL.createObjectURL(data as Blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      setSuccess('报告导出成功');
    } catch (err: any) {
      setError(err.message || '导出报告失败');
      console.error('Error exporting reports:', err);
    } finally {
      setIsExporting(false);
    }
  };

  // 修复报告数据
  const handleFixReports = async () => {
    try {
      setIsFixing(true);
      setError(null);

      // 构建修复URL
      const params = new URLSearchParams();
      if (searchParams.fromDate) params.append('fromDate', searchParams.fromDate);
      if (searchParams.toDate) params.append('toDate', searchParams.toDate);

      // 调用修复接口
      const { error } = await api.post('/api/chat-report/fix', {
        fromDate: searchParams.fromDate,
        toDate: searchParams.toDate
      });

      if (error) {
        throw new Error(error);
      }

      setSuccess('报告数据修复成功');
    } catch (err: any) {
      setError(err.message || '修复报告失败');
      console.error('Error fixing reports:', err);
    } finally {
      setIsFixing(false);
    }
  };

  return (
      <div className="min-h-screen bg-item-bg-primary  text-white">
        {/* Decorative overlay elements */}
        <div className="absolute top-0 left-0 right-0 h-20 bg-gradient-to-b from-item-purple/10 to-transparent pointer-events-none z-10"></div>
        <div className="absolute bottom-0 left-0 right-0 h-20 bg-gradient-to-t from-item-purple/10 to-transparent pointer-events-none z-10"></div>

        <div className="container mx-auto px-4 py-8 relative z-20">
          <h1 className="text-3xl font-bold mb-8 text-center   tracking-tight w-full">Chat Analysis Reports</h1>

          {/* Search Form */}
          <div className="bg-item-bg-card/80 backdrop-blur-sm p-6 rounded-xl mb-8 shadow-lg border border-item-gray-800">
            <h2 className="text-xl font-semibold mb-4 text-white ">Search Reports</h2>
            <form onSubmit={handleSearch} onKeyDown={handleKeyDown} className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <DatePicker
                label="From Date"
                name="fromDate"
                date={searchParams.fromDate}
                setDate={(date) => handleDateChange('fromDate', date)}
                placeholder="Select from date..."
              />

              <DatePicker
                label="To Date"
                name="toDate"
                date={searchParams.toDate}
                setDate={(date) => handleDateChange('toDate', date)}
                placeholder="Select to date..."
              />

              <CustomSelect
                label="Resolution Status"
                name="resolved"
                value={searchParams.resolved}
                onChange={(value) => handleSelectChange('resolved', value)}
                options={[
                  { value: "", label: "All" },
                  { value: "true", label: "Resolved" },
                  { value: "false", label: "Unresolved" }
                ]}
              />

              {/* Query field removed as requested */}

              <div className="md:col-span-4 flex flex-col gap-4">
                <div className="flex flex-col sm:flex-row justify-between gap-4">
                  <div className="flex gap-4">
                    <Button
                      type="button"
                      onClick={handleGenerateReports}
                      variant="outline"
                      className="bg-item-bg-hover hover:bg-item-gray-700 text-white border-item-gray-800  flex-1 sm:flex-none"
                      disabled={isGenerating}
                    >
                      {isGenerating ? (
                        <>
                          <Loader2 className="h-5 w-5 mr-2 animate-spin" />
                          Generating...
                        </>
                      ) : (
                        <>
                          <Plus className="h-5 w-5 mr-2" />
                          Generate Reports
                        </>
                      )}
                    </Button>

                    <Button
                      type="button"
                      onClick={handleFixReports}
                      variant="outline"
                      className="bg-item-bg-hover hover:bg-item-gray-700 text-white border-item-gray-800  flex-1 sm:flex-none"
                      disabled={isFixing}
                    >
                      {isFixing ? (
                        <>
                          <Loader2 className="h-5 w-5 mr-2 animate-spin" />
                          Fixing...
                        </>
                      ) : (
                        <>
                          <Wrench className="h-5 w-5 mr-2" />
                          Fix Reports
                        </>
                      )}
                    </Button>
                  </div>

                  <div className="flex gap-4">
                    <Button
                      type="button"
                      onClick={handleExport}
                      variant="outline"
                      className="bg-item-bg-hover hover:bg-item-gray-700 text-white border-item-gray-800 "
                      disabled={isExporting}
                    >
                      {isExporting ? (
                        <>
                          <Loader2 className="h-5 w-5 mr-2 animate-spin" />
                          Exporting...
                        </>
                      ) : (
                        <>
                          <Download className="h-5 w-5 mr-2" />
                          Export Reports
                        </>
                      )}
                    </Button>

                    <Button
                      type="submit"
                      variant="outline"
                      className="bg-item-bg-hover hover:bg-item-gray-700 text-white border-item-gray-800 "
                      disabled={isSearching}
                    >
                      {isSearching ? (
                        <>
                          <Loader2 className="h-5 w-5 mr-2 animate-spin" />
                          Searching...
                        </>
                      ) : (
                        <>
                          <Search className="h-5 w-5 mr-2" />
                          Search
                        </>
                      )}
                    </Button>
                  </div>
                </div>

              </div>
            </form>
          </div>

          {/* Success and error messages are now handled by global components */}

          {/* Loading State */}
          {loading && (
            <div className="flex items-center justify-center min-h-[400px]">
              <div className="text-center">
                <div className="inline-block animate-spin rounded-full h-12 w-12 border-4 border-item-purple border-t-transparent mb-4"></div>
                <p className="text-item-gray-300 text-lg ">Loading reports...</p>
              </div>
            </div>
          )}

          {/* Reports Table */}
          {!loading && reports.length > 0 && (
            <div className="overflow-x-auto rounded-xl shadow-lg border border-item-gray-800">
              <table className="min-w-full bg-item-bg-card/80 backdrop-blur-sm">
                <thead className="bg-item-bg-card/90 border-b border-item-gray-800">
                  <tr>
                    <th className="py-4 px-6 text-left text-sm font-semibold text-item-gray-300 uppercase tracking-wider">Date</th>
                    <th className="py-4 px-6 text-left text-sm font-semibold text-item-gray-300 uppercase tracking-wider">Total Chats</th>
                    <th className="py-4 px-6 text-left text-sm font-semibold text-item-gray-300 uppercase tracking-wider">Total Messages</th>
                    <th className="py-4 px-6 text-left text-sm font-semibold text-item-gray-300 uppercase tracking-wider">Resolved</th>
                    <th className="py-4 px-6 text-left text-sm font-semibold text-item-gray-300 uppercase tracking-wider">Unresolved</th>
                    <th className="py-4 px-6 text-left text-sm font-semibold text-item-gray-300 uppercase tracking-wider">Actions</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-item-gray-800/20">
                  {reports.map(report => (
                    <tr key={report.id} className="hover:bg-item-bg-hover/20 transition duration-150">
                      <td className="py-4 px-6 whitespace-nowrap text-item-purple-light font-medium ">{formatDate(report.date)}</td>
                      <td className="py-4 px-6 whitespace-nowrap text-white ">{report.totalChats}</td>
                      <td className="py-4 px-6 whitespace-nowrap text-white ">{report.totalMessages}</td>
                      <td className="py-4 px-6 whitespace-nowrap">
                        <span className="px-3 py-1 rounded-full text-sm font-medium bg-item-gray-700/60 text-item-gray-300 border border-item-gray-800 ">
                          {report.totalResolvedChats}
                        </span>
                      </td>
                      <td className="py-4 px-6 whitespace-nowrap">
                        <span className="px-3 py-1 rounded-full text-sm font-medium bg-item-gray-700/60 text-item-gray-300 border border-item-gray-800 ">
                          {report.totalUnresolvedChats}
                        </span>
                      </td>
                      <td className="py-4 px-6 whitespace-nowrap">
                        <button
                          onClick={() => handleViewReport(report.date)}
                          className="text-item-purple hover:text-item-purple-light transition flex items-center font-medium "
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                            <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                            <path fillRule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clipRule="evenodd" />
                          </svg>
                          View Details
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}

          {/* No Reports */}
          {!loading && reports.length === 0 && (
            <div className="text-center p-12 bg-item-bg-card/80 backdrop-blur-sm rounded-xl border border-item-gray-800 shadow-lg">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mx-auto text-item-gray-500 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <p className="text-item-gray-300 text-lg mb-4 ">No reports found.</p>
              <p className="text-item-gray-400/70 ">Generate a report to start analyzing your chat data.</p>
            </div>
          )}
        </div>
      </div>
  );
}

// 使用权限控制 HOC 包装组件
const ChatReportsPage = withPermission(ChatReportsPageContent, [
  UserRole.ADMIN,
  UserRole.REPORT_VIEWER
]);

export default ChatReportsPage;