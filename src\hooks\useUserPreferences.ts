'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/app/contexts/AuthContext';
import { UserPreferences } from '@/utils/userContext';
import { clientUserContextManager } from '@/utils/clientUserContext';
import api from '@/utils/apiClient';

/**
 * 用户偏好钩子
 * 提供获取和更新用户偏好的功能
 */
export function useUserPreferences() {
  const { isAuthenticated, tokenData } = useAuth();
  const [preferences, setPreferences] = useState<UserPreferences>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 获取用户偏好
  const fetchPreferences = async () => {
    if (!isAuthenticated) {
      setPreferences({});
      setLoading(false);
      return;
    }
    
    try {
      setLoading(true);
      
      const { data, error: apiError } = await api.get<UserPreferences>('/api/user/preferences');
      
      if (apiError) {
        throw new Error(apiError);
      }
      
      setPreferences(data || {});
      setError(null);
    } catch (err) {
      console.error('获取用户偏好失败:', err);
      setError(err instanceof Error ? err.message : '获取用户偏好失败');
    } finally {
      setLoading(false);
    }
  };

  // 更新用户偏好
  const updatePreferences = async (newPreferences: Partial<UserPreferences>) => {
    if (!isAuthenticated) {
      return;
    }
    
    try {
      const { data, error: apiError } = await api.patch<UserPreferences>(
        '/api/user/preferences', 
        newPreferences
      );
      
      if (apiError) {
        throw new Error(apiError);
      }
      
      setPreferences(data || {});
      setError(null);
      return data;
    } catch (err) {
      console.error('更新用户偏好失败:', err);
      setError(err instanceof Error ? err.message : '更新用户偏好失败');
      return null;
    }
  };

  // 当用户认证状态变化时获取偏好
  useEffect(() => {
    fetchPreferences();
  }, [isAuthenticated, tokenData?.sub]);

  return {
    preferences,
    loading,
    error,
    updatePreferences,
    refreshPreferences: fetchPreferences
  };
} 