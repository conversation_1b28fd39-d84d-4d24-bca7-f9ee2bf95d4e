{"permissions": {"allow": ["Bash(rg:*)", "Bash(grep:*)", "Bash(npm run lint)", "WebFetch(domain:design.item.com)", "Bash(# Replace gradients in ClockCard.tsx\nsed -i 's/bg-gradient-to-br from-item-orange\\/20 to-item-purple\\/20/bg-item-orange\\/20/g' src/app/components/ClockCard.tsx\n\n# Replace gradients in ErrorCard.tsx  \nsed -i 's/bg-gradient-to-br from-red-900\\/40 to-red-700\\/30/bg-red-900\\/35/g' src/app/components/ErrorCard.tsx\n\n# Replace gradients in JiraCard.tsx\nsed -i 's/bg-gradient-to-br from-blue-600\\/20 to-item-purple\\/20/bg-blue-600\\/20/g' src/app/components/JiraCard.tsx\n\n# Replace gradients in KnowledgeCard.tsx\nsed -i 's/bg-gradient-to-br from-indigo-600\\/20 to-item-purple\\/20/bg-indigo-600\\/20/g' src/app/components/KnowledgeCard.tsx\n\n# Replace gradients in MemoryToolCard.tsx\nsed -i 's/bg-gradient-to-br from-item-purple\\/20 to-blue-500\\/20/bg-item-purple\\/20/g' src/app/components/MemoryToolCard.tsx\n\n# Replace gradients in PlanningCard.tsx\nsed -i 's/bg-gradient-to-br from-item-purple\\/20 to-item-orange\\/20/bg-item-purple\\/20/g' src/app/components/PlanningCard.tsx\n\n# Replace gradients in WMSCard.tsx\nsed -i 's/bg-gradient-to-br from-green-600\\/20 to-blue-600\\/20/bg-green-600\\/20/g' src/app/components/WMSCard.tsx\n\n# Replace gradients in WeatherCard.tsx (remaining ones)\nsed -i 's/bg-gradient-to-br from-item-purple\\/20 to-item-orange\\/20/bg-item-purple\\/20/g' src/app/components/WeatherCard.tsx\n\necho \"All gradients replaced with solid colors\")", "Bash(npm run lint:*)", "Bash(npm install:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(cp:*)", "Bash(npm ls:*)", "Bash(timeout 30 npm run dev)", "Bash(npm run build:*)", "Bash(npx tsc:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(sed:*)", "Bash(node:*)", "Bash(awk:*)", "Bash(rm:*)", "WebFetch(domain:www.twilio.com)", "WebFetch(domain:srs.unisco.com)", "Bash(npm run dev:*)", "WebFetch(domain:ai-sdk.dev)", "WebFetch(domain:martinbarreto.com)", "Bash(git checkout:*)", "WebFetch(domain:sdk.vercel.ai)", "<PERSON><PERSON>(claude mcp)", "<PERSON><PERSON>(claude mcp:*)"], "deny": []}}