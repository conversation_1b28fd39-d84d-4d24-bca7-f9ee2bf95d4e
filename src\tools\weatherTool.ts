import { tool } from 'ai';
import { z } from 'zod';
import { BaseTool } from './baseTool';
import { ToolDefinition } from './types';

/**
 * 天气工具
 * 使用 Vercel AI SDK 的 tool 函数实现
 */
export const weatherTool = tool({
  description: 'Get current weather information for a location',
  parameters: z.object({
    location: z.string().describe('The city and state, e.g. San Francisco, CA or country e.g. France'),
    unit: z.enum(['celsius', 'fahrenheit']).optional().describe('The unit of temperature')
  }),
  execute: async ({ location, unit = 'celsius' }) => {
    console.log('WeatherTool.execute called with args:', JSON.stringify({ location, unit }));
    
    // 模拟天气数据
    // 在实际应用中，这里应该调用真实的天气API
    const weatherConditions = ['sunny', 'cloudy', 'rainy', 'snowy', 'windy', 'foggy'];
    const randomCondition = weatherConditions[Math.floor(Math.random() * weatherConditions.length)];
    
    // 生成随机温度
    const tempMin = unit === 'celsius' ? 10 : 50;
    const tempMax = unit === 'celsius' ? 35 : 95;
    const temperature = Math.floor(Math.random() * (tempMax - tempMin + 1)) + tempMin;
    
    // 生成随机湿度
    const humidity = Math.floor(Math.random() * 60) + 30;
    
    // 生成随机风速
    const windSpeed = Math.floor(Math.random() * 30) + 5;
    
    // 构建响应
    const weatherData = {
      location,
      temperature,
      unit,
      condition: randomCondition,
      humidity: humidity,
      windSpeed: windSpeed,
      lastUpdated: new Date().toISOString()
    };
    
    console.log('WeatherTool.execute response:', JSON.stringify(weatherData));
    
    return weatherData;
  }
});

/**
 * WeatherTool 类，继承自 BaseTool
 * 适配旧的工具系统
 */
export class WeatherTool extends BaseTool {
  constructor() {
    super();
  }
  
  getTools(): ToolDefinition[] {
    return [{
      name: 'getWeather',
      description: 'Get current weather information for a location',
      parameters: {
        type: 'object',
        properties: {
          location: {
            type: 'string',
            description: 'The city and state, e.g. San Francisco, CA or country e.g. France'
          },
          unit: {
            type: 'string',
            enum: ['celsius', 'fahrenheit'],
            description: 'The unit of temperature'
          }
        },
        required: ['location']
      }
    }];
  }
  
  async getWeather(location: string, unit: 'celsius' | 'fahrenheit' = 'celsius') {
    // 复用 weatherTool 的实现
    return await weatherTool.execute({ location, unit }, { 
      toolCallId: `weather-${Date.now()}`, 
      messages: []
    });
  }
  
  static getSystemPrompt(): string {
    return 'You can use the getWeather tool to get weather information for specified locations.';
  }
} 