'use client';

import * as React from 'react';
import { format, parse } from 'date-fns';
import { Calendar as CalendarIcon, Clock, X, ChevronLeft, ChevronRight, ChevronUp, ChevronDown } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { DayPicker } from 'react-day-picker';
import "react-day-picker/dist/style.css";
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';

interface DateTimePickerProps {
  date: string | undefined;
  setDate: (date: string | undefined) => void;
  className?: string;
  placeholder?: string;
  label?: string;
  name?: string;
}

export function DateTimePicker({
  date,
  setDate,
  className,
  placeholder = 'Select date and time...',
  label,
  name,
}: DateTimePickerProps) {
  const [isOpen, setIsOpen] = React.useState(false);
  const [tempDate, setTempDate] = React.useState<Date | undefined>(undefined);
  const [selectedHour, setSelectedHour] = React.useState<string>('00');
  const [selectedMinute, setSelectedMinute] = React.useState<string>('00');
  
  const hourRef = React.useRef<HTMLDivElement>(null);
  const minuteRef = React.useRef<HTMLDivElement>(null);
  
  // Hours and minutes for the time picker
  const hours = Array.from({ length: 24 }, (_, i) => i.toString().padStart(2, '0'));
  const minutes = Array.from({ length: 60 }, (_, i) => i.toString().padStart(2, '0'));
  
  // Initialize state based on provided date
  React.useEffect(() => {
    if (date) {
      const dateObj = parse(date, 'yyyy-MM-dd HH:mm:ss', new Date());
      setTempDate(dateObj);
      setSelectedHour(format(dateObj, 'HH'));
      setSelectedMinute(format(dateObj, 'mm'));
    } else {
      setTempDate(undefined);
      setSelectedHour('00');
      setSelectedMinute('00');
    }
  }, [date, isOpen]);
  
  // Scroll selected hour and minute into view when opening
  React.useEffect(() => {
    if (isOpen) {
      setTimeout(() => {
        if (hourRef.current) {
          const hourItem = hourRef.current.querySelector(`[data-hour="${selectedHour}"]`);
          if (hourItem) {
            hourItem.scrollIntoView({ block: 'center', behavior: 'smooth' });
          }
        }
        
        if (minuteRef.current) {
          const minuteItem = minuteRef.current.querySelector(`[data-minute="${selectedMinute}"]`);
          if (minuteItem) {
            minuteItem.scrollIntoView({ block: 'center', behavior: 'smooth' });
          }
        }
      }, 100);
    }
  }, [isOpen, selectedHour, selectedMinute]);
  
  // Handle day selection
  const handleDaySelect = (day: Date | undefined) => {
    if (!day) {
      setTempDate(undefined);
      return;
    }
    
    const newDate = new Date(
      day.getFullYear(),
      day.getMonth(),
      day.getDate(),
      selectedHour ? parseInt(selectedHour, 10) : 0,
      selectedMinute ? parseInt(selectedMinute, 10) : 0
    );
    
    setTempDate(newDate);
  };
  
  // Handle hour selection
  const handleHourSelect = (hour: string) => {
    setSelectedHour(hour);
    
    if (tempDate) {
      const newDate = new Date(tempDate);
      newDate.setHours(parseInt(hour, 10));
      setTempDate(newDate);
    }
  };
  
  // Handle minute selection
  const handleMinuteSelect = (minute: string) => {
    setSelectedMinute(minute);
    
    if (tempDate) {
      const newDate = new Date(tempDate);
      newDate.setMinutes(parseInt(minute, 10));
      setTempDate(newDate);
    }
  };
  
  // Apply selected date and time
  const handleOk = () => {
    if (tempDate) {
      const dateString = format(tempDate, 'yyyy-MM-dd HH:mm:ss');
      setDate(dateString);
    } else {
      setDate(undefined);
    }
    setIsOpen(false);
  };
  
  // Cancel selection
  const handleCancel = () => {
    if (date) {
      const dateObj = parse(date, 'yyyy-MM-dd HH:mm:ss', new Date());
      setTempDate(dateObj);
      setSelectedHour(format(dateObj, 'HH'));
      setSelectedMinute(format(dateObj, 'mm'));
    } else {
      setTempDate(undefined);
      setSelectedHour('00');
      setSelectedMinute('00');
    }
    setIsOpen(false);
  };
  
  // Set to current date and time
  const handleNow = () => {
    const now = new Date();
    setTempDate(now);
    setSelectedHour(format(now, 'HH'));
    setSelectedMinute(format(now, 'mm'));
  };
  
  // 将字符串日期转换为 Date 对象用于显示
  const dateObj = date ? parse(date, 'yyyy-MM-dd HH:mm:ss', new Date()) : undefined;
  
  return (
    <div className={className}>
      {label && (
        <label className="block text-sm font-medium mb-2 text-item-gray-400 font-mono">{label}</label>
      )}
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            className={cn(
              'w-full justify-between bg-item-bg-card/80 border-item-purple/30 text-white hover:bg-item-bg-input hover:text-white font-mono transition-all duration-200',
              !date && 'text-item-gray-500',
              'focus:ring-2 focus:ring-item-purple focus:border-transparent',
              'min-w-0'
            )}
          >
            <span className="truncate flex-1 text-left">
              {date ? format(dateObj!, 'MMM dd, yyyy HH:mm:ss') : <span>{placeholder}</span>}
            </span>
            <div className="flex items-center flex-shrink-0 ml-2">
              {date && (
                <div
                  onClick={(e) => {
                    e.stopPropagation();
                    e.preventDefault();
                    setDate(undefined);
                  }}
                  className="mr-2 cursor-pointer"
                >
                  <X className="h-4 w-4 text-item-gray-400 hover:text-red-400 transition-all duration-200" />
                </div>
              )}
              <Clock className="h-5 w-5 text-item-purple" />
            </div>
          </Button>
        </PopoverTrigger>
        <PopoverContent
          className="w-auto p-0 bg-item-bg-card/95 backdrop-blur-sm border border-item-purple/50 text-item-purple-light shadow-xl shadow-item-purple/30 z-[9999]"
          align="center"
          sideOffset={8}
        >
          <div className="flex flex-col md:flex-row">
            {/* Calendar */}
            <div className="p-3 md:border-r border-item-gray-700/50">
              <DayPicker
                mode="single"
                defaultMonth={tempDate}
                selected={tempDate}
                onSelect={handleDaySelect}
                classNames={{
                  root: 'rdp-root',
                  caption: 'rdp-caption flex items-center justify-center py-2 mb-4 relative',
                  caption_label: 'rdp-caption_label text-item-purple-light font-medium text-lg mx-auto',
                  nav: 'rdp-nav flex items-center justify-between absolute top-2 w-full px-2 pointer-events-auto',
                  nav_button: 'rdp-nav_button inline-flex justify-center items-center w-9 h-9 p-0 rounded-full text-item-purple hover:bg-item-purple/20 hover:text-item-purple-light focus:outline-none focus-visible:ring focus-visible:ring-item-purple transition-all duration-200',
                  nav_button_previous: 'rdp-nav_button_previous absolute left-1 hover:shadow-md',
                  nav_button_next: 'rdp-nav_button_next absolute right-1 hover:shadow-md',
                  table: 'rdp-table w-full border-collapse',
                  head: 'rdp-head',
                  head_row: 'rdp-head_row',
                  head_cell: 'rdp-head_cell text-item-purple-light font-medium text-center py-2',
                  tbody: 'rdp-tbody',
                  row: 'rdp-row',
                  cell: 'rdp-cell p-0 relative',
                  day: 'rdp-day w-10 h-10 text-center text-item-purple-light hover:bg-item-purple/20 rounded-full focus:outline-none focus-visible:ring focus-visible:ring-item-purple transition-all duration-200',
                  day_selected: 'rdp-day_selected bg-item-purple text-white hover:bg-item-purple-dark font-medium shadow-md transition-all duration-200',
                  day_today: 'rdp-day_today border border-item-purple font-medium text-item-purple-light',
                  day_outside: 'rdp-day_outside text-item-gray-500 opacity-50',
                  day_disabled: 'rdp-day_disabled text-item-gray-400 opacity-40',
                  day_hidden: 'rdp-day_hidden invisible',
                  button_reset: 'rdp-button_reset'
                }}
                styles={{
                  caption_label: { textAlign: 'center', position: 'relative', zIndex: 1 },
                  nav: { position: 'absolute', top: '8px', left: 0, right: 0, zIndex: 2 },
                  months: { display: 'flex', justifyContent: 'center' }
                }}
                components={{
                  Chevron: ({ orientation }) =>
                    orientation === 'left'
                      ? <ChevronLeft className="h-5 w-5 text-item-purple stroke-[2.5]" />
                      : <ChevronRight className="h-5 w-5 text-item-purple stroke-[2.5]" />
                }}
              />
            </div>
            
            {/* Time picker */}
            <div className="p-4 flex flex-col">
              <div className="text-center mb-2 text-item-purple-light font-medium">Time</div>
              <div className="flex space-x-4 items-center">
                {/* Hours */}
                <div className="flex flex-col items-center">
                  <div className="text-xs text-item-purple mb-1">Hour</div>
                  <Button 
                    variant="ghost" 
                    size="icon" 
                    className="h-6 w-6 rounded-full text-item-purple-light hover:text-item-purple-lighter hover:bg-item-purple/20 transition-all duration-200"
                    onClick={() => {
                      const currentIndex = hours.indexOf(selectedHour);
                      const newIndex = currentIndex > 0 ? currentIndex - 1 : 23;
                      handleHourSelect(hours[newIndex]);
                    }}
                  >
                    <ChevronUp className="h-4 w-4" />
                  </Button>
                  <div 
                    ref={hourRef}
                    className="h-32 overflow-y-auto w-14 scrollbar-thin scrollbar-thumb-item-purple scrollbar-track-item-bg-input/50 scrollbar-thumb-rounded-full"
                  >
                    <div className="flex flex-col items-center py-2">
                      {hours.map((hour) => (
                        <div
                          key={hour}
                          data-hour={hour}
                          className={cn(
                            "cursor-pointer py-1 px-2 min-w-[40px] text-center rounded-md my-0.5",
                            hour === selectedHour
                              ? "bg-item-purple text-white font-medium shadow-md transition-all duration-200"
                              : "text-item-gray-400 hover:bg-item-purple/20 hover:text-item-purple-light transition-all duration-200"
                          )}
                          onClick={() => handleHourSelect(hour)}
                        >
                          {hour}
                        </div>
                      ))}
                    </div>
                  </div>
                  <Button 
                    variant="ghost" 
                    size="icon" 
                    className="h-6 w-6 rounded-full text-item-purple-light hover:text-item-purple-lighter hover:bg-item-purple/20 transition-all duration-200"
                    onClick={() => {
                      const currentIndex = hours.indexOf(selectedHour);
                      const newIndex = currentIndex < 23 ? currentIndex + 1 : 0;
                      handleHourSelect(hours[newIndex]);
                    }}
                  >
                    <ChevronDown className="h-4 w-4" />
                  </Button>
                </div>
                
                <div className="text-item-purple-light text-lg font-medium">:</div>
                
                {/* Minutes */}
                <div className="flex flex-col items-center">
                  <div className="text-xs text-item-purple mb-1">Minute</div>
                  <Button 
                    variant="ghost" 
                    size="icon" 
                    className="h-6 w-6 rounded-full text-item-purple-light hover:text-item-purple-lighter hover:bg-item-purple/20 transition-all duration-200"
                    onClick={() => {
                      const currentIndex = minutes.indexOf(selectedMinute);
                      const newIndex = currentIndex > 0 ? currentIndex - 1 : 59;
                      handleMinuteSelect(minutes[newIndex]);
                    }}
                  >
                    <ChevronUp className="h-4 w-4" />
                  </Button>
                  <div 
                    ref={minuteRef}
                    className="h-32 overflow-y-auto w-14 scrollbar-thin scrollbar-thumb-item-purple scrollbar-track-item-bg-input/50 scrollbar-thumb-rounded-full"
                  >
                    <div className="flex flex-col items-center py-2">
                      {minutes.map((minute) => (
                        <div
                          key={minute}
                          data-minute={minute}
                          className={cn(
                            "cursor-pointer py-1 px-2 min-w-[40px] text-center rounded-md my-0.5",
                            minute === selectedMinute
                              ? "bg-item-purple text-white font-medium shadow-md transition-all duration-200"
                              : "text-item-gray-400 hover:bg-item-purple/20 hover:text-item-purple-light transition-all duration-200"
                          )}
                          onClick={() => handleMinuteSelect(minute)}
                        >
                          {minute}
                        </div>
                      ))}
                    </div>
                  </div>
                  <Button 
                    variant="ghost" 
                    size="icon" 
                    className="h-6 w-6 rounded-full text-item-purple-light hover:text-item-purple-lighter hover:bg-item-purple/20 transition-all duration-200"
                    onClick={() => {
                      const currentIndex = minutes.indexOf(selectedMinute);
                      const newIndex = currentIndex < 59 ? currentIndex + 1 : 0;
                      handleMinuteSelect(minutes[newIndex]);
                    }}
                  >
                    <ChevronDown className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          </div>
          
          {/* Button Bar */}
          <div className="flex justify-between items-center p-2 border-t border-item-gray-700/50">
            <Button
              variant="ghost"
              className="text-sm text-item-purple-light hover:bg-item-purple/20 hover:text-item-purple-lighter font-medium transition-all duration-200"
              onClick={handleNow}
            >
              Now
            </Button>
            
            <div className="flex gap-2">
              <Button
                variant="ghost"
                className="text-sm text-red-300 hover:bg-red-900/40 hover:text-red-100 font-medium transition-all duration-200"
                onClick={handleCancel}
              >
                Cancel
              </Button>
              <Button
                variant="ghost"
                className="text-sm text-item-orange hover:bg-item-orange/20 hover:text-item-orange-light font-medium transition-all duration-200"
                onClick={handleOk}
              >
                OK
              </Button>
            </div>
          </div>
        </PopoverContent>
      </Popover>
      {name && date && (
        <input
          type="hidden"
          name={name}
          value={date}
        />
      )}
      
      <style jsx global>{`
        .scrollbar-thin::-webkit-scrollbar {
          width: 6px;
        }
        .scrollbar-track-item-bg-input\/50::-webkit-scrollbar-track {
          background: var(--item-bg-input-alpha-50);
          border-radius: 10px;
        }
        .scrollbar-thumb-item-purple::-webkit-scrollbar-thumb {
          background: var(--item-purple);
          border-radius: 10px;
        }
        .scrollbar-thumb-rounded-full::-webkit-scrollbar-thumb {
          border-radius: 10px;
        }
        /* Firefox */
        .scrollbar-thin {
          scrollbar-width: thin;
          scrollbar-color: var(--item-purple) var(--item-bg-input-alpha-50);
        }
      `}</style>
    </div>
  );
} 