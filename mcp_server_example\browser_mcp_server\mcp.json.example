{"defaultServer": "wms", "mcpServers": {"wms": {"url": "http://localhost:8000/sse", "description": "WMS MCP Server provides tools (find_wms_api , call_wms_api) and prompts", "transport": "sse"}, "browser": {"url": "http://localhost:8001", "description": "Browser MCP Server provides web task automation", "transport": "sse", "timeout": 300000}, "playwright": {"command": "cmd", "args": ["/c", "npx", "@modelcontextprotocol/server-puppeteer"]}}}