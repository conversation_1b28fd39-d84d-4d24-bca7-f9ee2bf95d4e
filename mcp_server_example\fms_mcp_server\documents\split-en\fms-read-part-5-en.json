{"openapi": "3.0.1", "info": {"title": "FMS", "description": "", "version": "1.0.0"}, "paths": {"/fms-platform-order/crm-customer/code/{customer_code}": {"get": {"summary": "GetCustomerByCode", "deprecated": false, "description": "", "tags": ["CRM"], "parameters": [{"name": "customer_code", "in": "path", "description": "", "required": true, "example": "", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order/CRM/GetCustomerBillInfo": {"get": {"summary": "/fms-platform-order/CRM/GetCustomerBillInfo", "deprecated": false, "description": "", "tags": ["CRM"], "parameters": [{"name": "customerCode", "in": "query", "description": "", "required": false, "schema": {"type": "string"}}, {"name": "bill<PERSON><PERSON>", "in": "query", "description": "", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CrmRPCCustomerByBillToResponse"}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order/orders/hot-fix/all/length": {"get": {"summary": "/fms-platform-order/orders/hot-fix/all/length", "deprecated": false, "description": "", "tags": ["FixOrder2"], "parameters": [], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order/orders2/hot-fix/valid/today": {"get": {"summary": "Verify that the ES data and database data are consistent", "deprecated": false, "description": "", "tags": ["FixOrder2"], "parameters": [], "responses": {"200": {"description": "Success", "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order/document/shipment/signature-form": {"get": {"summary": "Get the signature form for shipping orders", "deprecated": false, "description": "", "tags": ["OrderDocument"], "parameters": [{"name": "orderNo", "in": "query", "description": "Order number", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/SignatureFormDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order/document/shipment/signature-form-list": {"post": {"summary": "Bulk acquisition", "deprecated": false, "description": "", "tags": ["OrderDocument"], "parameters": [], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/RpcShipmentOrderNosDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SignatureFormDto"}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order/OrderExternal/GetOrderActualFreightInfoTotalForInvoice": {"get": {"summary": "External\r\nProvide Invoice Rate Detail with an interface to query Order ActualFreightInfo Total data", "deprecated": false, "description": "", "tags": ["OrderExternal"], "parameters": [{"name": "orderNo", "in": "query", "description": "", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/OrderActualFreightInfoTotalForInvoiceDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order/OrderExternal/IsLockedForInvoice": {"get": {"summary": "Check if the order is locked IsLocked", "deprecated": false, "description": "", "tags": ["OrderExternal"], "parameters": [{"name": "orderNo", "in": "query", "description": "Order number", "required": false, "schema": {"type": "string"}}, {"name": "lockType", "in": "query", "description": "", "required": false, "schema": {"$ref": "#/components/schemas/OrderLockTypeEnum"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "boolean"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order/OrderExternal/GetOrderHoldLock": {"post": {"summary": "Check whether the order is held", "deprecated": false, "description": "", "tags": ["OrderExternal"], "parameters": [], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/RpcShipmentOrderListRequest"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/RpcOrderHoldLockResponse"}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order/shipment-orders/search-business-client": {"get": {"summary": "Query businessClient information", "deprecated": false, "description": "", "tags": ["OrderGeneral"], "parameters": [{"name": "Name", "in": "query", "description": "", "required": false, "schema": {"type": "string"}}, {"name": "Code", "in": "query", "description": "", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BusinessClientDtoPagedResultDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order/shipment/nearest-revenue-terminal": {"get": {"summary": "Query businessClient information", "deprecated": false, "description": "", "tags": ["OrderGeneral"], "parameters": [{"name": "companyCode", "in": "query", "description": "", "required": false, "schema": {"type": "string"}}, {"name": "lng", "in": "query", "description": "", "required": false, "schema": {"type": "number", "format": "double"}}, {"name": "lat", "in": "query", "description": "", "required": false, "schema": {"type": "number", "format": "double"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/NearestRevenueTerminalDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order/shipment/isLocked": {"get": {"summary": "Check if the order is locked IsLocked", "deprecated": false, "description": "", "tags": ["OrderGeneral"], "parameters": [{"name": "orderNo", "in": "query", "description": "Order number", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "boolean"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order/shipment/islocked/dirtyreading": {"get": {"summary": "Check if the order is locked (dirty read) islocked dirty reading", "deprecated": false, "description": "", "tags": ["OrderGeneral"], "parameters": [{"name": "orderNo", "in": "query", "description": "Order number", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "boolean"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order/pro-manage/generate-pro": {"get": {"summary": "Generate ProNo", "deprecated": false, "description": "", "tags": ["ProNoManage"], "parameters": [], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "string"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order/revenue-code/list": {"get": {"summary": "Get the RevenueCode list", "deprecated": false, "description": "", "tags": ["RevenueCode"], "parameters": [], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/RevenueCodeDto"}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order/shipment-orders/acclist": {"get": {"summary": "internal\r\nQuery the list of additional information of Acc", "deprecated": false, "description": "This method is used to query the relevant Acc additional information list based on the order number.", "tags": ["ShipmentOrderAcc"], "parameters": [{"name": "orderNo", "in": "query", "description": "Order number", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/OrderAccSaveDto"}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order/actual-freight/{orderNo}": {"get": {"summary": "GetActualFreight", "deprecated": false, "description": "Query the Actual Freight information (pallet) of the order, the in-parameter is do#, and the out-parameter is pallet detailed information.", "tags": ["ShipmentOrderActualFreight"], "parameters": [{"name": "orderNo", "in": "path", "description": "Order number", "required": true, "example": "", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ShipmentOrderActualFreightInfoDto"}}}}, "headers": {}}}, "security": [{"apikey-header-fms-token": []}]}}, "/fms-platform-order/ShipmentOrderActualFreight/GetPackageFreightDetail": {"get": {"summary": "internal\r\nCheck actual shipping details", "deprecated": false, "description": "", "tags": ["ShipmentOrderActualFreight"], "parameters": [{"name": "Id", "in": "query", "description": "The ID of the actual shipping information", "required": false, "schema": {"type": "integer", "format": "int64"}}, {"name": "ShowPkgOrPallet", "in": "query", "description": "Sign showing parcel or pallet", "required": false, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ShipmentOrderActualDetailInfo"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order/ShipmentOrderActualFreight/AbortPallets": {"get": {"summary": "/fms-platform-order/ShipmentOrderActualFreight/AbortPallets", "deprecated": false, "description": "", "tags": ["ShipmentOrderActualFreight"], "parameters": [{"name": "orderNo", "in": "query", "description": "", "required": false, "schema": {"type": "string"}}, {"name": "palletNos", "in": "query", "description": "", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-order/ShipmentOrderActualFreight/GeneratePallets": {"get": {"summary": "/fms-platform-order/ShipmentOrderActualFreight/GeneratePallets", "deprecated": false, "description": "", "tags": ["ShipmentOrderActualFreight"], "parameters": [{"name": "count", "in": "query", "description": "", "required": false, "schema": {"type": "integer", "format": "int32"}}, {"name": "orderNo", "in": "query", "description": "", "required": false, "schema": {"type": "string"}}, {"name": "batchNo", "in": "query", "description": "", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}}, "components": {"schemas": {"OrderAccSaveDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "code": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "quantity": {"type": "integer", "format": "int32"}, "uom": {"type": "string", "nullable": true}, "unit_price": {"type": "number", "format": "double"}, "amount": {"type": "number", "format": "double"}, "is_del": {"type": "boolean"}, "order_no": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BusinessClientDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "name": {"type": "string", "nullable": true}, "code": {"type": "string", "nullable": true}, "print_name": {"type": "string", "nullable": true}, "bill_to_only": {"type": "boolean"}, "type": {"type": "integer", "format": "int32"}, "telnet_id": {"type": "string", "nullable": true}, "company": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BusinessClientDtoPagedResultDto": {"type": "object", "properties": {"total_count": {"type": "integer", "format": "int64"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/BusinessClientDto"}, "nullable": true}}, "additionalProperties": false}, "CrmRPCCustomerByBillToResponse": {"type": "object", "properties": {"bill_to_code": {"type": "string", "nullable": true}, "bill_to_full_name": {"type": "string", "nullable": true}, "bill_to_name": {"type": "string", "nullable": true}, "customer_code": {"type": "string", "nullable": true}}, "additionalProperties": false}, "NearestRevenueTerminalDto": {"type": "object", "properties": {"code": {"type": "string", "nullable": true}, "lng": {"type": "number", "format": "double", "nullable": true}, "lat": {"type": "number", "format": "double", "nullable": true}}, "additionalProperties": false}, "OrderActualFreightInfoTotalForInvoiceDto": {"type": "object", "properties": {"total_quantity": {"type": "integer", "format": "int32"}, "total_weight": {"type": "number", "format": "double"}}, "additionalProperties": false}, "OrderLockTypeEnum": {"enum": [1, 2], "type": "integer", "format": "int32"}, "RevenueCodeDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "code": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "reference": {"type": "string", "nullable": true}, "reference2": {"type": "string", "nullable": true}, "reference3": {"type": "string", "nullable": true}}, "additionalProperties": false}, "RpcShipmentOrderNosDto": {"type": "object", "properties": {"order_nos": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "ShipmentOrderActualDetailInfo": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "package_no_or_pallet_no": {"type": "string", "nullable": true}, "show_pkg_or_pallet": {"type": "integer", "format": "int32"}, "org_length": {"type": "number", "format": "double"}, "org_width": {"type": "number", "format": "double"}, "org_height": {"type": "number", "format": "double"}, "org_weight": {"type": "number", "format": "double"}, "org_dim_weight": {"type": "number", "format": "double"}, "org_class": {"type": "number", "format": "double"}, "actual_length": {"type": "number", "format": "double"}, "actual_width": {"type": "number", "format": "double"}, "actual_height": {"type": "number", "format": "double"}, "actual_weight": {"type": "number", "format": "double"}, "actual_dim_weight": {"type": "number", "format": "double"}, "actual_class": {"type": "number", "format": "double"}, "last_update": {"type": "string", "nullable": true}, "last_update_date": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "RpcOrderHoldLockResponse": {"type": "object", "properties": {"order_no": {"type": "string", "nullable": true}, "is_hold": {"type": "integer", "format": "int32"}, "is_lock": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "RpcShipmentOrderListRequest": {"type": "object", "properties": {"order_nos": {"type": "array", "items": {"type": "string"}, "nullable": true}, "tracking_or_pro_nos": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "ShipmentOrderActualFreightInfoDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "package_no_or_pallet_no": {"type": "string", "nullable": true}, "show_pkg_or_pallet": {"type": "integer", "format": "int32"}, "current_location": {"type": "string", "nullable": true}, "exception": {"type": "string", "nullable": true}, "pick_up_quantity": {"type": "integer", "format": "int32"}, "quantity_uom": {"type": "string", "nullable": true}, "length": {"type": "number", "format": "double"}, "width": {"type": "number", "format": "double"}, "height": {"type": "number", "format": "double"}, "weight": {"type": "number", "format": "double"}, "delivery_qty": {"type": "integer", "format": "int32"}, "dim_weight": {"type": "number", "format": "double"}, "class": {"type": "number", "format": "double"}, "shipment_type": {"type": "integer", "format": "int32"}, "is_del": {"type": "boolean"}, "dock_location_name": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SignatureFormDto": {"type": "object", "properties": {"shipper_order_no": {"type": "string", "nullable": true}, "shipper_name": {"type": "string", "nullable": true}, "shipper_address1": {"type": "string", "nullable": true}, "shiperr_address_detail": {"type": "string", "nullable": true}, "shipper_phone": {"type": "string", "nullable": true}, "consignee_name": {"type": "string", "nullable": true}, "consignee_address1": {"type": "string", "nullable": true}, "consignee_address_detail": {"type": "string", "nullable": true}, "consignee_phone": {"type": "string", "nullable": true}, "shipment_date": {"type": "string", "nullable": true}, "reference": {"type": "string", "nullable": true}, "bol": {"type": "string", "nullable": true}, "poso": {"type": "string", "nullable": true}, "pallets": {"type": "array", "items": {"$ref": "#/components/schemas/SignatureFromPalletDto"}, "nullable": true}, "appointment_date": {"type": "string", "nullable": true}, "appointment_time_from_to": {"type": "string", "nullable": true}, "appointment_time_in": {"type": "string", "nullable": true}, "appointment_time_out": {"type": "string", "nullable": true}, "driver_name": {"type": "string", "nullable": true}, "consignee_print_name": {"type": "string", "nullable": true}, "signature": {"type": "string", "nullable": true}, "signature_date": {"type": "string", "nullable": true}, "company_icon": {"type": "string", "nullable": true}, "company_url": {"type": "string", "nullable": true}, "company_phone": {"type": "string", "nullable": true}, "tracking_no": {"type": "string", "nullable": true}, "exceptions": {"type": "string", "nullable": true}, "accs": {"type": "array", "items": {"type": "string"}, "nullable": true}, "billing_name": {"type": "string", "nullable": true}, "billing_address1": {"type": "string", "nullable": true}, "billing_address_detail": {"type": "string", "nullable": true}, "delivery_note": {"type": "string", "nullable": true}, "shipment_type": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SignatureFromPalletDto": {"type": "object", "properties": {"pallet_no": {"type": "string", "nullable": true}, "piece": {"type": "number", "format": "double"}, "description": {"type": "string", "nullable": true}, "weight": {"type": "number", "format": "double"}, "class": {"type": "number", "format": "double"}, "pallet": {"type": "integer", "format": "int32"}}, "additionalProperties": false}}}}