/**
 * @file 服务端站点配置管理
 * @description 用于服务端管理站点配置、密钥对和限流规则的工具
 * @server-side
 */

import { SiteConfig } from '../types/site';

// 站点配置存储
const siteConfigs: Map<string, SiteConfig> = new Map();

// 初始化站点配置
export function initSiteConfig(config: SiteConfig) {
  siteConfigs.set(config.siteId, config);
}

// 获取站点配置
export function getSiteConfig(siteId: string): SiteConfig | undefined {
  return siteConfigs.get(siteId);
}

// 获取所有站点配置
export function getAllSiteConfigs(): SiteConfig[] {
  return Array.from(siteConfigs.values());
}

// === 站点批量初始化 ===
initSiteConfig({
  siteId: 'unis',
  publicKey: `-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAg8WG1LZWl//YCgLA0zSN
mE44Ea8LCBII0fLEpr8ARsBDhekJBWRdIEyy8cQExXylO7cMsl9dxPbodMxsJspp
AjcMnRYPqVidxlRP/eMLp8NBr0Aw8FAI4Bn56pf/nhuyNgRvcgd5+VS7xf81Iagw
WL7jok+qEX1VzMF5m2wXSC0DE+YeT72xBw0eacUUP6ncuLddZRxJPF+Rh17JJZh6
W2pepd53XWJMyMTnkt3o/lDpcj/qk9ul+YW4fk0GBG2dBxs9vZdThtLNUqz+4+UO
0PIididJ2k8wL9NcmtGoF7W8G7qTtnM36giTtM+xujvEol31eF13H8Xa4hm9HfKr
2wIDAQAB
-----END PUBLIC KEY-----`,
  privateKey: `******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`,
  rateLimit: {
    points: 75,
    duration: 60
  }
});

initSiteConfig({
  siteId: 'marketplace',
  publicKey: `-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAu++NnFubdw4OOCzDHLSi
h1HTVJuwwoGvmiwxYwoHuzNcogkLhKQQFEQZFAQD1bArZjgMbS+RGKIbNfdIYRFu
50/donwITajY6WHJ4xTaPD2siFtgsOgwrRyxfxtesVGuGV+RRrmDHhdkE4ubPbtf
w6pxRSV2VXbYfc1j0Xkvbr9hfcqqULsQ3aSl5tmEcrY0HZzw/YX+nP01wUXdpTR4
bu+95Ep4rJdZBvlwwQbbsZjQesHJVGpKAzjx6pVQqh2nI8Av4q17k97dKcpuJ6NV
dil7VUyzob1n1qZMw6iKJ+Q1HNCdEkmgqxniRAa6PL3p70rvNXoxb2Ge6d6xq2BZ
KQIDAQAB
-----END PUBLIC KEY-----`,
  privateKey: `***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`,
  rateLimit: {
    points: 100,
    duration: 60
  }
}); 