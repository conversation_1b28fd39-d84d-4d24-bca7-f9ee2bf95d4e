'use client';

import * as React from 'react';
import { useState, useEffect, useRef } from 'react';
import { Scale, Loader2 } from 'lucide-react';
import { wmsApi } from '@/utils/wmsApiClient';

import { cn } from '@/lib/utils';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
  getCyberAgentPortalContainer,
} from "@/components/ui/select";

interface ItemUomOption {
  id: string;
  name: string;
  uomId: string;
  isDefaultUom?: boolean;
  status?: string;
}

interface ItemMasterUomSelectorProps {
  value?: string;
  onChange: (value: string, uomData?: ItemUomOption) => void;
  placeholder?: string;
  disabled?: boolean;
  required?: boolean;
  itemId?: string; // 依赖的物品ID
  apiHeaders?: Record<string, string>;
  defaultValue?: string;
}

// 定义WMS API响应类型
interface WmsApiResponse {
  success: boolean;
  msg?: string;
  data?: any[] | {
    list?: any[];
    [key: string]: any;
  };
  [key: string]: any;
}

export function ItemMasterUomSelector({
  value,
  onChange,
  placeholder = 'Select UOM',
  disabled = false,
  required = false,
  itemId,
  apiHeaders = {},
  defaultValue
}: ItemMasterUomSelectorProps) {
  // Fixed API path for item UOM
  const API_PATH = 'wms-bam/forward/itemmaster/item-uom/search';
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [options, setOptions] = useState<ItemUomOption[]>([]);
  const [selectedUom, setSelectedUom] = useState<ItemUomOption | null>(null);
  const initialLoadRef = useRef(false);
  const loadedItemIdRef = useRef<string | null>(null);

  // 当itemId变化时，加载该物品的计量单位
  useEffect(() => {
    if (itemId && itemId !== loadedItemIdRef.current) {
      loadedItemIdRef.current = itemId;
      loadUomsForItem(itemId);
    } else if (!itemId) {
      setOptions([]);
      if (value) {
        setSelectedUom(null);
        onChange('');
      }
    }
  }, [itemId]);

  // 初始化时如果有defaultValue但没有value，且有itemId，则设置默认值
  useEffect(() => {
    if (!initialLoadRef.current && defaultValue && !value && itemId) {
      initialLoadRef.current = true;
      loadUomsForItem(itemId);
    }
  }, [defaultValue, value, itemId]);

  // 当value变化时，如果已有选中的UOM ID与新value不同，清除已选UOM数据
  useEffect(() => {
    if (value !== selectedUom?.id) {
      setSelectedUom(null);
    }
  }, [value]);

  // 提取UOM数据的函数
  const extractUomData = (uom: any): ItemUomOption => {
    return {
      id: uom.id || uom.uomId || '',
      name: uom.name || uom.uomId || 'Unknown',
      uomId: uom.uomId || '',
      isDefaultUom: uom.isDefaultUom || false,
      status: uom.status || ''
    };
  };

  // 加载物品的计量单位
  const loadUomsForItem = async (id: string): Promise<void> => {
    if (!id) return;

    try {
      setLoading(true);
      console.log('Loading UOMs for item:', id);

      const response = await wmsApi.post<WmsApiResponse>(API_PATH, {
        itemId: id,
        status: "ENABLE",
        scenario: "AUTO_COMPLETE"
      });

      console.log('Item UOM response:', response);

      if (response.success) {
        // 解析API响应
        const uomList = parseApiResponse(response);
        console.log('Parsed UOM data:', uomList);

        if (uomList && uomList.length > 0) {
          // 将UOM转换为下拉选项
          const uomOptions = uomList.map((uom: any) => extractUomData(uom));
          console.log('UOM options:', uomOptions);
          
          setOptions(uomOptions);

          // 如果有defaultValue，尝试找到匹配的选项
          if (defaultValue && !value) {
            const defaultUom = uomOptions.find(uom => uom.id === defaultValue || uom.uomId === defaultValue);
            if (defaultUom) {
              console.log('Setting default UOM:', defaultUom);
              setSelectedUom(defaultUom);
              onChange(defaultUom.id, defaultUom);
            } else {
              // 如果没有找到匹配的默认值，尝试使用默认UOM
              const defaultUom = uomOptions.find(uom => uom.isDefaultUom);
              if (defaultUom) {
                console.log('Setting default UOM from isDefaultUom flag:', defaultUom);
                setSelectedUom(defaultUom);
                onChange(defaultUom.id, defaultUom);
              }
            }
          }
          // 如果当前选择的值在新的选项中不存在，清除它
          else if (value && !uomOptions.some(option => option.id === value)) {
            onChange('');
          }
          // 如果有value但没有selectedUom，尝试从新选项中找到它
          else if (value && !selectedUom) {
            const matchingOption = uomOptions.find(option => option.id === value);
            if (matchingOption) {
              setSelectedUom(matchingOption);
            }
          }
        } else {
          console.warn('No UOMs found for item:', id);
          setOptions([]);
          // 清除当前选择的值，因为没有可用的选项
          if (value) {
            onChange('');
          }
        }
      } else {
        console.error('Error fetching UOM data:', response.msg);
        setOptions([]);
      }
    } catch (error) {
      console.error('Error loading UOMs:', error);
      setOptions([]);
    } finally {
      setLoading(false);
    }
  };

  // 解析API响应的函数
  const parseApiResponse = (response: any): any[] => {
    // 检查response.data是数组的情况
    if (Array.isArray(response.data)) {
      return response.data;
    }

    // 检查response.data.list的情况
    if (response.data && Array.isArray(response.data.list)) {
      return response.data.list;
    }

    // 检查response.data是对象但包含data字段的情况
    if (response.data && response.data.data && Array.isArray(response.data.data)) {
      return response.data.data;
    }

    // 直接检查response是否包含data字段为数组的情况
    if (response.data && Array.isArray(response.data)) {
      return response.data;
    }

    console.warn('Could not extract data from API response:', response);
    return [];
  };

  // 处理UOM选择
  const handleUomSelect = (uomId: string) => {
    const uom = options.find(opt => opt.id === uomId);
    if (uom) {
      setSelectedUom(uom);
      onChange(uom.id, uom);
      setOpen(false);
    }
  };

  // 打开/关闭下拉框
  const handleOpenChange = (isOpen: boolean) => {
    setOpen(isOpen);
  };

  return (
    <div className="relative w-full">
      <Select
        value={value}
        onValueChange={handleUomSelect}
        open={open}
        onOpenChange={handleOpenChange}
        disabled={disabled || !itemId || options.length === 0}
      >
        <SelectTrigger
          className={cn(
            "w-full h-10 rounded-md bg-transparent",
            "border border-slate-700/50",
            "hover:border-slate-600 focus:border-slate-500",
            "flex items-center justify-between px-3 py-2 text-sm text-slate-200",
            "focus:outline-none focus:ring-0 focus:ring-offset-0",
            "data-[placeholder]:text-slate-400"
          )}
        >
          <SelectValue placeholder={!itemId ? "Select item first" : placeholder}>
            {loading ? (
              <div className="flex items-center">
                <Loader2 className="mr-2 h-4 w-4 animate-spin text-slate-400" />
                <span>Loading...</span>
              </div>
            ) : selectedUom ? (
              <div className="flex items-center">
                <Scale className="mr-2 h-4 w-4 text-slate-400" />
                <span>{selectedUom.name}</span>
              </div>
            ) : null}
          </SelectValue>
        </SelectTrigger>
        <SelectContent
          className={cn(
            "border border-slate-700/70 bg-slate-800/90 text-slate-200",
            "rounded-md shadow-lg min-w-[300px] overflow-hidden",
            "animate-in fade-in-80 zoom-in-95"
          )}
          portalContainer={getCyberAgentPortalContainer()}
        >
          {loading ? (
            <div className="flex items-center justify-center py-6">
              <Loader2 className="h-6 w-6 animate-spin text-slate-400" />
            </div>
          ) : options.length > 0 ? (
            <SelectGroup>
              <SelectLabel className="px-3 text-xs font-medium text-slate-400">UOM</SelectLabel>
              {options.map((uom) => (
                <SelectItem
                  key={uom.id}
                  value={uom.id}
                  className={cn(
                    "py-2 px-3 cursor-pointer text-slate-200",
                    "focus:bg-slate-700/70 focus:text-slate-200",
                    "data-[highlighted]:bg-slate-700/70 data-[highlighted]:text-slate-200"
                  )}
                >
                  <div className="flex items-center">
                    <Scale className="mr-2 h-4 w-4 text-slate-400" />
                    <div className="flex flex-col">
                      <div>{uom.name}</div>
                      {uom.isDefaultUom && (
                        <div className="text-xs text-blue-400">Default</div>
                      )}
                    </div>
                  </div>
                </SelectItem>
              ))}
            </SelectGroup>
          ) : !itemId ? (
            <div className="py-6 text-center text-sm text-slate-400">
              Please select an item first
            </div>
          ) : (
            <div className="py-6 text-center text-sm text-slate-400">
              No UOMs available for this item
            </div>
          )}
        </SelectContent>
      </Select>
    </div>
  );
}
