{"openapi": "3.0.1", "info": {"title": "FMS", "description": "", "version": "1.0.0"}, "paths": {"/fms-platform-user/Tractor/GetTractorByCurrentCompany": {"get": {"summary": "Get the data of the Tractor drop-down box under the current company\r\nforeign", "deprecated": false, "description": "", "tags": ["Tractor"], "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ObjectListResultDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-user/Tractor/GetTrailerByCurrentCompany": {"get": {"summary": "Get the data of the Trailer drop-down box under the current company\r\nforeign", "deprecated": false, "description": "", "tags": ["Tractor"], "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ObjectListResultDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-user/Tractor/GetTractorTest": {"get": {"summary": "/fms-platform-user/Tractor/GetTractorTest", "deprecated": false, "description": "", "tags": ["Tractor"], "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "string"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/shared/bam/asset/search-by-paging": {"post": {"summary": "asset/search-by-paging", "deprecated": false, "description": "", "tags": [], "parameters": [{"name": "Authorization", "in": "header", "description": "", "required": true, "example": "Basic eW1zb2NyX3VzZXI6TG1DbzJi", "schema": {"type": "string"}}, {"name": "User-Agent", "in": "header", "description": "", "required": true, "example": "Apifox/1.0.0 (https://apifox.com)", "schema": {"type": "string"}}, {"name": "Content-Type", "in": "header", "description": "", "required": true, "example": "application/json", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"paging": {"type": "object", "properties": {"pageNo": {"type": "integer"}, "limit": {"type": "integer"}}, "required": ["pageNo", "limit"]}, "status": {"type": "string"}, "categoryId": {"type": "string"}, "itemSpecId": {"type": "string"}, "facilityId": {"type": "string"}, "accountingId": {"type": "string"}, "usingCompany": {"type": "string"}, "brand": {"type": "string"}, "uniqueCode": {"type": "string"}}, "required": ["paging", "status", "categoryId", "itemSpecId", "facilityId", "accountingId", "usingCompany", "brand", "uniqueCode"]}, "example": {"paging": {"pageNo": 1, "limit": 10}, "status": "ACTIVE", "categoryId": "categoryId_d5a293b5b2f0", "itemSpecId": "itemSpecId_b5c70bd983f0", "facilityId": "facilityId_88f52cd9b949", "accountingId": "accountingId_7879672c48c4", "usingCompany": "IT", "brand": "brand_b811561ec2c0", "uniqueCode": "uniqueCode_d728d513fcc0"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"paging": {"type": "object", "properties": {"totalCount": {"type": "integer"}, "pageNo": {"type": "integer"}, "totalPage": {"type": "integer"}, "startIndex": {"type": "integer"}, "endIndex": {"type": "integer"}, "limit": {"type": "integer"}}, "required": ["totalCount", "pageNo", "totalPage", "startIndex", "endIndex", "limit"]}, "assetViews": {"type": "array", "items": {"type": "object", "properties": {"assetId": {"type": "string"}, "itemSpecId": {"type": "string"}, "categoryId": {"type": "string"}, "status": {"type": "string"}, "facilityId": {"type": "string"}, "usingCompany": {"type": "string"}, "ownership": {"type": "string"}, "ownershipType": {"type": "string"}, "imageFileIds": {"type": "array", "items": {"type": "string"}}, "assetFields": {"type": "array", "items": {"type": "object", "properties": {"propertyId": {"type": "string"}, "value": {"type": "string"}, "isRequired": {"type": "boolean"}}, "required": ["propertyId", "value", "isRequired"]}}, "fixedAsset": {"type": "boolean"}, "accountingStatus": {"type": "string"}, "uniqueCode": {"type": "string"}, "categoryName": {"type": "string"}, "itemName": {"type": "string"}, "itemBrand": {"type": "string"}, "itemDesc": {"type": "string"}, "facilityName": {"type": "string"}}}}}, "required": ["paging", "assetViews"]}}}, "headers": {}}}, "security": [{"apikey-header-Authorization": []}]}}, "/shared/bam/asset/category/search-by-paging": {"post": {"summary": "asset/category/search-by-paging", "deprecated": false, "description": "", "tags": [], "parameters": [{"name": "accept", "in": "header", "description": "", "required": true, "example": "application/json", "schema": {"type": "string"}}, {"name": "Content-Type", "in": "header", "description": "", "required": true, "example": "application/json", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"paging": {"type": "object", "properties": {"pageNo": {"type": "integer"}, "limit": {"type": "integer"}}, "required": ["pageNo", "limit"]}, "name": {"type": "string"}}, "required": ["paging", "name"]}, "example": {"paging": {"pageNo": 1, "limit": 10}, "name": ""}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {}}}}, "headers": {}}}, "security": [{"apikey-header-Authorization": []}]}}, "/shared/bam/asset/item/search-by-paging": {"post": {"summary": "asset/item/search-by-paging", "deprecated": false, "description": "", "tags": [], "parameters": [{"name": "accept", "in": "header", "description": "", "required": true, "example": "application/json", "schema": {"type": "string"}}, {"name": "Content-Type", "in": "header", "description": "", "required": true, "example": "application/json", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"paging": {"type": "object", "properties": {"pageNo": {"type": "integer"}, "limit": {"type": "integer"}}, "required": ["pageNo", "limit"]}, "name": {"type": "string"}}, "required": ["paging", "name"]}, "example": {"paging": {"pageNo": 1, "limit": 10}, "name": "iPhone 007"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {}}}}, "headers": {}}}, "security": [{"apikey-header-Authorization": []}]}}, "/location/v1/district/state/list": {"get": {"summary": "Query state", "deprecated": false, "description": "", "tags": [], "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/location/v1/district/city/list": {"get": {"summary": "Query the city", "deprecated": false, "description": "", "tags": [], "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/location/v1/district/zipCode/list": {"get": {"summary": "Query Zipcode", "deprecated": false, "description": "", "tags": [], "parameters": [{"name": "zip_code", "in": "query", "description": "", "required": false, "example": "90", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/location/v1/terminal/list": {"get": {"summary": "Terminal list by company", "deprecated": false, "description": "", "tags": [], "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/api/cp/report-center/gis/lso-service-area/search": {"post": {"summary": "Query polygon under Terminal", "deprecated": false, "description": "", "tags": [], "parameters": [{"name": "authorization", "in": "header", "description": "", "required": true, "example": "1c408e1e-f273-466f-8f76-44e3a8c1217a", "schema": {"type": "string"}}, {"name": "wise-company-id", "in": "header", "description": "", "required": true, "example": "CO20273", "schema": {"type": "string"}}, {"name": "wise-facility-id", "in": "header", "description": "", "required": true, "example": "CO20273_F1", "schema": {"type": "string"}}, {"name": "Content-Type", "in": "header", "description": "", "required": true, "example": "application/json", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"serviceCenterName": {"type": "string"}, "notNeedPolygons": {"type": "boolean"}}, "required": ["serviceCenterName", "notNeedPolygons"]}, "example": {"serviceCenterName": "LAX", "notNeedPolygons": true}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/apinew/transit/info": {"get": {"summary": "transit day", "deprecated": false, "description": "", "tags": [], "parameters": [{"name": "Content-Type", "in": "header", "description": "", "required": true, "example": "application/json", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"TIME": {"type": "string"}, "KEY": {"type": "string"}, "CHECK_CODE": {"type": "string"}, "HASH_CODE": {"type": "string"}, "SCAC": {"type": "string"}, "from_terminal_code": {"type": "string"}, "to_terminal_code": {"type": "string"}, "company_id": {"type": "integer"}}, "required": ["TIME", "KEY", "CHECK_CODE", "HASH_CODE", "SCAC", "from_terminal_code", "to_terminal_code", "company_id"]}, "example": {"TIME": "2024-08-06 23:10:13", "KEY": "bj_serivice_app_api_security", "CHECK_CODE": "746cae1720359889178fc9489d279af6", "HASH_CODE": "47ff57d15b1e7bff7c5c0b5385cc1ce4", "SCAC": "SBFH", "from_terminal_code": "ANG", "to_terminal_code": "HAY", "company_id": 23}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch/openjob/open-query": {"get": {"summary": "Open list display data", "deprecated": false, "description": "", "tags": ["OpenJob"], "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "additionalProperties": {"type": "string"}, "properties": {}}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch/openjob/accepeted-query": {"get": {"summary": "Accepted list display data", "deprecated": false, "description": "", "tags": ["OpenJob"], "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "additionalProperties": {"type": "string"}, "properties": {}}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch/openjob/assigned-query": {"get": {"summary": "Assigned list display data", "deprecated": false, "description": "", "tags": ["OpenJob"], "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "additionalProperties": {"type": "string"}, "properties": {}}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch/openjob/dispatched-query": {"get": {"summary": "Dispatched list display data", "deprecated": false, "description": "", "tags": ["OpenJob"], "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "additionalProperties": {"type": "string"}, "properties": {}}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch/openjob/openjob-accept": {"get": {"summary": "OpneJob Accept button trigger logic", "deprecated": false, "description": "", "tags": ["OpenJob"], "parameters": [{"name": "openJobId", "in": "query", "description": "", "required": false, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {}}}}, "headers": {}}}, "security": [{"bearer": []}]}}, "/fms-platform-dispatch/openjob/gettask_info": {"get": {"summary": "/fms-platform-dispatch/openjob/gettask_info", "deprecated": false, "description": "", "tags": ["OpenJob"], "parameters": [{"name": "orderNo", "in": "query", "description": "", "required": false, "schema": {"type": "string"}}, {"name": "taskType", "in": "query", "description": "", "required": false, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TaskInfoDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch/openjob/gettaskinfoby_tripno/{tripNo}": {"get": {"summary": "/fms-platform-dispatch/openjob/gettaskinfoby_tripno/{tripNo}", "deprecated": false, "description": "", "tags": ["OpenJob"], "parameters": [{"name": "tripNo", "in": "path", "description": "", "required": true, "example": 0, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TaskInfoRpcDto"}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch/my-tripsES": {"get": {"summary": "My Trip page, get list data (ES method)", "deprecated": false, "description": "", "tags": ["Trips"], "parameters": [{"name": "<PERSON><PERSON>", "in": "query", "description": "", "required": false, "schema": {"type": "integer", "format": "int32"}}, {"name": "MaxResoultCount", "in": "query", "description": "", "required": false, "schema": {"type": "integer", "format": "int32"}}, {"name": "Status", "in": "query", "description": "", "required": false, "schema": {"$ref": "#/components/schemas/TripInfoEnum"}}, {"name": "Filter", "in": "query", "description": "", "required": false, "schema": {"$ref": "#/components/schemas/SearchBy"}}, {"name": "Timing", "in": "query", "description": "", "required": false, "schema": {"$ref": "#/components/schemas/EventTiming"}}, {"name": "DateRange", "in": "query", "description": "", "required": false, "schema": {"type": "array", "items": {"type": "string", "format": "date-time"}}}, {"name": "Keyword", "in": "query", "description": "", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MyTripListItemDtoPagedResultDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}}, "components": {"schemas": {"EventTiming": {"enum": [1, 2, 3, 4], "type": "integer", "format": "int32"}, "MyTripListItemDto": {"type": "object", "properties": {"status": {"$ref": "#/components/schemas/TripStatusEnum"}, "trip_no": {"type": "integer", "format": "int64", "nullable": true}, "dispatch_terminal": {"type": "string", "nullable": true}, "dst_terminal": {"type": "string", "nullable": true}, "duration": {"type": "string", "nullable": true}, "total_distance": {"type": "number", "format": "double"}, "delivery_task": {"$ref": "#/components/schemas/TripTaskDto"}, "pickup_task": {"$ref": "#/components/schemas/TripTaskDto"}, "service_task": {"$ref": "#/components/schemas/TripTaskDto"}, "p_with_d_task": {"$ref": "#/components/schemas/TripTaskDto"}, "lh_task": {"$ref": "#/components/schemas/TripTaskDto"}, "stop": {"$ref": "#/components/schemas/TripStopDto"}, "trip_name": {"type": "string", "nullable": true}, "dispatch_date": {"type": "string", "format": "date-time"}, "start_time": {"type": "string", "format": "date-time"}, "is_offline": {"type": "integer", "format": "int32"}, "companion": {"type": "string", "nullable": true}, "is_add_companion": {"type": "boolean"}, "tractor": {"type": "string", "nullable": true}, "trailer": {"type": "string", "nullable": true}, "is_tcl": {"type": "boolean"}, "is_ltl": {"type": "boolean"}, "is_ftl": {"type": "boolean"}, "is_lh": {"type": "boolean"}, "is_first_org_terminal": {"type": "boolean"}, "actual_update_time": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "MyTripListItemDtoPagedResultDto": {"type": "object", "properties": {"total_count": {"type": "integer", "format": "int64"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/MyTripListItemDto"}, "nullable": true}}, "additionalProperties": false}, "ObjectListResultDto": {"type": "object", "properties": {"items": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "Package": {"type": "object", "properties": {"package_id": {"type": "integer", "format": "int64"}, "package_no": {"type": "string", "nullable": true}, "order_key": {"type": "integer", "format": "int64"}, "task_no": {"type": "integer", "format": "int64"}, "order_no": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Pallet": {"type": "object", "properties": {"pallet_no": {"type": "string", "nullable": true}, "pallet_seq": {"type": "string", "nullable": true}, "length": {"type": "integer", "format": "int32", "nullable": true}, "width": {"type": "integer", "format": "int32", "nullable": true}, "height": {"type": "integer", "format": "int32", "nullable": true}, "weight": {"type": "number", "format": "float", "nullable": true}, "file_ids": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "SearchBy": {"enum": [1, 2, 3, 4], "type": "integer", "format": "int32"}, "TaskInfoDto": {"type": "object", "properties": {"task_no": {"type": "integer", "format": "int64"}}, "additionalProperties": false}, "TaskInfoRpcDto": {"type": "object", "properties": {"task_no": {"type": "integer", "format": "int64"}, "task_type": {"type": "integer", "format": "int32"}, "task_org_terminal": {"type": "string", "nullable": true}, "task_dst_terminal": {"type": "string", "nullable": true}, "shipment_order_no": {"type": "string", "nullable": true}, "order_key": {"type": "integer", "format": "int64"}, "task_time_begin": {"type": "string", "format": "date-time"}, "from_lat": {"type": "number", "format": "double"}, "from_lng": {"type": "number", "format": "double"}, "drive_name": {"type": "string", "nullable": true}, "carrier_name": {"type": "string", "nullable": true}, "to_lat": {"type": "number", "format": "double"}, "to_lng": {"type": "number", "format": "double"}, "packages": {"type": "array", "items": {"$ref": "#/components/schemas/Package"}, "nullable": true}, "pallets": {"type": "array", "items": {"$ref": "#/components/schemas/Pallet"}, "nullable": true}}, "additionalProperties": false}, "TripStopDto": {"type": "object", "properties": {"total": {"type": "integer", "format": "int32"}, "completed": {"type": "integer", "format": "int32"}, "dry_run": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "TripTaskDto": {"type": "object", "properties": {"total": {"type": "integer", "format": "int32"}, "completed": {"type": "integer", "format": "int32"}, "dry_run": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "TripStatusEnum": {"enum": ["Pre Plan", "Dispatched", "Check In", "In Progress", "Canceled", "Complete", "Reviewed"], "type": "string"}, "TripInfoEnum": {"enum": [2, 3, 4, 5, 6, 10], "type": "integer", "format": "int32"}}}}