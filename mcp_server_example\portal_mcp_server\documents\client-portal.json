{"openapi": "3.1.0", "info": {"title": "Client Portal Related APIs", "version": "2.0"}, "paths": {"/seabrook/cp/report-center/outbound/outbound-inquiry-report/order-level/search-by-paging": {"post": {"summary": "Search outbound orders by paging", "operationId": "searchOutboundOrders", "tags": ["Report Center"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"paging": {"type": "object", "properties": {"pageNo": {"type": "integer", "example": 1}, "limit": {"type": "integer", "example": 10}}}, "reportCategory": {"type": "string", "required": true, "example": "OUTBOUND_INQUIRY"}, "loadStatuses": {"type": "array", "items": {"type": "string"}, "example": ["Loading"]}, "statuses": {"type": "array", "items": {"type": "string"}, "example": ["Planned"]}, "billingGrades": {"type": "array", "items": {"type": "string"}, "example": ["REGULAR"]}, "retailerIds": {"type": "array", "items": {"type": "string"}, "example": ["ORG-11976"]}, "customerId": {"type": "string", "example": "ORG-424390"}, "headerList": {"type": "array", "items": {"type": "string"}, "example": ["Facility", "Customer", "Order #", "Status", "<PERSON>", "Order Type", "Load #", "PO #", "Order Note", "Shipping Account No", "Source", "Ship Not Before", "BOL Note", "Routing Status", "Ship Not Later", "Hold At Location", "Hold At Carrier", "Consolidation No", "Ref.#", "Equipment Type", "Appointment Time", "Desired Schedule Date", "Delivery Request Date", "Shipped Time", "Carrier", "SCAC Code", "Delivery Service", "Pro #", "Freight Term", "Retailer Name", "Ship to ZIP Code", "Ship To Name", "Ship To Address", "Sold To Name", "Sold To Address", "Freight Bill To Name", "Label Notes", "InvoiceNo", "Order Created By", "Order Dynamic Property", "Shipped Date", "Created Date", "Create Time", "Update Time", "Load ID", "ShipToAddress1", "ShipToAddress2", "ShipToCity", "ShipToState", "ShipToZipcode", "ShipToCountry", "SO #", "CUSTOMER BOL", "Invoice#"]}, "keywords": {"type": "array", "items": {"type": "string"}}, "itemKeywords": {"type": "array", "items": {"type": "string"}, "example": ["4"]}, "orderTypeSelect": {"type": "string", "example": "DS"}, "orderType": {"type": "string", "example": "DS"}, "shipMethodSelect": {"type": "string", "example": "TL"}, "shipMethods": {"type": "array", "items": {"type": "string"}, "example": ["TL"]}}}}}}, "parameters": [{"in": "header", "name": "wise-company-id", "schema": {"type": "string"}, "required": true, "example": "ORG-1"}, {"in": "header", "name": "wise-facility-id", "schema": {"type": "string"}, "required": true, "example": "F1"}, {"in": "header", "name": "x-channel", "schema": {"type": "string"}, "required": true, "example": "CLIENT-PORTAL"}, {"in": "header", "name": "x-login-username", "schema": {"type": "string"}, "required": true, "example": "easonc"}], "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"type": "object"}}}}}}, "400": {"description": "Bad request"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}}}}, "/seabrook/report-center/outbound/outbound-inquiry-report/item-level/search-by-paging": {"post": {"summary": "Search outbound order items by paging", "operationId": "searchOutboundOrderItems", "tags": ["Report Center"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"paging": {"type": "object", "properties": {"pageNo": {"type": "integer", "example": 1}, "limit": {"type": "integer", "example": 10}}}, "reportCategory": {"type": "string", "required": true, "example": "OUTBOUND_INQUIRY"}, "billingGrades": {"type": "array", "items": {"type": "string"}}, "retailerIds": {"type": "array", "items": {"type": "string"}}, "customerId": {"type": "string", "example": "ORG-584337"}, "generalDynFields": {"type": "array", "items": {"type": "string"}}, "detailDynFields": {"type": "array", "items": {"type": "string"}}, "headerList": {"type": "array", "items": {"type": "string"}, "example": ["Item ID", "Short Description", "Description", "Lot #", "Grade", "Supplier", "Title", "Order Qty", "Shipped Qty", "UOM", "Order Total CFT", "Shipped Total CFT", "Order Weight", "Shipped Weight", "<PERSON><PERSON>t Q<PERSON>", "Tracking No", "Return Label", "Buyer Item ID", "Unit Price", "unitPriceCurrency", "Units Per Pallet", "EAN/UPC", "Order ItemLine Dynamic Property"]}, "facilityId": {"type": "string", "example": "F19"}, "facilityName": {"type": "string", "example": "seabrook"}, "orderIds": {"type": "array", "items": {"type": "string"}, "example": ["DN-49303"]}}}}}}, "parameters": [{"in": "header", "name": "wise-company-id", "schema": {"type": "string"}, "required": true, "example": "ORG-1"}, {"in": "header", "name": "wise-facility-id", "schema": {"type": "string"}, "required": true, "example": "F19"}, {"in": "header", "name": "x-channel", "schema": {"type": "string"}, "required": true, "example": "CLIENT-PORTAL"}, {"in": "header", "name": "x-login-username", "schema": {"type": "string"}, "required": true, "example": "cyberaiagent"}], "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"type": "object"}}}}}}, "400": {"description": "Bad request"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}}}}, "/seabrook/report-center/inventory/status-report-v2/search-by-paging": {"post": {"summary": "Search inventory status report by paging", "operationId": "searchInventoryStatus", "tags": ["Report Center"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"paging": {"type": "object", "properties": {"pageNo": {"type": "integer", "example": 1}, "limit": {"type": "integer", "example": 10}}}, "reportCategory": {"type": "string", "required": true, "example": "INVENTORY_STATUS"}, "includeZero": {"type": "boolean", "example": false}, "billingGrades": {"type": "array", "items": {"type": "string"}}, "customerId": {"type": "string", "description": "Customer ID , pattern: ORG-XXXXXX, default: ORG-584337", "required": true, "example": "ORG-584337"}, "itemSpecId": {"type": "string", "nullable": true}, "itemSpecIds": {"type": "array", "items": {"type": "string"}, "example": ["ITEM-1001390"]}, "headerList": {"type": "array", "items": {"type": "string"}, "example": ["CompanyID", "Item ID", "UPC Code", "Short Description", "Description", "Is Bundle", "Units/pkg", "UnitCuFt", "Customer", "Title", "Available", "Receiving", "Allocated", "Damaged", "Hold", "Incoming", "Open Order", "On Hand", "Return", "UOM", "Picked<PERSON><PERSON>", "ReceivedQtyForPast24Hours", "ShippedQtyForPast24Hours", "Total Cuft", "Time Stamp"]}}}}}}, "parameters": [{"in": "header", "name": "wise-company-id", "schema": {"type": "string"}, "required": true, "example": "ORG-1"}, {"in": "header", "name": "wise-facility-id", "schema": {"type": "string"}, "required": true, "example": "F19"}, {"in": "header", "name": "x-channel", "schema": {"type": "string"}, "required": true, "example": "CLIENT-PORTAL"}, {"in": "header", "name": "x-login-username", "schema": {"type": "string"}, "required": true, "example": "cyberaiagent"}], "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"type": "object"}}}}}}, "400": {"description": "Bad request"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}}}}}}