#!/usr/bin/env node

/**
 * WMS Agent A2A协作功能测试
 * 测试WMS Agent的agent发现和协作能力
 */

const http = require('http');

async function testWMSA2ACapabilities() {
  console.log('🔍 Testing WMS Agent A2A Collaboration Capabilities...\n');
  console.log('=' .repeat(60));
  
  try {
    // 1. 测试WMS Agent的Agent Card
    console.log('1️⃣ Testing WMS Agent Card...');
    const wmsCardResult = await testAgentCard('http://localhost:3001/.well-known/agent.json');
    
    if (!wmsCardResult.success) {
      console.log('❌ WMS Agent Card test failed:', wmsCardResult.error);
      return;
    }
    
    console.log('✅ WMS Agent Card test passed');
    console.log(`   Name: ${wmsCardResult.agentCard.name}`);
    console.log(`   Capabilities: ${wmsCardResult.agentCard.capabilities?.length || 0}`);
    console.log(`   Skills: ${wmsCardResult.agentCard.skills?.length || 0}`);
    
    // 2. 测试WMS Agent的发现API
    console.log('\n2️⃣ Testing WMS Agent Discovery API...');
    const wmsDiscoveryResult = await testDiscoveryAPI('http://localhost:3001');
    
    if (!wmsDiscoveryResult.success) {
      console.log('❌ WMS Discovery API test failed:', wmsDiscoveryResult.error);
    } else {
      console.log('✅ WMS Discovery API test passed');
      console.log(`   WMS discovered ${wmsDiscoveryResult.agents.length} other agents`);
      
      // 显示WMS发现的agents
      wmsDiscoveryResult.agents.forEach(agent => {
        const status = agent.status === 'online' ? '✅' : '❌';
        console.log(`   ${status} ${agent.name} (${agent.type})`);
      });
    }
    
    // 3. 测试WMS Agent健康检查
    console.log('\n3️⃣ Testing WMS Agent Health Check...');
    const wmsHealthResult = await testHealthCheck('http://localhost:3001');
    
    if (wmsHealthResult.success) {
      console.log('✅ WMS Health Check passed');
      console.log(`   Status: ${wmsHealthResult.status}`);
    } else {
      console.log('❌ WMS Health Check failed');
    }
    
    // 4. 测试跨Agent协作场景
    console.log('\n4️⃣ Testing Cross-Agent Collaboration Scenarios...');
    
    // 场景1: WMS发现TMS Agent
    console.log('\n📦 Scenario 1: WMS discovering TMS Agent');
    const tmsDiscovery = await testSpecificAgentDiscovery('http://localhost:3001', 'tms');
    if (tmsDiscovery.success && tmsDiscovery.agents.length > 0) {
      console.log('✅ WMS successfully discovered TMS Agent');
      console.log(`   TMS Agent: ${tmsDiscovery.agents[0].name}`);
    } else {
      console.log('⚠️ TMS Agent not found (may not be running)');
    }
    
    // 场景2: WMS发现Super Agent
    console.log('\n🎯 Scenario 2: WMS discovering Super Agent');
    const superDiscovery = await testSpecificAgentDiscovery('http://localhost:3001', 'super');
    if (superDiscovery.success && superDiscovery.agents.length > 0) {
      console.log('✅ WMS successfully discovered Super Agent');
      console.log(`   Super Agent: ${superDiscovery.agents[0].name}`);
    } else {
      console.log('⚠️ Super Agent not found (may not be running)');
    }
    
    console.log('\n' + '=' .repeat(60));
    console.log('🎉 WMS Agent A2A Collaboration Test Completed!');
    console.log('=' .repeat(60));
    
    // 总结
    console.log('\n📊 Test Summary:');
    console.log(`✅ WMS Agent Card: ${wmsCardResult.success ? 'PASS' : 'FAIL'}`);
    console.log(`✅ WMS Discovery API: ${wmsDiscoveryResult.success ? 'PASS' : 'FAIL'}`);
    console.log(`✅ WMS Health Check: ${wmsHealthResult.success ? 'PASS' : 'FAIL'}`);
    console.log(`✅ Cross-Agent Discovery: ${(tmsDiscovery.success || superDiscovery.success) ? 'PASS' : 'FAIL'}`);
    
  } catch (error) {
    console.error('❌ Test failed:', error);
    process.exit(1);
  }
}

// 测试Agent Card
function testAgentCard(agentCardUrl) {
  return new Promise((resolve) => {
    const req = http.get(agentCardUrl, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          if (res.statusCode === 200) {
            const agentCard = JSON.parse(data);
            resolve({
              success: true,
              agentCard
            });
          } else {
            resolve({
              success: false,
              error: `HTTP ${res.statusCode}`,
              response: data
            });
          }
        } catch (error) {
          resolve({
            success: false,
            error: error.message,
            response: data
          });
        }
      });
    });
    
    req.on('error', (error) => {
      resolve({
        success: false,
        error: error.message
      });
    });
    
    req.setTimeout(5000, () => {
      req.destroy();
      resolve({
        success: false,
        error: 'Timeout'
      });
    });
  });
}

// 测试发现API
function testDiscoveryAPI(baseUrl) {
  return new Promise((resolve) => {
    const req = http.get(`${baseUrl}/api/a2a/discovery`, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          if (res.statusCode === 200) {
            const result = JSON.parse(data);
            resolve({
              success: true,
              agents: result.agents || []
            });
          } else {
            resolve({
              success: false,
              error: `HTTP ${res.statusCode}`,
              response: data
            });
          }
        } catch (error) {
          resolve({
            success: false,
            error: error.message,
            response: data
          });
        }
      });
    });
    
    req.on('error', (error) => {
      resolve({
        success: false,
        error: error.message
      });
    });
    
    req.setTimeout(10000, () => {
      req.destroy();
      resolve({
        success: false,
        error: 'Timeout'
      });
    });
  });
}

// 测试健康检查
function testHealthCheck(baseUrl) {
  return new Promise((resolve) => {
    const req = http.get(`${baseUrl}/api/health`, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          if (res.statusCode === 200) {
            const result = JSON.parse(data);
            resolve({
              success: true,
              status: result.status || 'unknown'
            });
          } else {
            resolve({
              success: false,
              error: `HTTP ${res.statusCode}`,
              response: data
            });
          }
        } catch (error) {
          resolve({
            success: false,
            error: error.message,
            response: data
          });
        }
      });
    });
    
    req.on('error', (error) => {
      resolve({
        success: false,
        error: error.message
      });
    });
    
    req.setTimeout(5000, () => {
      req.destroy();
      resolve({
        success: false,
        error: 'Timeout'
      });
    });
  });
}

// 测试特定类型agent发现
function testSpecificAgentDiscovery(baseUrl, agentType) {
  return new Promise((resolve) => {
    const req = http.get(`${baseUrl}/api/a2a/discovery?agentType=${agentType}`, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          if (res.statusCode === 200) {
            const result = JSON.parse(data);
            resolve({
              success: true,
              agents: result.agents || []
            });
          } else {
            resolve({
              success: false,
              error: `HTTP ${res.statusCode}`,
              response: data
            });
          }
        } catch (error) {
          resolve({
            success: false,
            error: error.message,
            response: data
          });
        }
      });
    });
    
    req.on('error', (error) => {
      resolve({
        success: false,
        error: error.message
      });
    });
    
    req.setTimeout(5000, () => {
      req.destroy();
      resolve({
        success: false,
        error: 'Timeout'
      });
    });
  });
}

// 运行测试
testWMSA2ACapabilities().catch(error => {
  console.error('❌ Test failed:', error);
  process.exit(1);
}); 