import React from 'react';

export default function MemoryToolCardEmbed({ toolInvocation }: { toolInvocation: any }) {
  const { result, toolName } = toolInvocation;
  let content = '';
  let title = 'Memory';
  
  if (toolName === 'searchMemoriesTool' && Array.isArray(result?.memories)) {
    content = result.memories.slice(0, 2).map((m: any) => m.content).join('; ');
    title = 'Memory Search';
  } else if (toolName === 'saveMemoryTool' || toolName === 'updateMemoryTool') {
    content = 'Memory saved successfully';
    title = 'Memory Saved';
  } else if (toolName === 'deleteMemoryTool') {
    content = 'Memory deleted successfully';
    title = 'Memory Deleted';
  } else {
    content = result?.message || '';
  }
  
  return (
    <div className="embed-card memory-tool p-1 my-1 bg-gray-800/10 rounded border border-gray-700/30">
      <div className="flex items-center" style={{ whiteSpace: 'nowrap', width: '100%' }}>
        <div className="mr-2 text-gray-400 flex-shrink-0">
          <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
          </svg>
        </div>
        <div className="text-xs text-gray-300 flex-shrink-0 mr-2">{title}:</div>
        <div className="text-xs text-gray-400 overflow-hidden text-ellipsis" style={{ textOverflow: 'ellipsis', overflow: 'hidden', whiteSpace: 'nowrap' }}>
          {content}
        </div>
      </div>
    </div>
  );
} 