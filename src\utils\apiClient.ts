import { clientUserContextManager } from './clientUserContext';

type RequestMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';

interface RequestOptions {
  method?: RequestMethod;
  headers?: Record<string, string>;
  body?: any;
  skipAuth?: boolean;
  addUserId?: boolean;
  responseType?: 'json' | 'blob' | 'text';
}

interface ApiResponse<T> {
  data: T | null;
  error: string | null;
  status: number;
  headers?: Headers;
}

// 正在进行的 token 刷新操作
let refreshPromise: Promise<boolean> | null = null;
// 等待 token 刷新的请求队列
const waitingRequests: (() => void)[] = [];

/**
 * 刷新访问令牌
 * 使用当前的刷新令牌获取新的访问令牌
 */
const refreshToken = async (): Promise<boolean> => {
  try {
    if (!localStorage) return false;
    
    const refreshToken = localStorage.getItem('iam_refresh_token');
    if (!refreshToken) return false;
    
    // 调用刷新令牌API
    const response = await fetch('/api/auth/refresh', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ refreshToken })
    });
    
    if (!response.ok) return false;
    
    const tokens = await response.json();
    
    // 更新存储的令牌
    clientUserContextManager.setAuthToken(tokens.access_token);
    localStorage.setItem('iam_refresh_token', tokens.refresh_token);
    localStorage.setItem('iam_token_expiry', (Date.now() + tokens.expires_in * 1000).toString());
    
    return true;
  } catch (error) {
    console.error('Token刷新失败:', error);
    return false;
  }
};

/**
 * 处理刷新令牌并重试所有等待的请求
 */
const handleTokenRefresh = async (): Promise<boolean> => {
  // 如果已经有刷新操作在进行中，返回该Promise
  if (refreshPromise) return refreshPromise;
  
  // 创建新的刷新操作
  refreshPromise = refreshToken();
  
  try {
    const result = await refreshPromise;
    
    // 处理刷新结果，执行所有等待的请求
    if (result) {
      waitingRequests.forEach(callback => callback());
      waitingRequests.length = 0;
    }
    
    return result;
  } finally {
    refreshPromise = null;
  }
};

/**
 * 通用API请求函数
 * 处理认证、自动刷新令牌和重试
 */
export async function apiRequest<T = any>(
  url: string, 
  options: RequestOptions = {}
): Promise<ApiResponse<T>> {
  const {
    method = 'GET',
    headers = {},
    body,
    skipAuth = false,
    addUserId = false,
    responseType = 'json'
  } = options;
  
  // 准备请求头
  const requestHeaders: Record<string, string> = {
    'Content-Type': 'application/json',
    ...headers
  };
  
  // 添加认证头（除非明确跳过）
  if (!skipAuth) {
    const authHeaders = clientUserContextManager.getAuthHeaders();
    Object.assign(requestHeaders, authHeaders);
  }
  
  // 准备请求配置
  const requestConfig: RequestInit = {
    method,
    headers: requestHeaders,
    credentials: 'same-origin'
  };
  
  // 添加请求体（如果有）
  if (body && method !== 'GET') {
    requestConfig.body = typeof body === 'string' ? body : JSON.stringify(body);
  }
  
  // 创建一个函数，执行实际的API请求
  const executeRequest = async (): Promise<ApiResponse<T>> => {
    try {
      const response = await fetch(url, requestConfig);
      
      // 处理401错误（未授权）- 可能是令牌过期
      if (response.status === 401) {
        // 尝试刷新令牌
        const refreshed = await handleTokenRefresh();
        
        if (refreshed) {
          // 更新认证头
          const newAuthHeaders = clientUserContextManager.getAuthHeaders();
          Object.assign(requestConfig.headers as Record<string, string>, newAuthHeaders);
          
          // 重试请求
          const retryResponse = await fetch(url, requestConfig);
          
          // 如果重试成功，返回数据
          if (retryResponse.ok) {
            const data = await retryResponse.json();
            return { 
              data, 
              error: null, 
              status: retryResponse.status,
              headers: retryResponse.headers 
            };
          } else {
            // 重试仍然失败
            const errorText = await retryResponse.text();
            return { 
              data: null, 
              error: errorText || `请求失败: ${retryResponse.status}`, 
              status: retryResponse.status,
              headers: retryResponse.headers 
            };
          }
        } else {
          // 刷新令牌失败，可能需要重新登录
          return { 
            data: null, 
            error: '会话已过期，请重新登录', 
            status: 401,
            headers: response.headers 
          };
        }
      }
      
      // 处理其他响应
      if (response.ok) {
        let data: any;
        
        // 根据 responseType 处理响应
        switch (responseType) {
          case 'blob':
            data = await response.blob();
            break;
          case 'text':
            data = await response.text();
            break;
          case 'json':
          default:
            const contentType = response.headers.get('content-type');
            if (contentType && contentType.includes('application/json')) {
              data = await response.json();
            } else {
              data = await response.text();
            }
        }
        
        return { 
          data, 
          error: null, 
          status: response.status,
          headers: response.headers 
        };
      } else {
        // 请求失败但不是401
        const errorText = await response.text();
        return { 
          data: null, 
          error: errorText || `请求失败: ${response.status}`, 
          status: response.status,
          headers: response.headers 
        };
      }
    } catch (error) {
      // 网络错误或其他异常
      return { 
        data: null, 
        error: error instanceof Error ? error.message : '请求失败', 
        status: 0 
      };
    }
  };
  
  // 如果当前有token刷新操作，将该请求加入等待队列
  if (refreshPromise) {
    return new Promise<ApiResponse<T>>(resolve => {
      waitingRequests.push(() => {
        // 更新认证头后执行请求
        const newAuthHeaders = clientUserContextManager.getAuthHeaders();
        Object.assign(requestConfig.headers as Record<string, string>, newAuthHeaders);
        
        executeRequest().then(resolve);
      });
    });
  }
  
  // 执行请求
  return executeRequest();
}

// 为常用请求方法提供便捷函数
export const api = {
  get: <T>(url: string, options: Omit<RequestOptions, 'method' | 'body'> = {}) => 
    apiRequest<T>(url, { ...options, method: 'GET' }),
  
  post: <T>(url: string, body: any, options: Omit<RequestOptions, 'method'> = {}) => 
    apiRequest<T>(url, { ...options, method: 'POST', body }),
  
  put: <T>(url: string, body: any, options: Omit<RequestOptions, 'method'> = {}) => 
    apiRequest<T>(url, { ...options, method: 'PUT', body }),
  
  patch: <T>(url: string, body: any, options: Omit<RequestOptions, 'method'> = {}) => 
    apiRequest<T>(url, { ...options, method: 'PATCH', body }),
  
  delete: <T>(url: string, options: Omit<RequestOptions, 'method'> = {}) => 
    apiRequest<T>(url, { ...options, method: 'DELETE' })
};

export default api; 