import { NextRequest, NextResponse } from 'next/server';
import { getUserIdFromRequest } from '@/utils/authUtils';
import * as chatStorage from '@/utils/storage';
import { ChatHistory } from '@/utils/chatHistoryUtils';

// DELETE 请求处理 - 删除用户的所有聊天历史（从索引中移除）
export async function DELETE(req: NextRequest) {
  try {
    console.log('DELETE 请求: 删除用户的所有聊天历史（从索引中移除）');

    // 获取当前用户ID
    const userId = await getUserIdFromRequest(req);

    // 如果未登录，返回错误
    if (!userId) {
      console.log('未获取到用户ID，返回401错误');
      return NextResponse.json(
        { error: '用户未登录或会话已过期' },
        { status: 401 }
      );
    }

    console.log('使用用户ID:', userId);

    // 获取用户的聊天历史索引
    const chatHistories = await chatStorage.getChatHistoryList(userId);

    if (chatHistories.length === 0) {
      console.log('用户没有聊天历史记录');
      return NextResponse.json({ success: true, count: 0 });
    }

    console.log(`找到 ${chatHistories.length} 条聊天历史记录，准备删除`);

    // 逐个删除每个聊天历史
    const deletePromises = chatHistories.map(chat =>
      chatStorage.deleteChat(chat.id, userId)
    );

    // 等待所有删除操作完成
    await Promise.all(deletePromises);

    console.log(`已成功删除用户 ${userId} 的所有聊天历史记录（${chatHistories.length} 条）`);

    return NextResponse.json({
      success: true,
      count: chatHistories.length
    });
  } catch (error: any) {
    console.error('删除所有聊天历史失败:', error);
    return NextResponse.json(
      { error: '删除所有聊天历史失败', details: error?.message || String(error) },
      { status: 500 }
    );
  }
}
