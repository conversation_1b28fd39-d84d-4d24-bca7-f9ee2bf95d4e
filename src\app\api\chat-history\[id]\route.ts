import { NextRequest, NextResponse } from 'next/server';
import { getChatHistory } from '@/utils/serverChatHistoryUtils';
import { getUserIdFromRequest } from '@/utils/authUtils';
import { getSiteToolConfig } from '@/config/index';

// GET 请求处理 - 获取单个聊天历史
export async function GET(
  req: NextRequest
) {
  // 从URL中提取ID参数
  const id = req.nextUrl.pathname.split('/').pop();
  try {
    console.log('GET 请求: 获取单个聊天历史 by ID', id);

    // 获取当前用户ID
    const userId = await getUserIdFromRequest(req);

    // 如果未登录，返回错误
    if (!userId) {
      console.log('未获取到用户ID，返回401错误');
      return NextResponse.json(
        { error: '用户未登录或会话已过期' },
        { status: 401 }
      );
    }

    console.log('使用用户ID:', userId);

    if (!id) {
      console.log('未提供聊天历史ID');
      return NextResponse.json(
        { error: '未提供聊天历史ID' },
        { status: 400 }
      );
    }

    console.log('获取单个聊天历史:', id);
    const chatHistory = await getChatHistory(id, userId);

    if (!chatHistory) {
      // 新增：遍历所有站点ID
      const siteToolConfig = getSiteToolConfig();
      console.log('站点配置:', siteToolConfig);
      console.log('站点配置keys:', Object.keys(siteToolConfig));
      for (const siteId of Object.keys(siteToolConfig)) {
        console.log('遍历站点ID:', siteId);
        const siteChat = await getChatHistory(id, siteId);
        if (siteChat) {
          return NextResponse.json({ ...siteChat, siteLevel: true });
        }
      }
      console.log('未找到聊天历史:', id);
      return NextResponse.json({ error: '未找到聊天历史' }, { status: 404 });
    }

    // 确保只返回属于当前用户的聊天历史
    if (chatHistory.userId && chatHistory.userId !== userId) {
      console.log('聊天历史所有者不匹配:', chatHistory.userId, '!=', userId);
      return NextResponse.json(
        { error: '无权访问该聊天历史' },
        { status: 403 }
      );
    }

    return NextResponse.json(chatHistory);
  } catch (error) {
    console.error('获取聊天历史失败:', error);
    return NextResponse.json(
      { error: '获取聊天历史失败' },
      { status: 500 }
    );
  }
}
