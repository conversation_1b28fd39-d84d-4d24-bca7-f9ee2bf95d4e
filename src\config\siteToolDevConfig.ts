import { SiteToolConfigType } from './types';

export const siteToolDevConfig: SiteToolConfigType = {
  "unis": {
    "mcpServerIds": [],
    "tools": ["sequentialthinking", "kbTool", "quoteTool", "traceOrderTool"]
  },
  "wms": {
    "mcpServerIds": ["wms"],
    "tools": ["sequentialthinking", "find_wms_api", "call_wms_api", "kbTool", "getPageSchema", "domOperator"]
  },
  "fms": {
    "mcpServerIds": ["fms"],
    "tools": ["sequentialthinking", "find_fms_api", "call_fms_api", "getPageSchema", "domOperator"]
  },
  "cubework": {
    "mcpServerIds": [],
    "tools": ["kbTool"]
  },
  "lso": {
    "mcpServerIds": [],
    "tools": ["kbTool", "lsoCalcRateTool", "lsoTrackingTool"]
  },
  "portal": {
    "mcpServerIds": [],
    "tools": ["portalQuoteTool", "portalTrackingOrderTool", "getPageSchema", "domOperator", "portalSaveShippingQuoteTool", "portalGetUserPaymentCardsTool", 
      "portalCreateShippingOrderTool", "portalGetInvoiceDetailTool", "portalSubmitPreparedDisputeTool", "portalUtCustomersTool", "portalUfCustomersTool", 
      "portalAccListTool"]
  },
  "marketplace": {
    "mcpServerIds": [],
    "tools": ["kbtool"]
  }
}; 