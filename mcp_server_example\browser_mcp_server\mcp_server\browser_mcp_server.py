import asyncio
import logging
import os
import sys
import traceback
from typing import Dict, Any, Optional
from contextlib import asynccontextmanager
from collections.abc import AsyncIterator
from dataclasses import dataclass

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
)
logger = logging.getLogger("browser_mcp_server")

# Add parent directory to PATH to import tools
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
print(f"Current working directory: {os.getcwd()}")
print(f"Added to path: {parent_dir}")
sys.path.append(parent_dir)

# Try to import FastMCP and Browser tools
try:
    from mcp.server.fastmcp import FastMCP, Context
    logger.info("Successfully imported MCP FastMCP")
except ImportError as e:
    # If modules not found, try to install
    logger.warning(f"Failed to import required modules: {e}, trying to install...")
    try:
        import subprocess
        subprocess.check_call([sys.executable, "-m", "pip", "install", "mcp[cli]"])
        from mcp.server.fastmcp import FastMCP, Context
        logger.info("Successfully installed and imported MCP")
    except Exception as install_error:
        logger.error(f"Failed to install MCP: {install_error}")
        traceback.print_exc()
        sys.exit(1)

# Ensure config file exists
if not os.path.exists(os.path.join(parent_dir, "config.py")):
    try:
        with open(os.path.join(parent_dir, "config.py"), "w", encoding="utf-8") as f:
            f.write("""import os
from dotenv import load_dotenv

# Try to load environment variables from .env file if it exists
load_dotenv()

# Browser-Use Configuration
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY", "")
BROWSER_HEADLESS = os.getenv("BROWSER_HEADLESS", "true").lower() == "true"
""")
        logger.info("Created default config.py file")
    except Exception as config_error:
        logger.error(f"Failed to create config.py: {config_error}")

# Try to import Browser tools
try:
    from tools.browser_tools import BrowserTools
    logger.info("Successfully imported Browser tools")
except ImportError as e:
    logger.error(f"Failed to import Browser tools: {e}")
    traceback.print_exc()
    sys.exit(1)

# Define BrowserContext class for storing global browser tools instance
@dataclass
class BrowserContext:
    browser_tools: Optional[BrowserTools] = None

# Create server lifecycle manager
@asynccontextmanager
async def browser_lifespan(server: FastMCP) -> AsyncIterator[BrowserContext]:
    """Initialize browser tools instance and clean up resources when server stops"""
    logger.info("Starting Browser MCP Server lifecycle")
    
    # Initialize browser tools
    try:
        logger.info("Initializing Browser tools")
        browser_tools = BrowserTools()
        logger.info("Browser tools initialized successfully")
        
        # Create context object
        context = BrowserContext(browser_tools=browser_tools)
        
        # Add context directly to server app state, making it accessible in tool functions
        if hasattr(server, 'app'):
            if not hasattr(server.app, 'state'):
                server.app.state = type('State', (object,), {})
            server.app.state.browser_context = context
        
        yield context
    except Exception as e:
        logger.error(f"Failed to initialize Browser tools: {e}")
        traceback.print_exc()
        # Continue even if initialization fails, to avoid server startup failure
        yield BrowserContext(browser_tools=None)
    finally:
        # Clean up resources when server shuts down
        logger.info("Shutting down Browser MCP Server")

# Create FastMCP instance with name and lifecycle manager
mcp = FastMCP(
    name="Browser MCP Server", 
    version="1.0.0",
    description="Browser automation and web task execution MCP server",
    lifespan=browser_lifespan,
    dependencies=["browser-use", "langchain-openai", "dotenv"],
    timeout=300  # 增加超时时间为300秒(5分钟)
)

@mcp.tool()
async def perform_web_task(
    task_description: str,
    ctx: Context = None
) -> Dict[str, Any]:
    """
    Perform a web task using browser automation.
    
    Args:
        task_description: Detailed description of the web task to perform
        ctx: MCP context object
        
    Returns:
        Results of the web task
    """
    logger.info(f"Calling perform_web_task with task: {task_description}")
    
    # Get browser tools instance
    browser_tools = None
    if ctx:
        if hasattr(ctx, 'app') and hasattr(ctx.app, 'state') and hasattr(ctx.app.state, 'browser_context'):
            browser_tools = ctx.app.state.browser_context.browser_tools
        elif hasattr(ctx, 'lifespan_ctx'):
            browser_tools = getattr(ctx.lifespan_ctx, 'browser_tools', None)
        elif hasattr(ctx, 'browser_context'):
            browser_tools = ctx.browser_context.browser_tools
        elif hasattr(ctx, 'browser_tools'):
            browser_tools = ctx.browser_tools
    
    # If cannot get from context, create a new instance
    if not browser_tools:
        logger.warning("Could not get Browser tools from context, creating a new instance")
        try:
            from tools.browser_tools import BrowserTools
            browser_tools = BrowserTools()
        except Exception as e:
            error_msg = f"Failed to create Browser tools: {e}"
            logger.error(error_msg)
            raise Exception(error_msg)
    
    try:
        # Call the perform_web_task method
        result = await browser_tools.perform_web_task(task_description=task_description)
        logger.info(f"perform_web_task completed successfully")
        return result
    except Exception as e:
        logger.error(f"Error in perform_web_task execution: {e}")
        traceback.print_exc()
        raise

@mcp.prompt()
def browser_guide() -> str:
    """
    Provide guidance on how to use browser automation tools effectively.
    """
    return """
# Browser MCP Server

This MCP server provides a browser automation tool powered by browser-use library, allowing you to perform virtually any web task using natural language instructions.

## Tool Usage Guide

### perform_web_task
Use this tool for web tasks:
```
perform_web_task(task_description="Go to example.com and extract the main heading")
```

You can describe complex, multi-step tasks:
```
perform_web_task(task_description="Go to amazon.com, search for 'wireless headphones', and extract the names and prices of the top 5 results")
```

## Tips for Effective Use

- Be specific in your requests
- Break complex tasks into clear, sequential steps
- For data extraction, clearly describe what data you need
- URLs must be valid and include http/https protocol
- Provide specific search terms or navigation paths

## Limitations

- Some websites may block automation
- JavaScript-heavy websites may have limited functionality
- Login-required content cannot be accessed
- Task completion times vary based on complexity
"""

@mcp.resource("browser://usage-guide")
def get_browser_usage_guide() -> str:
    """
    Provide a detailed usage guide for browser automation.
    """
    return """
# Browser Automation Usage Guide

## Overview
Browser automation allows you to interact with websites programmatically. This MCP server provides a powerful tool to perform web tasks using natural language instructions.

## Detailed Usage Examples

### Simple Web Tasks
```python
# Extract information from a webpage
result = await perform_web_task(
    task_description="Go to news.ycombinator.com and list the titles of the top 10 stories"
)

# Search and extract data
prices = await perform_web_task(
    task_description="Go to amazon.com, search for 'laptop', and extract the names, prices, and ratings of the first 5 laptops"
)
```

### Complex Web Tasks
```python
# Multi-step navigation and extraction
task_result = await perform_web_task(
    task_description="Go to github.com, search for 'machine learning', filter by Python language, sort by stars, and extract the names, descriptions, and star counts of the top 5 repositories"
)

# Form filling and submission
form_result = await perform_web_task(
    task_description="Go to example.com/contact, fill out the contact form with name='John Doe', email='<EMAIL>', message='Hello, this is a test', and submit the form. Then extract any confirmation message."
)
```

## Best Practices
1. Be specific about what you want to extract
2. Include full URLs with http/https
3. Break complex tasks into clear, sequential steps
4. For forms, specify exact field values
5. Handle errors gracefully by checking the success field in the response

## Common Issues and Solutions
- If a website blocks automation, try using a different approach
- For dynamic content, specify to wait for content to load (e.g., "wait for the search results to load")
- Some websites may require specific navigation patterns
""" 