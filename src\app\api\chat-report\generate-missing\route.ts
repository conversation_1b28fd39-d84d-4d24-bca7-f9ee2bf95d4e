import { NextRequest, NextResponse } from 'next/server';
import { getUserIdFromRequest } from '@/utils/authUtils';
import { manualTriggerDailyReport } from '@/chat-report/scheduler';
import { listAllReports, getLatestReportDate, setLatestReportDate, getLatestReportTimestamp } from '@/chat-report/reportStorage';
import * as chatStorage from '@/utils/storage';

// Helper function to get dates that don't have reports since the latest report timestamp
const getMissingReportDates = async (): Promise<string[]> => {
  // Get the latest report timestamp from backend storage
  let latestReportTimestamp = await getLatestReportTimestamp();
  let latestReportDate = await getLatestReportDate(); // For logging

  // If no stored latest timestamp, try to find it from existing reports
  if (!latestReportTimestamp) {
    const existingReports = await listAllReports();

    if (existingReports.length > 0) {
      // Sort reports by date and get the latest one
      const sortedReports = [...existingReports].sort((a, b) => a.date.localeCompare(b.date));
      latestReportDate = sortedReports[sortedReports.length - 1].date;

      // Convert to end-of-day timestamp
      latestReportTimestamp = `${latestReportDate}T23:59:59.999Z`;

      // Store this date for future use
      if (latestReportDate) {
        await setLatestReportDate(latestReportDate);
      }
    }
  }

  console.log(`Latest report date: ${latestReportDate || 'None'}`);
  console.log(`Latest report timestamp: ${latestReportTimestamp || 'None'}`);

  // 获取所有已存在的报告日期
  const existingReports = await listAllReports();
  const existingReportDates = new Set(existingReports.map(report => report.date));

  // 获取所有聊天记录
  const allChats = await chatStorage.getChatHistoryList();

  // 按日期分组聊天记录
  const chatsByDate = new Map<string, number>();

  // 当前日期，用于限制未来日期
  const today = new Date().toISOString().split('T')[0];

  // 遍历所有聊天记录，按日期分组
  // 同时记录每个日期的最新聊天记录时间戳
  const latestChatTimestampByDate = new Map<string, string>();

  allChats.forEach(chat => {
    let chatDate: string | null = null;
    let chatTimestamp: string | null = null;

    // 从 createdAt 字段获取日期和时间戳
    if (chat.createdAt) {
      chatTimestamp = new Date(chat.createdAt).toISOString();
      chatDate = chatTimestamp.split('T')[0];
    }
    // 从 ID 提取日期（如果格式为 YYYYMMDD-*）
    else if (chat.id) {
      const match = chat.id.match(/^(\d{8})-/);
      if (match) {
        const year = match[1].substring(0, 4);
        const month = match[1].substring(4, 6);
        const day = match[1].substring(6, 8);
        chatDate = `${year}-${month}-${day}`;
        // 如果没有 createdAt，使用当天结束时间作为时间戳
        chatTimestamp = `${chatDate}T23:59:59.999Z`;
      }
    }

    // 如果有效且不是未来日期
    if (chatDate && chatTimestamp && chatDate <= today) {
      // 增加该日期的聊天记录计数
      chatsByDate.set(chatDate, (chatsByDate.get(chatDate) || 0) + 1);

      // 更新该日期的最新聊天记录时间戳
      const currentLatest = latestChatTimestampByDate.get(chatDate) || '';
      if (!currentLatest || chatTimestamp > currentLatest) {
        latestChatTimestampByDate.set(chatDate, chatTimestamp);
      }
    }
  });

  console.log('Latest chat timestamps by date:', Object.fromEntries(latestChatTimestampByDate));

  // 找出需要生成报告的日期
  const missingDates: string[] = [];
  const datesToRegenerate: string[] = [];

  chatsByDate.forEach((count, date) => {
    // 获取该日期的最新聊天记录时间戳
    const latestChatTimestamp = latestChatTimestampByDate.get(date);

    if (!latestChatTimestamp) {
      console.log(`No timestamp found for date ${date}, skipping`);
      return;
    }

    // 如果该日期没有报告且有聊天记录
    if (!existingReportDates.has(date) && count > 0) {
      console.log(`Date ${date} has no report yet, adding to missing dates`);
      missingDates.push(date);
    }
    // 如果该日期有报告，但最新聊天记录时间戳在最新报告时间戳之后
    else if (existingReportDates.has(date) && latestReportTimestamp && latestChatTimestamp > latestReportTimestamp) {
      console.log(`Date ${date} has new chats after the latest report timestamp, adding to regenerate list`);
      console.log(`Latest chat: ${latestChatTimestamp}, Latest report: ${latestReportTimestamp}`);
      datesToRegenerate.push(date);
    }
  });

  // 合并需要生成和需要重新生成的日期
  const allDatesToProcess = [...missingDates, ...datesToRegenerate];
  console.log(`Found ${missingDates.length} dates with no reports and ${datesToRegenerate.length} dates to regenerate`);

  // 按日期排序
  allDatesToProcess.sort();

  console.log(`Found ${allDatesToProcess.length} total dates to process`);
  return allDatesToProcess;
};

// POST - Generate reports for all missing dates
export async function POST(req: NextRequest) {
  try {
    // Get the current user ID
    const userId = await getUserIdFromRequest(req);

    // If not logged in, return error
    if (!userId) {
      return NextResponse.json(
        { error: 'User not logged in or session expired' },
        { status: 401 }
      );
    }

    // 获取所有已存在的报告日期
    const existingReports = await listAllReports();
    const existingReportDates = new Set(existingReports.map(report => report.date));

    // 获取需要生成或重新生成的日期
    const missingDates = await getMissingReportDates();

    if (missingDates.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'No missing reports found and no reports need to be regenerated.'
      });
    }

    // Generate reports for each missing date
    const results = [];
    for (const date of missingDates) {
      try {
        // 不再需要强制重新生成，因为 manualTriggerDailyReport 现在会自动追加新的聊天记录
        const isExistingDate = existingReportDates.has(date);
        console.log(`Processing date ${date}, existing report: ${isExistingDate}`);

        await manualTriggerDailyReport(date);
        results.push({ date, status: 'success', appended: isExistingDate });
      } catch (error) {
        console.error(`Error generating report for ${date}:`, error);
        results.push({ date, status: 'error', error: (error as Error).message });
      }
    }

    // 计算新生成和追加的报告数量
    const successfulResults = results.filter(r => r.status === 'success');
    const newReports = successfulResults.filter(r => !r.appended).length;
    const appendedReports = successfulResults.filter(r => r.appended).length;

    return NextResponse.json({
      success: true,
      message: `Generated ${newReports} new reports and appended to ${appendedReports} existing reports`,
      results
    });
  } catch (error) {
    console.error('Error generating missing reports:', error);
    return NextResponse.json(
      { error: 'Error generating missing reports' },
      { status: 500 }
    );
  }
}
