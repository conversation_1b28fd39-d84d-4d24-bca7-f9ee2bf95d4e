import { tool } from 'ai';
import { z } from 'zod';
import { TemplateFieldType } from '@/tools/types';

/**
 * User Input Request Tool (简化版)
 * 用于 AI 需要用户补充/确认信息时，生成 JSON 表单描述，前端用 dynamic-form.tsx 渲染。
 * 典型场景：AI 解析到需要用户输入/确认参数（如创建/更新 API），生成表单 JSON，传递给本工具，前端渲染表单并收集用户输入。
 */
export const requireUserInputTool = tool({
  description: 'Use this tool whenever you need to collect or confirm user input before making API calls. The tool receives a JSON form schema and returns it to the client for rendering. The client will display a form (using dynamic-form.tsx) and collect user input, then send the result back to continue the workflow. Field names MUST exactly match the API parameter names and be case-sensitive.',
  parameters: z.object({
    title: z.string().describe('Form title'),
    description: z.string().optional().describe('Form description'),
    fields: z.array(
      z.object({
        name: z.string().describe('Field name, must match API parameter'),
        label: z.string().describe('Field label'),
        type: z.enum([
          TemplateFieldType.TEXT,
          TemplateFieldType.TEXTAREA,
          TemplateFieldType.NUMBER,
          TemplateFieldType.SELECT,
          TemplateFieldType.CHECKBOX,
          TemplateFieldType.RADIO,
          TemplateFieldType.DATE,
          TemplateFieldType.DATETIME,
          TemplateFieldType.TIME,
          TemplateFieldType.ARRAY,
          TemplateFieldType.SWITCH,
          TemplateFieldType.ADDRESS,
          'userSelector',
          'customerSelector',
          'titleSelector',
          'generalProjectSelector',
          'jobcodeSelector',
          'carrierSelector',
          'deliveryServiceSelector',
          'itemmasterSelector',
          'itemmasterUomSelector',
          'stateSelector',
          'switch',
          'array',
          'address',
          'portalCustomerSelector',
          'portalInvoiceSelector',
          'accessorialMultipleSelector',
          'multipleUserSelector'
        ]).describe('Field type'),
        required: z.boolean().optional().describe('Is this field required'),
        options: z.array(z.object({ label: z.string(), value: z.string() })).optional().describe('Options for select/radio/checkbox'),
        description: z.string().optional().describe('Field description'),
        defaultValue: z.any().optional().describe('Default value or original value from prior API calls'),
        arrayItemFields: z.array(z.lazy(() => z.object({
          name: z.string().describe('Field name for array item'),
          label: z.string().describe('Field label for array item'),
          type: z.string().describe('Field type for array item'),
          required: z.boolean().optional().describe('Is this field required'),
          options: z.array(z.object({ label: z.string(), value: z.string() })).optional(),
          description: z.string().optional(),
          defaultValue: z.any().optional(),
          dependsOn: z.string().optional().describe('Field name that this field depends on, e.g., jobCode might depend on customer in the form')
        }))).optional().describe('Fields for array items when type is array'),
        apiHeaders: z.record(z.string()).optional().describe('API headers for userSelector component'),
        dependsOn: z.string().optional().describe('Field name that this field depends on, e.g., jobCode might depend on customer in the form'),
        readonly: z.boolean().optional().describe('If true, this field is read-only and cannot be edited by the user default is false')
      })
    ).describe('Form fields array')
  })
});