import { NextRequest, NextResponse } from 'next/server';
import { Message as VercelChatMessage, createDataStreamResponse } from 'ai';
import { openai } from '@ai-sdk/openai';
import { anthropic } from '@ai-sdk/anthropic';
import { google } from '@ai-sdk/google';
import { deepseek } from '@ai-sdk/deepseek';
import { getUserIdFromRequest } from '@/utils/authUtils';

// Import request context handling
import { clearRequestContext, getRequestContext } from '@/utils/requestContext';
import { withRequestContext } from '@/utils/apiWrapper';

// Import Agent system
import { globalAgentManager } from '@/agents/base/AgentManager';
import { SuperAgent } from '@/agents/SuperAgent';
import { WMSAgent } from '@/agents/specialized/WMSAgent';
import { BIAgent } from '@/agents/specialized/BIAgent';
import { AgentExecutionContext } from '@/agents/base/BaseAgent';

// Model mapping: Map the UI display name to the actual API call model name
const MODEL_MAPPING = {
  'gpt-4.1': 'gpt-4.1-2025-04-14', // GPT-4.1模型
  'claude-sonnet-4-0': 'claude-sonnet-4-0', // Claude Sonnet 4 (alias - always latest)
  'gemini-2.5-flash-preview-05-20': 'gemini-2.5-flash-preview-05-20', // Gemini 2.5 Flash Preview
  'deepseek-chat': 'deepseek-chat', // DeepSeek Chat V3 model - 支持函数调用
  'deepseek-reasoner': 'deepseek-reasoner', // DeepSeek Reasoner model - 推理模型
};

// Helper function to filter out invalid attachments
const filterValidAttachments = (messages: any[]) => {
  return messages.map(msg => {
    // Copy message object
    const msgCopy = { ...msg };

    // Process attachments
    if (msgCopy.experimental_attachments && Array.isArray(msgCopy.experimental_attachments)) {
      // Filter out attachments without URL
      msgCopy.experimental_attachments = msgCopy.experimental_attachments.filter(
        (attachment: any) => attachment && attachment.url
      );

      // If no attachments are left, delete this property
      if (msgCopy.experimental_attachments.length === 0) {
        delete msgCopy.experimental_attachments;
      }
    }

    return msgCopy;
  });
};

// 初始化Agent系统
let agentsInitialized = false;

const initializeAgents = async () => {
  if (agentsInitialized) return;
  
  console.log('[Chat] Initializing agent system...');
  
  // 注册所有Agent
  globalAgentManager.registerAgent(new SuperAgent());
  globalAgentManager.registerAgent(new WMSAgent());
  globalAgentManager.registerAgent(new BIAgent());
  
  agentsInitialized = true;
  console.log('[Chat] Agent system initialized');
};

// 使用高阶函数包装API处理程序
export const POST = withRequestContext(async (req: NextRequest, requestId: string) => {
  // 基础安全检查 - 验证是否已认证
  const userId = await getUserIdFromRequest(req);

  // 如果用户未认证，返回401错误
  if (!userId) {
    console.log(`[Chat:${requestId}] Secondary defense - API accessed without valid authentication`);
    return NextResponse.json(
      { error: 'User not authenticated or session expired' },
      { status: 401 }
    );
  }

  console.log(`[Chat:${requestId}] Authenticated user ${userId} accessing chat API`);

  // 初始化Agent系统
  await initializeAgents();

  // 解析请求
  const data = await req.json();
  // 提取消息和可能的模型信息
  let { messages, model: requestModel } = data;

  // 过滤无效附件
  messages = filterValidAttachments(messages);

  // 获取时区信息
  const requestContext = getRequestContext(requestId);
  const timezone = requestContext?.timezone;
  console.log(`[Chat:${requestId}] Client timezone: ${timezone}`);

  // 创建Agent执行上下文（初始不包含dataStream）
  console.log('messages', messages);
  const agentContext: AgentExecutionContext = {
    messages,
    model: requestModel,
    userId,
    requestId,
    timezone
  };

  // 始终使用SuperAgent作为协调者
  const selectedAgent = globalAgentManager.getAgent('super-agent');
  if (!selectedAgent) {
    throw new Error('SuperAgent not found');
  }
  
  console.log(`[Chat:${requestId}] Using SuperAgent as coordinator`);

  // Determine the model instance to use
  let modelInstance;
  let modelProvider;

  if (requestModel?.includes('claude')) {
    modelInstance = anthropic(requestModel);
    modelProvider = 'Anthropic';
  } else if (requestModel?.includes('gemini')) {
    modelInstance = google(requestModel);
    modelProvider = 'Google';
  } else if (requestModel?.includes('deepseek')) {
    modelInstance = deepseek(requestModel);
    modelProvider = 'DeepSeek';
  } else {
    modelInstance = openai(requestModel || 'gpt-4.1-2025-04-14'); // Default to GPT-4.1
    modelProvider = 'OpenAI';
  }

  // Log the model information
  console.log('==== Used model information ====');
  console.log(`Model name: ${requestModel || 'gpt-4.1-2025-04-14'}`);
  console.log(`Model provider: ${modelProvider}`);
  console.log('==== Model information end ====');

  // 使用createDataStreamResponse来支持流合并
  return createDataStreamResponse({
    execute: async (dataStream) => {
      try {
        console.log(`[Chat:${requestId}] Executing SuperAgent with unified stream`);
        
        // 将dataStream添加到context中
        const contextWithStream: AgentExecutionContext = {
          ...agentContext,
          dataStream
        };
        
        // 执行SuperAgent，所有的agent和工具都会写入同一个dataStream
        const result = await selectedAgent.execute(contextWithStream, modelInstance);
        
        console.log(`[Chat:${requestId}] SuperAgent execution started, merging main stream...`);
        
        // 将SuperAgent的结果合并到数据流中
        result.mergeIntoDataStream(dataStream);
        
      } catch (error) {
        console.error(`[Chat:${requestId}] Error in agent execution:`, error);
        console.error(`[Chat:${requestId}] Error details:`, {
          name: error?.name,
          message: error?.message,
          stack: error?.stack,
          cause: error?.cause
        });
        throw error;
      }
    },
    onError: (error) => {
      console.error(`[Chat:${requestId}] Error in createDataStreamResponse:`, error);
      clearRequestContext(requestId);
      return error instanceof Error ? error.message : String(error);
    }
  });
});