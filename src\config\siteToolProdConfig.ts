import { SiteToolConfigType } from './types';

export const siteToolProdConfig: SiteToolConfigType = {
  "marketplace": {
    "mcpServerIds": [],
    "tools": ["kbTool", "getPageSchema", "domOperator"]
  },
  "cuebwork": {
    "mcpServerIds": [],
    "tools": ["kbTool", "getPageSchema", "domOperator"]
  },
  "unis": {
    "mcpServerIds": [],
    "tools": ["kbTool", "tmsShipmentOrderTrackingTool", "tmsQuoteTool", "getPageSchema", "domOperator"]
  },
  "wms": {
    "mcpServerIds": ["wms"],
    "tools": ["sequentialthinking", "find_wms_api", "call_wms_api", "kbtool", "getPageSchema", "domOperator","kbTool"]
  }
}; 