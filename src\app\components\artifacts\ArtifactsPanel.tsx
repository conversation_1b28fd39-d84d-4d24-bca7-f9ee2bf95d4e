'use client';

import React from 'react';
import { useArtifactsStore } from '@/stores/useArtifactsStore';
import { ArtifactRenderer } from './ArtifactRenderer';

export function ArtifactsPanel() {
  const { activeArtifacts: artifacts, removeActiveArtifact: removeArtifact } = useArtifactsStore();

  if (artifacts.length === 0) {
    return null; // 如果没有 artifacts，则不渲染任何东西
  }

  return (
    <div className="flex flex-col h-full border-l border-item-gray-800/50 bg-item-bg-secondary">
      {/* 未来可以实现 Tab 切换来显示不同的 artifact */}
      {artifacts.map((artifact) => (
        <div key={artifact.id} className="flex-grow flex flex-col">
          <div className="p-3 border-b border-item-gray-800/50 bg-item-bg-card flex justify-between items-center">
            <span className="text-sm font-medium text-gray-200">{artifact.title}</span>
            <button 
              onClick={() => removeArtifact(artifact.id)} 
              className="text-gray-400 hover:text-gray-200 p-1 rounded-lg hover:bg-item-bg-hover transition-all duration-200"
            >
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-4 h-4">
                <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          <div className="flex-grow overflow-hidden">
            <ArtifactRenderer artifact={artifact} />
          </div>
        </div>
      ))}
    </div>
  );
}