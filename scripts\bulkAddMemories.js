const fs = require('fs');
const path = require('path');

// Load environment variables at the beginning of the file
const dotenv = require('dotenv');
const envPath = path.resolve(process.cwd(), '.env.local');
dotenv.config({ path: envPath });

// Verify if API Key is loaded
if (!process.env.OPENAI_API_KEY) {
  console.error('Error: Failed to load OPENAI_API_KEY environment variable');
  console.error(`Attempted to load from file path: ${envPath}`);
  console.error('Please ensure .env.local file exists and contains OPENAI_API_KEY');
  process.exit(1);
}

console.log('Environment variables loaded successfully');
const { getMemoryService, MemoryType } = require(path.join(process.cwd(), 'dist/src/services/memoryService'));

/**
 * Add memories to the memory system in bulk
 * @param {string} filePath - Path to the JSON file containing memory data
 * @param {object} options - Additional options
 * @param {boolean} options.infer - Whether to use LLM to infer memory, defaults to true
 * @returns {object} - Statistics of the addition operation
 */
async function bulkAddMemories(filePath, options = {}) {
  const { infer = true } = options;
  
  if (!fs.existsSync(filePath)) {
    throw new Error(`File does not exist: ${filePath}`);
  }
  
  console.log(`Loading memory data from file: ${filePath}`);
  const rawData = fs.readFileSync(filePath, 'utf8');
  const memories = JSON.parse(rawData);
  
  if (!Array.isArray(memories)) {
    throw new Error('File content is not a valid memory array');
  }
  
  console.log(`Preparing to add ${memories.length} memories... infer=${infer}`);
  
  // Get memory service instance
  const memoryService = getMemoryService();
  
  // Track success and failure counts
  const stats = {
    success: 0,
    error: 0,
    total: memories.length
  };
  
  // Process memories in batch, stop on error
  for (const memory of memories) {
    try {
      const { type, content, userId, metadata } = memory;
      
      // Select memory type enum based on type
      const memoryType = type === 'SYSTEM' ? MemoryType.SYSTEM : MemoryType.USER;
      
      // Add to memory system
      await memoryService.addMemory(
        memoryType,
        content,
        userId || 'system',
        metadata || {},
        infer
      );
      
      stats.success++;
    } catch (error) {
      console.error('Failed to add memory, stopping process:', error);
      stats.error++;
      // Stop processing immediately upon error
      break;
    }
  }
  
  console.log(`Bulk memory addition complete - Success: ${stats.success}, Failed: ${stats.error}, Total: ${stats.total}`);
  return stats;
}

// If script is executed directly, use command line arguments as file path
if (require.main === module) {
  if (process.argv.length < 3) {
    console.error('Usage: node bulkAddMemories.js <memory_file_path> [--no-infer]');
    process.exit(1);
  }
  
  const filePath = process.argv[2];
  const noInfer = process.argv.includes('--no-infer');
  
  bulkAddMemories(filePath, { infer: !noInfer })
    .then(stats => {
      console.log('Addition result:', stats);
      process.exit(0);
    })
    .catch(error => {
      console.error('Addition failed:', error);
      process.exit(1);
    });
} else {
  // If imported as a module, export the function
  module.exports = { bulkAddMemories };
} 