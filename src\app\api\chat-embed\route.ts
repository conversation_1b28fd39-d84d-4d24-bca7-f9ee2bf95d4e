import { NextRequest, NextResponse } from 'next/server';
import { Message as VercelChatMessage } from 'ai';
import { streamText } from 'ai';
import { openai } from '@ai-sdk/openai';
import { anthropic } from '@ai-sdk/anthropic';
import { google } from '@ai-sdk/google';
import { deepseek } from '@ai-sdk/deepseek';
import { nanoid } from 'nanoid';

// Import request context handling
import { clearRequestContext, formatToolCallId, getRequestContext } from '@/utils/requestContext';
import { withRequestContext } from '@/utils/apiWrapper';

// 导入记忆工具
import { memoryTools } from '@/tools/memoryTools';
import { getPageSchema, domOperator } from '@/tools/domTool';
import { domToolPrompt } from '@/tools/prompt/domToolPrompt';
import { emailTool } from '@/tools/emailTool';
// 导入基础工具
import { weatherTool } from '@/tools/weatherTool';
import { clockTool } from '@/tools/clockTool';
import { gisTool } from '@/tools/gisTool';
import { finishTaskTool } from '@/tools/commonTools';
import { requireUserInputTool } from '@/tools/requireUserInputTool';
import { kbTool } from '@/tools/kbTool';
// 导入TMS工具
import { tmsShipmentOrderTrackingTool, tmsQuoteTool } from '@/tools/tms-tool';

// 导入MCP工具和配置
import {
  getAllServersMcpTools,
  getMcpPromptsByServerIds
} from '@/tools/mcpTools';
import { getAllMcpServerIds } from '@/tools/mcpConfig';
import { getSitePrompt } from '@/prompts/getSitePrompt';
import { portalQuoteTool, portalGetCustomerListTool, portalGetInvoiceListTool, portalGetInvoiceDetailTool, portalUtCustomersTool, portalUfCustomersTool, portalAccListTool} from '@/tools/portal-tool';
import { portalSubmitPreparedDisputeTool, portalTrackingOrderTool, portalSaveShippingQuoteTool, portalGetUserPaymentCardsTool, portalCreateShippingOrderTool } from '@/tools/portal-tool';

// 导入新的站点工具配置工具
import { getSiteToolConfig } from '@/config/index';

// 缓存所有MCP工具，避免每次请求都重新加载
let cachedAllMcpTools: Record<string, any> | null = null;
let mcpToolsLoadTimestamp: number = 0;
// 工具缓存有效期为28天，可通过环境变量配置
const CHAT_TOOLS_CACHE_TTL = parseInt(process.env.MCP_CACHE_TTL || '2419200000'); // 28天

import { addCorsHeaders, createPreflightResponse } from '@/utils/cors';

/**
 * 处理OPTIONS请求（预检请求）
 */
export async function OPTIONS(req: NextRequest) {
  return createPreflightResponse();
}

/**
 * 获取指定站点ID支持的工具名称列表
 * @param siteId 站点ID
 */
function getSiteSupportedTools(siteId: string): string[] | null {
  if (!siteId) return null;
  
  const siteToolConfig = getSiteToolConfig();
  return siteToolConfig[siteId]?.tools || null;
}

/**
 * 根据站点ID过滤工具
 * @param tools 原始工具集合
 * @param siteId 站点ID
 * @param requestId 请求ID，用于日志记录
 * @param toolType 工具类型描述，用于日志记录
 * @returns 过滤后的工具集合
 */
function filterToolsBySiteId(
  tools: Record<string, any>, 
  siteId: string, 
  requestId: string,
  toolType: string = 'MCP'
): Record<string, any> {
  // 如果没有站点ID，直接返回原始工具集合
  if (!siteId) {
    return tools;
  }

  // 获取站点支持的工具列表
  const supportedTools = getSiteSupportedTools(siteId);
  if (!supportedTools) {
    console.log(`[ChatEmbed:${requestId}] 未找到站点 ${siteId} 的工具配置，使用所有${toolType}工具`);
    return tools;
  }

  // 过滤出站点支持的工具
  console.log(`[ChatEmbed:${requestId}] 为站点 ${siteId} 过滤${toolType}工具，支持的工具: ${supportedTools.join(', ')}`);
  const filteredTools = Object.entries(tools)
    .filter(([name]) => supportedTools.map(t => t.toLowerCase()).includes(name.toLowerCase()))
    .reduce((acc, [name, tool]) => {
      acc[name] = tool;
      return acc;
    }, {} as Record<string, any>);
  
  console.log(`[ChatEmbed:${requestId}] 站点 ${siteId} 过滤后的${toolType}工具数量: ${Object.keys(filteredTools).length}/${Object.keys(tools).length}`);
  return filteredTools;
}

/**
 * 获取所有MCP工具并缓存，减少重复加载
 * @param requestId 请求ID
 * @param siteId 可选的站点ID，用于筛选工具
 */
const getAllMcpToolsCached = async (requestId: string, siteId?: string): Promise<Record<string, any>> => {
  const now = Date.now();

  // 检查缓存是否有效
  if (
    cachedAllMcpTools &&
    mcpToolsLoadTimestamp &&
    now - mcpToolsLoadTimestamp < CHAT_TOOLS_CACHE_TTL
  ) {
    console.log(`[ChatEmbed:${requestId}] 使用缓存的MCP工具，工具数量: ${Object.keys(cachedAllMcpTools).length}`, Object.keys(cachedAllMcpTools));
    
    // 如果指定了siteId，根据配置过滤工具
    if (siteId) {
      // 直接使用标准过滤方法，让getSiteSupportedTools去获取tools配置
      return filterToolsBySiteId(cachedAllMcpTools, siteId, requestId, 'MCP');
    }
    
    return cachedAllMcpTools;
  }

  console.log(`[ChatEmbed:${requestId}] 工具缓存过期或不存在，重新加载所有MCP工具...`);

  // 使用现有方法加载所有服务器的工具
  const allTools = await getAllServersMcpTools();

  // 更新缓存
  cachedAllMcpTools = allTools;
  mcpToolsLoadTimestamp = now;

  console.log(`[ChatEmbed:${requestId}] 已缓存所有MCP工具，工具数量: ${Object.keys(allTools).length}`, Object.keys(allTools));
  
  // 如果指定了siteId，根据配置过滤工具
  if (siteId) {
    return filterToolsBySiteId(allTools, siteId, requestId, 'MCP');
  }
  
  return allTools;
};

// 定义附件类型
interface Attachment {
  contentType?: string;
  name?: string;
  url?: string;
}

// 模型映射：将UI显示名称映射到实际API调用模型名称
const MODEL_MAPPING = {
  'gpt-4.1': 'gpt-4.1-2025-04-14', // GPT-4.1模型
  'claude-sonnet-4-0': 'claude-sonnet-4-0', // Claude Sonnet 4 (alias - always latest)
  'gemini-2.5-flash-preview-05-20': 'gemini-2.5-flash-preview-05-20', // Gemini 2.5 Flash Preview
  'deepseek-chat': 'deepseek-chat', // DeepSeek Chat V3 model
};

// 过滤无效附件
function filterValidAttachments(messages: VercelChatMessage[]): VercelChatMessage[] {
  return messages.map(message => {
    // 使用类型断言来处理可能的附件
    const msgWithAttachments = message as any;

    // 如果消息没有附件，直接返回原始消息
    if (!msgWithAttachments.attachments || !Array.isArray(msgWithAttachments.attachments)) {
      return message;
    }

    // 过滤有效的附件（必须有url）
    const validAttachments = msgWithAttachments.attachments.filter(
      (attachment: any) => attachment && typeof attachment === 'object' && 'url' in attachment
    );

    return {
      ...message,
      attachments: validAttachments.length > 0 ? validAttachments : undefined
    } as VercelChatMessage;
  });
}

// 创建系统提示
async function createSystemPrompt(requestId: string, domain: string, siteId: string): Promise<string> {
  // 基础系统提示
  let systemPrompt = '';

  // 获取站点特定的提示词
  if (siteId) {
    const sitePrompt = getSitePrompt(siteId);
    if (sitePrompt) {
      systemPrompt += '\n\n' + sitePrompt;
      console.log(`[ChatEmbed:${requestId}] 添加站点特定提示词: ${siteId}`);
    }
  }

  // 如果有域名，可以添加特定于域名的提示
  if (domain) {
    // 这里可以根据域名添加特定的提示
    // 例如：systemPrompt += `\n\nYou are embedded on ${domain}.`;
  }

  // 获取站点配置中指定的MCP服务器ID
  const siteConfig = getSiteToolConfig()[siteId || 'default'];
  const configuredServerIds = siteConfig?.mcpServerIds || [];
  
  // 如果站点配置中有指定的服务器ID
  let mcpPrompts = null;
  if (configuredServerIds.length > 0) {
    console.log(`[ChatEmbed:${requestId}] 根据站点配置加载MCP提示词，服务器ID: ${configuredServerIds.join(', ')}`);
    mcpPrompts = await getMcpPromptsByServerIds(configuredServerIds);
  }
  
  if (mcpPrompts) {
    systemPrompt += `\n\n${mcpPrompts}`;
  }

  // 获取工具，传入域名、站点ID和请求ID
  const tools = await getDomainTools(domain, siteId, requestId);

  // 检查tools中是否包含DOM相关工具(getPageSchema或domOperator)，如果包含，则将domToolPrompt添加到系统提示词中
  if (Object.keys(tools).includes('getPageSchema') || Object.keys(tools).includes('domOperator')) {
    console.log(`[ChatEmbed:${requestId}] 检测到DOM工具，添加DOM工具提示词`);
    systemPrompt += `\n\n${domToolPrompt}`;
  }

  return systemPrompt;
}

// 获取站点特定工具
async function getSiteSpecificTools(siteId: string, requestId: string): Promise<Record<string, any>> {
  // 所有可用的站点特定工具
  const allSiteTools = {
    kbTool,
    getPageSchema,
    domOperator,
    tmsShipmentOrderTrackingTool,  // 添加TMS订单跟踪工具
    tmsQuoteTool,  // 添加TMS报价工具
    portalQuoteTool,  // 添加Portal报价工具
    portalGetCustomerListTool,  // 添加Portal获取客户列表工具
    portalGetInvoiceListTool,  // 添加Portal获取发票列表工具
    portalGetInvoiceDetailTool,  // 添加Portal获取发票详情工具
    portalSubmitPreparedDisputeTool,  // 添加Portal提交准备好的争议工具
    portalTrackingOrderTool,  // 添加Portal订单跟踪工具
    portalSaveShippingQuoteTool,  // 添加Portal保存运输报价工具
    portalGetUserPaymentCardsTool,  // 添加Portal获取用户支付卡工具
    portalCreateShippingOrderTool,  // 添加Portal创建运输订单工具
    portalUtCustomersTool,  // 添加Portal获取UT客户列表工具
    portalUfCustomersTool,  // 添加Portal获取UF客户列表工具
    portalAccListTool  // 添加Portal获取ACC列表工具
    // 将来可能会增加更多站点特定工具
  };

  // 如果没有站点ID，则提供默认的kbTool
  if (!siteId || siteId === 'default') {
    console.log(`[ChatEmbed:${requestId}] 使用默认站点ID，加载所有知识库工具`);
    return allSiteTools;
  }
  
  // 使用通用过滤函数过滤站点特定工具
  return filterToolsBySiteId(allSiteTools, siteId, requestId, '站点特定');
}

// 获取特定于域名和站点ID的工具
async function getDomainTools(domain: string, siteId: string, requestId: string) {
  console.log(`[ChatEmbed:${requestId}] 开始获取站点 ${siteId} 的适用工具`);
  
  // 获取MCP工具（根据siteId过滤）
  const allMcpTools = await getAllMcpToolsCached(requestId, siteId);
  console.log(`[ChatEmbed:${requestId}] 获取MCP工具完成，数量: ${Object.keys(allMcpTools).length}`);

  // 基础工具集（这些工具不根据站点ID过滤）
  const baseTools = {
    clockTool,
    gisTool,
    requireUserInputTool,
    finishTaskTool
  };
  
  // 获取站点特定工具（根据siteId过滤）
  const siteTools = await getSiteSpecificTools(siteId, requestId);
  console.log(`[ChatEmbed:${requestId}] 获取站点特定工具完成，数量: ${Object.keys(siteTools).length}`);
  
  // 返回所有工具的组合
  const combinedTools = {
    ...baseTools,
    ...allMcpTools,
    ...siteTools
  };
  
  console.log(`[ChatEmbed:${requestId}] 最终组合工具数量: ${Object.keys(combinedTools).length}`, Object.keys(combinedTools));
  return combinedTools;
}

// 使用高阶函数包装API处理程序
export const POST = withRequestContext(async (req: NextRequest, requestId: string) => {
  // 获取域名
  const domain = req.headers.get('X-Embed-Domain') || '';
  const referer = req.headers.get('Referer') || '';
  
  // 获取站点ID
  const siteId = req.headers.get('X-Site-ID') || 'default';

  // 从Referer中提取域名（如果X-Embed-Domain未提供）
  const refererDomain = referer ? new URL(referer).hostname : '';
  const effectiveDomain = domain || refererDomain;
  // console.log('domain', domain, 'referer', referer, 'siteId', siteId, 'effectiveDomain', effectiveDomain);

  // 解析请求
  const data = await req.json();
  // 提取消息和可能的模型信息
  let { messages, model: requestModel } = data;

  // 过滤无效附件
  messages = filterValidAttachments(messages);

  // 获取系统提示，传入requestId、域名和站点ID
  const systemPrompt = await createSystemPrompt(requestId, effectiveDomain, siteId);
  console.log(`[ChatEmbed:${requestId}] 系统提示: ${systemPrompt}`);
  
  // 获取工具，传入域名、站点ID和请求ID
  const tools = await getDomainTools(effectiveDomain, siteId, requestId);

  // 为工具添加前缀，以便在请求上下文中跟踪
  const prefixedTools = Object.entries(tools).reduce((acc, [name, tool]) => {
    // 不再给工具名添加后缀，保持与主应用的一致性
    // 使用和主应用相同的方法，通过包装execute函数传递上下文
    
    // 为MCP工具和记忆工具添加请求ID前缀
    if (name !== 'weatherTool' &&
        name !== 'requireUserInputTool' && name !== 'finishTaskTool' &&
        name !== 'getPageSchema' && name !== 'domOperator' && 
        name !== 'tmsShipmentOrderTrackingTool' && name !== 'tmsQuoteTool') {
      // 创建一个包装器，为toolCallId添加请求ID前缀
      acc[name] = {
        ...tool,
        execute: async (args: any, options: { toolCallId: string; messages: any[]; abortSignal?: AbortSignal }) => {
          // 替换原始toolCallId为带前缀的版本
          const prefixedOptions = {
            ...options,
            toolCallId: formatToolCallId(requestId, options.toolCallId)
          };

          // 使用带前缀的toolCallId调用原始execute函数
          return (tool as any).execute(args, prefixedOptions);
        }
      };
    } else {
      // 标准工具保持不变
      acc[name] = tool;
    }
    return acc;
  }, {} as Record<string, any>);

  // 确定要使用的模型实例
  let modelInstance;
  let modelProvider;

  if (requestModel?.includes('claude')) {
    modelInstance = anthropic(requestModel);
    modelProvider = 'Anthropic';
  } else if (requestModel?.includes('gemini')) {
    modelInstance = google(requestModel);
    modelProvider = 'Google';
  } else if (requestModel?.includes('deepseek')) {
    modelInstance = deepseek(requestModel);
    modelProvider = 'DeepSeek';
  } else {
    modelInstance = openai(requestModel || 'gpt-4.1-2025-04-14'); // 默认使用GPT-4.1
    modelProvider = 'OpenAI';
  }

  console.log(`[ChatEmbed:${requestId}] 使用模型: ${requestModel || 'gpt-4.1-2025-04-14'} (${modelProvider})`);
    
  // 获取时区信息
  const requestContext = getRequestContext(requestId);
  const timezone = requestContext?.timezone;
  console.log(`[ChatEmbed:${requestId}] Client timezone: ${timezone}`);
    
  // 使用Vercel AI SDK API
  try {
    // 将系统提示作为最后一条消息，确保在长对话中不会失效
    const messagesWithSystemPrompt = [
      ...messages, // 原有对话历史
      { 
        role: 'system', 
        content: systemPrompt 
      }
    ];

    // 使用streamText创建流式响应
    const stream = streamText({
      model: modelInstance,
      // 不使用 system 参数，改为在 messages 中作为最后一条消息
      messages: messagesWithSystemPrompt, // 传递包含系统提示的消息数组
      temperature: 0.1,
      tools: prefixedTools,
      maxSteps: 15, // 允许模型执行最多15次工具调用（嵌入式版本限制更严格）
      headers: timezone ? { 'X-Timezone': timezone } : undefined,
      onFinish: async () => {
        console.log(`[ChatEmbed:${requestId}] 聊天完成`);
        clearRequestContext(requestId);
      },
      onError: async (err) => {
        console.error(`[ChatEmbed:${requestId}] 聊天错误:`, err);
        clearRequestContext(requestId);
      }
    });

    // 将流式响应转换为Response对象，并添加CORS头
    const streamResponse = stream.toDataStreamResponse();
    return addCorsHeaders(streamResponse);
  } catch (error) {
    console.error(`[ChatEmbed:${requestId}] 处理聊天请求时出错:`, error);
    return addCorsHeaders(NextResponse.json(
      { error: '处理聊天请求时出错', details: String(error) },
      { status: 500 }
    ));
  }
});