'use client';

import React, { useState, useEffect, useRef } from 'react';
import { useAuth } from '@/app/contexts/AuthContext';
import { Facility } from '@/utils/clientUserContext';
import { getUserFacilities, getCurrentFacility, switchFacility, initializeWmsUserInfo } from '@/utils/wmsService';
import '@/styles/item-design-system.css';

interface FacilitySelectorProps {
  className?: string;
}

export default function FacilitySelector({ className = '' }: FacilitySelectorProps) {
  const { user, tokenData, isAuthenticated } = useAuth();
  const [facilities, setFacilities] = useState<Facility[]>([]);
  const [currentFacility, setCurrentFacility] = useState<Facility | undefined>();
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [showTooltip, setShowTooltip] = useState(false);
  
  const userId = tokenData?.sub || tokenData?.data?.user_id || (user?.id ? String(user.id) : null);
  
  // 加载用户设施
  useEffect(() => {
    if (isAuthenticated && userId) {
      console.log('加载用户设施，用户ID:', userId);
      setIsLoading(true);
      
      // Show tooltip after login
      setShowTooltip(true);
      const tooltipTimer = setTimeout(() => {
        setShowTooltip(false);
      }, 10000); // Hide tooltip after 10 seconds
      
      const loadFacilities = () => {
        try {
          const userFacilities = getUserFacilities(userId);
          
          if (!userFacilities || userFacilities.length === 0) {
            console.warn('用户没有可用的设施，尝试初始化WMS用户信息');
            setError('Unable to load facilities...');
            
            // 初始化WMS用户信息
            initializeWmsUserInfo(userId).then(success => {
              if (success) {
                console.log('WMS用户信息初始化成功，重新获取设施');
                // 重新获取设施列表
                const refreshedFacilities = getUserFacilities(userId);
                
                if (refreshedFacilities.length > 0) {
                  setFacilities(refreshedFacilities);
                  setError(null);
                  
                  // 获取当前设施
                  const current = getCurrentFacility(userId);
                  if (current) {
                    console.log('当前设施:', current.name);
                    setCurrentFacility(current);
                  } else {
                    console.warn('未找到当前设施');
                  }
                } else {
                  console.error('初始化后仍未获取到设施');
                  setError('Unable to load facilities...');
                }
                
                setIsLoading(false);
              } else {
                console.error('WMS用户信息初始化失败');
                setError('无法连接到WMS服务');
                setIsLoading(false);
              }
            });
          } else {
            // 设施列表非空，正常处理
            setFacilities(userFacilities);
            setError(null);
            
            // 获取当前设施
            const current = getCurrentFacility(userId);
            if (current) {
              console.log('当前设施:', current.name);
              setCurrentFacility(current);
            } else if (userFacilities.length > 0) {
              // 如果没有当前设施但有设施列表，使用第一个
              console.log('没有当前设施，使用第一个设施:', userFacilities[0].name);
              setCurrentFacility(userFacilities[0]);
              // 设置当前设施
              switchFacility(userId, userFacilities[0].id);
            }
            
            setIsLoading(false);
          }
        } catch (err) {
          console.error('加载设施时出错:', err);
          setError('加载设施失败');
          setIsLoading(false);
        }
      };
      
      // 执行加载
      loadFacilities();
      
      return () => {
        clearTimeout(tooltipTimer);
      };
    }
  }, [isAuthenticated, userId]);
  
  // 切换设施
  const handleFacilityChange = (facilityId: string) => {
    if (!userId) return;
    
    try {
      console.log('尝试切换设施:', facilityId);
      setIsLoading(true);
      
      const success = switchFacility(userId, facilityId);
      
      if (success) {
        const newCurrentFacility = facilities.find(f => f.id === facilityId);
        setCurrentFacility(newCurrentFacility);
        console.log('已切换到设施:', newCurrentFacility?.name);
      } else {
        console.error('切换设施失败');
        setError('切换设施失败');
      }
      
      setIsLoading(false);
    } catch (err) {
      console.error('切换设施时出错:', err);
      setError('切换设施失败');
      setIsLoading(false);
    }
    
    setIsDropdownOpen(false);
  };
  
  // 添加下拉菜单点击外部关闭功能
  const dropdownRef = useRef<HTMLDivElement>(null);
  
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsDropdownOpen(false);
        setShowTooltip(false);
      }
    }
    
    function handleMouseLeave() {
      setIsDropdownOpen(false);
    }
    
    document.addEventListener('mousedown', handleClickOutside);
    
    if (dropdownRef.current) {
      dropdownRef.current.addEventListener('mouseleave', handleMouseLeave);
    }
    
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      if (dropdownRef.current) {
        dropdownRef.current.removeEventListener('mouseleave', handleMouseLeave);
      }
    };
  }, []);
  
  // 如果用户未登录，不显示选择器
  if (!isAuthenticated) {
    console.log('用户未登录，不显示设施选择器');
    return null;
  }
  
  // 如果正在加载或没有设施，显示加载状态
  if (isLoading || facilities.length === 0) {
    return (
      <div className={`relative ${className}`}>
        <button
          className={`px-2 py-1 text-xs rounded-md ${isLoading ? 'bg-item-orange/20 text-item-orange-light border border-item-orange/40' : 'bg-item-bg-card text-item-gray-400 border border-item-gray-800'} cursor-not-allowed flex items-center gap-1`}
          disabled
          title={error || (isLoading ? "加载中..." : "没有可用设施")}
          onMouseEnter={() => setShowTooltip(true)}
        >
          {isLoading ? (
            <>
              <svg className="animate-spin h-3 w-3 text-item-orange-light" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              <span className="text-xs">加载中</span>
            </>
          ) : (
            <svg 
              xmlns="http://www.w3.org/2000/svg" 
              fill="none" 
              viewBox="0 0 24 24" 
              strokeWidth={1.5} 
              stroke="currentColor" 
              className="w-4 h-4"
            >
              <path strokeLinecap="round" strokeLinejoin="round" d="M8.25 21v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21m0 0h4.5V3.545M12.75 21h7.5V10.75M2.25 21h1.5m18 0h-18M2.25 9l4.5-1.636M18.75 3l-1.5.545m0 6.205l3 1m1.5.5l-1.5-.5M6.75 7.364V3h-3v18m3-13.636l10.5-3.819" />
            </svg>
          )}
        </button>
        
        {showTooltip && (
          <div className="absolute top-full right-0 mt-1 px-3 py-2 bg-item-bg-card text-white text-xs rounded-md shadow-xl shadow-item-purple/20 border border-item-purple/30 z-50 w-64 item-animate-in">
            <p>For WMS-related questions, please ensure your account has Item WMS 3.0 permission. This enables the facilities list here.</p>
            <button 
              className="absolute top-1 right-1 text-item-gray-400 hover:text-white"
              onClick={() => setShowTooltip(false)}
              aria-label="Close tooltip"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        )}
      </div>
    );
  }
  
  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      <button
        onClick={() => setIsDropdownOpen(!isDropdownOpen)}
        className="px-4 py-2 text-sm rounded-lg bg-item-bg-card hover:bg-item-bg-hover flex items-center gap-2 transition-all duration-200 border border-item-gray-800 hover:border-item-purple/30 shadow-sm"
        aria-label="选择设施"
        onMouseEnter={() => setShowTooltip(true)}
      >
        <svg 
          xmlns="http://www.w3.org/2000/svg" 
          fill="none" 
          viewBox="0 0 24 24" 
          strokeWidth={1.5} 
          stroke="currentColor" 
          className="w-4 h-4 text-item-gray-400"
        >
          <path strokeLinecap="round" strokeLinejoin="round" d="M8.25 21v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21m0 0h4.5V3.545M12.75 21h7.5V10.75M2.25 21h1.5m18 0h-18M2.25 9l4.5-1.636M18.75 3l-1.5.545m0 6.205l3 1m1.5.5l-1.5-.5M6.75 7.364V3h-3v18m3-13.636l10.5-3.819" />
        </svg>
        <span className="hidden sm:inline font-medium">{currentFacility?.name || '选择设施'}</span>
        <span className="inline sm:hidden font-medium">{currentFacility?.code || '设施'}</span>
      </button>
      
      {isDropdownOpen && (
        <div className="absolute z-50 mt-1 right-0 bg-item-bg-card border border-item-purple/30 rounded-md shadow-xl shadow-item-purple/20 overflow-hidden min-w-[200px] max-w-[320px] item-animate-in">
          <div className="py-1 max-h-[280px] overflow-y-auto item-scrollbar">
            {facilities.length > 8 ? (
              // 按首字母分组显示设施
              (() => {
                // 按名称排序并分组
                const sortedFacilities = [...facilities].sort((a, b) => a.name.localeCompare(b.name));
                const groups: Record<string, Facility[]> = {};
                
                // 分组
                sortedFacilities.forEach(facility => {
                  const firstLetter = facility.name.charAt(0).toUpperCase();
                  if (!groups[firstLetter]) {
                    groups[firstLetter] = [];
                  }
                  groups[firstLetter].push(facility);
                });
                
                // 渲染分组
                return Object.entries(groups).map(([letter, facilitiesGroup]) => (
                  <div key={letter} className="mb-1 last:mb-0">
                    <div className="px-3 py-1 text-xs text-item-gray-400 bg-item-bg-card sticky top-0 border-b border-item-gray-800">
                      {letter}
                    </div>
                    <ul>
                      {facilitiesGroup.map(facility => (
                        <li key={facility.id}>
                          <button
                            onClick={() => handleFacilityChange(facility.id)}
                            className={`w-full text-left flex items-center px-3 py-1.5 text-sm hover:bg-item-purple/20 transition-all duration-200 ${
                              currentFacility?.id === facility.id ? 'font-medium text-item-purple-light border-l-2 border-item-purple' : 'text-white'
                            }`}
                          >
                            <span className="flex-1 truncate">{facility.name}</span>
                            <span className="ml-1.5 text-xs text-item-gray-400 flex-shrink-0">({facility.code})</span>
                          </button>
                        </li>
                      ))}
                    </ul>
                  </div>
                ));
              })()
            ) : (
              // 简单列表显示设施
              <ul>
                {facilities.map(facility => (
                  <li key={facility.id}>
                    <button
                      onClick={() => handleFacilityChange(facility.id)}
                      className={`w-full text-left flex items-center px-3 py-1.5 text-sm hover:bg-item-purple/20 transition-all duration-200 ${
                        currentFacility?.id === facility.id ? 'font-medium text-item-purple-light border-l-2 border-item-purple' : 'text-white'
                      }`}
                    >
                      <span className="flex-1 truncate">{facility.name}</span>
                      <span className="ml-1.5 text-xs text-item-gray-400 flex-shrink-0">({facility.code})</span>
                    </button>
                  </li>
                ))}
              </ul>
            )}
          </div>
        </div>
      )}
      
      {/* Only show permission tooltip when there are no facilities available */}
      {showTooltip && !isDropdownOpen && facilities.length === 0 && (
        <div className="absolute top-full right-0 mt-1 px-3 py-2 bg-item-bg-card text-white text-xs rounded-md shadow-xl shadow-item-purple/20 border border-item-purple/30 z-50 w-64 item-animate-in">
          <p>For WMS-related questions, please ensure your account has Item WMS 3.0 permission. This enables the facilities list in the top-right header.</p>
          <button 
            className="absolute top-1 right-1 text-item-gray-400 hover:text-white"
            onClick={() => setShowTooltip(false)}
            aria-label="Close tooltip"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      )}
      
      {error && (
        <div className="absolute top-full right-0 mt-1 px-2 py-1 bg-red-900/70 text-red-200 text-xs rounded-md border border-red-500/30">
          {error}
        </div>
      )}
    </div>
  );
} 