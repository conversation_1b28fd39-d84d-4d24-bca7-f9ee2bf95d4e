# Multi-Layer Agent Architecture Design

## 概述

支持多层 Agent 嵌套的架构设计，确保在复杂的 Agent 调用链中正确传递请求上下文和实现实时流式输出。

## 架构层级

```
用户请求
    ↓
SuperAgent (L1) - 总协调者
    ↓
WMSAgent (L2) - 仓库管理专家
    ↓
InventoryAgent (L3) - 库存专家
    ↓
MCP Tools (L4) - 具体的API调用
```

## 核心技术方案

### 1. RequestId 传递机制

**问题**: 多层嵌套时 toolCallId 会被重复包装，导致无法正确解析
**解决**: 智能检测已有的 requestId，避免重复包装

```typescript
// 原始: "call_ABC123"
// Level 1: "req123_call_call_ABC123" ✓
// Level 2: "req123_call_call_ABC123" ✓ (检测到已有，不重复包装)
// Level 3: "req123_call_call_ABC123" ✓ (检测到已有，不重复包装)
```

### 2. 流式输出合并

**挑战**: 多层 Agent 的流需要正确合并到主流
**方案**: 每层 Agent 都支持流合并，但只有最顶层负责最终输出

```typescript
// SuperAgent: 创建主数据流
createDataStreamResponse({
  execute: async (dataStream) => {
    // WMSAgent: 将子流合并到主流
    const subStream = await wmsAgent.execute(...);
    dataStream.merge(subStream);
  }
});
```

### 3. 上下文继承

**原则**: 子 Agent 继承父 Agent 的所有上下文信息
- 认证信息 (Authorization, tenant-id, facility-id)
- 用户信息 (userId, timezone)
- 请求标识 (requestId)

## 实现示例

### Level 1: SuperAgent
```typescript
export class SuperAgent extends BaseAgent {
  async executeWithTools(context, modelInstance, tools, dataStream) {
    // 处理委托工具，支持流合并
    const delegateToWms = this.createDelegateToWMSAgentTool(context, modelInstance, dataStream);
    
    return await streamText({
      tools: { ...tools, delegateToWms },
      // ... 其他配置
    });
  }
}
```

### Level 2: WMSAgent  
```typescript
export class WMSAgent extends BaseAgent {
  async execute(context, modelInstance) {
    // 自动包装工具，传递 requestId
    const wrappedTools = this.wrapToolsWithRequestId(tools, context.requestId);
    
    // 支持进一步委托给子 Agent
    const delegateToInventory = this.createDelegateToInventoryTool(context, modelInstance);
    
    return await streamText({
      tools: { ...wrappedTools, delegateToInventory },
      // ... 其他配置
    });
  }
}
```

### Level 3: InventoryAgent
```typescript
export class InventoryAgent extends BaseAgent {
  protected async loadTools() {
    // 加载库存相关的 MCP 工具
    return await getSpecificServersMcpTools(['inventory', 'stock']);
  }
}
```

## 优势

### 1. **无限嵌套支持**
- 任意深度的 Agent 嵌套
- 每层都正确传递上下文
- 避免重复包装问题

### 2. **实时流式输出**
- 任意层级的 Agent 执行都能实时显示
- 流合并机制确保用户看到完整过程
- 支持并行和串行的子 Agent 执行

### 3. **上下文完整性**
- 认证信息在整个调用链中保持
- MCP 工具始终能获取到正确的请求上下文
- 支持多租户和多设施场景

### 4. **可扩展性**
- 新增 Agent 类型只需继承 BaseAgent
- 自动获得多层支持能力
- 统一的流合并和上下文传递机制

## 使用场景

### 场景1: 复杂仓库操作
```
用户: "处理订单 DN-123 的入库和上架"
SuperAgent → WMSAgent → [InboundAgent, ShelvingAgent] → MCP Tools
```

### 场景2: 跨系统业务流程  
```
用户: "为客户A创建报价并生成BI报告"
SuperAgent → [PortalAgent, BIAgent] → [QuoteAgent, ReportAgent] → MCP Tools
```

### 场景3: 故障诊断
```
用户: "检查系统健康状态"
SuperAgent → DiagnosticAgent → [WMSHealthAgent, APIHealthAgent] → MCP Tools
```

## 性能考虑

1. **工具包装开销**: 通过智能检测减少不必要的包装
2. **流合并效率**: 使用原生 ReadableStream API，性能最优
3. **内存管理**: 及时清理请求上下文，避免内存泄漏

## 总结

这个多层 Agent 架构支持：
- ✅ 无限层级嵌套
- ✅ 完整的上下文传递  
- ✅ 实时流式输出
- ✅ 高性能和可扩展性

可以处理任意复杂的业务场景，同时保持代码的简洁性和可维护性。