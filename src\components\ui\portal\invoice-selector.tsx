'use client';

import * as React from 'react';
import { useState, useEffect, useRef } from 'react';
import { FileText, Search, Loader2 } from 'lucide-react';
// @ts-ignore 忽略类型检查
import { debounce } from 'lodash';
import { portalApi } from '@/utils/portalApiClient';

import { Input } from '@/components/ui/input';
import { cn } from '@/lib/utils';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
  getCyberAgentPortalContainer,
} from "@/components/ui/select";

interface InvoiceOption {
  id: string;
  invoiceNumber: string;
  amount?: number;
  status?: string;
  customerId?: string;
}

interface InvoiceSelectorProps {
  value?: string;
  onChange: (value: string, invoiceData?: InvoiceOption) => void;
  placeholder?: string;
  disabled?: boolean;
  required?: boolean;
  customerId?: string;
  customerCode?: string;
  customerName?: string;
  apiHeaders?: Record<string, string>;
  defaultValue?: string;
}

export function InvoiceSelector({
  value,
  onChange,
  placeholder = 'Select invoice',
  disabled = false,
  required = false,
  customerId,
  customerCode,
  customerName,
  apiHeaders = {},
  defaultValue
}: InvoiceSelectorProps) {
  const API_PATH = 'api/v1/web/crm-hand-out/bnp/invoice/list';
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [options, setOptions] = useState<InvoiceOption[]>([]);
  const [selectedInvoice, setSelectedInvoice] = useState<InvoiceOption | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const searchInputRef = useRef<HTMLInputElement>(null);
  const initialLoadRef = useRef(false);

  useEffect(() => {
    if (!initialLoadRef.current && defaultValue && !value) {
      initialLoadRef.current = true;
      fetchInvoiceById(defaultValue).then(invoice => {
        if (invoice) {
          onChange(invoice.invoiceNumber, invoice);
        }
      });
    }
  }, [defaultValue, value]);

  const extractInvoiceData = (invoice: any): InvoiceOption => {
    return {
      id: invoice.invoiceNumber || '',
      invoiceNumber: invoice.invoiceNumber || 'Unknown Invoice',
      amount: invoice.invoiceAmount,
      status: invoice.invoiceStatus,
      customerId: invoice.customerId
    };
  };

  const fetchInvoiceById = async (invoiceNumber: string): Promise<InvoiceOption | null> => {
    try {
      setLoading(true);
      const response = await portalApi.post(API_PATH, {
        invoiceNumber,
        department: 'UT',
        customerId,
        customerCode,
        customerName
      });
      console.log('fetchInvoiceById response', response);
      const data = response.data as { data?: { data?: any[] } };
      if (
        response.success &&
        data &&
        data.data &&
        Array.isArray(data.data)
      ) {
        const invoice = data.data.find((item: any) => item.invoiceNumber === invoiceNumber);
        if (invoice) {
          const invoiceOption = extractInvoiceData(invoice);
          setSelectedInvoice(invoiceOption);
          return invoiceOption;
        }
      }
    } catch (error) {
      console.error('Failed to fetch invoice data:', error);
    } finally {
      setLoading(false);
    }
    return null;
  };

  const searchInvoices = async (query: string, params?: any) => {
    try {
      setLoading(true);
      const searchParams = params || {
        currentPage: 1,
        pageSize: 20,
        department: 'UT',
        invoiceNumber: query.trim() || '',
        customerId,
        customerCode,
        customerName
      };

      const response = await portalApi.post(API_PATH, searchParams);
      console.log('searchInvoices response', response);
      const data = response.data as { data?: { data?: any[] } };
      if (response.success && data && data.data && Array.isArray(data.data)) {
        const invoiceList = data.data;
        const invoices = invoiceList.map((invoice: any) => extractInvoiceData(invoice));
        setOptions(invoices);
      } else {
        setOptions([]);
      }
    } catch (error) {
      console.error('Failed to search invoices:', error);
      setOptions([]);
    } finally {
      setLoading(false);
    }
  };

  const debouncedSearch = useRef(
    debounce((query: string, cId?: string, cCode?: string, cName?: string) => {
      // 使用传入的 customer 信息进行搜索
      const searchParams: any = {
        currentPage: 1,
        pageSize: 20,
        department: 'UT',
        invoiceNumber: query.trim() || '',
        customerId: cId,
        customerCode: cCode,
        customerName: cName
      };
      searchInvoices(query, searchParams);
    }, 300)
  ).current;

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchQuery(value);
    debouncedSearch(value, customerId, customerCode, customerName);
  };

  const handleInvoiceSelect = (invoiceNumber: string) => {
    const invoice = options.find(opt => opt.invoiceNumber === invoiceNumber);
    if (invoice) {
      setSelectedInvoice(invoice);
      onChange(invoice.invoiceNumber, invoice);
      setSearchQuery('');
      setOptions([]);
      setOpen(false);
    }
  };

  const handleOpenChange = (isOpen: boolean) => {
    setOpen(isOpen);
    if (!isOpen) {
      setSearchQuery('');
      setOptions([]);
    } else {
      // 打开下拉框时自动触发搜索，不依赖搜索关键字
      searchInvoices('');
    }
  };

  return (
    <div className="relative w-full">
      <Select
        value={value}
        onValueChange={handleInvoiceSelect}
        open={open}
        onOpenChange={handleOpenChange}
        disabled={disabled}
      >
        <SelectTrigger
          className={cn(
            "w-full h-10 rounded-md bg-transparent",
            "border border-slate-700/50",
            "hover:border-slate-600 focus:border-slate-500",
            "flex items-center justify-between px-3 py-2 text-sm text-slate-200",
            "focus:outline-none focus:ring-0 focus:ring-offset-0",
            "data-[placeholder]:text-slate-400"
          )}
        >
          <SelectValue placeholder={placeholder}>
            {loading ? (
              <div className="flex items-center">
                <Loader2 className="mr-2 h-4 w-4 animate-spin text-slate-400" />
                <span>Loading...</span>
              </div>
            ) : selectedInvoice ? (
              <div className="flex items-center">
                <FileText className="mr-2 h-4 w-4 text-slate-400" />
                <span>{selectedInvoice.invoiceNumber}</span>
              </div>
            ) : null}
          </SelectValue>
        </SelectTrigger>
        <SelectContent
          className={cn(
            "border border-slate-700/70 bg-slate-800/90 text-slate-200",
            "rounded-md shadow-lg min-w-[300px] overflow-hidden",
            "animate-in fade-in-80 zoom-in-95"
          )}
          portalContainer={getCyberAgentPortalContainer()}
        >
          <div className="flex items-center px-3 py-2 border-b border-slate-700/50">
            <Search className="mr-2 h-4 w-4 shrink-0 text-slate-400" />
            <Input
              ref={searchInputRef}
              className="h-8 border-0 p-0 bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0 text-slate-200 placeholder:text-slate-400"
              placeholder="Search invoices..."
              value={searchQuery}
              onChange={handleSearchChange}
            />
          </div>

          <div className="max-h-[320px] overflow-auto scrollbar-thin scrollbar-thumb-slate-600 scrollbar-track-transparent">
            {loading ? (
              <div className="flex items-center justify-center py-6">
                <Loader2 className="h-6 w-6 animate-spin text-slate-400" />
              </div>
            ) : options.length > 0 ? (
              <SelectGroup>
                <SelectLabel className="px-3 text-xs font-medium text-slate-400">Invoice List</SelectLabel>
                {options.map((invoice) => (
                  <SelectItem
                    key={invoice.invoiceNumber}
                    value={invoice.invoiceNumber}
                    className={cn(
                      "py-2 px-3 cursor-pointer text-slate-200",
                      "focus:bg-slate-700/70 focus:text-slate-200",
                      "data-[highlighted]:bg-slate-700/70 data-[highlighted]:text-slate-200"
                    )}
                  >
                    <div className="flex items-center">
                      <FileText className="mr-2 h-4 w-4 text-slate-400" />
                      <div>
                        <div className="font-medium">{invoice.invoiceNumber}</div>
                        {invoice.amount && (
                          <div className="text-xs text-slate-400">
                            Amount: {invoice.amount.toLocaleString('en-US', { style: 'currency', currency: 'USD' })}
                          </div>
                        )}
                      </div>
                    </div>
                  </SelectItem>
                ))}
              </SelectGroup>
            ) : searchQuery || customerId ? (
              <div className="py-6 text-center text-sm text-slate-400">
                No invoices found
              </div>
            ) : (
              <div className="py-6 text-center text-sm text-slate-400">
                {customerId ? 'Loading invoices...' : 'Type to search invoices'}
              </div>
            )}
          </div>
        </SelectContent>
      </Select>
    </div>
  );
} 