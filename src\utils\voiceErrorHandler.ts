export enum VoiceErrorType {
  CONNECTION_FAILED = 'CONNECTION_FAILED',
  AUTHENTICATION_FAILED = 'AUTHENTICATION_FAILED', 
  MICROPHONE_PERMISSION_DENIED = 'MICROPHONE_PERMISSION_DENIED',
  AUDIO_PLAYBACK_FAILED = 'AUDIO_PLAYBACK_FAILED',
  NETWORK_ERROR = 'NETWORK_ERROR',
  OPENAI_API_ERROR = 'OPENAI_API_ERROR',
  SESSION_EXPIRED = 'SESSION_EXPIRED',
  BROWSER_NOT_SUPPORTED = 'BROWSER_NOT_SUPPORTED',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

export interface VoiceError {
  type: VoiceErrorType;
  message: string;
  details?: any;
  recoverable: boolean;
  userMessage: string;
  actionText?: string;
  onAction?: () => void;
}

export class VoiceErrorHandler {
  static createError(type: VoiceErrorType, originalError?: any, onRetry?: () => void): VoiceError {
    const baseError: VoiceError = {
      type,
      message: originalError?.message || 'Unknown error',
      details: originalError,
      recoverable: false,
      userMessage: '',
      onAction: onRetry
    };

    switch (type) {
      case VoiceErrorType.CONNECTION_FAILED:
        return {
          ...baseError,
          recoverable: true,
          userMessage: '语音连接失败，请检查网络连接',
          actionText: '重新连接',
        };
        
      case VoiceErrorType.AUTHENTICATION_FAILED:
        return {
          ...baseError,
          recoverable: true,
          userMessage: '身份验证失败，请重新登录',
          actionText: '重新登录',
        };
        
      case VoiceErrorType.MICROPHONE_PERMISSION_DENIED:
        return {
          ...baseError,
          recoverable: true,
          userMessage: '需要麦克风权限才能使用语音功能',
          actionText: '授权麦克风',
          onAction: () => {
            navigator.mediaDevices?.getUserMedia({ audio: true })
              .then(() => onRetry?.())
              .catch(console.error);
          }
        };
        
      case VoiceErrorType.NETWORK_ERROR:
        return {
          ...baseError,
          recoverable: true,
          userMessage: '网络连接异常，请检查网络',
          actionText: '重试',
        };
        
      case VoiceErrorType.OPENAI_API_ERROR:
        return {
          ...baseError,
          recoverable: true,
          userMessage: 'OpenAI服务暂时不可用',
          actionText: '重试',
        };

      case VoiceErrorType.SESSION_EXPIRED:
        return {
          ...baseError,
          recoverable: true,
          userMessage: '语音会话已过期，需要重新连接',
          actionText: '重新连接',
        };

      case VoiceErrorType.BROWSER_NOT_SUPPORTED:
        return {
          ...baseError,
          recoverable: false,
          userMessage: '当前浏览器不支持语音功能，请使用Chrome、Firefox或Safari',
        };
        
      default:
        return {
          ...baseError,
          recoverable: true,
          userMessage: '语音功能出现未知错误',
          actionText: '重试',
        };
    }
  }
  
  static handleError(error: any, onRetry?: () => void): VoiceError {
    console.error('[VoiceErrorHandler] Handling error:', error);
    
    const errorMessage = error?.message?.toLowerCase() || '';
    
    if (errorMessage.includes('401') || errorMessage.includes('unauthorized')) {
      return this.createError(VoiceErrorType.AUTHENTICATION_FAILED, error, onRetry);
    }
    
    if (errorMessage.includes('permission') || errorMessage.includes('microphone')) {
      return this.createError(VoiceErrorType.MICROPHONE_PERMISSION_DENIED, error, onRetry);
    }
    
    if (errorMessage.includes('network') || errorMessage.includes('fetch') || errorMessage.includes('timeout')) {
      return this.createError(VoiceErrorType.NETWORK_ERROR, error, onRetry);
    }
    
    if (errorMessage.includes('openai') || errorMessage.includes('api')) {
      return this.createError(VoiceErrorType.OPENAI_API_ERROR, error, onRetry);
    }

    if (errorMessage.includes('session') || errorMessage.includes('expired')) {
      return this.createError(VoiceErrorType.SESSION_EXPIRED, error, onRetry);
    }

    if (errorMessage.includes('not supported') || errorMessage.includes('browser')) {
      return this.createError(VoiceErrorType.BROWSER_NOT_SUPPORTED, error, onRetry);
    }
    
    return this.createError(VoiceErrorType.UNKNOWN_ERROR, error, onRetry);
  }
  
  static getErrorIcon(type: VoiceErrorType): string {
    switch (type) {
      case VoiceErrorType.MICROPHONE_PERMISSION_DENIED:
        return '🎤';
      case VoiceErrorType.NETWORK_ERROR:
        return '🌐';
      case VoiceErrorType.AUTHENTICATION_FAILED:
        return '🔐';
      case VoiceErrorType.AUDIO_PLAYBACK_FAILED:
        return '🔊';
      default:
        return '⚠️';
    }
  }
}