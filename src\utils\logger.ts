// 日志管理工具
export type LogLevel = 'debug' | 'info' | 'warn' | 'error' | 'none';

export interface LoggerConfig {
  level: LogLevel;
}

class Logger {
  private config: LoggerConfig = {
    level: process.env.NODE_ENV === 'production' ? 'warn' : 'info'
  };

  private logLevels: Record<LogLevel, number> = {
    debug: 0,
    info: 1,
    warn: 2,
    error: 3,
    none: 4
  };

  configure(config: Partial<LoggerConfig>) {
    this.config = { ...this.config, ...config };
  }

  private shouldLog(level: LogLevel): boolean {
    return this.logLevels[level] >= this.logLevels[this.config.level];
  }

  debug(message: string, data?: any, module?: string) {
    if (this.shouldLog('debug')) {
      const prefix = module ? `[${module}]` : '';
      console.log(`${prefix} ${message}`, data || '');
    }
  }

  info(message: string, data?: any, module?: string) {
    if (this.shouldLog('info')) {
      const prefix = module ? `[${module}]` : '';
      console.info(`${prefix} ${message}`, data || '');
    }
  }

  warn(message: string, data?: any, module?: string) {
    if (this.shouldLog('warn')) {
      const prefix = module ? `[${module}]` : '';
      console.warn(`${prefix} ${message}`, data || '');
    }
  }

  error(message: string, error?: any, module?: string) {
    if (this.shouldLog('error')) {
      const prefix = module ? `[${module}]` : '';
      console.error(`${prefix} ${message}`, error || '');
    }
  }

  // 设置日志级别
  setLevel(level: LogLevel) {
    this.config.level = level;
  }

  // 获取当前配置
  getConfig(): LoggerConfig {
    return { ...this.config };
  }
}

// 导出单例实例
export const logger = new Logger();

// 导出便捷方法
export const log = {
  debug: (message: string, data?: any, module?: string) => logger.debug(message, data, module),
  info: (message: string, data?: any, module?: string) => logger.info(message, data, module),
  warn: (message: string, data?: any, module?: string) => logger.warn(message, data, module),
  error: (message: string, error?: any, module?: string) => logger.error(message, error, module),
};

// 开发环境下的调试工具
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  (window as any).logger = logger;
  (window as any).setLogLevel = (level: LogLevel) => {
    logger.setLevel(level);
    console.log(`Log level set to: ${level}`);
  };
  (window as any).showLoggerConfig = () => {
    console.log('Current logger config:', logger.getConfig());
  };
}