import { tool } from 'ai';
import { z } from 'zod';

/**
 * Twi<PERSON> 配置接口
 */
interface TwilioConfig {
  username: string;        // Account SID (用作username)
  password: string;        // Auth Token (用作password)
  account_sid: string;     // Account SID 
  from_number: string;     // 发送方电话号码
}

/**
 * 获取 Twilio 配置
 */
function getTwilioConfig(): TwilioConfig {
  const config = {
    username: process.env.TWILIO_USERNAME || '',
    password: process.env.TWILIO_PASSWORD || '',
    account_sid: process.env.TWILIO_ACCOUNT_SID || '',
    from_number: process.env.TWILIO_FROM_NUMBER || ''
  };

  // 验证配置
  const requiredFields = ['username', 'password', 'account_sid', 'from_number'];
  for (const field of requiredFields) {
    if (!config[field as keyof TwilioConfig]) {
      throw new Error(`Missing required Twilio configuration: ${field}`);
    }
  }

  return config;
}

/**
 * Twilio SMS 工具
 * 使用 AI SDK 工具格式，实现与 Python 版本相同的功能
 */
export const twilioTool = tool({
  description: 'Send SMS message to a specified phone number using Twilio. Make sure to use E.164 format for phone numbers (e.g., +***********).',
  parameters: z.object({
    to_number: z.string().describe('Recipient phone number in E.164 format (e.g., +***********)'),
    message: z.string().describe('SMS message content')
  }),
  execute: async ({ to_number, message }) => {
    try {
      const config = getTwilioConfig();
      
      // 准备 API 请求
      const url = `https://api.twilio.com/2010-04-01/Accounts/${config.account_sid}/Messages.json`;
      
      // 创建 Basic Auth (使用 username 和 password)
      const auth = Buffer.from(`${config.username}:${config.password}`).toString('base64');
      
      const headers = {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Authorization': `Basic ${auth}`
      };

      // 准备请求数据
      const formData = new URLSearchParams({
        'From': config.from_number,
        'To': to_number,
        'Body': message
      });

      // 发送请求
      const response = await fetch(url, {
        method: 'POST',
        headers,
        body: formData
      });

      const responseData = await response.json();

      // 检查响应
      if (response.status >= 200 && response.status < 300) {
        console.log(`SMS sent successfully to ${to_number}`);
        return {
          success: true,
          message: 'SMS sent successfully',
          details: {
            to: to_number,
            sid: responseData.sid || '',
            status: responseData.status || ''
          }
        };
      } else {
        const errorMessage = responseData.message || 'Unknown error';
        console.error(`Failed to send SMS: ${errorMessage}`);
        return {
          success: false,
          error: errorMessage,
          message: 'Failed to send SMS'
        };
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error(`Failed to send SMS: ${errorMessage}`);
      return {
        success: false,
        error: errorMessage,
        message: 'Failed to send SMS'
      };
    }
  }
}); 