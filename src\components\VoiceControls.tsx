import React, { useEffect, useCallback } from 'react';
import { Mic, MicOff, Phone, PhoneOff, Volume2, VolumeX, AlertCircle, X } from 'lucide-react';
import { useRealtimeVoice, type VoiceMode } from '@/hooks/useRealtimeVoice';
import { VoiceErrorHandler, type VoiceError } from '@/utils/voiceErrorHandler';

interface VoiceControlsProps {
  onUserTranscript?: (transcript: string) => void;
  onAssistantTranscriptDelta?: (delta: string) => void;
  onAssistantTranscriptComplete?: () => void;
  onError?: (error: VoiceError) => void;
  onHybridAgentStream?: (data: any) => void;
}

export function VoiceControls({ 
  onUserTranscript, 
  onAssistantTranscriptDelta,
  onAssistantTranscriptComplete,
  onError,
  onHybridAgentStream
}: VoiceControlsProps) {
  const {
    isConnected,
    isConnecting,
    mode,
    isPTTActive,
    isRecording,
    isMuted,
    isAssistantResponding,
    error,
    toggleConnection,
    toggleMode,
    startPTT,
    stopPTT,
    toggleMute,
    clearError,
  } = useRealtimeVoice({
    onUserTranscript,
    onAssistantTranscriptDelta,
    onAssistantTranscriptComplete,
    onError,
    onHybridAgentStream,
  });

  // PTT 控制函数 - 支持多种输入方式
  const handlePTTStart = useCallback(() => {
    if (mode === 'ptt' && isConnected) {
      startPTT();
    }
  }, [mode, isConnected, startPTT]);

  const handlePTTEnd = useCallback(() => {
    if (mode === 'ptt' && isConnected) {
      stopPTT();
    }
  }, [mode, isConnected, stopPTT]);

  // 键盘事件处理
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.code === 'Space' && mode === 'ptt' && isConnected && !event.repeat) {
        event.preventDefault();
        handlePTTStart();
      }
    };

    const handleKeyUp = (event: KeyboardEvent) => {
      if (event.code === 'Space' && mode === 'ptt' && isConnected) {
        event.preventDefault();
        handlePTTEnd();
      }
    };

    if (mode === 'ptt' && isConnected) {
      document.addEventListener('keydown', handleKeyDown);
      document.addEventListener('keyup', handleKeyUp);
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('keyup', handleKeyUp);
    };
  }, [mode, isConnected, handlePTTStart, handlePTTEnd]);

  const getConnectionButtonText = () => {
    if (isConnecting) return 'Connecting...';
    if (isConnected) return 'Disconnect';
    return 'Connect Voice';
  };

  const getConnectionButtonIcon = () => {
    if (isConnected) return <PhoneOff className="w-4 h-4" />;
    return <Phone className="w-4 h-4" />;
  };

  const getModeButtonText = () => {
    return mode === 'live' ? 'Switch to PTT' : 'Switch to Live';
  };

  const getPTTButtonText = () => {
    if (!isConnected) return 'Not Connected';
    if (isPTTActive) return 'Release to Send';
    return 'Hold to Talk';
  };

  return (
    <div className="flex flex-col gap-3 p-4 bg-gray-50 rounded-lg border">
      {/* 错误显示 */}
      {error && (
        <div className="text-sm text-red-600 bg-red-50 p-3 rounded border border-red-200 flex items-start gap-2">
          <AlertCircle className="w-4 h-4 text-red-500 flex-shrink-0 mt-0.5" />
          <div className="flex-1">
            <div className="font-medium">{VoiceErrorHandler.getErrorIcon(error.type)} {error.userMessage}</div>
            {error.actionText && error.onAction && (
              <button
                onClick={error.onAction}
                className="mt-2 px-3 py-1 text-xs bg-red-100 hover:bg-red-200 text-red-700 rounded transition-colors"
              >
                {error.actionText}
              </button>
            )}
          </div>
          <button
            onClick={clearError}
            className="text-red-400 hover:text-red-600 flex-shrink-0"
          >
            <X className="w-4 h-4" />
          </button>
        </div>
      )}

      {/* 连接控制 */}
      <div className="flex items-center gap-2">
        <button
          onClick={toggleConnection}
          disabled={isConnecting}
          className={`flex items-center gap-2 px-4 py-2 rounded-md font-medium transition-colors ${
            isConnected
              ? 'bg-red-500 hover:bg-red-600 text-white'
              : 'bg-green-500 hover:bg-green-600 text-white disabled:bg-gray-400'
          }`}
        >
          {getConnectionButtonIcon()}
          {getConnectionButtonText()}
        </button>

        {/* 连接状态指示器 */}
        <div className="flex items-center gap-2">
          <div
            className={`w-2 h-2 rounded-full ${
              isConnected
                ? 'bg-green-500'
                : isConnecting
                ? 'bg-yellow-500 animate-pulse'
                : 'bg-gray-400'
            }`}
          />
          <span className="text-sm text-gray-600">
            {isConnected ? 'Connected' : isConnecting ? 'Connecting' : 'Disconnected'}
          </span>
        </div>
      </div>

      {/* 模式切换 */}
      {isConnected && (
        <div className="flex items-center gap-2">
          <button
            onClick={toggleMode}
            className="px-3 py-1 text-sm bg-blue-100 hover:bg-blue-200 text-blue-700 rounded-md transition-colors"
          >
            {getModeButtonText()}
          </button>
          <span className="text-sm text-gray-600">
            Mode: <span className="font-medium">{mode.toUpperCase()}</span>
          </span>
        </div>
      )}

      {/* PTT 控制 (仅在 PTT 模式下显示) */}
      {isConnected && mode === 'ptt' && (
        <div className="flex items-center gap-2">
          <button
            onMouseDown={handlePTTStart}
            onMouseUp={handlePTTEnd}
            onMouseLeave={handlePTTEnd}
            onTouchStart={handlePTTStart}
            onTouchEnd={handlePTTEnd}
            disabled={!isConnected}
            className={`flex items-center gap-2 px-4 py-2 rounded-md font-medium transition-all duration-200 select-none transform ${
              isPTTActive
                ? 'bg-red-500 text-white scale-95 shadow-inner'
                : 'bg-blue-500 hover:bg-blue-600 text-white disabled:bg-gray-400 hover:scale-105 shadow-lg'
            }`}
            style={{
              touchAction: 'manipulation', // 优化触摸体验
            }}
          >
            <Mic className={`w-4 h-4 ${isPTTActive ? 'animate-pulse' : ''}`} />
            {getPTTButtonText()}
          </button>
        </div>
      )}

      {/* 录音状态指示器 */}
      {isConnected && (
        <div className="flex items-center gap-4">
          {/* 录音状态 */}
          <div className="flex items-center gap-2">
            {isRecording ? (
              <MicOff className="w-4 h-4 text-red-500" />
            ) : (
              <Mic className="w-4 h-4 text-gray-400" />
            )}
            <span className="text-sm text-gray-600">
              {isRecording ? 'Recording...' : 'Not recording'}
            </span>
          </div>

          {/* 静音控制 */}
          <button
            onClick={toggleMute}
            className={`flex items-center gap-1 px-2 py-1 text-sm rounded transition-colors ${
              isMuted
                ? 'bg-red-100 text-red-700 hover:bg-red-200'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            {isMuted ? <VolumeX className="w-3 h-3" /> : <Volume2 className="w-3 h-3" />}
            {isMuted ? 'Unmute' : 'Mute'}
          </button>

          {/* 助手响应状态 */}
          {isAssistantResponding && (
            <div className="flex items-center gap-1 px-2 py-1 text-sm bg-blue-100 text-blue-700 rounded">
              <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" />
              AI Responding
            </div>
          )}
        </div>
      )}

      {/* 使用说明 */}
      <div className="text-xs text-gray-500 space-y-1">
        <div><strong>Live Mode:</strong> Voice detection is automatic</div>
        <div><strong>PTT Mode:</strong> Hold button/spacebar to talk, supports touch</div>
        {mode === 'ptt' && isConnected && (
          <div className="text-blue-600"><strong>Tip:</strong> Press and hold spacebar for hands-free PTT</div>
        )}
      </div>
    </div>
  );
} 