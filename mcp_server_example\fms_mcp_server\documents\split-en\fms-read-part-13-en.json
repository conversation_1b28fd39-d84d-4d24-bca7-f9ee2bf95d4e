{"openapi": "3.0.1", "info": {"title": "FMS", "description": "", "version": "1.0.0"}, "paths": {"/fms-platform-dispatch-management/test/route-engine/appendPackageToRouteResult": {"post": {"summary": "Test the logic that triggers AppendPackageToRouteResult", "deprecated": false, "description": "", "tags": ["TestRouteEngine"], "parameters": [], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/AppendPackagesInputV2Dto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AppendPackagesOutputV2DtoRouteEngineApiV2BaseResponse"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/TripTaskList/GetTripTaskList": {"post": {"summary": "Get a task list\r\nGet trip task list", "deprecated": false, "description": "", "tags": ["TripTaskList"], "parameters": [], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/TripTaskListRequestDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TripTaskResponseDto"}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/TripTaskList/BatchCheckOrderTask": {"post": {"summary": "Bulk check order task information\r\nBatch check order task information", "deprecated": false, "description": "", "tags": ["TripTaskList"], "parameters": [], "requestBody": {"content": {"application/json-patch+json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CheckOrderTaskRequestDto"}}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BatchCheckOrderTaskResponseDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-user/Auth/CheckLogin": {"get": {"summary": "AI: Check user login status", "deprecated": false, "description": "", "tags": ["<PERSON><PERSON>"], "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FMSCurrentContextDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-user/Auth/Logout": {"get": {"summary": "AI: User logout endpoint", "deprecated": false, "description": "", "tags": ["<PERSON><PERSON>"], "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-user/Carrier/GetCarriersByCompany": {"get": {"summary": "Query all Carriers under the Company to which the current user belongs\r\nforeign", "deprecated": false, "description": "", "tags": ["Carrier"], "parameters": [{"name": "search", "in": "query", "description": "", "required": false, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CarrierDto"}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-user/Carrier/GetCarriersByCompanyAndTypes": {"post": {"summary": "Query Carrier by company and carrierType", "deprecated": false, "description": "", "tags": ["Carrier"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetCarrierByCompanyAndTypesDto"}, "example": ""}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CarrierDto"}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-user/Carrier/GetDriversByCompany": {"get": {"summary": "Click CarrierCode to query Drivers under Carrier", "deprecated": false, "description": "", "tags": ["Carrier"], "parameters": [{"name": "carrierCode", "in": "query", "description": "", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DriverDto"}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-user/carriers/outsourcing": {"get": {"summary": "Query \"outsourced\" Carriers\r\n<param name=\"search\"></param>", "deprecated": false, "description": "", "tags": ["Carrier"], "parameters": [{"name": "search", "in": "query", "description": "", "required": false, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CarrierDto"}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-user/Carrier/GetDriversByContractorAndIndividual": {"get": {"summary": "Check Contractor and Individual", "deprecated": false, "description": "", "tags": ["Carrier"], "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DriverDto"}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-user/Carrier/GetCarrierByCarrierCode": {"get": {"summary": "Press CarrierCode to query Carrier information", "deprecated": false, "description": "", "tags": ["Carrier"], "parameters": [{"name": "carrierCode", "in": "query", "description": "", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CarrierRPCDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-user/Carrier/GetCarrierNameByCode": {"get": {"summary": "Press CarrierCode to query Carrier<PERSON>ame", "deprecated": false, "description": "", "tags": ["Carrier"], "parameters": [{"name": "carrierCode", "in": "query", "description": "", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "string"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-user/Carrier/GetDriversByCarrier": {"get": {"summary": "Click CarrierCode to query Drivers under Carrier\r\nforeign", "deprecated": false, "description": "", "tags": ["Carrier"], "parameters": [{"name": "carrierCode", "in": "query", "description": "", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DriverDto"}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-user/Carrier/GetDriversByType": {"get": {"summary": "Query the Drivers of EmployeeAd", "deprecated": false, "description": "", "tags": ["Carrier"], "parameters": [{"name": "userType", "in": "query", "description": "0: Full quantity 1: EmployeeAD", "required": false, "schema": {"type": "integer", "format": "int32"}}, {"name": "search", "in": "query", "description": "", "required": false, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DriverDto"}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-user/carriers/driver-redis-hash": {"get": {"summary": "/fms-platform-user/carriers/driver-redis-hash", "deprecated": false, "description": "", "tags": ["Carrier"], "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-user/companies/all": {"get": {"summary": "Inquiry company-all", "deprecated": false, "description": "", "tags": ["Companies"], "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompanySelectListItemDtoListResultDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-user/companies/mine": {"get": {"summary": "Inquiry company-all", "deprecated": false, "description": "", "tags": ["Companies"], "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StringFmsSelectListItemListResultDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-user/company-info/{source}": {"get": {"summary": "Query company", "deprecated": false, "description": "", "tags": ["Companies"], "parameters": [{"name": "source", "in": "path", "description": "source value 1:unis,2:item", "required": true, "example": 0, "schema": {"type": "integer", "format": "int32", "default": 1}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CompanyInfoBySourceResDto"}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-user/DemoTest/DemoTestList": {"post": {"summary": "/fms-platform-user/DemoTest/DemoTestList", "deprecated": false, "description": "", "tags": ["DemoTest"], "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DemoTestDto"}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-user/Home/HealthCheck": {"get": {"summary": "AI: Health check endpoint, allowing anonymous access", "deprecated": false, "description": "", "tags": ["Home"], "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "string"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}}, "components": {"schemas": {"AccessorialServiceDto": {"type": "object", "properties": {"code": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CarrierTypeEnum": {"enum": [2, 6, 7, 8], "type": "integer", "format": "int32"}, "CarrierRPCDto": {"type": "object", "properties": {"is_active": {"type": "boolean"}, "carrier_name": {"type": "string", "nullable": true}, "carrier_code": {"type": "string", "nullable": true}, "carrier_type": {"$ref": "#/components/schemas/CarrierTypeEnum"}, "auto_accept": {"type": "boolean"}, "vendor_code": {"type": "string", "nullable": true}, "vendor_name": {"type": "string", "nullable": true}, "company_id": {"type": "integer", "format": "int32"}, "company_name": {"type": "string", "nullable": true}, "create_from": {"type": "string", "nullable": true}, "source": {"type": "string", "nullable": true}, "system_id": {"type": "string", "nullable": true}, "tenant_id": {"type": "string", "nullable": true}, "id": {"type": "integer", "format": "int64"}}, "additionalProperties": false}, "CompanyInfoBySourceResDto": {"type": "object", "properties": {"company_id": {"type": "string", "nullable": true}, "company_name": {"type": "string", "nullable": true}, "company_alias": {"type": "string", "nullable": true}, "company_code": {"type": "string", "nullable": true}, "company_number": {"type": "string", "nullable": true}, "company_status": {"type": "integer", "format": "int32"}, "company_status_text": {"type": "string", "nullable": true}, "source": {"type": "integer", "format": "int32"}, "parent_id": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CompanySelectListItemDto": {"type": "object", "properties": {"company_code": {"type": "string", "nullable": true}, "company_name": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ApptObject": {"type": "object", "properties": {"pickupDate": {"type": "string", "nullable": true}, "pickupFromTime": {"type": "string", "nullable": true}, "pickupToTime": {"type": "string", "nullable": true}, "deliveryDate": {"type": "string", "nullable": true}, "deliveryFromTime": {"type": "string", "nullable": true}, "deliveryToTime": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CompanySelectListItemDtoListResultDto": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/CompanySelectListItemDto"}, "nullable": true}}, "additionalProperties": false}, "GetCarrierByCompanyAndTypesDto": {"type": "object", "properties": {"company_code": {"type": "string", "nullable": true}, "search": {"type": "string", "nullable": true}, "carrier_types": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}}, "additionalProperties": false}, "AppendPackagesInputV2Dto": {"type": "object", "properties": {"routeResultId": {"type": "integer", "format": "int64"}, "packageNos": {"type": "array", "items": {"type": "string"}, "nullable": true}, "withoutSpaceCheck": {"type": "integer", "format": "int32"}, "withoutAreaCheck": {"type": "integer", "format": "int32"}, "packageList": {"type": "array", "items": {"$ref": "#/components/schemas/PackagePushV2Object"}, "nullable": true}}, "additionalProperties": false}, "Location": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "street": {"type": "string", "nullable": true}, "city": {"type": "string", "nullable": true}, "state": {"type": "string", "nullable": true}, "zip_code": {"type": "string", "nullable": true}}, "additionalProperties": false}, "AppendPackagesOutputV2Dto": {"type": "object", "properties": {"successList": {"type": "array", "items": {"$ref": "#/components/schemas/BaseResultResultOutPutV2Dto"}, "nullable": true}, "failList": {"type": "array", "items": {"$ref": "#/components/schemas/BaseResultResultOutPutV2Dto"}, "nullable": true}}, "additionalProperties": false}, "AppendPackagesOutputV2DtoRouteEngineApiV2BaseResponse": {"type": "object", "properties": {"code": {"type": "string", "nullable": true}, "msg": {"type": "string", "nullable": true}, "error_log_id": {"type": "string", "nullable": true}, "data": {"$ref": "#/components/schemas/AppendPackagesOutputV2Dto"}}, "additionalProperties": false}, "Dispatch": {"type": "object", "properties": {"dispatchDate": {"type": "string", "nullable": true}}, "additionalProperties": false}, "DemoTestDto": {"type": "object", "properties": {"age": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "id": {"type": "integer", "format": "int64"}}, "additionalProperties": false}, "CarrierDto": {"type": "object", "properties": {"carrier_name": {"type": "string", "nullable": true}, "carrier_code": {"type": "string", "nullable": true}, "carrier_type": {"$ref": "#/components/schemas/CarrierTypeEnum"}, "company_name": {"type": "string", "nullable": true}, "id": {"type": "integer", "format": "int64"}}, "additionalProperties": false}, "AssignCarrierTripInfo": {"type": "object", "properties": {"trip_no": {"type": "integer", "format": "int64"}, "dispatch_date": {"type": "string", "format": "date-time", "nullable": true}, "driver": {"type": "string", "nullable": true}, "driver_name": {"type": "string", "nullable": true}, "carrier": {"type": "string", "nullable": true}, "carrier_name": {"type": "string", "nullable": true}, "trailer": {"type": "string", "nullable": true}, "tractor": {"type": "string", "nullable": true}, "seal": {"type": "string", "nullable": true}, "dst_terminal": {"type": "string", "nullable": true}, "dispatcher_note": {"type": "string", "nullable": true}}, "additionalProperties": false}, "FMSClientTypeEnum": {"enum": [0, 1, 2], "type": "integer", "format": "int32"}, "BaseResultResultOutPutV2Dto": {"type": "object", "properties": {"packageNo": {"type": "string", "nullable": true}, "message": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BatchCheckOrderTaskResponseDto": {"type": "object", "properties": {"order_tasks": {"type": "array", "items": {"$ref": "#/components/schemas/CheckOrderTaskResponseDto"}, "nullable": true}, "dispatch_order_tasks": {"type": "array", "items": {"$ref": "#/components/schemas/CheckOrderTaskResponseDto"}, "nullable": true}, "preplan_order_tasks": {"type": "array", "items": {"$ref": "#/components/schemas/CheckOrderTaskResponseDto"}, "nullable": true}, "event_type": {"$ref": "#/components/schemas/EventTypeEnum"}, "trip_no": {"type": "integer", "format": "int64"}, "batch_no": {"type": "string", "nullable": true}, "assign_carrier_trip_info": {"$ref": "#/components/schemas/AssignCarrierTripInfo"}}, "additionalProperties": false}, "FMSCurrentContextDto": {"type": "object", "properties": {"user_account": {"type": "string", "nullable": true}, "user_id": {"type": "integer", "format": "int64"}, "user_roles": {"type": "array", "items": {"type": "string"}, "nullable": true}, "user_email": {"type": "string", "nullable": true}, "user_type": {"$ref": "#/components/schemas/UserTypeEnum"}, "first_name": {"type": "string", "nullable": true}, "last_name": {"type": "string", "nullable": true}, "fms_client_type": {"$ref": "#/components/schemas/FMSClientTypeEnum"}, "token": {"type": "string", "nullable": true}, "system_id": {"type": "string", "nullable": true}, "carrier_code": {"type": "string", "nullable": true}, "carrier_name": {"type": "string", "nullable": true}, "primary_contractor_role": {"type": "string", "nullable": true}, "user_code": {"type": "string", "nullable": true}, "full_name": {"type": "string", "nullable": true}, "company_id": {"type": "integer", "format": "int32"}, "company_code": {"type": "string", "nullable": true}, "tenant_id": {"type": "string", "nullable": true}, "time_zone": {"type": "string", "nullable": true}, "actual_operate_time": {"type": "string", "format": "date-time"}, "display_job_rate": {"type": "integer", "format": "int32"}, "terminal": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "CheckOrderTaskRequestDto": {"type": "object", "properties": {"batch_no": {"type": "string", "nullable": true}, "order_no": {"type": "string", "nullable": true}, "pu_no": {"type": "string", "nullable": true}, "task_type": {"type": "string", "nullable": true}, "quote_id": {"type": "integer", "format": "int64"}}, "additionalProperties": false}, "CheckOrderTaskResponseDto": {"type": "object", "properties": {"batch_no": {"type": "string", "nullable": true}, "order_no": {"type": "string", "nullable": true}, "pro": {"type": "string", "nullable": true}, "pu_no": {"type": "string", "nullable": true}, "task_no": {"type": "integer", "format": "int64", "nullable": true}, "task_type": {"type": "string", "nullable": true}, "trip_no": {"type": "integer", "format": "int64", "nullable": true}, "trip_status": {"type": "string", "nullable": true}, "trip_assign_status": {"type": "string", "nullable": true}, "carrier_code": {"type": "string", "nullable": true}, "carrier_name": {"type": "string", "nullable": true}, "driver_code": {"type": "string", "nullable": true}, "driver_name": {"type": "string", "nullable": true}, "quote_id": {"type": "integer", "format": "int64"}, "appointment_time": {"type": "string", "nullable": true}, "appointment_time_from": {"type": "string", "nullable": true}, "appointment_time_to": {"type": "string", "nullable": true}, "weight": {"type": "number", "format": "double", "nullable": true}, "pallets": {"type": "integer", "format": "int32", "nullable": true}, "service_level": {"type": "string", "nullable": true}, "sub_event_type": {"$ref": "#/components/schemas/EventTypeEnum"}, "address_detail": {"$ref": "#/components/schemas/Location"}}, "additionalProperties": false}, "DriverDto": {"type": "object", "properties": {"driver_code": {"type": "string", "nullable": true}, "driver_name": {"type": "string", "nullable": true}, "carrier_name": {"type": "string", "nullable": true}, "carrier_code": {"type": "string", "nullable": true}, "user_type": {"$ref": "#/components/schemas/UserTypeEnum"}}, "additionalProperties": false}, "EventTypeEnum": {"enum": [0, 1, 2, 3], "type": "integer", "format": "int32"}, "Manifest": {"type": "object", "properties": {"code": {"type": "string", "nullable": true}, "pallet": {"type": "integer", "format": "int64", "nullable": true}, "weight": {"type": "integer", "format": "int64", "nullable": true}}, "additionalProperties": false}, "LinehaulPreDispatchObject": {"type": "object", "properties": {"etd": {"type": "string", "nullable": true}, "route_id": {"type": "string", "nullable": true}}, "additionalProperties": false}, "LocationV2Object": {"type": "object", "properties": {"address1": {"type": "string", "nullable": true}, "address2": {"type": "string", "nullable": true}, "address3": {"type": "string", "nullable": true}, "address4": {"type": "string", "nullable": true}, "city": {"type": "string", "nullable": true}, "code": {"type": "string", "nullable": true}, "country": {"type": "string", "nullable": true}, "group": {"type": "string", "nullable": true}, "lat": {"type": "number", "format": "double"}, "lng": {"type": "number", "format": "double"}, "name": {"type": "string", "nullable": true}, "state": {"type": "string", "nullable": true}, "street": {"type": "string", "nullable": true}, "zip": {"type": "string", "nullable": true}, "terminalCode": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UserTypeEnum": {"enum": [0, 1, 2, 3, 4, 5, 6, 7, 8], "type": "integer", "format": "int32"}, "PackagePushV2Object": {"type": "object", "properties": {"packageNo": {"type": "string", "nullable": true}, "orderNo": {"type": "string", "nullable": true}, "taskNo": {"type": "string", "nullable": true}, "orderCreateTime": {"type": "string", "nullable": true}, "taskType": {"type": "integer", "format": "int32"}, "revenueType": {"type": "string", "nullable": true}, "shipmentType": {"type": "string", "nullable": true}, "serviceLevel": {"type": "string", "nullable": true}, "linear": {"type": "integer", "format": "int32"}, "polygonCode": {"type": "string", "nullable": true}, "toPolygonCode": {"type": "string", "nullable": true}, "appointment": {"$ref": "#/components/schemas/ApptObject"}, "shipper": {"$ref": "#/components/schemas/LocationV2Object"}, "consignee": {"$ref": "#/components/schemas/LocationV2Object"}, "orgTerminalCode": {"type": "string", "nullable": true}, "destTerminalCode": {"type": "string", "nullable": true}, "manifest": {"type": "array", "items": {"$ref": "#/components/schemas/Manifest"}, "nullable": true}, "totalWeight": {"type": "integer", "format": "int32"}, "totalPallet": {"type": "integer", "format": "int32"}, "billto": {"type": "string", "nullable": true}, "dispatch": {"$ref": "#/components/schemas/Dispatch"}, "accessorialServiceList": {"type": "array", "items": {"$ref": "#/components/schemas/AccessorialServiceDto"}, "nullable": true}, "hotZoneCode": {"type": "string", "nullable": true}, "ratedWeight": {"type": "number", "format": "double"}, "linehaulPreDispatch": {"$ref": "#/components/schemas/LinehaulPreDispatchObject"}}, "additionalProperties": false}, "StringFmsSelectListItem": {"type": "object", "properties": {"text": {"type": "string", "nullable": true}, "value": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StringFmsSelectListItemListResultDto": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/StringFmsSelectListItem"}, "nullable": true}}, "additionalProperties": false}, "TripTaskListRequestDto": {"type": "object", "properties": {"trip_no": {"type": "integer", "format": "int64"}, "lh_no": {"type": "integer", "format": "int64"}, "order_nos": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "TripTaskResponseDto": {"type": "object", "properties": {"trip_no": {"type": "integer", "format": "int64"}, "trip_org_terminal": {"type": "string", "nullable": true}, "trip_dst_terminal": {"type": "string", "nullable": true}, "trip_status": {"type": "string", "nullable": true}, "trip_assign_status": {"type": "string", "nullable": true}, "trip_carrier_code": {"type": "string", "nullable": true}, "lh_no": {"type": "integer", "format": "int64"}, "lh_from_terminal": {"type": "string", "nullable": true}, "lh_to_terminal": {"type": "string", "nullable": true}, "order_no": {"type": "string", "nullable": true}, "task_no": {"type": "integer", "format": "int64"}, "task_type": {"type": "string", "nullable": true}, "task_status": {"type": "string", "nullable": true}}, "additionalProperties": false}}}}