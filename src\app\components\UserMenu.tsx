'use client';

import React, { useState, useRef, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { Brain, LogOut } from 'lucide-react';
import { useRouter } from 'next/navigation';
import '@/styles/item-design-system.css';

export default function UserMenu() {
  const { user, logout } = useAuth();
  const [isOpen, setIsOpen] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);
  const router = useRouter();

  // 处理点击外部关闭菜单
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // 处理打开记忆管理页面
  const handleOpenMemoryManager = () => {
    setIsOpen(false);
    router.push('/admin/memories');
  };

  if (!user) return null;

  return (
    <div className="relative" ref={menuRef}>
      {/* Menu icon button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="text-item-gray-400 hover:text-white transition-all duration-200 focus:outline-none p-1.5 rounded-lg hover:bg-item-bg-hover"
        aria-label="User menu"
      >
        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
          <circle cx="12" cy="12" r="1"></circle>
          <circle cx="12" cy="5" r="1"></circle>
          <circle cx="12" cy="19" r="1"></circle>
        </svg>
      </button>

      {/* Dropdown menu */}
      {isOpen && (
        <div 
          className="absolute right-0 bottom-full mb-2 w-48 bg-item-bg-card border border-item-purple/30 rounded-lg shadow-xl shadow-item-purple/20 z-50 overflow-hidden item-animate-in"
          onMouseMove={(e) => e.stopPropagation()}
          onClick={(e) => e.stopPropagation()}
        >
          {/* Menu options */}
          <div className="py-1">
            {/* 记忆管理选项 */}
            <button
              onClick={handleOpenMemoryManager}
              className="w-full text-left px-4 py-2 text-sm text-white hover:bg-item-bg-hover transition-all duration-200 flex items-center"
            >
              <Brain className="h-4 w-4 mr-2 text-item-purple" />
              <span>Memory</span>
            </button>

            {/* 登出选项 */}
            <button
              onClick={logout}
              className="w-full text-left px-4 py-2 text-sm text-white hover:bg-item-bg-hover transition-all duration-200 flex items-center"
            >
              <LogOut className="h-4 w-4 mr-2 text-item-orange" />
              <span>Logout</span>
            </button>
          </div>
        </div>
      )}


    </div>
  );
}