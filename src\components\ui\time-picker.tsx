'use client';

import * as React from 'react';
import { format } from 'date-fns';
import { Clock, X, ChevronUp, ChevronDown } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';

interface TimePickerProps {
  time: string | undefined;
  setTime: (time: string | undefined) => void;
  className?: string;
  placeholder?: string;
  label?: string;
  name?: string;
  showSeconds?: boolean;
}

export function TimePicker({
  time,
  setTime,
  className,
  placeholder = 'Select time...',
  label,
  name,
  showSeconds = true,
}: TimePickerProps) {
  const [isOpen, setIsOpen] = React.useState(false);
  const [tempTime, setTempTime] = React.useState<Date | undefined>(undefined);
  const [selectedHour, setSelectedHour] = React.useState<string>('00');
  const [selectedMinute, setSelectedMinute] = React.useState<string>('00');
  const [selectedSecond, setSelectedSecond] = React.useState<string>('00');
  
  const hourRef = React.useRef<HTMLDivElement>(null);
  const minuteRef = React.useRef<HTMLDivElement>(null);
  const secondRef = React.useRef<HTMLDivElement>(null);
  
  // Hours, minutes and seconds for the time picker
  const hours = Array.from({ length: 24 }, (_, i) => i.toString().padStart(2, '0'));
  const minutes = Array.from({ length: 60 }, (_, i) => i.toString().padStart(2, '0'));
  const seconds = Array.from({ length: 60 }, (_, i) => i.toString().padStart(2, '0'));
  
  // Parse time string to Date object
  const parseTimeString = (timeStr: string): Date => {
    const [hours, minutes, seconds] = timeStr.split(':').map(Number);
    const date = new Date();
    date.setHours(hours || 0);
    date.setMinutes(minutes || 0);
    date.setSeconds(seconds || 0);
    return date;
  };
  
  // Format time string from Date object
  const formatTimeString = (date: Date): string => {
    if (showSeconds) {
      return format(date, 'HH:mm:ss');
    }
    return format(date, 'HH:mm');
  };
  
  // Initialize state based on provided time
  React.useEffect(() => {
    if (time) {
      try {
        const parsedTime = parseTimeString(time);
        setTempTime(parsedTime);
        setSelectedHour(format(parsedTime, 'HH'));
        setSelectedMinute(format(parsedTime, 'mm'));
        setSelectedSecond(format(parsedTime, 'ss'));
      } catch (error) {
        console.error('Invalid time format:', time);
        setTempTime(undefined);
        setSelectedHour('00');
        setSelectedMinute('00');
        setSelectedSecond('00');
      }
    } else {
      setTempTime(undefined);
      setSelectedHour('00');
      setSelectedMinute('00');
      setSelectedSecond('00');
    }
  }, [time, isOpen]);
  
  // Ensure tempTime is set when opening the picker
  React.useEffect(() => {
    if (isOpen && !tempTime) {
      // Create a default time object with selected values when opening
      const defaultTime = new Date();
      defaultTime.setHours(parseInt(selectedHour, 10));
      defaultTime.setMinutes(parseInt(selectedMinute, 10));
      defaultTime.setSeconds(parseInt(selectedSecond, 10));
      setTempTime(defaultTime);
    }
  }, [isOpen, tempTime, selectedHour, selectedMinute, selectedSecond]);
  
  // Scroll selected hour, minute and second into view when opening
  React.useEffect(() => {
    if (isOpen) {
      setTimeout(() => {
        if (hourRef.current) {
          const hourItem = hourRef.current.querySelector(`[data-hour="${selectedHour}"]`);
          if (hourItem) {
            hourItem.scrollIntoView({ block: 'center', behavior: 'smooth' });
          }
        }
        
        if (minuteRef.current) {
          const minuteItem = minuteRef.current.querySelector(`[data-minute="${selectedMinute}"]`);
          if (minuteItem) {
            minuteItem.scrollIntoView({ block: 'center', behavior: 'smooth' });
          }
        }
        
        if (secondRef.current && showSeconds) {
          const secondItem = secondRef.current.querySelector(`[data-second="${selectedSecond}"]`);
          if (secondItem) {
            secondItem.scrollIntoView({ block: 'center', behavior: 'smooth' });
          }
        }
      }, 100);
    }
  }, [isOpen, selectedHour, selectedMinute, selectedSecond, showSeconds]);
  
  // Handle hour selection
  const handleHourSelect = (hour: string) => {
    setSelectedHour(hour);
    
    // Create a new time object if tempTime doesn't exist, otherwise update existing one
    const newTime = tempTime ? new Date(tempTime) : new Date();
    newTime.setHours(parseInt(hour, 10));
    newTime.setMinutes(parseInt(selectedMinute, 10));
    newTime.setSeconds(parseInt(selectedSecond, 10));
    setTempTime(newTime);
  };
  
  // Handle minute selection
  const handleMinuteSelect = (minute: string) => {
    setSelectedMinute(minute);
    
    // Create a new time object if tempTime doesn't exist, otherwise update existing one
    const newTime = tempTime ? new Date(tempTime) : new Date();
    newTime.setHours(parseInt(selectedHour, 10));
    newTime.setMinutes(parseInt(minute, 10));
    newTime.setSeconds(parseInt(selectedSecond, 10));
    setTempTime(newTime);
  };
  
  // Handle second selection
  const handleSecondSelect = (second: string) => {
    setSelectedSecond(second);
    
    // Create a new time object if tempTime doesn't exist, otherwise update existing one
    const newTime = tempTime ? new Date(tempTime) : new Date();
    newTime.setHours(parseInt(selectedHour, 10));
    newTime.setMinutes(parseInt(selectedMinute, 10));
    newTime.setSeconds(parseInt(second, 10));
    setTempTime(newTime);
  };
  
  // Apply selected time
  const handleOk = () => {
    console.log('tempTime', tempTime);
    setTime(tempTime ? formatTimeString(tempTime) : undefined);
    setIsOpen(false);
  };
  
  // Cancel selection
  const handleCancel = () => {
    if (time) {
      try {
        const parsedTime = parseTimeString(time);
        setTempTime(parsedTime);
        setSelectedHour(format(parsedTime, 'HH'));
        setSelectedMinute(format(parsedTime, 'mm'));
        setSelectedSecond(format(parsedTime, 'ss'));
      } catch (error) {
        setTempTime(undefined);
        setSelectedHour('00');
        setSelectedMinute('00');
        setSelectedSecond('00');
      }
    } else {
      setTempTime(undefined);
      setSelectedHour('00');
      setSelectedMinute('00');
      setSelectedSecond('00');
    }
    setIsOpen(false);
  };
  
  // Set to current time
  const handleNow = () => {
    const now = new Date();
    setTempTime(now);
    setSelectedHour(format(now, 'HH'));
    setSelectedMinute(format(now, 'mm'));
    setSelectedSecond(format(now, 'ss'));
  };
  
  // Format time for display
  const formatTimeDisplay = (date: Date) => {
    if (showSeconds) {
      return format(date, 'HH:mm:ss');
    }
    return format(date, 'HH:mm');
  };
  
  // Format time for hidden input
  const formatTimeInput = (date: Date) => {
    if (showSeconds) {
      return format(date, 'HH:mm:ss');
    }
    return format(date, 'HH:mm');
  };
  
  return (
    <div className={className}>
      {label && (
        <label className="block text-sm font-medium mb-2 text-item-gray-400 font-mono">{label}</label>
      )}
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            className={cn(
              'w-full justify-between bg-item-bg-card/80 border-item-purple/30 text-white hover:bg-item-bg-input hover:text-white font-mono transition-all duration-200',
              !time && 'text-item-gray-500',
              'focus:ring-2 focus:ring-item-purple focus:border-transparent',
              'min-w-0'
            )}
          >
            <span className="truncate flex-1 text-left">
              {time ? time : <span>{placeholder}</span>}
            </span>
            <div className="flex items-center flex-shrink-0 ml-2">
              {time && (
                <div
                  onClick={(e) => {
                    e.stopPropagation();
                    e.preventDefault();
                    setTime(undefined);
                  }}
                  className="mr-2 cursor-pointer"
                >
                  <X className="h-4 w-4 text-item-gray-400 hover:text-red-400 transition-all duration-200" />
                </div>
              )}
              <Clock className="h-5 w-5 text-item-purple" />
            </div>
          </Button>
        </PopoverTrigger>
        <PopoverContent
          className="w-auto p-0 bg-item-bg-card/95 backdrop-blur-sm border border-item-purple/50 text-item-purple-light shadow-xl shadow-item-purple/30 z-[9999]"
          align="center"
          sideOffset={8}
        >
          <div className="p-4 flex flex-col">
            <div className="text-center mb-2 text-item-purple-light font-medium">Time</div>
            <div className="flex space-x-4 items-center justify-center">
              {/* Hours */}
              <div className="flex flex-col items-center">
                <div className="text-xs text-item-purple mb-1">Hour</div>
                <Button 
                  variant="ghost" 
                  size="icon" 
                  className="h-6 w-6 rounded-full text-item-purple-light hover:text-item-purple-lighter hover:bg-item-purple/20 transition-all duration-200"
                  onClick={() => {
                    const currentIndex = hours.indexOf(selectedHour);
                    const newIndex = currentIndex > 0 ? currentIndex - 1 : 23;
                    handleHourSelect(hours[newIndex]);
                  }}
                >
                  <ChevronUp className="h-4 w-4" />
                </Button>
                <div 
                  ref={hourRef}
                  className="h-32 overflow-y-auto w-14 scrollbar-thin scrollbar-thumb-item-purple scrollbar-track-item-bg-input/50 scrollbar-thumb-rounded-full"
                >
                  <div className="flex flex-col items-center py-2">
                    {hours.map((hour) => (
                      <div
                        key={hour}
                        data-hour={hour}
                        className={cn(
                          "cursor-pointer py-1 px-2 min-w-[40px] text-center rounded-md my-0.5",
                          hour === selectedHour
                            ? "bg-item-purple text-white font-medium shadow-md transition-all duration-200"
                            : "text-item-gray-400 hover:bg-item-purple/20 hover:text-item-purple-light transition-all duration-200"
                        )}
                        onClick={() => handleHourSelect(hour)}
                      >
                        {hour}
                      </div>
                    ))}
                  </div>
                </div>
                <Button 
                  variant="ghost" 
                  size="icon" 
                  className="h-6 w-6 rounded-full text-item-purple-light hover:text-item-purple-lighter hover:bg-item-purple/20 transition-all duration-200"
                  onClick={() => {
                    const currentIndex = hours.indexOf(selectedHour);
                    const newIndex = currentIndex < 23 ? currentIndex + 1 : 0;
                    handleHourSelect(hours[newIndex]);
                  }}
                >
                  <ChevronDown className="h-4 w-4" />
                </Button>
              </div>
              
              <div className="text-item-purple-light text-lg font-medium">:</div>
              
              {/* Minutes */}
              <div className="flex flex-col items-center">
                <div className="text-xs text-item-purple mb-1">Minute</div>
                <Button 
                  variant="ghost" 
                  size="icon" 
                  className="h-6 w-6 rounded-full text-item-purple-light hover:text-item-purple-lighter hover:bg-item-purple/20 transition-all duration-200"
                  onClick={() => {
                    const currentIndex = minutes.indexOf(selectedMinute);
                    const newIndex = currentIndex > 0 ? currentIndex - 1 : 59;
                    handleMinuteSelect(minutes[newIndex]);
                  }}
                >
                  <ChevronUp className="h-4 w-4" />
                </Button>
                <div 
                  ref={minuteRef}
                  className="h-32 overflow-y-auto w-14 scrollbar-thin scrollbar-thumb-item-purple scrollbar-track-item-bg-input/50 scrollbar-thumb-rounded-full"
                >
                  <div className="flex flex-col items-center py-2">
                    {minutes.map((minute) => (
                      <div
                        key={minute}
                        data-minute={minute}
                        className={cn(
                          "cursor-pointer py-1 px-2 min-w-[40px] text-center rounded-md my-0.5",
                          minute === selectedMinute
                            ? "bg-item-purple text-white font-medium shadow-md transition-all duration-200"
                            : "text-item-gray-400 hover:bg-item-purple/20 hover:text-item-purple-light transition-all duration-200"
                        )}
                        onClick={() => handleMinuteSelect(minute)}
                      >
                        {minute}
                      </div>
                    ))}
                  </div>
                </div>
                <Button 
                  variant="ghost" 
                  size="icon" 
                  className="h-6 w-6 rounded-full text-item-purple-light hover:text-item-purple-lighter hover:bg-item-purple/20 transition-all duration-200"
                  onClick={() => {
                    const currentIndex = minutes.indexOf(selectedMinute);
                    const newIndex = currentIndex < 59 ? currentIndex + 1 : 0;
                    handleMinuteSelect(minutes[newIndex]);
                  }}
                >
                  <ChevronDown className="h-4 w-4" />
                </Button>
              </div>
              
              {showSeconds && (
                <>
                  <div className="text-item-purple-light text-lg font-medium">:</div>
                  
                  {/* Seconds */}
                  <div className="flex flex-col items-center">
                    <div className="text-xs text-item-purple mb-1">Second</div>
                    <Button 
                      variant="ghost" 
                      size="icon" 
                      className="h-6 w-6 rounded-full text-item-purple-light hover:text-item-purple-lighter hover:bg-item-purple/20 transition-all duration-200"
                      onClick={() => {
                        const currentIndex = seconds.indexOf(selectedSecond);
                        const newIndex = currentIndex > 0 ? currentIndex - 1 : 59;
                        handleSecondSelect(seconds[newIndex]);
                      }}
                    >
                      <ChevronUp className="h-4 w-4" />
                    </Button>
                    <div 
                      ref={secondRef}
                      className="h-32 overflow-y-auto w-14 scrollbar-thin scrollbar-thumb-item-purple scrollbar-track-item-bg-input/50 scrollbar-thumb-rounded-full"
                    >
                      <div className="flex flex-col items-center py-2">
                        {seconds.map((second) => (
                          <div
                            key={second}
                            data-second={second}
                            className={cn(
                              "cursor-pointer py-1 px-2 min-w-[40px] text-center rounded-md my-0.5",
                              second === selectedSecond
                                ? "bg-item-purple text-white font-medium shadow-md transition-all duration-200"
                                : "text-item-gray-400 hover:bg-item-purple/20 hover:text-item-purple-light transition-all duration-200"
                            )}
                            onClick={() => handleSecondSelect(second)}
                          >
                            {second}
                          </div>
                        ))}
                      </div>
                    </div>
                    <Button 
                      variant="ghost" 
                      size="icon" 
                      className="h-6 w-6 rounded-full text-item-purple-light hover:text-item-purple-lighter hover:bg-item-purple/20 transition-all duration-200"
                      onClick={() => {
                        const currentIndex = seconds.indexOf(selectedSecond);
                        const newIndex = currentIndex < 59 ? currentIndex + 1 : 0;
                        handleSecondSelect(seconds[newIndex]);
                      }}
                    >
                      <ChevronDown className="h-4 w-4" />
                    </Button>
                  </div>
                </>
              )}
            </div>
          </div>
          
          {/* Button Bar */}
          <div className="flex justify-between items-center p-2 border-t border-item-gray-700/50">
            <Button
              variant="ghost"
              className="text-sm text-item-purple-light hover:bg-item-purple/20 hover:text-item-purple-lighter font-medium transition-all duration-200"
              onClick={handleNow}
            >
              Now
            </Button>
            
            <div className="flex gap-2">
              <Button
                variant="ghost"
                className="text-sm text-red-300 hover:bg-red-900/40 hover:text-red-100 font-medium transition-all duration-200"
                onClick={handleCancel}
              >
                Cancel
              </Button>
              <Button
                variant="ghost"
                className="text-sm text-item-orange hover:bg-item-orange/20 hover:text-item-orange-light font-medium transition-all duration-200"
                onClick={handleOk}
              >
                OK
              </Button>
            </div>
          </div>
        </PopoverContent>
      </Popover>
      {name && time && (
        <input
          type="hidden"
          name={name}
          value={time}
        />
      )}
      
      <style jsx global>{`
        .scrollbar-thin::-webkit-scrollbar {
          width: 6px;
        }
        .scrollbar-track-item-bg-input\/50::-webkit-scrollbar-track {
          background: var(--item-bg-input-alpha-50);
          border-radius: 10px;
        }
        .scrollbar-thumb-item-purple::-webkit-scrollbar-thumb {
          background: var(--item-purple);
          border-radius: 10px;
        }
        .scrollbar-thumb-rounded-full::-webkit-scrollbar-thumb {
          border-radius: 10px;
        }
        /* Firefox */
        .scrollbar-thin {
          scrollbar-width: thin;
          scrollbar-color: var(--item-purple) var(--item-bg-input-alpha-50);
        }
      `}</style>
    </div>
  );
} 