import { BaseAgent, AgentConfig, AgentExecutionContext } from '../base/BaseAgent';
import { CoreTool } from 'ai';
import { 
  getCubeDescTool, 
  getCubeSchemaInfoTool, 
  getCubeFieldEnumQueryIdsTool, 
  getCubeEnumValuesTool, 
  testQuerySqlTool, 
  createDashboardTool 
} from '@/tools/biTool';
import { biSystemPrompt } from '@/prompts/agents/biPrompt';

export class BIAgent extends BaseAgent {
  constructor() {
    const config: AgentConfig = {
      id: 'bi-agent',
      name: 'BI Specialist',
      description: 'Specialized agent for Business Intelligence operations. Handles data analysis, SQL generation, dashboard creation, and reporting.',
      tools: [
        'getCubeDescTool',
        'getCubeSchemaInfoTool', 
        'getCubeFieldEnumQueryIdsTool',
        'getCubeEnumValuesTool',
        'testQuerySqlTool',
        'createDashboardTool'
      ],
      systemPrompt: biSystemPrompt
    };
    
    super(config);
  }
  
  // 加载BI专业工具
  protected async loadSpecializedTools(): Promise<Record<string, CoreTool>> {
    console.log('[BIAgent] Loading BI tools...');
    
    // BI工具是内置的，不是从MCP加载
    const tools: Record<string, CoreTool> = {
      getCubeDescTool,
      getCubeSchemaInfoTool,
      getCubeFieldEnumQueryIdsTool,
      getCubeEnumValuesTool,
      testQuerySqlTool,
      createDashboardTool
    };
    
    console.log(`[BIAgent] Loaded ${Object.keys(tools).length} BI tools`);
    return tools;
  }
  
}