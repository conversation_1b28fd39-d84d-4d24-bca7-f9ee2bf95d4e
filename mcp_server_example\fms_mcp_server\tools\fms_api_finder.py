from llama_index.core import Document, VectorStoreIndex, Settings
from llama_index.embeddings.huggingface import Hugging<PERSON><PERSON>Embedding
from typing import List, Dict, Any
import json
from pathlib import Path
from urllib.parse import unquote
import logging
import os

logger = logging.getLogger(__name__)

class FMSApiFinder:
    _instances = {}  # 改为字典，支持多个模型实例
    
    def __new__(cls, api_file_paths: List[str] = None, model_name: str = "sentence-transformers/all-MiniLM-L12-v2"):
        instance_key = f"{model_name}"
        if instance_key not in cls._instances:
            cls._instances[instance_key] = super(FMSApiFinder, cls).__new__(cls)
            cls._instances[instance_key]._initialized = False
        return cls._instances[instance_key]

    def __init__(self, api_file_paths: List[str] = None, model_name: str = "sentence-transformers/all-MiniLM-L12-v2"):
        if hasattr(self, '_initialized') and self._initialized:
            return
                
        logger.info(f"Initializing FMSApiFinder with model: {model_name}...")
        if api_file_paths is None:
            docs_dir = Path(__file__).parent.parent / 'documents'
            api_file_paths = [
                # str(docs_dir / 'fms-read.json'),
                # str(docs_dir / 'split' / 'fms-read-part-1.json'),
                # str(docs_dir / 'split' / 'fms-read-part-2.json'),
                # str(docs_dir / 'split' / 'fms-read-part-3.json'),
                # str(docs_dir / 'split' / 'fms-read-part-4.json'),
                # str(docs_dir / 'split' / 'fms-read-part-5.json'),
                # str(docs_dir / 'split' / 'fms-read-part-6.json'),
                # str(docs_dir / 'split' / 'fms-read-part-7.json'),
                # str(docs_dir / 'split' / 'fms-read-part-8.json'),
                # str(docs_dir / 'split' / 'fms-read-part-9.json'),
                # str(docs_dir / 'split' / 'fms-read-part-10.json'),
                # str(docs_dir / 'split' / 'fms-read-part-11.json'),
                # str(docs_dir / 'split' / 'fms-read-part-12.json'),
                # str(docs_dir / 'split' / 'fms-read-part-13.json'),
                # str(docs_dir / 'split' / 'fms-read-part-14.json'),
                # str(docs_dir / 'split' / 'fms-read-part-15.json'),
                # str(docs_dir / 'split' / 'fms-read-part-16.json'),
                # str(docs_dir / 'split' / 'fms-read-part-17.json'),
                # str(docs_dir / 'split' / 'fms-read-part-18.json'),
                str(docs_dir / 'split-en' / 'fms-read-part-1-en.json'),
                str(docs_dir / 'split-en' / 'fms-read-part-2-en.json'),
                str(docs_dir / 'split-en' / 'fms-read-part-3-en.json'),
                str(docs_dir / 'split-en' / 'fms-read-part-4-en.json'),
                str(docs_dir / 'split-en' / 'fms-read-part-5-en.json'),
                str(docs_dir / 'split-en' / 'fms-read-part-6-en.json'),
                str(docs_dir / 'split-en' / 'fms-read-part-7-en.json'),
                str(docs_dir / 'split-en' / 'fms-read-part-8-en.json'),
                str(docs_dir / 'split-en' / 'fms-read-part-9-en.json'),
                str(docs_dir / 'split-en' / 'fms-read-part-10-en.json'),
                str(docs_dir / 'split-en' / 'fms-read-part-11-en.json'),
                str(docs_dir / 'split-en' / 'fms-read-part-12-en.json'),
                str(docs_dir / 'split-en' / 'fms-read-part-13-en.json'),
                str(docs_dir / 'split-en' / 'fms-read-part-14-en.json'),
                str(docs_dir / 'split-en' / 'fms-read-part-15-en.json'),
                str(docs_dir / 'split-en' / 'fms-read-part-16-en.json'),
                str(docs_dir / 'split-en' / 'fms-read-part-17-en.json'),
                str(docs_dir / 'split-en' / 'fms-read-part-18-en.json'),
            ]
            
        # 重要：禁用 LLM
        Settings.llm = None
        Settings.chunk_size = 16384
        Settings.embed_model = HuggingFaceEmbedding(model_name=model_name)
            
        self.api_file_paths = api_file_paths
        self.model_name = model_name
        self.api_data = self._load_api_files()
        self.schemas = self._preprocess_schemas()
        self.documents = self._create_documents()
        self.index = self._create_index()
        
        # 记录所有API路径，用于调试和验证
        self.all_api_paths = set()
        for path in self.api_data.get('paths', {}).keys():
            self.all_api_paths.add(path)
        logger.info(f"Total APIs loaded: {len(self.all_api_paths)} with model {model_name}")
            
        self._initialized = True
        logger.info(f"FMSApiFinder initialized successfully with {model_name}")

    @classmethod
    def get_instance(cls, api_file_path: str = None, model_name: str = "sentence-transformers/all-MiniLM-L12-v2"):
        return cls(api_file_path, model_name)

    def _preprocess_schemas(self) -> Dict:
        """预处理所有 schemas，建立引用映射"""
        schemas = {}
        raw_schemas = self.api_data.get('components', {}).get('schemas', {})
        
        # 处理所有可能的编码形式
        for key, value in raw_schemas.items():
            # 存储原始 key
            schemas[key] = value
            # 存储 URL 解码后的 key
            decoded_key = unquote(key)
            if decoded_key != key:
                schemas[decoded_key] = value
            # 存储编码后的 key
            encoded_key = key.replace("<", "%C2%AB").replace(">", "%C2%BB")
            if encoded_key != key:
                schemas[encoded_key] = value
                
        return schemas

    def _resolve_schema_ref(self, schema: Dict, depth: int = 0) -> Dict:
        """Resolve $ref in schema and replace with actual fields"""
        if depth > 10:  # 防止循环引用
            return {"type": "object", "description": "Max depth reached"}
            
        if not isinstance(schema, dict):
            return schema
            
        if '$ref' in schema:
            ref_path = schema['$ref'].split('/')
            ref_name = ref_path[-1]
            
            # 尝试不同形式的引用名称
            if ref_name not in self.schemas:
                # URL 解码尝试
                decoded_name = unquote(ref_name)
                if decoded_name in self.schemas:
                    ref_name = decoded_name
                else:
                    # 如果找不到引用，返回基本结构
                    return {
                        "type": "object",
                        "description": f"Unresolved reference: {ref_name}"
                    }
            
            ref_schema = self.schemas[ref_name]
            return self._resolve_schema_ref(ref_schema, depth + 1)
            
        resolved_schema = {}
        for key, value in schema.items():
            if key == 'properties':
                resolved_schema[key] = {
                    prop_name: self._resolve_schema_ref(prop_schema, depth + 1)
                    for prop_name, prop_schema in value.items()
                }
            elif isinstance(value, dict):
                resolved_schema[key] = self._resolve_schema_ref(value, depth + 1)
            elif isinstance(value, list):
                resolved_schema[key] = [
                    self._resolve_schema_ref(item, depth + 1) if isinstance(item, dict) else item
                    for item in value
                ]
            else:
                resolved_schema[key] = value
                
        return resolved_schema

    def _load_api_files(self) -> Dict[str, Any]:
        merged_data = {
            'paths': {},
            'components': {'schemas': {}}
        }

        total_paths = 0
        """Load FMS API file"""
        for file_path in self.api_file_paths:
            try:
                logger.info(f"Loading API file: {file_path}")
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    
                    # 检查并记录文件内容
                    path_count = len(data.get('paths', {}))
                    schema_count = len(data.get('components', {}).get('schemas', {}))
                    logger.info(f"  - Found {path_count} API paths and {schema_count} schemas in {file_path}")
                    
                    # 记录一些API路径示例
                    if path_count > 0:
                        sample_paths = list(data.get('paths', {}).keys())[:3]
                        logger.info(f"  - Sample paths: {sample_paths}")
                    
                    # Merge paths
                    if 'paths' in data:
                        merged_data['paths'].update(data['paths'])
                        total_paths += path_count
                        
                    # Merge schemas
                    if 'components' in data and 'schemas' in data['components']:
                        merged_data['components']['schemas'].update(
                            data['components']['schemas']
                        )
            except Exception as e:
                logger.error(f"Error loading API file {file_path}: {str(e)}")
                continue

        logger.info(f"Total API paths loaded: {total_paths}")
        return merged_data

    def _create_documents(self) -> List[Document]:
        documents = []
        
        # 定义FMS相关的操作类型和主实体类型
        operation_types = {
            'search': ['search', 'find', 'query', 'list', 'get'],
            'update': ['update', 'modify', 'change', 'edit', 'put'],
            'create': ['create', 'add', 'insert', 'post'],
            'delete': ['delete', 'remove', 'cancel']
        }
        
        # FMS相关的实体类型
        entity_types = [
            'order', 'shipment', 'freight', 'package', 'consignee', 'shipper', 
            'workorder', 'appointment', 'trip', 'task', 'dispatch', 'route',
            'customer', 'carrier', 'location', 'track', 'charge', 'document',
            'master', 'linehaul', 'crossdock', 'revenue'
        ]
        
        for path, methods in self.api_data.get('paths', {}).items():
            for method, details in methods.items():
                # 提取路径中的关键部分
                path_segments = path.lower().split('/')
                path_segments = [s for s in path_segments if s and not s.startswith('{')]
                
                # 识别实体类型
                detected_entity = None
                for entity in entity_types:
                    if entity in path_segments or entity in path.lower():
                        detected_entity = entity
                        break
                
                # 识别操作类型
                detected_operation = None
                for op_type, op_terms in operation_types.items():
                    for term in op_terms:
                        # 优化：只要 term 是 path_segments 某个元素的子串，也算匹配
                        if any(term in seg for seg in path_segments) or (method.lower() in ['post', 'put', 'delete'] and op_type in ['create', 'update', 'delete']):
                            detected_operation = op_type
                            break
                    if detected_operation:
                        break
                        
                # HTTP方法也可以暗示操作类型
                if not detected_operation:
                    if method.lower() == 'get':
                        detected_operation = 'search'
                    elif method.lower() == 'post':
                        detected_operation = 'create'
                    elif method.lower() == 'put':
                        detected_operation = 'update'
                    elif method.lower() == 'delete':
                        detected_operation = 'delete'
                
                # 为语义理解提供更清晰的文档
                content = f"""
                API Path: {path}
                Method: {method.upper()}
                Summary: {details.get('summary', '')}
                """
                
                # 增强文档内容以提高语义匹配，使用通用语言，不限定特定API
                # 添加实体类型描述
                if detected_entity:
                    content += f"\nThis API deals with {detected_entity}s in FMS (Freight Management System). "
                    
                # 添加操作类型描述
                if detected_operation:
                    if detected_operation == 'search':
                        content += f"\nThis API is used to search or retrieve freight management information. "
                    elif detected_operation == 'update':
                        content += f"\nThis API is used to update or modify existing freight data. "
                    elif detected_operation == 'create':
                        content += f"\nThis API is used to create or add new freight data. "
                    elif detected_operation == 'delete':
                        content += f"\nThis API is used to delete or remove freight data. "
                
                # 对搜索/分页API进行通用描述（不限于特定实体）
                if 'search' in path.lower() or 'find' in path.lower() or 'query' in path.lower():
                    content += f"\nThis is a search API for finding freight management information. "
                
                if 'paging' in path.lower() or 'pagination' in path.lower():
                    content += f"\nThis API supports pagination for handling large result sets. "
                
                # 添加详细描述
                content += f"\nDescription: {details.get('description', '')}"
                content += f"\nOperation ID: {details.get('operationId', '')}"
                content += f"\nTags: {', '.join(details.get('tags', []))}"
                
                # 添加参数信息
                parameters_text = ""
                for param in details.get('parameters', []):
                    parameters_text += f"{param.get('name', '')}: {param.get('description', '')}\n"
                
                if parameters_text:
                    content += f"\nParameters:\n{parameters_text}"
                
                # 添加请求体信息
                if 'requestBody' in details and 'content' in details['requestBody']:
                    for content_type, content_details in details['requestBody']['content'].items():
                        if 'schema' in content_details:
                            schema = content_details['schema']
                            if 'properties' in schema:
                                properties = schema['properties']
                                body_text = "\nRequest Body Fields:\n"
                                for prop_name, prop_details in properties.items():
                                    body_text += f"{prop_name}: {prop_details.get('description', '')}\n"
                                content += body_text
                
                metadata = {
                    'path': path,
                    'method': method.upper(),
                    'summary': details.get('summary', ''),
                    'entity_type': detected_entity,
                    'operation_type': detected_operation
                }
                
                documents.append(Document(
                    text=content,
                    metadata=metadata,
                    excluded_llm_metadata_keys=['path', 'method', 'summary', 'entity_type', 'operation_type']
                ))
                
        return documents
        
    def _create_index(self) -> VectorStoreIndex:
        return VectorStoreIndex.from_documents(
            self.documents,
            show_progress=True
        )
        
    def search_apis(self, query: str, top_k: int = 10) -> List[Dict]:
        """Search FMS APIs using embedding similarity and return detailed API information"""
        
        # 保证 top_k 至少为 5
        if top_k <= 5:
            top_k = 5
        
        try:
            # 记录原始查询
            original_query = query
            
            # 使用更多语义信息增强查询
            enhanced_query = query
            query_lower = query.lower()
            
            # 识别查询中的操作和实体
            operations = {
                'search': ['find', 'search', 'get', 'query', 'list', 'retrieve', 'show'],
                'update': ['update', 'modify', 'change', 'edit', 'put'],
                'create': ['create', 'add', 'insert', 'post', 'new'],
                'delete': ['delete', 'remove', 'cancel']
            }
            
            # FMS相关实体
            entities = [
                'order', 'shipment', 'freight', 'package', 'consignee', 'shipper', 
                'workorder', 'appointment', 'trip', 'task', 'dispatch', 'route',
                'customer', 'carrier', 'location', 'track', 'charge', 'document',
                'master', 'linehaul', 'crossdock', 'revenue'
            ]
            
            # 检测查询中的意图
            detected_operation_type = None
            detected_operation_term = None
            detected_entity = None
            
            # 查找操作类型
            for op_type, terms in operations.items():
                for term in terms:
                    if term in query_lower:
                        detected_operation_type = op_type
                        detected_operation_term = term
                        break
                if detected_operation_type:
                    break
                    
            # 查找实体类型
            for entity in entities:
                if entity in query_lower:
                    detected_entity = entity
                    break
            
            # 增强查询以改善向量搜索
            if detected_operation_type and detected_entity:
                if detected_operation_type == 'search':
                    enhanced_query = f"{detected_entity} {detected_operation_term} API that can be used to {detected_operation_term} {detected_entity}s in freight management system with filtering and pagination"
                elif detected_operation_type == 'update':
                    enhanced_query = f"{detected_entity} {detected_operation_term} API that can be used to {detected_operation_term} existing {detected_entity}s in freight management"
                elif detected_operation_type == 'create':
                    enhanced_query = f"{detected_entity} {detected_operation_term} API that can be used to {detected_operation_term} new {detected_entity}s in freight management"
                elif detected_operation_type == 'delete':
                    enhanced_query = f"{detected_entity} {detected_operation_term} API that can be used to {detected_operation_term} {detected_entity}s in freight management"
            # 向量检索 - 使用增强查询，增加搜索范围
            retriever = self.index.as_retriever(similarity_top_k=max(top_k * 3, 30))  # 增加搜索范围
            vector_nodes = retriever.retrieve(enhanced_query)
            # 从向量搜索创建结果
            vector_results = []
            for node in vector_nodes:
                try:
                    metadata = node.metadata
                    path = metadata['path']
                    method = metadata['method'].lower()
                    
                    # 降低相似度阈值，让更多结果通过
                    if node.score is not None and node.score < 0.1:  # 降低阈值从 0.2 到 0.1
                        continue
                        
                    api_details = self.api_data['paths'][path][method]
                    result = self._format_api_result(path, method, api_details, node.score)
                    result['matched_by'] = 'vector_search'
                    
                    # 定义 path_lower 变量
                    path_lower = path.lower()
                    
                    # 为匹配当前查询的API增加分数 - 公平对待所有类型的API
                    if detected_entity and detected_entity in path_lower:
                        result['similarity'] += 0.1
                        
                    if detected_operation_type:
                        # 对于搜索操作
                        if detected_operation_type == 'search' and ('search' in path_lower or 'find' in path_lower or 'query' in path_lower or 'list' in path_lower or 'get' in path_lower or 'show' in path_lower):
                            result['similarity'] += 0.1
                        # 对于更新操作
                        elif detected_operation_type == 'update' and ('update' in path_lower or 'edit' in path_lower or 'modify' in path_lower):
                            result['similarity'] += 0.1
                        # 对于创建操作
                        elif detected_operation_type == 'create' and ('create' in path_lower or 'add' in path_lower or 'new' in path_lower):
                            result['similarity'] += 0.1
                        # 对于删除操作
                        elif detected_operation_type == 'delete' and ('delete' in path_lower or 'remove' in path_lower or 'cancel' in path_lower):
                            result['similarity'] += 0.1
                    
                    # 特别提升直接匹配的API
                    query_words = set(query_lower.split())
                    path_words = set(path_lower.replace('/', ' ').replace('-', ' ').split())
                    
                    # 如果查询词直接出现在路径中，给予额外加分
                    for word in query_words:
                        if len(word) > 2 and word in path_lower:
                            result['similarity'] += 0.15
                    
                    # 特别处理 "GetTrip" 这样的直接匹配
                    if query_lower.replace(' ', '').replace('_', '').replace('-', '') in path_lower.replace(' ', '').replace('_', '').replace('-', ''):
                        result['similarity'] += 0.3
                        
                    vector_results.append(result)
                except Exception as e:
                    logger.warning(f"Error processing vector API result: {str(e)}")
                    continue
            
            # 改进的关键词匹配（仅作为补充）
            keyword_matches = []
            if detected_entity or detected_operation_type:
                # 查询术语和相关词
                query_terms = set(query_lower.split())
                
                # 同义词扩展 - 扩展到所有操作类型
                synonym_map = {}
                for op_type, terms in operations.items():
                    for term in terms:
                        synonym_map[term] = terms
                
                # 扩展FMS实体同义词
                entity_synonyms = {
                    'order': ['shipment', 'orders', 'shipments'],
                    'freight': ['cargo', 'goods'],
                    'package': ['packages', 'parcel'],
                    'trip': ['trips', 'journey'],
                    'task': ['tasks', 'job'],
                    'dispatch': ['dispatching', 'assignment']
                }
                
                for entity, synonyms in entity_synonyms.items():
                    for synonym in synonyms:
                        synonym_map[synonym] = [entity] + synonyms
                
                # 获取扩展查询词
                expanded_query_terms = set(query_terms)
                for term in query_terms:
                    if term in synonym_map:
                        expanded_query_terms.update(synonym_map[term])
                
                # 查找潜在匹配
                for path in self.all_api_paths:
                    path_lower = path.lower()
                    
                    # 基本匹配检查 - 任何实体类型和操作类型的组合
                    if ((detected_entity and detected_entity in path_lower) or 
                        (detected_operation_type and any(term in path_lower for term in operations[detected_operation_type]))):
                        match_score = 0.5  # 基础分
                        method = next(iter(self.api_data['paths'][path].keys()))
                        
                        # 直接匹配查询词的路径给予更高分数
                        for word in query_terms:
                            if len(word) > 2 and word in path_lower:
                                match_score += 0.2
                        
                        keyword_matches.append({
                            'path': path,
                            'method': method.upper(),
                            'similarity': match_score,
                            'matched_by': 'keyword_match'
                        })
            
            # 处理关键词匹配结果
            keyword_results = []
            for match in keyword_matches:
                try:
                    api_details = self.api_data['paths'][match['path']][match['method'].lower()]
                    result = self._format_api_result(match['path'], match['method'], api_details, match['similarity'])
                    result['matched_by'] = 'keyword_match'
                    keyword_results.append(result)
                except Exception as e:
                    logger.warning(f"Error processing keyword API result: {str(e)}")
                    continue
            
            # 合并结果，移除重复项
            all_results = []
            processed_paths = set()
            
            # 首先添加向量搜索结果（优先）
            for result in vector_results:
                path_method = result['path'] + result['method']
                if path_method not in processed_paths:
                    processed_paths.add(path_method)
                    all_results.append(result)
            
            # 再添加关键词匹配结果（仅添加新的）
            for result in keyword_results:
                path_method = result['path'] + result['method']
                if path_method not in processed_paths:
                    processed_paths.add(path_method)
                    all_results.append(result)
            
            # 排序：向量搜索优先，按相似度排序
            all_results.sort(key=lambda x: -x['similarity'])
            
            # 移除对特定API的偏好，支持各种操作类型的API
            # 为分页API提供轻微提升，但不限于特定实体类型
            if detected_operation_type == 'search':
                for i, result in enumerate(all_results):
                    if ('paging' in result['path'].lower() or 'pagination' in result['path'].lower()):
                        # 轻微提升分页API，但不指定实体类型
                        result['similarity'] += 0.05
            
            # 重新排序
            all_results.sort(key=lambda x: -x['similarity'])
            
            # 限制返回结果数量
            return all_results[:top_k]
            
        except Exception as e:
            logger.error(f"Error searching FMS APIs: {str(e)}")
            return []

    def _format_api_result(self, path: str, method: str, api_details: Dict, similarity: float) -> Dict:
        """格式化API结果"""
        result = {
            'path': path,
            'method': method.upper(),
            'similarity': similarity if similarity is not None else 0.0,
            'details': {
                'summary': api_details.get('summary', ''),
                'description': api_details.get('description', ''),
                'tags': api_details.get('tags', []),
                'parameters': {
                    'headers': [],
                    'path': [],
                    'query': [],
                    'body': None
                },
                'responses': {}
            }
        }
        
        # 处理参数
        for param in api_details.get('parameters', []):
            param_info = {
                'name': param.get('name'),
                'description': param.get('description', ''),
                'required': param.get('required', False),
                'schema': self._resolve_schema_ref(param.get('schema', {})),
                'example': param.get('example'),
            }
            
            param_location = param.get('in', '')
            if param_location == 'header':
                result['details']['parameters']['headers'].append(param_info)
            elif param_location == 'path':
                result['details']['parameters']['path'].append(param_info)
            elif param_location == 'query':
                result['details']['parameters']['query'].append(param_info)
        
        # 处理请求体
        if 'requestBody' in api_details:
            request_body = api_details['requestBody']
            content_type = next(iter(request_body.get('content', {})), None)
            if content_type:
                result['details']['parameters']['body'] = {
                    'content_type': content_type,
                    'required': request_body.get('required', False),
                    'description': request_body.get('description', ''),
                    'schema': self._resolve_schema_ref(request_body['content'][content_type].get('schema', {})),
                }
        
        return result

    def get_api_details(self, path: str, method: str = None) -> Dict:
        if path not in self.api_data.get('paths', {}):
            return {}
            
        if method:
            method = method.lower()
            return self.api_data['paths'][path].get(method, {})
            
        return self.api_data['paths'][path] 
    
    def get_available_paths(self) -> List[str]:
        """返回所有可用的API路径，用于调试"""
        return sorted(list(self.all_api_paths))


def compare_embedding_models():
    """对比不同嵌入模型的搜索效果"""
    
    # 测试的嵌入模型列表
    models_to_test = [
        "sentence-transformers/all-MiniLM-L12-v2",  # 原始模型
        # "sentence-transformers/all-MiniLM-L6-v2",   # 更小更快的模型
        # "sentence-transformers/paraphrase-MiniLM-L6-v2",  # 专门用于语义相似性
        # "sentence-transformers/multi-qa-MiniLM-L6-cos-v1",  # 专门用于问答匹配
        # "sentence-transformers/all-mpnet-base-v2",  # 更大更准确的模型
    ]
    
    # test_queries = [
    #     "find trip",
    #     "search order", 
    #     "query shipment",
    #     "get freight information",
    #     "list dispatch tasks"
    # ]
    
    test_queries = [
        "search shipment orders"
        # "what is the order's current status: 12300005631"
    ]

    print("=" * 80)
    print("FMS API Finder - 嵌入模型对比测试")
    print("=" * 80)
    
    for model_name in models_to_test:
        print(f"\n{'='*60}")
        print(f"测试模型: {model_name}")
        print(f"{'='*60}")
        
        try:
            # 创建该模型的实例
            api_finder = FMSApiFinder(model_name=model_name)
            
            for query in test_queries:
                print(f"\n查询: '{query}'")
                print("-" * 40)
                
                results = api_finder.search_apis(query, top_k=5)
                
                if results:
                    for i, result in enumerate(results[:5]):
                        print(f"{i+1}. {result['path']} ({result['method']})")
                        print(f"   相似度: {result['similarity']:.3f}")
                        print(f"   摘要: {result['details']['summary'][:100]}...")
                else:
                    print("   未找到相关结果")
                    
        except Exception as e:
            print(f"模型 {model_name} 测试失败: {str(e)}")
            continue
    
    print(f"\n{'='*80}")
    print("测试完成")
    print(f"{'='*80}")


if __name__ == "__main__":
    # 运行模型对比测试
    compare_embedding_models() 