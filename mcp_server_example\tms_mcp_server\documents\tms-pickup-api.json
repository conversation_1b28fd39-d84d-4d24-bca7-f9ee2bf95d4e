{"openapi": "3.0.0", "info": {"title": "Freight App TMS Orders List API", "version": "1.0.0", "description": "API for retrieving and filtering multiple TMS orders with various criteria. This API is designed for listing and searching orders in bulk, not for detailed single order lookup."}, "servers": [{"url": "https://clientstage.freightapp.com", "description": "Staging server"}], "paths": {"/write_new/get_tms_orders_customer.php": {"post": {"summary": "List and filter multiple TMS orders", "description": "Retrieves a list of TMS orders based on various filter criteria. This endpoint is primarily used for dashboard displays and bulk order searching, not for single order tracking.", "operationId": "listTmsOrdersWithFilters", "requestBody": {"required": true, "content": {"application/x-www-form-urlencoded": {"schema": {"type": "object", "properties": {"input_cust_ref_number": {"type": "string", "description": "Customer reference number"}, "input_pro": {"type": "string", "description": "PRO number , like 10913333466"}, "input_pu": {"type": "string", "description": "Pickup number , like 6920937"}, "input_status": {"type": "array", "items": {"type": "string"}, "description": "Status codes for filtering , -99=All, -1=Cancelled, -90=<PERSON>uo<PERSON>, 0=Pickup, 2=Pickup Complete, 1=<PERSON>haul, 3=Linehaul Complete, 4=Out-For-Delivery, 5=Delivery Complete", "example": ["-99", "-1", "-90", "0", "2", "1", "3", "4", "5"]}, "input_created_from": {"type": "string", "format": "date", "description": "Start date for order creation filter", "example": "2025-01-13"}, "input_created_to": {"type": "string", "format": "date", "description": "End date for order creation filter", "example": "2025-01-13"}, "input_pickup_from": {"type": "string", "description": "Request Pickup start date"}, "input_pickup_to": {"type": "string", "description": "Request Pickup end date"}, "input_pickup_city": {"type": "string", "description": "Pickup city filter"}, "input_delivery_from": {"type": "string", "description": "Delivery start date"}, "input_delivery_to": {"type": "string", "description": "Delivery end date"}, "input_delivery_city": {"type": "string", "description": "Delivery city filter"}, "input_pickup": {"type": "string", "description": "Request Pickup location"}, "input_delivery": {"type": "string", "description": "Delivery location"}, "input_filter_billto": {"type": "string", "description": "Bill-to filter"}, "UserID": {"type": "string", "description": "User ID for authentication, use 29931 by default", "example": "29931"}, "UserToken": {"type": "string", "description": "Authentication token,  use A4tJz18rGC by default", "example": "A4tJz18rGC"}, "pageName": {"type": "string", "description": "Page identifier", "example": "dashboardTms"}}}}}}, "responses": {"200": {"description": "Successful response with list of orders", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "description": "Indicates if the request was successful"}, "data": {"type": "array", "description": "Array of order objects matching the filter criteria", "items": {"type": "object", "properties": {"order_id": {"type": "string", "description": "Order identifier"}, "pu_number": {"type": "string", "description": "Pickup number"}, "pro_number": {"type": "string", "description": "Progressive number"}}}}, "total_count": {"type": "integer", "description": "Total number of orders matching the criteria"}}}}}}}, "tags": ["Order Listing"]}}}, "components": {"securitySchemes": {"UserAuth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "in": "header", "name": "UserToken"}}}}