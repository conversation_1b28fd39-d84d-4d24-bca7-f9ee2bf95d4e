import fs from 'fs';
import path from 'path';
import { ChatHistory } from '@/utils/chatHistoryUtils';
import { ChatAnalysisResult, ChatReport } from './types';
import * as chatStorage from '@/utils/storage';
import { analyzeWithGoogleAI } from './googleAIAnalyzer';

// 添加延迟函数，用于在API调用之间添加间隔
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// Google API请求间隔时间（毫秒）- 每次请求之间等待10秒
const API_REQUEST_INTERVAL = 20000;

// Helper function to load chat history
const loadChatHistory = async (chatId: string): Promise<ChatHistory | null> => {
  try {
    // 使用与应用相同的存储机制加载聊天记录
    const chat = await chatStorage.getChatHistory(chatId);
    if (!chat) {
      console.warn(`聊天记录未找到: ${chatId}`);
      return null;
    }
    return chat;
  } catch (error) {
    console.error(`加载聊天记录 ${chatId} 时出错:`, error);
    return null;
  }
};



/**
 * 获取指定日期的聊天记录
 */
async function getChatsForDate(date: string): Promise<ChatHistory[]> {
  // 获取所有聊天记录
  const allChats = await chatStorage.getChatHistoryList();

  // 将日期转换为不带连字符的格式，用于匹配聊天ID的前缀
  const datePrefix = date.replace(/-/g, '');

  // 过滤出指定日期的聊天记录
  return allChats.filter(chat =>
    chat.id.startsWith(datePrefix) ||
    (chat.createdAt && new Date(chat.createdAt).toISOString().split('T')[0] === date)
  );
}

/**
 * Generate a report for a specific date
 */
export const generateDailyReport = async (date: string): Promise<ChatReport> => {
  console.log(`[ReportAnalyzer] Generating report for ${date}...`);

  // 获取指定日期的所有聊天记录
  const dateChats = await getChatsForDate(date);

  console.log(`[ReportAnalyzer] Found ${dateChats.length} chats for ${date}`);

  // 生成唯一的聊天ID列表和用户ID映射
  const chatIds = dateChats.map(chat => chat.id);
  const chatUserMap: { [chatId: string]: string } = {};
  
  // 记录每个聊天ID对应的用户ID
  dateChats.forEach(chat => {
    if (chat.userId) {
      chatUserMap[chat.id] = chat.userId;
    }
  });

  // 加载并分析每个聊天记录
  const chatAnalyses: Record<string, ChatAnalysisResult> = {};
  let totalMessages = 0;
  let totalResolvedChats = 0;
  let totalUnresolvedChats = 0;

  for (const chat of dateChats) {
    console.log(`[ReportAnalyzer] Analyzing chat: ${chat.id}`);

    try {
      if (!chat.messages) {
        console.warn(`[ReportAnalyzer] Chat ${chat.id} has no messages, skipping`);
        continue;
      }

      totalMessages += chat.messages.length;

      // 使用Google AI分析聊天记录
      const analysis = await analyzeWithGoogleAI(chat);

      // 存储分析结果并更新统计信息
      chatAnalyses[chat.id] = analysis;

      if (analysis.resolved) {
        totalResolvedChats++;
      } else {
        totalUnresolvedChats++;
      }

      // 在处理下一个聊天记录之前添加延迟，避免超出API速率限制
      if (dateChats.indexOf(chat) < dateChats.length - 1) {
        console.log(`[ReportAnalyzer] Waiting ${API_REQUEST_INTERVAL/1000} seconds before next analysis to avoid rate limits...`);
        await delay(API_REQUEST_INTERVAL);
      }
    } catch (error) {
      console.error(`[ReportAnalyzer] Error analyzing chat ${chat.id}:`, error);

      // 如果发生错误，也添加延迟，以防是由于速率限制导致的
      if (dateChats.indexOf(chat) < dateChats.length - 1) {
        console.log(`[ReportAnalyzer] Error occurred, waiting ${API_REQUEST_INTERVAL/1000} seconds before retry...`);
        await delay(API_REQUEST_INTERVAL);
      }
    }
  }

  // 创建报告对象
  const report: ChatReport = {
    id: `report-${date}`,
    date,
    chatIds,
    chatUserMap,
    totalChats: chatIds.length,
    totalMessages,
    totalResolvedChats,
    totalUnresolvedChats,
    chatAnalyses,
    createdAt: new Date().toISOString()
  };
  console.log('[ReportAnalyzer] Report chatUserMap', chatUserMap);
  console.log(`[ReportAnalyzer] Report for ${date} generated successfully`);
  console.log(`[ReportAnalyzer] Stats: ${totalResolvedChats} resolved, ${totalUnresolvedChats} unresolved chats`);

  return report;
};

/**
 * Append new chat analyses to an existing report
 * This function only analyzes chats that are not already in the report
 */
export const appendToDailyReport = async (date: string, existingReport: ChatReport, sinceTimestamp: string): Promise<ChatReport> => {
  console.log(`[ReportAnalyzer] Appending to report for ${date} since ${sinceTimestamp}...`);

  // 获取指定日期的所有聊天记录
  const dateChats = await getChatsForDate(date);

  // 过滤出在指定时间戳之后创建的聊天记录
  const sinceDate = new Date(sinceTimestamp);
  const newChats = dateChats.filter(chat => {
    // 如果该聊天记录已经在报告中，则跳过
    if (existingReport.chatIds.includes(chat.id)) {
      return false;
    }

    // 如果聊天记录有 createdAt 字段，则检查是否在指定时间戳之后创建
    if (chat.createdAt) {
      const chatDate = new Date(chat.createdAt);
      return chatDate > sinceDate;
    }

    // 如果没有 createdAt 字段，则假设它是新的
    return true;
  });

  console.log(`[ReportAnalyzer] Found ${newChats.length} new chats to append for ${date}`);

  if (newChats.length === 0) {
    console.log(`[ReportAnalyzer] No new chats to append, returning existing report`);
    return existingReport;
  }

  // 分析新的聊天记录
  let totalMessages = existingReport.totalMessages;
  let totalResolvedChats = existingReport.totalResolvedChats;
  let totalUnresolvedChats = existingReport.totalUnresolvedChats;
  const chatAnalyses = { ...existingReport.chatAnalyses };
  const chatIds = [...existingReport.chatIds];

  for (const chat of newChats) {
    console.log(`[ReportAnalyzer] Analyzing new chat: ${chat.id}`);

    try {
      if (!chat.messages) {
        console.warn(`[ReportAnalyzer] Chat ${chat.id} has no messages, skipping`);
        continue;
      }

      // 添加到聊天ID列表
      chatIds.push(chat.id);
      totalMessages += chat.messages.length;

      // 使用Google AI分析聊天记录
      const analysis = await analyzeWithGoogleAI(chat);

      // 存储分析结果并更新统计信息
      chatAnalyses[chat.id] = analysis;

      if (analysis.resolved) {
        totalResolvedChats++;
      } else {
        totalUnresolvedChats++;
      }

      // 在处理下一个聊天记录之前添加延迟，避免超出API速率限制
      if (newChats.indexOf(chat) < newChats.length - 1) {
        console.log(`[ReportAnalyzer] Waiting ${API_REQUEST_INTERVAL/1000} seconds before next analysis to avoid rate limits...`);
        await delay(API_REQUEST_INTERVAL);
      }
    } catch (error) {
      console.error(`[ReportAnalyzer] Error analyzing chat ${chat.id}:`, error);

      // 如果发生错误，也添加延迟，以防是由于速率限制导致的
      if (newChats.indexOf(chat) < newChats.length - 1) {
        console.log(`[ReportAnalyzer] Error occurred, waiting ${API_REQUEST_INTERVAL/1000} seconds before retry...`);
        await delay(API_REQUEST_INTERVAL);
      }
    }
  }

  // 更新报告对象
  const updatedReport: ChatReport = {
    ...existingReport,
    totalChats: chatIds.length,
    totalMessages,
    totalResolvedChats,
    totalUnresolvedChats,
    chatIds,
    chatAnalyses,
    // 不更新 createdAt，保留原始创建时间
  };

  console.log(`[ReportAnalyzer] Appended ${newChats.length} new chats to report for ${date}`);
  console.log(`[ReportAnalyzer] Updated stats: ${totalResolvedChats} resolved, ${totalUnresolvedChats} unresolved chats`);

  return updatedReport;
};