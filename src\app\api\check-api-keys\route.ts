import { NextRequest, NextResponse } from 'next/server';

/**
 * API端点: 检查各AI模型提供商的API密钥是否已配置
 * 这个端点不会暴露实际的API密钥，只返回是否已配置的布尔值
 */
export async function GET(req: NextRequest) {
  // 从环境变量中检查各提供商的API密钥是否存在
  const hasOpenAIKey = !!process.env.OPENAI_API_KEY || !!process.env.OPENAI_API_KEY_2;
  const hasAnthropicKey = !!process.env.ANTHROPIC_API_KEY || !!process.env.ANTHROPIC_API_KEY_2;
  const hasGoogleKey = !!process.env.GOOGLE_GENERATIVE_AI_API_KEY || !!process.env.GOOGLE_API_KEY || !!process.env.GOOGLE_API_KEY_2;
  const hasDeepSeekKey = !!process.env.DEEPSEEK_API_KEY;
  
  // 记录一下可用的API密钥情况（不记录实际密钥，只记录可用状态）
  console.log('API Keys availability check:');
  console.log(`OpenAI API key available: ${hasOpenAIKey}`);
  console.log(`Anthropic API key available: ${hasAnthropicKey}`);
  console.log(`Google API key available: ${hasGoogleKey}`);
  console.log(`DeepSeek API key available: ${hasDeepSeekKey}`);
  
  // 返回各提供商API密钥的可用性
  return NextResponse.json({
    openai: hasOpenAIKey,
    anthropic: hasAnthropicKey,
    google: hasGoogleKey,
    deepseek: hasDeepSeekKey
  });
} 