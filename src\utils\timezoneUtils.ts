/**
 * 时区工具函数
 * 提供获取客户端时区信息的功能
 */

/**
 * 获取客户端时区信息
 * @returns 时区信息对象
 */
export function getClientTimezone() {
  try {
    // 获取时区偏移量（分钟）
    const timezoneOffset = new Date().getTimezoneOffset();
    
    // 获取时区名称
    const timezoneName = Intl.DateTimeFormat().resolvedOptions().timeZone;
    
    // 计算时区偏移小时数（注意getTimezoneOffset返回的是负值）
    const timezoneOffsetHours = -timezoneOffset / 60;
    
    // 格式化时区偏移字符串
    const timezoneOffsetString = timezoneOffsetHours >= 0 
      ? `+${timezoneOffsetHours.toString().padStart(2, '0')}:00`
      : `-${Math.abs(timezoneOffsetHours).toString().padStart(2, '0')}:00`;
    
    return {
      name: timezoneName,
      offset: timezoneOffsetHours,
      offsetString: timezoneOffsetString,
      // 兼容性：提供ISO格式的时区字符串
      iso: `UTC${timezoneOffsetString}`
    };
  } catch (error) {
    console.error('Failed to get timezone:', error);
    // 返回默认时区信息
    return {
      name: 'UTC',
      offset: 0,
      offsetString: '+00:00',
      iso: 'UTC+00:00'
    };
  }
}

/**
 * 获取时区信息的字符串表示
 * @returns 时区信息字符串
 */
export function getTimezoneString(): string {
  const timezone = getClientTimezone();
  return `${timezone.name}`;
}

/**
 * 获取时区偏移量字符串
 * @returns 时区偏移量字符串，如 "+08:00" 或 "-05:00"
 */
export function getTimezoneOffsetString(): string {
  const timezone = getClientTimezone();
  return timezone.offsetString;
}

/**
 * 根据时区获取当前日期字符串
 * @param timezone 时区字符串，如 "Asia/Shanghai"
 * @returns 当前日期字符串，格式为 YYYY-MM-DD
 */
export function getCurrentDateByTimezone(timezone?: string): string {
  try {
    if (timezone) {
      // 使用指定时区获取当前时间
      const now = new Date();
      const options: Intl.DateTimeFormatOptions = {
        timeZone: timezone,
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      };
      
      const dateString = new Intl.DateTimeFormat('en-CA', options).format(now);
      return dateString; // 返回 YYYY-MM-DD 格式
    } else {
      // 如果没有指定时区，使用本地时间
      return new Date().toISOString().split('T')[0];
    }
  } catch (error) {
    console.error('Error getting current date by timezone:', error);
    // 如果时区无效，回退到本地时间
    return new Date().toISOString().split('T')[0];
  }
}

/**
 * 根据时区获取当前时间字符串
 * @param timezone 时区字符串，如 "Asia/Shanghai"
 * @returns 当前时间字符串，格式为 YYYY-MM-DD HH:MM:SS
 */
export function getCurrentDateTimeByTimezone(timezone?: string): string {
  try {
    if (timezone) {
      // 使用指定时区获取当前时间
      const now = new Date();
      const options: Intl.DateTimeFormatOptions = {
        timeZone: timezone,
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
      };
      
      const dateTimeString = new Intl.DateTimeFormat('en-CA', options).format(now);
      // 将格式转换为 YYYY-MM-DD HH:MM:SS
      const parts = dateTimeString.split(', ');
      const datePart = parts[0];
      const timePart = parts[1];
      return `${datePart} ${timePart}`;
    } else {
      // 如果没有指定时区，使用本地时间
      const now = new Date();
      const dateString = now.toISOString().split('T')[0];
      const timeString = now.toTimeString().split(' ')[0];
      return `${dateString} ${timeString}`;
    }
  } catch (error) {
    console.error('Error getting current datetime by timezone:', error);
    // 如果时区无效，回退到本地时间
    const now = new Date();
    const dateString = now.toISOString().split('T')[0];
    const timeString = now.toTimeString().split(' ')[0];
    return `${dateString} ${timeString}`;
  }
}

/**
 * 测试时区功能
 * 用于验证时区获取是否正常工作
 */
export function testTimezoneFunction() {
  const timezone = getClientTimezone();
  const timezoneString = getTimezoneString();
  const offsetString = getTimezoneOffsetString();
  
  // 测试不同时区的日期时间获取
  const testTimezones = ['Asia/Shanghai', 'America/New_York', 'Europe/London', 'UTC'];
  const timezoneResults: any = {};
  
  testTimezones.forEach(tz => {
    timezoneResults[tz] = {
      date: getCurrentDateByTimezone(tz),
      datetime: getCurrentDateTimeByTimezone(tz)
    };
  });
  
  console.log('Timezone test results:');
  console.log('Full timezone object:', timezone);
  console.log('Timezone string:', timezoneString);
  console.log('Offset string:', offsetString);
  console.log('Timezone-specific dates:', timezoneResults);
  
  return {
    timezone,
    timezoneString,
    offsetString,
    timezoneResults
  };
}