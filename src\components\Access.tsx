'use client';

import React, { ReactNode } from 'react';
import { usePermission } from '@/hooks/usePermission';
import { UserRole } from '@/utils/permissionConfig';

interface AccessProps {
  /**
   * 所需的角色或权限
   * 可以是单个角色名称或角色数组
   * 可以是 UserRole 枚举或字符串
   *
   * 例如:
   * - role={UserRole.ADMIN}
   * - role="admin"
   * - role={[UserRole.ADMIN, UserRole.REPORT_VIEWER]}
   * - role={["admin", "report_viewer"]}
   */
  role?: UserRole | UserRole[] | string | string[];

  /**
   * 所需的角色名称 (字符串形式)
   * 与 role 属性功能相同，但更明确地表示使用字符串角色名称
   * 提供此属性是为了向后兼容 RoleAccess 组件
   *
   * 例如:
   * - auth="admin"
   * - auth={["admin", "report_viewer"]}
   */
  auth?: string | string[];

  /**
   * 当用户没有权限时显示的内容
   * 如果不提供，则不显示任何内容
   */
  fallback?: ReactNode;

  /**
   * 子元素 - 当用户有权限时显示
   */
  children: ReactNode;
}

/**
 * 访问控制组件
 * 根据用户角色控制内容的显示
 */
export function Access({ role, auth, fallback, children }: AccessProps) {
  const { hasRole, userRoles } = usePermission();

  // 将角色名称转换为 UserRole 枚举
  const getRoleEnum = (roleName: string): UserRole | null => {
    // 将角色名称转换为小写
    const lowerCaseRole = roleName.toLowerCase();

    // 映射常见的角色名称
    switch (lowerCaseRole) {
      case 'admin':
        return UserRole.ADMIN;
      case 'user':
        return UserRole.USER;
      case 'report_viewer':
        return UserRole.REPORT_VIEWER;
      default:
        // 尝试直接匹配 UserRole 枚举
        return (UserRole as any)[roleName] || null;
    }
  };

  // 检查用户是否有权限
  const hasAccess = (): boolean => {
    // 合并 role 和 auth 属性
    const combinedRole = role || auth;

    // 如果没有指定角色，则允许访问
    if (!combinedRole) return true;

    // 将单个角色转换为数组
    const requiredRoles = Array.isArray(combinedRole) ? combinedRole : [combinedRole];

    // 检查用户是否拥有任一所需角色
    return requiredRoles.some(requiredRole => {
      // 如果是 UserRole 枚举，直接使用 hasRole 方法
      if (Object.values(UserRole).includes(requiredRole as UserRole)) {
        return hasRole(requiredRole as UserRole);
      }

      // 如果是字符串角色名称
      if (typeof requiredRole === 'string') {
        // 尝试将角色名称转换为 UserRole 枚举
        const roleEnum = getRoleEnum(requiredRole);

        // 如果能转换成枚举，检查用户是否拥有该角色
        if (roleEnum !== null) {
          return hasRole(roleEnum);
        }

        // 否则，直接检查 userRoles 中是否包含该角色名称
        return userRoles.some(role => role.toString() === requiredRole);
      }

      return false;
    });
  };

  // 如果用户有权限，显示子元素
  if (hasAccess()) {
    return <>{children}</>;
  }

  // 如果用户没有权限，显示 fallback 或什么都不显示
  return fallback ? <>{fallback}</> : null;
}

export default Access;
