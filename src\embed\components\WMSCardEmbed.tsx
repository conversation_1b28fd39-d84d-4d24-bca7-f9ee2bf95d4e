import React from 'react';

export default function WMSCardEmbed({ toolInvocation }: { toolInvocation: any }) {
  // Use either the specified display type or infer from tool name
  const displayType = toolInvocation.displayType || 
                     (toolInvocation.toolName === 'find_wms_api' ? 'search' :
                      toolInvocation.toolName === 'call_wms_api' ? 'call' : 'generic');
  
  const isSearch = displayType === 'search';
  const isCall = displayType === 'call';
  
  // Extract the appropriate result data
  const result = toolInvocation.result || {};
  const args = toolInvocation.args || {};
  
  // Get search results count for find operation - check different possible result formats
  let searchResults = [];
  let searchResultsCount = 0;
  
  if (isSearch) {
    // Check all possible locations where results might be stored
    if (result.results) {
      searchResults = Array.isArray(result.results) ? result.results : [result.results];
    } else if (result.apis) {
      searchResults = Array.isArray(result.apis) ? result.apis : [result.apis];
    } else if (result.endpoints) {
      searchResults = Array.isArray(result.endpoints) ? result.endpoints : [result.endpoints];
    } else if (result.data && Array.isArray(result.data)) {
      searchResults = result.data;
    } else if (result.array && Array.isArray(result.array)) {
      searchResults = result.array;
    }
    
    // If an array is empty but we have a count field, use that
    searchResultsCount = searchResults.length || result.count || 0;
  }
  
  // API endpoint information
  let endpoint = '';
  let method = 'GET';
  
  // Get the endpoint from all possible locations
  if (args.endpoint) endpoint = args.endpoint;
  else if (args.url) endpoint = args.url;
  else if (args.path) endpoint = args.path;
  else if (result.endpoint) endpoint = result.endpoint;
  else if (result.url) endpoint = result.url;
  else if (result.path) endpoint = result.path;
  
  // Get the method
  if (args.method) method = args.method;
  else if (result.method) method = result.method;
  
  return (
    <div className="embed-card wms-api p-2 my-1 bg-blue-800/20 rounded border-l-2 border-blue-600/30 border-t border-r border-b border-blue-700/20">
      <div className="flex items-center" style={{ whiteSpace: 'nowrap', width: '100%' }}>
        <div className="mr-2 text-blue-400 flex-shrink-0">
          <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
          </svg>
        </div>
        <div className="text-xs text-blue-300 flex-shrink-0 mr-2">
          {isSearch ? 'API Search:' : isCall ? 'API Call:' : 'WMS:'}
        </div>
        <div className="text-xs text-blue-400 overflow-hidden text-ellipsis" style={{ textOverflow: 'ellipsis', overflow: 'hidden', whiteSpace: 'nowrap' }}>
          {isSearch ? 
            `Found ${searchResultsCount} API${searchResultsCount !== 1 ? 's' : ''}` : 
            `${method} ${endpoint}`}
        </div>
      </div>
    </div>
  );
}