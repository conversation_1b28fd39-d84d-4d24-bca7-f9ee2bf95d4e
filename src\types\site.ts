/**
 * @file 通用类型定义
 * @description 客户端和服务端共用的类型定义
 * @shared
 */

export interface SiteConfig {
  siteId: string;
  publicKey: string;
  privateKey: string;
  rateLimit: {
    points: number;      // 允许的请求次数
    duration: number;    // 时间窗口（秒）
  };
}

export interface TokenPayload {
  domain: string;
  timestamp: number;
  siteId: string;
  nonce: string;  // 请求唯一标识，防止重放攻击
}

export interface RateLimitConfig {
  points: number;
  duration: number;
}

export interface SiteRateLimitConfig {
  [siteId: string]: RateLimitConfig;
} 