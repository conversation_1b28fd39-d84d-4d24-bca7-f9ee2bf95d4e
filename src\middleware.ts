import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { apiAuthMiddleware } from './app/middleware/apiAuthMiddleware';
import { roleAuthMiddleware } from './app/middleware/roleAuthMiddleware';

/**
 * 中间件管道函数
 * 按顺序执行多个中间件函数，如果任何一个返回非200状态，则中断执行
 * @param middlewares 要执行的中间件函数数组
 * @returns 组合后的中间件函数
 */
const middlewarePipeline = (middlewares: Array<(req: NextRequest) => Promise<NextResponse>>) => {
  return async (req: NextRequest): Promise<NextResponse> => {
    let response = NextResponse.next();

    for (const middleware of middlewares) {
      response = await middleware(req);

      // 如果中间件返回非200状态，中断执行
      if (response.status !== 200) {
        return response;
      }
    }

    return response;
  };
};

/**
 * Next.js Middleware
 * 这是所有中间件处理的入口点
 */
export async function middleware(req: NextRequest) {
  // 对API路由应用中间件
  if (req.nextUrl.pathname.startsWith('/api/')) {
    // 使用中间件管道按顺序执行身份验证和角色验证
    const apiMiddlewarePipeline = middlewarePipeline([
      apiAuthMiddleware,
      roleAuthMiddleware
    ]);

    return await apiMiddlewarePipeline(req);
  }

  // 默认 - 不应用中间件
  return NextResponse.next();
}

/**
 * 配置中间件运行的路由
 * https://nextjs.org/docs/app/building-your-application/routing/middleware#matcher
 */
export const config = {
  matcher: [
    // 匹配所有API路由
    '/api/:path*',
    // 根据需要在此处添加更多要由中间件处理的路由
  ],
};