{"openapi": "3.0.1", "info": {"title": "FMS", "description": "", "version": "1.0.0"}, "paths": {"/fms-platform-dispatch-management/Trips/GetFileInfoByTripId": {"post": {"summary": "/fms-platform-dispatch-management/Trips/GetFileInfoByTripId", "deprecated": false, "description": "", "tags": ["Trips"], "parameters": [], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/FileInputDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/FileOutputDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/trip/task/gettask-stop-info": {"post": {"summary": "/fms-platform-dispatch-management/trip/task/gettask-stop-info", "deprecated": false, "description": "", "tags": ["Trips"], "parameters": [{"name": "orderNo", "in": "query", "description": "", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TaskStopInfoDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/trip/task/gettrip-task-info": {"post": {"summary": "Internal interface", "deprecated": false, "description": "", "tags": ["Trips"], "parameters": [], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/AppointmentSelectDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TripTaskRpcDto"}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/Trips/GetTaskInfoByOrderKey": {"post": {"summary": "Internal interface", "deprecated": false, "description": "", "tags": ["Trips"], "parameters": [], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/AppointmentSelectDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TripTaskRpcDto"}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/trip/task/get-task-tasktype-list": {"get": {"summary": "Get the enumeration list of task task type", "deprecated": false, "description": "", "tags": ["Trips"], "parameters": [], "responses": {"200": {"description": "Success", "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/trip/delivery-receipt": {"get": {"summary": "GetDeliveryReceipt", "deprecated": false, "description": "", "tags": ["Trips"], "parameters": [{"name": "tripNo", "in": "query", "description": "", "required": false, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/management-trips/status": {"get": {"summary": "Trip Management list page, Status drop-down box data", "deprecated": false, "description": "", "tags": ["Trips"], "parameters": [], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Int32FmsSelectListItemListResultDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/WeatherForecast": {"get": {"summary": "AI: Get weather forecasts", "deprecated": false, "description": "", "operationId": "GetWeatherForecast", "tags": ["WeatherForecast"], "parameters": [], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WeatherForecast"}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/route-engine-dispatch/trip-gettrips": {"post": {"summary": "Get the Trip list\r\n<PERSON>\r\nJuly 25, 2024 20:35:45", "deprecated": false, "description": "", "tags": ["TripDispatch"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetTripsInputDto"}, "example": ""}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/GetTripsOutputDto"}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/route-engine-dispatch/get-linehaul": {"post": {"summary": "Get Linehaul list", "deprecated": false, "description": "", "tags": ["TripDispatch"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetLinehaulListInputDto"}, "example": ""}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/GetLinehaulListOutputDto"}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/route-engine-dispatch/get-trips": {"post": {"summary": "AI: Get a Trip list\r\n<PERSON>\r\nJuly 25, 2024 20:35:45", "deprecated": false, "description": "", "tags": ["TripDispatch"], "parameters": [], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/GetTripsInputDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/GetTripsOutputDtoPagedResultDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/route-engine-dispatch/get-tripdetails": {"post": {"summary": "AI: <PERSON>", "deprecated": false, "description": "", "tags": ["TripDispatch"], "parameters": [], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/GetTripDetailsInputDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/GetTripDetailsOutputDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/route-engine-dispatch/get-customer-name": {"get": {"summary": "AI: Fuzzy match <PERSON>perName and Consignee<PERSON>ame\r\nforeign", "deprecated": false, "description": "", "tags": ["TripDispatch"], "parameters": [{"name": "shipperOrConsignee", "in": "query", "description": "1:<PERSON><PERSON> 2:Consignee", "required": false, "schema": {"type": "integer", "format": "int32"}}, {"name": "name", "in": "query", "description": "", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringFmsSelectListItemListResultDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/route-engine-dispatch/getShipmentType": {"get": {"summary": "AI: Get the getShipmentType drop-down box data\r\nforeign", "deprecated": false, "description": "", "tags": ["TripDispatch"], "parameters": [], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Int32FmsSelectListItemListResultDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/route-engine-dispatch/getTaskStatus": {"get": {"summary": "AI: Get TaskStatus drop-down box data\r\nforeign", "deprecated": false, "description": "", "tags": ["TripDispatch"], "parameters": [], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Int32FmsSelectListItemListResultDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/route-engine-dispatch/getServiceLevel": {"get": {"summary": "AI: Get ServiceLevel drop-down box data\r\nforeign", "deprecated": false, "description": "", "tags": ["TripDispatch"], "parameters": [], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Int32FmsSelectListItemListResultDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/route-engine-dispatch/full-city": {"get": {"summary": "AI: Get the full amount of City drop-down box data\r\nforeign", "deprecated": false, "description": "", "tags": ["TripDispatch"], "parameters": [{"name": "state", "in": "query", "description": "", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringFmsSelectListItemListResultDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/route-engine-dispatch/full-state": {"get": {"summary": "AI: Get the full amount of State drop-down box data\r\nforeign", "deprecated": false, "description": "", "tags": ["TripDispatch"], "parameters": [], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringFmsSelectListItemListResultDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/route-engine-dispatch/full-zipcode": {"get": {"summary": "AI: Get the full amount of Zipcode drop-down box data\r\nforeign", "deprecated": false, "description": "", "tags": ["TripDispatch"], "parameters": [{"name": "zipcode", "in": "query", "description": "", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringFmsSelectListItemListResultDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/route-engine-dispatch/get-order-add-to-lh": {"get": {"summary": "AI: Get Order information addlinehaul", "deprecated": false, "description": "", "tags": ["TripDispatch"], "parameters": [], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/GetOrderAddToLinehaulInputDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/GetOrderAddToLinehaulOutputDto"}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}}, "components": {"schemas": {"AppointmentSelectDto": {"type": "object", "properties": {"order_keys": {"type": "array", "items": {"type": "integer", "format": "int64"}, "nullable": true}}, "additionalProperties": false}, "BatchFileInputDto": {"type": "object", "properties": {"public_url": {"type": "string", "nullable": true}, "file_extension": {"type": "string", "nullable": true}}, "additionalProperties": false}, "GetLinehaulListInputDto": {"type": "object", "properties": {"linehaul_no": {"type": "array", "items": {"type": "integer", "format": "int64"}, "nullable": true}, "org_terminal": {"type": "array", "items": {"type": "string"}, "nullable": true}, "dst_terminal": {"type": "array", "items": {"type": "string"}, "nullable": true}, "delivery_appt_date": {"type": "string", "nullable": true}, "date_type": {"type": "integer", "format": "int32", "nullable": true}, "from_date": {"type": "string", "nullable": true}, "to_date": {"type": "string", "nullable": true}}, "additionalProperties": false}, "GetLinehaulListOrderOutputDto": {"type": "object", "properties": {"order_no": {"type": "string", "nullable": true}, "tracking_no": {"type": "string", "nullable": true}, "shipper_name": {"type": "string", "nullable": true}, "consignee_name": {"type": "string", "nullable": true}, "org_terminal": {"type": "string", "nullable": true}, "dst_terminal": {"type": "string", "nullable": true}, "bill_to_code": {"type": "string", "nullable": true}, "bill_to_name": {"type": "string", "nullable": true}, "consignee_terminal": {"type": "string", "nullable": true}, "consignee_state": {"type": "string", "nullable": true}, "consignee_zipcode": {"type": "string", "nullable": true}, "pallets": {"type": "integer", "format": "int32"}, "weight": {"type": "string", "nullable": true}, "is_dispatch_trip": {"type": "integer", "format": "int32"}, "consignee_city": {"type": "string", "nullable": true}, "eta": {"type": "string", "format": "date-time"}, "est_rev": {"type": "number", "format": "double"}}, "additionalProperties": false}, "GetLinehaulListOutputDto": {"type": "object", "properties": {"is_dispatch_trip": {"type": "integer", "format": "int32"}, "linehaul_no": {"type": "integer", "format": "int64"}, "org_terminal": {"type": "string", "nullable": true}, "dst_terminal": {"type": "string", "nullable": true}, "delivery_appt_date": {"type": "string", "nullable": true}, "appointment_time_from": {"type": "string", "nullable": true}, "appointment_time_to": {"type": "string", "nullable": true}, "pallets": {"type": "integer", "format": "int32"}, "weight": {"type": "string", "nullable": true}, "create_date": {"type": "string", "nullable": true}, "dst_terminal_street": {"type": "string", "nullable": true}, "dst_terminal_city": {"type": "string", "nullable": true}, "est_depature_date": {"type": "string", "nullable": true}, "est_depature_time_from": {"type": "string", "nullable": true}, "est_depature_time_to": {"type": "string", "nullable": true}, "est_arrival_date": {"type": "string", "nullable": true}, "est_arrival_time_from": {"type": "string", "nullable": true}, "est_arrival_time_to": {"type": "string", "nullable": true}, "order_list": {"type": "array", "items": {"$ref": "#/components/schemas/GetLinehaulListOrderOutputDto"}, "nullable": true}, "trip_no": {"type": "integer", "format": "int64"}, "linehaul_status_text": {"type": "string", "nullable": true}}, "additionalProperties": false}, "GetTripDetailDeliveryOutputDto": {"type": "object", "properties": {"stop_total": {"type": "integer", "format": "int32"}, "packages": {"type": "integer", "format": "int32"}, "weight": {"type": "string", "nullable": true}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/GetTripDetailDeliveryPackageOutputDto"}, "nullable": true}}, "additionalProperties": false}, "GetTripDetailLinehaulOutputDto": {"type": "object", "properties": {"packages": {"type": "integer", "format": "int32"}, "weight": {"type": "string", "nullable": true}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/GetTripDetailLinehaulPackageOutputDto"}, "nullable": true}}, "additionalProperties": false}, "GetTripDetailLinehaulPackageOutputDto": {"type": "object", "properties": {"linehaul_id": {"type": "string", "nullable": true}, "task_status": {"type": "string", "nullable": true}, "org_terminal_code": {"type": "string", "nullable": true}, "dst_terminal_code": {"type": "string", "nullable": true}, "plt": {"type": "string", "readOnly": true, "nullable": true}, "ctn": {"type": "string", "readOnly": true, "nullable": true}, "wt": {"type": "string", "nullable": true}, "delivery_appt_date": {"type": "string", "nullable": true}, "mabd": {"type": "string", "nullable": true}, "pallet": {"type": "string", "readOnly": true, "nullable": true}, "package": {"type": "integer", "format": "int32", "readOnly": true}, "weight": {"type": "string", "nullable": true}, "lat": {"type": "string", "nullable": true}, "lng": {"type": "string", "nullable": true}, "status": {"type": "integer", "format": "int32"}, "stop_sequence": {"type": "integer", "format": "int32"}, "eta_from": {"type": "string", "format": "date-time"}, "eta_to": {"type": "string", "format": "date-time"}, "task_no": {"type": "integer", "format": "int64"}, "order_no": {"type": "string", "nullable": true}, "order_key": {"type": "integer", "format": "int64"}, "total_pallets": {"type": "integer", "format": "int32"}, "total_weights": {"type": "number", "format": "double"}, "total_spaces": {"type": "number", "format": "double"}, "pallet_nos": {"type": "array", "items": {"type": "string"}, "nullable": true}, "package_nos": {"type": "array", "items": {"type": "string"}, "nullable": true}, "tms_revenue": {"type": "number", "format": "double"}}, "additionalProperties": false}, "GetTripDetailPickupOutputDto": {"type": "object", "properties": {"stop_total": {"type": "integer", "format": "int32"}, "packages": {"type": "integer", "format": "int32"}, "weight": {"type": "string", "nullable": true}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/GetTripDetailPickupPackageOutputDto"}, "nullable": true}}, "additionalProperties": false}, "GetOrderAddToLinehaulInputDto": {"type": "object", "properties": {"type": {"type": "integer", "format": "int32", "nullable": true}, "order_no": {"type": "string", "nullable": true}}, "additionalProperties": false}, "GetTripDetailPickupPackageOutputDto": {"type": "object", "properties": {"tracking_no": {"type": "string", "nullable": true}, "task_status": {"type": "string", "nullable": true}, "plt": {"type": "string", "readOnly": true, "nullable": true}, "ctn": {"type": "string", "readOnly": true, "nullable": true}, "wt": {"type": "string", "nullable": true}, "pickup_appt_date": {"type": "string", "nullable": true}, "stop_sequence": {"type": "integer", "format": "int32"}, "pallet": {"type": "string", "readOnly": true, "nullable": true}, "package": {"type": "integer", "format": "int32", "readOnly": true}, "weight": {"type": "string", "nullable": true}, "status": {"type": "integer", "format": "int32"}, "consignee_name": {"type": "string", "nullable": true}, "consignee_city": {"type": "string", "nullable": true}, "consignee_state": {"type": "string", "nullable": true}, "consignee_zipcode": {"type": "string", "nullable": true}, "consignee_address1": {"type": "string", "nullable": true}, "consignee_address2": {"type": "string", "nullable": true}, "consignee_lat": {"type": "string", "nullable": true}, "consignee_lng": {"type": "string", "nullable": true}, "consignee_phone": {"type": "string", "nullable": true}, "consignee_email": {"type": "string", "nullable": true}, "shipper_name": {"type": "string", "nullable": true}, "shipper_city": {"type": "string", "nullable": true}, "shipper_state": {"type": "string", "nullable": true}, "shipper_zipcode": {"type": "string", "nullable": true}, "shipper_address1": {"type": "string", "nullable": true}, "shipper_address2": {"type": "string", "nullable": true}, "shipper_lat": {"type": "string", "nullable": true}, "shipper_lng": {"type": "string", "nullable": true}, "shipper_phone": {"type": "string", "nullable": true}, "shipper_email": {"type": "string", "nullable": true}, "task_no": {"type": "integer", "format": "int64"}, "order_no": {"type": "string", "nullable": true}, "order_key": {"type": "integer", "format": "int64"}, "total_pallets": {"type": "integer", "format": "int32"}, "total_weights": {"type": "number", "format": "double"}, "total_spaces": {"type": "number", "format": "double"}, "pallet_nos": {"type": "array", "items": {"type": "string"}, "nullable": true}, "package_nos": {"type": "array", "items": {"type": "string"}, "nullable": true}, "tms_revenue": {"type": "number", "format": "double"}}, "additionalProperties": false}, "GetOrderAddToLinehaulOutputDto": {"type": "object", "properties": {"order_no": {"type": "string", "nullable": true}, "tracking_no": {"type": "string", "nullable": true}, "shipper_name": {"type": "string", "nullable": true}, "shipper_city": {"type": "string", "nullable": true}, "consignee_name": {"type": "string", "nullable": true}, "consignee_city": {"type": "string", "nullable": true}, "consignee_zip_code": {"type": "string", "nullable": true}}, "additionalProperties": false}, "GetTripDetailsInputDto": {"type": "object", "properties": {"trip_no": {"type": "integer", "format": "int64"}}, "additionalProperties": false}, "GetTripDetailsOutputDto": {"type": "object", "properties": {"trip_no": {"type": "integer", "format": "int64"}, "trip_status": {"type": "integer", "format": "int32"}, "trip_status_name": {"type": "string", "nullable": true}, "trip_type": {"type": "integer", "format": "int32"}, "carrier_code": {"type": "string", "nullable": true}, "carrier_name": {"type": "string", "nullable": true}, "driver_code": {"type": "string", "nullable": true}, "driver_name": {"type": "string", "nullable": true}, "route_id": {"type": "string", "nullable": true}, "route_name": {"type": "string", "nullable": true}, "dispatch_date": {"type": "string", "nullable": true}, "pallets_current": {"type": "integer", "format": "int32"}, "pallets_total": {"type": "integer", "format": "int32"}, "weight_current": {"type": "string", "nullable": true}, "weight_total": {"type": "string", "nullable": true}, "complete_pallets": {"type": "integer", "format": "int32"}, "complete_weight": {"type": "string", "nullable": true}, "stop_total": {"type": "integer", "format": "int32"}, "appt_total": {"type": "integer", "format": "int32"}, "trailer_code": {"type": "string", "nullable": true}, "tractor_code": {"type": "string", "nullable": true}, "org_terminal": {"type": "string", "nullable": true}, "dst_termina": {"type": "string", "nullable": true}, "note": {"type": "string", "nullable": true}, "pickup_list": {"$ref": "#/components/schemas/GetTripDetailPickupOutputDto"}, "delivery_list": {"$ref": "#/components/schemas/GetTripDetailDeliveryOutputDto"}, "service_list": {"$ref": "#/components/schemas/GetTripDetailServiceOutputDto"}, "linehaul_list": {"$ref": "#/components/schemas/GetTripDetailLinehaulOutputDto"}, "pick_up_and_delivery_list": {"$ref": "#/components/schemas/GetTripDetailPickUpAndDeliveryOutputDto"}}, "additionalProperties": false}, "GetTripsOutputDtoPagedResultDto": {"type": "object", "properties": {"total_count": {"type": "integer", "format": "int64"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/GetTripsOutputDto"}, "nullable": true}}, "additionalProperties": false}, "GetTripDetailDeliveryPackageOutputDto": {"type": "object", "properties": {"tracking_no": {"type": "string", "nullable": true}, "task_status": {"type": "string", "nullable": true}, "plt": {"type": "string", "readOnly": true, "nullable": true}, "ctn": {"type": "string", "readOnly": true, "nullable": true}, "wt": {"type": "string", "nullable": true}, "pickup_appt_date": {"type": "string", "nullable": true}, "stop_sequence": {"type": "integer", "format": "int32"}, "pallet": {"type": "string", "readOnly": true, "nullable": true}, "package": {"type": "integer", "format": "int32", "readOnly": true}, "weight": {"type": "string", "nullable": true}, "status": {"type": "integer", "format": "int32"}, "consignee_name": {"type": "string", "nullable": true}, "consignee_city": {"type": "string", "nullable": true}, "consignee_state": {"type": "string", "nullable": true}, "consignee_zipcode": {"type": "string", "nullable": true}, "consignee_address1": {"type": "string", "nullable": true}, "consignee_address2": {"type": "string", "nullable": true}, "consignee_lat": {"type": "string", "nullable": true}, "consignee_lng": {"type": "string", "nullable": true}, "consignee_phone": {"type": "string", "nullable": true}, "consignee_email": {"type": "string", "nullable": true}, "shipper_name": {"type": "string", "nullable": true}, "shipper_city": {"type": "string", "nullable": true}, "shipper_state": {"type": "string", "nullable": true}, "shipper_zipcode": {"type": "string", "nullable": true}, "shipper_address1": {"type": "string", "nullable": true}, "shipper_address2": {"type": "string", "nullable": true}, "shipper_lat": {"type": "string", "nullable": true}, "shipper_lng": {"type": "string", "nullable": true}, "shipper_phone": {"type": "string", "nullable": true}, "shipper_email": {"type": "string", "nullable": true}, "task_no": {"type": "integer", "format": "int64"}, "order_no": {"type": "string", "nullable": true}, "order_key": {"type": "integer", "format": "int64"}, "total_pallets": {"type": "integer", "format": "int32"}, "total_weights": {"type": "number", "format": "double"}, "total_spaces": {"type": "number", "format": "double"}, "pallet_nos": {"type": "array", "items": {"type": "string"}, "nullable": true}, "package_nos": {"type": "array", "items": {"type": "string"}, "nullable": true}, "tms_revenue": {"type": "number", "format": "double"}}, "additionalProperties": false}, "GetTripDetailServiceOutputDto": {"type": "object", "properties": {"stop_total": {"type": "integer", "format": "int32"}, "packages": {"type": "integer", "format": "int32"}, "weight": {"type": "string", "nullable": true}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/GetTripDetailServicePackageOutputDto"}, "nullable": true}}, "additionalProperties": false}, "GetTripDetailServicePackageOutputDto": {"type": "object", "properties": {"tracking_no": {"type": "string", "nullable": true}, "task_no": {"type": "string", "nullable": true}, "task_status": {"type": "string", "nullable": true}, "plt": {"type": "string", "nullable": true}, "ctn": {"type": "string", "nullable": true}, "wt": {"type": "string", "nullable": true}, "pickup_appt_date": {"type": "string", "nullable": true}, "stop_sequence": {"type": "integer", "format": "int32"}, "pallet": {"type": "string", "nullable": true}, "package": {"type": "integer", "format": "int32"}, "weight": {"type": "string", "nullable": true}, "order_no": {"type": "string", "nullable": true}, "status": {"type": "integer", "format": "int32"}, "consignee_name": {"type": "string", "nullable": true}, "consignee_city": {"type": "string", "nullable": true}, "consignee_state": {"type": "string", "nullable": true}, "consignee_zipcode": {"type": "string", "nullable": true}, "consignee_address1": {"type": "string", "nullable": true}, "consignee_address2": {"type": "string", "nullable": true}, "consignee_lat": {"type": "string", "nullable": true}, "consignee_lng": {"type": "string", "nullable": true}, "consignee_phone": {"type": "string", "nullable": true}, "consignee_email": {"type": "string", "nullable": true}, "shipper_name": {"type": "string", "nullable": true}, "shipper_city": {"type": "string", "nullable": true}, "shipper_state": {"type": "string", "nullable": true}, "shipper_zipcode": {"type": "string", "nullable": true}, "shipper_address1": {"type": "string", "nullable": true}, "shipper_address2": {"type": "string", "nullable": true}, "shipper_lat": {"type": "string", "nullable": true}, "shipper_lng": {"type": "string", "nullable": true}, "shipper_phone": {"type": "string", "nullable": true}, "shipper_email": {"type": "string", "nullable": true}}, "additionalProperties": false}, "FileInputDto": {"type": "object", "properties": {"trip_no": {"type": "integer", "format": "int64"}, "file_name": {"type": "string", "nullable": true}, "file_id": {"type": "integer", "format": "int32", "nullable": true}, "rotation_angle": {"type": "string", "nullable": true}, "tracking_no": {"type": "string", "nullable": true}, "file_type": {"type": "string", "nullable": true}, "file_extension": {"type": "string", "nullable": true}, "image_type": {"type": "string", "nullable": true}, "public_url": {"type": "string", "nullable": true}, "public_url_array": {"type": "array", "items": {"$ref": "#/components/schemas/BatchFileInputDto"}, "nullable": true}}, "additionalProperties": false}, "GetTripDetailPickUpAndDeliveryOutputDto": {"type": "object", "properties": {"stop_total": {"type": "integer", "format": "int32"}, "packages": {"type": "integer", "format": "int32"}, "weight": {"type": "string", "nullable": true}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/GetTripDetailPickUpAndDeliveryPackageOutputDto"}, "nullable": true}}, "additionalProperties": false}, "GetTripDetailPickUpAndDeliveryPackageOutputDto": {"type": "object", "properties": {"stops": {"type": "array", "items": {"$ref": "#/components/schemas/GetTripDetailPickUpAndDeliveryServiceStopsOutputDto"}, "nullable": true}, "task_no": {"type": "string", "nullable": true}, "task_status": {"type": "string", "nullable": true}, "plt": {"type": "string", "nullable": true}, "ctn": {"type": "string", "nullable": true}, "wt": {"type": "string", "nullable": true}, "pallet": {"type": "string", "nullable": true}, "package": {"type": "integer", "format": "int32"}, "weight": {"type": "string", "nullable": true}}, "additionalProperties": false}, "FileOutputDto": {"type": "object", "properties": {"trip_no": {"type": "integer", "format": "int64"}, "files": {"type": "array", "items": {"$ref": "#/components/schemas/FileOutputDtoInfo"}, "nullable": true}}, "additionalProperties": false}, "FileOutputDtoInfo": {"type": "object", "properties": {"file_id": {"type": "integer", "format": "int32"}, "tracking_no": {"type": "string", "nullable": true}, "file_type": {"type": "string", "nullable": true}, "file_category": {"type": "string", "nullable": true}, "file_public_url": {"type": "string", "nullable": true}, "rotation_angle": {"type": "string", "nullable": true}, "upload_time": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "GetTripDetailPickUpAndDeliveryServiceStopsOutputDto": {"type": "object", "properties": {"stop_no": {"type": "integer", "format": "int64"}, "stop_sequence": {"type": "integer", "format": "int32"}, "stop_status": {"type": "string", "nullable": true}, "tracking_no": {"type": "string", "nullable": true}, "order_no": {"type": "string", "nullable": true}, "pickup_or_delivery_appt_date": {"type": "string", "nullable": true}, "shipper_or_consignee_name": {"type": "string", "nullable": true}, "shipper_or_consignee_city": {"type": "string", "nullable": true}, "shipper_or_consignee_state": {"type": "string", "nullable": true}, "shipper_or_consignee_zipcode": {"type": "string", "nullable": true}, "shipper_or_consignee_address1": {"type": "string", "nullable": true}, "shipper_or_consignee_address2": {"type": "string", "nullable": true}, "shipper_or_consignee_lat": {"type": "string", "nullable": true}, "shipper_or_consignee_lng": {"type": "string", "nullable": true}, "shipper_or_consignee_phone": {"type": "string", "nullable": true}, "shipper_or_consignee_email": {"type": "string", "nullable": true}}, "additionalProperties": false}, "GetTripsInputDto": {"type": "object", "properties": {"trip_no": {"type": "array", "items": {"type": "integer", "format": "int64"}, "nullable": true}, "trip_type": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}, "org_terminal": {"type": "array", "items": {"type": "string"}, "nullable": true}, "dst_terminal": {"type": "array", "items": {"type": "string"}, "nullable": true}, "trip_status": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}, "route_id": {"type": "array", "items": {"type": "string"}, "nullable": true}, "route_name": {"type": "string", "nullable": true}, "carrier_code": {"type": "string", "nullable": true}, "driver_code": {"type": "string", "nullable": true}, "date_from": {"type": "string", "nullable": true}, "date_to": {"type": "string", "nullable": true}, "tractor_code": {"type": "string", "nullable": true}, "trailer_code": {"type": "string", "nullable": true}, "page": {"type": "integer", "format": "int32"}, "per_page": {"type": "integer", "format": "int32"}, "user_terminal": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "GetTripsOutputDto": {"type": "object", "properties": {"trip_no": {"type": "integer", "format": "int64"}, "tms_trip_id": {"type": "integer", "format": "int64"}, "trip_status": {"type": "integer", "format": "int32"}, "org_terminal": {"type": "string", "nullable": true}, "dst_terminal": {"type": "string", "nullable": true}, "carrier_code": {"type": "string", "nullable": true}, "carrier_name": {"type": "string", "nullable": true}, "driver_code": {"type": "string", "nullable": true}, "driver_name": {"type": "string", "nullable": true}, "trip_type": {"type": "integer", "format": "int32"}, "route_id": {"type": "string", "nullable": true}, "route_name": {"type": "string", "nullable": true}, "stop_total": {"type": "integer", "format": "int32"}, "appt_total": {"type": "integer", "format": "int32"}, "dispatch_date": {"type": "string", "nullable": true}, "pallets_current": {"type": "integer", "format": "int32"}, "pallets_total": {"type": "integer", "format": "int32"}, "weight_current": {"type": "string", "nullable": true}, "weight_total": {"type": "string", "nullable": true}, "complete_pallets": {"type": "integer", "format": "int32"}, "complete_weight": {"type": "string", "nullable": true}, "is_auto": {"type": "boolean"}, "note": {"type": "string", "nullable": true}, "dispatcher_note": {"type": "string", "nullable": true}, "complete_tasks": {"type": "integer", "format": "int32"}, "total_tasks": {"type": "integer", "format": "int32"}, "tractor": {"type": "string", "nullable": true}, "trailer": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Int32FmsSelectListItem": {"type": "object", "properties": {"text": {"type": "string", "nullable": true}, "value": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "Int32FmsSelectListItemListResultDto": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/Int32FmsSelectListItem"}, "nullable": true}}, "additionalProperties": false}, "TaskStopInfoDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "check_in_time": {"type": "string", "format": "date-time", "nullable": true}, "check_out_time": {"type": "string", "format": "date-time", "nullable": true}, "driver_name": {"type": "string", "nullable": true}, "trip_no": {"type": "integer", "format": "int64"}, "task_no": {"type": "integer", "format": "int64"}, "exception_data": {"type": "string", "nullable": true}}, "additionalProperties": false}, "TripTaskRpcDto": {"type": "object", "properties": {"carrier_name": {"type": "string", "nullable": true}, "driver_name": {"type": "string", "nullable": true}, "dst_terminal": {"type": "string", "nullable": true}, "eta_from": {"type": "string", "format": "date-time"}, "etd_from": {"type": "string", "format": "date-time"}, "lh_dst_terminal": {"type": "string", "nullable": true}, "lh_eta_from": {"type": "string", "format": "date-time"}, "lh_etd_from": {"type": "string", "format": "date-time"}, "lh_org_terminal": {"type": "string", "nullable": true}, "lh_task_status": {"type": "integer", "format": "int32"}, "order_key": {"type": "integer", "format": "int64"}, "org_terminal": {"type": "string", "nullable": true}, "shipment_order_no": {"type": "string", "nullable": true}, "task_no": {"type": "integer", "format": "int64"}, "task_type": {"type": "integer", "format": "int32"}, "trip_date": {"type": "string", "format": "date-time"}, "trip_no": {"type": "integer", "format": "int64"}, "trip_type": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "WeatherForecast": {"type": "object", "properties": {"date": {"type": "string", "format": "date"}, "temperature_c": {"type": "integer", "format": "int32"}, "temperature_f": {"type": "integer", "format": "int32", "readOnly": true}, "summary": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StringFmsSelectListItem": {"type": "object", "properties": {"text": {"type": "string", "nullable": true}, "value": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StringFmsSelectListItemListResultDto": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/StringFmsSelectListItem"}, "nullable": true}}, "additionalProperties": false}}}}