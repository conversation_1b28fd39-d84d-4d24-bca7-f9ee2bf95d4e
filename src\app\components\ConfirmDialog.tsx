'use client';

import React from 'react';
import '@/styles/item-design-system.css';

interface ConfirmDialogProps {
  isOpen: boolean;
  title: string;
  message: string;
  onConfirm: () => void;
  onCancel: () => void;
  /** 是否在容器内显示（相对定位），默认为 false（全屏显示） */
  containerScoped?: boolean;
}

export default function ConfirmDialog({
  isOpen,
  title,
  message,
  onConfirm,
  onCancel,
  containerScoped = false
}: ConfirmDialogProps) {
  if (!isOpen) return null;

  // 根据 containerScoped 决定定位方式和样式
  const overlayClass = containerScoped 
    ? "absolute inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center item-animate-in"
    : "fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center item-animate-in";
  
  const dialogClass = containerScoped
    ? "item-card bg-item-bg-card border border-item-purple/30 rounded-xl p-6 max-w-sm w-full mx-4 shadow-2xl shadow-item-purple/20"
    : "item-card bg-item-bg-card border border-item-purple/30 rounded-xl p-6 max-w-md w-full mx-4 shadow-2xl shadow-item-purple/20";

  const zIndex = containerScoped ? 50 : 9999;

  return (
    <div 
      className={overlayClass}
      style={{ zIndex }}
    >
      <div className={dialogClass}>
        <h3 className="text-lg font-medium text-item-purple mb-2">
          {title}
        </h3>
        <p className="text-item-gray-300 mb-6 leading-relaxed text-sm">
          {message}
        </p>
        <div className="flex justify-end space-x-3">
          <button
            onClick={onCancel}
            className="px-4 py-2 text-sm text-item-gray-400 hover:text-white hover:bg-item-bg-hover rounded-lg transition-all duration-200"
          >
            Cancel
          </button>
          <button
            onClick={onConfirm}
            className="px-4 py-2 text-sm bg-red-600 hover:bg-red-700 text-white rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl"
          >
            Delete
          </button>
        </div>
      </div>
    </div>
  );
} 