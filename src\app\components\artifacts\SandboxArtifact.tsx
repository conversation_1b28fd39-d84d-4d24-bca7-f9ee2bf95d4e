'use client';

import React from 'react';

interface SandboxArtifactProps {
  initialUrl: string;
  sessionId: string;
}

export function SandboxArtifact({ initialUrl, sessionId }: SandboxArtifactProps) {
  return (
    <div className="w-full h-full flex items-center justify-center bg-gray-900/50 rounded-lg">
      <div className="text-center">
        <div className="text-gray-400 mb-2">Sandbox Session: {sessionId}</div>
        <div className="text-sm text-gray-500">Sandbox functionality coming soon...</div>
      </div>
    </div>
  );
}