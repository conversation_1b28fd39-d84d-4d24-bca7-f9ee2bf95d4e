import { useState, useEffect, useRef, useCallback } from 'react';
import { RealtimeClient } from '@/lib/RealtimeClient';
import { RealtimeAgent } from '@openai/agents/realtime';
import { hybridAgentTool } from '@/tools/hybridAgentTool';
import { nanoid } from 'nanoid';
import { api } from '@/utils/apiClient';
import { VoiceErrorHandler, type VoiceError } from '@/utils/voiceErrorHandler';
import { log } from '@/utils/logger';

export type VoiceMode = 'live' | 'ptt';

// 简化的事件队列
class SimpleVoiceEventQueue {
  private queue: Array<{type: 'delta' | 'complete', data?: string}> = [];
  private waitingForUser = false;
  private timeoutId: NodeJS.Timeout | null = null;

  constructor(
    private onDelta: (delta: string) => void,
    private onComplete: () => void
  ) {}

  onUserSpeechStopped() {
    this.waitingForUser = true;
    this.timeoutId = setTimeout(() => {
      this.waitingForUser = false;
      this.processQueue();
    }, 3000); // 减少到3秒
  }

  onUserTranscript() {
    if (this.timeoutId) {
      clearTimeout(this.timeoutId);
      this.timeoutId = null;
    }
    this.waitingForUser = false;
    this.processQueue();
  }

  enqueueAssistant(type: 'delta' | 'complete', data?: string) {
    if (this.waitingForUser) {
      this.queue.push({ type, data });
    } else {
      this.processEvent({ type, data });
    }
  }

  private processQueue() {
    const events = [...this.queue];
    this.queue = [];
    events.forEach(event => this.processEvent(event));
  }

  private processEvent(event: {type: 'delta' | 'complete', data?: string}) {
    if (event.type === 'delta' && event.data) {
      this.onDelta(event.data);
    } else if (event.type === 'complete') {
      this.onComplete();
    }
  }

  cleanup() {
    if (this.timeoutId) {
      clearTimeout(this.timeoutId);
      this.timeoutId = null;
    }
    this.queue = [];
    this.waitingForUser = false;
  }
}

interface UseRealtimeVoiceOptions {
  onUserTranscript?: (transcript: string) => void;
  onAssistantTranscriptDelta?: (delta: string) => void;
  onAssistantTranscriptComplete?: () => void;
  onError?: (error: VoiceError) => void; // 改为详细错误类型
  onHybridAgentStream?: (data: any) => void;
  onFrequencyData?: (frequencies: number[]) => void; // 频谱数据回调
}

export function useRealtimeVoice({
  onUserTranscript,
  onAssistantTranscriptDelta,
  onAssistantTranscriptComplete,
  onError,
  onHybridAgentStream,
  onFrequencyData,
}: UseRealtimeVoiceOptions = {}) {
  const [isConnected, setIsConnected] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [isPTTMode, setIsPTTMode] = useState(true); // true = PTT 模式, false = Live 模式
  const [isPTTActive, setIsPTTActive] = useState(false); // PTT 按钮是否被按下
  const [isRecording, setIsRecording] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const [isAssistantResponding, setIsAssistantResponding] = useState(false);
  const [error, setError] = useState<VoiceError | null>(null);
  const [userTranscripts, setUserTranscripts] = useState<string[]>([]);
  const [assistantTranscripts, setAssistantTranscripts] = useState<string[]>([]);
  
  // 简化的事件队列
  const eventQueueRef = useRef<SimpleVoiceEventQueue | null>(null);
  
  // 音频分析相关
  const audioContextRef = useRef<AudioContext | null>(null);
  const analyserRef = useRef<AnalyserNode | null>(null);
  const dataArrayRef = useRef<Uint8Array | null>(null);
  const animationFrameRef = useRef<number | null>(null);

  const clientRef = useRef<RealtimeClient | null>(null);
  const audioElementRef = useRef<HTMLAudioElement | null>(null);
  const agentRef = useRef<RealtimeAgent | null>(null);
  const onHybridAgentStreamRef = useRef(onHybridAgentStream);
  const errorTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 更新回调 ref
  useEffect(() => {
    onHybridAgentStreamRef.current = onHybridAgentStream;
  }, [onHybridAgentStream]);

  // 错误处理
  const handleError = useCallback((originalError: any) => {
    log.error('Error occurred:', originalError, 'useRealtimeVoice');
    
    const voiceError = VoiceErrorHandler.handleError(originalError, () => {
      // 重试逻辑
      if (isConnected) {
        disconnect();
      }
      setTimeout(() => connect(), 1000);
    });
    
    setError(voiceError);
    onError?.(voiceError);
    
    // 清除之前的定时器
    if (errorTimeoutRef.current) {
      clearTimeout(errorTimeoutRef.current);
    }
    
    // 自动清除错误（5秒后）
    errorTimeoutRef.current = setTimeout(() => {
      setError(null);
      errorTimeoutRef.current = null;
    }, 5000);
  }, [isConnected, onError]);

  // 创建事件队列
  const createEventQueue = useCallback(() => {
    if (eventQueueRef.current) {
      eventQueueRef.current.cleanup();
    }

    eventQueueRef.current = new SimpleVoiceEventQueue(
      (delta) => onAssistantTranscriptDelta?.(delta),
      () => onAssistantTranscriptComplete?.()
    );

    return eventQueueRef.current;
  }, [onAssistantTranscriptDelta, onAssistantTranscriptComplete]);

  // 设置音频分析
  const setupAudioAnalysis = useCallback(async () => {
    if (!onFrequencyData) return null;

    try {
      // 获取用户媒体流
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      
      // 创建音频上下文
      const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
      audioContextRef.current = audioContext;
      
      // 创建分析器节点
      const analyser = audioContext.createAnalyser();
      analyser.fftSize = 256; // 128个频率bins
      analyser.smoothingTimeConstant = 0.7; // 稍微降低平滑度，让变化更明显
      analyserRef.current = analyser;
      
      // 创建数据数组
      const bufferLength = analyser.frequencyBinCount;
      const dataArray = new Uint8Array(bufferLength);
      dataArrayRef.current = dataArray;
      
      // 连接音频流到分析器
      const source = audioContext.createMediaStreamSource(stream);
      source.connect(analyser);
      
      // 开始分析音频级别和频谱
      const analyzeAudio = () => {
        if (!analyserRef.current || !dataArrayRef.current) return;
        
        analyserRef.current.getByteFrequencyData(dataArrayRef.current);
        

        
        // 提供频谱数据（用于波形可视化）
        if (onFrequencyData) {
          // 将频谱数据分成6个频段，模拟常见的音频可视化
          const frequencyBands = 6;
          const bandsData: number[] = [];
          const bandSize = Math.floor(dataArrayRef.current.length / frequencyBands);
          
          for (let i = 0; i < frequencyBands; i++) {
            let bandSum = 0;
            const startIndex = i * bandSize;
            const endIndex = Math.min(startIndex + bandSize, dataArrayRef.current.length);
            
            for (let j = startIndex; j < endIndex; j++) {
              bandSum += dataArrayRef.current[j];
            }
            
            const bandAverage = bandSum / (endIndex - startIndex);
            bandsData.push(Math.min(bandAverage / 255, 1)); // 归一化到 0-1
          }
          
          onFrequencyData(bandsData);
        }
        
        animationFrameRef.current = requestAnimationFrame(analyzeAudio);
      };
      
      analyzeAudio();
      
      return stream;
    } catch (error) {
      console.warn('[useRealtimeVoice] Failed to setup audio analysis:', error);
      return null;
    }
  }, [onFrequencyData]);

  // 清理音频分析
  const cleanupAudioAnalysis = useCallback(() => {
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
      animationFrameRef.current = null;
    }
    
    if (audioContextRef.current) {
      audioContextRef.current.close();
      audioContextRef.current = null;
    }
    
    analyserRef.current = null;
    dataArrayRef.current = null;
  }, []);

  // 监听混合代理工具调用事件
  useEffect(() => {
    const handleHybridAgentToolCall = (event: Event) => {
      const customEvent = event as CustomEvent;
      const { toolCallId, taskDescription } = customEvent.detail;
      console.log('[useRealtimeVoice] Hybrid agent tool call started:', { toolCallId, taskDescription });
      
      // 设置监听该特定工具调用的流式事件
      const handleStreamEvent = (streamEvent: Event) => {
        const customStreamEvent = streamEvent as CustomEvent;
        const { type, data, error } = customStreamEvent.detail;
        
        console.log('[useRealtimeVoice] Received stream event:', { toolCallId, type, data: data?.substring(0, 100), error });
        
        // 使用 ref 来获取最新的回调，避免依赖导致的重复监听器
        const currentCallback = onHybridAgentStreamRef.current;
        if (currentCallback) {
          console.log('[useRealtimeVoice] Calling onHybridAgentStream callback');
          currentCallback({
            toolCallId,
            type,
            data,
            error
          });
        } else {
          console.log('[useRealtimeVoice] No onHybridAgentStream callback provided');
        }
        
        // 如果是完成或错误事件，移除监听器
        if (type === 'completed' || type === 'error') {
          console.log('[useRealtimeVoice] Removing stream event listener for:', toolCallId);
          window.removeEventListener(`hybrid-agent-${toolCallId}`, handleStreamEvent);
        }
      };
      
      console.log('[useRealtimeVoice] Adding stream event listener for:', `hybrid-agent-${toolCallId}`);
      // 监听特定工具调用的流式事件
      window.addEventListener(`hybrid-agent-${toolCallId}`, handleStreamEvent);
    };

    console.log('[useRealtimeVoice] Adding hybrid-agent-tool-call event listener');
    // 监听混合代理工具调用开始事件
    window.addEventListener('hybrid-agent-tool-call', handleHybridAgentToolCall);

    // 清理函数
    return () => {
      console.log('[useRealtimeVoice] Removing hybrid-agent-tool-call event listener');
      window.removeEventListener('hybrid-agent-tool-call', handleHybridAgentToolCall);
    };
  }, []); // 移除依赖，使用 ref 来访问最新的回调

  // 创建 RealtimeAgent
  const createAgent = useCallback(() => {
    if (agentRef.current) return agentRef.current;

    const agent = new RealtimeAgent({
      name: 'hybridAgent',
      voice: 'sage',
      instructions: `You are a helpful AI assistant with access to various tools and capabilities.

# Language Support
- This system only supports English, Chinese (Mandarin), and Spanish languages
- Only respond in these three languages
- Do not attempt to communicate in other languages

# Instructions
- You can help with weather information, Warehouse Management System (WMS), memory management, JIRA tasks, IoT device control, and general questions
- Always be helpful, concise, and accurate in your responses
- Use the available tools when needed to provide the best assistance
- For voice conversations, keep responses brief and conversational
- If you need to use tools, explain what you're doing briefly
- Respond in the same language the user is speaking (English, Chinese, or Spanish only)

# Available Tools
- hybridAgentTool: Access to weather, WMS System, memory, JIRA, IoT, and other capabilities 

# Response Style
- Keep responses concise for voice interaction
- Use natural, conversational language
- Avoid long lists or complex formatting
- Prioritize the most important information
- Match the user's language (English, Chinese, or Spanish only)`,
      tools: [hybridAgentTool],
    });

    agentRef.current = agent;
    return agent;
  }, []);

  // 创建音频元素
  const createAudioElement = useCallback(() => {
    if (audioElementRef.current) return audioElementRef.current;

    const audio = new Audio();
    audio.autoplay = true;
    audioElementRef.current = audio;
    return audio;
  }, []);

  // 获取临时密钥
  const getEphemeralKey = useCallback(async (): Promise<string> => {
    try {
      const { data, error } = await api.post<{
        client_secret: {
          value: string;
        };
      }>('/api/voice/session', {});

      if (error || !data) {
        throw new Error(`Failed to get ephemeral key: ${error || 'No data received'}`);
      }

      if (!data.client_secret?.value) {
        throw new Error('No ephemeral key in response');
      }

      return data.client_secret.value;
    } catch (error) {
      console.error('[useRealtimeVoice] Failed to get ephemeral key:', error);
      throw error;
    }
  }, []);

  // 更新会话配置
  const updateSession = useCallback(() => {
    if (!clientRef.current || !isConnected) return;

    log.debug('Updating session, isPTTMode:', isPTTMode, 'useRealtimeVoice');
    
    // PTT 模式：禁用服务器 VAD，需要手动控制
    // Live 模式：启用服务器 VAD，自动检测语音
    const turnDetection = isPTTMode ? null : {
      type: 'server_vad',
      threshold: 0.9,
      prefix_padding_ms: 300,
      silence_duration_ms: 500,
      create_response: true,
    };

    try {
      clientRef.current.sendEvent({
        type: 'session.update',
        session: {
          turn_detection: turnDetection,
          input_audio_transcription: {
           model: 'whisper-1',
           },
          modalities: ['text', 'audio'],
        },
      });
      log.debug('Session updated with turn_detection:', turnDetection, 'useRealtimeVoice');
    } catch (err) {
      log.warn('Failed to update session:', err, 'useRealtimeVoice');
    }
  }, [isPTTMode, isConnected]);

  // 连接语音服务
  const connect = useCallback(async () => {
    if (isConnecting || isConnected) return;

    try {
      setIsConnecting(true);
      setError(null);
      log.info('Starting connection...', undefined, 'useRealtimeVoice');

      // 检查浏览器支持
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        throw new Error('Browser not supported');
      }

      const agent = createAgent();
      const audioElement = createAudioElement();
      const eventQueue = createEventQueue();

      const client = new RealtimeClient({
        getEphemeralKey,
        audioElement,
        agent,
        extraContext: {
          user_id: 'voice_user',
          session_type: 'voice_chat'
        }
      });

      // 设置事件监听器
      client.on('connection_change', (status) => {
        log.info('Connection status:', status, 'useRealtimeVoice');
        setIsConnected(status === 'connected');
        setIsConnecting(status === 'connecting');
        
        if (status === 'disconnected') {
          setIsPTTActive(false);
          setIsRecording(false);
          setIsAssistantResponding(false);
          cleanupAudioAnalysis();
        } else if (status === 'connected') {
          // 连接成功后立即更新会话配置
          setTimeout(() => updateSession(), 100);
          // 启动音频分析
          setupAudioAnalysis();
          // 清除之前的错误，因为连接成功
          setError(null);
        }
      });

      // 简化的事件处理 - 使用事件队列
      client.on('user_transcript', (transcript) => {
        log.debug('User transcript received:', transcript, 'useRealtimeVoice');
        setUserTranscripts(prev => [...prev, transcript]);
        onUserTranscript?.(transcript);
        eventQueue.onUserTranscript();
      });

      client.on('assistant_transcript_delta', (transcriptDelta) => {
        log.debug('Assistant transcript delta:', transcriptDelta, 'useRealtimeVoice');
        eventQueue.enqueueAssistant('delta', transcriptDelta);
      });

      client.on('assistant_transcript_done', (finalTranscript) => {
        log.debug('Assistant transcript completed:', finalTranscript, 'useRealtimeVoice');
        eventQueue.enqueueAssistant('complete');
      });

      client.on('assistant_response_created', () => {
        log.debug('Assistant started responding', undefined, 'useRealtimeVoice');
        setIsAssistantResponding(true);
      });

      client.on('assistant_response_done', () => {
        log.debug('Assistant response fully completed', undefined, 'useRealtimeVoice');
        setIsAssistantResponding(false);
      });

      client.on('user_speech_started', () => {
        log.debug('User started speaking', undefined, 'useRealtimeVoice');
        setIsRecording(true);
        // 清除之前的错误，因为用户重新开始说话
        setError(null);
      });

      client.on('user_speech_stopped', () => {
        log.debug('User stopped speaking', undefined, 'useRealtimeVoice');
        setIsRecording(false);
        eventQueue.onUserSpeechStopped();
      });

      client.on('audio_interrupted', () => {
        log.debug('Audio interrupted', undefined, 'useRealtimeVoice');
        setIsRecording(false);
      });

      client.on('message', (event) => {
        // 处理其他事件
        if (event.type === 'error') {
          log.error('Realtime error:', event, 'useRealtimeVoice');
          handleError(event.error || new Error('Unknown realtime error'));
        }
      });

      clientRef.current = client;
      await client.connect();

    } catch (error) {
      console.error('[useRealtimeVoice] Connection failed:', error);
      handleError(error);
      setIsConnecting(false);
    }
  }, [isConnecting, isConnected, getEphemeralKey, createAgent, createAudioElement, createEventQueue, updateSession, handleError]);

  // 断开连接
  const disconnect = useCallback(() => {
    if (clientRef.current) {
      clientRef.current.disconnect();
      clientRef.current = null;
    }
    if (eventQueueRef.current) {
      eventQueueRef.current.cleanup();
      eventQueueRef.current = null;
    }
    cleanupAudioAnalysis();
    setIsConnected(false);
    setIsConnecting(false);
    setIsPTTActive(false);
    setIsRecording(false);
    setIsAssistantResponding(false);
    setError(null);
  }, [cleanupAudioAnalysis]);

  // 切换连接状态
  const toggleConnection = useCallback(() => {
    if (isConnected) {
      disconnect();
    } else {
      connect();
    }
  }, [isConnected, connect, disconnect]);

  // 切换模式
  const toggleMode = useCallback(() => {
    setIsPTTMode(prev => !prev);
  }, []);

  // PTT 开始 - 添加触觉反馈
  const startPTT = useCallback(() => {
    if (!clientRef.current || !isConnected || !isPTTMode || isPTTActive) return;
    
    log.debug('Starting PTT', undefined, 'useRealtimeVoice');
    setIsPTTActive(true);
    
    // 添加触觉反馈
    if (navigator.vibrate) {
      navigator.vibrate(50);
    }
    
    clientRef.current.pushToTalkStart();
  }, [isConnected, isPTTMode, isPTTActive]);

  // PTT 结束 - 添加触觉反馈
  const stopPTT = useCallback(() => {
    if (!clientRef.current || !isConnected || !isPTTMode || !isPTTActive) return;
    
    log.debug('Stopping PTT', undefined, 'useRealtimeVoice');
    setIsPTTActive(false);
    
    // 添加触觉反馈
    if (navigator.vibrate) {
      navigator.vibrate(100);
    }
    
    clientRef.current.pushToTalkStop();
  }, [isConnected, isPTTMode, isPTTActive]);

  // 切换静音
  const toggleMute = useCallback(() => {
    if (!clientRef.current || !isConnected) return;
    
    const newMutedState = !isMuted;
    setIsMuted(newMutedState);
    clientRef.current.mute(newMutedState);
    log.debug('Mute toggled:', newMutedState, 'useRealtimeVoice');
  }, [isMuted, isConnected]);

  // 当模式改变时更新会话
  useEffect(() => {
    if (isConnected) {
      log.debug('Mode changed, updating session. isPTTMode:', isPTTMode, 'useRealtimeVoice');
      updateSession();
    }
  }, [isPTTMode, isConnected, updateSession]);

  // 移除了基于启发式检测的逻辑，现在使用 assistant_response_done 事件

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      if (clientRef.current) {
        clientRef.current.disconnect();
      }
    };
  }, []);

  // 监听连接状态变化
  useEffect(() => {
    log.info('Connection status:', isConnected ? 'connected' : 'disconnected', 'useRealtimeVoice');
  }, [isConnected]);

  // 清理定时器
  useEffect(() => {
    return () => {
      if (errorTimeoutRef.current) {
        clearTimeout(errorTimeoutRef.current);
        errorTimeoutRef.current = null;
      }
    };
  }, []);

  // 清除错误
  const clearError = useCallback(() => {
    if (errorTimeoutRef.current) {
      clearTimeout(errorTimeoutRef.current);
      errorTimeoutRef.current = null;
    }
    setError(null);
  }, []);

  // 获取音频元素用于录音
  const getAudioElement = useCallback(() => {
    return clientRef.current?.getAudioElement();
  }, []);

  return {
    // 状态
    isConnected,
    isConnecting,
    mode: isPTTMode ? 'ptt' : 'live' as VoiceMode,
    isPTTActive,
    isRecording,
    isMuted,
    isAssistantResponding,
    error,
    userTranscripts,
    assistantTranscripts,
    
    // 操作
    connect,
    disconnect,
    toggleConnection,
    toggleMode,
    startPTT,
    stopPTT,
    toggleMute,
    clearError,
    
    // 工具函数
    getLatestUserTranscript: () => userTranscripts[userTranscripts.length - 1] || '',
    getLatestAssistantTranscript: () => assistantTranscripts[assistantTranscripts.length - 1] || '',
    clearTranscripts: () => {
      setUserTranscripts([]);
      setAssistantTranscripts([]);
    },
    getAudioElement, // 暴露音频元素获取方法
  };
} 