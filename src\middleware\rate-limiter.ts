/**
 * @file 服务端限流中间件
 * @description 用于服务端实现请求限流的中间件
 * @server-side
 */

import { NextRequest, NextResponse } from 'next/server';
import { getSiteConfig } from '../utils/site-config';

// 限流配置接口
interface RateLimitConfig {
  maxRequests: number;
  windowSeconds: number;
}

// 获取限流配置
function getRateLimitConfig(siteId: string): RateLimitConfig {
  const config = getSiteConfig(siteId);
  const defaultConfig = {
    maxRequests: 60,
    windowSeconds: 60
  };

  if (!config?.rateLimit) {
    return defaultConfig;
  }

  // 转换旧的配置格式
  if ('points' in config.rateLimit && 'duration' in config.rateLimit) {
    return {
      maxRequests: config.rateLimit.points,
      windowSeconds: config.rateLimit.duration
    };
  }

  // 使用新的配置格式
  return {
    maxRequests: (config.rateLimit as any).maxRequests || defaultConfig.maxRequests,
    windowSeconds: (config.rateLimit as any).windowSeconds || defaultConfig.windowSeconds
  };
}

// 限流中间件
export async function rateLimiterMiddleware(
  req: NextRequest,
  siteId: string,
  customIP?: string
): Promise<NextResponse | null> {
  try {
    // 优先使用自定义IP，否则从请求头或IP中获取
    const clientIP = customIP || 
      req.headers.get('x-forwarded-for')?.split(',')[0] || 
      req.headers.get('x-real-ip') || 
      'unknown';

    // 获取限流配置
    const config = getRateLimitConfig(siteId);

    // 使用内存存储进行限流（临时方案，后续可替换为 Redis）
    const key = `rate-limit:${siteId}:${clientIP}`;
    const now = Date.now();
    const windowMs = config.windowSeconds * 1000;

    // 获取当前计数
    let current = rateLimitStore.get(key) || { count: 0, resetTime: now + windowMs };
    
    // 如果已经过期，重置计数
    if (now > current.resetTime) {
      current = { count: 0, resetTime: now + windowMs };
    }

    // 增加计数
    current.count++;
    rateLimitStore.set(key, current);

    // 检查是否超过限制
    if (current.count > config.maxRequests) {
      return NextResponse.json(
        { error: 'Rate limit exceeded' },
        { 
          status: 429,
          headers: {
            'Retry-After': config.windowSeconds.toString(),
            'X-RateLimit-Limit': config.maxRequests.toString(),
            'X-RateLimit-Remaining': '0',
            'X-RateLimit-Reset': Math.floor(current.resetTime / 1000).toString()
          }
        }
      );
    }

    // 添加限流信息到响应头
    const response = NextResponse.next();
    response.headers.set('X-RateLimit-Limit', config.maxRequests.toString());
    response.headers.set('X-RateLimit-Remaining', Math.max(0, config.maxRequests - current.count).toString());
    response.headers.set('X-RateLimit-Reset', Math.floor(current.resetTime / 1000).toString());

    return null;
  } catch (error) {
    console.error('[Rate Limiter] Error:', error);
    return null;
  }
}

// 内存存储（临时方案）
interface RateLimitEntry {
  count: number;
  resetTime: number;
}

const rateLimitStore = new Map<string, RateLimitEntry>();

// 定期清理过期数据
setInterval(() => {
  const now = Date.now();
  for (const [key, value] of rateLimitStore.entries()) {
    if (now > value.resetTime) {
      rateLimitStore.delete(key);
    }
  }
}, 60000); // 每分钟清理一次 