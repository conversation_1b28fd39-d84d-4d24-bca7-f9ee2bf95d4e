import { EventEmitter } from 'events';

export class LoadStateManager {
    private networkRequests: Set<string> = new Set();
    private mutationObserver: MutationObserver | null = null;
    private loadingStates = {
        networkIdle: false,
        domContentLoaded: false,
        load: false,
        domStable: false
    };
    private eventEmitter = new EventEmitter();
    private domStableTimeout: NodeJS.Timeout | null = null;

    constructor(private options = {
        networkIdleTimeout: 500,    // 网络空闲等待时间
        domStableTimeout: 200,      // DOM 稳定等待时间
        maxWaitTime: 3000          // 最大等待时间
    }) {
        this.setupNetworkMonitor();
        this.setupDOMObserver();
        this.setupLoadEvents();
    }

    private setupNetworkMonitor() {
        // 拦截 XHR
        const originalXHR = window.XMLHttpRequest.prototype.send;
        const manager = this;

        window.XMLHttpRequest.prototype.send = function(this: XMLHttpRequest, body?: Document | XMLHttpRequestBodyInit | null): void {
            const requestId = Math.random().toString(36);
            manager.networkRequests.add(requestId);
            manager.loadingStates.networkIdle = false;
            
            this.addEventListener('loadend', () => {
                manager.networkRequests.delete(requestId);
                if (manager.networkRequests.size === 0) {
                    setTimeout(() => {
                        if (manager.networkRequests.size === 0) {
                            manager.loadingStates.networkIdle = true;
                            manager.eventEmitter.emit('networkIdle');
                        }
                    }, manager.options.networkIdleTimeout);
                }
            });
            
            return originalXHR.call(this, body);
        };

        // 拦截 Fetch
        const originalFetch = window.fetch;
        window.fetch = async (input: RequestInfo | URL, init?: RequestInit): Promise<Response> => {
            const requestId = Math.random().toString(36);
            this.networkRequests.add(requestId);
            this.loadingStates.networkIdle = false;

            try {
                const response = await originalFetch.call(window, input, init);
                return response;
            } finally {
                this.networkRequests.delete(requestId);
                if (this.networkRequests.size === 0) {
                    setTimeout(() => {
                        if (this.networkRequests.size === 0) {
                            this.loadingStates.networkIdle = true;
                            this.eventEmitter.emit('networkIdle');
                        }
                    }, this.options.networkIdleTimeout);
                }
            }
        };
    }

    private setupDOMObserver() {
        this.mutationObserver = new MutationObserver(() => {
            // 每次DOM变化，都标记为不稳定
            this.loadingStates.domStable = false;
            
            // 清除之前的定时器
            if (this.domStableTimeout) {
                clearTimeout(this.domStableTimeout);
            }
            
            // 设置新的定时器，等待一段时间后如果没有新的变化，则认为DOM稳定
            this.domStableTimeout = setTimeout(() => {
                this.loadingStates.domStable = true;
                this.eventEmitter.emit('domStable');
            }, this.options.domStableTimeout);
        });

        // 配置观察器，只观察重要的变化
        this.mutationObserver.observe(document.body, {
            childList: true,
            subtree: true,
            attributes: true,
            attributeFilter: ['class', 'style', 'disabled', 'aria-hidden', 'value'],
            characterData: false  // 不监听文本内容变化
        });

        // 初始状态设为不稳定，等待第一次稳定
        this.loadingStates.domStable = false;
        
        // 触发初始检查
        if (this.domStableTimeout) {
            clearTimeout(this.domStableTimeout);
        }
        this.domStableTimeout = setTimeout(() => {
            this.loadingStates.domStable = true;
            this.eventEmitter.emit('domStable');
        }, this.options.domStableTimeout);
    }

    private setupLoadEvents() {
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                this.loadingStates.domContentLoaded = true;
                this.eventEmitter.emit('domcontentloaded');
            });
        } else {
            this.loadingStates.domContentLoaded = true;
        }

        if (document.readyState !== 'complete') {
            window.addEventListener('load', () => {
                this.loadingStates.load = true;
                this.eventEmitter.emit('load');
            });
        } else {
            this.loadingStates.load = true;
        }
    }

    public async waitForLoadState(state: keyof typeof this.loadingStates): Promise<boolean> {
        return new Promise((resolve, reject) => {
            // 如果状态已经是完成的，直接返回
            if (this.loadingStates[state]) {
                resolve(true);
                return;
            }

            // 设置一个监听器等待状态变化
            const handleStateChange = () => {
                resolve(true);
            };

            // 设置超时
            const timeoutId = setTimeout(() => {
                this.eventEmitter.removeListener(state, handleStateChange);
                reject(new Error(`Timeout waiting for ${state}`));
            }, this.options.maxWaitTime);

            // 监听状态变化
            this.eventEmitter.once(state, () => {
                clearTimeout(timeoutId);
                handleStateChange();
            });
        });
    }

    public async waitForComplete(): Promise<boolean> {
        try {
            await Promise.all([
                this.waitForLoadState('networkIdle'),
                this.waitForLoadState('domContentLoaded'),
                this.waitForLoadState('load'),
                this.waitForLoadState('domStable')
            ]);
            return true;
        } catch (error) {
            console.error('Wait for complete failed:', error);
            return false;
        }
    }

    public destroy() {
        if (this.mutationObserver) {
            this.mutationObserver.disconnect();
        }
        this.eventEmitter.removeAllListeners();
    }
}

export const loadStateManager = new LoadStateManager(); 