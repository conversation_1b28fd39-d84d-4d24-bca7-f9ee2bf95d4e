import { DOMBaseNode, DOMElementNode, DOMTextNode, DOMState } from './types';

export class DOMService {
    private static instance: DOMService;
    private highlightCounter: number = 0;

    public static getInstance(): DOMService {
        if (!DOMService.instance) {
            DOMService.instance = new DOMService();
        }
        return DOMService.instance;
    }

    private constructor() {}

    public async getInteractiveElements(): Promise<string> {
        this.highlightCounter = 0;  // Reset counter
        const domState = await this.getDOMState();
        return this.elementTreeToString(domState.element_tree);
    }

    public async getDOMState(): Promise<DOMState> {
        this.highlightCounter = 0;  // Reset counter here too
        const element_tree = await this.buildDomTree();
        const selector_map = this.createSelectorMap(element_tree);
        
        return {
            url: window.location.href,
            tabs: [document.title], // For single tab scenario
            element_tree,
            selector_map
        };
    }

    public async getStateDescription(): Promise<string> {
        const state = await this.getDOMState();
        return [
            `Current url: ${state.url}`,
            'Available tabs:',
            state.tabs.join('\n'),
            'Interactive elements:',
            this.elementTreeToString(state.element_tree)
        ].join('\n');
    }

    public domStateToJSON(domState: DOMState): any {
        return {
            element_tree: this.nodeToJSON(domState.element_tree),
            selector_map: Object.fromEntries(
                Object.entries(domState.selector_map).map(([key, value]) => [
                    key,
                    this.nodeToJSON(value)
                ])
            )
        };
    }

    private nodeToJSON(node: DOMBaseNode): any {
        if (this.isDOMElementNode(node)) {
            return {
                type: 'element',
                tag_name: node.tag_name,
                attributes: node.attributes,
                is_interactive: node.is_interactive,
                is_visible: node.is_visible,
                xpath: node.xpath,
                children: node.children.map(child => this.nodeToJSON(child))
            };
        } else {
            return {
                type: 'text',
                text: (node as DOMTextNode).text,
                is_visible: node.is_visible
            };
        }
    }

    private async buildDomTree(): Promise<DOMElementNode> {
        const root = document.documentElement;
        return this.parseNode(root) as DOMElementNode;
    }

    private createSelectorMap(element_tree: DOMElementNode): Record<string, DOMElementNode> {
        const selector_map: Record<string, DOMElementNode> = {};
        
        const processNode = (node: DOMBaseNode) => {
            if (this.isDOMElementNode(node)) {
                if (node.highlight_index !== undefined) {
                    selector_map[node.xpath] = node;
                }
                node.children.forEach(processNode);
            }
        };

        processNode(element_tree);
        return selector_map;
    }

    private isElementAccepted(element: Element): boolean {
        const leafElementDenyList = new Set(['svg', 'script', 'style', 'link', 'meta', 'noscript', 'iframe']);
        return !leafElementDenyList.has(element.tagName.toLowerCase());
    }

    private parseNode(element: Element | Text, parent?: DOMElementNode): DOMBaseNode | null {
        if (element instanceof Text) {
            const text = element.textContent?.trim() || '';
            if (!text) return null;  // Skip empty text nodes
            return this.createTextNode(element, parent);
        }

        // Skip non-accepted elements
        if (!this.isElementAccepted(element)) {
            return null;
        }

        const node = this.createElement(element, parent);

        // Process children
        const children: DOMBaseNode[] = [];
        for (const child of Array.from(element.childNodes)) {
            if (child instanceof Element || child instanceof Text) {
                const childNode = this.parseNode(child, node);
                if (childNode) {
                    children.push(childNode);
                }
            }
        }
        node.children = children;

        return node;
    }

    private createTextNode(textElement: Text, parent?: DOMElementNode): DOMTextNode {
        const is_visible = this.isNodeVisible(textElement);
        return {
            type: 'TEXT_NODE',
            text: textElement.textContent || '',
            is_visible,
            parent
        };
    }

    private createElement(element: Element, parent?: DOMElementNode): DOMElementNode {
        const tag_name = element.tagName.toLowerCase();
        const xpath = this.getXPath(element);
        const attributes = this.getElementAttributes(element);
        const is_visible = this.isNodeVisible(element);
        const is_interactive = this.isInteractiveElement(element);
        
        return {
            tag_name,
            xpath,
            attributes,
            children: [],
            is_visible,
            is_interactive,
            is_top_element: !parent,
            shadow_root: !!element.shadowRoot,
            parent,
            highlight_index: is_interactive ? this.highlightCounter++ : undefined
        };
    }

    private isInteractiveElement(element: Element): boolean {
        const tag_name = element.tagName.toLowerCase();
        const role = element.getAttribute('role');
        const tabIndex = element.getAttribute('tabindex');
        
        // 基本交互元素
        const interactiveElements = new Set([
            'a', 'button', 'input', 'select', 'textarea'
        ]);

        // 交互角色
        const interactiveRoles = new Set([
            'button', 'link', 'menuitem', 'option', 'tab',
            'menuitemcheckbox', 'menuitemradio', 'textbox', 'combobox'
        ]);

        // 检查基本交互元素
        if (interactiveElements.has(tag_name)) {
            // 链接必须有 href
            if (tag_name === 'a' && !element.hasAttribute('href')) {
                return false;
            }
            // 按钮不能被禁用
            if ((tag_name === 'button' || tag_name === 'input') && 
                (element.hasAttribute('disabled') || element.getAttribute('aria-disabled') === 'true')) {
                return false;
            }
            return true;
        }

        // 检查角色
        if (role && interactiveRoles.has(role)) {
            // menuitem 必须在 menu/menubar 内
            if (role === 'menuitem') {
                let parent = element.parentElement;
                while (parent) {
                    const parentRole = parent.getAttribute('role');
                    if (parentRole === 'menu' || parentRole === 'menubar') {
                        return true;
                    }
                    parent = parent.parentElement;
                }
                return false;
            }
            return true;
        }

        // 检查 tabindex
        if (tabIndex !== null && tabIndex !== '-1') {
            return true;
        }

        // 检查 ARIA 状态
        const hasAriaState = ['aria-expanded', 'aria-pressed', 'aria-selected'].some(attr => {
            const value = element.getAttribute(attr);
            return value === 'true' || value === 'false';
        });

        if (hasAriaState) {
            return true;
        }

        // 检查事件处理器
        const hasEventHandler = 
            element.hasAttribute('onclick') ||
            element.hasAttribute('ng-click') ||
            element.hasAttribute('@click');

        return hasEventHandler;
    }

    private getXPath(element: Element): string {
        const segments: string[] = [];
        let currentElement: Element | null = element;

        while (currentElement && currentElement.nodeType === Node.ELEMENT_NODE) {
            let index = 0;
            let sibling = currentElement.previousElementSibling;
            
            while (sibling) {
                if (sibling.nodeName === currentElement.nodeName) {
                    index++;
                }
                sibling = sibling.previousElementSibling;
            }

            const tag_name = currentElement.nodeName.toLowerCase();
            const xpathIndex = index > 0 ? `[${index + 1}]` : '';
            segments.unshift(`${tag_name}${xpathIndex}`);

            currentElement = currentElement.parentElement;
        }

        return segments.join('/');
    }

    private getElementAttributes(element: Element): Record<string, string> {
        const attributes: Record<string, string> = {};
        const importantAttributes = new Set([
            // 基本属性
            'type', 'role', 'href', 'value',
            'target',
            // ARIA 属性
            'aria-expanded', 'aria-pressed', 'aria-selected', 'aria-checked',
            'aria-label', 'aria-disabled', 'aria-controls', 'aria-haspopup',
            'aria-hidden', 'aria-describedby', 'aria-required',
            // 交互属性
            'tabindex', 'disabled', 'readonly',
            // 描述性属性
            'placeholder', 'title', 'alt', 'name',
            // 状态属性
            'checked', 'selected', 'required'
        ]);
        
        // 特殊处理：如果是输入框，确保保留 placeholder
        const tag_name = element.tagName.toLowerCase();
        if (tag_name === 'input' || tag_name === 'textarea') {
            const placeholder = element.getAttribute('placeholder');
            if (placeholder) {
                attributes['placeholder'] = placeholder;
            }
        }

        // 处理其他属性
        for (const attr of Array.from(element.attributes)) {
            if (importantAttributes.has(attr.name) || attr.name.startsWith('aria-')) {
                attributes[attr.name] = attr.value;
            }
        }

        return attributes;
    }

    private isNodeVisible(node: Element | Text): boolean {
        if (node instanceof Text) {
            const text = node.textContent;
            return text !== null && text.trim().length > 0;
        }

        const style = window.getComputedStyle(node);
        return style.display !== 'none' && 
               style.visibility !== 'hidden' && 
               style.opacity !== '0';
    }

    private isDOMElementNode(node: DOMBaseNode): node is DOMElementNode {
        return 'tag_name' in node;
    }

    public elementTreeToString(node: DOMBaseNode): string {
        const formatted_text: string[] = [];

        const hasParentWithHighlightIndex = (node: DOMBaseNode): boolean => {
            let current = node.parent;
            while (current) {
                if (this.isDOMElementNode(current) && current.highlight_index !== undefined) {
                    return true;
                }
                current = current.parent;
            }
            return false;
        };

        const getAllTextTillNextClickable = (node: DOMElementNode): string => {
            const text_parts: string[] = [];

            const collectText = (currentNode: DOMBaseNode) => {
                // Skip this branch if we hit a highlighted element (except for the current node)
                if (this.isDOMElementNode(currentNode) && 
                    currentNode !== node && 
                    currentNode.highlight_index !== undefined) {
                    return;
                }

                if (!this.isDOMElementNode(currentNode)) {
                    const textNode = currentNode as DOMTextNode;
                    const trimmedText = textNode.text.trim();
                    if (trimmedText) {
                        text_parts.push(trimmedText);
                    }
                } else {
                    currentNode.children.forEach(child => collectText(child));
                }
            };

            collectText(node);
            return text_parts.join('\n').trim();
        };

        const getImportantAttributes = (node: DOMElementNode): string => {
            const importantAttrs = [
                // 基本属性
                'type', 'role', 'href', 'value', 'target',
                // ARIA 属性
                'aria-expanded', 'aria-pressed', 'aria-selected', 'aria-checked',
                'aria-label', 'aria-disabled', 'aria-controls', 'aria-haspopup',
                // 交互属性
                'tabindex', 'disabled',
                // 描述性属性
                'placeholder'
            ];

            // 特殊处理：确保输入框的 placeholder 和 role 属性
            const tag_name = node.tag_name.toLowerCase();
            if (tag_name === 'input' || tag_name === 'textarea') {
                if (node.attributes['placeholder'] && !importantAttrs.includes('placeholder')) {
                    importantAttrs.push('placeholder');
                }
                if (!node.attributes['role'] && node.attributes['type'] === 'text') {
                    node.attributes['role'] = 'textbox';
                }
            }

            // 按照特定顺序排序属性
            const attrs = Object.entries(node.attributes)
                .filter(([key]) => importantAttrs.includes(key) || key.startsWith('aria-'))
                .sort(([a], [b]) => {
                    // role 属性放在最前面
                    if (a === 'role') return -1;
                    if (b === 'role') return 1;
                    // type 属性放在第二位
                    if (a === 'type') return -1;
                    if (b === 'type') return 1;
                    return a.localeCompare(b);
                })
                .map(([key, value]) => `${key}="${value}"`);

            return attrs.length ? ' ' + attrs.join(' ') : '';
        };

        const processNode = (currentNode: DOMBaseNode) => {
            if (this.isDOMElementNode(currentNode)) {
                if (currentNode.highlight_index !== undefined) {
                    const text = getAllTextTillNextClickable(currentNode);
                    const attrs = getImportantAttributes(currentNode);
                    formatted_text.push(`${currentNode.highlight_index}[:]<${currentNode.tag_name}${attrs}>${text}</${currentNode.tag_name}>`);
                }
                currentNode.children.forEach(child => processNode(child));
            } else {
                const textNode = currentNode as DOMTextNode;
                const trimmedText = textNode.text.trim();
                if (trimmedText && !hasParentWithHighlightIndex(currentNode)) {
                    formatted_text.push(`_[:]${trimmedText}`);
                }
            }
        };

        processNode(node);
        return formatted_text.join('\n');
    }
} 