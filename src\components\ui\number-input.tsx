'use client';

import * as React from "react";
import { cn } from "@/lib/utils";
import { ChevronUp, ChevronDown } from "lucide-react";

interface NumberInputProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'onChange'> {
  value: number;
  onChange: (value: number) => void;
  min?: number;
  max?: number;
  step?: number;
  className?: string;
  buttonClassName?: string;
}

const NumberInput = React.forwardRef<HTMLDivElement, NumberInputProps>(
  ({ className, value, onChange, min = 1, max = 10, step = 1, buttonClassName, disabled, ...props }, ref) => {
    const handleIncrement = () => {
      if (disabled) return;
      const newValue = Math.min(max, value + step);
      onChange(newValue);
    };

    const handleDecrement = () => {
      if (disabled) return;
      const newValue = Math.max(min, value - step);
      onChange(newValue);
    };

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      if (disabled) return;
      const newValue = parseInt(e.target.value);
      if (isNaN(newValue)) return;
      
      // Clamp value between min and max
      const clampedValue = Math.max(min, Math.min(max, newValue));
      onChange(clampedValue);
    };

    return (
      <div 
        ref={ref} 
        className={cn(
          "flex h-9 rounded-md border border-blue-700/30 bg-gray-900/70 overflow-hidden",
          className
        )}
      >
        <input
          type="number"
          value={value}
          onChange={handleChange}
          min={min}
          max={max}
          step={step}
          disabled={disabled}
          className="w-full bg-transparent px-3 py-1 text-sm text-cyan-100 border-none focus:outline-none focus:ring-0"
          {...props}
        />
        <div className="flex flex-col border-l border-blue-700/30">
          <button
            type="button"
            onClick={handleIncrement}
            disabled={disabled || value >= max}
            className={cn(
              "flex items-center justify-center h-4.5 px-1 text-cyan-400 hover:bg-gray-800/80 hover:text-cyan-300 border-b border-blue-700/30 transition-colors",
              disabled && "opacity-50 cursor-not-allowed",
              buttonClassName
            )}
          >
            <ChevronUp size={14} />
          </button>
          <button
            type="button"
            onClick={handleDecrement}
            disabled={disabled || value <= min}
            className={cn(
              "flex items-center justify-center h-4.5 px-1 text-cyan-400 hover:bg-gray-800/80 hover:text-cyan-300 transition-colors",
              disabled && "opacity-50 cursor-not-allowed",
              buttonClassName
            )}
          >
            <ChevronDown size={14} />
          </button>
        </div>
      </div>
    );
  }
);

NumberInput.displayName = "NumberInput";

export { NumberInput };
