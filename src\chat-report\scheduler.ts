import { generateDailyReport, appendToDailyReport } from './reportAnalyzer';
import { saveReport, getReportByDate, getLatestReportTimestamp } from './reportStorage';
import * as chatStorage from '@/utils/storage';

// Time to run the report generation (24-hour format)
const REPORT_GENERATION_HOUR = 0; // Midnight
const REPORT_GENERATION_MINUTE = 0;

// Check if the scheduler is running in a web environment
const isWebEnvironment = typeof window !== 'undefined';

// Flag to ensure we don't run twice in the same day
let lastRunDate: string | null = null;

// Calculate ms until next run time
const getMsUntilNextRun = (): number => {
  const now = new Date();
  const nextRun = new Date(now);

  // Set next run time to today at the specified hour and minute
  nextRun.setHours(REPORT_GENERATION_HOUR, REPORT_GENERATION_MINUTE, 0, 0);

  // If it's already past today's run time, set to tomorrow
  if (now.getTime() > nextRun.getTime()) {
    nextRun.setDate(nextRun.getDate() + 1);
  }

  return nextRun.getTime() - now.getTime();
};

// Run the daily report generation
const runDailyReportGeneration = async (): Promise<void> => {
  try {
    const today = new Date().toISOString().split('T')[0];

    // Skip if already run today
    if (lastRunDate === today) {
      console.log('Daily report already generated today, skipping');
      return;
    }

    console.log(`Generating daily report for ${today}`);
    const report = await generateDailyReport(today);

    // Save the generated report
    await saveReport(report);

    console.log(`Daily report for ${today} generated successfully`);

    // Update last run date
    lastRunDate = today;
  } catch (error) {
    console.error('Error generating daily report:', error);
  }
};

// Schedule the next run
const scheduleNextRun = (): void => {
  const msUntilNextRun = getMsUntilNextRun();
  console.log(`Scheduling next report generation in ${msUntilNextRun}ms (${msUntilNextRun / (1000 * 60 * 60)} hours)`);

  setTimeout(() => {
    runDailyReportGeneration().then(() => {
      // Schedule the next one after this completes
      scheduleNextRun();
    });
  }, msUntilNextRun);
};

// Function to start the scheduler
export const startReportScheduler = (): void => {
  if (isWebEnvironment) {
    console.log('Report scheduler not started - browser environment detected');
    return;
  }

  console.log('Starting report scheduler');
  // Run immediately if it's first start
  runDailyReportGeneration().then(() => {
    // Then schedule for regular runs
    scheduleNextRun();
  });
};

// Export for manual testing and triggering
export const manualTriggerDailyReport = async (date?: string, forceRegenerate: boolean = false): Promise<void> => {
  try {
    const reportDate = date || new Date().toISOString().split('T')[0];
    console.log(`Manually triggering report generation for ${reportDate}${forceRegenerate ? ' (forced)' : ''}`);

    // 检查是否已经存在该日期的报告
    const existingReport = await getReportByDate(reportDate);

    // 获取最新报告时间戳
    const latestReportTimestamp = await getLatestReportTimestamp();

    if (existingReport && !forceRegenerate) {
      // 如果已经存在报告且不强制重新生成，则尝试追加新的聊天记录
      if (latestReportTimestamp) {
        console.log(`Report for ${reportDate} already exists, checking for new chats since ${latestReportTimestamp}...`);

        // 将新的聊天记录追加到现有报告
        const updatedReport = await appendToDailyReport(reportDate, existingReport, latestReportTimestamp);

        // 如果有新的聊天记录被追加，则保存更新后的报告
        if (updatedReport.chatIds.length > existingReport.chatIds.length) {
          console.log(`Appended ${updatedReport.chatIds.length - existingReport.chatIds.length} new chats to report for ${reportDate}`);
          await saveReport(updatedReport);
          return;
        } else {
          console.log(`No new chats to append for ${reportDate}, skipping`);
          return;
        }
      } else {
        console.log(`Report for ${reportDate} already exists and no latest report timestamp available, skipping generation`);
        return;
      }
    }

    if (existingReport && forceRegenerate) {
      console.log(`Report for ${reportDate} already exists but force regenerate is enabled, regenerating...`);
    }

    // 获取指定日期的聊天记录数量
    const allChats = await chatStorage.getChatHistoryList();
    const datePrefix = reportDate.replace(/-/g, '');

    // 过滤出指定日期的聊天记录
    const dateChats = allChats.filter(chat =>
      chat.id.startsWith(datePrefix) ||
      (chat.createdAt && new Date(chat.createdAt).toISOString().split('T')[0] === reportDate)
    );

    if (dateChats.length === 0) {
      console.log(`No chats found for ${reportDate}, skipping report generation`);
      return;
    }

    console.log(`Found ${dateChats.length} chats for ${reportDate}, generating report...`);
    const report = await generateDailyReport(reportDate);

    // Save the generated report
    await saveReport(report);

    console.log(`Report generated and saved for ${reportDate}`);
  } catch (error) {
    console.error(`Error manually generating report: ${error}`);
    throw error; // Re-throw to allow calling code to handle error
  }
};