import { clientUserContextManager } from './clientUserContext';

// 检查是否在开发环境中
const isDevelopment = process.env.NODE_ENV === 'development';

// 获取环境变量中的 WMS 端点地址
const WMS_ENDPOINT = process.env.NEXT_PUBLIC_WMS_ENDPOINT || 'https://wms-staging.item.com';

type RequestMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';

interface RequestOptions {
  method?: RequestMethod;
  headers?: Record<string, string>;
  body?: any;
  skipAuth?: boolean;
  tenantId?: string;
}

interface WmsApiResponse<T> {
  data: T | null;
  error: string | null;
  status: number;
  success?: boolean;
  msg?: string;
}

/**
 * 发送请求到 WMS API
 * 处理认证头和错误处理
 */
export async function wmsApiRequest<T = any>(
  path: string,
  options: RequestOptions = {}
): Promise<WmsApiResponse<T>> {
  // 确保 path 没有以 / 开头，因为我们会添加它
  const apiPath = path.startsWith('/') ? path.substring(1) : path;
  
  // 在开发环境中使用本地代理，在生产环境中使用实际的 WMS 端点
  let url;
  if (isDevelopment) {
    // 检查路径是否属于 wms-bam 或 wms 或 mdm
    if (apiPath.startsWith('wms-bam/')) {
      url = `/api/wms-bam/${apiPath.substring(8)}`; // 移除 'wms-bam/' 前缀
    } else if (apiPath.startsWith('mdm/')) {
      url = `/api/mdm/${apiPath.substring(4)}`; // 移除 'mdm/' 前缀
    } else {
      url = `/api/wms/${apiPath}`;
    }
  } else {
    url = `${WMS_ENDPOINT}/api/${apiPath}`;
  }
  
  const {
    method = 'GET',
    headers = {},
    body,
    skipAuth = false,
    tenantId
  } = options;
  
  // 准备请求头
  const requestHeaders: Record<string, string> = {
    'Content-Type': 'application/json',
    ...headers
  };
  
  // 添加认证头（除非明确跳过）
  if (!skipAuth) {
    const token = clientUserContextManager.getAuthToken();
    if (token) {
      requestHeaders['Authorization'] = token.startsWith('Bearer ') ? token : `Bearer ${token}`;
    }
    
    // 添加租户ID
    if (tenantId) {
      requestHeaders['x-tenant-id'] = tenantId;
    } else {
      // 尝试从用户上下文获取租户ID
      const userInfo = clientUserContextManager.getWmsUserInfo();
      if (userInfo?.tenantId) {
        requestHeaders['x-tenant-id'] = userInfo.tenantId;
      }
    }
    
    // 添加设施ID头
    const currentFacilityId = clientUserContextManager.getCurrentFacilityId();
    if (currentFacilityId) {
      requestHeaders['X-Facility-Id'] = currentFacilityId;
    }
  }
  
  // 准备请求配置
  const requestConfig: RequestInit = {
    method,
    headers: requestHeaders,
    credentials: 'same-origin'
  };
  
  // 添加请求体（如果有）
  if (body && method !== 'GET') {
    requestConfig.body = typeof body === 'string' ? body : JSON.stringify(body);
  }
  
  try {
    console.log(`[WMS API] ${method} ${url}`, { headers: requestHeaders, body });
    const response = await fetch(url, requestConfig);
    
    // 处理响应
    if (response.ok) {
      try {
        // 尝试解析 JSON 响应
        const data = await response.json();
        
        // WMS API 通常返回 { success: true/false, data: {...}, msg: '...' } 结构
        if (data && typeof data.success === 'boolean') {
          if (data.success) {
            return { 
              data: data.data, 
              error: null, 
              status: response.status,
              success: true,
              msg: data.msg
            };
          } else {
            return { 
              data: null, 
              error: data.msg || '请求失败', 
              status: response.status,
              success: false,
              msg: data.msg
            };
          }
        }
        
        // 如果不是标准 WMS 响应结构，直接返回数据
        return { data, error: null, status: response.status };
      } catch (e) {
        // 如果无法解析为 JSON，返回文本
        const text = await response.text();
        return { data: text as any, error: null, status: response.status };
      }
    } else {
      // 处理 HTTP 错误
      let errorMessage: string;
      try {
        const errorData = await response.json();
        errorMessage = errorData.msg || errorData.message || `HTTP 错误: ${response.status}`;
      } catch (e) {
        errorMessage = await response.text() || `HTTP 错误: ${response.status}`;
      }
      
      return {
        data: null,
        error: errorMessage,
        status: response.status,
        success: false
      };
    }
  } catch (error) {
    // 处理网络错误或其他异常
    console.error('[WMS API] 请求失败:', error);
    return {
      data: null,
      error: error instanceof Error ? error.message : '请求失败',
      status: 0,
      success: false
    };
  }
}

// 为常用请求方法提供便捷函数
export const wmsApi = {
  get: <T>(path: string, options: Omit<RequestOptions, 'method' | 'body'> = {}) => 
    wmsApiRequest<T>(path, { ...options, method: 'GET' }),
  
  post: <T>(path: string, body: any, options: Omit<RequestOptions, 'method'> = {}) => 
    wmsApiRequest<T>(path, { ...options, method: 'POST', body }),
  
  put: <T>(path: string, body: any, options: Omit<RequestOptions, 'method'> = {}) => 
    wmsApiRequest<T>(path, { ...options, method: 'PUT', body }),
  
  patch: <T>(path: string, body: any, options: Omit<RequestOptions, 'method'> = {}) => 
    wmsApiRequest<T>(path, { ...options, method: 'PATCH', body }),
  
  delete: <T>(path: string, options: Omit<RequestOptions, 'method'> = {}) => 
    wmsApiRequest<T>(path, { ...options, method: 'DELETE' })
};

export default wmsApi; 