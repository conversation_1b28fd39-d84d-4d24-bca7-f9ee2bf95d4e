'use client';

import React, { useEffect } from 'react';
import { usePermission } from '@/hooks/usePermission';
import { UserRole } from '@/utils/permissionConfig';

// Unauthorized page component
const UnauthorizedPage = () => (
  <div className="min-h-screen bg-[#202123] flex flex-col items-center justify-center text-[#ECECF1] p-4">
    <div className="bg-[#343541]/80 backdrop-blur-sm p-8 rounded-xl border border-[#565869] shadow-lg max-w-md w-full">
      <h1 className="text-2xl font-bold text-[#ECECF1] mb-4 font-mono">Access Denied</h1>
      <p className="mb-6 text-[#A2A2AD]">
        You don't have permission to access this page. If you believe this is an error, please contact your system administrator.
      </p>
      <a
        href="/"
        className="inline-block bg-[#7E5FF7] hover:bg-[#7E5FF7]/90 text-white font-medium py-2 px-4 rounded transition-colors font-mono"
      >
        Return to Home
      </a>
    </div>
  </div>
);

// Loading component - 通用加载页面，不显示正在检查权限
const LoadingPage = () => (
  <div className="min-h-screen bg-[#202123] flex items-center justify-center text-[#ECECF1]">
    <div className="text-center">
      <div className="inline-block animate-spin rounded-full h-12 w-12 border-4 border-[#7E5FF7] border-t-transparent mb-4"></div>
      <div className="animate-pulse text-xl font-mono text-[#A2A2AD]">Loading...</div>
    </div>
  </div>
);

// Permission control HOC
export function withPermission<P extends object>(
  Component: React.ComponentType<P>,
  requiredRoles?: UserRole[]
) {
  return function ProtectedComponent(props: P) {
    const { isAuthorized, isChecking, userRoles } = usePermission();

    console.log('DEBUG-WITHPERMISSION: 组件渲染', {
      isAuthorized,
      isChecking,
      userRoles,
      requiredRoles,
      componentName: Component.displayName || Component.name || 'UnknownComponent'
    });

    // If required roles are specified, check if user has any of these roles
    const hasRequiredRoles = requiredRoles
      ? requiredRoles.some(role => userRoles.includes(role))
      : true;

    console.log('DEBUG-WITHPERMISSION: 角色检查结果', { hasRequiredRoles });

    // 如果正在检查权限或认证状态不确定，显示通用加载页面
    if (isChecking || isAuthorized === null) {
      console.log('DEBUG-WITHPERMISSION: 显示加载页面', { isChecking, isAuthorized });
      return <LoadingPage />;
    }

    // 如果权限检查完成且未授权，显示未授权页面
    if (isAuthorized === false || (requiredRoles && !hasRequiredRoles)) {
      console.log('DEBUG-WITHPERMISSION: 显示未授权页面', {
        isAuthorized,
        hasRequiredRoles,
        condition: isAuthorized === false || (requiredRoles && !hasRequiredRoles)
      });
      return <UnauthorizedPage />;
    }

    // 如果已授权，渲染原始组件
    console.log('DEBUG-WITHPERMISSION: 显示原始组件 (已授权)');
    return <Component {...props} />;
  };
}

export default withPermission;
