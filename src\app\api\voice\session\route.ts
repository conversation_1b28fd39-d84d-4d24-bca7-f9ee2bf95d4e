import { NextRequest, NextResponse } from 'next/server';
import { getUserIdFromRequest } from '@/utils/authUtils';

export async function POST(req: NextRequest) {
  // 身份验证
  const userId = await getUserIdFromRequest(req);
  if (!userId) {
    return NextResponse.json(
      { error: 'User not authenticated' },
      { status: 401 }
    );
  }

  try {
    const body = await req.json();
    const { model = 'gpt-4o-realtime-preview-2025-06-03', voice = 'alloy' } = body;

    console.log(`[VoiceSession] Creating session for user ${userId} with model ${model}`);

    const response = await fetch('https://api.openai.com/v1/realtime/sessions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${process.env.OPENAI_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model,
        voice,
        input_audio_transcription: {
          model: 'whisper-1',
       },
        modalities: ['text', 'audio'],
        turn_detection: {
          type: 'server_vad',
          threshold: 0.9,
          prefix_padding_ms: 300,
          silence_duration_ms: 500,
          create_response: true,
        }
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`[VoiceSession] OpenAI API error: ${response.status} - ${errorText}`);
      throw new Error(`OpenAI API error: ${response.status}`);
    }

    const data = await response.json();
    console.log(`[VoiceSession] Session created successfully for user ${userId}`);
    
    return NextResponse.json(data);
  } catch (error) {
    console.error('[VoiceSession] Error creating voice session:', error);
    return NextResponse.json(
      { error: 'Failed to create voice session' },
      { status: 500 }
    );
  }
} 