'use client';

import React from 'react';
import ArtifactToolCard from './ArtifactToolCard';

interface BiDashboardCardProps {
  toolInvocation: any;
}

export default function BiDashboardCard({ toolInvocation }: BiDashboardCardProps) {
  const result = toolInvocation.result;
  const dashboardName = result?.dashboard_name || toolInvocation.args?.params?.[0]?.dashboard_name || 'BI Dashboard';
  const hasArtifact = result?.artifact;
  
  const biDashboardIcon = (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-4 h-4">
      <path strokeLinecap="round" strokeLinejoin="round" d="M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 013 19.875v-6.75zM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 01-1.125-1.125V8.625zM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 01-1.125-1.125V4.125z" />
    </svg>
  );

  return (
    <ArtifactToolCard
      toolInvocation={toolInvocation}
      title={`${dashboardName}`}
      icon={biDashboardIcon}
      borderColor="border-item-green/30"
      iconBgColor="bg-item-green/20"
      iconTextColor="text-item-green"
    />
  );
} 