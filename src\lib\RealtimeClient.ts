/*
 * Realtime Client adapter for our project
 * Based on openai-realtime-agents implementation
 */

import { RealtimeSession, RealtimeAgent, OpenAIRealtimeWebRTC } from '@openai/agents/realtime';
import { log } from '@/utils/logger';

// 简化的事件发射器
type Listener<Args extends any[]> = (...args: Args) => void;

class MiniEmitter<Events extends Record<string, any[]>> {
  private events = new Map<keyof Events, Listener<any[]>[]>();

  on<K extends keyof Events>(event: K, fn: Listener<Events[K]>) {
    const arr = this.events.get(event) || [];
    arr.push(fn);
    this.events.set(event, arr);
  }

  off<K extends keyof Events>(event: K, fn: Listener<Events[K]>) {
    const arr = this.events.get(event) || [];
    this.events.set(
      event,
      arr.filter((f) => f !== fn),
    );
  }

  emit<K extends keyof Events>(event: K, ...args: Events[K]) {
    const arr = this.events.get(event) || [];
    arr.forEach((fn) => fn(...args));
  }
}

export type ClientEvents = {
  connection_change: ['connected' | 'connecting' | 'disconnected'];
  message: [any]; // raw transport events
  audio_interrupted: [];
  history_added: [any];
  history_updated: [any[]];
  
  // 用户相关事件
  user_transcript: [string]; // 用户语音转文本完成
  user_speech_started: []; // 用户开始说话
  user_speech_stopped: []; // 用户停止说话
  
  // 助手相关事件 - 简化为统一的转录事件
  assistant_transcript_delta: [string]; // AI回复文本增量（包括原始文本和音频转录）
  assistant_transcript_done: [string]; // AI回复文本完成
  assistant_response_created: []; // AI开始回复
  assistant_response_done: []; // AI回复完成
  
  // 音频相关事件
  audio_received: [ArrayBuffer]; // 接收到音频数据
  audio_playback_started: []; // 音频播放开始
  audio_playback_finished: []; // 音频播放结束
};

export interface RealtimeClientOptions {
  getEphemeralKey: () => Promise<string>; // returns ek_ string
  audioElement?: HTMLAudioElement;
  extraContext?: Record<string, any>;
  agent?: RealtimeAgent;
}

export class RealtimeClient {
  private session: RealtimeSession | null = null;
  private events = new MiniEmitter<ClientEvents>();
  private options: RealtimeClientOptions;
  private isConnected = false;

  constructor(options: RealtimeClientOptions) {
    this.options = options;
  }

  // 获取音频元素（用于录音）
  getAudioElement(): HTMLAudioElement | undefined {
    return this.options.audioElement;
  }

  on<K extends keyof ClientEvents>(event: K, listener: (...args: ClientEvents[K]) => void) {
    this.events.on(event, listener as any);
  }

  off<K extends keyof ClientEvents>(event: K, listener: (...args: ClientEvents[K]) => void) {
    this.events.off(event, listener as any);
  }

  async connect() {
    if (this.session || this.isConnected) return;

    try {
      const ek = await this.options.getEphemeralKey();
      
      log.info('Starting connection...', undefined, 'RealtimeClient');
      this.events.emit('connection_change', 'connecting');

      // 确保有代理
      if (!this.options.agent) {
        throw new Error('No agent provided');
      }

      // 创建传输层
      const transportValue: any = this.options.audioElement
        ? new OpenAIRealtimeWebRTC({
            useInsecureApiKey: true,
            audioElement: this.options.audioElement,
          })
        : 'webrtc';

      // 创建会话
      this.session = new RealtimeSession(this.options.agent, {
        transport: transportValue,
        context: this.options.extraContext ?? {},
      });

      this.setupEventListeners();

      // 连接
      await this.session.connect({ apiKey: ek });
      
      this.isConnected = true;
      this.events.emit('connection_change', 'connected');
      log.info('Connected successfully', undefined, 'RealtimeClient');

    } catch (error) {
      log.error('Connection failed:', error, 'RealtimeClient');
      this.events.emit('connection_change', 'disconnected');
      throw error;
    }
  }

  disconnect() {
    if (this.session) {
      this.session.close();
      this.session = null;
    }
    this.isConnected = false;
    this.events.emit('connection_change', 'disconnected');
    log.info('Disconnected', undefined, 'RealtimeClient');
  }

  sendUserText(text: string) {
    if (!this.session || !this.isConnected) {
      throw new Error('Not connected');
    }
    this.session.sendMessage(text);
  }

  pushToTalkStart() {
    if (!this.session || !this.isConnected) return;
    
    // 清空音频缓冲区
    this.session.transport.sendEvent({ type: 'input_audio_buffer.clear' } as any);
    this.events.emit('user_speech_started');
  }

  pushToTalkStop() {
    if (!this.session || !this.isConnected) return;
    
    // 提交音频并创建响应
    this.session.transport.sendEvent({ type: 'input_audio_buffer.commit' } as any);
    this.session.transport.sendEvent({ type: 'response.create' } as any);
    this.events.emit('user_speech_stopped');
  }

  sendEvent(event: any) {
    this.session?.transport.sendEvent(event);
  }

  interrupt() {
    this.session?.transport.interrupt();
    this.events.emit('audio_interrupted');
  }

  mute(muted: boolean) {
    this.session?.mute(muted);
  }

  private setupEventListeners() {
    if (!this.session) return;

    // 监听传输层事件
    const transport = this.session.transport as any;
    if (transport && transport.on) {
      // 转发所有事件
      transport.on('*', (ev: any) => {
        this.events.emit('message', ev);
        this.handleTransportEvent(ev);
      });

      // 连接状态变化
      transport.on('connection_change', (status: any) => {
        if (status === 'disconnected') {
          this.isConnected = false;
          this.events.emit('connection_change', 'disconnected');
        }
      });
    }

    // 监听会话事件
    this.session.on('history_updated', (history: any) => {
      this.events.emit('history_updated', history);
    });

    this.session.on('audio_interrupted', () => {
      this.events.emit('audio_interrupted');
    });
  }

  private handleTransportEvent(ev: any) {
    try {
      // 用户语音转文本完成
      if (ev.type === 'conversation.item.input_audio_transcription.completed') {
        const transcript = ev.transcript;
        if (transcript && transcript.trim()) {
          log.debug('User transcript:', transcript, 'RealtimeClient');
          this.events.emit('user_transcript', transcript.trim());
        }
      }

      // 用户开始说话
      if (ev.type === 'input_audio_buffer.speech_started') {
        this.events.emit('user_speech_started');
      }

      // 用户停止说话
      if (ev.type === 'input_audio_buffer.speech_stopped') {
        this.events.emit('user_speech_stopped');
      }

      // 助手开始回复
      if (ev.type === 'response.created') {
        log.debug('Assistant response created', undefined, 'RealtimeClient');
        this.events.emit('assistant_response_created');
      }

      // 助手回复文本增量 (AI回复文本增量，包括原始文本和音频转录)
      if (ev.type === 'response.text.delta') {
        const delta = ev.delta;
        if (delta) {
          log.debug('Assistant text delta:', delta, 'RealtimeClient');
          this.events.emit('assistant_transcript_delta', delta);
        }
      }

      // 助手音频转录增量 (音频的转录)
      if (ev.type === 'response.audio_transcript.delta') {
        const delta = ev.delta;
        if (delta) {
          log.debug('Assistant transcript delta:', delta, 'RealtimeClient');
          this.events.emit('assistant_transcript_delta', delta);
        }
      }

      // 助手回复文本完成
      if (ev.type === 'response.text.done') {
        log.debug('Assistant text response completed', undefined, 'RealtimeClient');
        this.events.emit('assistant_transcript_done', ev.text || '');
      }

      // 助手音频转录完成
      if (ev.type === 'response.audio_transcript.done') {
        log.debug('Assistant audio transcript completed', undefined, 'RealtimeClient');
        this.events.emit('assistant_transcript_done', ev.transcript || '');
      }

      // 接收到音频数据
      if (ev.type === 'response.audio.delta') {
        const audioData = ev.delta;
        if (audioData) {
          // 将 base64 转换为 ArrayBuffer
          const arrayBuffer = this.base64ToArrayBuffer(audioData);
          this.events.emit('audio_received', arrayBuffer);
        }
      }

      // 音频播放开始
      if (ev.type === 'response.audio.done') {
        log.debug('Audio response completed', undefined, 'RealtimeClient');
        this.events.emit('audio_playback_finished');
      }

      // 助手回复完全完成
      if (ev.type === 'response.done') {
        log.debug('Assistant response fully completed', undefined, 'RealtimeClient');
        this.events.emit('assistant_response_done');
      }
    } catch (error) {
      console.warn('[RealtimeClient] Error handling transport event:', error);
    }
  }

  private base64ToArrayBuffer(base64: string): ArrayBuffer {
    const binaryString = atob(base64);
    const bytes = new Uint8Array(binaryString.length);
    for (let i = 0; i < binaryString.length; i++) {
      bytes[i] = binaryString.charCodeAt(i);
    }
    return bytes.buffer;
  }
} 