import api from './apiClient';

// BI登录信息接口
export interface BiLoginResponse {
  idToken?: string;
  accessToken?: string;
  refreshToken?: string;
  expiresIn?: number;
  tokenType?: string;
  [key: string]: any; // 允许其他字段
}

/**
 * BI系统登录工具类
 */
export class BiAuth {
  /**
   * 使用后端API登录BI系统
   * @returns BI登录响应
   */
  static async login(): Promise<BiLoginResponse> {
    try {
      console.log('调用BI登录API...');

      // 使用 apiClient 调用后端BI登录接口
      const { data, error } = await api.post<BiLoginResponse>('/api/auth/bi-login', {});

      if (error) {
        throw new Error(error);
      }

      if (data && data.success) {
        console.log('BI登录成功:', data.data);
        return data.data;
      } else {
        throw new Error(data?.error || 'BI登录失败');
      }
    } catch (error) {
      console.error('BI登录失败:', error);
      throw new Error(`BI登录失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 验证BI登录响应是否有效
   * @param response BI登录响应
   * @returns 是否有效
   */
  static isValidResponse(response: BiLoginResponse): boolean {
    return !!(response && (response.idToken || response.accessToken));
  }

  /**
   * 保存BI登录信息到localStorage
   * @param biInfo BI登录信息
   */
  static saveBiInfo(biInfo: BiLoginResponse): void {
    if (typeof window === 'undefined') return;

    try {
      localStorage.setItem('bi_user_info', JSON.stringify(biInfo));
      console.log('BI登录信息已保存到localStorage');
    } catch (error) {
      console.error('保存BI登录信息失败:', error);
    }
  }

  /**
   * 从localStorage获取BI登录信息
   * @returns BI登录信息
   */
  static getBiInfo(): BiLoginResponse | null {
    if (typeof window === 'undefined') return null;

    try {
      const biInfo = localStorage.getItem('bi_user_info');
      return biInfo ? JSON.parse(biInfo) : null;
    } catch (error) {
      console.error('获取BI登录信息失败:', error);
      return null;
    }
  }

  /**
   * 清除BI登录信息
   */
  static clearBiInfo(): void {
    if (typeof window === 'undefined') return;

    try {
      localStorage.removeItem('bi_user_info');
      console.log('BI登录信息已清除');
    } catch (error) {
      console.error('清除BI登录信息失败:', error);
    }
  }

  /**
   * 检查BI登录信息是否存在
   * @returns 是否存在
   */
  static hasBiInfo(): boolean {
    const biInfo = this.getBiInfo();
    return this.isValidResponse(biInfo || {});
  }
} 