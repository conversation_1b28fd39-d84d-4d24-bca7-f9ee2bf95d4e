'use client';

import React, { useState, useMemo, useEffect } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { Message } from '@ai-sdk/react';
import WeatherCard from './WeatherCard';
import ClockCard from './ClockCard';
import WMSCard from './WMSCard';
import PlanningCard from './PlanningCard';
import ErrorCard from './ErrorCard';
import SequentialThinkingCard from './SequentialThinkingCard';
import MemoryToolCard from './MemoryToolCard';
import KnowledgeCard from './KnowledgeCard';
import JiraCard from './JiraCard';
import RobotDogCard from './RobotDogCard';
import CameraCard from './CameraCard';
import GISCard from './GISCard';
import BiDashboardCard from './BiDashboardCard';
import { DynamicForm as SimpleDynamicForm } from '@/components/ui/dynamic-form';
import { useArtifactsStore } from '@/stores/useArtifactsStore';
import '@/styles/item-design-system.css';
// AIResponseBuffer 已移除

// 直接使用Message类型
interface ChatMessageProps {
  message: Message;
  modelName?: string; // 添加模型名称属性
  onUserInputSubmit?: (toolCallId: string, data: Record<string, any>) => void;
  onUserInputCancel?: (toolCallId: string) => void;
}

function ChatMessage({ message, modelName = 'gpt-4o-2024-11-20', onUserInputSubmit, onUserInputCancel }: ChatMessageProps) {
  const { role, content, parts } = message;
  const isUser = role === 'user';
  const { 
    activeArtifacts: artifacts, 
    activateArtifacts, 
    registerAvailableArtifacts, 
    getAvailableArtifacts,
    activateArtifact 
  } = useArtifactsStore();
  
  // Check if this is a voice message based on the message ID
  const isVoiceMessage = message.id?.startsWith('voice-transcript-') || message.id?.startsWith('voice-user-');
  
  // 检测是否是新消息（30秒内的消息认为是新消息）
  const isRecentMessage = useMemo(() => {
    if (!message.createdAt) return true; // 如果没有创建时间，假设是新消息
    const messageTime = new Date(message.createdAt).getTime();
    const now = Date.now();
    const timeDiff = now - messageTime;
    return timeDiff < 30000; // 30秒内
  }, [message.createdAt, message.id]);
  
  // Process artifacts from tool results
  useEffect(() => {
    if (parts && message.id) {
      const messageArtifacts: any[] = [];
      
      parts.forEach((part: any) => {
        if (part.type === 'tool-invocation' && part.toolInvocation?.result?.artifact) {
          const artifact = {
            ...part.toolInvocation.result.artifact,
            messageId: message.id,
            toolName: part.toolInvocation.toolName
          };
          messageArtifacts.push(artifact);
        }
      });
      
      if (messageArtifacts.length > 0) {
        if (isRecentMessage && !isUser) {
          // 新的AI消息：自动激活artifacts
          activateArtifacts(messageArtifacts);
        } else {
          // 历史消息：只注册，不激活
          registerAvailableArtifacts(message.id, messageArtifacts);
        }
      }
    }
  }, [parts, message.id, isRecentMessage, isUser, activateArtifacts, registerAvailableArtifacts]);
  
  // Dynamic width based on artifacts presence and message type - AI gets more space, User gets appropriate space
  const getMessageWidthClass = () => {
    if (artifacts.length > 0) {
      // When artifacts panel is open
      return isUser 
        ? 'max-w-[70%] md:max-w-[60%] lg:max-w-[55%]' // User messages: compact, content-appropriate
        : 'w-full max-w-none'; // AI messages: use full width
    } else {
      // When no artifacts panel
      return isUser 
        ? 'max-w-[80%] md:max-w-[65%] lg:max-w-[55%] xl:max-w-[50%]' // User messages: compact but readable
        : 'w-full max-w-[96%] lg:max-w-[92%] xl:max-w-[88%] 2xl:max-w-[85%]'; // AI messages: generous space
    }
  };
  
  const messageWidthClass = getMessageWidthClass();
  


  // 获取当前时间 - 改为使用消息的创建时间或当前时间
  const time = message.createdAt 
    ? new Date(message.createdAt).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    : new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });

  // 根据模型类型决定AI助手的显示名称和样式
  const isClaudeModel = modelName?.includes('claude');
  const isGeminiModel = modelName?.includes('gemini');

  let assistantName = 'AI Assistant';
  let assistantIconColor = 'bg-item-purple/20 text-item-purple';

  if (isClaudeModel) {
    assistantName = 'Claude';
    assistantIconColor = 'bg-item-purple/20 text-item-purple';
  } else if (isGeminiModel) {
    assistantName = 'Gemini';
    assistantIconColor = 'bg-item-orange/20 text-item-orange';
  }

  // 移除调试日志以提高性能

  // 图像检测逻辑已在渲染部分直接处理

  // 检测是否包含计划信息
  const extractPlanningData = (textContent: string) => {
    try {
      // 查找包含有效 JSON 的内容
      // 匹配: ```json { ...json内容... } ```
      const jsonRegex = /```(?:json)?\s*(\{[\s\S]*?\})\s*```/g;
      const matchIterator = textContent.matchAll(jsonRegex);

      for (const match of matchIterator) {
        if (match && match[1]) {
          const jsonStr = match[1];
          const planData = JSON.parse(jsonStr);

          // 检查是否为有效的计划数据
          if (
            planData &&
            typeof planData === 'object' &&
            planData.summary &&
            Array.isArray(planData.steps) &&
            planData.steps.length > 0
          ) {
            return {
              planningData: planData,
              // 移除JSON块以避免重复显示
              cleanedText: textContent.replace(match[0], '')
            };
          }
        }
      }
    } catch (e) {
      console.error('Error parsing planning JSON:', e);
    }

    return null;
  };

  return (
    <div className={`flex ${isUser ? 'justify-end' : 'justify-start'} mb-6`}>
      {/* 消息内容部分 */}
      <div
        className={`item-card flex flex-col ${messageWidthClass} rounded-lg shadow-lg border transition-all duration-200 hover:shadow-xl overflow-hidden
          ${isUser
            ? 'bg-item-bg-card border-item-blue/30 text-white item-glow-blue-subtle'
            : isClaudeModel
              ? 'bg-item-bg-card border-item-purple/20 text-white'
              : isGeminiModel
                ? 'bg-item-bg-card border-item-orange/20 text-white'
                : 'bg-item-bg-card border-item-gray-700 text-white'
          }`}
      >
        <div className="flex items-center px-4 py-3 border-b border-item-gray-800/60 bg-item-bg-primary/50">
          {/* AI头像部分 - 在标题里 */}
          {!isUser && (
            <div className="flex-shrink-0 mr-3">
              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${assistantIconColor} transition-all duration-200 hover:scale-105`}>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  strokeWidth={1.5}
                  stroke="currentColor"
                  className="w-5 h-5"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456zM16.894 20.567L16.5 21.75l-.394-1.183a2.25 2.25 0 00-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 001.423-1.423l.394-1.183.394 1.183a2.25 2.25 0 001.423 1.423l1.183.394-1.183.394a2.25 2.25 0 00-1.423 1.423z"
                  />
                </svg>
              </div>
            </div>
          )}
          <div className="flex-1">
            <span className="font-semibold text-sm text-white">{isUser ? 'You' : assistantName}</span>
          </div>
          <div className="text-xs text-item-gray-400 ml-2 font-medium">{time}</div>
          {isVoiceMessage && (
            <div className="ml-2 text-xs text-item-purple flex items-center" title="Voice message">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-3.5 h-3.5">
                <path strokeLinecap="round" strokeLinejoin="round" d="M12 18.75a6 6 0 006-6v-1.5m-6 7.5a6 6 0 01-6-6v-1.5m6 7.5v3.75m-3.75 0h7.5M12 15.75a3 3 0 01-3-3V4.5a3 3 0 116 0v8.25a3 3 0 01-3 3z" />
              </svg>
            </div>
          )}
        </div>
        <div className="px-4 py-4 overflow-x-auto">
          {/* 多模态内容处理 */}
          {Array.isArray(content) ? (
            <div className="space-y-3">
              {content.map((item: any, i: number) => {
                if (item.type === 'text') {
                  // 如果文本已被处理为工具调用的reasoning，则跳过
                  if ((item as any)._processed) {
                    return null;
                  }

                  // 检查是否包含计划信息
                  const planningDataResult = extractPlanningData(item.text || '');

                  return (
                    <div key={i}>
                      {planningDataResult && (
                        <div className="mb-4">
                          <PlanningCard planningData={planningDataResult.planningData} />
                        </div>
                      )}
                      <div className="prose prose-invert prose-sm max-w-none break-words">
                        {!isUser ? (
                          // 直接使用 ReactMarkdown 渲染 AI 回复
                          <ReactMarkdown
                            remarkPlugins={[remarkGfm]}
                            components={{
                              pre: ({ node, ...props }) => (
                              <div className="overflow-x-auto bg-black/50 rounded-md p-2 my-2">
                                <pre {...props} className="text-sm" />
                              </div>
                            ),
                              code: ({ node, ...props }: { node?: any, inline?: boolean }) => {
                                const { inline, ...restProps } = props as { inline?: boolean };
                                return inline ? (
                                  <code {...restProps} className="bg-gray-800 px-1 py-0.5 rounded text-cyan-300" />
                                ) : (
                                  <code {...restProps} />
                                );
                              },
                              ul: ({ node, ...props }) => <ul {...props} className="list-disc pl-4 space-y-1.5" />,
                              ol: ({ node, ...props }) => <ol {...props} className="list-decimal pl-4 space-y-1.5" />,
                              li: ({ node, ...props }) => <li {...props} className="pl-1" />,
                              p: ({ node, ...props }) => <p {...props} className="mb-2 break-words" />,
                              a: ({ node, ...props }) => (
                                <a {...props} className="text-cyan-400 hover:underline hover:text-cyan-300 break-all" target="_blank" rel="noopener noreferrer" />
                              ),
                              table: ({ node, ...props }) => (
                                <div className="overflow-x-auto my-2">
                                  <table {...props} className="border-collapse border border-gray-700" />
                                </div>
                              ),
                              thead: ({ node, ...props }) => <thead {...props} className="bg-gray-800" />,
                              tbody: ({ node, ...props }) => <tbody {...props} className="bg-gray-900/50" />,
                              tr: ({ node, ...props }) => <tr {...props} className="border-b border-gray-700" />,
                              th: ({ node, ...props }) => <th {...props} className="px-4 py-2 text-left" />,
                              td: ({ node, ...props }) => <td {...props} className="px-4 py-2 border-r border-gray-700 last:border-r-0" />,
                            }}
                          >
                            {planningDataResult ? planningDataResult.cleanedText : item.text || ''}
                          </ReactMarkdown>
                        ) : (
                          // 对用户消息使用普通渲染
                          <ReactMarkdown
                            remarkPlugins={[remarkGfm]}
                            components={{
                              pre: ({ node, ...props }) => (
                              <div className="overflow-x-auto bg-black/50 rounded-md p-2 my-2">
                                <pre {...props} className="text-sm" />
                              </div>
                            ),
                              code: ({ node, ...props }: { node?: any, inline?: boolean }) => {
                                const { inline, ...restProps } = props as { inline?: boolean };
                                return inline ? (
                                  <code {...restProps} className="bg-gray-800 px-1 py-0.5 rounded text-cyan-300" />
                                ) : (
                                  <code {...restProps} />
                                );
                              },
                              ul: ({ node, ...props }) => <ul {...props} className="list-disc pl-4 space-y-1.5" />,
                              ol: ({ node, ...props }) => <ol {...props} className="list-decimal pl-4 space-y-1.5" />,
                              li: ({ node, ...props }) => <li {...props} className="pl-1" />,
                              p: ({ node, ...props }) => <p {...props} className="mb-2 break-words" />,
                              a: ({ node, ...props }) => (
                                <a {...props} className="text-cyan-400 hover:underline hover:text-cyan-300 break-all" target="_blank" rel="noopener noreferrer" />
                              ),
                              table: ({ node, ...props }) => (
                                <div className="overflow-x-auto my-2">
                                  <table {...props} className="border-collapse border border-gray-700" />
                                </div>
                              ),
                              thead: ({ node, ...props }) => <thead {...props} className="bg-gray-800" />,
                              tbody: ({ node, ...props }) => <tbody {...props} className="bg-gray-900/50" />,
                              tr: ({ node, ...props }) => <tr {...props} className="border-b border-gray-700" />,
                              th: ({ node, ...props }) => <th {...props} className="px-4 py-2 text-left" />,
                              td: ({ node, ...props }) => <td {...props} className="px-4 py-2 border-r border-gray-700 last:border-r-0" />,
                            }}
                          >
                            {planningDataResult ? planningDataResult.cleanedText : item.text || ''}
                          </ReactMarkdown>
                        )}
                      </div>
                    </div>
                  );
                } else if (item.type === 'image_url') {
                  return (
                    <div key={i} className="border border-gray-700 rounded-md overflow-hidden bg-black/30 max-w-[500px]">
                      <img
                        src={item.image_url?.url}
                        alt="User uploaded image"
                        className="max-w-full object-contain"
                      />
                    </div>
                  );
                }
                return null;
              })}
            </div>
          ) : parts && parts.length > 0 ? (
            <div className="space-y-4">
              {parts.map((part: any, index: number) => {
                if (part.type === 'text') {
                  // 不要立即标记为_processed，只有被确认为某个工具的reasoning时才标记
                  // 检查是否包含计划信息
                  const planningDataResult = extractPlanningData(part.text || '');

                  // PlanningCard需要特殊处理，确保它总是被显示
                  if (planningDataResult && planningDataResult.planningData) {
                    return (
                      <div key={index}>
                        <div className="mb-4">
                          <PlanningCard planningData={planningDataResult.planningData} />
                        </div>
                        {planningDataResult.cleanedText && planningDataResult.cleanedText.trim() !== '' && (
                          <div className="prose prose-invert prose-sm max-w-none break-words">
                            <ReactMarkdown
                              remarkPlugins={[remarkGfm]}
                              components={{
                                pre: ({ node, ...props }) => (
                                <div className="overflow-x-auto bg-black/50 rounded-md p-2 my-2">
                                  <pre {...props} className="text-sm" />
                                </div>
                              ),
                                code: ({ node, ...props }: { node?: any, inline?: boolean }) => {
                                  const { inline, ...restProps } = props as { inline?: boolean };
                                  return inline ? (
                                    <code {...restProps} className="bg-gray-800 px-1 py-0.5 rounded text-cyan-300" />
                                  ) : (
                                    <code {...restProps} />
                                  );
                                },
                                ul: ({ node, ...props }) => <ul {...props} className="list-disc pl-4 space-y-1.5" />,
                                ol: ({ node, ...props }) => <ol {...props} className="list-decimal pl-4 space-y-1.5" />,
                                li: ({ node, ...props }) => <li {...props} className="pl-1" />,
                                a: ({ node, ...props }) => (
                                  <a {...props} className="text-cyan-400 hover:underline hover:text-cyan-300" target="_blank" rel="noopener noreferrer" />
                                ),
                                table: ({ node, ...props }) => (
                                  <div className="overflow-x-auto my-2">
                                    <table {...props} className="border-collapse border border-gray-700" />
                                  </div>
                                ),
                                thead: ({ node, ...props }) => <thead {...props} className="bg-gray-800" />,
                                tbody: ({ node, ...props }) => <tbody {...props} className="bg-gray-900/50" />,
                                tr: ({ node, ...props }) => <tr {...props} className="border-b border-gray-700" />,
                                th: ({ node, ...props }) => <th {...props} className="px-4 py-2 text-left" />,
                                td: ({ node, ...props }) => <td {...props} className="px-4 py-2 border-r border-gray-700 last:border-r-0" />,
                              }}
                            >
                              {planningDataResult.cleanedText}
                            </ReactMarkdown>
                          </div>
                        )}
                      </div>
                    );
                  }

                  // 如果文本已被处理为工具调用的reasoning，则跳过
                  if ((part as any)._processed) {
                    return null;
                  }

                  return (
                    <div key={index} className="ai-message-content">
                      <div className="prose prose-invert prose-sm max-w-none break-words">
                        {!isUser ? (
                          // 直接使用 ReactMarkdown 渲染 AI 回复
                          <ReactMarkdown
                            remarkPlugins={[remarkGfm]}
                            components={{
                              pre: ({ node, ...props }) => (
                              <div className="overflow-x-auto bg-black/50 rounded-md p-2 my-2">
                                <pre {...props} className="text-sm" />
                              </div>
                            ),
                              code: ({ node, ...props }: { node?: any, inline?: boolean }) => {
                                const { inline, ...restProps } = props as { inline?: boolean };
                                return inline ? (
                                  <code {...restProps} className="bg-gray-800 px-1 py-0.5 rounded text-cyan-300" />
                                ) : (
                                  <code {...restProps} />
                                );
                              },
                              ul: ({ node, ...props }) => <ul {...props} className="list-disc pl-4 space-y-1.5" />,
                              ol: ({ node, ...props }) => <ol {...props} className="list-decimal pl-4 space-y-1.5" />,
                              li: ({ node, ...props }) => <li {...props} className="pl-1" />,
                              p: ({ node, ...props }) => <p {...props} className="mb-2 break-words" />,
                              a: ({ node, ...props }) => (
                                <a {...props} className="text-cyan-400 hover:underline hover:text-cyan-300 break-all" target="_blank" rel="noopener noreferrer" />
                              ),
                              table: ({ node, ...props }) => (
                                <div className="overflow-x-auto my-2">
                                  <table {...props} className="border-collapse border border-gray-700" />
                                </div>
                              ),
                              thead: ({ node, ...props }) => <thead {...props} className="bg-gray-800" />,
                              tbody: ({ node, ...props }) => <tbody {...props} className="bg-gray-900/50" />,
                              tr: ({ node, ...props }) => <tr {...props} className="border-b border-gray-700" />,
                              th: ({ node, ...props }) => <th {...props} className="px-4 py-2 text-left" />,
                              td: ({ node, ...props }) => <td {...props} className="px-4 py-2 border-r border-gray-700 last:border-r-0" />,
                            }}
                          >
                            {part.text || ''}
                          </ReactMarkdown>
                        ) : (
                          // 对用户消息使用普通渲染
                          <ReactMarkdown
                            remarkPlugins={[remarkGfm]}
                            components={{
                              pre: ({ node, ...props }) => (
                              <div className="overflow-x-auto bg-black/50 rounded-md p-2 my-2">
                                <pre {...props} className="text-sm" />
                              </div>
                            ),
                              code: ({ node, ...props }: { node?: any, inline?: boolean }) => {
                                const { inline, ...restProps } = props as { inline?: boolean };
                                return inline ? (
                                  <code {...restProps} className="bg-gray-800 px-1 py-0.5 rounded text-cyan-300" />
                                ) : (
                                  <code {...restProps} />
                                );
                              },
                              ul: ({ node, ...props }) => <ul {...props} className="list-disc pl-4 space-y-1.5" />,
                              ol: ({ node, ...props }) => <ol {...props} className="list-decimal pl-4 space-y-1.5" />,
                              li: ({ node, ...props }) => <li {...props} className="pl-1" />,
                              p: ({ node, ...props }) => <p {...props} className="mb-2 text-gray-300" />,
                              h1: ({ node, ...props }) => <h1 {...props} className="text-xl font-bold mt-4 mb-2 text-gray-200" />,
                              h2: ({ node, ...props }) => <h2 {...props} className="text-lg font-bold mt-3 mb-2 text-gray-200" />,
                              h3: ({ node, ...props }) => <h3 {...props} className="text-md font-bold mt-3 mb-1 text-gray-200" />,
                              strong: ({ node, ...props }) => <strong {...props} className="font-semibold text-gray-200" />,
                              a: ({ node, ...props }) => (
                                <a {...props} className="text-cyan-400 hover:underline hover:text-cyan-300 break-all" target="_blank" rel="noopener noreferrer" />
                              ),
                              table: ({ node, ...props }) => (
                                <div className="overflow-x-auto my-2">
                                  <table {...props} className="border-collapse border border-gray-700" />
                                </div>
                              ),
                              thead: ({ node, ...props }) => <thead {...props} className="bg-gray-800" />,
                              tbody: ({ node, ...props }) => <tbody {...props} className="bg-gray-900/50" />,
                              tr: ({ node, ...props }) => <tr {...props} className="border-b border-gray-700" />,
                              th: ({ node, ...props }) => <th {...props} className="px-4 py-2 text-left text-gray-200" />,
                              td: ({ node, ...props }) => <td {...props} className="px-4 py-2 border-r border-gray-700 last:border-r-0 text-gray-300" />,
                              blockquote: ({ node, ...props }) => <blockquote {...props} className="border-l-4 border-gray-600 pl-4 py-1 my-2 text-gray-400 italic" />,
                              hr: ({ node, ...props }) => <hr {...props} className="border-gray-700 my-4" />,
                            }}
                          >
                            {part.text || ''}
                          </ReactMarkdown>
                        )}
                      </div>
                    </div>
                  );
                } else if (part.type === 'image_url') {
                  return (
                    <div key={index} className="border border-gray-700 rounded-md overflow-hidden bg-black/30 max-w-[500px]">
                      <img
                        src={part.image_url?.url}
                        alt="Image"
                        className="max-w-full object-contain"
                      />
                    </div>
                  );
                } else if (part.type === 'tool-invocation') {
                  // 收集所有工具调用以便分组处理
                  const toolInvocations = parts
                    .filter(p => p.type === 'tool-invocation')
                    .map(p => {
                      // 将部分索引和messageId存储到工具调用对象，以便后续查找对应的reasoning
                      return {...p.toolInvocation, _partIndex: parts.findIndex(item => item === p), messageId: message.id};
                    });

                  // 构建reasoning映射，将文本部分与工具调用关联
                  const reasoningMap = new Map<number, React.ReactNode>();
                  // 记录哪些文本部分被处理为reasoning
                  const processedTextIndices = new Set<number>();

                  // 为每个工具调用寻找可能的前置reasoning文本
                  for (let i = 0; i < parts.length; i++) {
                    const currentPart = parts[i];

                    // 如果当前部分是工具调用
                    if (currentPart.type === 'tool-invocation') {
                      // 查找前面最近的文本部分
                      let foundReasoning = false;
                      for (let j = i - 1; j >= 0; j--) {
                        const prevPart = parts[j];
                        // 检查前一个部分是否是文本且不是planning数据
                        if (prevPart.type === 'text' && !processedTextIndices.has(j)) {
                          // 找到工具调用前的文本部分
                          const planningDataResult = extractPlanningData(prevPart.text || '');
                          // 如果包含PlanningData，不要将其作为reasoning处理
                          if (planningDataResult && planningDataResult.planningData) {
                            continue; // 跳过这个文本部分，不将其视为reasoning
                          }

                          const reasoning = (
                            <div className="prose prose-invert prose-sm max-w-none mb-2 break-words">
                              <ReactMarkdown
                                remarkPlugins={[remarkGfm]}
                                components={{
                                  pre: ({ node, ...props }) => (
                                  <div className="overflow-x-auto bg-black/50 rounded-md p-2 my-2">
                                    <pre {...props} className="text-sm" />
                                  </div>
                                ),
                                  code: ({ node, ...props }: { node?: any, inline?: boolean }) => {
                                    const { inline, ...restProps } = props as { inline?: boolean };
                                    return inline ? (
                                      <code {...restProps} className="bg-gray-800 px-1 py-0.5 rounded text-cyan-300" />
                                    ) : (
                                      <code {...restProps} />
                                    );
                                  },
                                  ul: ({ node, ...props }) => <ul {...props} className="list-disc pl-4 space-y-1.5" />,
                                  ol: ({ node, ...props }) => <ol {...props} className="list-decimal pl-4 space-y-1.5" />,
                                  li: ({ node, ...props }) => <li {...props} className="pl-1" />,
                                  a: ({ node, ...props }) => (
                                    <a {...props} className="text-cyan-400 hover:underline hover:text-cyan-300" target="_blank" rel="noopener noreferrer" />
                                  ),
                                  table: ({ node, ...props }) => (
                                    <div className="overflow-x-auto my-2">
                                      <table {...props} className="border-collapse border border-gray-700" />
                                    </div>
                                  ),
                                  thead: ({ node, ...props }) => <thead {...props} className="bg-gray-800" />,
                                  tbody: ({ node, ...props }) => <tbody {...props} className="bg-gray-900/50" />,
                                  tr: ({ node, ...props }) => <tr {...props} className="border-b border-gray-700" />,
                                  th: ({ node, ...props }) => <th {...props} className="px-4 py-2 text-left" />,
                                  td: ({ node, ...props }) => <td {...props} className="px-4 py-2 border-r border-gray-700 last:border-r-0" />,
                                }}
                              >
                                {planningDataResult ? planningDataResult.cleanedText : prevPart.text || ''}
                              </ReactMarkdown>
                            </div>
                          );

                          // 将这个reasoning组件与工具调用关联
                          reasoningMap.set(i, reasoning);

                          // 标记这个文本部分已被处理
                          processedTextIndices.add(j);
                          (prevPart as any)._processed = true;

                          foundReasoning = true;
                          break;
                        }
                      }

                      // 如果没有找到前置reasoning文本，创建一个空的map entry
                      if (!foundReasoning) {
                        reasoningMap.set(i, null);
                      }
                    }
                  }

                  // 如果是第一个工具调用，处理整个工具调用组
                  if (index === parts.findIndex(p => p.type === 'tool-invocation')) {
                    // 定义工具组件映射
                    const toolComponentMap = {
                      'weatherTool': (props: any) => <WeatherCard {...props} />,
                      'clockTool': (props: any) => <ClockCard {...props} />,
                      'find_wms_api': (props: any) => <WMSCard {...props} type="search" />,
                      'call_wms_api': (props: any) => <WMSCard {...props} type="call" />,
                      'wms': (props: any) => <WMSCard {...props} />,
                      'sequentialthinking': (props: any) => <SequentialThinkingCard {...props} />,
                      'kbTool': (props: any) => <KnowledgeCard {...props} />,
                      'getJiraCreateMeta': (props: any) => <JiraCard {...props} />,
                      'createJiraIssue': (props: any) => <JiraCard {...props} />,
                      'getJiraProjects': (props: any) => <JiraCard {...props} />,
                      'searchJiraIssues': (props: any) => <JiraCard {...props} />,
                      'getJiraIssueTransitions': (props: any) => <JiraCard {...props} />,
                      'doJiraIssueTransition': (props: any) => <JiraCard {...props} />,
                      'robotDogPatrolTool': (props: any) => <RobotDogCard {...props} />,
                      'cameraLiveStreamTool': (props: any) => <CameraCard {...props} />,
                      'gisTool': (props: any) => <GISCard {...props} />,
                      'createDashboardTool': (props: any) => <BiDashboardCard {...props} />,

                      'requireUserInputTool': (props: any) => {
                        // Use useMemo to memoize the component based on props
                        // 优化：只在关键状态变化时重新渲染，避免流式更新导致的表单闪烁
                        return useMemo(() => {
                          // 将 requireUserInputTool 返回的结构转换为 dynamic-form 期望的格式
                          const { title, description, fields } = props.toolInvocation.args;

                          // 检查确保组件已导入并存在
                          if (!SimpleDynamicForm) {
                            console.error("SimpleDynamicForm component not found");
                            return <div className="text-red-500">Error: SimpleDynamicForm not available</div>;
                          }

                          // 检查是否有提交结果，如果有则显示只读模式
                          // 同时检查result中的__submitted标记，这是为了支持从历史记录加载
                          const hasResult = props.toolInvocation.state === 'result' && props.toolInvocation.result;
                          // 检查是否取消 - 如果result包含canceled:true，则表示已取消
                          const isCanceled = hasResult && props.toolInvocation.result.canceled === true;
                          // 使用特殊标记来确保历史记录加载时能识别提交状态
                          const isSubmitted = hasResult && (!isCanceled && (props.toolInvocation.result.__submitted || !props.toolInvocation.result.canceled));
                          const submittedData = isSubmitted ? props.toolInvocation.result : null;

                          // 已取消或已提交都不显示表单
                          if (isCanceled) {
                            // 显示已取消消息
                            return (
                              <div className="item-card bg-item-bg-card rounded-lg border border-item-orange/30 p-4">
                                <div className="mb-2 flex items-center">
                                  <div className="bg-item-orange/20 p-2 rounded-lg mr-3">
                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5 text-item-orange">
                                      <path strokeLinecap="round" strokeLinejoin="round" d="M12 9v3.75m9-.75a9 9 0 11-18 0 9 9 0 0118 0zm-9 3.75h.008v.008H12v-.008z" />
                                    </svg>
                                  </div>
                                  <div className="text-base font-medium text-item-gray-500">{title} - Canceled</div>
                                </div>
                                <div className="text-sm text-item-gray-300">User canceled this input request</div>
                              </div>
                            );
                          }

                          if (isSubmitted) {
                            // 显示已提交的表单数据（只读模式），优化样式使其更紧凑
                            return (
                              <div className="item-card bg-item-bg-card rounded-lg border border-item-purple/30 p-4">
                                <div className="mb-3 flex items-center">
                                  <div className="bg-item-purple/20 p-2 rounded-lg mr-3">
                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5 text-item-purple">
                                      <path strokeLinecap="round" strokeLinejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                  </div>
                                  <div className="text-base font-medium text-item-gray-500">{title}</div>
                                </div>

                                {description && <div className="mb-4 text-sm text-item-gray-300">{description}</div>}

                                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                                  {fields.map((field: any, index: number) => {
                                    // 获取提交的值，排除内部使用的__submitted标记
                                    if (field.name === '__submitted') return null;
                                    const value = submittedData[field.name];
                                    if (value === undefined) return null;

                                    // 为不同类型的字段渲染不同的只读显示
                                    return (
                                      <div key={index} className="bg-item-bg-hover/50 p-3 rounded-lg border border-item-gray-800/30">
                                        <div className="text-sm font-semibold text-item-purple mb-1">{field.label}</div>
                                        {field.type === 'select' && (
                                          <div className="text-sm text-gray-200">
                                            {field.options?.find((opt: any) => opt.value === value)?.label || value}
                                          </div>
                                        )}
                                        {field.type === 'checkbox' && (
                                          <div className="text-sm text-gray-200">
                                            {value ? '✓ Yes' : '✗ No'}
                                          </div>
                                        )}
                                        {field.type === 'textarea' && (
                                          <div className="text-sm text-gray-200 whitespace-pre-wrap">
                                            {value || '(No input)'}
                                          </div>
                                        )}
                                        {(field.type === 'text' || field.type === 'number') && (
                                          <div className="text-sm text-gray-200">
                                            {value || '(No input)'}
                                          </div>
                                        )}
                                        {(field.type === 'date' || field.type === 'datetime' || field.type === 'time') && (
                                          <div className="text-sm text-gray-200">
                                            {value instanceof Date
                                              ? value.toLocaleString()
                                              : value || '(No input)'}
                                          </div>
                                        )}
                                        {(field.type === 'userSelector' ||
                                          field.type === 'customerSelector' ||
                                          field.type === 'titleSelector' ||
                                          field.type === 'carrierSelector' ||
                                          field.type === 'deliveryServiceSelector' ||
                                          field.type === 'generalProjectSelector' ||
                                          field.type === 'jobcodeSelector' ||
                                          field.type === 'itemmasterSelector' ||
                                          field.type === 'itemmasterUomSelector') && (
                                          <div className="text-sm text-gray-200">
                                            {/* 使用标签映射显示标签值 */}
                                            {submittedData.__labelMappings &&
                                             submittedData.__labelMappings[field.name] ?
                                              submittedData.__labelMappings[field.name].label :
                                              value || '(No input)'}
                                          </div>
                                        )}
                                        {field.type === 'multipleUserSelector' && (
                                          <div className="text-sm text-gray-200">
                                            {/* 显示多个用户的标签 */}
                                            {submittedData.__labelMappings &&
                                             submittedData.__labelMappings[field.name] ?
                                              submittedData.__labelMappings[field.name].map((user: any, idx: number) => 
                                                user.label || user.name
                                              ).join(', ') :
                                              (Array.isArray(value) ? value.join(', ') : value || '(No input)')}
                                          </div>
                                        )}
                                        {field.type === 'array' && Array.isArray(value) && (
                                          <div className="space-y-1">
                                            {value.length === 0 ?
                                              <div className="text-sm text-gray-400">(No items)</div> :
                                              value.map((item: any, itemIndex: number) => (
                                                <div key={itemIndex} className="bg-gray-800/30 p-1.5 rounded-md text-xs">
                                                  {Object.entries(item).map(([key, val]: [string, any]) => {
                                                    // 跳过元数据字段
                                                    if (key === '__metadata') return null;

                                                    const itemField = field.arrayItemFields?.find((f: any) => f.name === key);
                                                    if (!itemField) return null;

                                                    // 检查是否有标签映射
                                                    const hasMetadata = item.__metadata && item.__metadata[key];
                                                    const displayValue = hasMetadata ? item.__metadata[key].label : val?.toString() || '(Empty)';

                                                    return (
                                                      <div key={key} className="mb-0.5">
                                                        <span className="text-xs text-gray-200">{itemField.label || key}: </span>
                                                        <span className="text-xs text-gray-200">{displayValue}</span>
                                                      </div>
                                                    );
                                                  })}
                                                </div>
                                              ))
                                            }
                                          </div>
                                        )}
                                      </div>
                                    );
                                  })}
                                </div>
                              </div>
                            );
                          }

                          // 显示正常的表单（可交互模式）
                          return (
                            <SimpleDynamicForm
                              fields={fields}
                              title={title}
                              description={description}
                              onSubmit={(data) => {
                                if (onUserInputSubmit) {
                                  // 在数据中添加一个标记，表示这是一个已提交的表单
                                  // 这样在历史记录中加载时也能识别
                                  const submittedData = {
                                    ...data,
                                    __submitted: true // 特殊标记，用于历史记录中识别已提交状态
                                  };
                                  onUserInputSubmit(props.toolInvocation.toolCallId, submittedData);
                                } else {
                                  console.log('No submit handler', props.toolInvocation.toolCallId, data);
                                }
                              }}
                              onCancel={() => {
                                if (onUserInputCancel) {
                                  onUserInputCancel(props.toolInvocation.toolCallId);
                                } else {
                                  console.log('No cancel handler', props.toolInvocation.toolCallId);
                                }
                              }}
                              cancelText="Cancel"
                              submitText="Submit"
                            />
                          );
                        }, [
                          // 优化依赖项：只在真正影响表单渲染的属性变化时重新渲染
                          props.toolInvocation.args?.title,
                          props.toolInvocation.args?.description,
                          props.toolInvocation.args?.fields?.length, // 字段数量变化
                          props.toolInvocation.toolCallId, // 工具调用ID
                          props.toolInvocation.state, // 状态变化（pending -> result）
                          // 只在结果的关键属性变化时重新渲染，避免流式更新导致的闪烁
                          props.toolInvocation.result?.canceled,
                          props.toolInvocation.result?.__submitted,
                          onUserInputSubmit,
                          onUserInputCancel
                        ]); // 优化：避免JSON.stringify导致的不必要重新渲染
                      },
                      'finishTaskTool': (props: any) => <FinishTaskCard {...props} />,
                      'searchMemoriesTool': (props: any) => <MemoryToolCard {...props} type="search" />,
                      'saveMemoryTool': (props: any) => <MemoryToolCard {...props} type="add" />,
                      'updateMemoryTool': (props: any) => <MemoryToolCard {...props} type="add" />,
                      'deleteMemoryTool': (props: any) => <MemoryToolCard {...props} type="delete" />,
                      'error': (props: any) => <ErrorCard
                        toolName={props.toolInvocation.toolName}
                        error={props.toolInvocation.error}
                        args={props.toolInvocation.args}
                      />
                    };

                    // 使用分组函数对工具调用进行分组
                    return (
                      <div key={index} className="mt-2 space-y-1">
                        {groupToolInvocations(toolInvocations).map((group, groupIndex) => (
                          <div key={groupIndex}>
                            {group.length <= 1 ? (
                              (() => {
                                const tool = group[0];
                                const { toolName, state } = tool;

                                // 检查是否有错误
                                if (tool.error) {
                                  return <ErrorCard
                                    toolName={toolName}
                                    error={tool.error}
                                    args={tool.args}
                                  />;
                                }

                                // 根据工具类型显示对应组件
                                const ToolComponent = toolName in toolComponentMap
                                  ? toolComponentMap[toolName as keyof typeof toolComponentMap]
                                  : null;
                                if (ToolComponent) {
                                  return <ToolComponent toolInvocation={tool} />;
                                } else {
                                  return (
                                    <ToolResultCollapsible toolName={toolName} state={state} toolInvocation={tool} />
                                  );
                                }
                              })()
                            ) : (
                              <ToolInvocationGroup
                                toolInvocations={group}
                                toolComponentMap={toolComponentMap}
                                reasoningMap={reasoningMap}
                              />
                            )}
                          </div>
                        ))}
                      </div>
                    );
                  } else {
                    // 如果不是第一个工具调用，则跳过（已经在第一个工具调用中处理过）
                    return null;
                  }
                }

                return null;
              })}
            </div>
          ) : (
            // 兼容旧版本，直接显示content
            <div className="prose prose-invert prose-sm max-w-none break-words">
              {!isUser ? (
                // 直接使用 ReactMarkdown 渲染 AI 回复
                <ReactMarkdown
                  remarkPlugins={[remarkGfm]}
                  components={{
                    pre: ({ node, ...props }) => (
                      <div className="overflow-x-auto bg-black/50 rounded-md p-2 my-2">
                        <pre {...props} className="text-sm" />
                      </div>
                    ),
                    code: ({ node, ...props }: { node?: any, inline?: boolean }) => {
                      const { inline, ...restProps } = props as { inline?: boolean };
                      return inline ? (
                        <code {...restProps} className="bg-gray-800 px-1 py-0.5 rounded text-cyan-300" />
                      ) : (
                        <code {...restProps} />
                      );
                    },
                    ul: ({ node, ...props }) => <ul {...props} className="list-disc pl-4 space-y-1.5" />,
                    ol: ({ node, ...props }) => <ol {...props} className="list-decimal pl-4 space-y-1.5" />,
                    li: ({ node, ...props }) => <li {...props} className="pl-1" />,
                    a: ({ node, ...props }) => (
                      <a {...props} className="text-cyan-400 hover:underline hover:text-cyan-300" target="_blank" rel="noopener noreferrer" />
                    ),
                    table: ({ node, ...props }) => (
                      <div className="overflow-x-auto my-2">
                        <table {...props} className="border-collapse border border-gray-700" />
                      </div>
                    ),
                    thead: ({ node, ...props }) => <thead {...props} className="bg-gray-800" />,
                    tbody: ({ node, ...props }) => <tbody {...props} className="bg-gray-900/50" />,
                    tr: ({ node, ...props }) => <tr {...props} className="border-b border-gray-700" />,
                    th: ({ node, ...props }) => <th {...props} className="px-4 py-2 text-left" />,
                    td: ({ node, ...props }) => <td {...props} className="px-4 py-2 border-r border-gray-700 last:border-r-0" />,
                  }}
                >
                  {content || ''}
                </ReactMarkdown>
              ) : (
                // 对用户消息使用普通渲染
                <ReactMarkdown
                  remarkPlugins={[remarkGfm]}
                  components={{
                    pre: ({ node, ...props }) => (
                      <div className="overflow-x-auto bg-black/50 rounded-md p-2 my-2">
                        <pre {...props} className="text-sm" />
                      </div>
                    ),
                    code: ({ node, ...props }: { node?: any, inline?: boolean }) => {
                      const { inline, ...restProps } = props as { inline?: boolean };
                      return inline ? (
                        <code {...restProps} className="bg-gray-800 px-1 py-0.5 rounded text-cyan-300" />
                      ) : (
                        <code {...restProps} />
                      );
                    },
                    ul: ({ node, ...props }) => <ul {...props} className="list-disc pl-4 space-y-1.5" />,
                    ol: ({ node, ...props }) => <ol {...props} className="list-decimal pl-4 space-y-1.5" />,
                    li: ({ node, ...props }) => <li {...props} className="pl-1" />,
                    p: ({ node, ...props }) => <p {...props} className="mb-2 text-gray-300" />,
                    h1: ({ node, ...props }) => <h1 {...props} className="text-xl font-bold mt-4 mb-2 text-gray-200" />,
                    h2: ({ node, ...props }) => <h2 {...props} className="text-lg font-bold mt-3 mb-2 text-gray-200" />,
                    h3: ({ node, ...props }) => <h3 {...props} className="text-md font-bold mt-3 mb-1 text-gray-200" />,
                    strong: ({ node, ...props }) => <strong {...props} className="font-semibold text-gray-200" />,
                    a: ({ node, ...props }) => (
                      <a {...props} className="text-cyan-400 hover:underline hover:text-cyan-300" target="_blank" rel="noopener noreferrer" />
                    ),
                    table: ({ node, ...props }) => (
                      <div className="overflow-x-auto my-2">
                        <table {...props} className="border-collapse border border-gray-700" />
                      </div>
                    ),
                    thead: ({ node, ...props }) => <thead {...props} className="bg-gray-800" />,
                    tbody: ({ node, ...props }) => <tbody {...props} className="bg-gray-900/50" />,
                    tr: ({ node, ...props }) => <tr {...props} className="border-b border-gray-700" />,
                    th: ({ node, ...props }) => <th {...props} className="px-4 py-2 text-left text-gray-200" />,
                    td: ({ node, ...props }) => <td {...props} className="px-4 py-2 border-r border-gray-700 last:border-r-0 text-gray-300" />,
                    blockquote: ({ node, ...props }) => <blockquote {...props} className="border-l-4 border-gray-600 pl-4 py-1 my-2 text-gray-400 italic" />,
                    hr: ({ node, ...props }) => <hr {...props} className="border-gray-700 my-4" />,
                  }}
                >
                  {content || ''}
                </ReactMarkdown>
              )}
            </div>
          )}
          {/* 处理实验性附件 */}
          {message.experimental_attachments && message.experimental_attachments.length > 0 && (
            <div className="mb-3">
              {message.experimental_attachments
                .filter(attachment => attachment.contentType?.startsWith('image/'))
                .map((attachment, index) => (
                  <div key={`attachment-${index}`} className="border border-gray-700 rounded-md overflow-hidden bg-black/30 max-w-[500px] mb-3">
                    <img
                      src={attachment.url}
                      alt={attachment.name || `Attachment ${index + 1}`}
                      className="max-w-full object-contain"
                    />
                  </div>
                ))}
            </div>
          )}
        </div>
      </div>
      
      {/* 用户头像部分 - 在消息右侧外部 */}
      {isUser && (
        <div className="flex-shrink-0 ml-3">
          <div className="w-10 h-10 rounded-full flex items-center justify-center bg-item-blue/20 text-item-blue transition-all duration-200 hover:scale-105">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth={1.5}
              stroke="currentColor"
              className="w-6 h-6"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M15.75 6a3.75 3.75 0 11-7.5 0 3.75 3.75 0 017.5 0zM4.501 20.118a7.5 7.5 0 0114.998 0A17.933 17.933 0 0112 21.75c-2.676 0-5.216-.584-7.499-1.632z"
              />
            </svg>
          </div>
        </div>
      )}
    </div>
  );
}

// 工具调用结果可折叠组件
function ToolResultCollapsible({ toolName, state, toolInvocation }: { toolName: string; state: string; toolInvocation: any }) {
  const [isExpanded, setIsExpanded] = React.useState(false);
  const hasError = toolInvocation.error !== undefined;

  // 处理中状态
  if (state === 'partial-call' || state === 'call') {
    return (
      <div className="item-card bg-item-bg-card rounded-lg border border-item-gray-800 p-2 text-white shadow-lg my-2">
        <div className="flex items-center">
          <div className="bg-item-bg-hover p-1.5 rounded-lg mr-2.5">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-4 h-4 text-item-gray-400">
              <path strokeLinecap="round" strokeLinejoin="round" d="M7.5 21L3 16.5m0 0L7.5 12M3 16.5h13.5m0-13.5L21 7.5m0 0L16.5 12M21 7.5H7.5" />
            </svg>
          </div>
          <div className="flex-1 text-sm text-white font-medium pr-2 leading-snug">
            {state === 'partial-call' ? 'Processing...' : 'Invoked'} {toolName}
          </div>
        </div>
      </div>
    );
  }

  // 折叠视图
  if (!isExpanded) {
    return (
      <div className="item-card bg-item-bg-card rounded-lg border border-item-gray-800 p-2 text-white shadow-lg my-2 cursor-pointer hover:border-item-gray-700 hover:shadow-xl transition-all duration-200" onClick={() => setIsExpanded(true)}>
        <div className="flex items-center">
          <div className="bg-item-bg-hover p-1.5 rounded-lg mr-2.5">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-4 h-4 text-item-gray-400">
              <path strokeLinecap="round" strokeLinejoin="round" d="M7.5 21L3 16.5m0 0L7.5 12M3 16.5h13.5m0-13.5L21 7.5m0 0L16.5 12M21 7.5H7.5" />
            </svg>
          </div>
          <div className="text-sm text-item-gray-500 font-medium pr-2 leading-snug">
            Result {toolName}
          </div>
          <button className="text-item-gray-400 hover:text-item-gray-300 ml-2 flex-shrink-0 transition-colors duration-200">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-4 h-4">
              <path strokeLinecap="round" strokeLinejoin="round" d="M19.5 8.25l-7.5 7.5-7.5-7.5" />
            </svg>
          </button>
        </div>
      </div>
    );
  }

  // 展开视图
  return (
    <div className="item-card bg-item-bg-card rounded-lg border border-item-gray-800 p-2 text-white shadow-lg my-2">
      <div className="flex justify-between" onClick={() => setIsExpanded(!isExpanded)}>
        <div className="flex items-center flex-1">
          <div className="bg-item-bg-hover p-1.5 rounded-lg mr-2.5">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-4 h-4 text-item-gray-400">
              <path strokeLinecap="round" strokeLinejoin="round" d="M7.5 21L3 16.5m0 0L7.5 12M3 16.5h13.5m0-13.5L21 7.5m0 0L16.5 12M21 7.5H7.5" />
            </svg>
          </div>
          <div className="text-sm text-white font-semibold pr-2 leading-snug">
            Result {toolName}
          </div>
        </div>
        <button className="text-item-gray-400 hover:text-item-gray-300 hover:bg-item-bg-hover p-1.5 rounded-lg ml-2 flex-shrink-0 transition-all duration-200">
          {isExpanded ? (
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-4 h-4">
              <path strokeLinecap="round" strokeLinejoin="round" d="M4.5 15.75l7.5-7.5 7.5 7.5" />
            </svg>
          ) : (
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-4 h-4">
              <path strokeLinecap="round" strokeLinejoin="round" d="M19.5 8.25l-7.5 7.5-7.5-7.5" />
            </svg>
          )}
        </button>
      </div>

      {isExpanded && (
        <div className="mt-3 pt-2.5 border-t border-item-gray-800/40">
          {/* Parameters */}
          <div className="mb-3 bg-item-bg-card rounded-lg p-2 border border-item-gray-800/30">
            <div className="text-xs text-item-gray-400 mb-1.5 font-semibold uppercase tracking-wide">Parameters:</div>
            <pre className="text-xs overflow-auto max-h-20 font-mono whitespace-pre-wrap break-words text-white">
              {JSON.stringify(toolInvocation.args, null, 2)}
            </pre>
          </div>

          {/* Error */}
          {hasError && (
            <div className="mb-3 bg-red-900/20 p-2 rounded-lg border border-red-500/30">
              <div className="text-xs text-red-400 mb-1.5 font-semibold uppercase tracking-wide">Error:</div>
              <pre className="text-xs overflow-auto max-h-20 font-mono whitespace-pre-wrap break-words text-red-300">
                {typeof toolInvocation.error === 'string'
                  ? toolInvocation.error
                  : JSON.stringify(toolInvocation.error, null, 2)}
              </pre>
            </div>
          )}

          {/* Results */}
          {toolInvocation.result && !hasError && (
            <div className="bg-item-bg-card rounded-lg p-2 border border-item-gray-800/30">
              <div className="text-xs text-item-gray-400 mb-1.5 font-semibold uppercase tracking-wide">Results:</div>
              <pre className="text-xs overflow-auto max-h-60 font-mono whitespace-pre-wrap break-words text-white">
                {JSON.stringify(toolInvocation.result, null, 2)}
              </pre>
            </div>
          )}
        </div>
      )}

      <div className="text-xs text-item-gray-400 mt-2.5 text-right font-medium">
        Tool Results
      </div>
    </div>
  );
}

// 任务完成卡片组件
function FinishTaskCard({ toolInvocation }: { toolInvocation: any }) {
  const message = toolInvocation.args?.message || toolInvocation.result?.message || 'Task completed successfully';
  const [isExpanded, setIsExpanded] = React.useState(false);

  // 折叠视图
  if (!isExpanded) {
    return (
      <div
        className="item-card bg-green-600/20 rounded-lg border border-green-500/30 p-2 text-white shadow-lg my-2 cursor-pointer transition-all duration-200 hover:border-green-500/50 hover:shadow-xl item-glow-green-subtle"
        onClick={() => setIsExpanded(true)}
      >
        <div className="flex justify-between">
          <div className="flex items-center flex-1">
            <div className="bg-item-bg-hover p-1.5 rounded-lg mr-2.5">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-4 h-4 text-green-400">
                <path strokeLinecap="round" strokeLinejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div className="text-sm text-item-gray-500 font-medium pr-2 leading-snug">
              <span className="text-item-gray-500 font-medium">Task Completed</span>
              <div className="text-xs text-item-gray-500 mt-0.5 truncate">
                {message.length > 60 ? `${message.substring(0, 60)}...` : message}
              </div>
            </div>
          </div>
          <button className="text-green-400 hover:text-green-300 ml-2 flex-shrink-0 transition-colors duration-200">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-4 h-4">
              <path strokeLinecap="round" strokeLinejoin="round" d="M19.5 8.25l-7.5 7.5-7.5-7.5" />
            </svg>
          </button>
        </div>
      </div>
    );
  }

  // 展开视图
  return (
    <div className="item-card bg-green-600/20 rounded-lg border border-green-500/30 p-2 text-white shadow-lg my-2 item-glow-green-subtle">
      <div className="flex justify-between cursor-pointer" onClick={() => setIsExpanded(!isExpanded)}>
        <div className="flex items-center flex-1">
          <div className="bg-item-bg-hover p-1.5 rounded-lg mr-2.5">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-4 h-4 text-green-400">
              <path strokeLinecap="round" strokeLinejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <div className="text-sm text-white font-semibold leading-snug">
            <span className="text-green-400 font-semibold">Task Completed</span>
          </div>
        </div>
        <button className="text-green-400 hover:text-green-300 hover:bg-item-bg-hover p-1.5 rounded-lg ml-2 flex-shrink-0 transition-all duration-200">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-4 h-4">
            <path strokeLinecap="round" strokeLinejoin="round" d="M4.5 15.75l7.5-7.5 7.5 7.5" />
          </svg>
        </button>
      </div>

      <div className="mt-3 pt-2.5 border-t border-item-gray-800/40">
        <div className="bg-item-bg-card p-2 rounded-lg relative border border-item-gray-800/30">
          <div className="prose prose-invert prose-sm max-w-none break-words">
            <ReactMarkdown
              remarkPlugins={[remarkGfm]}
              components={{
                pre: ({ node, ...props }) => (
                  <div className="overflow-x-auto bg-item-bg-hover rounded-lg p-3 my-2">
                    <pre {...props} className="text-sm" />
                  </div>
                ),
                code: ({ node, ...props }: { node?: any, inline?: boolean }) => {
                  const { inline, ...restProps } = props as { inline?: boolean };
                  return inline ? (
                    <code {...restProps} className="bg-item-bg-hover px-2 py-1 rounded text-green-400 font-medium" />
                  ) : (
                    <code {...restProps} />
                  );
                },
                ul: ({ node, ...props }) => <ul {...props} className="list-disc pl-4 space-y-1.5" />,
                ol: ({ node, ...props }) => <ol {...props} className="list-decimal pl-4 space-y-1.5" />,
                li: ({ node, ...props }) => <li {...props} className="pl-1" />,
                p: ({ node, ...props }) => <p {...props} className="mb-2 text-white" />,
                a: ({ node, ...props }) => (
                  <a {...props} className="text-green-400 hover:underline hover:text-green-300 transition-colors duration-200" target="_blank" rel="noopener noreferrer" />
                ),
                table: ({ node, ...props }) => (
                  <div className="overflow-x-auto my-3">
                    <table {...props} className="border-collapse border border-item-gray-700 w-full" />
                  </div>
                ),
                thead: ({ node, ...props }) => <thead {...props} className="bg-item-bg-hover" />,
                tbody: ({ node, ...props }) => <tbody {...props} className="bg-item-bg-card/50" />,
                tr: ({ node, ...props }) => <tr {...props} className="border-b border-item-gray-700" />,
                th: ({ node, ...props }) => <th {...props} className="px-3 py-2 text-left text-xs text-green-400 font-semibold" />,
                td: ({ node, ...props }) => <td {...props} className="px-3 py-2 text-sm text-white border-r border-item-gray-700 last:border-r-0" />
              }}
            >
              {message}
            </ReactMarkdown>
          </div>
        </div>

        {/* 底部指示条 - 保留但使用更柔和的颜色 */}
        <div className="mt-4 flex space-x-1">
          {[...Array(3)].map((_, i) => (
            <div
              key={i}
              className="h-1 flex-1 bg-green-500/30 rounded-full"
            ></div>
          ))}
        </div>
      </div>
    </div>
  );
}

// 在合适的位置添加一个新的组件来处理工具调用的分组
interface ToolInvocationGroupProps {
  toolInvocations: any[];
  toolComponentMap: any;
  reasoningMap?: Map<number, React.ReactNode>; // 添加reasoning映射
}

function ToolInvocationGroup({ toolInvocations, toolComponentMap, reasoningMap }: ToolInvocationGroupProps) {
  const [isCollapsed, setIsCollapsed] = useState(true);
  const firstTool = toolInvocations[0];
  const lastTool = toolInvocations[toolInvocations.length - 1];

  if (toolInvocations.length <= 1) {
    // 只有一个工具调用，直接渲染不需要分组
    const ToolComponent = firstTool.toolName in toolComponentMap
      ? toolComponentMap[firstTool.toolName as keyof typeof toolComponentMap]
      : null;

    const indexInParts = firstTool._partIndex;
    const reasoning = reasoningMap?.get(indexInParts);

    return (
      <>
        {reasoning}
        {ToolComponent ? <ToolComponent toolInvocation={firstTool} /> : null}
      </>
    );
  }

  // 阻止事件冒泡，避免点击折叠按钮时触发父元素事件
  const handleToggleCollapse = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsCollapsed(!isCollapsed);
  };

  // 获取第一个工具调用的reasoning
  const firstIndex = firstTool._partIndex;
  const firstReasoning = reasoningMap?.get(firstIndex);

  return (
    <div className="mb-2">
      {/* 始终显示首个工具调用的reasoning，即使在折叠状态 */}
      {firstReasoning && <div className="mb-2">{firstReasoning}</div>}

      <div className="flex items-center mb-1">
        <button
          onClick={handleToggleCollapse}
          className="text-gray-400 hover:text-gray-300 flex items-center text-xs bg-gray-800/40 px-2 py-1 rounded"
        >
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-3 h-3 mr-1">
            {isCollapsed ? (
              <path strokeLinecap="round" strokeLinejoin="round" d="M8.25 4.5l7.5 7.5-7.5 7.5" />
            ) : (
              <path strokeLinecap="round" strokeLinejoin="round" d="M19.5 8.25l-7.5 7.5-7.5-7.5" />
            )}
          </svg>
          {isCollapsed ? `Show all ${toolInvocations.length} tool calls` : `Collapse tool calls`}
        </button>
        <div className="text-xs text-gray-500 ml-2">{firstTool.toolName} related operations</div>
      </div>

      {isCollapsed ? (
        // 折叠状态下只显示第一个和最后一个工具调用
        <>
          {/* 渲染第一个工具调用，但不再渲染其reasoning（已在上面渲染） */}
          {(() => {
            const FirstToolComponent = firstTool.toolName in toolComponentMap
              ? toolComponentMap[firstTool.toolName as keyof typeof toolComponentMap]
              : null;

            return FirstToolComponent ? <FirstToolComponent toolInvocation={firstTool} /> : null;
          })()}

          {toolInvocations.length > 2 && (
            <div className="text-center py-1 text-xs text-gray-500">
              ... {toolInvocations.length - 2} more tool calls collapsed ...
            </div>
          )}

          {/* 如果有多个工具调用，渲染最后一个 */}
          {toolInvocations.length > 1 && (() => {
            const LastToolComponent = lastTool.toolName in toolComponentMap
              ? toolComponentMap[lastTool.toolName as keyof typeof toolComponentMap]
              : null;

            const lastIndex = lastTool._partIndex;
            const lastReasoning = reasoningMap?.get(lastIndex);

            return (
              <>
                {lastReasoning}
                {LastToolComponent ? <LastToolComponent toolInvocation={lastTool} /> : null}
              </>
            );
          })()}
        </>
      ) : (
        // 展开状态下显示所有工具调用
        <>
          {toolInvocations.map((tool, index) => {
            const ToolComponent = tool.toolName in toolComponentMap
              ? toolComponentMap[tool.toolName as keyof typeof toolComponentMap]
              : null;

            const partIndex = tool._partIndex;
            // 对于第一个工具调用，不再重复渲染reasoning（已在上面渲染）
            const reasoning = index === 0 ? null : reasoningMap?.get(partIndex);

            return (
              <div key={index} className={index > 0 ? "mt-2" : ""}>
                {reasoning}
                {ToolComponent ? <ToolComponent toolInvocation={tool} /> : null}
              </div>
            );
          })}
        </>
      )}
    </div>
  );
}

// 修改分组逻辑，将所有连续工具调用分到一组
function groupToolInvocations(toolInvocations: any[]) {
  if (!toolInvocations || toolInvocations.length <= 1) return [toolInvocations];

  // 简化分组逻辑：所有连续工具调用都算一组
  // 如果需要保留更细致的分组，取消这一行的注释并删除下面的 return
  return [toolInvocations];

  /*
  const groups: any[][] = [];
  let currentGroup: any[] = [toolInvocations[0]];
  let currentToolName = toolInvocations[0].toolName;

  for (let i = 1; i < toolInvocations.length; i++) {
    const tool = toolInvocations[i];
    const isSameToolType = tool.toolName === currentToolName ||
                           (tool.toolName.includes('wms_api') && currentToolName.includes('wms_api')) ||
                           (tool.toolName.includes('sequentialthinking') && currentToolName.includes('sequentialthinking'));

    if (isSameToolType) {
      // 如果是相同类型的工具，添加到当前组
      currentGroup.push(tool);
    } else {
      // 如果是不同类型的工具，结束当前组并开始一个新组
      groups.push([...currentGroup]);
      currentGroup = [tool];
      currentToolName = tool.toolName;
    }
  }

  // 添加最后一个组
  if (currentGroup.length > 0) {
    groups.push(currentGroup);
  }

  return groups;
  */
}

export default React.memo(ChatMessage, (prevProps, nextProps) => {
  // 如果消息ID不同，需要重新渲染
  if (prevProps.message.id !== nextProps.message.id) {
    return false;
  }
  
  // 如果消息内容变化了，需要重新渲染
  if (JSON.stringify(prevProps.message) !== JSON.stringify(nextProps.message)) {
    return false;
  }
  
  // 如果其他props变化了，需要重新渲染
  if (prevProps.modelName !== nextProps.modelName ||
      prevProps.onUserInputSubmit !== nextProps.onUserInputSubmit ||
      prevProps.onUserInputCancel !== nextProps.onUserInputCancel) {
    return false;
  }
  
  return true;
});