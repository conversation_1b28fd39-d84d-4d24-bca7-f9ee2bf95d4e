import type { UserContext, SessionContext, WmsUserInfo, Facility } from '@/utils/clientUserContext';

export interface IClientUserContextManager {
  getAuthToken(): string | null;
  setAuthToken(token: string): void;
  clearAuthToken(): void;
  getAuthHeaders(): Record<string, string>;
  initUserSession(userId: string, username: string, email?: string, tenant?: string, facility?: string): Promise<UserContext>;
  addWmsUserInfo(userId: string, wmsUserInfo: WmsUserInfo): Promise<boolean>;
  setCurrentFacility(userId: string, facilityId: string): boolean;
  getUserFacilities(userId: string): Facility[];
  getCurrentFacility(userId: string): Facility | undefined;
  getUserContext(userId: string): Promise<UserContext | null>;
  updateSessionContext(userId: string, updates: Partial<SessionContext>): SessionContext | undefined;
  clearUserSession(userId: string): void;
  getUserSession(userId: string): SessionContext | undefined;
  updateWmsUserInfo(userId: string, wmsUserInfo: WmsUserInfo): boolean;
  getWmsUserInfo(): any | null;
  getWmsUserInfoByUserId(userId: string): WmsUserInfo | null;
  getCurrentTenantId(): string | null;
  getCurrentFacilityId(): string | null;
  getIAMUserInfo(): any | null;
  getUserRoleNames(): string[];
  getBiToken(): string | null;
} 