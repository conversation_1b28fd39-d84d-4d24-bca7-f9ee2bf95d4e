/**
 * @file 服务端Token工具
 * @description 用于服务端Token生成和验证的核心工具函数
 * @server-side
 */

import JSEncrypt from 'jsencrypt';
import { TokenPayload } from '../types/site';
import { getSiteConfig } from './site-config';

// Token有效期（毫秒）
const TOKEN_EXPIRY = process.env.TOKEN_EXPIRY ? parseInt(process.env.TOKEN_EXPIRY) : 5 * 60 * 1000; // 5分钟

// 生成Token
export function generateToken(payload: TokenPayload, publicKey: string): string {
  const encrypt = new JSEncrypt();
  encrypt.setPublicKey(publicKey);
  
  const tokenData = {
    ...payload,
    timestamp: Date.now()
  };
  
  const encrypted = encrypt.encrypt(JSON.stringify(tokenData));
  if (!encrypted) {
    throw new Error('Encryption failed');
  }
  
  return encrypted;
}

// 验证Token
export function verifyEmbedToken(token: string, siteId: string): TokenPayload | null {
  try {
    const siteConfig = getSiteConfig(siteId);
    if (!siteConfig) {
      return null;
    }

    const decrypt = new JSEncrypt();
    decrypt.setPrivateKey(siteConfig.privateKey);
    
    const decrypted = decrypt.decrypt(token);
    if (!decrypted) {
      return null;
    }
    
    const payload: TokenPayload = JSON.parse(decrypted);
    
    // 验证时间戳
    if (Date.now() - payload.timestamp > TOKEN_EXPIRY) {
      return null;
    }
    
    // 验证站点ID
    if (payload.siteId !== siteId) {
      return null;
    }
    
    return payload;
  } catch (error) {
    console.error('verifyEmbedToken error', error);
    return null;
  }
}
