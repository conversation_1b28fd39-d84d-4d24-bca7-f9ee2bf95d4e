import asyncio
import json
import logging
import sys
import traceback
from typing import Dict, Any, Optional
import os

try:
    from browser_use import Agent, Controller
    from browser_use.browser.context import Browser<PERSON>ontext, BrowserContextConfig
    from browser_use import Browser
    from browser_use.agent.views import ActionResult
    from langchain_openai import ChatOpenAI
    from langchain_anthropic import ChatAnthropic
except ImportError as e:
    print(f"Failed to import required modules: {e}")
    sys.exit(1)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
)
logger = logging.getLogger("browser_tools")

# Add parent directory to path to import config
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

# Import config
from config import OPENAI_API_KEY, BROWSER_HEADLESS, ANTHROPIC_API_KEY, LLM_MODEL

class BrowserTools:
    """Browser tools using browser-use library for web navigation and task execution."""
    
    def __init__(self):
        """Initialize BrowserTools with necessary controllers and configuration."""
        # 根据模型类型选择对应的客户端
        if LLM_MODEL.startswith("gpt"):
            self.llm = ChatOpenAI(
                model=LLM_MODEL,
                api_key=OPENAI_API_KEY,
                temperature=0.6
            )
            logger.info(f"使用 OpenAI 模型: {LLM_MODEL}")
        else:
            self.llm = ChatAnthropic(
                model=LLM_MODEL,
                anthropic_api_key=ANTHROPIC_API_KEY,
                temperature=0.6
            )
            logger.info(f"使用 Anthropic 模型: {LLM_MODEL}")
        
        logger.info("BrowserTools initialized successfully")
    
    async def perform_web_task(self, task_description: str) -> Dict[str, Any]:
        """
        Perform a web task using browser-use.
        
        Args:
            task_description: Detailed description of the web task to perform
            
        Returns:
            Dictionary with task results
        """
        try:
            # Create a general controller
            task_controller = Controller()
           
            config = BrowserContextConfig(
            viewport_expansion=-1  # -1表示不限制视口范围，识别所有元素
            )

            browser = Browser()
            context = BrowserContext(browser=browser, config=config)
            logger.info(f"Performing web task: {task_description}")

            # Define message context
            message_context = """When dropdown options appear, you need to click on the dropdown to select options. Sometimes date picker input fields are readonly - in these cases, you need to click on the input field to trigger the date picker dialog for selection.

Do not repeatedly try the same action. If you've attempted an action more than 2 times with the same result, stop repeating that action and try a different approach."""

            # Initialize Agent with message_context
            agent = Agent(
                task=task_description, 
                llm=self.llm,
                message_context=message_context,  
                browser_context=context,
                include_attributes=[
                    'title',
                    'type',
                    'name',
                    'role',
                    'tabindex',
                    'aria-label',
                    'placeholder',
                    'tabindex',
                    'value',
                    'alt',
                    'readonly',
                    'u_remark',
                    'aria-expanded',
                    'data-testid',     # 添加更多有用的属性
                    'data-cy',
                    'data-qa',
                    'class',
                    'id',
                    'href',
                    'src',
                    'disabled',
                    'required',
                    'aria-disabled',
                    'aria-hidden',
                    'aria-selected',
                    'aria-checked',
                    'aria-pressed',
                ],  # 重试延迟（秒）
                use_vision=True,       # 容中包含工具调用
                max_actions_per_step=3,    # 每步最大动作数
            )
            
            history = await agent.run()
            
            # 打印history的结构以便调试
            logger.info(f"History type: {type(history)}")
            logger.info(f"History attributes: {dir(history)}")
            
            # Format the steps taken
            steps = []
            if hasattr(history, 'steps'):
                for step in history.steps:
                    if hasattr(step, 'messages') and step.messages:
                        for msg in step.messages:
                            if hasattr(msg, 'content'):
                                steps.append(f"{msg.content}")
            
            # 使用 AgentHistoryList 的内置方法提取结果
            final_answer = "No result"
            
            # 1. 首先尝试使用 final_result() 方法获取最终结果
            if hasattr(history, 'final_result'):
                result = history.final_result()
                if result:
                    final_answer = result
                    logger.info(f"从 final_result 找到结果: {final_answer}")
            
            # 2. 如果没有最终结果，检查所有提取的内容
            if final_answer == "No result" and hasattr(history, 'extracted_content'):
                contents = history.extracted_content()
                if contents and contents[-1]:  # 获取最后一个提取的内容
                    final_answer = contents[-1]
                    logger.info(f"从 extracted_content 找到结果: {final_answer}")
            
            # 3. 如果还没有结果，检查所有操作结果
            if final_answer == "No result" and hasattr(history, 'action_results'):
                results = history.action_results()
                for result in reversed(results):  # 从最后一个结果开始检查
                    if result.is_done and result.extracted_content:
                        final_answer = result.extracted_content
                        logger.info(f"从 action_results 找到结果: {final_answer}")
                        break
            
            # 4. 如果还是没有结果，检查 done 操作
            if final_answer == "No result" and hasattr(history, 'model_actions'):
                actions = history.model_actions()
                for action in reversed(actions):
                    if isinstance(action, dict) and 'done' in action:
                        done_action = action['done']
                        if isinstance(done_action, dict) and 'text' in done_action:
                            final_answer = done_action['text']
                            logger.info(f"从 model_actions 找到结果: {final_answer}")
                            break
            
            logger.info(f"Final answer: {final_answer}")
            
            # 检查任务是否成功完成
            is_done = hasattr(history, 'is_done') and history.is_done()
            
            return {
                "task": task_description,
                "result": final_answer,
                "steps": steps,
                "success": is_done
            }
                
        except Exception as e:
            logger.error(f"Error performing web task: {e}")
            traceback.print_exc()
            return {
                "error": str(e),
                "success": False,
                "task": task_description
            } 

# 测试入口
async def main():
    """测试入口，直接测试浏览器任务"""
    import os
    from dotenv import load_dotenv
    
    # 加载环境变量
    load_dotenv()
    
    # 设置日志级别为DEBUG以查看更多信息
    logging.getLogger("browser_tools").setLevel(logging.DEBUG)
    
    # 创建工具类实例
    tools = BrowserTools()
    
    # 定义测试任务
    test_task = "告诉我后天厦门到北京最便宜的机票"
    
    print(f"\n\n======== 开始测试: '{test_task}' ========\n")
    print(f"使用模型: gpt-4o, API Key: {OPENAI_API_KEY[:5]}...{OPENAI_API_KEY[-5:] if len(OPENAI_API_KEY) > 10 else '***'}")
    print(f"浏览器无头模式: {BROWSER_HEADLESS}")
    
    try:
        # 设置更长的超时
        print("开始执行任务，超时时间600秒...")
        import asyncio
        result = await asyncio.wait_for(
            tools.perform_web_task(task_description=test_task),
            timeout=600  # 给予更长的超时时间(10分钟)
        )
        
        # 输出结果
        print("\n======== 任务结果 ========")
        print(f"成功: {result['success']}")
        
        # 安全访问结果字段
        if 'result' in result:
            print(f"结果: {result['result']}")
        elif 'error' in result:
            print(f"错误: {result['error']}")
        else:
            print("结果: 无法获取结果")
            
        print("\n======== 执行步骤 ========")
        for i, step in enumerate(result.get('steps', []), 1):
            print(f"步骤 {i}: {step}")
        
    except Exception as e:
        print(f"\n======== 执行失败 ========")
        print(f"错误: {str(e)}")
        traceback.print_exc()

# 直接执行脚本时运行测试
if __name__ == "__main__":
    import asyncio
    
    # 设置更详细的日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    )
    
    print("启动浏览器任务测试...")
    asyncio.run(main())
        
        