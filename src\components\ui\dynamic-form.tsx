'use client';

import * as React from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { DatePicker } from '@/components/ui/date-picker';
import { DateTimePicker } from '@/components/ui/datetime-picker';
import { TimePicker } from '@/components/ui/time-picker';
import { CustomSelect } from '@/components/ui/custom-select';
import { UserSelector } from '@/components/ui/wms/user-selector';
import { MultipleUserSelector } from '@/components/ui/wms/multiple-user-selector';
import { CustomerSelector } from '@/components/ui/wms/customer-selector';
import { TitleSelector } from '@/components/ui/wms/title-selector';
import { GeneralProjectSelector } from '@/components/ui/wms/general-project-selector';
import { JobCodeSelector } from '@/components/ui/wms/jobcode-selector';
import { CarrierSelector } from '@/components/ui/wms/carrier-selector';
import { DeliveryServiceSelector } from '@/components/ui/wms/delivery-service-selector';
import { ItemMasterSelector } from '@/components/ui/wms/itemmaster-selector';
import { ItemMasterUomSelector } from '@/components/ui/wms/itemmaster-uom-selector';
import { StateSelector } from '@/components/ui/wms/state-selector';
import { ArrayField } from '@/components/ui/array-field';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { TemplateFieldType } from '@/tools/types';
import { Switch } from '@/components/ui/switch';
import { AddressInput } from '@/components/ui/address-input';
import { CustomRadioGroup } from '@/components/ui/radio-group';
import { CustomerSelector as PortalCustomerSelector } from '@/components/ui/portal/customer-selector';
import { InvoiceSelector as PortalInvoiceSelector } from '@/components/ui/portal/invoice-selector';
import { AccessorialMultipleSelector } from '@/components/ui/portal/accessorial-multiple-selector';
import { format } from 'date-fns';

interface TemplateField {
  name: string;
  label: string;
  type: string;
  required?: boolean;
  description?: string;
  options?: { label: string; value: string }[];
  apiUrl?: string;
  apiPath?: string;
  apiHeaders?: Record<string, string>;
  dependsOn?: string; // Field name that this field depends on
  defaultValue?: any;
  arrayItemFields?: TemplateField[]; // Fields for array items when type is ARRAY
  readonly?: boolean; // If true, this field is read-only and cannot be edited by the user
}

interface DynamicFormProps {
  fields: TemplateField[];
  onSubmit: (data: any) => void;
  title?: string;
  description?: string;
  submitText?: string;
  cancelText?: string;
  onCancel?: () => void;
}

// 稳定的表单组件，使用 React.memo 优化重新渲染
const StableDynamicForm = React.memo(function StableDynamicForm({
  fields,
  onSubmit,
  title,
  description,
  submitText = 'Submit',
  cancelText = 'Cancel',
  onCancel,
}: DynamicFormProps) {
  const [formValues, setFormValues] = React.useState<Record<string, any>>({});
  const [formErrors, setFormErrors] = React.useState<Record<string, string>>({});
  // 添加标签映射状态，用于存储字段ID到标签的映射
  const [labelMappings, setLabelMappings] = React.useState<Record<string, any>>({});

  // 添加表单稳定性标记，防止不必要的重新初始化
  const [isInitialized, setIsInitialized] = React.useState(false);
  

  // 创建初始默认值 - 优化：只在首次初始化或字段配置真正变化时执行
  React.useEffect(() => {
    // 如果已经初始化且表单有值，不要重新初始化（防止用户输入丢失）
    if (isInitialized && Object.keys(formValues).length > 0) {
      return;
    }

    const initialValues: Record<string, any> = {};
    fields.forEach((field) => {
      if (field.defaultValue !== undefined) {
        // 处理日期和日期时间类型的默认值，将字符串转换为 Date 对象
        if (field.type === TemplateFieldType.DATE || field.type === TemplateFieldType.DATETIME || field.type === TemplateFieldType.TIME) {
          try {
            if (field.defaultValue instanceof Date) {
              // Date对象转字符串
              let str = '';
              if (field.type === TemplateFieldType.DATE) {
                str = format(field.defaultValue, 'yyyy-MM-dd');
              } else if (field.type === TemplateFieldType.DATETIME) {
                str = format(field.defaultValue, 'yyyy-MM-dd HH:mm:ss');
              } else if (field.type === TemplateFieldType.TIME) {
                str = format(field.defaultValue, 'HH:mm:ss');
              }
              initialValues[field.name] = str;
            } else if (typeof field.defaultValue === 'string') {
              // 已经是字符串，直接赋值
              initialValues[field.name] = field.defaultValue;
            } else {
              initialValues[field.name] = undefined;
            }
          } catch (e) {
            console.error(`Error converting ${field.type} default value:`, e);
            initialValues[field.name] = undefined;
          }
        } else if (field.type === TemplateFieldType.ADDRESS) {
          // 处理地址类型的默认值，确保是对象
          initialValues[field.name] = field.defaultValue && typeof field.defaultValue === 'object' 
            ? field.defaultValue 
            : {};
        } else {
          // 其他类型直接赋值
          initialValues[field.name] = field.defaultValue;
        }
      } else {
        switch (field.type) {
          case TemplateFieldType.CHECKBOX:
            initialValues[field.name] = false;
            break;
          case TemplateFieldType.DATE:
          case TemplateFieldType.DATETIME:
          case TemplateFieldType.TIME:
            initialValues[field.name] = undefined;
            break;
          case TemplateFieldType.ADDRESS:
            initialValues[field.name] = {};
            break;
          case TemplateFieldType.ACCESSORIAL_MULTIPLE_SELECTOR:
          case TemplateFieldType.MULTIPLE_USER_SELECTOR:
            initialValues[field.name] = [];
            break;
          default:
            initialValues[field.name] = '';
        }
      }
    });

    setFormValues(initialValues);
    setIsInitialized(true);
  }, [fields, isInitialized, formValues]); // 优化依赖项，避免不必要的重新初始化

  // 处理字段值变化
  const handleFieldChange = (name: string, value: any, itemData?: any) => {
    setFormValues((prev) => {
      const newValues = {
        ...prev,
        [name]: value
      };

      // 检查是否有依赖于此字段的其他字段，如果有，则重置它们的值
      fields.forEach(field => {
        if (field.dependsOn === name) {
          // 重置依赖字段的值
          newValues[field.name] = '';
        }
      });

      // 如果是客户ID字段，添加特殊处理
      if (name === "taskCmd.customerId" || name === "customerId" || name.endsWith(".customerId")) {
        // 为了确保数组项中的 JobCodeSelector 可以访问到客户ID，我们添加一个额外的字段
        if (name.includes(".")) {
          // 如果是嵌套字段，也添加一个简单的 customerId 字段
          newValues.customerId = value;
        } else if (name === "customerId") {
          // 如果已经是 customerId，确保 taskCmd.customerId 也存在
          if (!newValues.taskCmd) newValues.taskCmd = {};
          newValues.taskCmd.customerId = value;
        }
      }

      return newValues;
    });

    // 如果提供了项目数据（如用户对象、承运商对象等），保存到标签映射中
    if (itemData) {
      // 对于多选组件（如 MultipleUserSelector），itemData 是数组
      if (Array.isArray(itemData)) {
        setLabelMappings(prev => ({
          ...prev,
          [name]: itemData.map(item => ({
            value: item.id || item.value,
            label: item.name || item.label || String(item.id || item.value),
            data: item
          }))
        }));
      } else {
        // 对于单选组件
        setLabelMappings(prev => ({
          ...prev,
          [name]: {
            value: value,
            label: itemData.name || itemData.label || String(value),
            data: itemData
          }
        }));
      }
    }

    // 如果字段有值且有错误，清除该错误
    if (
      (value !== undefined && value !== null && value !== '') ||
      (typeof value === 'string' && value.trim() !== '')
    ) {
      setFormErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  // 表单提交处理
  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    // 首先尝试使用浏览器的原生表单验证
    const form = e.currentTarget;
    const isNativeValid = form.checkValidity();

    // 如果原生验证通过，我们只需要验证自定义组件
    if (isNativeValid) {
      e.preventDefault(); // 阻止默认提交

      // 只验证自定义组件（它们不支持原生required属性）
      const newErrors: Record<string, string> = {};

      fields.forEach((field) => {
        if (field.required) {
          const value = formValues[field.name];

          // 只验证自定义组件类型
          if (
            (field.type === TemplateFieldType.USER_SELECTOR ||
            field.type === TemplateFieldType.CUSTOMER_SELECTOR ||
            field.type === TemplateFieldType.TITLE_SELECTOR ||
            field.type === TemplateFieldType.GENERAL_PROJECT_SELECTOR ||
            field.type === TemplateFieldType.JOBCODE_SELECTOR ||
            field.type === TemplateFieldType.STATE_SELECTOR ||
            field.type === TemplateFieldType.SELECT ||
            field.type === TemplateFieldType.DATE ||
            field.type === TemplateFieldType.DATETIME ||
            field.type === TemplateFieldType.TIME ||
            field.type === TemplateFieldType.PORTAL_CUSTOMER_SELECTOR ||
            field.type === TemplateFieldType.PORTAL_INVOICE_SELECTOR ||
            field.type === TemplateFieldType.MULTIPLE_USER_SELECTOR) &&
            (value === undefined || value === null || value === '' ||
             (typeof value === 'string' && value.trim() === ''))
          ) {
            newErrors[field.name] = `${field.label} is required`;
          }

          // 验证地址类型
          if (field.type === TemplateFieldType.ADDRESS) {
            if (!value || typeof value !== 'object') {
              newErrors[field.name] = `${field.label} is required`;
            } else {
              // 检查地址对象的必填字段
              const addressValue = value as any;
              const requiredAddressFields = ['name', 'street', 'city', 'state', 'zip'];
              const missingFields = requiredAddressFields.filter(fieldName => 
                !addressValue[fieldName] || (typeof addressValue[fieldName] === 'string' && addressValue[fieldName].trim() === '')
              );
              
              if (missingFields.length > 0) {
                newErrors[field.name] = `Please complete all required fields in ${field.label}: ${missingFields.join(', ')}`;
              }
            }
          }

          // 对于复选框，如果是必填的，则必须选中
          if (field.type === TemplateFieldType.CHECKBOX && field.required && value !== true) {
            newErrors[field.name] = `${field.label} must be checked`;
          }

          // 验证多选用户类型
          if (field.type === TemplateFieldType.MULTIPLE_USER_SELECTOR) {
            if (!Array.isArray(value) || value.length === 0) {
              newErrors[field.name] = `Please select at least one ${field.label}`;
            }
          }

          // 验证数组类型
          if (field.type === TemplateFieldType.ARRAY) {
            // 检查数组是否为空
            if (!Array.isArray(value) || value.length === 0) {
              newErrors[field.name] = `Please add at least one ${field.label}`;
            } else if (field.arrayItemFields) {
              // 检查数组中的每个项目的必填字段
              value.forEach((item, index) => {
                field.arrayItemFields?.forEach(itemField => {
                  if (itemField.required) {
                    const itemValue = item[itemField.name];
                    if (itemValue === undefined || itemValue === null || itemValue === '' ||
                       (typeof itemValue === 'string' && itemValue.trim() === '')) {
                      newErrors[`${field.name}[${index}].${itemField.name}`] =
                        `Please enter ${itemField.label} for ${field.label} #${index + 1}`;
                    }
                  }
                });
              });
            }
          }
        }
      });

      setFormErrors(newErrors);

      // 如果自定义组件验证也通过，提交表单
      if (Object.keys(newErrors).length === 0) {
        // 将标签映射添加到提交数据中
        onSubmit({
          ...formValues,
          __labelMappings: labelMappings // 添加标签映射作为元数据
        });
      }
    } else {
      e.preventDefault(); // 阻止默认提交，让原生错误提示显示
      // 触发浏览器显示错误信息
      const invalidFields = form.querySelectorAll(':invalid');
      if (invalidFields.length > 0) {
        (invalidFields[0] as HTMLElement).focus();
      }
    }
  };

  return (
    <div className="item-card bg-item-bg-card rounded-lg border border-item-purple/30 p-5 shadow-lg animate-fade-in">
      {title && <h3 className="text-lg font-semibold mb-3 text-white">{title}</h3>}
      {description && <p className="mb-4 text-item-gray-300 text-sm">{description}</p>}

      <form onSubmit={handleSubmit} className="space-y-4">
        {fields.map((field) => (
          <div key={field.name} className="space-y-1.5">
            {field.type !== TemplateFieldType.CHECKBOX && (
              <label className="text-item-gray-300 block">
                {field.label}
                {field.required && <span className="text-item-orange ml-1">*</span>}
              </label>
            )}

            {(() => {
              switch (field.type) {
                case TemplateFieldType.TEXT:
                  return (
                    <Input
                      value={formValues[field.name] || ''}
                      onChange={(e) => handleFieldChange(field.name, e.target.value)}
                      className="bg-item-bg-card border-item-gray-700 text-white focus:ring-1 focus:ring-item-purple focus:border-item-purple transition"
                      required={field.required}
                      readOnly={field.readonly}
                    />
                  );
                case TemplateFieldType.TEXTAREA:
                  return (
                    <textarea
                      value={formValues[field.name] || ''}
                      onChange={(e) => handleFieldChange(field.name, e.target.value)}
                      className="w-full bg-item-bg-card border border-item-gray-700 rounded-md p-2 text-white focus:ring-1 focus:ring-item-purple focus:border-item-purple transition"
                      rows={4}
                      required={field.required}
                      readOnly={field.readonly}
                    />
                  );
                case TemplateFieldType.NUMBER:
                  return (
                    <Input
                      type="number"
                      value={formValues[field.name] || ''}
                      onChange={(e) => handleFieldChange(field.name, e.target.value)}
                      className="bg-item-bg-card border-item-gray-700 text-white focus:ring-1 focus:ring-item-purple focus:border-item-purple transition"
                      required={field.required}
                      readOnly={field.readonly}
                    />
                  );
                case TemplateFieldType.SELECT:
                  return (
                    <CustomSelect
                      value={formValues[field.name] !== undefined ? formValues[field.name] : ''}
                      onChange={(value) => handleFieldChange(field.name, value)}
                      options={field.options || []}
                      placeholder={`Select ${field.label}`}
                    />
                  );
                case TemplateFieldType.USER_SELECTOR:
                  return (
                    <UserSelector
                      value={formValues[field.name] || ''}
                      onChange={(value, userData) => handleFieldChange(field.name, value, userData)}
                      placeholder={`Select ${field.label}`}
                      apiHeaders={field.apiHeaders}
                      required={field.required}
                      disabled={field.readonly}
                    />
                  );
                case TemplateFieldType.CUSTOMER_SELECTOR:
                  return (
                    <CustomerSelector
                      value={formValues[field.name] || ''}
                      onChange={(value, customerData) => handleFieldChange(field.name, value, customerData)}
                      placeholder={`Select ${field.label}`}
                      apiHeaders={field.apiHeaders}
                      required={field.required}
                      disabled={field.readonly}
                    />
                  );
                case TemplateFieldType.TITLE_SELECTOR:
                  return (
                    <TitleSelector
                      value={formValues[field.name] || ''}
                      onChange={(value, titleData) => handleFieldChange(field.name, value, titleData)}
                      placeholder={`Select ${field.label}`}
                      apiHeaders={field.apiHeaders}
                      required={field.required}
                      disabled={field.readonly}
                    />
                  );
                case TemplateFieldType.GENERAL_PROJECT_SELECTOR:
                  return (
                    <GeneralProjectSelector
                      value={formValues[field.name] || ''}
                      onChange={(value, projectData) => handleFieldChange(field.name, value, projectData)}
                      placeholder={`Select ${field.label}`}
                      apiHeaders={field.apiHeaders}
                      required={field.required}
                      disabled={field.readonly}
                    />
                  );
                case TemplateFieldType.JOBCODE_SELECTOR:
                  return (
                    <JobCodeSelector
                      value={formValues[field.name] || ''}
                      onChange={(value, jobcodeData) => handleFieldChange(field.name, value, jobcodeData)}
                      placeholder={`Select ${field.label}`}
                      customerId={field.dependsOn ? formValues[field.dependsOn] : undefined}
                      apiHeaders={field.apiHeaders}
                      required={field.required}
                      disabled={field.readonly}
                    />
                  );
                case TemplateFieldType.CARRIER_SELECTOR:
                  return (
                    <CarrierSelector
                      value={formValues[field.name] || ''}
                      onChange={(value, carrierData) => handleFieldChange(field.name, value, carrierData)}
                      placeholder={`Select ${field.label}`}
                      apiHeaders={field.apiHeaders}
                      required={field.required}
                      defaultValue={field.defaultValue}
                      disabled={field.readonly}
                    />
                  );
                case TemplateFieldType.DELIVERY_SERVICE_SELECTOR:
                  return (
                    <DeliveryServiceSelector
                      value={formValues[field.name] || ''}
                      onChange={(value, serviceData) => handleFieldChange(field.name, value, serviceData)}
                      carrierId={field.dependsOn ? formValues[field.dependsOn] : undefined}
                      placeholder={`Select ${field.label}`}
                      apiHeaders={field.apiHeaders}
                      required={field.required}
                      defaultValue={field.defaultValue}
                      disabled={field.readonly}
                    />
                  );
                case TemplateFieldType.ITEMMASTER_SELECTOR:
                  return (
                    <ItemMasterSelector
                      value={formValues[field.name] || ''}
                      onChange={(value, itemData) => handleFieldChange(field.name, value, itemData)}
                      customerId={field.dependsOn ? formValues[field.dependsOn] : undefined}
                      placeholder={`Select ${field.label}`}
                      apiHeaders={field.apiHeaders}
                      required={field.required}
                      defaultValue={field.defaultValue}
                      disabled={field.readonly}
                    />
                  );
                case TemplateFieldType.ITEMMASTER_UOM_SELECTOR:
                  return (
                    <ItemMasterUomSelector
                      value={formValues[field.name] || ''}
                      onChange={(value, uomData) => handleFieldChange(field.name, value, uomData)}
                      itemId={field.dependsOn ? formValues[field.dependsOn] : undefined}
                      placeholder={`Select ${field.label}`}
                      apiHeaders={field.apiHeaders}
                      required={field.required}
                      defaultValue={field.defaultValue}
                      disabled={field.readonly}
                    />
                  );
                case TemplateFieldType.STATE_SELECTOR:
                  return (
                    <StateSelector
                      value={formValues[field.name] || ''}
                      onChange={(value, stateData) => handleFieldChange(field.name, value, stateData)}
                      placeholder={`Select ${field.label}`}
                      required={field.required}
                      defaultValue={field.defaultValue}
                    />
                  );
                case TemplateFieldType.DATE:
                  return (
                    <DatePicker
                      date={formValues[field.name]}
                      setDate={(date) => handleFieldChange(field.name, date)}
                      placeholder={`Select ${field.label}`}
                    />
                  );
                case TemplateFieldType.DATETIME:
                  return (
                    <DateTimePicker
                      date={formValues[field.name]}
                      setDate={(date) => handleFieldChange(field.name, date)}
                      placeholder={`Select ${field.label}`}
                    />
                  );
                case TemplateFieldType.TIME:
                  return (
                    <TimePicker
                      time={formValues[field.name]}
                      setTime={(time) => handleFieldChange(field.name, time)}
                      placeholder={`Select ${field.label}`}
                    />
                  );
                case TemplateFieldType.CHECKBOX:
                  return (
                    <div className="flex items-center space-x-2 py-1">
                      <Checkbox
                        id={field.name}
                        checked={!!formValues[field.name]}
                        onCheckedChange={(checked) =>
                          handleFieldChange(field.name, checked === true)
                        }
                        className="bg-item-bg-card border-item-gray-700 data-[state=checked]:bg-item-purple"
                        disabled={field.readonly}
                      />
                      <Label
                        htmlFor={field.name}
                        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 text-item-gray-300"
                      >
                        {field.label}
                        {field.required && <span className="text-item-orange ml-1">*</span>}
                      </Label>
                    </div>
                  );
                case TemplateFieldType.ARRAY:
                  return (
                    <ArrayField
                      field={field}
                      value={formValues[field.name] || []}
                      onChange={(value) => handleFieldChange(field.name, value)}
                      className="mt-1"
                      parentFormValues={formValues} // 传递父表单的所有值
                    />
                  );
                case TemplateFieldType.SWITCH:
                  return (
                    <div className="flex items-center justify-between space-x-2 py-1">
                      <div className="space-y-0.5">
                        <Label
                          htmlFor={field.name}
                          className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 text-item-gray-300"
                        >
                          {field.label}
                          {field.required && <span className="text-item-orange ml-1">*</span>}
                        </Label>
                        {field.description && (
                          <p className="text-item-gray-400 text-xs">{field.description}</p>
                        )}
                      </div>
                      <Switch
                        id={field.name}
                        checked={!!formValues[field.name]}
                        onCheckedChange={(checked) => handleFieldChange(field.name, checked === true)}
                        disabled={field.readonly}
                      />
                    </div>
                  );
                case TemplateFieldType.ADDRESS:
                  return (
                    <AddressInput
                      value={formValues[field.name] || {}}
                      onChange={(value) => handleFieldChange(field.name, value)}
                      required={field.required}
                      label={field.label}
                    />
                  );
                case TemplateFieldType.RADIO:
                  return (
                    <CustomRadioGroup
                      value={formValues[field.name] || ''}
                      onChange={(value) => handleFieldChange(field.name, value)}
                      options={field.options || []}
                      required={field.required}
                      className="space-y-2"
                      disabled={field.readonly}
                    />
                  );
                case TemplateFieldType.PORTAL_CUSTOMER_SELECTOR:
                  return (
                    <PortalCustomerSelector
                      value={formValues[field.name] || ''}
                      onChange={(value, customerData) => handleFieldChange(field.name, value, customerData)}
                      placeholder={`Select ${field.label}`}
                      apiHeaders={field.apiHeaders}
                      required={field.required}
                      defaultValue={field.defaultValue}
                      disabled={field.readonly}
                    />
                  );
                case TemplateFieldType.PORTAL_INVOICE_SELECTOR:
                  // 获取依赖的 customer 字段名和数据
                  const customerFieldName = field.dependsOn ? field.dependsOn : 'customerId';
                  const customerData = labelMappings[customerFieldName]?.data;
                  const customerCode = customerData?.code;
                  const customerName = customerData?.name;
                  return (
                    <PortalInvoiceSelector
                      value={formValues[field.name] || ''}
                      onChange={(value, invoiceData) => handleFieldChange(field.name, value, invoiceData)}
                      placeholder={`Select ${field.label}`}
                      customerId={field.dependsOn ? formValues[field.dependsOn] : undefined}
                      customerCode={customerCode}
                      customerName={customerName}
                      apiHeaders={field.apiHeaders}
                      required={field.required}
                      defaultValue={field.defaultValue}
                      disabled={field.readonly}
                    />
                  );
                case TemplateFieldType.ACCESSORIAL_MULTIPLE_SELECTOR:
                  return (
                    <AccessorialMultipleSelector
                      value={formValues[field.name] || []}
                      onChange={(value) => handleFieldChange(field.name, value)}
                      placeholder={`Select ${field.label}`}
                      required={field.required}
                      defaultValue={field.defaultValue}
                      disabled={field.readonly}
                    />
                  );
                case TemplateFieldType.MULTIPLE_USER_SELECTOR:
                  return (
                    <MultipleUserSelector
                      value={formValues[field.name] || []}
                      onChange={(value, userData) => handleFieldChange(field.name, value, userData)}
                      placeholder={`Select ${field.label}`}
                      apiHeaders={field.apiHeaders}
                      required={field.required}
                      defaultValue={field.defaultValue}
                      disabled={field.readonly}
                    />
                  );
                default:
                  return (
                    <Input
                      value={formValues[field.name] || ''}
                      onChange={(e) => handleFieldChange(field.name, e.target.value)}
                      className="bg-item-bg-card border-item-gray-700 text-white focus:ring-1 focus:ring-item-purple focus:border-item-purple transition"
                      readOnly={field.readonly}
                    />
                  );
              }
            })()}

            {field.description && (
              <p className="text-item-gray-400 text-sm">{field.description}</p>
            )}

            {formErrors[field.name] && (
              <p className="text-item-orange text-sm">{formErrors[field.name]}</p>
            )}
          </div>
        ))}

        <div className="flex justify-end space-x-3 pt-3 border-t border-item-gray-700/50">
          {onCancel && (
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              className="bg-item-bg-hover text-white hover:bg-item-bg-card border border-item-gray-700 hover:border-item-gray-600 transition-all duration-200"
            >
              {cancelText}
            </Button>
          )}
          <Button
            type="submit"
            className="bg-item-purple hover:bg-item-purple-dark text-white border-none transition-all duration-200 shadow-lg"
          >
            {submitText}
          </Button>
        </div>
      </form>
    </div>
  );
}, (prevProps, nextProps) => {
  // 自定义比较函数，只在关键属性变化时重新渲染
  return (
    JSON.stringify(prevProps.fields) === JSON.stringify(nextProps.fields) &&
    prevProps.title === nextProps.title &&
    prevProps.description === nextProps.description &&
    prevProps.submitText === nextProps.submitText &&
    prevProps.cancelText === nextProps.cancelText &&
    prevProps.onSubmit === nextProps.onSubmit &&
    prevProps.onCancel === nextProps.onCancel
  );
});

// 导出稳定的表单组件
export function DynamicForm(props: DynamicFormProps) {
  return <StableDynamicForm {...props} />;
}
