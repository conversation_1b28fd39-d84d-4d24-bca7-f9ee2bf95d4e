/* 覆盖 react-day-picker 默认样式 - Item Design System */

/* 使用 CSS 变量覆盖默认样式 */
.rdp-root {
  /* 主题颜色 - Item Purple */
  --rdp-accent-color: #8B5CF6 !important; /* item-purple */
  --rdp-accent-background-color: rgba(139, 92, 246, 0.2) !important;

  /* 日期尺寸 */
  --rdp-day-width: 40px !important;
  --rdp-day-height: 40px !important;

  /* 选中日期样式 - Item Orange border */
  --rdp-selected-border: 2px solid #F97316 !important; /* item-orange */

  /* 今天日期样式 - Item Purple */
  --rdp-today-color: #8B5CF6 !important; /* item-purple */
}

/* 选中日期样式 - 使用更强的选择器确保覆盖 */
.rdp-day_selected:not(.rdp-day_disabled):not(.rdp-day_outside) {
  background: linear-gradient(135deg, #8B5CF6, #F97316) !important; /* 紫色到橙色渐变 */
  color: white !important;
  font-weight: 700 !important; /* 加粗字体 */
  box-shadow: 0 4px 8px -1px rgba(139, 92, 246, 0.5), 0 2px 6px -1px rgba(249, 115, 22, 0.3) !important; /* 紫橙阴影 */
  border-radius: 9999px !important;
  transform: scale(1.1) !important; /* 稍微放大 */
  border: 2px solid #F97316 !important; /* 橙色边框 */
  position: relative !important;
  z-index: 10 !important; /* 确保在其他元素之上 */
}

/* 选中日期悬停样式 */
.rdp-day_selected:hover:not(.rdp-day_disabled):not(.rdp-day_outside) {
  background: linear-gradient(135deg, #7C3AED, #EA580C) !important; /* 更深的紫橙渐变 */
  color: white !important;
  border: 2px solid #FB923C !important; /* 更亮的橙色边框 */
}

/* 今天日期样式 */
.rdp-day_today:not(.rdp-day_selected) {
  border: 2px solid #8B5CF6 !important; /* 紫色边框 */
  color: #8B5CF6 !important;
  font-weight: 600 !important; /* 更粗的字体 */
  background-color: rgba(139, 92, 246, 0.15) !important; /* 淡紫色背景 */
  border-radius: 9999px !important; /* 圆形 */
}

/* 普通日期悬停样式 */
.rdp-day:hover:not(.rdp-day_selected):not(.rdp-day_disabled):not(.rdp-day_outside) {
  background-color: rgba(139, 92, 246, 0.2) !important;
  color: #e2e8f0 !important;
}

/* 导航按钮样式 */
.rdp-nav_button {
  color: #8B5CF6 !important;
}

.rdp-nav_button:hover {
  background-color: rgba(139, 92, 246, 0.2) !important;
  color: #e2e8f0 !important;
}

/* 月份标题样式 */
.rdp-caption_label {
  color: #8B5CF6 !important;
  font-weight: 500 !important;
}

/* 星期标题样式 */
.rdp-head_cell {
  color: #F97316 !important; /* 橙色星期标题 */
  font-weight: 500 !important;
}
