#!/usr/bin/env node

/**
 * Super Agent启动脚本
 * 设置环境变量并启动Super Agent协调器
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

// 为Super Agent创建独立的构建目录
const buildDir = '.next-super';
if (!fs.existsSync(buildDir)) {
  fs.mkdirSync(buildDir, { recursive: true });
}

// 设置Super Agent环境变量
const env = {
  ...process.env,
  AGENT_TYPE: 'super',
  AGENT_NAME: 'CyberBot Super Agent',
  AGENT_PORT: '3000',
  PORT: '3000', // 强制指定端口
  SUPER_AGENT_URL: 'http://localhost:3000',
  MCP_SERVERS: 'sequentialthinking', // 只加载通用工具
  // 使用独立的构建目录
  NEXT_BUILD_DIR: buildDir
};

console.log('🚀 Starting CyberBot Super Agent...');
console.log('🎯 Agent Type: SUPER (Coordinator)');
console.log('🌐 Port: 3000');
console.log('🔧 MCP Servers: sequentialthinking');
console.log('📁 Build Dir:', path.resolve(buildDir));
console.log('🤖 A2A Coordination: ENABLED');
console.log('');

// 启动Next.js开发服务器
const nextProcess = spawn('npm', ['run', 'dev'], {
  env,
  stdio: 'inherit',
  shell: true,
  cwd: path.resolve(__dirname, '..')
});

// 处理进程退出
nextProcess.on('close', (code) => {
  console.log(`\n🛑 Super Agent process exited with code ${code}`);
  process.exit(code);
});

// 处理Ctrl+C
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down Super Agent...');
  nextProcess.kill('SIGINT');
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Terminating Super Agent...');
  nextProcess.kill('SIGTERM');
}); 