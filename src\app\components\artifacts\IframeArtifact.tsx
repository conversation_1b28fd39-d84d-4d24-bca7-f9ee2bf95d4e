'use client';

import React from 'react';

interface IframeArtifactProps {
  src: string;
  width?: string | number;
  height?: string | number;
  style?: React.CSSProperties;
}

export function IframeArtifact({ src, width = '100%', height = '100%', style }: IframeArtifactProps) {
  return (
    <div className="w-full h-full">
      <iframe
        src={src}
        width={width}
        height={height}
        style={{
          width: '100%',
          ...style
        }}
        className="border-0 rounded-lg"
        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
        allowFullScreen
      />
    </div>
  );
}