import { Client } from "@modelcontextprotocol/sdk/client/index.js";
import { StdioClientTransport } from "@modelcontextprotocol/sdk/client/stdio.js";
import { SSEClientTransport } from "@modelcontextprotocol/sdk/client/sse.js";
import { tool } from 'ai';
import { z } from 'zod';
import fs from 'fs';
import path from 'path';
import { getRequestContext, extractRequestIdFromToolCallId } from '../utils/requestContext';

// 从mcp.json加载配置
export interface McpConfig {
  defaultServer: string;
  mcpServers: Record<string, { 
    url: string; 
    description: string;
    transport?: 'sse' | 'stdio';
    command?: string;
    args?: string[];
    timeout?: number;
    resetTimeoutOnProgress?: boolean;
    toolPrefix?: string;
  }>;
}

// 缓存工具列表，避免重复加载
const cachedMcpTools: Record<string, Record<string, any>> = {};
const cachedMcpToolsTimestamp: Record<string, number> = {};
// 默认缓存时间为28天，可通过环境变量配置
const CACHE_TTL = parseInt(process.env.MCP_CACHE_TTL || '2419200000'); // 28天

let mcpConfig: McpConfig = {
  defaultServer: "",
  mcpServers: {}
};

try {
  const configPath = path.resolve(process.cwd(), 'mcp.json');
  const configData = fs.readFileSync(configPath, 'utf-8');
  mcpConfig = JSON.parse(configData);
  console.log("[MCP] 已加载MCP配置:", Object.keys(mcpConfig.mcpServers));
} catch (error) {
  console.error("[MCP] Unable to load MCP config:", error);
}

// 缓存客户端实例
const clientCache: Record<string, Client> = {};

/**
 * 获取MCP服务器URL
 */
export function getMcpServerUrl(serverId: string): string {
  const server = mcpConfig.mcpServers[serverId];
  if (!server) {
    throw new Error(`MCP server not found: ${serverId}`);
  }
  return server.url;
}

/**
 * 获取当前活动的MCP服务器ID
 */
export function getActiveMcpServerId(): string {
  return mcpConfig.defaultServer;
}

/**
 * 获取所有MCP服务器ID
 */
export function getAllMcpServerIds(): string[] {
  return Object.keys(mcpConfig.mcpServers);
}

/**
 * 确定服务器传输类型
 */
function determineTransportType(serverId: string): 'sse' | 'stdio' {
  const server = mcpConfig.mcpServers[serverId];
  if (!server) {
    throw new Error(`MCP server not found: ${serverId}`);
  }
  
  // 如果指定了传输类型，使用指定的类型
  if (server.transport) {
    return server.transport as 'sse' | 'stdio';
  }
  
  // 如果指定了command，默认使用stdio
  if (server.command) {
    return 'stdio';
  }
  
  // 否则默认使用SSE
  return 'sse';
}

/**
 * 获取或创建MCP客户端
 */
async function getMcpClient(serverId?: string): Promise<Client> {
  const targetServerId = serverId || getActiveMcpServerId();
  
  if (clientCache[targetServerId]) {
    return clientCache[targetServerId];
  }
  
  try {
    console.log(`[MCP:${targetServerId}] 创建新客户端...`);
    
    const server = mcpConfig.mcpServers[targetServerId];
    if (!server) {
      throw new Error(`MCP server not found: ${targetServerId}`);
    }
    
    const serverUrl = server.url;
    
    // 创建新的客户端实例
    const client = new Client({
      name: "cyberbot-client",
      version: "1.0.0"
    });
    
    // 根据传输类型创建对应的传输对象
    const transportType = determineTransportType(targetServerId);
    let transport;
    
    if (transportType === 'stdio') {
      if (!server.command) {
        throw new Error(`MCP server ${targetServerId} is configured for stdio transport but no command specified`);
      }
      transport = new StdioClientTransport({
        command: server.command,
        args: server.args || []
      });
    } else if (transportType === 'sse') {
      transport = new SSEClientTransport(new URL(serverUrl));
    } else {
      throw new Error(`Unsupported transport type: ${transportType}`);
    }
    
    // 连接到服务器
    await client.connect(transport);
    console.log(`[MCP:${targetServerId}] 连接成功`);
    
    // 初始化完成
    console.log(`[MCP:${targetServerId}] 初始化成功`);
    
    // 缓存客户端
    clientCache[targetServerId] = client;
    return client;
  } catch (error) {
    console.error(`[MCP:${targetServerId}] 连接失败:`, error);
    throw error;
  }
}

/**
 * 处理工具调用结果
 */
function processToolResult(result: any) {
  if (!result) {
    return null;
  }
  
  try {
    // 如果结果是字符串，尝试解析JSON
    if (typeof result === 'string') {
      try {
        const parsed = JSON.parse(result);
        return parsed;
      } catch (e) {
        return result;
      }
    }
    
    // 如果结果有content字段且是数组
    if (result.content && Array.isArray(result.content) && result.content.length > 0) {
      const content = result.content[0];
      
      // 处理文本类型内容
      if (content.type === 'text' && content.text) {
        try {
          const parsed = JSON.parse(content.text);
          return parsed;
        } catch (e) {
          return content.text;
        }
      }
      
      return content;
    }
    
    // 返回原始结果
    return result;
  } catch (e) {
    console.error('[MCP] 处理结果时出错:', e);
    return result;
  }
}

/**
 * 创建Zod模式（从参数数组）
 */
function createZodSchema(parameters: any[] = []): z.ZodObject<any> {
  if (!parameters || !parameters.length) {
    return z.object({});
  }
  
  const props: Record<string, any> = {};
  
  for (const param of parameters) {
    if (!param || !param.name) {
      continue;
    }
    
    let schema;
    
    // 根据类型创建合适的Zod模式
    switch (param.schema?.type) {
      case 'string':
        schema = z.string();
        break;
      case 'integer':
      case 'number':
        schema = z.number();
        break;
      case 'boolean':
        schema = z.boolean();
        break;
      case 'array':
        schema = z.array(z.any());
        break;
      case 'object':
        schema = z.record(z.any());
        break;
      default:
        schema = z.any();
    }
    
    // 添加描述
    if (param.description) {
      schema = schema.describe(param.description);
    }
    
    // 非必填参数设为可选
    if (!param.required) {
      schema = schema.optional();
    }
    
    props[param.name] = schema;
  }
  
  const finalSchema = z.object(props);
  return finalSchema;
}

/**
 * 从JSON Schema创建Zod模式
 */
function createZodSchemaFromJsonSchema(jsonSchema: any): z.ZodObject<any> {
  if (!jsonSchema || typeof jsonSchema !== 'object') {
    return z.object({});
  }
  
  /**
   * 递归处理单个属性，保持嵌套结构
   */
  function processProperty(propSchema: any, propName: string): any {
    if (!propSchema || typeof propSchema !== 'object') {
      return z.any();
    }
    
    const propType = propSchema.type;
    const propDesc = propSchema.description || propSchema.title || propName;
    
    let schema;
    
    // 根据类型创建合适的Zod模式
    switch (propType) {
      case 'string':
        schema = z.string();
        break;
        
      case 'integer':
      case 'number':
        schema = z.number();
        break;
        
      case 'boolean':
        schema = z.boolean();
        break;
        
      case 'array':
        // 处理数组类型
        if (propSchema.items) {
          const itemSchema = processProperty(propSchema.items, `${propName}[item]`);
          schema = z.array(itemSchema);
        } else {
          // 如果没有items定义，默认使用字符串数组
          schema = z.array(z.string()).describe(`${propName}数组`);
        }
        break;
        
      case 'object':
        // 处理嵌套对象 - 保持嵌套结构
        const nestedProperties = propSchema.properties;
        const nestedRequired = propSchema.required || [];
        
        if (nestedProperties && Object.keys(nestedProperties).length > 0) {
          // 递归处理嵌套属性，保持嵌套结构
          const nestedProps = processProperties(nestedProperties, nestedRequired);
          schema = z.object(nestedProps);
        } else {
          // 空对象或没有properties的对象
          schema = z.record(z.any());
        }
        break;
        
      default:
        schema = z.any();
    }
    
    // 添加描述
    if (propDesc) {
      schema = schema.describe(propDesc);
    }
    
    return schema;
  }
  
  /**
   * 处理对象的所有属性
   */
  function processProperties(properties: any, required: string[] = []): Record<string, any> {
    const props: Record<string, any> = {};
    
    for (const [propName, propSchema] of Object.entries(properties)) {
      if (!propSchema || typeof propSchema !== 'object') continue;
      
      const isRequired = required.includes(propName);
      
      // 处理单个属性
      let schema = processProperty(propSchema, propName);
      
      // 非必填参数设为可选
      if (!isRequired) {
        schema = schema.optional();
      }
      
      props[propName] = schema;
    }
    
    return props;
  }
  
  try {
    // 处理顶层properties
    const properties = jsonSchema.properties || {};
    const required = Array.isArray(jsonSchema.required) ? jsonSchema.required : [];
    
    const allProps = processProperties(properties, required);
    
    const finalSchema = z.object(allProps);
    
    return finalSchema;
  } catch (error) {
    console.error('[MCP:JsonSchema] 处理JSON Schema时出错:', error);
    return z.object({});
  }
}

/**
 * 获取MCP请求选项
 */
function getMcpRequestOptions(serverId: string) {
  const serverConfig = mcpConfig.mcpServers[serverId];
  if (!serverConfig) return {};

  const options: { timeout?: number; resetTimeoutOnProgress?: boolean } = {};
  
  // 只有当配置中明确设置了值时才添加到选项中
  if (typeof serverConfig.timeout === 'number') {
    options.timeout = serverConfig.timeout;
  }
  
  if (typeof serverConfig.resetTimeoutOnProgress === 'boolean') {
    options.resetTimeoutOnProgress = serverConfig.resetTimeoutOnProgress;
  }
  
  return options;
}

/**
 * 获取MCP工具列表
 */
export async function getMcpTools(serverId?: string) {
  const targetServerId = serverId || getActiveMcpServerId();
  
  // 检查缓存是否有效
  const now = Date.now();
  if (
    cachedMcpTools[targetServerId] && 
    cachedMcpToolsTimestamp[targetServerId] &&
    now - cachedMcpToolsTimestamp[targetServerId] < CACHE_TTL
  ) {
    console.log(`[MCP:${targetServerId}] 使用缓存的工具列表，缓存时间: ${Math.round((now - cachedMcpToolsTimestamp[targetServerId])/1000)}秒`);
    return cachedMcpTools[targetServerId];
  }
  
  console.log(`[MCP:${targetServerId}] 缓存无效或过期，重新获取工具列表...`);
  
  try {
    const client = await getMcpClient(targetServerId);
    const toolsResponse = await client.listTools();
    
    if (!toolsResponse || !toolsResponse.tools || !Array.isArray(toolsResponse.tools)) {
      console.warn(`[MCP:${targetServerId}] 工具列表不是数组:`, toolsResponse);
      return {};
    }
    
    const tools = toolsResponse.tools;
    console.log(`[MCP:${targetServerId}] 发现${tools.length}个工具`);
    
    // 创建工具映射
    const toolsMap: Record<string, any> = {};
    
    for (const toolInfo of tools) {
      const toolName = toolInfo.name;
      
      // 获取服务器配置中的工具前缀
      const serverConfig = mcpConfig.mcpServers[targetServerId];
      const toolPrefix = serverConfig?.toolPrefix;
      
      // 如果配置了工具前缀，则添加前缀和连字符
      const finalToolName = toolPrefix ? `${toolPrefix}-${toolName}` : toolName;
      
      // 确定参数定义来源 - 优先使用 inputSchema
      const hasInputSchema = !!toolInfo.inputSchema;
      
      // 创建参数模式
      let schema;
      if (hasInputSchema) {
        // 使用inputSchema创建参数模式
        schema = createZodSchemaFromJsonSchema(toolInfo.inputSchema);
      } else if (Array.isArray(toolInfo.parameters) && toolInfo.parameters.length > 0) {
        // 兼容使用parameters数组
        schema = createZodSchema(toolInfo.parameters);
      } else {
        // 没有参数定义，使用空对象
        schema = z.object({});
      }
      
      // 创建AI SDK工具
      toolsMap[finalToolName] = tool({
        description: toolInfo.description || '',
        parameters: schema,
        execute: async (args: any, options: { toolCallId: string; messages: any[]; abortSignal?: AbortSignal }) => {
          try {
            console.log(`[MCP:${targetServerId}] 调用工具: ${finalToolName}`);
            
            // 从工具调用ID中提取请求ID
            const requestId = extractRequestIdFromToolCallId(options.toolCallId);
            const requestContext = getRequestContext(requestId);
            
            // 为 WMS 工具设置默认值和头信息
            if (toolName === 'call_wms_api' && typeof args === 'object') {
              setWmsApiHeaders(args, requestContext, targetServerId);
            }
            // 为 FMS 工具设置默认值和头信息
            if (toolName === 'call_fms_api' && typeof args === 'object') {
              setFmsApiHeaders(args, requestContext, targetServerId);
            }
            // 为 Portal 和 TMS 工具设置默认值和头信息
            if ((toolName === 'call_portal_api' || toolName === 'call_tms_api') && typeof args === 'object') {
              setPortalApiHeaders(args, requestContext, targetServerId);
            }
            
            const result = await client.callTool(
              {
                name: toolName,
                arguments: args
              },
              undefined,
              getMcpRequestOptions(targetServerId)
            );
            
            const processedResult = processToolResult(result);
            console.log(`[MCP:${targetServerId}] 工具调用成功: ${finalToolName}`);
            
            return processedResult;
          } catch (error) {
            console.error(`[MCP:${targetServerId}] 工具调用失败: ${finalToolName}`, error);
            
            // 对所有错误都进行连接清理，确保下次调用可用
            console.log(`[MCP:${targetServerId}] 检测到错误，正在重置连接状态...`);
            await cleanupMcpClient(targetServerId);
            
            // 创建错误对象
            const errorObj: {
              error: any;
              cause: any;
              args: any;
              toolName: string;
              serverId: string;
              suggestion?: string;
            } = {
              error: error instanceof Error ? error.message : "Unknown error occurred",
              cause: (error instanceof Error && 'cause' in error) ? (error as any).cause : null,
              args: args,
              toolName: finalToolName,
              serverId: targetServerId,
              suggestion: "Tool call failed, connection has been reset. If the problem persists, please check parameters or contact administrator."
            };
            
            return { 
              error: errorObj,
              success: false 
            };
          }
        },
      });
    }
    
    console.log(`[MCP:${targetServerId}] 工具创建完成, 工具数量: ${Object.keys(toolsMap).length}`);
    
    // 将工具列表保存到缓存
    cachedMcpTools[targetServerId] = toolsMap;
    cachedMcpToolsTimestamp[targetServerId] = now;
    
    return toolsMap;
  } catch (error) {
    console.error(`[MCP:${targetServerId}] 获取工具失败:`, error);
    return {};
  }
}

/**
 * 强制重新加载MCP工具列表（清除缓存）
 */
export async function reloadMcpTools(serverId?: string): Promise<Record<string, any>> {
  const targetServerId = serverId || getActiveMcpServerId();
  console.log(`[MCP:${targetServerId}] 强制重新加载工具列表...`);
  
  // 清除指定服务器的缓存
  delete cachedMcpTools[targetServerId];
  delete cachedMcpToolsTimestamp[targetServerId];
  
  // 重新获取工具列表
  return await getMcpTools(targetServerId);
}

/**
 * 获取所有MCP服务器的工具
 */
export async function getAllServersMcpTools() {
  const serverIds = getAllMcpServerIds();
  const allTools: Record<string, any> = {};
  
  console.log(`[MCP] 正在获取所有服务器工具:`, serverIds);
  
  await Promise.all(
    serverIds.map(async (serverId) => {
      try {
        // 使用缓存系统获取工具
        const tools = await getMcpTools(serverId);
        
        // 合并工具到全局映射
        Object.entries(tools).forEach(([name, toolObj]) => {
          console.log(`[MCP] 合并服务器 ${serverId} 的工具: ${name}`);
          console.log(`[MCP] 工具 ${name} 的定义:`, {
            description: toolObj.description,
            hasExecute: !!toolObj.execute,
            parametersType: typeof toolObj.parameters
          });
          allTools[name] = toolObj;
        });
        
        console.log(`[MCP] 获取服务器工具成功: ${serverId}, 数量: ${Object.keys(tools).length}`);
      } catch (error) {
        console.error(`[MCP] 获取服务器工具失败: ${serverId}`, error);
      }
    })
  );
  
  console.log(`[MCP] 总工具数量: ${Object.keys(allTools).length}, 工具列表:`, Object.keys(allTools));
  return allTools;
}

/**
 * 获取指定MCP服务器的工具
 */
export async function getSpecificServersMcpTools(serverIds: string[]) {
  const allTools: Record<string, any> = {};
  
  console.log(`[MCP] 正在获取指定服务器工具:`, serverIds);
  
  await Promise.all(
    serverIds.map(async (serverId) => {
      try {
        // 使用缓存系统获取工具
        const tools = await getMcpTools(serverId);
        
        // 合并工具到全局映射
        Object.entries(tools).forEach(([name, toolObj]) => {
          console.log(`[MCP] 合并服务器 ${serverId} 的工具: ${name}`);
          console.log(`[MCP] 工具 ${name} 的定义:`, {
            description: toolObj.description,
            hasExecute: !!toolObj.execute,
            parametersType: typeof toolObj.parameters
          });
          allTools[name] = toolObj;
        });
        
        console.log(`[MCP] 获取服务器工具成功: ${serverId}, 数量: ${Object.keys(tools).length}`);
      } catch (error) {
        console.error(`[MCP] 获取服务器工具失败: ${serverId}`, error);
      }
    })
  );
  
  console.log(`[MCP] 指定服务器总工具数量: ${Object.keys(allTools).length}, 工具列表:`, Object.keys(allTools));
  return allTools;
}

/**
 * 处理提示词内容
 */
function extractPromptText(promptData: any): string {
  if (!promptData || !promptData.messages || !Array.isArray(promptData.messages)) {
    return '';
  }
  
  const textParts: string[] = [];
  
  for (const message of promptData.messages) {
    if (!message || !message.content) continue;
    
    if (typeof message.content === 'string') {
      textParts.push(message.content);
    } else if (typeof message.content === 'object') {
      if (Array.isArray(message.content)) {
        // 处理数组形式的内容
        for (const part of message.content) {
          if (part.type === 'text' && part.text) {
            textParts.push(part.text);
          }
        }
      } else if (message.content.type === 'text' && message.content.text) {
        textParts.push(message.content.text);
      }
    }
  }
  
  return textParts.join('\n\n');
}

/**
 * 获取MCP服务器提示词
 */
export async function getAllMcpPrompts(serverId?: string): Promise<string | null> {
  const targetServerId = serverId || getActiveMcpServerId();
  
  try {
    console.log(`[MCP:${targetServerId}] 获取提示词列表...`);
    
    const client = await getMcpClient(targetServerId);
    const promptsResponse = await client.listPrompts();
    
    if (!promptsResponse || !promptsResponse.prompts || !Array.isArray(promptsResponse.prompts) || promptsResponse.prompts.length === 0) {
      console.log(`[MCP:${targetServerId}] 没有找到提示词`);
      return null;
    }
    
    const prompts = promptsResponse.prompts;
    console.log(`[MCP:${targetServerId}] 发现${prompts.length}个提示词`);
    
    // 构建提示词内容
    let combinedPrompt = `## MCP Server: ${targetServerId} Tool Instructions\n\n`;
    
    for (const promptInfo of prompts) {
      try {
        const prompt = await client.getPrompt({
          name: promptInfo.name
        });
        
        combinedPrompt += `### ${promptInfo.description || promptInfo.name}\n\n`;
        combinedPrompt += `${extractPromptText(prompt)}\n\n`;
        
      } catch (error) {
        console.error(`[MCP:${targetServerId}] 获取提示词详情失败: ${promptInfo.name}`, error);
      }
    }
    
    return combinedPrompt;
  } catch (error) {
    console.error(`[MCP:${targetServerId}] 获取提示词失败:`, error);
    return null;
  }
}

/**
 * 获取所有MCP服务器提示词
 */
export async function getAllServersMcpPrompts(): Promise<string | null> {
  const serverIds = getAllMcpServerIds();
  const allPrompts: string[] = [];
  
  console.log(`[MCP] 正在获取所有服务器提示词:`, serverIds);
  
  await Promise.all(
    serverIds.map(async (serverId) => {
      try {
        const prompts = await getAllMcpPrompts(serverId);
        if (prompts) {
          allPrompts.push(prompts);
        }
      } catch (error) {
        console.error(`[MCP] 获取服务器提示词失败: ${serverId}`, error);
      }
    })
  );
  
  if (allPrompts.length === 0) {
    console.log(`[MCP] 未找到任何提示词`);
    return null;
  }
  
  return allPrompts.join('\n\n');
}

/**
 * 获取指定MCP服务器的提示词
 */
export async function getSpecificServersMcpPrompts(serverIds: string[]): Promise<string | null> {
  const allPrompts: string[] = [];
  
  console.log(`[MCP] 正在获取指定服务器提示词:`, serverIds);
  
  await Promise.all(
    serverIds.map(async (serverId) => {
      try {
        const prompts = await getAllMcpPrompts(serverId);
        if (prompts) {
          allPrompts.push(prompts);
        }
      } catch (error) {
        console.error(`[MCP] 获取服务器提示词失败: ${serverId}`, error);
      }
    })
  );
  
  if (allPrompts.length === 0) {
    console.log(`[MCP] 指定服务器未找到任何提示词`);
    return null;
  }
  
  return allPrompts.join('\n\n');
}

/**
 * 获取指定前缀的提示词
 */
export async function getPromptsByPrefix(prefix: string, serverId?: string): Promise<string | null> {
  const targetServerId = serverId || getActiveMcpServerId();
  
  try {
    console.log(`[MCP:${targetServerId}] 获取前缀为 '${prefix}' 的提示词...`);
    
    const client = await getMcpClient(targetServerId);
    const promptsResponse = await client.listPrompts();
    
    if (!promptsResponse || !promptsResponse.prompts || !Array.isArray(promptsResponse.prompts) || promptsResponse.prompts.length === 0) {
      return null;
    }
    
    // 筛选匹配的提示词
    const matchingPrompts = promptsResponse.prompts.filter((p: any) => 
      p.name.toLowerCase().includes(prefix.toLowerCase())
    );
    
    if (matchingPrompts.length === 0) {
      return null;
    }
    
    console.log(`[MCP:${targetServerId}] 找到${matchingPrompts.length}个匹配提示词`);
    
    // 构建提示词内容
    let combinedPrompt = `## ${prefix.toUpperCase()} Related Tools (Server: ${targetServerId})\n\n`;
    
    for (const promptInfo of matchingPrompts) {
      try {
        const prompt = await client.getPrompt({
          name: promptInfo.name
        });
        
        combinedPrompt += `### ${promptInfo.description || promptInfo.name}\n\n`;
        combinedPrompt += `${extractPromptText(prompt)}\n\n`;
        
      } catch (error) {
        console.error(`[MCP:${targetServerId}] 获取提示词详情失败: ${promptInfo.name}`, error);
      }
    }
    
    return combinedPrompt;
  } catch (error) {
    console.error(`[MCP:${targetServerId}] 获取提示词失败:`, error);
    return null;
  }
}

/**
 * 获取所有服务器指定前缀的提示词
 */
export async function getAllServersPromptsByPrefix(prefix: string): Promise<string | null> {
  const serverIds = getAllMcpServerIds();
  const matchingPrompts: string[] = [];
  
  console.log(`[MCP] 正在获取所有服务器前缀为 '${prefix}' 的提示词`);
  
  await Promise.all(
    serverIds.map(async (serverId) => {
      try {
        const prompts = await getPromptsByPrefix(prefix, serverId);
        if (prompts) {
          matchingPrompts.push(prompts);
        }
      } catch (error) {
        console.error(`[MCP] 获取服务器提示词失败: ${serverId}`, error);
      }
    })
  );
  
  if (matchingPrompts.length === 0) {
    return null;
  }
  
  return matchingPrompts.join('\n\n');
}

/**
 * 读取MCP资源
 */
export async function readMcpResource(resourceUri: string, serverId?: string) {
  const targetServerId = serverId || getActiveMcpServerId();
  
  try {
    console.log(`[MCP:${targetServerId}] 读取资源: ${resourceUri}`);
    
    const client = await getMcpClient(targetServerId);
    const response = await client.readResource({
      uri: resourceUri
    });
    
    // 处理响应
    if (!response || !response.contents || !Array.isArray(response.contents) || response.contents.length === 0) {
      return { content: null, mimeType: 'text/plain' };
    }
    
    const contentItem = response.contents[0];
    
    return { 
      content: contentItem.text || contentItem.data, 
      mimeType: contentItem.mimeType || 'text/plain' 
    };
  } catch (error) {
    console.error(`[MCP:${targetServerId}] 资源读取失败: ${resourceUri}`, error);
    throw error;
  }
}

export interface McpServerConfig {
  // 服务器URL
  url: string; 
  // 命令行启动配置（如果需要本地启动）
  command?: string;
  args?: string[];
  // 环境变量
  env?: Record<string, string>;
  // 其他配置选项
  options?: Record<string, any>;
  // 服务器描述
  description?: string;
  // 传输类型
  transport?: 'sse' | 'stdio';
  // 超时配置（毫秒）
  timeout?: number;
  // 是否在有进度时重置超时
  resetTimeoutOnProgress?: boolean;
}

/**
 * 清理MCP客户端连接
 */
async function cleanupMcpClient(serverId: string) {
  try {
    const client = clientCache[serverId];
    if (client) {
      console.log(`[MCP:${serverId}] 正在清理客户端连接...`);
      // 关闭传输层连接
      if (client.transport) {
        await client.transport.close();
      }
      // 从缓存中移除客户端实例
      delete clientCache[serverId];
      console.log(`[MCP:${serverId}] 客户端连接已清理`);
    }
  } catch (error) {
    console.error(`[MCP:${serverId}] 清理客户端连接时出错:`, error);
  }
}

/**
 * 在应用启动时预加载所有MCP服务器的工具
 */
export async function preloadAllMcpTools(): Promise<void> {
  const serverIds = getAllMcpServerIds();
  console.log(`[MCP] 开始预加载所有服务器(${serverIds.length}个)的工具列表...`);
  
  const startTime = Date.now();
  
  if (serverIds.length === 0) {
    console.log('[MCP] 没有找到MCP服务器配置，跳过预加载');
    return;
  }
  
  try {
    await Promise.all(
      serverIds.map(async (serverId) => {
        try {
          const serverStartTime = Date.now();
          await getMcpTools(serverId);
          const duration = Date.now() - serverStartTime;
          console.log(`[MCP] 成功预加载服务器 ${serverId} 的工具，耗时: ${duration}ms`);
        } catch (error) {
          console.error(`[MCP] 预加载服务器 ${serverId} 的工具失败:`, error);
        }
      })
    );
    
    // 预加载提示词
    await getAllServersMcpPrompts();
    
    const totalDuration = Date.now() - startTime;
    console.log(`[MCP] 所有服务器工具预加载完成，总耗时: ${totalDuration}ms`);
  } catch (error) {
    console.error('[MCP] 预加载过程中发生错误:', error);
  }
}

// 启动预加载过程（仅在服务器端运行）
if (typeof window === 'undefined') {
  console.log('[MCP] 服务器端环境，开始预加载MCP工具...');
  // 使用setTimeout确保在应用启动完成后再执行预加载
  setTimeout(() => {
    preloadAllMcpTools().catch(error => {
      console.error('[MCP] 预加载过程失败:', error);
    });
  }, 2000);
} else {
  console.log('[MCP] 客户端环境，跳过MCP工具预加载');
}

/**
 * 根据服务器ID列表获取MCP提示词
 * @param serverIds 要获取提示词的服务器ID数组
 * @returns 组合后的提示词字符串，如果没有找到任何提示词则返回null
 */
export async function getMcpPromptsByServerIds(serverIds: string[]): Promise<string | null> {
  if (!serverIds || serverIds.length === 0) {
    console.log(`[MCP] 未提供服务器ID列表，无法获取提示词`);
    return null;
  }
  
  const validPrompts: string[] = [];
  
  console.log(`[MCP] 获取指定服务器提示词，服务器列表: ${serverIds.join(', ')}`);
  
  await Promise.all(
    serverIds.map(async (serverId) => {
      try {
        const prompts = await getAllMcpPrompts(serverId);
        if (prompts) {
          validPrompts.push(prompts);
          console.log(`[MCP] 成功获取服务器 ${serverId} 的提示词`);
        }
      } catch (error) {
        console.error(`[MCP] 获取服务器 ${serverId} 提示词失败:`, error);
      }
    })
  );
  
  if (validPrompts.length === 0) {
    return null;
  }
  
  return validPrompts.join('\n\n');
}

// ================= 工具头信息设置方法 =================
/**
 * 设置WMS工具的headers参数
 */
function setWmsApiHeaders(args: any, requestContext: any, targetServerId: string) {
  const headers: Record<string, string> = {};
  // 添加认证头
  if (requestContext?.authorization) {
    headers['Authorization'] = requestContext.authorization;
    console.log(`[MCP:${targetServerId}] 设置Authorization头`);
  }
  // 添加租户和设施头
  const tenantId = requestContext?.tenantId;
  if (tenantId) {
    headers['x-tenant-id'] = tenantId;
  }
  const facilityId = requestContext?.facilityId;
  if (facilityId) {
    headers['x-facility-id'] = facilityId;
  }
  // 合并headers到参数
  if (Object.keys(headers).length > 0) {
    if (args.headers && typeof args.headers === 'object') {
      args.headers = { ...headers, ...args.headers };
      if (tenantId && headers['x-tenant-id']) {
        args.headers['x-tenant-id'] = headers['x-tenant-id'];
      }
      if (facilityId && headers['x-facility-id']) {
        args.headers['x-facility-id'] = headers['x-facility-id'];
      }
    } else {
      args.headers = headers;
    }
    console.log(`[MCP:${targetServerId}] 已设置headers参数，包含${Object.keys(args.headers).length}个头信息`);
  }
  console.log(`[MCP:${targetServerId}] 修正后的参数:`, JSON.stringify(args, (key, value) => {
    if (key === 'headers' && value.Authorization) {
      return { ...value, Authorization: 'Bearer [REDACTED]' };
    }
    return value;
  }));
}

/**
 * 设置FMS工具的headers参数
 * Company-id: requestContext.tenantId
 * Fms-token: requestContext.authorization（去除Bearer前缀）
 */
function setFmsApiHeaders(args: any, requestContext: any, targetServerId: string) {
  const headers: Record<string, string> = {};
  // Company-id
  if (requestContext?.tenantId) {
    headers['Company-id'] = requestContext.tenantId;
  }
  
  // authorization，去除Bearer前缀
  if (requestContext?.authorization) {
    let token = requestContext.authorization;
    if (token.startsWith('Bearer ')) {
      token = token.slice(7);
    }
    headers['authorization'] = token;
  }

  // Fms-token，去除Bearer前缀
  if (requestContext?.fmsToken) {
    let token = requestContext.fmsToken;
    if (token.startsWith('Bearer ')) {
      token = token.slice(7);
    }
    headers['fms-token'] = token;
  }

  // 合并headers到参数
  if (Object.keys(headers).length > 0) {
    if (args.headers && typeof args.headers === 'object') {
      args.headers = { ...headers, ...args.headers };
      if (headers['Company-id']) {
        args.headers['Company-id'] = headers['Company-id'];
      }
      if (headers['Fms-token']) {
        args.headers['Fms-token'] = headers['Fms-token'];
      }
    } else {
      args.headers = headers;
    }
    console.log(`[MCP:${targetServerId}] FMS已设置headers参数，包含${Object.keys(args.headers).length}个头信息`);
  }
  console.log(`[MCP:${targetServerId}] FMS修正后的参数:`, JSON.stringify(args, (key, value) => {
    if (key === 'headers' && value['Fms-token']) {
      return { ...value, 'Fms-token': '[REDACTED]' };
    }
    return value;
  }));
}

/**
 * 设置Portal和TMS工具的headers参数
 */
function setPortalApiHeaders(args: any, requestContext: any, targetServerId: string) {
  const headers: Record<string, string> = {};
  
  // 使用 portalToken 作为 authorization
  if (requestContext?.portalToken) {
    let token = requestContext.portalToken;
    if (token.startsWith('Bearer ')) {
      token = token.slice(7);
    }
    headers['Authorization'] = token;
    headers['token'] = token;
  }

  // 合并headers到参数
  if (Object.keys(headers).length > 0) {
    if (args.headers && typeof args.headers === 'object') {
      args.headers = { ...headers, ...args.headers };
      if (headers['Authorization']) {
        args.headers['Authorization'] = headers['Authorization'];
      }
      if (headers['token']) {
        args.headers['token'] = headers['token'];
      }
    } else {
      args.headers = headers;
    }
    console.log(`[MCP:${targetServerId}] Portal/TMS已设置headers参数，包含${Object.keys(args.headers).length}个头信息`);
  }
  console.log(`[MCP:${targetServerId}] Portal/TMS修正后的参数:`, JSON.stringify(args, (key, value) => {
    if (key === 'headers' && (value['Authorization'] || value['token'])) {
      return { 
        ...value, 
        'Authorization': '[REDACTED]',
        'token': '[REDACTED]'
      };
    }
    return value;
  }));
} 