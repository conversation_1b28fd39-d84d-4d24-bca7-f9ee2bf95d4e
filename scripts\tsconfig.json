{"compilerOptions": {"target": "es2022", "module": "NodeNext", "moduleResolution": "NodeNext", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "outDir": "../dist", "baseUrl": "..", "paths": {"@/*": ["./src/*"]}}, "ts-node": {"esm": true, "experimentalSpecifierResolution": "node"}, "include": ["./**/*.ts"], "exclude": ["node_modules"]}