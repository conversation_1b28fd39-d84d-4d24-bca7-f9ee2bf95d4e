import React from 'react';

export default function ErrorCardEmbed({ error, message }: { error?: any, message?: string }) {
  const errorMessage = message || (typeof error === 'string' ? error : 'Operation failed, please try again');
  const errorDetails = typeof error === 'object' && error !== null ? JSON.stringify(error, null, 2) : null;
  
  return (
    <div className="embed-card error p-3 my-1 bg-gray-800/30 rounded-md border-l-4 border-red-600/50 border-t border-r border-b border-gray-700/30">
      <div className="flex items-center w-full">
        <div className="flex-shrink-0 mr-2 bg-gray-700/40 p-1 rounded">
          <span className="text-red-400 text-base">❌</span>
        </div>
        <div className="flex-1">
          <div className="text-sm text-red-400 font-medium">Error</div>
          <div className="text-xs text-gray-200 mt-1">
            {errorMessage}
          </div>
        </div>
      </div>
      
      {errorDetails && (
        <div className="mt-2 pt-2 border-t border-gray-700/30 pl-7">
          <div className="text-xs bg-red-900/20 p-2 rounded overflow-auto max-h-60 border border-red-800/20">
            <pre className="whitespace-pre-wrap break-words text-red-200 font-mono text-xs">
              {errorDetails}
            </pre>
          </div>
        </div>
      )}
    </div>
  );
} 