import { NextRequest, NextResponse } from 'next/server';
import axios from 'axios';

// IAM配置
const IAM_CONFIG = {
  TOKEN_ENDPOINT: `${process.env.NEXT_PUBLIC_IAM_ENDPOINT}/oauth2/token`
};

// 检查环境变量
if (!process.env.NEXT_PUBLIC_IAM_ENDPOINT) {
  console.error('Missing NEXT_PUBLIC_IAM_ENDPOINT configuration');
}

export async function POST(req: NextRequest) {
  try {
    // 解析请求体
    const { code, redirectUri } = await req.json();
    
    if (!code) {
      return NextResponse.json(
        { error: 'Missing authorization code' },
        { status: 400 }
      );
    }
    
    // 获取客户端凭据（从环境变量）
    const clientId = process.env.NEXT_PUBLIC_IAM_CLIENT_ID;
    const clientSecret = process.env.IAM_CLIENT_SECRET;
    
    if (!clientId || !clientSecret) {
      console.error('Missing IAM client credentials', {
        hasClientId: !!clientId,
        hasClientSecret: !!clientSecret,
        envKeys: Object.keys(process.env).filter(key => key.includes('IAM'))
      });
      return NextResponse.json(
        { error: 'Server configuration error' },
        { status: 500 }
      );
    }
    
    // 打印安全日志（不显示实际密钥）
    console.log('Token exchange info:', {
      clientIdPrefix: clientId.substring(0, 5) + '...',
      hasClientSecret: !!clientSecret,
      clientSecretLength: clientSecret.length,
      redirectUri
    });
    
    // 准备Basic认证头
    const authStr = `${clientId}:${clientSecret}`;
    const authHeader = Buffer.from(authStr).toString('base64');
    
    // 请求体
    const params = new URLSearchParams();
    params.append('grant_type', 'authorization_code');
    params.append('code', code);
    params.append('redirect_uri', redirectUri);
    
    // 发送请求到IAM令牌端点
    const response = await axios.post(
      IAM_CONFIG.TOKEN_ENDPOINT,
      params,
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Authorization': `Basic ${authHeader}`
        }
      }
    );
    
    // 返回令牌响应
    return NextResponse.json(response.data);
  } catch (error) {
    console.error('Token exchange error:', error);
    
    // 处理特定的错误响应
    if (axios.isAxiosError(error) && error.response) {
      const { status, data } = error.response;
      console.error('IAM server response:', {
        status,
        data
      });
      return NextResponse.json(
        { error: data.error || 'Token exchange failed', details: data },
        { status }
      );
    }
    
    // 通用错误处理
    return NextResponse.json(
      { error: 'Failed to exchange authorization code for token' },
      { status: 500 }
    );
  }
} 