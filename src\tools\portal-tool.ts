import { tool } from 'ai';
import { z } from 'zod';
import { BaseTool } from './baseTool';
import { ToolDefinition } from './types';
import { extractRequestIdFromToolCallId, getRequestContext, RequestContext } from '@/utils/requestContext';
import { getCurrentDateByTimezone } from '@/utils/timezoneUtils';

/**
 * 构建Portal API请求的通用headers
 * @param requestContext 请求上下文
 * @param contentType 内容类型，默认为application/json
 * @returns 包含认证信息的headers对象
 */
function buildPortalHeaders(requestContext: RequestContext | undefined, contentType: string = 'application/json'): Record<string, string> {
  const authToken = requestContext?.portalToken;
  const iamToken = requestContext?.authorization;
  let tenantId = requestContext?.tenantId;
  
  // tenantId 为空时，设置为 SBFH
  if (!tenantId) {
    tenantId = 'SBFH';
  } else if (tenantId !== 'SBFH') {
    tenantId = 'SBFH';
  }

  // iamToken 和 authToken 不能同时为空
  if (!iamToken && !authToken) {
    throw new Error('Authentication error: Both iamToken and authToken cannot be empty');
  }

  const headers: Record<string, string> = {
    'Content-Type': contentType
  };
  
  // 添加认证token
  if (authToken) {
    headers['Authorization'] = authToken;
    headers['Token'] = authToken;
  }
  
  // 添加iam-token
  if (iamToken) {
    // 移除 Bearer 前缀（如果存在）
    const cleanIamToken = iamToken.replace(/^Bearer\s+/i, '');
    headers['iam-token'] = cleanIamToken;
  }
  
  // 添加tenant ID
  if (tenantId) {
    headers['X-Tenant-ID'] = tenantId;
  }
  console.log('buildPortalHeaders headers:', headers);
  return headers;
}

/**
 * Portal报价工具
 * 使用 Vercel AI SDK 的 tool 函数实现
 */
export const portalQuoteTool = tool({
  description: 'Calculate real-time freight shipping quotes and pricing for transportation services. Use when user requests shipping quotes, freight pricing, or cost estimates for transportation. Examples: "Get a quote for shipping from CA to TX for customer SAMSBF00", "From 77002 to 77003, a shipment is being transported with the additional service of LIFTGATE PICKUP?"',
  parameters: z.object({
    customer_code: z.string().describe('Customer code for the quote, must explicitly selected by the user from the customer list if user not mentions customer_code. Never auto-fill or default this value. Never use portalCustomerSelector/customerSelector for this field.'),
    shipper: z.object({
      city: z.string().describe('Shipper city'),
      state: z.string().describe('Shipper state (2-letter code)'),
      zip: z.string().describe('Shipper ZIP code')
    }).describe('Shipper information'),
    consignee: z.object({
      city: z.string().describe('Consignee city'),
      state: z.string().describe('Consignee state (2-letter code)'),
      zip: z.string().describe('Consignee ZIP code')
    }).describe('Consignee information'),
    manifests: z.array(z.object({
      item: z.string().optional().describe('Item description'),
      quantity: z.number().describe('Number of items'),
      width: z.number().describe('Width in inches'),
      length: z.number().describe('Length in inches'),
      height: z.number().describe('Height in inches'),
      pallets_stackable: z.boolean().describe('Whether pallets are stackable'),
      class: z.string().describe('Freight class eg. 50, 55, 60, 65, 70, 77, 85, 92, 100, 110, 125, 150, 175, 200, 250, 300, 400'),
      weight: z.number().describe('Weight in pounds'),
      nmfc: z.string().optional().describe('NMFC code'),
      description: z.string().optional().describe('Additional description')
    })).describe('Array of manifest items'),
    additional_quote_lines: z.array(z.object({
      code: z.string().describe('Service code (e.g., "LGPUP", "APPTD")'),
      name: z.string().describe('Service name (e.g., "LIFTGATE PICKUP", "APPOINTMENT REQUIRED")')
    })).optional().describe('Additional quote lines for special services (optional). Example: [{"code":"LGPUP","name":"LIFTGATE PICKUP"}]')
  }),
  execute: async (params, options: { toolCallId: string; messages: any[] }) => {
    console.log('PortalQuoteTool.execute called with args:', JSON.stringify(params));
    
    try {
      // 从环境变量获取Portal基础URL，如果未设置则使用默认值
      const baseUrl = process.env.PORTAL_BASEURL || 'https://portal-staging.item.com';
      const apiUrl = `${baseUrl}/api/v1/web/rate-shopping/quote`;
      
      // 从工具调用ID中提取请求ID
      const requestId = extractRequestIdFromToolCallId(options.toolCallId);
      const requestContext = getRequestContext(requestId);

      if (!params.customer_code) {
        throw new Error('Missing customer code in request context');
      }

      // 构建请求体
      const requestBody = {
        acc_lines: (params.additional_quote_lines || []).map(line => ({
          ...line,
          qty: 0  // 固定设置 qty 为 0
        })),
        consignee: params.consignee,
        extra: params.customer_code === 'UNIS-500' ? {} : {
          customer_code: params.customer_code
        },
        manifests: params.manifests,
        shipper: params.shipper
      };

      console.log('PortalQuoteTool request body:', JSON.stringify(requestBody));
      
      // 发送API请求
      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: buildPortalHeaders(requestContext),
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      
      console.log('PortalQuoteTool.execute response:', JSON.stringify(data));
      
      return {
        success: true,
        quote_data: data,
        request_params: requestBody,
        baseUrl,
        timestamp: new Date().toISOString()
      };
      
    } catch (error) {
      console.error('PortalQuoteTool.execute error:', error);
      
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        timestamp: new Date().toISOString()
      };
    }
  }
});

export const disputeTypes = ['quantity', 'quality', 'pricing', 'damage', 'other'] as const;

/**
 * 获取发票详情工具
 */
export const portalGetInvoiceDetailTool = tool({
  description: 'Get detailed information for a specific invoice including line items. Use when user wants to view detailed invoice information or needs invoice details for dispute submission. Examples: "Get invoice detail for INV001", "Show me the details of invoice INV001", "What\'s in invoice INV001?"',
  parameters: z.object({
    invoice_number: z.string().describe('Invoice number to query'),
    customer_code: z.string().describe('Customer code for the invoice')
  }),
  execute: async (params, options: { toolCallId: string; messages: any[] }) => {
    console.log('PortalGetInvoiceDetailTool.execute called with args:', JSON.stringify(params));
    
    try {
      // 1. 参数验证
      if (!params.invoice_number || params.invoice_number.trim() === "") {
        throw new Error('Invoice number cannot be empty');
      }
      
      if (!params.customer_code || params.customer_code.trim() === "") {
        throw new Error('Customer code cannot be empty');
      }
      
      // 从环境变量获取Portal基础URL
      const baseUrl = process.env.PORTAL_BASEURL || 'https://portal-staging.item.com';
      const apiUrl = `${baseUrl}/api/v1/web/crm-hand-out/bnp/invoice/${params.invoice_number}`;
      
      // 从工具调用ID中提取请求ID
      const requestId = extractRequestIdFromToolCallId(options.toolCallId);
      const requestContext = getRequestContext(requestId);
      
      // 构建URL参数
      const urlParams = new URLSearchParams({
        'department': 'UT',  // 固定值
        'customerCode': params.customer_code
      });
      
      const fullUrl = `${apiUrl}?${urlParams.toString()}`;
      
      console.log('Getting invoice detail from:', fullUrl);
      
      // 发送API请求
      const response = await fetch(fullUrl, {
        method: 'GET',
        headers: buildPortalHeaders(requestContext)
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      
      console.log('PortalGetInvoiceDetailTool.execute response:', JSON.stringify(data));
      
      // 处理API响应
      if (data.code === 0) {
        // 获取成功，处理发票详情数据
        const api_data = data.data || {};
        const invoice_detail = api_data.data || {};
        
        // 格式化发票详情
        const formatted_invoice_detail = {
          "invoiceId": invoice_detail.invoiceID || invoice_detail.id || invoice_detail.invoiceId,
          "invoiceNumber": invoice_detail.invoiceNumber || invoice_detail.invoice_number || invoice_detail.number,
          "invoiceAmount": invoice_detail.invoiceAmount || invoice_detail.amount || invoice_detail.total,
          "invoiceDate": invoice_detail.invoiceDate || invoice_detail.date || invoice_detail.issue_date,
          "invoiceStatusID": invoice_detail.invoiceStatusID || invoice_detail.status_id,
          "invoiceStatus": invoice_detail.invoiceStatus || invoice_detail.status || '已开票',
          "items": invoice_detail.items || []
        };
        
        // 移除空值
        const cleaned_invoice_detail = Object.fromEntries(
          Object.entries(formatted_invoice_detail).filter(([_, value]) => value !== null && value !== undefined && value !== '')
        );
        
        return {
          success: true,
          message: 'Successfully retrieved invoice details',
          data: {
            invoice_detail: cleaned_invoice_detail,
            guidance: {
              message: "成功获取发票详情",
              next_step: "请根据发票详情填写争议申请",
              available_actions: ["提交争议申请"]
            },
            usage_examples: {
              submit_dispute: "请填写争议原因和选择争议类型，提交争议申请"
            }
          },
          timestamp: new Date().toISOString()
        };
      } else {
        // 获取失败
        throw new Error(`Failed to get invoice details: ${data.msg || 'Unknown error'}`);
      }
      
    } catch (error) {
      console.error('PortalGetInvoiceDetailTool.execute error:', error);
      
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        timestamp: new Date().toISOString()
      };
    }
  }
});

/**
 * 提交准备好的争议申请工具
 */
export const portalSubmitPreparedDisputeTool = tool({
  description: 'Submit prepared dispute for an invoice with complete customer and invoice information. Use when user wants to submit a dispute for an invoice after gathering all required information. Examples: "Submit dispute for invoice INV001", "File a claim for incorrect pricing", "Dispute this invoice"',
  parameters: z.object({
    customer_id: z.string().describe('Customer ID'),
    customer_code: z.string().describe('Customer code'),
    customer_name: z.string().describe('Customer name'),
    invoice_number: z.string().describe('Invoice number to dispute'),
    dispute_type: z.enum(disputeTypes).describe('Type of dispute'),
    dispute_reason: z.string().describe('Reason for dispute'),
    dispute_items: z.array(z.record(z.any())).describe('Dispute items for dispute (choose from invoice detail response)')
  }),
  execute: async (params, options: { toolCallId: string; messages: any[] }) => {
    console.log('PortalSubmitPreparedDisputeTool.execute called with args:', JSON.stringify(params));
    
    try {
      // 1. 参数验证
      if (!params.customer_id || params.customer_id.trim() === "") {
        throw new Error('Customer ID cannot be empty');
      }
      
      if (!params.customer_code || params.customer_code.trim() === "") {
        throw new Error('Customer code cannot be empty');
      }
      
      if (!params.customer_name || params.customer_name.trim() === "") {
        throw new Error('Customer name cannot be empty');
      }
      
      if (!params.invoice_number || params.invoice_number.trim() === "") {
        throw new Error('Invoice number cannot be empty');
      }
      
      if (!params.dispute_type) {
        throw new Error('Dispute type cannot be empty');
      }
      
      if (!params.dispute_reason || params.dispute_reason.trim() === "") {
        throw new Error('Dispute reason cannot be empty');
      }
      
      // 验证争议原因长度
      const reason_length = params.dispute_reason.trim().length;
      if (reason_length < 1) {
        throw new Error('Dispute reason is too short, minimum 1 character required');
      }
      if (reason_length > 500) {
        throw new Error('Dispute reason is too long, maximum 500 characters allowed');
      }
      
      // 从环境变量获取Portal基础URL
      const baseUrl = process.env.PORTAL_BASEURL || 'https://portal-staging.item.com';
      const apiUrl = `${baseUrl}/api/v1/web/crm-hand-out/claim/ut-dispute`;
      
      // 从工具调用ID中提取请求ID
      const requestId = extractRequestIdFromToolCallId(options.toolCallId);
      const requestContext = getRequestContext(requestId);
      
      // 构建争议申请数据
      const dispute_data = {
        customerId: params.customer_id,
        customerCode: params.customer_code,
        customerName: params.customer_name,
        invoiceNumber: params.invoice_number,
        items: params.dispute_items || [],
        reason: params.dispute_reason.trim()
      };
      
      console.log('Submitting dispute with data:', JSON.stringify(dispute_data));
      
      // 发送API请求
      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: buildPortalHeaders(requestContext),
        body: JSON.stringify(dispute_data)
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      
      console.log('PortalSubmitPreparedDisputeTool.execute response:', JSON.stringify(data));
      
      // 处理API响应
      if (data.code === 0) {
        // 提交成功
        const result_data = data.data || {};
        
        return {
          success: true,
          message: 'Dispute submitted successfully',
          data: {
            dispute_id: result_data.dispute_id || result_data.id,
            invoice_number: params.invoice_number,
            dispute_type: params.dispute_type,
            dispute_reason: params.dispute_reason,
            status: result_data.status || '已提交',
            reference_number: result_data.reference_number,
            submission_time: result_data.created_time,
            next_steps: {
              message: "争议申请已成功提交",
              expected_response_time: "通常在2-3个工作日内处理",
              tracking_info: "您可以使用争议ID跟踪申请状态"
            }
          },
          timestamp: new Date().toISOString()
        };
      } else {
        // 提交失败
        throw new Error(`Failed to submit dispute: ${data.msg || 'Unknown error'}`);
      }
      
    } catch (error) {
      console.error('PortalSubmitPreparedDisputeTool.execute error:', error);
      
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        timestamp: new Date().toISOString()
      };
    }
  }
});

/**
 * 订单追踪工具
 */
export const portalTrackingOrderTool = tool({
  description: 'Track order status and details through Portal API. Use when user wants to track an order or query an order. Examples: "Track order DO00036423", "Get order status for DO00036423", "Query order DO00036423", "Query order status for DO00036423"',
  parameters: z.object({
    searchKeyword: z.string().describe('Order number or tracking number to search'),
    page: z.number().optional().describe('Page number for pagination').default(1),
    limit: z.number().optional().describe('Number of items per page').default(10),
  }),
  execute: async (params, options: { toolCallId: string; messages: any[] }) => {
    console.log('PortalTrackingOrderTool.execute called with args:', JSON.stringify(params));
    
    try {
      // 从环境变量获取Portal基础URL，如果未设置则使用默认值
      const baseUrl = process.env.PORTAL_BASEURL || 'https://portal-staging.item.com';
      const apiUrl = `${baseUrl}/api/v1/app/order/search`;
      
      // 从工具调用ID中提取请求ID
      const requestId = extractRequestIdFromToolCallId(options.toolCallId);
      const requestContext = getRequestContext(requestId);

      // 构建请求体
      const requestBody = {
        page: params.page,
        limit: params.limit,
        search_keyword: params.searchKeyword,
        status_type: -1,
        filter_str: [],
        search_type: 4,
        order_type: [1,2]
      };

      console.log('PortalTrackingOrderTool api request body:', apiUrl, JSON.stringify(requestBody));
      
      // 发送API请求
      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: buildPortalHeaders(requestContext),
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      
      // 为每个订单添加 trackingUrl
      if (data.data?.data) {
        const trackingUrlPrefix = process.env.PORTAL_TRACKING_URL_PREFIX || 'https://portal-staging.item.com';
        data.data.data = data.data.data.map((order: any) => {
          if (order.id) {
            return {
              ...order,
              trackingUrl: `${trackingUrlPrefix}/client-portal/order-details/${order.id}?from=chatbot`
            };
          }
          return order;
        });
      }
      console.log('PortalTrackingOrderTool.execute response:', JSON.stringify(data));
      
      return {
        success: true,
        data: data.data,
        request_params: requestBody,
        timestamp: new Date().toISOString()
      };
      
    } catch (error) {
      console.error('PortalTrackingOrderTool.execute error:', error);
      
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        timestamp: new Date().toISOString()
      };
    }
  }
});

/**
 * PortalTool 类，继承自 BaseTool
 * 适配旧的工具系统
 */
export class PortalTool extends BaseTool {
  constructor() {
    super();
  }
  
  getTools(): ToolDefinition[] {
    return [
      {
        name: 'getPortalQuote',
        description: 'Calculate shipping quotes through Portal API for freight transportation',
        parameters: {
          type: 'object',
          properties: {
            shipper: {
              type: 'object',
              properties: {
                city: { type: 'string', description: 'Shipper city' },
                state: { type: 'string', description: 'Shipper state (2-letter code)' },
                zip: { type: 'string', description: 'Shipper ZIP code' }
              },
              required: ['city', 'state', 'zip']
            },
            consignee: {
              type: 'object',
              properties: {
                city: { type: 'string', description: 'Consignee city' },
                state: { type: 'string', description: 'Consignee state (2-letter code)' },
                zip: { type: 'string', description: 'Consignee ZIP code' }
              },
              required: ['city', 'state', 'zip']
            },
            manifests: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  item: { type: 'string', description: 'Item description' },
                  quantity: { type: 'number', description: 'Number of items' },
                  width: { type: 'number', description: 'Width in inches' },
                  length: { type: 'number', description: 'Length in inches' },
                  height: { type: 'number', description: 'Height in inches' },
                  pallets_stackable: { type: 'boolean', description: 'Whether pallets are stackable' },
                  class: { type: 'string', description: 'Freight class' },
                  weight: { type: 'number', description: 'Weight in pounds' },
                  nmfc: { type: 'string', description: 'NMFC code' },
                  description: { type: 'string', description: 'Additional description' }
                },
                required: ['quantity', 'width', 'length', 'height', 'class', 'weight']
              }
            },
            extra: {
              type: 'object',
              properties: {
                location_address: {
                  type: 'object',
                  properties: {
                    city: { type: 'string', description: 'Location city' },
                    state: { type: 'string', description: 'Location state (2-letter code)' },
                    zip: { type: 'string', description: 'Location ZIP code' }
                  }
                },
                customer_code: { type: 'string', description: 'Customer code' }
              }
            },
            po_number: { type: 'string', description: 'Purchase order number' },
            reference_number: { type: 'string', description: 'Reference number' },
            total_freight_value: { type: 'number', description: 'Total freight value', default: 0 }
          },
          required: ['shipper', 'consignee', 'manifests']
        }
      },
      {
        name: 'saveShippingQuote',
        description: 'Save selected shipping quote for order creation. Use when user wants to save a specific quote from the quote results for creating a shipping order. Examples: "Save the quote with rate_uuid 3542493a-cdd4-40fc-a830-fe14b72c1cc1", "Save this shipping quote", "Select this rate for my order"',
        parameters: {
          type: 'object',
          properties: {
            rate_uuid: { type: 'string', description: 'Carrier quote unique identifier (from quote results)' },
            quote_id: { type: 'number', description: 'Quote ID (optional, defaults to 0)', default: 0 },
            token: { type: 'string', description: 'Quote token (optional - if not provided, will be retrieved from session)' }
          },
          required: ['rate_uuid']
        }
      },
      {
        name: 'getUserPaymentCards',
        description: 'Get user payment cards list for shipping order payment. Use when user needs to select a payment method for shipping order or wants to view their saved payment cards or needs to select a pay_card_id for shipping order creation. Examples: "Show my payment cards", "Get my saved payment methods", "What payment cards do I have?"',
        parameters: {
          type: 'object',
          properties: {}
        }
      },
      {
        name: 'getCustomerList',
        description: 'Get customer list for invoice management and dispute processing',
        parameters: {
          type: 'object',
          properties: {
            refresh_cache: { type: 'boolean', description: 'Whether to refresh cache and force reload data', default: false }
          }
        }
      },
      {
        name: 'getInvoiceList',
        description: 'Get invoice list for a specific customer',
        parameters: {
          type: 'object',
          properties: {
            customer_id: { type: 'string', description: 'Customer ID to get invoices for' }
          },
          required: ['customer_id']
        }
      },
      {
        name: 'getInvoiceDetail',
        description: 'Get detailed information for a specific invoice including line items. Use when user wants to view detailed invoice information or needs invoice details for dispute submission. Examples: "Get invoice detail for INV001", "Show me the details of invoice INV001", "What\'s in invoice INV001?"',
        parameters: {
          type: 'object',
          properties: {
            invoice_number: { type: 'string', description: 'Invoice number to query' },
            customer_code: { type: 'string', description: 'Customer code for the invoice' }
          },
          required: ['invoice_number', 'customer_code']
        }
      },
      {
        name: 'submitPreparedDispute',
        description: 'Submit prepared dispute for an invoice with complete customer and invoice information',
        parameters: {
          type: 'object',
          properties: {
            customer_id: { type: 'string', description: 'Customer ID' },
            customer_code: { type: 'string', description: 'Customer code' },
            customer_name: { type: 'string', description: 'Customer name' },
            invoice_number: { type: 'string', description: 'Invoice number to dispute' },
            dispute_type: { type: 'string', description: 'Type of dispute' },
            dispute_reason: { type: 'string', description: 'Reason for dispute' },
            dispute_items: { type: 'array', items: { type: 'object' }, description: 'Dispute items (optional)' }
          },
          required: ['customer_id', 'customer_code', 'customer_name', 'invoice_number', 'dispute_type', 'dispute_reason']
        }
      },
      {
        name: 'trackOrder',
        description: 'Track order status and details through Portal API. Use when user wants to track an order or query an order. Examples: "Track order DO00036423", "Get order status for DO00036423", "Query order DO00036423", "Query order status for DO00036423"',
        parameters: {
          type: 'object',
          properties: {
            searchKeyword: { type: 'string', description: 'Order number or tracking number to search' },
            page: { type: 'number', description: 'Page number for pagination', default: 1 },
            limit: { type: 'number', description: 'Number of items per page', default: 10 },
            statusType: { type: 'number', description: 'Status type filter (-1 for all)', default: -1 },
            orderType: { type: 'array', items: { type: 'number' }, description: 'Order type filter (1 for LTL, 2 for FTL)', default: [1, 2] }
          },
          required: ['searchKeyword']
        }
      },
      {
        name: 'createShippingOrder',
        description: 'Create shipping order with saved quote and payment information. Use when user wants to create a shipping order after saving a quote and selecting payment method. Examples: "Create shipping order with my saved quote", "Place order with payment card 123", "Create order for my shipment"',
        parameters: {
          type: 'object',
          properties: {
            quote_id: { type: 'number', description: 'Quote ID (required)' },
            pay_card_id: { type: 'number', description: 'Payment card ID (required)' },
            pay_amount: { type: 'number', description: 'Payment amount (required)' },
            shipper_name: { type: 'string', description: 'Shipper name (required)' },
            shipper_phone: { type: 'string', description: 'Shipper phone (required)' },
            shipper_email: { type: 'string', description: 'Shipper email (required)' },
            shipper_street: { type: 'string', description: 'Shipper street address (required)' },
            shipper_city: { type: 'string', description: 'Shipper city (required)' },
            shipper_state: { type: 'string', description: 'Shipper state (required)' },
            shipper_zip: { type: 'string', description: 'Shipper ZIP code (required)' },
            consignee_name: { type: 'string', description: 'Consignee name (required)' },
            consignee_phone: { type: 'string', description: 'Consignee phone (required)' },
            consignee_email: { type: 'string', description: 'Consignee email (required)' },
            consignee_street: { type: 'string', description: 'Consignee street address (required)' },
            consignee_city: { type: 'string', description: 'Consignee city (required)' },
            consignee_state: { type: 'string', description: 'Consignee state (required)' },
            consignee_zip: { type: 'string', description: 'Consignee ZIP code (required)' },
            pickup_date: { type: 'string', description: 'Pickup date (format: YYYY-MM-DD)' },
            pickup_time_earliest: { type: 'string', description: 'Earliest pickup time (format: YYYY-MM-DD HH:MM:SS)' },
            pickup_time_latest: { type: 'string', description: 'Latest pickup time (format: YYYY-MM-DD HH:MM:SS)' },
            order_type: { type: 'number', description: 'Order type (optional, default 1)' },
            need_quote: { type: 'boolean', description: 'Whether quote is needed (optional, default true)' },
            additional_quote_lines: { 
              type: 'array', 
              items: { 
                type: 'object',
                properties: {
                  code: { type: 'string', description: 'Service code (e.g., "LGPUP", "APPTD")' },
                  name: { type: 'string', description: 'Service name (e.g., "LIFTGATE PICKUP", "APPOINTMENT REQUIRED")' },
                  qty: { type: 'number', description: 'Quantity for the service (usually 0 for boolean services)' }
                }
              }, 
              description: 'Additional quote lines for special services (optional). Example: [{"code":"LGPUP","name":"LIFTGATE PICKUP","qty":0},{"code":"APPTD","name":"APPOINTMENT REQUIRED","qty":0}]' 
            }
          },
          required: ['quote_id', 'pay_card_id', 'pay_amount', 'shipper_name', 'shipper_phone', 'shipper_email', 'shipper_street', 'shipper_city', 'shipper_state', 'shipper_zip', 'consignee_name', 'consignee_phone', 'consignee_email', 'consignee_street', 'consignee_city', 'consignee_state', 'consignee_zip']
        }
      },
      {
        name: 'getUtCustomers',
        description: 'Get the ut_customers (UT customer code list) for the current user',
        parameters: {
          type: 'object',
          properties: {}
        }
      },
      {
        name: 'getUfCustomers',
        description: 'Get the uf_customers (UF customer code list) for the current user',
        parameters: {
          type: 'object',
          properties: {}
        }
      },
      {
        name: 'getAccList',
        description: 'Get supported additional services (ACC list) for shipping quotes and orders. Use when before creating quotes that need additional services (liftgate, inside delivery, etc.) or when user asks about available services. Examples: "What additional services are available?", "Do you support liftgate service?"',
        parameters: {
          type: 'object',
          properties: {}
        }
      },
      {
        name: 'getUfCustomers4Iam',
        description: 'Get UF customers list through IAM API /v1/web/user/info',
        parameters: {
          type: 'object',
          properties: {}
        }
      },
      {
        name: 'getUtCustomers4Iam',
        description: 'Get UT customers list through IAM API /v1/web/user/info',
        parameters: {
          type: 'object',
          properties: {}
        }
      }
    ];
  }
  
  async getPortalQuote(params: any) {
    return await portalQuoteTool.execute(params, { toolCallId: this.context.toolCallId, messages: [] });
  }

  async saveShippingQuote(params: any) {
    return await portalSaveShippingQuoteTool.execute(params, { toolCallId: this.context.toolCallId, messages: [] });
  }

  async getUserPaymentCards(params: any) {
    return await portalGetUserPaymentCardsTool.execute(params, { toolCallId: this.context.toolCallId, messages: [] });
  }

  async getCustomerList(params: any) {
    return await portalGetCustomerListTool.execute(params, { toolCallId: this.context.toolCallId, messages: [] });
  }

  async getInvoiceList(params: any) {
    return await portalGetInvoiceListTool.execute(params, { toolCallId: this.context.toolCallId, messages: [] });
  }

  async getInvoiceDetail(params: any) {
    return await portalGetInvoiceDetailTool.execute(params, { toolCallId: this.context.toolCallId, messages: [] });
  }

  async submitPreparedDispute(params: any) {
    return await portalSubmitPreparedDisputeTool.execute(params, { toolCallId: this.context.toolCallId, messages: [] });
  }

  async createShippingOrder(params: any) {
    return await portalCreateShippingOrderTool.execute(params, { toolCallId: this.context.toolCallId, messages: [] });
  }

  async trackOrder(params: any) {
    return await portalTrackingOrderTool.execute(params, { toolCallId: this.context.toolCallId, messages: [] });
  }

  async getUtCustomers(params: any) {
    return await portalUtCustomersTool.execute(params, { toolCallId: this.context.toolCallId, messages: [] });
  }

  async getUfCustomers(params: any) {
    return await portalUfCustomersTool.execute(params, { toolCallId: this.context.toolCallId, messages: [] });
  }

  async getAccList(params: any) {
    return await portalAccListTool.execute(params, { toolCallId: this.context.toolCallId, messages: [] });
  }

  async getUfCustomers4Iam(params: any) {
    return await getUfCustomers4IamTool.execute(params, { toolCallId: this.context.toolCallId, messages: [] });
  }

  async getUtCustomers4Iam(params: any) {
    return await getUtCustomers4IamTool.execute(params, { toolCallId: this.context.toolCallId, messages: [] });
  }

  static getSystemPrompt(): string {
    return `You can use the Portal tools to manage invoices, calculate shipping quotes, and track orders.

Available tools:
1. getPortalQuote - Calculate shipping quotes through Portal API for freight transportation
2. saveShippingQuote - Save selected shipping quote for order creation
3. getUserPaymentCards - Get user payment cards list for shipping order payment
4. getCustomerList - Get customer list for invoice management and dispute processing
5. getInvoiceList - Get invoice list for a specific customer
6. getInvoiceDetail - Get detailed information for a specific invoice
7. submitPreparedDispute - Submit prepared dispute for an invoice with complete customer and invoice information
8. createShippingOrder - Create shipping order with saved quote and payment information
9. trackOrder - Track order status and details through Portal API
10. getUtCustomers - Get the ut_customers (UT customer code list) for the current user
11. getUfCustomers - Get the uf_customers (UF customer code list) for the current user
12. getAccList - Get supported additional services (ACC list) for shipping quotes and orders
13. getUfCustomers4Iam - Get UF customers list through IAM API /v1/web/user/info
14. getUtCustomers4Iam - Get UT customers list through IAM API /v1/web/user/info

For getPortalQuote:
- Calculates freight shipping quotes between origin and destination
- Requires shipper and consignee information (addresses, ZIP codes, etc.)
- Requires manifest details (weight, dimensions, freight class, etc.)
- Returns detailed pricing information

For saveShippingQuote:
- Save selected quote for order creation
- Requires rate_uuid from quote results
- Optional quote_id and token parameters
- Authentication required

For getUserPaymentCards:
- Retrieve available payment options for order processing
- Authentication required
- Returns list of payment cards with details

For getCustomerList:
- Get customer list for invoice management and dispute processing
- Optional refresh_cache parameter to force reload data
- Returns formatted customer information with IDs, names, and contact details
- Authentication required

For getInvoiceList:
- Get invoice list for a specific customer
- Requires customer_id parameter
- Returns formatted invoice information with numbers, amounts, dates, and status
- Authentication required

For getInvoiceDetail:
- Get detailed information for a specific invoice
- Requires invoice_number and customer_code
- Returns invoice details including line items
- Authentication required

For submitPreparedDispute:
- Submit prepared dispute for an invoice with complete customer and invoice information
- Requires customer_id, customer_code, customer_name, invoice_number, dispute_type, dispute_reason
- Optional dispute_items parameter
- Authentication required

For createShippingOrder:
- Create shipping order with complete shipper and consignee information
- Requires quote_id, pay_card_id, pay_amount, and complete address details
- Optional appointment scheduling and order type configuration
- Authentication required

For trackOrder:
- Search and track orders by order number or tracking number
- Get detailed order information including pickup/delivery details
- View order status and GPS tracking information (if available)
- Supports pagination and filtering by order type

For getUtCustomers:
- Get the ut_customers (UT customer code list) for the current user
- Returns list of UT customer codes available to the user
- No parameters required
- Authentication required

For getUfCustomers:
- Get the uf_customers (UF customer code list) for the current user
- Returns list of UF customer codes available to the user
- No parameters required
- Authentication required

For getAccList:
- Get supported additional services (ACC list) for shipping quotes and orders
- Returns comprehensive list of available additional services with codes and descriptions
- Includes popular services like liftgate pickup/delivery, inside delivery, appointment required, etc.
- No parameters required
- No authentication required
- Use returned service codes in additional_quote_lines parameter for getPortalQuote and createShippingOrder

For getUfCustomers4Iam:
- Get UF customers list through IAM API /v1/web/user/info
- Returns list of UF customer codes available to the user from IAM system
- No parameters required
- Authentication required
- Uses GET method to call /v1/web/user/info endpoint

For getUtCustomers4Iam:
- Get UT customers list through IAM API /v1/web/user/info
- Returns list of UT customer codes available to the user from IAM system
- No parameters required
- Authentication required
- Uses GET method to call /v1/web/user/info endpoint

Example queries:
- "Get a quote for shipping from Los Angeles, CA to San Francisco, CA"
- "Save the selected shipping quote with rate_uuid 3542493a-cdd4-40fc-a830-fe14b72c1cc1"
- "Get my payment cards for shipping order"
- "Get customer list for invoice management"
- "Get invoice list for customer ID 123"
- "Get invoice detail for invoice INV001 with customer code CUST001"
- "Submit dispute for invoice INV001 with customer ID 123, customer code CUST001, customer name ABC Corp, dispute type pricing, and reason 'Incorrect pricing applied'"
- "Create shipping order with my saved quote"
- "Track order DO00036423"
- "Get status for tracking number DO00036423"
- "Get available additional services for shipping"
- "Show me the list of supported additional services"
- "Get UF customers from IAM API"
- "Get UT customers from IAM API"`;
  }
}

/**
 * 保存运输报价工具
 */
export const portalSaveShippingQuoteTool = tool({
  description: 'Save selected shipping quote for order creation. Use when user wants to save a specific quote from the quote results for creating a shipping order. Examples: "Save the quote with rate_uuid 3542493a-cdd4-40fc-a830-fe14b72c1cc1", "Save this shipping quote", "Select this rate for my order"',
  parameters: z.object({
    rate_uuid: z.string().describe('Carrier quote unique identifier (from portalQuoteTool results)'),
    quote_id: z.number().optional().describe('Quote ID (optional, defaults to 0)').default(0),
    token: z.string().describe('Quote token (required from portalQuoteTool result)')
  }),
  execute: async (params, options: { toolCallId: string; messages: any[] }) => {
    console.log('PortalSaveShippingQuoteTool.execute called with args:', JSON.stringify(params));
    
    try {
      // 1. 参数验证
      if (!params.rate_uuid || params.rate_uuid.trim() === "") {
        throw new Error('Carrier quote UUID cannot be empty, please provide a valid rate_uuid');
      }
      
      // 1.5. UUID格式验证
      const rate_uuid_clean = params.rate_uuid.trim();
      try {
        // 验证UUID格式
        const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
        if (!uuidRegex.test(rate_uuid_clean)) {
          throw new Error('Invalid UUID format');
        }
        console.log(`UUID format validation passed: ${rate_uuid_clean}`);
      } catch (error) {
        console.warn(`Invalid UUID format: ${rate_uuid_clean}`);
        throw new Error(`Invalid rate_uuid format: '${rate_uuid_clean}'. UUID format should be: xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx (36 characters)`);
      }
      
      // 从环境变量获取Portal基础URL
      const baseUrl = process.env.PORTAL_BASEURL || 'https://portal-staging.item.com';
      const apiUrl = `${baseUrl}/api/v1/web/rate-shopping/save`;
      
      // 从工具调用ID中提取请求ID
      const requestId = extractRequestIdFromToolCallId(options.toolCallId);
      const requestContext = getRequestContext(requestId);
      
      // 2. 获取quote响应中的token - 优先使用传入的token
      let quote_token = params.token;
      if (!quote_token) {
        // 这里应该从session中获取token，但在这个实现中我们暂时使用一个占位符
        // 实际实现中需要从session或context中获取
        console.warn('Quote token not provided and session retrieval not implemented');
        throw new Error('No valid quote token found, please call get_shipping_quote to get a quote first');
      }
      
      console.log(`Using quote token: ${params.token ? 'provided as parameter' : 'from session'} - ${quote_token}`);
      
      // 3. 构建保存报价的请求参数
      const requestBody = {
        token: quote_token,
        quote_id: params.quote_id || 0,
        rate_uuid: rate_uuid_clean
      };
      
      console.log('PortalSaveShippingQuoteTool request body:', JSON.stringify(requestBody));
      
      // 4. 发送API请求
      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: buildPortalHeaders(requestContext),
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      
      console.log('PortalSaveShippingQuoteTool.execute response:', JSON.stringify(data));
      
      // 5. 处理响应结果
      if (data.code === 0) {
        // 保存成功，提取返回的重要信息
        const saved_data = data.data || {};
        const actual_quote_id = saved_data.quote_id;
        const actual_rate_id = saved_data.rate_id;
        
        return {
          success: true,
          message: 'Shipping quote saved successfully',
          data: {
            saved_quote: saved_data,
            rate_uuid: rate_uuid_clean,
            quote_id: actual_quote_id || params.quote_id,
            rate_id: actual_rate_id,
            session_state: {
              has_saved_quote: true,
              quote_id: actual_quote_id || params.quote_id,
              rate_uuid: rate_uuid_clean,
              rate_id: actual_rate_id
            },
            next_step: 'Can get payment card list and create shipping order'
          },
          timestamp: new Date().toISOString()
        };
      } else {
        // 保存失败
        throw new Error(`Failed to save quote: ${data.msg || 'Unknown error'}`);
      }
      
    } catch (error) {
      console.error('PortalSaveShippingQuoteTool.execute error:', error);
      
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        timestamp: new Date().toISOString()
      };
    }
  }
});

/**
 * 获取用户支付卡列表工具
 */
export const portalGetUserPaymentCardsTool = tool({
  description: 'Get user payment cards list for shipping order payment. Use when user needs to select a payment method for shipping order or wants to view their saved payment cards or needs to select a pay_card_id for shipping order creation. ',
  parameters: z.object({}),
  execute: async (params, options: { toolCallId: string; messages: any[] }) => {
    console.log('PortalGetUserPaymentCardsTool.execute called with args:', JSON.stringify(params));
    
    try {
      // 1. 检查用户认证状态
      const requestId = extractRequestIdFromToolCallId(options.toolCallId);
      const requestContext = getRequestContext(requestId);
      
      // 2. 调用获取支付卡列表API
      const baseUrl = process.env.PORTAL_BASEURL || 'https://portal-staging.item.com';
      const apiUrl = `${baseUrl}/api/v1/web/user/card/list`;
      
      console.log('Getting user payment cards from:', apiUrl);
      
      const response = await fetch(apiUrl, {
        method: 'GET',
        headers: buildPortalHeaders(requestContext)
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      
      console.log('PortalGetUserPaymentCardsTool.execute response:', JSON.stringify(data));
      
      // 3. 处理响应结果
      if (data.code === 0) {
        // 获取成功，处理支付卡数据
        const api_response = data.data || {};
        
        // API响应格式: {"code": 0, "msg": "ok", "data": {"data": [cards], "total": 6}}
        // 提取实际的卡片数据
        let cards_list = [];
        if (typeof api_response === 'object') {
          if (api_response.code === 0 && api_response.data) {
            // 标准API响应格式
            const cards_data = api_response.data;
            if (typeof cards_data === 'object' && cards_data.data) {
              cards_list = cards_data.data; // 实际的卡片列表在data.data中
            }
          } else {
            // 直接的卡片数据
            cards_list = api_response.cards || api_response.data || [];
          }
        } else if (Array.isArray(api_response)) {
          // 如果api_response直接是列表
          cards_list = api_response;
        }
        
        // 格式化支付卡信息
        const formatted_cards = [];
        for (const card of cards_list) {
          if (typeof card === 'object') {
            // 从实际API响应中提取字段
            const card_id = card.id;
            const card_holder = card.card_holder || '';
            const card_num = card.card_num || '';
            const card_type = card.card_type || 'Unknown';
            const card_default = card.card_default || 0;
            const expiration_date = card.expiration_date || 0;
            const cvv = card.cvv || '';
            
            // 提取卡号后四位
            let last_four = '****';
            if (card_num && card_num.length >= 4) {
              // 处理带空格或星号的卡号格式
              const clean_card_num = card_num.replace(/[\s*]/g, '');
              if (clean_card_num.length >= 4) {
                last_four = clean_card_num.slice(-4);
              }
            }
            
            // 处理过期时间 (Unix timestamp 转换为 MM/YYYY 格式)
            let expiry = 'XX/XXXX';
            if (expiration_date && expiration_date > 0) {
              try {
                const exp_datetime = new Date(expiration_date * 1000);
                expiry = `${String(exp_datetime.getMonth() + 1).padStart(2, '0')}/${exp_datetime.getFullYear()}`;
              } catch (error) {
                console.warn('Error parsing expiration date:', error);
              }
            }
            
            const formatted_card = {
              card_id: card_id,
              card_holder: card_holder,
              card_type: card_type,
              last_four: last_four,
              full_card_display: card_num, // 保留原始显示格式
              brand: card_type, // 使用card_type作为brand
              is_default: Boolean(card_default === 1),
              expiry: expiry,
              cvv_masked: cvv ? '***' : '',
              billing_address: card.billing_address || '',
              zip_code: card.zip_code || '',
              city: card.city || '',
              state: card.state || ''
            };
            formatted_cards.push(formatted_card);
          }
        }
        
        // 检查是否有默认卡
        const has_default = formatted_cards.some(card => card.is_default);
        const default_card = formatted_cards.find(card => card.is_default);
        
        console.log(`Successfully parsed ${formatted_cards.length} payment cards`);
        
        return {
          success: true,
          message: `Successfully retrieved ${formatted_cards.length} payment cards`,
          data: {
            cards: formatted_cards,
            total_cards: formatted_cards.length,
            has_default: has_default,
            default_card: default_card,
            next_step: formatted_cards.length > 0 ? 'Select payment card and create shipping order' : 'Please add payment card first'
          },
          timestamp: new Date().toISOString()
        };
      } else {
        // 获取失败
        throw new Error(`Failed to get payment card list: ${data.msg || 'Unknown error'}`);
      }
      
    } catch (error) {
      console.error('PortalGetUserPaymentCardsTool.execute error:', error);
      
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        timestamp: new Date().toISOString()
      };
    }
  }
});

/**
 * 创建运输订单工具
 */
export const portalCreateShippingOrderTool = tool({
  description: 'Create shipping order with saved quote and payment information. Use when user wants to create a shipping order after saving a quote and selecting payment method. Examples: "Create shipping order with my saved quote", "Place order with payment card 123", "Create order for my shipment"',
  parameters: z.object({
    quote_id: z.number().describe('Quote ID (required from portalSaveShippingQuoteTool results)'),
    pay_card_id: z.number().describe('Payment card ID (required) must explicitly selected by the user from the payment card list(portalGetUserPaymentCardsTool results). Never auto-fill or default this value.'),
    pay_amount: z.number().describe('Payment amount (required)'),
    shipper_name: z.string().describe('Shipper name (required)'),
    shipper_phone: z.string().describe('Shipper phone (required)'),
    shipper_email: z.string().describe('Shipper email (required)'),
    shipper_street: z.string().describe('Shipper street address (required)'),
    shipper_city: z.string().describe('Shipper city (required)'),
    shipper_state: z.string().describe('Shipper state (required)'),
    shipper_zip: z.string().describe('Shipper ZIP code (required)'),
    consignee_name: z.string().describe('Consignee name (required)'),
    consignee_phone: z.string().describe('Consignee phone (required)'),
    consignee_email: z.string().describe('Consignee email (required)'),
    consignee_street: z.string().describe('Consignee street address (required)'),
    consignee_city: z.string().describe('Consignee city (required)'),
    consignee_state: z.string().describe('Consignee state (required)'),
    consignee_zip: z.string().describe('Consignee ZIP code (required)'),
    pickup_date: z.string().describe('Pickup date (format: YYYY-MM-DD) must be greater than or equal to tomorrow date'),
    pickup_time_earliest: z.string().describe('Earliest pickup time (format: YYYY-MM-DD HH:MM:SS) must be greater than or equal to tomorrow date'),
    pickup_time_latest: z.string().describe('Latest pickup time (format: YYYY-MM-DD HH:MM:SS) must be greater than or equal to tomorrow date'),
    order_type: z.number().optional().describe('Order type (optional, default 1)'),
    need_quote: z.boolean().optional().describe('Whether quote is needed (optional, default true)'),
    additional_quote_lines: z.array(z.object({
      code: z.string().describe('Service code (e.g., "LGPUP", "APPTD")'),
      name: z.string().describe('Service name (e.g., "LIFTGATE PICKUP", "APPOINTMENT REQUIRED")'),
      qty: z.number().describe('Quantity for the service (usually 0 for boolean services)')
    })).optional().describe('Additional quote lines for special services (optional). Example: [{"code":"LGPUP","name":"LIFTGATE PICKUP","qty":0},{"code":"APPTD","name":"APPOINTMENT REQUIRED","qty":0}]')
  }),
  execute: async (params, options: { toolCallId: string; messages: any[] }) => {
    console.log('PortalCreateShippingOrderTool.execute called with args:', JSON.stringify(params));
    
    try {
      // 1. 检查用户认证状态
      const requestId = extractRequestIdFromToolCallId(options.toolCallId);
      const requestContext = getRequestContext(requestId);
      const timezone = requestContext?.timezone;
      // 1.5. 智能处理quote_id - 如果为0或None，尝试从状态管理中获取
      let final_quote_id = params.quote_id;
      if (params.quote_id === undefined || params.quote_id === null || params.quote_id === 0) {
        // 这里应该从session中获取saved_quote_id，但在这个实现中我们暂时使用一个占位符
        // 实际实现中需要从session或context中获取
        console.warn('Quote ID not provided and session retrieval not implemented');
        throw new Error('No valid quote ID found. Please get shipping quote and save selected carrier quote first.');
      }
      
      // 2. 参数验证
      const validation_result = validateOrderParams(
        final_quote_id!, params.pay_card_id, params.pay_amount,
        params.shipper_name, params.shipper_phone, params.shipper_email, params.shipper_street, params.shipper_city, params.shipper_state, params.shipper_zip,
        params.consignee_name, params.consignee_phone, params.consignee_email, params.consignee_street, params.consignee_city, params.consignee_state, params.consignee_zip,
        params.pickup_date, params.pickup_time_earliest, params.pickup_time_latest, timezone
      );
      if (!validation_result.valid) {
        throw new Error(validation_result.error);
      }
      
      // 3. 构建订单数据
      const order_data = buildOrderData(
        final_quote_id!, params.pay_card_id, params.pay_amount,
        params.shipper_name, params.shipper_phone, params.shipper_email, params.shipper_street, params.shipper_city, params.shipper_state, params.shipper_zip,
        params.consignee_name, params.consignee_phone, params.consignee_email, params.consignee_street, params.consignee_city, params.consignee_state, params.consignee_zip,
        params.pickup_date, params.pickup_time_earliest, params.pickup_time_latest, params.order_type, params.need_quote, params.additional_quote_lines
      );
      
      // 4. 调用创建订单API
      const baseUrl = process.env.PORTAL_BASEURL || 'https://portal-staging.item.com';
      const apiUrl = `${baseUrl}/api/v1/app/order/create`;
      
      console.log('Creating shipping order with quote_id:', final_quote_id);
      console.log('Order data:', JSON.stringify(order_data));
      
      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: buildPortalHeaders(requestContext),
        body: JSON.stringify(order_data)
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      
      console.log('PortalCreateShippingOrderTool.execute response:', JSON.stringify(data));
      
      // 5. 处理响应结果
      if (data.code === 0) {
        // 订单创建成功
        const order_result = data.data || {};
        
        return {
          success: true,
          message: 'Shipping order created successfully!',
          data: {
            order_info: order_result,
            order_id: order_result.order_id || order_result.id,
            quote_id: final_quote_id,
            pay_amount: params.pay_amount,
            payment_card: params.pay_card_id,
            shipper: {
              name: params.shipper_name,
              city: params.shipper_city,
              state: params.shipper_state,
              zip: params.shipper_zip
            },
            consignee: {
              name: params.consignee_name,
              city: params.consignee_city,
              state: params.consignee_state,
              zip: params.consignee_zip
            },
            next_step: 'Order created, can track order status and logistics information'
          },
          timestamp: new Date().toISOString()
        };
      } else {
        // 订单创建失败
        throw new Error(`Failed to create shipping order: ${data.msg || 'Unknown error'}`);
      }
      
    } catch (error) {
      console.error('PortalCreateShippingOrderTool.execute error:', error);
      
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        timestamp: new Date().toISOString()
      };
    }
  }
});

// 辅助函数：验证订单参数
function validateOrderParams(
  quote_id: number, pay_card_id: number, pay_amount: number,
  shipper_name: string, shipper_phone: string, shipper_email: string,
  shipper_street: string, shipper_city: string, shipper_state: string, shipper_zip: string,
  consignee_name: string, consignee_phone: string, consignee_email: string,
  consignee_street: string, consignee_city: string, consignee_state: string, consignee_zip: string,
  pickup_date?: string, pickup_time_earliest?: string, pickup_time_latest?: string,
  timezone?: string
): { valid: boolean; error?: string } {
  // 1. 验证基础数字参数
  if (quote_id === undefined || quote_id === null || quote_id <= 0) {
    return { valid: false, error: 'Quote ID must be a valid positive integer' };
  }
  
  if (pay_card_id === undefined || pay_card_id === null || pay_card_id <= 0) {
    return { valid: false, error: 'Payment card ID must be a valid positive integer' };
  }
  
  if (pay_amount === undefined || pay_amount === null || pay_amount <= 0) {
    return { valid: false, error: 'Payment amount must be greater than 0' };
  }
  
  // 2. 验证发货人信息
  const shipper_fields = {
    'Shipper name': shipper_name,
    'Shipper phone': shipper_phone,
    'Shipper email': shipper_email,
    'Shipper street address': shipper_street,
    'Shipper city': shipper_city,
    'Shipper state': shipper_state,
    'Shipper ZIP code': shipper_zip
  };
  
  for (const [field_name, field_value] of Object.entries(shipper_fields)) {
    if (!field_value || String(field_value).trim() === '') {
      return { valid: false, error: `${field_name} cannot be empty` };
    }
  }
  
  // 3. 验证收货人信息
  const consignee_fields = {
    'Consignee name': consignee_name,
    'Consignee phone': consignee_phone,
    'Consignee email': consignee_email,
    'Consignee street address': consignee_street,
    'Consignee city': consignee_city,
    'Consignee state': consignee_state,
    'Consignee ZIP code': consignee_zip
  };
  
  for (const [field_name, field_value] of Object.entries(consignee_fields)) {
    if (!field_value || String(field_value).trim() === '') {
      return { valid: false, error: `${field_name} cannot be empty` };
    }
  }
  
  // 4. 验证邮编格式
  const zip_pattern = /^\d{5}$/;
  if (!zip_pattern.test(String(shipper_zip).trim())) {
    return { valid: false, error: `Shipper ZIP code format error, must be 5 digits: '${shipper_zip}'` };
  }
  if (!zip_pattern.test(String(consignee_zip).trim())) {
    return { valid: false, error: `Consignee ZIP code format error, must be 5 digits: '${consignee_zip}'` };
  }
  
  // 5. 验证邮箱格式
  const email_pattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
  if (!email_pattern.test(String(shipper_email).trim())) {
    return { valid: false, error: `Shipper email format error: '${shipper_email}'` };
  }
  if (!email_pattern.test(String(consignee_email).trim())) {
    return { valid: false, error: `Consignee email format error: '${consignee_email}'` };
  }
  
  // 6. 验证电话格式（美国电话号码）
  const phone_pattern = /^\d{10,11}$/;
  if (!phone_pattern.test(String(shipper_phone).replace(/[^\d]/g, ''))) {
    return { valid: false, error: `Shipper phone format error, should be 10-11 digits: '${shipper_phone}'` };
  }
  if (!phone_pattern.test(String(consignee_phone).replace(/[^\d]/g, ''))) {
    return { valid: false, error: `Consignee phone format error, should be 10-11 digits: '${consignee_phone}'` };
  }
  
  // 7. 验证取货时间参数
  if (pickup_date || pickup_time_earliest || pickup_time_latest) {
    // 根据时区获取当前日期字符串（YYYY-MM-DD格式）
    const today = getCurrentDateByTimezone(timezone);
    console.log('pickup_date:', pickup_date, 'pickup_time_earliest:', pickup_time_earliest, 'pickup_time_latest:', pickup_time_latest, 'today:', today, 'timezone:', timezone);
    
    // 验证 pickup_date - 校验格式和是否大于当前日期
    if (pickup_date) {
      try {
        // 验证日期格式 YYYY-MM-DD
        const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
        if (!dateRegex.test(pickup_date)) {
          return { valid: false, error: `Invalid pickup date format: '${pickup_date}'. Expected format: YYYY-MM-DD` };
        }
        
        // 验证日期是否大于等于今天
        if (pickup_date <= today) {
          return { valid: false, error: `Pickup date must be greater than or equal to today (${today}) in timezone ${timezone || 'local'}` };
        }
      } catch (error) {
        return { valid: false, error: `Invalid pickup date format: '${pickup_date}'. Expected format: YYYY-MM-DD` };
      }
    }
    
    // 验证 pickup_time_earliest - 校验格式和必须大于等于pickup_date
    if (pickup_time_earliest) {
      try {
        // 验证时间格式 YYYY-MM-DD HH:MM:SS
        const timeRegex = /^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/;
        if (!timeRegex.test(pickup_time_earliest)) {
          return { valid: false, error: `Invalid earliest pickup time format: '${pickup_time_earliest}'. Expected format: YYYY-MM-DD HH:MM:SS` };
        }
        
        // 如果提供了pickup_date，验证earliest时间必须大于等于pickup_date
        if (pickup_date) {
          const earliestDate = pickup_time_earliest.split(' ')[0];
          if (earliestDate < pickup_date) {
            return { valid: false, error: `Earliest pickup time date must be greater than or equal to pickup date (${pickup_date})` };
          }
        }
      } catch (error) {
        return { valid: false, error: `Invalid earliest pickup time format: '${pickup_time_earliest}'. Expected format: YYYY-MM-DD HH:MM:SS` };
      }
    }
    
    // 验证 pickup_time_latest - 校验格式和必须大于等于pickup_time_earliest
    if (pickup_time_latest) {
      try {
        // 验证时间格式 YYYY-MM-DD HH:MM:SS
        const timeRegex = /^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/;
        if (!timeRegex.test(pickup_time_latest)) {
          return { valid: false, error: `Invalid latest pickup time format: '${pickup_time_latest}'. Expected format: YYYY-MM-DD HH:MM:SS` };
        }
        
        // 如果同时提供了 earliest 和 latest，验证 latest 必须大于等于 earliest
        if (pickup_time_earliest) {
          if (pickup_time_latest < pickup_time_earliest) {
            return { valid: false, error: `Latest pickup time must be greater than or equal to earliest pickup time` };
          }
        }
      } catch (error) {
        return { valid: false, error: `Invalid latest pickup time format: '${pickup_time_latest}'. Expected format: YYYY-MM-DD HH:MM:SS` };
      }
    }
  }
  
  return { valid: true };
}

// 辅助函数：构建订单数据
function buildOrderData(
  quote_id: number, pay_card_id: number, pay_amount: number,
  shipper_name: string, shipper_phone: string, shipper_email: string,
  shipper_street: string, shipper_city: string, shipper_state: string, shipper_zip: string,
  consignee_name: string, consignee_phone: string, consignee_email: string,
  consignee_street: string, consignee_city: string, consignee_state: string, consignee_zip: string,
  appointment_date?: string, appointment_time?: string, appointment_time_to?: string,
  order_type?: number, need_quote?: boolean, additional_quote_lines?: Array<{
    code: string;
    name: string;
    qty: number;
  }>
): any {
  // 默认配置
  const order_defaults = {
    order_type: 1,
    need_quote: true,
    appointment_hours_ahead: 2
  };
  
  // 使用配置中的默认值或参数值
  const final_order_type = order_type !== undefined ? order_type : order_defaults.order_type;
  const final_need_quote = need_quote !== undefined ? need_quote : order_defaults.need_quote;
  const final_additional_lines = additional_quote_lines || [];
  
  // 处理预约时间，如果未提供则生成默认值
  let final_appointment_date = appointment_date;
  let final_appointment_time = appointment_time;
  let final_appointment_time_to = appointment_time_to;
  
  if (!final_appointment_date) {
    // 默认预约明天
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    final_appointment_date = tomorrow.toISOString().split('T')[0]; // YYYY-MM-DD
  }
  
  if (!final_appointment_time) {
    // 默认预约时间：明天的当前时间+默认提前小时数
    const base_time = new Date();
    base_time.setDate(base_time.getDate() + 1);
    base_time.setHours(base_time.getHours() + order_defaults.appointment_hours_ahead);
    final_appointment_time = base_time.toISOString().slice(0, 19).replace('T', ' '); // YYYY-MM-DD HH:MM:SS
  }

  if(!final_appointment_time_to) {
    // 如果提供了预约时间，结束时间为开始时间+2小时
    try {
      const start_time = new Date(final_appointment_time.replace(' ', 'T'));
      const end_time = new Date(start_time);
      end_time.setHours(end_time.getHours() + 2);
      final_appointment_time_to = end_time.toISOString().slice(0, 19).replace('T', ' ');
    } catch (error) {
      // 如果时间格式错误，使用默认逻辑
      const base_time = new Date();
      const end_time = new Date(base_time);
      end_time.setHours(end_time.getHours() + 2);
      final_appointment_time_to = end_time.toISOString().slice(0, 19).replace('T', ' ');
    }
  }
  
  console.log('Time data for API call:');
  console.log('  appointment_date:', final_appointment_date);
  console.log('  appointment_time:', final_appointment_time);
  console.log('  appointment_time_to:', final_appointment_time_to);
  
  // 构建订单数据
  const order_data = {
    additional_quote_lines: final_additional_lines,
    appointment_date: final_appointment_date,
    appointment_time: final_appointment_time,
    appointment_time_to: final_appointment_time_to,
    consignee: {
      city: consignee_city.trim(),
      email: consignee_email.trim(),
      name: consignee_name.trim(),
      phone: String(consignee_phone).replace(/[^\d]/g, ''), // 仅保留数字
      state: consignee_state.trim(),
      street: consignee_street.trim(),
      zip: consignee_zip.trim()
    },
    order_type: final_order_type,
    pay_amount: Number(pay_amount),
    quote_id: Number(quote_id),
    pay_card_id: Number(pay_card_id),
    shipper: {
      city: shipper_city.trim(),
      email: shipper_email.trim(),
      name: shipper_name.trim(),
      phone: String(shipper_phone).replace(/[^\d]/g, ''), // 仅保留数字
      state: shipper_state.trim(),
      street: shipper_street.trim(),
      zip: shipper_zip.trim()
    },
    need_quote: final_need_quote
  };
  
  return order_data;
}

/**
 * 获取客户列表工具
 */
export const portalGetCustomerListTool = tool({
  description: 'Get customer list for invoice management and dispute processing',
  parameters: z.object({
    refresh_cache: z.boolean().optional().describe('Whether to refresh cache and force reload data').default(false)
  }),
  execute: async (params, options: { toolCallId: string; messages: any[] }) => {
    console.log('PortalGetCustomerListTool.execute called with args:', JSON.stringify(params));
    
    try {
      // 从环境变量获取Portal基础URL
      const baseUrl = process.env.PORTAL_BASEURL || 'https://portal-staging.item.com';
      const apiUrl = `${baseUrl}/api/v1/web/crm-hand-out/cpapi/tms/user/customer`;
      
      // 从工具调用ID中提取请求ID
      const requestId = extractRequestIdFromToolCallId(options.toolCallId);
      const requestContext = getRequestContext(requestId);
      
      // 准备POST请求数据 - 根据claim.py中的逻辑
      const requestData = {
        "ssoUsername": "<EMAIL>"  // 从文档中的示例数据
      };
      
      console.log('Getting customer list from:', apiUrl);
      
      // 发送API请求
      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: buildPortalHeaders(requestContext),
        body: JSON.stringify(requestData)
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      
      console.log('PortalGetCustomerListTool.execute response:', JSON.stringify(data));
      
      // 处理API响应
      if (data.code === 0) {
        // 获取成功，处理客户数据
        const customer_data = data.data || {};
        
        // 格式化客户列表响应
        const formatted_response = formatCustomerListResponse(customer_data);
        
        return {
          success: true,
          message: 'Successfully retrieved customer list',
          data: formatted_response,
          timestamp: new Date().toISOString()
        };
      } else {
        // 获取失败
        throw new Error(`Failed to get customer list: ${data.msg || 'Unknown error'}`);
      }
      
    } catch (error) {
      console.error('PortalGetCustomerListTool.execute error:', error);
      
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        timestamp: new Date().toISOString()
      };
    }
  }
});

/**
 * 获取发票列表工具
 */
export const portalGetInvoiceListTool = tool({
  description: 'Get invoice list for a specific customer',
  parameters: z.object({
    customer_id: z.string().describe('Customer ID to get invoices for')
  }),
  execute: async (params, options: { toolCallId: string; messages: any[] }) => {
    console.log('PortalGetInvoiceListTool.execute called with args:', JSON.stringify(params));
    
    try {
      // 1. 参数验证
      if (!params.customer_id || params.customer_id.trim() === "") {
        throw new Error('Customer ID cannot be empty');
      }
      
      // 从环境变量获取Portal基础URL
      const baseUrl = process.env.PORTAL_BASEURL || 'https://portal-staging.item.com';
      const apiUrl = `${baseUrl}/api/v1/web/crm-hand-out/bnp/invoice/list`;
      
      // 从工具调用ID中提取请求ID
      const requestId = extractRequestIdFromToolCallId(options.toolCallId);
      const requestContext = getRequestContext(requestId);
      
      // 注意：这里需要先获取客户信息，但为了简化实现，我们直接使用传入的customer_id
      // 在实际实现中，可能需要先调用客户列表API来获取完整的客户信息
      
      // 准备POST请求数据 - 根据claim.py中的逻辑
      const requestData = {
        "department": "UT",  // 固定值，根据文档
        "customerCode": "",  // 这里需要从客户信息中获取，暂时留空
        "customerId": params.customer_id,  // 客户ID
        "customerName": "",  // 这里需要从客户信息中获取，暂时留空
        "invoiceNumber": ""  // 空字符串表示获取所有发票
      };
      
      console.log('Getting invoice list for customer:', params.customer_id);
      console.log('Request data:', JSON.stringify(requestData));
      
      // 发送API请求
      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: buildPortalHeaders(requestContext),
        body: JSON.stringify(requestData)
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      
      console.log('PortalGetInvoiceListTool.execute response:', JSON.stringify(data));
      
      // 处理API响应
      if (data.code === 0) {
        // 获取成功，处理发票数据
        const api_data = data.data || {};
        const invoice_data = api_data.data || [];
        
        // 格式化发票列表响应
        const formatted_response = formatInvoiceListResponse({ invoices: invoice_data });
        
        return {
          success: true,
          message: 'Successfully retrieved invoice list',
          data: formatted_response,
          timestamp: new Date().toISOString()
        };
      } else {
        // 获取失败
        throw new Error(`Failed to get invoice list: ${data.msg || 'Unknown error'}`);
      }
      
    } catch (error) {
      console.error('PortalGetInvoiceListTool.execute error:', error);
      
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        timestamp: new Date().toISOString()
      };
    }
  }
});

// 辅助函数：格式化客户列表响应
function formatCustomerListResponse(customer_data: any): any {
  // 根据claim.py中的逻辑，API返回的数据结构是 billing_list
  let customers = customer_data.billing_list || [];
  if (Array.isArray(customer_data)) {
    customers = customer_data;
  }
  
  // 如果没有客户数据
  if (!customers || customers.length === 0) {
    return {
      success: true,
      customer_count: 0,
      message: "未找到任何客户信息",
      guidance: {
        next_step: "请联系管理员确认您的账户权限",
        troubleshooting: "可能的原因：1) 账户无客户关联 2) 权限不足 3) 数据暂时不可用"
      }
    };
  }
  
  // 格式化客户列表 - 根据API文档字段名
  const formatted_customers = [];
  for (let i = 0; i < customers.length; i++) {
    const customer = customers[i];
    // 根据文档，字段名为：location_id, location_name, location_code
    const customer_info = {
      "序号": i + 1,
      "客户ID": customer.location_id,  // 使用location_id作为客户ID
      "客户名称": customer.location_name,  // 使用location_name作为客户名称  
      "客户代码": customer.location_code,  // 使用location_code作为客户代码
      "联系人": customer.contact_person || customer.contact || customer.contactPerson,
      "电话": customer.phone || customer.telephone || customer.phoneNumber,
      "邮箱": customer.email || customer.emailAddress,
      "地址": customer.address || customer.location,
      "状态": customer.status || customer.customerStatus || '活跃'
    };
    
    // 移除空值
    const cleaned_customer_info = Object.fromEntries(
      Object.entries(customer_info).filter(([_, value]) => value !== null && value !== undefined && value !== '')
    );
    formatted_customers.push(cleaned_customer_info);
  }
  
  return {
    success: true,
    customer_count: formatted_customers.length,
    customers: formatted_customers,
    guidance: {
      message: `成功获取 ${formatted_customers.length} 个客户信息`,
      next_step: "请选择一个客户以查看其发票信息",
      selection_tip: "记住客户ID，在获取发票时需要使用",
      available_actions: [
        "选择客户ID查看发票列表",
        "使用 refresh_cache=true 刷新客户列表"
      ]
    },
    usage_examples: {
      select_customer: "请使用客户ID（如：221120）获取该客户的发票列表",
      refresh_data: "如需刷新数据，请调用 get_customer_list(refresh_cache=true)"
    }
  };
}

// 辅助函数：格式化发票列表响应
function formatInvoiceListResponse(invoice_data: any): any {
  let invoices = invoice_data.invoices || [];
  if (Array.isArray(invoice_data)) {
    invoices = invoice_data;
  }
  
  // 如果没有发票数据
  if (!invoices || invoices.length === 0) {
    return {
      success: true,
      invoice_count: 0,
      message: "未找到任何发票信息",
      guidance: {
        next_step: "请联系管理员确认您的账户权限",
        troubleshooting: "可能的原因：1) 账户无发票关联 2) 权限不足 3) 数据暂时不可用"
      }
    };
  }
  
  // 格式化发票列表
  const formatted_invoices = [];
  for (let i = 0; i < invoices.length; i++) {
    const invoice = invoices[i];
    // 根据文档中的实际API响应字段名处理发票数据
    const invoice_info = {
      "序号": i + 1,
      "发票号": invoice.invoiceNumber || invoice.invoice_number || invoice.number,
      "发票金额": invoice.invoiceAmount || invoice.amount || invoice.total,
      "发票日期": invoice.invoiceDate || invoice.date || invoice.issue_date,
      "发票ID": invoice.invoiceID || invoice.id || invoice.invoiceId,
      "状态": invoice.invoiceStatus || invoice.status || '已开票',
      "状态ID": invoice.invoiceStatusID || invoice.status_id
    };
    
    // 移除空值
    const cleaned_invoice_info = Object.fromEntries(
      Object.entries(invoice_info).filter(([_, value]) => value !== null && value !== undefined && value !== '')
    );
    formatted_invoices.push(cleaned_invoice_info);
  }
  
  return {
    success: true,
    invoice_count: formatted_invoices.length,
    invoices: formatted_invoices,
    guidance: {
      message: `成功获取 ${formatted_invoices.length} 张发票信息`,
      next_step: "请选择一张发票以查看详情",
      selection_tip: "记住发票号，在提交争议申请时需要使用",
      available_actions: [
        "选择发票号查看发票详情",
        "使用 refresh_cache=true 刷新发票列表"
      ]
    },
    usage_examples: {
      select_invoice: "请使用发票号（如：INV001）获取该发票的详细信息",
      refresh_data: "如需刷新数据，请调用 get_invoice_list(refresh_cache=true)"
    }
  };
}

// ========== 新增：获取 utCustomers 工具 ==========
export const portalUtCustomersTool = tool({
  description: 'Retrieve the list of UT customers (ut_customers) available to the current user. Use when the user needs to explicitly select which customer to use for quote pricing operations. Examples: "Show my UT customers", "List ut_customers for quoting"',
  parameters: z.object({}),
  execute: async (_params, options: { toolCallId: string; messages: any[] }) => {
    try {
      const requestId = extractRequestIdFromToolCallId(options.toolCallId);
      const requestContext = getRequestContext(requestId);
      let utCustomers: string[] = [];
      if (typeof requestContext?.utCustomers === 'string') {
        try {
          utCustomers = JSON.parse(requestContext.utCustomers);
        } catch {
          utCustomers = [];
        }
      } else if (Array.isArray(requestContext?.utCustomers)) {
        utCustomers = requestContext.utCustomers;
      }
      console.log('portalUtCustomersTool utCustomers', utCustomers);
      // 如果为空，尝试用 customerCode 填充
      if ((!utCustomers || utCustomers.length === 0) && requestContext?.customerCode) {
        utCustomers = [requestContext.customerCode];
      }
      return {
        success: true,
        ut_customers: utCustomers,
        message: 'Successfully retrieved ut_customers',
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        timestamp: new Date().toISOString()
      };
    }
  }
});

// ========== 新增：获取 ufCustomers 工具 ==========
export const portalUfCustomersTool = tool({
  description: 'Retrieve the list of UF customers (uf_customers) available to the current user. Use when the user needs to explicitly select which customer to use for DN-/RN- related data queries. Examples: "Show my UF customers", "List uf_customers for DN/RN queries"',
  parameters: z.object({}),
  execute: async (_params, options: { toolCallId: string; messages: any[] }) => {
    try {
      const requestId = extractRequestIdFromToolCallId(options.toolCallId);
      const requestContext = getRequestContext(requestId);
      let ufCustomers: string[] = [];
      if (typeof requestContext?.ufCustomers === 'string') {
        try {
          ufCustomers = JSON.parse(requestContext.ufCustomers);
        } catch {
          ufCustomers = [];
        }
      } else if (Array.isArray(requestContext?.ufCustomers)) {
        ufCustomers = requestContext.ufCustomers;
      }
      console.log('portalUfCustomersTool ufCustomers', ufCustomers);
      // 如果为空，尝试用 customerCode 填充
      if ((!ufCustomers || ufCustomers.length === 0) && requestContext?.customerCode) {
        ufCustomers = [requestContext.customerCode];
      }
      return {
        success: true,
        uf_customers: ufCustomers,
        message: 'Successfully retrieved uf_customers',
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        timestamp: new Date().toISOString()
      };
    }
  }
});

// ========== 新增：获取 ACC List 工具 ==========
export const portalAccListTool = tool({
  description: 'Get supported additional services (ACC list) for shipping quotes and orders. Use when before creating quotes that need additional services (liftgate, inside delivery, etc.) or when user asks about available services. Examples: "What additional services are available?", "Do you support liftgate service?"',
  parameters: z.object({}),
  execute: async (_params, options: { toolCallId: string; messages: any[] }) => {
    console.log('PortalAccListTool.execute called');
    
    try {
      // 从环境变量获取Portal基础URL
      const baseUrl = process.env.PORTAL_BASEURL || 'https://portal-staging.item.com';
      const apiUrl = `${baseUrl}/api/v1/web/quote/accessorial`;
      
      // 从工具调用ID中提取请求ID
      const requestId = extractRequestIdFromToolCallId(options.toolCallId);
      const requestContext = getRequestContext(requestId);
      
      console.log('Getting additional services from:', apiUrl);
      
      // 发送API请求
      const response = await fetch(apiUrl, {
        method: 'GET',
        headers: buildPortalHeaders(requestContext)
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      
      console.log('PortalAccListTool.execute response:', JSON.stringify(data));
      
      // 处理API响应
      if (data.code === 0) {
        // 获取成功，直接返回API响应数据
        return {
          success: true,
          code: data.code,
          msg: data.msg || 'Success',
          data: data.data || [],
          timestamp: new Date().toISOString()
        };
      } else {
        // API 返回错误
        throw new Error(`Failed to get additional services: ${data.msg || 'Unknown error'}`);
      }
      
    } catch (error) {
      console.error('PortalAccListTool.execute error:', error);
      
      return {
        success: false,
        code: -1,
        msg: error instanceof Error ? error.message : 'Unknown error occurred',
        data: [],
        timestamp: new Date().toISOString()
      };
    }
  }
}); 

/**
 * 通过IAM接口获取UF客户列表工具
 */
export const getUfCustomers4IamTool = tool({
  description: 'Get UF customers list through IAM API /v1/web/user/info',
  parameters: z.object({}),
  execute: async (_params, options: { toolCallId: string; messages: any[] }) => {
    console.log('GetUfCustomers4IamTool.execute called');
    
    try {
      // 从环境变量获取Portal基础URL
      const baseUrl = process.env.PORTAL_BASEURL || 'https://portal-staging.item.com';
      const apiUrl = `${baseUrl}/api/v1/web/user/info`;
      
      // 从工具调用ID中提取请求ID
      const requestId = extractRequestIdFromToolCallId(options.toolCallId);
      const requestContext = getRequestContext(requestId);
      
      console.log('Getting UF customers from IAM API:', apiUrl);
      
      // 发送API请求
      const response = await fetch(apiUrl, {
        method: 'GET',
        headers: buildPortalHeaders(requestContext)
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      
      console.log('GetUfCustomers4IamTool.execute response:', JSON.stringify(data));
      
      // 处理API响应
      if (data.code === 0) {
        // 获取成功，从响应中提取uf_customers
        const userInfo = data.data || {};
        const ufCustomers = userInfo.uf_customers || [{
          "id": "1",
          "customer_type": 2,
          "customer_code": "UNIS-500"
        }];
        
        return {
          success: true,
          message: 'Successfully retrieved UF customers from IAM API',
          data: {
            uf_customers: ufCustomers,
            total_count: ufCustomers.length,
            source: 'IAM API /v1/web/user/info',
            user_info: {
              user_id: userInfo.id,
              username: userInfo.name,
              email: userInfo.email
            }
          },
          timestamp: new Date().toISOString()
        };
      } else {
        // 获取失败
        throw new Error(`Failed to get UF customers from IAM API: ${data.msg || 'Unknown error'}`);
      }
      
    } catch (error) {
      console.error('GetUfCustomers4IamTool.execute error:', error);
      
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        timestamp: new Date().toISOString()
      };
    }
  }
});

/**
 * 通过IAM接口获取UT客户列表工具
 */
export const getUtCustomers4IamTool = tool({
  description: 'Get UT customers list through IAM API /v1/web/user/info. Use when the user needs to explicitly select which customer to use for quote pricing operations. Examples: "Show my UT customers", "List ut_customers for quoting"',
  parameters: z.object({}),
  execute: async (_params, options: { toolCallId: string; messages: any[] }) => {
    console.log('GetUtCustomers4IamTool.execute called');
    
    try {
      // 从环境变量获取Portal基础URL
      const baseUrl = process.env.PORTAL_BASEURL || 'https://portal-staging.item.com';
      const apiUrl = `${baseUrl}/api/v1/web/user/info`;
      
      // 从工具调用ID中提取请求ID
      const requestId = extractRequestIdFromToolCallId(options.toolCallId);
      const requestContext = getRequestContext(requestId);
      
      console.log('Getting UT customers from IAM API:', apiUrl);
      
      // 发送API请求
      const response = await fetch(apiUrl, {
        method: 'GET',
        headers: buildPortalHeaders(requestContext)
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      
      console.log('GetUtCustomers4IamTool.execute response:', JSON.stringify(data));
      
      // 处理API响应
      if (data.code === 0) {
        // 获取成功，从响应中提取ut_customers
        const userInfo = data.data || {};
        const utCustomers = userInfo.ut_customers || [{
          "id": "1",
          "customer_type": 2,
          "customer_code": "UNIS-500"
        }];
        
        return {
          success: true,
          message: 'Successfully retrieved UT customers from IAM API',
          data: {
            ut_customers: utCustomers,
            total_count: utCustomers.length,
            source: 'IAM API /v1/web/user/info',
            user_info: {
              user_id: userInfo.id,
              username: userInfo.name,
              email: userInfo.email
            }
          },
          timestamp: new Date().toISOString()
        };
      } else {
        // 获取失败
        throw new Error(`Failed to get UT customers from IAM API: ${data.msg || 'Unknown error'}`);
      }
      
    } catch (error) {
      console.error('GetUtCustomers4IamTool.execute error:', error);
      
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        timestamp: new Date().toISOString()
      };
    }
  }
});