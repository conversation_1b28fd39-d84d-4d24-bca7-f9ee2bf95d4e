'use client';

import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { useChat } from '@ai-sdk/react';
import { Message } from '@ai-sdk/react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import WeatherCardEmbed from './components/WeatherCardEmbed';
import WMSCardEmbed from './components/WMSCardEmbed';
import TMSCardEmbed from './components/TMSCardEmbed';
import FinishTaskCardEmbed from './components/FinishTaskCardEmbed';
import ErrorCardEmbed from './components/ErrorCardEmbed';
import SequentialThinkingCardEmbed from './components/SequentialThinkingCardEmbed';
import MemoryToolCardEmbed from './components/MemoryToolCardEmbed';
import ClockCardEmbed from './components/ClockCardEmbed';
import GenericToolCardEmbed from './components/GenericToolCardEmbed';
import KnowledgeCardEmbed from './components/KnowledgeCardEmbed';
import { DOMService } from '@/lib/dom/DOMService';
import { DOMController } from '@/lib/dom/DOMController';
import { ScreenshotTool } from '@/lib/screenshot';
import { DynamicForm as SimpleDynamicForm } from '@/components/ui/dynamic-form';
import { getEmbedTokenManager } from './EmbedTokenManager';
import { getSiteSpecificHeaders } from './siteHeaders';
import { generateRequestToken } from '../utils/client-token';
import { getTimezoneString } from '../utils/timezoneUtils';


// Component props definition
interface EmbeddedChatProps {
  token?: string;
  domain?: string;
  theme?: 'light' | 'dark';
  initialPosition?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left'; // Used for positioning the chat window
  onError?: (error: Error) => void;
  onMinimize?: () => void; // Add the onMinimize callback to props
  tools?: string[];
  config?: any;
  tenantId?: string; // 添加租户ID属性
  facilityId?: string; // 添加设施ID属性
  siteId?: string; // 添加站点ID属性
  onThemeChange?: (theme: 'light' | 'dark') => void; // 添加主题切换回调函数
  welcomeSuggestions?: string[]; // 添加欢迎建议选项
  apiUrl?: string; // 新增apiUrl属性
  hideToolUsage?: boolean; // 新增隐藏工具使用的配置
  publicKey?: string; // 添加公钥属性
}

// Tool result component - simplified non-collapsible version
function SimpleToolCard({ toolName, state, toolInvocation }: { toolName: string; state: string; toolInvocation: any }) {
  // Check if tool invocation has an error
  const hasError = toolInvocation.error !== undefined;

  // Convert tool names to English for better consistency
  const displayToolName = toolName === 'find_wms_api' ? 'API Search' :
                          toolName === 'call_wms_api' ? 'API Call' :
                          toolName === 'tmsShipmentOrderTrackingTool' ? 'TMS Tracking' :
                          toolName === 'tmsQuoteTool' ? 'TMS Quote' :
                          toolName;

  return (
    <div className="embed-card p-1 my-1 bg-gray-800/10 rounded border border-gray-700/30">
      <div className="flex items-center" style={{ whiteSpace: 'nowrap', width: '100%' }}>
        <div className="mr-2 text-gray-400 flex-shrink-0">
          <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
          </svg>
        </div>
        <div className={`text-xs ${hasError ? 'text-red-400' : 'text-gray-300'} flex-shrink-0 mr-2`}>
          {displayToolName}:
        </div>
        <div className={`text-xs ${hasError ? 'text-red-400' : 'text-gray-400'} overflow-hidden text-ellipsis`} style={{ textOverflow: 'ellipsis', overflow: 'hidden', whiteSpace: 'nowrap' }}>
          {state === 'partial-call' && 'Processing...'}
          {state === 'call' && 'Invoked'}
          {state === 'result' && (hasError ? 'Failed' : 'Completed')}
        </div>
      </div>
    </div>
  );
}

// Tool invocation group component
interface ToolInvocationGroupProps {
  toolInvocations: any[];
  toolComponentMap: any;
  reasoningMap?: Map<number, React.ReactNode>;
  hideToolUsage?: boolean;
}

// 通用处理卡片组件
const GenericProcessingCard = ({ toolInvocation }: { toolInvocation: any }) => {
  const isCompleted = toolInvocation.state === 'result';
  const hasError = toolInvocation.error !== undefined;
  
  return (
    <div className="embed-card p-2 my-1 bg-gray-800/10 rounded border border-gray-700/30">
      <div className="flex items-center" style={{ whiteSpace: 'nowrap', width: '100%' }}>
        <div className="mr-2 text-gray-400 flex-shrink-0">
          <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
          </svg>
        </div>
        <div className={`text-xs ${hasError ? 'text-red-400' : 'text-gray-300'} flex-shrink-0 mr-2`}>
          Processing:
        </div>
        <div className={`text-xs ${hasError ? 'text-red-400' : 'text-gray-400'} overflow-hidden text-ellipsis`} style={{ textOverflow: 'ellipsis', overflow: 'hidden', whiteSpace: 'nowrap' }}>
          {!isCompleted && 'Working...'}
          {isCompleted && (hasError ? 'Completed with issues' : 'Completed')}
        </div>
      </div>
    </div>
  );
};

function ToolInvocationGroup({ toolInvocations, toolComponentMap, reasoningMap, hideToolUsage }: ToolInvocationGroupProps) {
  const firstTool = toolInvocations[0];

  // If there's only one tool call, render it directly without grouping
  if (toolInvocations.length <= 1) {
    const ToolComponent = firstTool.toolName in toolComponentMap
      ? toolComponentMap[firstTool.toolName as keyof typeof toolComponentMap]
      : null;

    const indexInParts = firstTool._partIndex;
    const reasoning = reasoningMap?.get(indexInParts);

    return (
      <div>
        {reasoning && (
          <div className="reasoning text-xs text-gray-300 mb-1 bg-blue-900/10 p-1 rounded border-l-2 border-blue-400">
            {reasoning}
          </div>
        )}
        {ToolComponent ? (
          <ToolComponent toolInvocation={firstTool} />
        ) : (
          // 对于SimpleToolCard，我们需要手动检查hideToolUsage
          hideToolUsage && firstTool.toolName !== 'requireUserInputTool' ? (
            <GenericProcessingCard toolInvocation={firstTool} />
          ) : (
            <SimpleToolCard
              toolName={firstTool.toolName}
              state={firstTool.state || 'call'}
              toolInvocation={firstTool}
            />
          )
        )}
      </div>
    );
  }

  // Special case handling for sequential thinking tools - consolidate all steps
  const hasSequentialThinking = toolInvocations.some(tool => tool.toolName === 'sequentialthinking');
  if (hasSequentialThinking && toolInvocations.every(tool => tool.toolName === 'sequentialthinking')) {
    // All tools are sequential thinking, use a specialized component
    const sequentialThinkingTools = toolInvocations;
    const ToolComponent = toolComponentMap['sequentialthinking'];
    const indexInParts = firstTool._partIndex;
    const reasoning = reasoningMap?.get(indexInParts);

    return (
      <div>
        {reasoning && (
          <div className="reasoning text-xs text-gray-300 mb-1 bg-blue-900/10 p-1 rounded border-l-2 border-blue-400">
            {reasoning}
          </div>
        )}
        <ToolComponent toolInvocation={sequentialThinkingTools[0]} allInvocations={sequentialThinkingTools} />
      </div>
    );
  }

  // For multiple tools, render each one directly
  return (
    <div className="space-y-2">
      {toolInvocations.map((tool, index) => {
        const ToolComponent = tool.toolName in toolComponentMap
          ? toolComponentMap[tool.toolName as keyof typeof toolComponentMap]
          : null;

        const toolIndex = tool._partIndex;
        const toolReasoning = reasoningMap?.get(toolIndex);

        return (
          <div key={index} className="mb-2">
            {toolReasoning && (
              <div className="reasoning text-xs text-gray-300 mb-1 bg-blue-900/10 p-1 rounded border-l-2 border-blue-400">
                {toolReasoning}
              </div>
            )}
            {ToolComponent ? (
              <ToolComponent toolInvocation={tool} />
            ) : (
              // 对于SimpleToolCard，我们需要手动检查hideToolUsage
              hideToolUsage && tool.toolName !== 'requireUserInputTool' ? (
                <GenericProcessingCard toolInvocation={tool} />
              ) : (
                <SimpleToolCard
                  toolName={tool.toolName}
                  state={tool.state || 'call'}
                  toolInvocation={tool}
                />
              )
            )}
          </div>
        );
      })}
    </div>
  );
}

// Embedded chat component
const EmbeddedChat: React.FC<EmbeddedChatProps> = ({
  token,
  domain = window.location.origin,
  theme = 'dark',
  initialPosition = 'bottom-right', // Default position, used in CSS classes
  onError,
  onMinimize,
  config,
  tenantId,
  facilityId,
  siteId = 'default',
  onThemeChange,
  welcomeSuggestions = [
    'Tell me about order DN-123',
    'Why did DN-123 commit fail?',
    'Help me commit the order from this page'
  ], // 默认欢迎建议选项
  apiUrl,
  hideToolUsage = false, // 默认不隐藏工具使用
  publicKey, // 添加公钥参数
}) => {

  // Component state
  const [isMinimized, setIsMinimized] = useState(false);
  const [isThinking, setIsThinking] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hasNewMessages, setHasNewMessages] = useState(false);
  const [currentTheme, setCurrentTheme] = useState<'light' | 'dark'>(theme);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const chatMessagesRef = useRef<HTMLDivElement>(null);
  const hasLoadedHistoryRef = useRef(false);

  // 聊天ID和保存相关状态
  const [chatId, setChatId] = useState<string | null>(null);
  // 添加一个ref来跟踪上次保存的消息内容状态
  const lastSavedMessagesState = useRef('');
  // 使用useRef来跟踪上一次保存的消息数量，避免不必要的保存
  const lastSavedMessageCount = useRef(0);
  const [isSaving, setIsSaving] = useState(false);

  // 本地存储相关的工具函数
  const LOCAL_STORAGE_KEY = 'CyberBot-ClientChatHistory';

  const saveToLocalStorage = useCallback((messages: Message[],chatId:string) => {
    const historyCacheTime = config?.historyCacheTime;
    if (!historyCacheTime || historyCacheTime <= 0) return;
    
    const data = {
      messages,
      expireTime: Date.now() + (historyCacheTime * 1000),
      chatId
    };
    // console.log('saveToLocalStorage data:', data);
    try {
      localStorage.setItem(LOCAL_STORAGE_KEY, JSON.stringify(data));
    } catch (error) {
      console.error('Failed to save chat history to localStorage:', error);
    }
  }, [config]);

  const loadFromLocalStorage = useCallback(() => {
    try {
      const storedData = localStorage.getItem(LOCAL_STORAGE_KEY);
      if (!storedData) return null;
      
      const data = JSON.parse(storedData);
      // 检查是否过期
      if (data.expireTime && data.expireTime < Date.now()) {
        localStorage.removeItem(LOCAL_STORAGE_KEY);
        return null;
      }
      // console.log('loadFromLocalStorage data:', data);
      
      return data;
    } catch (error) {
      console.error('Failed to load chat history from localStorage:', error);
      return null;
    }
  }, []);

  const clearLocalStorage = useCallback(() => {
    try {
      localStorage.removeItem(LOCAL_STORAGE_KEY);
    } catch (error) {
      console.error('Failed to clear chat history from localStorage:', error);
    }
  }, []);

  // 监听 isMinimized 状态变化，在首次最大化时加载聊天记录
  useEffect(() => {
    const loadHistory = async () => {
      if (!isMinimized && !hasLoadedHistoryRef.current && setMessages) {
        const storedData = loadFromLocalStorage();
        if (storedData && storedData.messages && storedData.messages.length > 0) {
          setMessages(storedData.messages);
          if (storedData.chatId) {
            setChatId(storedData.chatId);
          }
          // 更新最后保存的消息状态
          lastSavedMessagesState.current = JSON.stringify(storedData.messages);
          lastSavedMessageCount.current = storedData.messages.length;
        }
        hasLoadedHistoryRef.current = true;
      }
    };
    loadHistory();
  }, [isMinimized]);

  // 监听主题变化
  useEffect(() => {
    setCurrentTheme(theme);
  }, [theme]);

  // 处理主题切换
  const handleThemeToggle = () => {
    const newTheme = currentTheme === 'light' ? 'dark' : 'light';
    setCurrentTheme(newTheme);
    if (onThemeChange) {
      onThemeChange(newTheme);
    }
  };

  // Setup mutation observer to track minimized state changes from parent container
  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    // Check initial state
    if (container.closest('.minimized') || container.parentElement?.classList.contains('minimized')) {
      setIsMinimized(true);
    }

    // Create a mutation observer to track class changes
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
          const target = mutation.target as HTMLElement;
          const isMinimizedNow = target.classList.contains('minimized');
          setIsMinimized(isMinimizedNow);
        }
      });
    });

    // Start observing the container
    let parentElement = container.parentElement;
    while (parentElement) {
      observer.observe(parentElement, { attributes: true });
      parentElement = parentElement.parentElement;
    }

    return () => {
      observer.disconnect();
    };
  }, []);

  // Function to get token from localStorage
  const getTokenFromLocalStorage = (): string | null => {
    const tokenManager = getEmbedTokenManager(siteId);
    return tokenManager.getCurrentToken();
  };

  const baseApiUrl = apiUrl || (config && config.apiUrl) || 'http://localhost:3000';
  const tokenManager = getEmbedTokenManager(siteId);
  const [effectiveToken, setEffectiveToken] = useState<string | null>(token || getTokenFromLocalStorage());

  // 使用 useRef 存储 tempToken，这样在组件重新渲染时不会重新生成
  const tempTokenRef = useRef<string>('');
  const [isLoggedIn, setIsLoggedIn] = useState<boolean>(false);

  // 生成新的 tempToken
  const generateNewTempToken = useCallback(() => {
    if (!publicKey) return '';
    console.log('generateNewTempToken', siteId, publicKey);
    const newToken = generateRequestToken(siteId, publicKey);
    tempTokenRef.current = newToken;
    console.log('generateNewTempToken', tempTokenRef.current);
    return newToken;
  }, [siteId, publicKey]);

  // 初始化时检查是否需要生成 tempToken
  useEffect(() => {
    if (!effectiveToken && publicKey && !tempTokenRef.current) {
      generateNewTempToken();
    }
    // 检查是否登录状态
    setIsLoggedIn(!!effectiveToken);
  }, []);
  
  // console.log('in EmbeddedChat isLoggedIn', isLoggedIn);

  async function embedFetchWithAuth(input: RequestInfo | URL, init?: RequestInit): Promise<Response> {
    
    let response = await fetch(input, init);
    const headers = new Headers(init?.headers);
    // 处理 401 响应
    if (response.status === 401) {
      console.log('embedFetchWithAuth isLoggedIn', isLoggedIn);
      // 如果是登录状态，走原有的 token 刷新逻辑
      if (isLoggedIn && tokenManager) {
        // 原有的 token 刷新逻辑
        const newToken = await tokenManager.refreshToken();
        if (!newToken) {
          tokenManager.removeToken();
          setEffectiveToken(null); // 清除 token 状态
          console.log('embedFetchWithAuth refreshToken failed ', newToken);
          throw new Error('Token refresh failed');
        }

        // 更新 effectiveToken 状态
        setEffectiveToken(newToken);
        
        headers.set('Authorization', `Bearer ${newToken}`);
        const retryInit = { ...init, headers };
        // 延迟3秒再请求，避免nbf校验异常
        await new Promise(resolve => setTimeout(resolve, 1500));
        response = await fetch(input, retryInit);

        if (response.status === 401) {
          tokenManager.removeToken();
          setEffectiveToken(null); // 清除 token 状态
          console.log('embedFetchWithAuth refreshToken req failed ', newToken);
          throw new Error('Token refresh failed');
        }
      } else if (!isLoggedIn) {
        // 非登录状态，重新生成 tempToken 并重试
        const newTempToken = generateNewTempToken();
        if (!newTempToken) {
          throw new Error('Failed to generate new temp token');
        }
        
        headers.set('X-Temp-Token', newTempToken);
        const retryInit = { ...init, headers };
        response = await fetch(input, retryInit);

        if (response.status === 401) {
          throw new Error('Temp token refresh failed');
        }
      }
    }

    return response;
  }

  // Use AI SDK's useChat hook
  const {
    messages,
    input,
    handleInputChange,
    handleSubmit: originalHandleSubmit,
    error: chatError,
    stop,
    addToolResult,
    status,
    setMessages
  } = useChat({
    api: baseApiUrl+'/api/chat-embed',
    headers: {
      'Authorization': effectiveToken 
        ? (effectiveToken.startsWith('Bearer ') ? effectiveToken : `Bearer ${effectiveToken}`)
        : '',
      ...(effectiveToken ? {} : { 'X-Temp-Token': tempTokenRef.current }),
      'X-Embed-Domain': domain || window.location.hostname,
      'x-tenant-id': tenantId || '',
      'x-facility-id': facilityId || '',
      'X-Site-ID': siteId || 'default',
      ...getSiteSpecificHeaders(siteId || 'default'),
      'X-Timezone': getTimezoneString()
    },
    body: {
      model: 'gpt-4.1', // Default to GPT-4.1
      tools: true
    },
    fetch: embedFetchWithAuth, // 关键：自定义fetch实现拦截
    experimental_throttle: 200,
    onError: (error) => {
      console.error('Chat error:', error);
      setIsThinking(false);
      let errorMessage = error.message;
      try {
        const parsedError = JSON.parse(error.message);
        if (parsedError && typeof parsedError === 'object' && parsedError.error) {
          errorMessage = parsedError.error;
        }
      } catch (e) {
        // 如果解析失败，保持原始错误消息
      }
      setError(`Error: ${errorMessage}`);
      if (onError) onError(error);
    },
    onFinish: (message: Message) => {
      const lastPart = message.parts && message.parts.length > 0 ? message.parts[message.parts.length - 1] : null;
      const hasActiveDOMTools = lastPart && 
                              lastPart.type === 'tool-invocation' && 
                              (lastPart.toolInvocation?.toolName === 'getPageSchema' || 
                               lastPart.toolInvocation?.toolName === 'domOperator');
      // 只有在没有活跃的DOM工具调用时才结束思考状态
      if (!hasActiveDOMTools) {
        setIsThinking(false);
      }
    },
    onToolCall: async ({ toolCall }) => {
      
      try {
        // 处理获取页面结构工具调用
        if (toolCall.toolName === 'getPageSchema') {
          const result = await handleGetDocument(toolCall.args);
          addToolResult({ toolCallId: toolCall.toolCallId, result });
          return result;
        }
        
        // 处理DOM操作工具调用
        if (toolCall.toolName === 'domOperator') {
          const result = await handleDOMOperation(toolCall.args);
          addToolResult({ toolCallId: toolCall.toolCallId, result });
          return result;
        }
      } catch (error) {
        console.error('工具调用执行失败:', error);
        // 返回错误信息
        return { 
          success: false, 
          error: error instanceof Error ? error.message : 'Unknown error' 
        };
      }
    }
  });

  // DOM操作相关功能实现
  const screenshotTool = useRef<ScreenshotTool>(new ScreenshotTool());
  const domService = useRef<DOMService>(DOMService.getInstance());
  
  // 处理获取页面结构的方法
  const handleGetDocument = async (args: any = {}) => {
    try {
      const options = args.options;
      
      // 添加页面加载检测逻辑
      await waitForPageLoaded();
      
      const stateDescription = await domService.current.getStateDescription();
      const response: any = {
        success: true,
        html: stateDescription
      };
      // 处理截图选项
      if (options?.screenshot?.enabled) {
        try {
          const result = await screenshotTool.current?.capture({
            quality: options.screenshot.quality || 0.8,
            maxWidth: 1920,
            maxHeight: 1080
          });
          if (result?.success && result.image) {
            response.screenshot = {
              type: 'image/jpeg',
              data: result.image.split(',')[1]  // 移除 data:image/jpeg;base64, 前缀
            };
          }
        } catch (screenshotError) {
          console.error('截图失败:', screenshotError);
        }
      }
      

      return response;
      
    } catch (error) {
      console.error('获取页面结构失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  };
  
  // 页面加载完成检测函数
  const waitForPageLoaded = () => {
    return new Promise<void>((resolve) => {
      // 设置最大等待时间，防止无限等待
      const maxWaitTime = 10000; // 最大等待10秒
      const startTime = Date.now();
      
      // 检测是否存在加载指示器
      const checkLoading = () => {
        // 检查是否超时
        if (Date.now() - startTime > maxWaitTime) {

          resolve();
          return;
        }
        
        // 检查常见的加载指示器类名和属性
        const loadingElements = document.querySelectorAll(
          '.loading, .loading-indicator, .spinner, [data-loading="true"], [aria-busy="true"]'
        );
        
        // 检查禁用的按钮（可能是因为加载状态而禁用）
        const disabledButtons = document.querySelectorAll(
          'button[disabled], button[aria-disabled="true"], [role="button"][aria-disabled="true"]'
        );
        
        if (loadingElements.length === 0 && disabledButtons.length === 0) {
          // 额外等待500ms确保DOM完全渲染
          setTimeout(resolve, 500);
        } else {

          setTimeout(checkLoading, 300);
        }
      };
      
      checkLoading();
    });
  };

  // 处理DOM操作的方法
  const handleDOMOperation = async (args: any) => {
    try {

      let { actions, auto_get_document } = args as { 
        actions: any, 
        auto_get_document?: boolean 
      };
      // 确保actions是数组
      if (!Array.isArray(actions)) {
        if (typeof actions === 'string') {
          try {
            actions = JSON.parse(actions);
          } catch (e) {
            console.error('解析actions字符串失败:', e);
            actions = [];
          }
        } else {
          actions = [actions];
        }
      }
      
      // 执行DOM操作
      const domController = DOMController.getInstance();
      const results = await domController.executeActions(actions);
      
      // 准备响应数据
      const responseData: any = {
        success: results.every(r => r.success),
        actions: results.map(r => ({
          success: r.success,
          error: r.error,
          extracted_content: r.extracted_content,
          include_in_memory: r.include_in_memory
        }))
      };
      
      // 如果请求中指定了自动获取文档，则获取并添加到响应中
      try {
        // 添加页面加载检测逻辑
        await waitForPageLoaded();
        
        const stateDescription = await domService.current.getStateDescription();
        responseData.updated_document = {
          html: stateDescription
        };
      } catch (docError) {
        console.error('操作后获取文档失败:', docError);
      }

      return responseData;

    } catch (error) {
      console.error('DOM操作失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        actions: []
      };
    }
  };

  // 保存聊天记录的函数
  const saveChatHistory = useCallback(async () => {
    if (isSaving) return;
    if (!messages || messages.length === 0) return;
    // 获取当前消息状态的序列化字符串，用于比较变化
    const currentMessagesState = JSON.stringify(messages);
    // 如果消息数量没有变化且内容没有变化，也不执行保存
    if (messages.length === lastSavedMessageCount.current && currentMessagesState === lastSavedMessagesState.current){
      return;
    }
    setIsSaving(true);
    try {
      const body: any = {
        id: chatId || undefined,
        messages,
        model: 'gpt-4.1',
        title: messages[0]?.content?.slice(0, 20) || 'New Chat',
      };
      let res = await fetch(baseApiUrl+'/api/chat-history', {
        method: 'POST',
        headers: {
          'Authorization': effectiveToken 
            ? (effectiveToken.startsWith('Bearer ') ? effectiveToken : `Bearer ${effectiveToken}`)
            : '',
          ...(effectiveToken ? {} : { 'X-Temp-Token': tempTokenRef.current }),
          'X-Embed-Domain': domain || window.location.hostname,
          'x-tenant-id': tenantId || '',
          'x-facility-id': facilityId || '',
          'X-Site-ID': siteId || 'default',
          'X-Timezone': getTimezoneString()
        },
        body: JSON.stringify(body),
      });
      // 如果返回401，尝试刷新token或生成临时token后重试
      if (res.status === 401) {
        if (tokenManager && isLoggedIn) {
          const newToken = await tokenManager.refreshToken();
          if (newToken) {
            setEffectiveToken(newToken);
            const headers = {
              'Authorization': newToken.startsWith('Bearer ') ? newToken : `Bearer ${newToken}`,
              'X-Embed-Domain': domain || window.location.hostname,
              'x-tenant-id': tenantId || '',
              'x-facility-id': facilityId || '',
              'X-Site-ID': siteId || 'default',
              'X-Timezone': getTimezoneString()
            };
            await new Promise(resolve => setTimeout(resolve, 1500)); // 避免nbf校验异常
            res = await fetch(baseApiUrl+'/api/chat-history', {
              method: 'POST',
              headers,
              body: JSON.stringify(body),
            });
          }
        } else if (!isLoggedIn && publicKey) {
          // 未登录且有公钥，生成临时token重试
          const newTempToken = generateNewTempToken();
          if (newTempToken) {
            const headers = {
              'X-Temp-Token': newTempToken,
              'X-Embed-Domain': domain || window.location.hostname,
              'x-tenant-id': tenantId || '',
              'x-facility-id': facilityId || '',
              'X-Site-ID': siteId || 'default',
              'X-Timezone': getTimezoneString()
            };
            await new Promise(resolve => setTimeout(resolve, 1500));
            res = await fetch(baseApiUrl+'/api/chat-history', {
              method: 'POST',
              headers,
              body: JSON.stringify(body),
            });
          }
        }
      }
      if (res.ok) {
        const data = await res.json();
        if (data.id && data.id !== chatId) {
          setChatId(data.id);
        }
        lastSavedMessageCount.current = messages.length;
        lastSavedMessagesState.current = currentMessagesState;
        
        // 同时保存到本地存储
        saveToLocalStorage(messages,data.id);
      }
    } catch (e) {
      // setError('保存聊天记录失败');
    } finally {
      setIsSaving(false);
    }
  }, [messages, chatId, isSaving, effectiveToken, domain, tenantId, facilityId, siteId, baseApiUrl, saveToLocalStorage]);

  // 在AI回复完成时自动保存
  useEffect(() => {
    // 获取当前消息状态的序列化字符串，用于比较变化
    const currentMessagesState = JSON.stringify(messages);
    if (status === 'ready' && !isThinking && messages.length > 0 && (messages.length !== lastSavedMessageCount.current || currentMessagesState !== lastSavedMessagesState.current)) {
      saveChatHistory();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [messages, status, isThinking]);

  // Handle message submission or stopping
  // 简单的滚动函数
  const scrollToUserMessage = () => {
    setTimeout(() => {
      const chatMessagesContainer = chatMessagesRef.current;
      if (!chatMessagesContainer) return;
      
      // 找到所有消息行
      const messageRows = chatMessagesContainer.querySelectorAll('.message-row');
      if (messageRows.length === 0) return;
      
      // 获取最后一个消息行（用户刚发送的消息）
      const lastMessageRow = messageRows[messageRows.length - 1] as HTMLElement;
      
      // 计算滚动位置：将用户消息放在容器顶部附近（留出很少空间）
      const containerHeight = chatMessagesContainer.clientHeight;
      const messageTop = lastMessageRow.offsetTop;
      const topOffset = 0; // 用户消息直接贴近顶部
      const targetScrollTop = messageTop - topOffset;
      

      
      chatMessagesContainer.scrollTo({
        top: Math.max(0, targetScrollTop),
        behavior: 'smooth'
      });
    }, 200); // 等待DOM更新
  };

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    
    // 如果当前正在思考中，则执行停止
    if (isThinking) {
      stop();
      setIsThinking(false);
      return;
    }
    
    // 如果没有输入内容，则不执行提交
    if (!input.trim()) return;

    setIsThinking(true);
    setError(null);
    originalHandleSubmit(e);
    
    // 用户发送消息后立即触发滚动
    scrollToUserMessage();
  };

  // 不再需要复杂的 useEffect 监听，直接在用户提交时触发滚动

  // Check for new messages when minimized
  useEffect(() => {
    // Set new message indicator if chat is minimized and there are messages
    if (isMinimized && messages.length > 0) {
      // Only set if the last message is from the assistant
      const lastMessage = messages[messages.length - 1];
      if (lastMessage && lastMessage.role === 'assistant') {
        setHasNewMessages(true);
      }
    }
  }, [messages, isMinimized]);

  // Handle errors
  useEffect(() => {
    if (chatError) {
      let errorMessage = chatError.message;
      try {
        const parsedError = JSON.parse(chatError.message);
        if (parsedError && typeof parsedError === 'object' && parsedError.error) {
          errorMessage = parsedError.error;
        }
      } catch (e) {
        // 如果解析失败，保持原始错误消息
      }
      setError(`Error: ${errorMessage}`);
      setIsThinking(false);
    }
  }, [chatError]);

  // Extract planning data from text content
  const extractPlanningData = (textContent: string) => {
    try {
      // Look for valid JSON content
      // Match: ```json { ...json content... } ```
      const jsonRegex = /```(?:json)?\s*(\{[\s\S]*?\})\s*```/g;
      const matchIterator = textContent.matchAll(jsonRegex);

      for (const match of matchIterator) {
        if (match && match[1]) {
          const jsonStr = match[1];
          const planData = JSON.parse(jsonStr);

          // Check if it's valid planning data
          if (
            planData &&
            typeof planData === 'object' &&
            planData.summary &&
            Array.isArray(planData.steps) &&
            planData.steps.length > 0
          ) {
            return {
              planningData: planData,
              // Remove JSON block to avoid duplicate display
              cleanedText: textContent.replace(match[0], '')
            };
          }
        }
      }
    } catch (e) {
      console.error('Error parsing planning JSON:', e);
    }

    return null;
  };

  // Function to group sequential thinking tool invocations together
  function groupToolInvocations(toolInvocations: any[]) {
    if (!toolInvocations || toolInvocations.length === 0) return [];

    // Group sequential thinking tools together
    const sequentialTools = toolInvocations.filter(tool =>
      tool.toolName === 'sequentialthinking' || tool.toolName === 'sequential_thinking'
    );

    // Other tools should be displayed individually
    const otherTools = toolInvocations.filter(tool =>
      tool.toolName !== 'sequentialthinking' && tool.toolName !== 'sequential_thinking'
    );

    const groups = [];

    // Group sequential thinking tools together if there are any
    if (sequentialTools.length > 0) {
      groups.push(sequentialTools);
    }

    // Add each other tool as an individual group
    otherTools.forEach(tool => {
      groups.push([tool]);
    });

    // If no groups were created, return all tools as a single group
    return groups.length > 0 ? groups : [toolInvocations];
  }

  // Toggle minimize/maximize
  const toggleMinimize = (e?: React.MouseEvent) => {
    // Prevent event propagation to avoid triggering parent handlers
    if (e) {
      e.stopPropagation();
    }

    // Call the onMinimize callback if provided
    // This will trigger the parent container's minimize/maximize methods
    if (onMinimize) {
      onMinimize();
      return;
    }

    // If no onMinimize callback is provided, handle state locally
    const newMinimizedState = !isMinimized;
    setIsMinimized(newMinimizedState);

    // 当聊天框最大化时，从 localStorage 加载聊天记录
    if (!newMinimizedState) {
      const storedData = loadFromLocalStorage();
      if (storedData && storedData.messages && storedData.messages.length > 0) {
        setMessages(storedData.messages);
        if (storedData.chatId) {
          setChatId(storedData.chatId);
        }
        // 更新最后保存的消息状态
        lastSavedMessagesState.current = JSON.stringify(storedData.messages);
        lastSavedMessageCount.current = storedData.messages.length;
      }
      setHasNewMessages(false);
    }
  };

  // 聊天清空操作
  const handleClearChat = () => {
    if (typeof setMessages === 'function') {
      setMessages([]); // 清空所有消息
    }
    setChatId(null);
    lastSavedMessagesState.current = '';
    lastSavedMessageCount.current = 0;
    setError(null);
    setIsThinking(false);
    setHasNewMessages(false);
    
    // 清除本地存储
    clearLocalStorage();
  };

  // 添加处理用户输入提交的方法
  const handleUserInputSubmit = useCallback((toolCallId: string, data: Record<string, any>) => {
    try {
      // 设置思考状态
      setIsThinking(true);

      // 使用addToolResult添加工具结果
      addToolResult({
        toolCallId,
        result: data
      });

    } catch (error) {
      console.error('处理用户输入提交时出错:', error);
      setError('Error processing user input, please try again');
      setIsThinking(false);
    }
  }, [addToolResult, setError]);

  // 添加处理用户输入取消的方法
  const handleUserInputCancel = useCallback((toolCallId: string) => {
    try {
      // 设置思考状态
      setIsThinking(true);

      // 用户取消的结果格式
      addToolResult({
        toolCallId,
        result: {
          canceled: true,
          message: 'User canceled input'
        }
      });
    } catch (error) {
      console.error('处理用户输入取消时出错:', error);
      setError('Error handling user input cancellation');
      setIsThinking(false);
    }
  }, [addToolResult, setError]);

  // 高阶组件：用于包装工具组件，统一处理hideToolUsage逻辑
  const withToolVisibility = (ToolComponent: React.ComponentType<any>, toolName: string) => {
    return (props: any) => {
      // requireUserInputTool 始终显示，因为它是动态表单
      if (hideToolUsage && toolName !== 'requireUserInputTool') {
        // 返回通用处理卡片而不是null
        return <GenericProcessingCard toolInvocation={props.toolInvocation} />;
      }
      return <ToolComponent {...props} />;
    };
  };

  // Define tool component mapping - consistent with main app
  const toolComponentMapEmbed = {
    'weatherTool': withToolVisibility((props: any) => {
      return <WeatherCardEmbed toolInvocation={props.toolInvocation} />;
    }, 'weatherTool'),
    'weather': withToolVisibility((props: any) => {
      return <WeatherCardEmbed toolInvocation={props.toolInvocation} />;
    }, 'weather'),
    'clockTool': withToolVisibility((props: any) => {
      return <ClockCardEmbed toolInvocation={props.toolInvocation} />;
    }, 'clockTool'),
    'find_wms_api': withToolVisibility((props: any) => {
      // Set display type explicitly
      const modifiedProps = {
        ...props,
        toolInvocation: {
          ...props.toolInvocation,
          displayType: 'search'
        }
      };
      return <WMSCardEmbed toolInvocation={modifiedProps.toolInvocation} />;
    }, 'find_wms_api'),
    'call_wms_api': withToolVisibility((props: any) => {
      // Set display type explicitly
      const modifiedProps = {
        ...props,
        toolInvocation: {
          ...props.toolInvocation,
          displayType: 'call'
        }
      };
      return <WMSCardEmbed toolInvocation={modifiedProps.toolInvocation} />;
    }, 'call_wms_api'),
    'wms': withToolVisibility((props: any) => {
      return <WMSCardEmbed toolInvocation={props.toolInvocation} />;
    }, 'wms'),
    'tmsShipmentOrderTrackingTool': withToolVisibility((props: any) => {
      return <TMSCardEmbed toolInvocation={props.toolInvocation} />;
    }, 'tmsShipmentOrderTrackingTool'),
    'tmsQuoteTool': withToolVisibility((props: any) => {
      return <TMSCardEmbed toolInvocation={props.toolInvocation} />;
    }, 'tmsQuoteTool'),
    'sequentialthinking': withToolVisibility((props: any) => {
      // If we have multiple invocations, pass them all
      if (props.allInvocations && props.allInvocations.length > 0) {
        return <SequentialThinkingCardEmbed toolInvocation={props.toolInvocation} allInvocations={props.allInvocations} />;
      }
      return <SequentialThinkingCardEmbed toolInvocation={props.toolInvocation} />;
    }, 'sequentialthinking'),
    'finishTaskTool': withToolVisibility((props: any) => {
      return <FinishTaskCardEmbed toolInvocation={props.toolInvocation} />;
    }, 'finishTaskTool'),
    'searchMemoriesTool': withToolVisibility((props: any) => {
      return <MemoryToolCardEmbed toolInvocation={props.toolInvocation} />;
    }, 'searchMemoriesTool'),
    'saveMemoryTool': withToolVisibility((props: any) => {
      return <MemoryToolCardEmbed toolInvocation={props.toolInvocation} />;
    }, 'saveMemoryTool'),
    'updateMemoryTool': withToolVisibility((props: any) => {
      return <MemoryToolCardEmbed toolInvocation={props.toolInvocation} />;
    }, 'updateMemoryTool'),
    'deleteMemoryTool': withToolVisibility((props: any) => {
      return <MemoryToolCardEmbed toolInvocation={props.toolInvocation} />;
    }, 'deleteMemoryTool'),
    'kbTool': withToolVisibility((props: any) => {
      return <KnowledgeCardEmbed toolInvocation={props.toolInvocation} />;
    }, 'kbTool'),
    'error': withToolVisibility((props: any) => {
      const error = props.toolInvocation?.error;
      const message = typeof error === 'string' ? error : 'Operation failed, please try again';
      return <ErrorCardEmbed error={error} message={message} />;
    }, 'error'),
    'getPageSchema': withToolVisibility((props: any) => {
      return (
        <div className="embed-card p-2 my-1 bg-gray-800/20 rounded border border-gray-700/30">
          <div className="text-xs text-gray-300 mb-1">getPageSchema</div>
          <div className="text-xs text-gray-400">
            {!props.toolInvocation.result 
              ? 'getPageSchema doing...' 
              : props.toolInvocation.result.success 
                ? 'getPageSchema success' 
                : 'getPageSchema failed'}
          </div>
        </div>
      );
    }, 'getPageSchema'),
    'domOperator': withToolVisibility((props: any) => {
      const result = props.toolInvocation.result;
      const actionResults = result?.actions || [];
      
      return (
        <div className="embed-card p-2 my-1 bg-gray-800/20 rounded border border-gray-700/30">
          <div className="text-xs text-gray-300 mb-1">domOperator</div>
          <div className="text-xs text-gray-400">
            {!result 
              ? 'domOperator doing...' 
              : result.success 
                ? 'domOperator success' 
                : 'domOperator failed'}
          </div>
          {actionResults.length > 0 && (
            <div className="mt-1 text-xs">
              <div className="text-gray-400">action results:</div>
              <ul className="list-disc pl-4 mt-1">
                {actionResults.map((action: any, index: number) => (
                  <li key={index} className={`text-xs ${action.success ? 'text-green-400' : 'text-red-400'}`}>
                    {action.success ? 'success' : `failed: ${action.error || 'unknown error'}`}
                    {action.extracted_content && (
                      <div className="text-gray-300 mt-1">
                        extracted content: {typeof action.extracted_content === 'string' 
                          ? action.extracted_content 
                          : JSON.stringify(action.extracted_content)}
                      </div>
                    )}
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>
      );
    }, 'domOperator'),
    'requireUserInputTool': (props: any) => {
      // requireUserInputTool should always be shown regardless of hideToolUsage setting
      // because it's a dynamic form that requires user interaction
      // Use useMemo to memoize the component based on props
      // 优化：只在关键状态变化时重新渲染，避免流式更新导致的表单闪烁
      return useMemo(() => {
        // 将 requireUserInputTool 返回的结构转换为 dynamic-form 期望的格式
        const { title, description, fields } = props.toolInvocation.args;

        // 检查确保组件已导入并存在
        if (!SimpleDynamicForm) {
          console.error("SimpleDynamicForm component not found");
          return <div className="text-red-500">Error: SimpleDynamicForm not available</div>;
        }

        // 检查是否有提交结果，如果有则显示只读模式
        // 同时检查result中的__submitted标记，这是为了支持从历史记录加载
        const hasResult = props.toolInvocation.state === 'result' && props.toolInvocation.result;
        // 检查是否取消 - 如果result包含canceled:true，则表示已取消
        const isCanceled = hasResult && props.toolInvocation.result.canceled === true;
        // 使用特殊标记来确保历史记录加载时能识别提交状态
        const isSubmitted = hasResult && (!isCanceled && (props.toolInvocation.result.__submitted || !props.toolInvocation.result.canceled));
        const submittedData = isSubmitted ? props.toolInvocation.result : null;

        // 已取消或已提交都不显示表单
        if (isCanceled) {
          // 显示已取消消息
          return (
            <div className="bg-gray-800/40 rounded-lg border border-gray-700/30 p-3">
              <div className="mb-2 flex items-center">
                <div className="bg-orange-900/40 p-1 rounded mr-2">
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-4 h-4 text-orange-400">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M12 9v3.75m9-.75a9 9 0 11-18 0 9 9 0 0118 0zm-9 3.75h.008v.008H12v-.008z" />
                  </svg>
                </div>
                <div className="text-base font-medium text-white">{title} - Canceled</div>
              </div>
              <div className="text-sm text-gray-400">User canceled this input request</div>
            </div>
          );
        }

        if (isSubmitted) {
          // 显示已提交的表单数据（只读模式），优化样式使其更紧凑
          return (
            <div className="bg-gray-800/40 rounded-lg border border-gray-700/30 p-3">
              <div className="mb-2 flex items-center">
                <div className="bg-cyan-900/40 p-1 rounded mr-2">
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-4 h-4 text-cyan-400">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div className="text-base font-medium text-white">{title}</div>
              </div>

              {description && <div className="mb-3 text-sm text-gray-300">{description}</div>}

              <div className="grid grid-cols-1 md:grid-cols-2 gap-1.5">
                {fields.map((field: any, index: number) => {
                  // 获取提交的值，排除内部使用的__submitted标记
                  if (field.name === '__submitted') return null;
                  const value = submittedData[field.name];
                  if (value === undefined) return null;

                  // 为不同类型的字段渲染不同的只读显示
                  return (
                    <div key={index} className="bg-gray-800/50 p-1.5 rounded-md">
                      <div className="text-xs font-medium text-cyan-300/90 mb-0.5">{field.label}</div>
                      {field.type === 'select' && (
                        <div className="text-xs text-gray-200">
                          {field.options?.find((opt: any) => opt.value === value)?.label || value}
                        </div>
                      )}
                      {field.type === 'checkbox' && (
                        <div className="text-xs text-gray-200">
                          {value ? '✓ Yes' : '✗ No'}
                        </div>
                      )}
                      {field.type === 'textarea' && (
                        <div className="text-xs text-gray-200 whitespace-pre-wrap">
                          {value || '(No input)'}
                        </div>
                      )}
                      {(field.type === 'text' || field.type === 'number') && (
                        <div className="text-xs text-gray-200">
                          {value || '(No input)'}
                        </div>
                      )}
                      {(field.type === 'date' || field.type === 'datetime' || field.type === 'time') && (
                        <div className="text-xs text-gray-200">
                          {value instanceof Date
                            ? value.toLocaleString()
                            : value || '(No input)'}
                        </div>
                      )}
                      {(field.type === 'userSelector' ||
                        field.type === 'customerSelector' ||
                        field.type === 'titleSelector' ||
                        field.type === 'carrierSelector' ||
                        field.type === 'deliveryServiceSelector' ||
                        field.type === 'generalProjectSelector' ||
                        field.type === 'jobcodeSelector' ||
                        field.type === 'itemmasterSelector' ||
                        field.type === 'itemmasterUomSelector' ||
                        field.type === 'stateSelector' ||
                        field.type === 'portalCustomerSelector' ||
                        field.type === 'portalInvoiceSelector') && (
                        <div className="text-xs text-gray-200">
                          {/* 使用标签映射显示标签值 */}
                          {submittedData.__labelMappings &&
                            submittedData.__labelMappings[field.name] ?
                            submittedData.__labelMappings[field.name].label :
                            value || '(No input)'}
                        </div>
                      )}
                      {field.type === 'accessorialMultipleSelector' && (
                        <div className="text-xs text-gray-200">
                          {Array.isArray(value) && value.length > 0 ? (
                            <div className="flex flex-wrap gap-1">
                              {value.map((item: any, itemIndex: number) => (
                                <span key={itemIndex} className="inline-block bg-gray-700/50 px-1.5 py-0.5 rounded text-xs">
                                  {item.name || item.code || item}
                                </span>
                              ))}
                            </div>
                          ) : (
                            '(No accessorial charges selected)'
                          )}
                        </div>
                      )}
                      {field.type === 'address' && (
                        <div className="text-xs text-gray-200 space-y-0.5">
                          {value && typeof value === 'object' ? (
                            <div className="bg-gray-800/70 p-1.5 rounded-md space-y-0.5">
                              {value.name && (
                                <div><span className="text-cyan-300/80 text-xs">Name:</span> <span className="text-xs">{value.name}</span></div>
                              )}
                              {value.street && (
                                <div><span className="text-cyan-300/80 text-xs">Street:</span> <span className="text-xs">{value.street}</span></div>
                              )}
                              {value.street2 && (
                                <div><span className="text-cyan-300/80 text-xs">Street 2:</span> <span className="text-xs">{value.street2}</span></div>
                              )}
                              <div className="flex flex-wrap gap-2">
                                {value.city && (
                                  <span className="text-xs"><span className="text-cyan-300/80">City:</span> {value.city}</span>
                                )}
                                {value.state && (
                                  <span className="text-xs"><span className="text-cyan-300/80">State:</span> {value.state}</span>
                                )}
                                {value.zip && (
                                  <span className="text-xs"><span className="text-cyan-300/80">ZIP:</span> {value.zip}</span>
                                )}
                              </div>
                              {value.phone && (
                                <div><span className="text-cyan-300/80 text-xs">Phone:</span> <span className="text-xs">{value.phone}</span></div>
                              )}
                              {value.contact && (
                                <div><span className="text-cyan-300/80 text-xs">Contact:</span> <span className="text-xs">{value.contact}</span></div>
                              )}
                            </div>
                          ) : (
                            '(No address provided)'
                          )}
                        </div>
                      )}
                      {field.type === 'array' && Array.isArray(value) && (
                        <div className="space-y-0.5">
                          {value.length === 0 ?
                            <div className="text-xs text-gray-400">(No items)</div> :
                            value.map((item: any, itemIndex: number) => (
                              <div key={itemIndex} className="bg-gray-800/70 p-1 rounded-md">
                                {Object.entries(item).map(([key, val]: [string, any]) => {
                                  // 跳过元数据字段
                                  if (key === '__metadata') return null;

                                  const itemField = field.arrayItemFields?.find((f: any) => f.name === key);
                                  if (!itemField) return null;

                                  // 检查是否有标签映射
                                  const hasMetadata = item.__metadata && item.__metadata[key];
                                  const displayValue = hasMetadata ? item.__metadata[key].label : val?.toString() || '(Empty)';

                                  return (
                                    <div key={key} className="mb-0.5">
                                      <span className="text-xs text-cyan-300/80">{itemField.label || key}: </span>
                                      <span className="text-xs text-gray-300">{displayValue}</span>
                                    </div>
                                  );
                                })}
                              </div>
                            ))
                          }
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
            </div>
          );
        }

        // 显示正常的表单（可交互模式）
        return (
          <SimpleDynamicForm
            fields={fields}
            title={title}
            description={description}
            onSubmit={(data) => {
              if (handleUserInputSubmit) {
                // 在数据中添加一个标记，表示这是一个已提交的表单
                // 这样在历史记录中加载时也能识别
                const submittedData = {
                  ...data,
                  __submitted: true // 特殊标记，用于历史记录中识别已提交状态
                };
                handleUserInputSubmit(props.toolInvocation.toolCallId, submittedData);
              } else {

              }
            }}
            onCancel={() => {
              if (handleUserInputCancel) {
                handleUserInputCancel(props.toolInvocation.toolCallId);
              } else {

              }
            }}
            cancelText="Cancel"
            submitText="Submit"
          />
        );
      }, [
        // 优化依赖项：只在真正影响表单渲染的属性变化时重新渲染
        JSON.stringify(props.toolInvocation.args), // 表单配置
        props.toolInvocation.toolCallId, // 工具调用ID
        props.toolInvocation.state, // 状态变化（pending -> result）
        // 只在结果的关键属性变化时重新渲染，避免流式更新导致的闪烁
        props.toolInvocation.result?.canceled,
        props.toolInvocation.result?.__submitted,
        handleUserInputSubmit,
        handleUserInputCancel
      ]); // 优化：减少不必要的重新渲染，提高表单稳定性
    },
    'default': withToolVisibility((props: any) => {
      return <GenericToolCardEmbed toolInvocation={props.toolInvocation} />;
    }, 'default'),
  };

  // Render message
  const renderMessage = (message: Message) => {
    if (message.role === 'user') {
      return (
        <div key={message.id} className="message-row justify-end">
           <div className="user-avatar">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeOpacity="0.9">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M15.75 6a3.75 3.75 0 11-7.5 0 3.75 3.75 0 017.5 0zM4.501 20.118a7.5 7.5 0 0114.998 0A17.933 17.933 0 0112 21.75c-2.676 0-5.216-.584-7.499-1.632z" />
            </svg>
          </div>
          <div className="message user-message">
            <div className="message-content">
              {typeof message.content === 'string' ? message.content : ''}
            </div>
          </div>

        </div>
      );
    } else {
      // Handle assistant message
      // Check if message has tool calls
      const hasToolCalls = message.parts && message.parts.some(part => part.type === 'tool-invocation');

      // If tool calls were extracted from text
      if (hasToolCalls) {
        return (
          <div key={message.id} className="message-row">
            <div className="assistant-avatar">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="white">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 18v-5.25m0 0a6.01 6.01 0 001.5-.189m-1.5.189a6.01 6.01 0 01-1.5-.189m3.75 7.478a12.06 12.06 0 01-4.5 0m3.75 2.383a14.406 14.406 0 01-3 0M14.25 18v-.192c0-.983.658-1.823 1.508-2.316a7.5 7.5 0 10-7.517 0c.85.493 1.509 1.333 1.509 2.316V18" />
              </svg>
            </div>
            <div className="message assistant-message">
              <div className="message-content">
                {/* Do not render the original content as markdown, as it will be included in renderMessageParts */}
                {/* Use renderMessageParts to render tool calls and associated text */}
                {renderMessageParts(message)}
              </div>
            </div>
          </div>
        );
      }

      // If no tool calls, use simple rendering
      if (!hasToolCalls) {
        return (
          <div key={message.id} className="message-row">
            <div className="assistant-avatar">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="white">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 18v-5.25m0 0a6.01 6.01 0 001.5-.189m-1.5.189a6.01 6.01 0 01-1.5-.189m3.75 7.478a12.06 12.06 0 01-4.5 0m3.75 2.383a14.406 14.406 0 01-3 0M14.25 18v-.192c0-.983.658-1.823 1.508-2.316a7.5 7.5 0 10-7.517 0c.85.493 1.509 1.333 1.509 2.316V18" />
              </svg>
            </div>
            <div className="message assistant-message">
              <div className="message-content">
                {typeof message.content === 'string'
                  ? (message.content.includes("function_call") || message.content.includes("tool call")
                    ? <>
                        <div className="tool-call-indicator">Calling tools...</div>
                        {message.content}
                      </>
                    : <ReactMarkdown
                        remarkPlugins={[remarkGfm]}
                        components={{
                          pre: ({ node, ...props }) => (
                            <div className="overflow-x-auto bg-black/50 rounded-md p-2 my-2">
                              <pre {...props} className="text-sm" />
                            </div>
                          ),
                          code: ({ node, ...props }: { node?: any, inline?: boolean }) => {
                            const { inline, ...restProps } = props as { inline?: boolean };
                            return inline ? (
                              <code {...restProps} className="bg-gray-800 px-1 py-0.5 rounded text-cyan-300" />
                            ) : (
                              <code {...restProps} />
                            );
                          },
                          ul: ({ node, ...props }) => <ul {...props} className="list-disc pl-4 space-y-1.5" />,
                          ol: ({ node, ...props }) => <ol {...props} className="list-decimal pl-4 space-y-1.5" />,
                          li: ({ node, ...props }) => <li {...props} className="pl-1" />,
                          a: ({ node, ...props }) => (
                            <a {...props} className="text-cyan-400 hover:underline hover:text-cyan-300" target="_blank" rel="noopener noreferrer" />
                          ),
                          table: ({ node, ...props }) => (
                            <div className="overflow-x-auto my-2">
                              <table {...props} className="border-collapse border border-gray-700" />
                            </div>
                          ),
                          thead: ({ node, ...props }) => <thead {...props} className="bg-gray-800" />,
                          tbody: ({ node, ...props }) => <tbody {...props} className="bg-gray-900/50" />,
                          tr: ({ node, ...props }) => <tr {...props} className="border-b border-gray-700" />,
                          th: ({ node, ...props }) => <th {...props} className="px-4 py-2 text-left" />,
                          td: ({ node, ...props }) => <td {...props} className="px-4 py-2 border-r border-gray-700 last:border-r-0" />,
                        }}
                      >
                        {message.content}
                      </ReactMarkdown>
                  )
                  : ''
                }
              </div>
            </div>
          </div>
        );
      }

      // Handle message with tool calls
      return (
        <div key={message.id} className="message-row">
          <div className="assistant-avatar">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="white">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 18v-5.25m0 0a6.01 6.01 0 001.5-.189m-1.5.189a6.01 6.01 0 01-1.5-.189m3.75 7.478a12.06 12.06 0 01-4.5 0m3.75 2.383a14.406 14.406 0 01-3 0M14.25 18v-.192c0-.983.658-1.823 1.508-2.316a7.5 7.5 0 10-7.517 0c.85.493 1.509 1.333 1.509 2.316V18" />
            </svg>
          </div>
          <div className="message assistant-message">
            <div className="message-content">
              {renderMessageParts(message)}
            </div>
          </div>
        </div>
      );
    }
  };

  // Render message parts (including tool calls)
  const renderMessageParts = (message: Message) => {
    if (!message.parts || message.parts.length === 0) {
      return typeof message.content === 'string' ? (
        <ReactMarkdown
          remarkPlugins={[remarkGfm]}
          components={{
            pre: ({ node, ...props }) => (
              <div className="overflow-x-auto bg-black/50 rounded-md p-2 my-2">
                <pre {...props} className="text-sm" />
              </div>
            ),
            code: ({ node, ...props }: { node?: any, inline?: boolean }) => {
              const { inline, ...restProps } = props as { inline?: boolean };
              return inline ? (
                <code {...restProps} className="bg-gray-800 px-1 py-0.5 rounded text-cyan-300" />
              ) : (
                <code {...restProps} />
              );
            },
            ul: ({ node, ...props }) => <ul {...props} className="list-disc pl-4 space-y-1.5" />,
            ol: ({ node, ...props }) => <ol {...props} className="list-decimal pl-4 space-y-1.5" />,
            li: ({ node, ...props }) => <li {...props} className="pl-1" />,
            a: ({ node, ...props }) => (
              <a {...props} className="text-cyan-400 hover:underline hover:text-cyan-300" target="_blank" rel="noopener noreferrer" />
            ),
            table: ({ node, ...props }) => (
              <div className="overflow-x-auto my-2">
                <table {...props} className="border-collapse border border-gray-700" />
              </div>
            ),
            thead: ({ node, ...props }) => <thead {...props} className="bg-gray-800" />,
            tbody: ({ node, ...props }) => <tbody {...props} className="bg-gray-900/50" />,
            tr: ({ node, ...props }) => <tr {...props} className="border-b border-gray-700" />,
            th: ({ node, ...props }) => <th {...props} className="px-4 py-2 text-left" />,
            td: ({ node, ...props }) => <td {...props} className="px-4 py-2 border-r border-gray-700 last:border-r-0" />,
          }}
        >
          {message.content}
        </ReactMarkdown>
      ) : '';
    }

    // Collect all tool invocations
    const toolInvocations = message.parts
      .filter(p => p.type === 'tool-invocation')
      .map((p: any) => {
        // Store part index in tool invocation object for later reasoning lookup
        return {
          ...p.toolInvocation,
          _partIndex: message.parts?.findIndex(item => item === p) || 0,
          // Ensure tool invocation has correct state
          state: p.toolInvocation.state || (p.toolInvocation.result ? 'result' : 'call')
        };
      });

    // Build reasoning map to associate text parts with tool calls
    const reasoningMap = new Map<number, React.ReactNode>();
    // Track which text parts have been processed as reasoning
    const processedTextIndices = new Set<number>();

    // For each tool call, find possible preceding reasoning text
    for (let i = 0; i < message.parts.length; i++) {
      const currentPart = message.parts[i];

      // If current part is a tool call
      if (currentPart.type === 'tool-invocation') {
        // Look for the nearest preceding text part
        let foundReasoning = false;
        for (let j = i - 1; j >= 0; j--) {
          const prevPart = message.parts[j];
          // Check if previous part is text and not already processed
          if (prevPart.type === 'text' && !processedTextIndices.has(j)) {
            // Found text part before tool call
            const planningDataResult = extractPlanningData(prevPart.text || '');
            // If it contains planning data, don't process it as reasoning
            if (planningDataResult && planningDataResult.planningData) {
              continue; // Skip this text part, don't treat it as reasoning
            }

            const reasoning = (
              <div className="prose prose-invert prose-sm max-w-none mb-2">
                <ReactMarkdown
                  remarkPlugins={[remarkGfm]}
                  components={{
                    pre: ({ node, ...props }) => (
                      <div className="overflow-x-auto bg-black/50 rounded-md p-2 my-2">
                        <pre {...props} className="text-sm" />
                      </div>
                    ),
                    code: ({ node, ...props }: { node?: any, inline?: boolean }) => {
                      const { inline, ...restProps } = props as { inline?: boolean };
                      return inline ? (
                        <code {...restProps} className="bg-gray-800 px-1 py-0.5 rounded text-cyan-300" />
                      ) : (
                        <code {...restProps} />
                      );
                    },
                    ul: ({ node, ...props }) => <ul {...props} className="list-disc pl-4 space-y-1.5" />,
                    ol: ({ node, ...props }) => <ol {...props} className="list-decimal pl-4 space-y-1.5" />,
                    li: ({ node, ...props }) => <li {...props} className="pl-1" />,
                    a: ({ node, ...props }) => (
                      <a {...props} className="text-cyan-400 hover:underline hover:text-cyan-300" target="_blank" rel="noopener noreferrer" />
                    ),
                    table: ({ node, ...props }) => (
                      <div className="overflow-x-auto my-2">
                        <table {...props} className="border-collapse border border-gray-700" />
                      </div>
                    ),
                    thead: ({ node, ...props }) => <thead {...props} className="bg-gray-800" />,
                    tbody: ({ node, ...props }) => <tbody {...props} className="bg-gray-900/50" />,
                    tr: ({ node, ...props }) => <tr {...props} className="border-b border-gray-700" />,
                    th: ({ node, ...props }) => <th {...props} className="px-4 py-2 text-left" />,
                    td: ({ node, ...props }) => <td {...props} className="px-4 py-2 border-r border-gray-700 last:border-r-0" />,
                  }}
                >
                  {planningDataResult ? planningDataResult.cleanedText : prevPart.text || ''}
                </ReactMarkdown>
              </div>
            );

            // Associate this reasoning component with the tool call
            reasoningMap.set(i, reasoning);

            // Mark this text part as processed
            processedTextIndices.add(j);
            (prevPart as any)._processed = true;

            foundReasoning = true;
            break;
          }
        }

        // If no preceding reasoning text was found, create an empty map entry
        if (!foundReasoning) {
          reasoningMap.set(i, null);
        }
      }
    }

    // 按照原始顺序渲染所有parts
    return (
      <div className="space-y-2">
        {message.parts?.map((part: any, index: number) => {
          if (part.type === 'text') {
            // 如果文本已被处理为工具调用的reasoning，则跳过
            if ((part as any)._processed) {
              return null;
            }

            // 检查是否包含计划信息
            const planningDataResult = extractPlanningData(part.text || '');

            return (
              <div key={index}>
                {planningDataResult && (
                  <div className="mb-4">
                                         {/* 简化的计划显示，不使用专门的组件 */}
                     <div className="embed-card planning p-2 my-1 bg-blue-800/20 rounded border-l-2 border-blue-600/30 border-t border-r border-b border-blue-700/20">
                       <div className="flex items-center mb-2">
                         <div className="mr-2 text-blue-400 flex-shrink-0">
                           <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                             <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8.25 6.75h12M8.25 12h12m-12 5.25h12M3.75 6.75h.007v.008H3.75V6.75zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zM3.75 12h.007v.008H3.75V12zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm-.375 5.25h.007v.008H3.75v-.008zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0z" />
                           </svg>
                         </div>
                         <div className="text-xs text-blue-300 flex-shrink-0 mr-2">Task Plan:</div>
                         <div className="text-xs text-blue-400 overflow-hidden text-ellipsis" style={{ textOverflow: 'ellipsis', overflow: 'hidden', whiteSpace: 'nowrap' }}>
                           {planningDataResult.planningData.summary}
                         </div>
                       </div>
                       
                       {planningDataResult.planningData.steps && planningDataResult.planningData.steps.length > 0 && (
                         <div className="text-xs text-blue-400 ml-6">
                           {planningDataResult.planningData.steps.length} step{planningDataResult.planningData.steps.length !== 1 ? 's' : ''} planned
                         </div>
                       )}
                    </div>
                  </div>
                )}
                <div className="prose prose-invert prose-sm max-w-none">
                  <ReactMarkdown
                    remarkPlugins={[remarkGfm]}
                    components={{
                      pre: ({ node, ...props }) => (
                        <div className="overflow-x-auto bg-black/50 rounded-md p-2 my-2">
                          <pre {...props} className="text-sm" />
                        </div>
                      ),
                      code: ({ node, ...props }: { node?: any, inline?: boolean }) => {
                        const { inline, ...restProps } = props as { inline?: boolean };
                        return inline ? (
                          <code {...restProps} className="bg-gray-800 px-1 py-0.5 rounded text-cyan-300" />
                        ) : (
                          <code {...restProps} />
                        );
                      },
                      ul: ({ node, ...props }) => <ul {...props} className="list-disc pl-4 space-y-1.5" />,
                      ol: ({ node, ...props }) => <ol {...props} className="list-decimal pl-4 space-y-1.5" />,
                      li: ({ node, ...props }) => <li {...props} className="pl-1" />,
                      a: ({ node, ...props }) => (
                        <a {...props} className="text-cyan-400 hover:underline hover:text-cyan-300" target="_blank" rel="noopener noreferrer" />
                      ),
                      table: ({ node, ...props }) => (
                        <div className="overflow-x-auto my-2">
                          <table {...props} className="border-collapse border border-gray-700" />
                        </div>
                      ),
                      thead: ({ node, ...props }) => <thead {...props} className="bg-gray-800" />,
                      tbody: ({ node, ...props }) => <tbody {...props} className="bg-gray-900/50" />,
                      tr: ({ node, ...props }) => <tr {...props} className="border-b border-gray-700" />,
                      th: ({ node, ...props }) => <th {...props} className="px-4 py-2 text-left" />,
                      td: ({ node, ...props }) => <td {...props} className="px-4 py-2 border-r border-gray-700 last:border-r-0" />,
                    }}
                  >
                    {planningDataResult ? planningDataResult.cleanedText : part.text || ''}
                  </ReactMarkdown>
                </div>
              </div>
            );
          } else if (part.type === 'tool-invocation') {
            // 如果是第一个工具调用，处理整个工具调用组
            if (index === message.parts?.findIndex(p => p.type === 'tool-invocation')) {
              // 定义工具组件映射
              const toolComponentMap = toolComponentMapEmbed;

              // 使用分组函数对工具调用进行分组
              return (
                <div key={index} className="mt-3 space-y-2">
                  {groupToolInvocations(toolInvocations).map((group, groupIndex) => (
                    <div key={groupIndex}>
                      {group.length <= 1 ? (
                        (() => {
                          const tool = group[0];
                          const { toolName, state } = tool;

                          // 检查是否有错误
                          if (tool.error) {
                            return <ErrorCardEmbed
                              error={tool.error}
                              message={typeof tool.error === 'string' ? tool.error : `Error with ${toolName} operation`}
                            />;
                          }

                          // 根据工具类型显示对应组件
                          const ToolComponent = toolName in toolComponentMap
                            ? toolComponentMap[toolName as keyof typeof toolComponentMap]
                            : toolComponentMap['default'];
                          
                          return <ToolComponent toolInvocation={tool} />;
                        })()
                      ) : (
                        <ToolInvocationGroup
                          toolInvocations={group}
                          toolComponentMap={toolComponentMap}
                          reasoningMap={reasoningMap}
                          hideToolUsage={hideToolUsage}
                        />
                      )}
                    </div>
                  ))}
                </div>
              );
            } else {
              // 如果不是第一个工具调用，则跳过（已经在第一个工具调用中处理过）
              return null;
            }
          }

          return null;
        })}
      </div>
    );
  }; 

  return (
    <div
      ref={containerRef}
      className={`embedded-chat ${currentTheme} ${isMinimized ? 'minimized' : ''} position-${initialPosition}`}
      onClick={isMinimized ? toggleMinimize : undefined}
      style={{ cursor: isMinimized ? 'default' : 'auto' }}
    >
      {/* Chat header - draggable area */}
      <div
        className="chat-header"
        style={{ cursor: isMinimized ? 'default' : 'move' }}
      >
        <div className="chat-title flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" viewBox="0 0 16 16" className="mr-2">
            <path d="M6 12.5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 0 1h-3a.5.5 0 0 1-.5-.5M3 8.062C3 6.76 4.235 5.765 5.53 5.886a26.6 26.6 0 0 0 4.94 0C11.765 5.765 13 6.76 13 8.062v1.157a.93.93 0 0 1-.765.935c-.845.147-2.34.346-4.235.346s-3.39-.2-4.235-.346A.93.93 0 0 1 3 9.219zm4.542-.827a.25.25 0 0 0-.217.068l-.92.9a25 25 0 0 1-1.871-.183.25.25 0 0 0-.068.495c.55.076 1.232.149 2.02.193a.25.25 0 0 0 .189-.071l.754-.736.847 1.71a.25.25 0 0 0 .404.062l.932-.97a25 25 0 0 0 1.922-.188.25.25 0 0 0-.068-.495c-.538.074-1.207.145-1.98.189a.25.25 0 0 0-.166.076l-.754.785-.842-1.7a.25.25 0 0 0-.182-.135"/>
            <path d="M8.5 1.866a1 1 0 1 0-1 0V3h-2A4.5 4.5 0 0 0 1 7.5V8a1 1 0 0 0-1 1v2a1 1 0 0 0 1 1v1a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2v-1a1 1 0 0 0 1-1V9a1 1 0 0 0-1-1v-.5A4.5 4.5 0 0 0 10.5 3h-2zM14 7.5V13a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V7.5A3.5 3.5 0 0 1 5.5 4h5A3.5 3.5 0 0 1 14 7.5"/>
          </svg>
          AI Assistant
        </div>
        <div className="flex items-center">
          {/* 清空按钮 */}
          {!isMinimized && (
            <button
              className="toggle-button mr-2"
              onClick={handleClearChat}
              aria-label="Clear chat"
              title="Clear chat"
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M3 6h18M8 6v12a2 2 0 002 2h4a2 2 0 002-2V6m-6 0V4a2 2 0 012-2h0a2 2 0 012 2v2" />
              </svg>
            </button>
          )}
          {/* 原有最小化按钮 */}
          {!isMinimized && (
            <button
              className="toggle-button"
              onClick={(e) => toggleMinimize(e)}
              aria-label="Minimize chat"
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M4 20L20 4M4 4l16 16"></path>
              </svg>
            </button>
          )}
        </div>
        {isMinimized && (
          <>
            {hasNewMessages && <div className="notification-indicator"></div>}
          </>
        )}
      </div>

      {/* Chat messages area - only show when not minimized */}
      {!isMinimized && (
        <div className="chat-messages" ref={chatMessagesRef}>
          {messages.length === 0 ? (
            <div className="welcome-message">
              <div className="welcome-title">Welcome to AI Assistant</div>
              <div className="welcome-description">
                This is a chat agent to help query and operate on the web pages
              </div>
              <div className="welcome-suggestions">
                <h3>You can try asking me:</h3>
                <ul>
                  {welcomeSuggestions.map((suggestion, index) => (
                    <li key={index}>{suggestion}</li>
                  ))}
                </ul>
              </div>
            </div>
          ) : (
            messages.map(renderMessage)
          )}
          {isThinking && (
            <div className="message-row">
              <div className="assistant-avatar">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                  <path d="M6 12.5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 0 1h-3a.5.5 0 0 1-.5-.5M3 8.062C3 6.76 4.235 5.765 5.53 5.886a26.6 26.6 0 0 0 4.94 0C11.765 5.765 13 6.76 13 8.062v1.157a.93.93 0 0 1-.765.935c-.845.147-2.34.346-4.235.346s-3.39-.2-4.235-.346A.93.93 0 0 1 3 9.219zm4.542-.827a.25.25 0 0 0-.217.068l-.92.9a25 25 0 0 1-1.871-.183.25.25 0 0 0-.068.495c.55.076 1.232.149 2.02.193a.25.25 0 0 0 .189-.071l.754-.736.847 1.71a.25.25 0 0 0 .404.062l.932-.97a25 25 0 0 0 1.922-.188.25.25 0 0 0-.068-.495c-.538.074-1.207.145-1.98.189a.25.25 0 0 0-.166.076l-.754.785-.842-1.7a.25.25 0 0 0-.182-.135"/>
                  <path d="M8.5 1.866a1 1 0 1 0-1 0V3h-2A4.5 4.5 0 0 0 1 7.5V8a1 1 0 0 0-1 1v2a1 1 0 0 0 1 1v1a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2v-1a1 1 0 0 0 1-1V9a1 1 0 0 0-1-1v-.5A4.5 4.5 0 0 0 10.5 3h-2zM14 7.5V13a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V7.5A3.5 3.5 0 0 1 5.5 4h5A3.5 3.5 0 0 1 14 7.5"/>
                </svg>
              </div>
              <div className="thinking-indicator">
                <div className="dot"></div>
                <div className="dot"></div>
                <div className="dot"></div>
              </div>
            </div>
          )}
          {error && (
            <div className="error-message">
              {error}
            </div>
          )}
          <div ref={messagesEndRef} />
        </div>
      )}

      {/* Chat input area - only show when not minimized */}
      {!isMinimized && (
        <form className="chat-input" onSubmit={handleSubmit}>
          <input
            type="text"
            value={input}
            onChange={handleInputChange}
            placeholder="Type your message..."
            disabled={isThinking}
            className="focus:ring-2 focus:ring-blue-500"
          />
          <button
            type="submit"
            className="hover:bg-blue-600 transition-colors"
          >
            {isThinking ? (
              <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            ) : (
              <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
              </svg>
            )}
          </button>
        </form>
      )}
    </div>
  );
};

export default EmbeddedChat;

