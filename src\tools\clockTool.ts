import { extractRequestIdFromToolCallId, getRequestContext } from '@/utils/requestContext';
import { tool } from 'ai';
import { z } from 'zod';

/**
 * 时钟工具
 * 使用 Vercel AI SDK 的 tool 函数实现
 */
export const clockTool = tool({
  description: 'Get current time information',
  parameters: z.object({
    hourFormat: z.enum(['12', '24']).optional().describe('The hour format, 12-hour or 24-hour format')
  }),
  execute: async (_params, options: { toolCallId: string; messages: any[] }) => {
    const requestId = extractRequestIdFromToolCallId(options.toolCallId);
    const requestContext = getRequestContext(requestId);
    const timezone = requestContext?.timezone;
    const hourFormat = _params.hourFormat || '24';
    console.log('ClockTool.execute called with args:', JSON.stringify({ timezone, hourFormat }));
    
    // 获取当前时间
    const now = new Date();
    
    // 如果提供了时区，则转换时间
    let timezoneName = timezone || Intl.DateTimeFormat().resolvedOptions().timeZone;
    
    try {
      // 格式化时间
      const timeOptions: Intl.DateTimeFormatOptions = {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: hourFormat === '12',
        timeZone: timezoneName
      };
      
      const dateOptions: Intl.DateTimeFormatOptions = {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        timeZone: timezoneName
      };
      
      const timeString = now.toLocaleTimeString([], timeOptions);
      const dateString = now.toLocaleDateString([], dateOptions);
      
      // 提取时、分、秒
      const hours = now.toLocaleTimeString([], {
        hour: '2-digit', 
        timeZone: timezoneName,
        hour12: false
      });
      const minutes = now.toLocaleTimeString([], {
        minute: '2-digit', 
        timeZone: timezoneName
      }).split(':')[1];
      const seconds = now.getSeconds();
      
      // 构建响应
      const clockData = {
        time: timeString,
        date: dateString,
        hours: parseInt(hours),
        minutes: parseInt(minutes),
        seconds: seconds,
        timezone: timezoneName,
        hourFormat: hourFormat
      };
      
      console.log('ClockTool.execute response:', JSON.stringify(clockData));
      
      return clockData;
    } catch (error) {
      console.error('Error in ClockTool:', error);
      // 如果时区无效，返回默认时区的时间
      return {
        time: now.toLocaleTimeString(),
        date: now.toLocaleDateString(),
        hours: now.getHours(),
        minutes: now.getMinutes(),
        seconds: now.getSeconds(),
        timezone: 'Etc/UTC',
        hourFormat: hourFormat,
        error: 'Invalid timezone. Using UTC instead.'
      };
    }
  }
}); 