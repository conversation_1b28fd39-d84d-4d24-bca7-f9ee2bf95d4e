import { Message } from '@ai-sdk/react';
import { ChatHistory } from '@/utils/chatHistoryUtils';
import { ChatAnalysisResult } from './types';
import { google } from '@ai-sdk/google';
import { generateText } from 'ai';

/**
 * Analyze chat history using Google AI Gemini model
 */
export async function analyzeWithGoogleAI(chat: ChatHistory): Promise<ChatAnalysisResult> {
  // Handle empty chat case
  if (!chat.messages || chat.messages.length === 0) {
    return {
      summary: 'Empty chat history',
      resolved: false,
      unresolvedReason: 'No messages in chat',
      userQuestions: [],
      agentResponses: [],
      keyInsights: []
    };
  }

  // Extract messages for analysis
  const userQuestions: string[] = [];
  const agentResponses: string[] = [];

  chat.messages.forEach(msg => {
    if (msg.role === 'user') {
      userQuestions.push(typeof msg.content === 'string' ? msg.content : JSON.stringify(msg.content));
    } else if (msg.role === 'assistant') {
      agentResponses.push(typeof msg.content === 'string' ? msg.content : JSON.stringify(msg.content));
    }
  });

  try {
    // 直接将整个聊天记录作为字符串发送，不做复杂处理
    // 这样可以确保所有信息都被完整传递给 Google AI
    const serializedChat = JSON.stringify(chat, null, 2);

    // Create the prompt for Google AI
    const prompt = `
      Below is a complete JSON representation of a conversation between a user and an AI assistant. Please analyze this conversation with a focus on whether the user's actual data needs were satisfied, especially regarding data retrieval.

      Analysis requirements:
      1. A summary of the conversation (100 words or less)

      2. Determine if the user's actual data needs were resolved successfully (true/false)
         - Focus ONLY on whether the user received the specific data they were looking for
         - Ignore conversation length or generic phrases like "hope this helps"
         - A conversation is only resolved if the assistant successfully retrieved and presented the exact data the user needed
         - IMPORTANT: If the system searched but found NO RESULTS or ZERO RECORDS, always mark as UNRESOLVED, even if the search was technically correct
         - If the user was looking for specific records or information and the system couldn't find it, mark as unresolved
         - "No data found" is NOT a resolution - the user's need was to find actual data, not to confirm its absence

      3. If not resolved, provide a detailed technical analysis of the failure point:
         - At which exact step did the data retrieval process fail?
         - Was it during entity recognition, parameter extraction, database query, or result processing?
         - What specific data was the user looking for that wasn't found?

      4. Identify and analyze specific technical issues:
         - Data retrieval issues: Did the system fail to find requested data in the database? Look for phrases indicating no results were found.
         - Parameter extraction issues: Did the system fail to correctly extract search parameters from the user's query?
         - Entity recognition issues: Did the system fail to recognize entities mentioned by the user (names, IDs, abbreviations, etc.)?
         - Tool usage issues: Were the correct tools/databases queried with the right parameters?

      5. For any identified issues, SUMMARIZE THE TOOL CALLS AND PARAMETERS from the conversation:
         - Look for tool_calls or function_call objects in the messages
         - Instead of copying the complete JSON, create a concise summary of each tool call
         - For each tool call, include: tool name, method, path (if applicable), and a brief summary of key parameters
         - For results, only include success/failure status and brief summary (e.g., "found 0 records", "error code 404")
         - DO NOT include sensitive information like authorization tokens or credentials

      6. A list of key technical insights from the conversation (up to 5 bullet points)

      Conversation JSON:
      ${serializedChat}

      Please format your response as a JSON object with these fields:
      {
        "summary": "brief summary of the conversation",
        "resolved": true or false, // IMPORTANT: Set to false if system found NO RESULTS, even if search was technically correct
        "unresolvedReason": "detailed technical explanation of why the data retrieval failed",
        "keyInsights": ["insight 1", "insight 2", ...],
        "dataRetrievalIssue": true or false,
        "parameterExtractionIssue": true or false,
        "entityRecognitionIssue": true or false,
        "toolUsageIssue": true or false,
        "issueDetails": "detailed technical explanation of the specific issue",
        "failedTools": ["exact tool name that failed"],
        "failedParameters": "PROVIDE A CONCISE SUMMARY OF THE FAILED PARAMETERS, NOT THE COMPLETE JSON",
        "rawToolCalls": ["INCLUDE BRIEF SUMMARIES OF TOOL CALLS WITH FOCUS ON TOOL NAME, METHOD, KEY PARAMETERS, AND RESULT STATUS"],
        "userActualIntent": "precise description of what data the user was looking for",
        "improvementSuggestions": ["specific technical improvement 1", "specific technical improvement 2"]
      }
    `;

    // 直接使用 Google AI SDK 调用 Gemini
    const googleModel = google('gemini-2.5-flash-preview-05-20');

    // 使用 generateText 调用模型
    const response = await generateText({
      model: googleModel,
      prompt: prompt,
      maxTokens: 16000 // 步增加 token 限制，确保能处理大型聊天记录和生成完整响应
    });

    // 获取完整的响应文本
    const text = response.text;

    // Extract JSON from response
    const jsonMatch = text.match(/\{[\s\S]*\}/);
    if (!jsonMatch) {
      console.error('Failed to extract JSON from Google AI response');
      console.error('Raw response:', text);
      // 失败时使用备用分析
      return fallbackAnalysis(chat, userQuestions, agentResponses);
    }

    try {
      // 尝试解析 JSON
      const analysisResult = JSON.parse(jsonMatch[0]);
      return processAnalysisResult(analysisResult, userQuestions, agentResponses, chat.id);
    } catch (jsonError) {
      console.error('Error parsing JSON from Google AI response:', jsonError);
      console.error('Extracted JSON:', jsonMatch[0]);

      // 尝试修复截断的 JSON
      try {
        // 尝试修复常见的 JSON 截断问题
        let fixedJson = jsonMatch[0];

        // 检查是否有未闭合的对象
        const openBraces = (fixedJson.match(/\{/g) || []).length;
        const closeBraces = (fixedJson.match(/\}/g) || []).length;

        // 添加缺失的闭合括号
        if (openBraces > closeBraces) {
          for (let i = 0; i < openBraces - closeBraces; i++) {
            fixedJson += '}';
          }
        }

        // 尝试解析修复后的 JSON
        const fixedResult = JSON.parse(fixedJson);
        console.log('Successfully fixed and parsed JSON');
        return processAnalysisResult(fixedResult, userQuestions, agentResponses, chat.id);
      } catch (fixError) {
        console.error('Failed to fix JSON:', fixError);
        // 如果修复失败，使用备用分析
        return fallbackAnalysis(chat, userQuestions, agentResponses);
      }
    }


  } catch (error) {
    console.error('Error analyzing chat with Google AI:', error);

    // Fall back to basic analysis if Google AI fails
    return fallbackAnalysis(chat, userQuestions, agentResponses);
  }
}

/**
 * Process the analysis result from Google AI
 */
function processAnalysisResult(
  analysisResult: any,
  userQuestions: string[],
  agentResponses: string[],
  chatId?: string
): ChatAnalysisResult {
  // 处理 failedParameters 字段 - 保持原始 JSON 格式
  let failedParams = null;
  try {
    if (analysisResult.failedParameters) {
      failedParams = analysisResult.failedParameters;

      // 如果是字符串，尝试修复可能的 JSON 截断
      if (typeof failedParams === 'string') {
        // 检查是否是截断的 JSON
        const openBraces = (failedParams.match(/\{/g) || []).length;
        const closeBraces = (failedParams.match(/\}/g) || []).length;

        if (openBraces > closeBraces) {
          // 如果是截断的 JSON，添加缺失的闭合括号
          for (let i = 0; i < openBraces - closeBraces; i++) {
            failedParams += '}';
          }
        }
      }
    }
  } catch (e) {
    console.error('Error processing failedParameters:', e);
    failedParams = null;
  }

  // 处理 rawToolCalls 字段
  let rawToolCalls: string[] = [];
  try {
    if (analysisResult.rawToolCalls) {
      const rawToolCallsData = analysisResult.rawToolCalls;

      if (Array.isArray(rawToolCallsData)) {
        // 确保数组中的每个元素都是字符串
        rawToolCalls = rawToolCallsData.map((item: any) => {
          if (typeof item === 'string') {
            return item;
          } else {
            try {
              return JSON.stringify(item, null, 2);
            } catch (e) {
              return String(item);
            }
          }
        });
      } else {
        // 如果不是数组，将其转换为数组
        try {
          rawToolCalls = [typeof rawToolCallsData === 'string' ? rawToolCallsData : JSON.stringify(rawToolCallsData, null, 2)];
        } catch (e) {
          rawToolCalls = [String(rawToolCallsData)];
        }
      }
    }
  } catch (e) {
    console.error('Error processing rawToolCalls:', e);
    rawToolCalls = [];
  }

  // Construct and return analysis result
  return {
    summary: analysisResult.summary || 'No summary available',
    resolved: !!analysisResult.resolved,
    unresolvedReason: !analysisResult.resolved ? (analysisResult.unresolvedReason || 'Unknown reason') : undefined,
    userQuestions,
    agentResponses,
    keyInsights: analysisResult.keyInsights || [],
    dataRetrievalIssue: !!analysisResult.dataRetrievalIssue,
    parameterExtractionIssue: !!analysisResult.parameterExtractionIssue,
    entityRecognitionIssue: !!analysisResult.entityRecognitionIssue,
    toolUsageIssue: !!analysisResult.toolUsageIssue,
    issueDetails: analysisResult.issueDetails || undefined,
    failedTools: analysisResult.failedTools || [],
    failedParameters: failedParams,
    rawToolCalls: rawToolCalls,
    userActualIntent: analysisResult.userActualIntent || undefined,
    improvementSuggestions: analysisResult.improvementSuggestions || [],
    chatId: chatId
  };
}

/**
 * Fallback analysis method if Google AI fails
 */
function fallbackAnalysis(chat: ChatHistory, userQuestions: string[], agentResponses: string[]): ChatAnalysisResult {
  // Simple heuristic to determine if chat is resolved:
  const hasUserMessages = userQuestions.length > 0;
  const hasAgentMessages = agentResponses.length > 0;
  const lastMessageIsFromAgent = chat.messages.length > 0 && chat.messages[chat.messages.length - 1].role === 'assistant';
  const hasMultipleExchanges = chat.messages.length >= 3;

  // Check for common resolution phrases in the last agent message
  const lastAgentMessage = agentResponses[agentResponses.length - 1] || '';
  const containsResolutionPhrases = [
    'hope this helps',
    'solved',
    'fixed',
    'completed',
    'done',
    'resolved',
    'is there anything else',
    'working now',
    'successful',
    'you can now'
  ].some(phrase => lastAgentMessage.toLowerCase().includes(phrase));

  // Check for data retrieval issues
  const dataRetrievalPhrases = [
    'no user relationship data found',
    'couldn\'t find data',
    'no data available',
    'no records found',
    'no matching records',
    'no results found',
    'unable to find',
    'no information found',
    'no data for',
    'no user data',
    'no relationship data',
    'no data matching',
    'could not retrieve',
    'failed to retrieve',
    'no data exists',
    'found 0 records',
    'found no records',
    'zero results',
    'zero records',
    'no orders found',
    'no items found',
    'no entries found',
    'no matches found',
    'no search results',
    'search returned 0 results',
    'query returned 0 results',
    'empty result set',
    'empty results'
  ];

  const dataRetrievalIssue = agentResponses.some(response =>
    dataRetrievalPhrases.some(phrase => response.toLowerCase().includes(phrase))
  );

  // Check for parameter extraction issues
  const parameterExtractionPhrases = [
    'need more specific',
    'could not understand',
    'unclear parameter',
    'missing parameter',
    'invalid parameter',
    'need more information',
    'please provide more details',
    'could not extract',
    'unable to parse',
    'invalid format',
    'incorrect format'
  ];

  const parameterExtractionIssue = agentResponses.some(response =>
    parameterExtractionPhrases.some(phrase => response.toLowerCase().includes(phrase))
  );

  // Determine if conversation was resolved
  // IMPORTANT: If there's a data retrieval issue (no results found), always mark as unresolved
  // even if the conversation has resolution phrases
  const resolved = hasUserMessages &&
                  hasAgentMessages &&
                  lastMessageIsFromAgent &&
                  hasMultipleExchanges &&
                  containsResolutionPhrases &&
                  !dataRetrievalIssue; // Data retrieval issues (including "no results found") mean the conversation is not resolved

  // Generate summary and reason if unresolved
  let summary = '';
  let unresolvedReason = undefined;
  let keyInsights: string[] = [];
  let issueDetails = undefined;

  if (userQuestions.length > 0) {
    summary = `Chat about: ${userQuestions[0].substring(0, 100)}${userQuestions[0].length > 100 ? '...' : ''}`;
    keyInsights.push(`Main question: ${userQuestions[0].substring(0, 150)}${userQuestions[0].length > 150 ? '...' : ''}`);

    if (userQuestions.length > 1) {
      keyInsights.push(`Follow-up questions: ${userQuestions.length - 1}`);
    }
  }

  if (!resolved) {
    // Determine the reason why the conversation might not have been resolved
    if (dataRetrievalIssue) {
      unresolvedReason = 'Data retrieval issue detected';
      issueDetails = 'The assistant was unable to find or retrieve the requested data. This could indicate a problem with search parameters, database access, or the data simply does not exist.';
      keyInsights.push('⚠️ Data retrieval issue detected - no matching records found');
    } else if (parameterExtractionIssue) {
      unresolvedReason = 'Parameter extraction issue detected';
      issueDetails = 'The assistant had difficulty extracting or understanding search parameters';
      keyInsights.push('⚠️ Parameter extraction issue detected');
    } else if (!hasAgentMessages) {
      unresolvedReason = 'No agent responses';
    } else if (!lastMessageIsFromAgent) {
      unresolvedReason = 'User had the last word, possibly waiting for agent response';
    } else if (!hasMultipleExchanges) {
      unresolvedReason = 'Not enough interaction to determine resolution';
    } else {
      unresolvedReason = 'No resolution phrases found in last agent message';
    }

    if (!keyInsights.some(insight => insight.includes('issue detected'))) {
      keyInsights.push(`Unresolved: ${unresolvedReason}`);
    }
  } else {
    keyInsights.push('Conversation successfully resolved');
  }

  keyInsights.push(`Conversation length: ${chat.messages.length} messages`);

  return {
    summary,
    resolved,
    unresolvedReason,
    userQuestions,
    agentResponses,
    keyInsights,
    dataRetrievalIssue,
    parameterExtractionIssue,
    entityRecognitionIssue: false,
    toolUsageIssue: false,
    issueDetails,
    failedTools: [],
    failedParameters: null,  // 使用 null 而不是空对象，以便于前端判断
    rawToolCalls: [],
    userActualIntent: userQuestions.length > 0 ? userQuestions[0].substring(0, 100) : undefined,
    improvementSuggestions: [],
    chatId: chat.id
  };
}