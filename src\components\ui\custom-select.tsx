'use client';

import * as React from 'react';
import { ChevronDown, X } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  getCyberAgentPortalContainer,
} from '@/components/ui/select';

interface Option {
  value: string;
  label: string;
}

interface CustomSelectProps {
  value: string;
  onChange: (value: string, optionData?: Option) => void;
  options: Option[];
  className?: string;
  placeholder?: string;
  label?: string;
  name?: string;
}

export function CustomSelect({
  value,
  onChange,
  options,
  className,
  placeholder = 'Please select...',
  label,
  name,
}: CustomSelectProps) {
  // 使用一个特殊值代表"全部"或"无选择"，而不是空字符串
  const EMPTY_VALUE = "__empty__";

  // 将外部的空字符串值映射为我们的特殊值
  const internalValue = value === '' ? EMPTY_VALUE : value;

  // 处理内部值变化，将特殊值映射回空字符串
  const handleValueChange = (newValue: string) => {
    // 如果新值是空字符串且当前值不是空字符串，这可能是一个意外的重置
    // 在这种情况下，我们不应该清除值，而是忽略这个变化
    if (newValue === '' && value !== '') {
      return;
    }
    
    // 找到对应的选项，以便传递完整的选项数据
    const selectedOption = internalOptions.find(opt => opt.value === newValue);
    
    // 如果是空值或找不到选项，只传递值
    if (newValue === EMPTY_VALUE || !selectedOption) {
      onChange('');
    } else {
      // 传递值和选项数据
      const finalValue = selectedOption.value === EMPTY_VALUE ? '' : selectedOption.value;
      onChange(finalValue, {
        value: finalValue,
        label: selectedOption.label
      });
    }
  };

  // 过滤和转换选项，确保没有空字符串值
  const internalOptions = options.map(option => ({
    ...option,
    value: option.value === '' ? EMPTY_VALUE : option.value
  }));

  return (
    <div className={className}>
      {label && (
        <label className="block text-sm font-medium mb-2 text-item-gray-300 font-mono">{label}</label>
      )}
      <Select value={internalValue} onValueChange={handleValueChange} name={name}>
        <SelectTrigger
          className={cn(
            'w-full bg-item-bg-card border-item-purple/30 text-white hover:bg-item-bg-hover hover:text-white font-mono',
            !value && 'text-item-gray-500',
            'focus:border-item-purple focus:outline-none transition-all duration-200',
            'min-w-0'
          )}
        >
          <div className="flex items-center w-full min-w-0 overflow-hidden">
            <div className="flex-1 min-w-0 overflow-hidden">
              <SelectValue placeholder={placeholder} className="truncate block text-left" />
            </div>
            {value && (
              <X
                className="ml-2 h-4 w-4 text-item-gray-400 hover:text-item-orange cursor-pointer transition-colors duration-200 flex-shrink-0"
                onPointerDown={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  onChange(''); // 清除值时只需传递空字符串
                }}
              />
            )}
          </div>
        </SelectTrigger>
        <SelectContent
          className="bg-item-bg-card/95 backdrop-blur-sm border-item-purple/30 text-white w-[var(--radix-select-trigger-width)]"
          portalContainer={getCyberAgentPortalContainer()}
        >
          {internalOptions.map((option) => (
            <SelectItem
              key={option.value}
              value={option.value}
              className={cn(
                'font-mono text-item-gray-300 hover:!bg-item-purple/20 focus:!bg-item-purple/20 focus:text-white transition-all duration-200',
                option.value === internalValue && '!bg-transparent border-l-2 border-item-purple',
                '[&>span]:truncate [&>span]:max-w-[calc(100%-1.25rem)] [&>span]:inline-block'
              )}
            >
              {option.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
}
