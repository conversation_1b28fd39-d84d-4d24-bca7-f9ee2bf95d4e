import { ChatHistory } from '../chatHistoryUtils';
import {
  ChatHistoryStorage,
  StorageConfig,
  ChatHistoryIndexItem,
  UserChatHistoryIndex
} from './types';
import {
  S3Client,
  PutObjectCommand,
  GetObjectCommand,
  ListObjectsV2Command
} from '@aws-sdk/client-s3';

// S3存储实现
export class S3Storage implements ChatHistoryStorage {
  private readonly s3Client: S3Client;
  private readonly bucketName: string;
  private readonly region: string;
  private readonly prefix: string;

  constructor(config: StorageConfig) {
    this.bucketName = config.s3?.bucketName || '';
    this.region = config.s3?.region || 'us-west-2';
    this.prefix = (config.s3?.prefix || 'chat-history/').replace(/\/*$/, '/');

    // 基本S3客户端配置
    const clientConfig = {
      region: this.region,
      credentials: {
        accessKeyId: config.s3?.accessKeyId || '',
        secretAccessKey: config.s3?.secretAccessKey || '',
      }
    };

    console.log('初始化S3客户端，区域:', this.region, '桶名:', this.bucketName);

    // 使用标准配置初始化S3客户端，避免自定义endpoint
    this.s3Client = new S3Client(clientConfig);
  }

  /**
   * 获取用户前缀路径
   */
  private getUserPrefix(userId: string): string {
    return `${this.prefix}${userId}/`;
  }

  /**
   * 获取对象键
   */
  private getObjectKey(userId: string, chatId: string): string {
    return `${this.getUserPrefix(userId)}${chatId}.json`;
  }

  /**
   * 获取用户特定的索引文件键
   */
  private getUserIndexKey(userId: string): string {
    return `${this.getUserPrefix(userId)}user_index.json`;
  }

  /**
   * 读取用户特定的索引文件
   */
  private async readUserIndexFile(userId: string): Promise<UserChatHistoryIndex> {
    try {
      console.log(`[S3Storage] 尝试读取用户 ${userId} 的索引文件，路径:`, this.getUserIndexKey(userId));

      const command = new GetObjectCommand({
        Bucket: this.bucketName,
        Key: this.getUserIndexKey(userId),
      });

      try {
        console.log(`[S3Storage] 发送获取用户 ${userId} 索引文件请求...`);
        const response = await this.s3Client.send(command);
        const rawData = await response.Body?.transformToString();

        if (rawData) {
          console.log(`[S3Storage] 成功读取用户 ${userId} 索引文件，长度:`, rawData.length);

          try {
            const userIndex = JSON.parse(rawData) as UserChatHistoryIndex;
            console.log(`[S3Storage] 用户 ${userId} 的索引文件包含 ${userIndex.chats.length} 条聊天记录`);
            return userIndex;
          } catch (parseError) {
            console.error(`[S3Storage] 解析用户 ${userId} 的索引文件失败:`, parseError);
            console.log(`[S3Storage] 索引文件内容: ${rawData.substring(0, 200)}...`);
            throw parseError;
          }
        } else {
          console.log(`[S3Storage] 用户 ${userId} 索引文件内容为空`);
        }
      } catch (e) {
        // 如果文件不存在，创建一个空索引
        console.log(`[S3Storage] 获取用户 ${userId} 索引文件失败:`, e);
        console.log(`[S3Storage] 用户 ${userId} 索引文件不存在，将创建新索引`);
      }

      // 创建空索引
      const emptyUserIndex: UserChatHistoryIndex = {
        userId,
        chats: []
      };

      // 保存空索引
      console.log(`[S3Storage] 创建用户 ${userId} 的新空索引文件`);
      await this.writeUserIndexFile(emptyUserIndex);

      return emptyUserIndex;
    } catch (error) {
      console.error(`[S3Storage] 读取用户 ${userId} 索引文件失败:`, error);
      // 返回空索引
      return {
        userId,
        chats: []
      };
    }
  }

  /**
   * 写入用户特定的索引文件
   */
  private async writeUserIndexFile(userIndex: UserChatHistoryIndex): Promise<void> {
    try {
      const command = new PutObjectCommand({
        Bucket: this.bucketName,
        Key: this.getUserIndexKey(userIndex.userId),
        Body: JSON.stringify(userIndex, null, 2),
        ContentType: 'application/json',
      });

      await this.s3Client.send(command);
      console.log(`[S3Storage] 成功写入用户 ${userIndex.userId} 索引文件，包含 ${userIndex.chats.length} 条聊天记录`);
    } catch (error) {
      console.error(`[S3Storage] 写入用户 ${userIndex.userId} 索引文件失败:`, error);
      throw new Error(`写入用户索引文件失败: ${error}`);
    }
  }

  /**
   * 从聊天历史生成索引项
   */
  private createIndexItem(chatHistory: ChatHistory): ChatHistoryIndexItem {
    return {
      id: chatHistory.id,
      title: chatHistory.title,
      createdAt: chatHistory.createdAt,
      updatedAt: chatHistory.updatedAt
    };
  }

  /**
   * 更新聊天历史索引
   */
  async updateChatHistoryIndex(chatHistory: ChatHistory, isDelete: boolean = false): Promise<void> {
    if (!chatHistory.userId) {
      throw new Error('更新索引需要提供用户ID');
    }

    const userId = chatHistory.userId;

    console.log(`[S3Storage] 更新用户 ${userId} 的聊天历史索引`);

    // 读取用户索引
    const userIndex = await this.readUserIndexFile(userId);

    if (isDelete) {
      // 删除模式：从索引中移除聊天历史
      userIndex.chats = userIndex.chats.filter(
        chat => chat.id !== chatHistory.id
      );
    } else {
      // 更新模式：更新或添加聊天历史到索引
      const indexItem = this.createIndexItem(chatHistory);
      const existingIndex = userIndex.chats.findIndex(
        chat => chat.id === chatHistory.id
      );

      if (existingIndex >= 0) {
        // 更新现有项
        userIndex.chats[existingIndex] = indexItem;
      } else {
        // 添加新项
        userIndex.chats.push(indexItem);
      }

      // 按更新时间倒序排序
      userIndex.chats.sort((a, b) =>
        new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
      );
    }

    // 保存用户索引
    await this.writeUserIndexFile(userIndex);
  }

  /**
   * 获取用户聊天历史索引
   */
  async getUserChatHistoryIndex(userId: string): Promise<ChatHistoryIndexItem[]> {
    console.log(`[S3Storage] 获取用户 ${userId} 的聊天历史索引`);
    const userIndex = await this.readUserIndexFile(userId);
    return userIndex.chats;
  }

  /**
   * 获取所有聊天历史索引
   */
  async getAllChatHistoryIndex(): Promise<Record<string, ChatHistoryIndexItem[]>> {
    console.log('[S3Storage] 获取所有用户的聊天历史索引');

    const result: Record<string, ChatHistoryIndexItem[]> = {};

    try {
      // 列出所有对象以找到用户目录
      const command = new ListObjectsV2Command({
        Bucket: this.bucketName,
        Prefix: this.prefix,
        Delimiter: '/'
      });

      const response = await this.s3Client.send(command);

      // 从CommonPrefixes中提取用户ID
      if (response.CommonPrefixes && response.CommonPrefixes.length > 0) {
        for (const prefix of response.CommonPrefixes) {
          if (prefix.Prefix) {
            // 从前缀中提取用户ID
            const userPrefix = prefix.Prefix;
            const userId = userPrefix.substring(this.prefix.length).replace('/', '');

            if (userId) {
              console.log(`[S3Storage] 找到用户目录: ${userId}`);

              try {
                // 读取用户特定的索引文件
                const userIndex = await this.readUserIndexFile(userId);
                result[userId] = userIndex.chats;
              } catch (userError) {
                console.error(`[S3Storage] 读取用户 ${userId} 索引失败:`, userError);
                // 如果读取用户索引失败，设置为空数组
                result[userId] = [];
              }
            }
          }
        }
      }
    } catch (error) {
      console.error('[S3Storage] 列出用户目录失败:', error);
    }

    return result;
  }

  /**
   * 重建索引文件（用于初始化或修复）
   */
  public async rebuildIndex(): Promise<void> {
    try {
      console.log('[S3Storage] 开始重建用户索引文件');

      // 获取所有用户目录
      const command = new ListObjectsV2Command({
        Bucket: this.bucketName,
        Prefix: this.prefix,
        Delimiter: '/'
      });

      const response = await this.s3Client.send(command);

      // 从CommonPrefixes中提取用户ID
      if (response.CommonPrefixes && response.CommonPrefixes.length > 0) {
        console.log(`[S3Storage] 找到 ${response.CommonPrefixes.length} 个用户目录`);

        for (const prefix of response.CommonPrefixes) {
          if (prefix.Prefix) {
            // 从前缀中提取用户ID
            const userPrefix = prefix.Prefix;
            const userId = userPrefix.substring(this.prefix.length).replace('/', '');

            if (userId) {
              try {
                console.log(`[S3Storage] 重建用户 ${userId} 的索引`);

                // 列出用户目录中的所有对象
                const userCommand = new ListObjectsV2Command({
                  Bucket: this.bucketName,
                  Prefix: userPrefix
                });

                const userResponse = await this.s3Client.send(userCommand);

                if (userResponse.Contents && userResponse.Contents.length > 0) {
                  // 过滤出聊天历史文件（排除索引文件）
                  const chatFiles = userResponse.Contents.filter(item =>
                    item.Key &&
                    item.Key.endsWith('.json') &&
                    !item.Key.endsWith('user_index.json')
                  );

                  if (chatFiles.length === 0) {
                    console.log(`[S3Storage] 用户 ${userId} 没有聊天记录，跳过`);
                    continue;
                  }

                  // 创建用户索引
                  const userIndex: UserChatHistoryIndex = {
                    userId,
                    chats: []
                  };

                  // 读取每个聊天历史文件
                  for (const file of chatFiles) {
                    if (file.Key) {
                      try {
                        const getCommand = new GetObjectCommand({
                          Bucket: this.bucketName,
                          Key: file.Key
                        });

                        const fileResponse = await this.s3Client.send(getCommand);
                        const fileContent = await fileResponse.Body?.transformToString();

                        if (fileContent) {
                          const chatHistory = JSON.parse(fileContent) as ChatHistory;

                          // 确保userId存在
                          chatHistory.userId = userId;

                          // 添加到索引
                          userIndex.chats.push(this.createIndexItem(chatHistory));
                        }
                      } catch (fileError) {
                        console.error(`[S3Storage] 处理文件 ${file.Key} 失败:`, fileError);
                      }
                    }
                  }

                  // 排序
                  if (userIndex.chats.length > 0) {
                    userIndex.chats.sort((a, b) =>
                      new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
                    );
                  }

                  // 保存用户索引
                  await this.writeUserIndexFile(userIndex);
                  console.log(`[S3Storage] 用户 ${userId} 的索引重建完成，包含 ${userIndex.chats.length} 条聊天记录`);
                } else {
                  console.log(`[S3Storage] 用户 ${userId} 目录为空，跳过`);
                }
              } catch (userError) {
                console.error(`[S3Storage] 重建用户 ${userId} 索引失败:`, userError);
              }
            }
          }
        }
      } else {
        console.log('[S3Storage] 未找到任何用户目录');
      }

      console.log('[S3Storage] 所有用户索引重建完成');
    } catch (error) {
      console.error('[S3Storage] 重建索引失败:', error);
      throw new Error('重建索引失败');
    }
  }

  /**
   * 保存聊天历史
   */
  async saveChat(chatHistory: ChatHistory): Promise<void> {
    // 确保userId存在
    if (!chatHistory.userId) {
      throw new Error('保存聊天历史需要提供用户ID');
    }

    const objectKey = this.getObjectKey(chatHistory.userId, chatHistory.id);

    // 更新 updatedAt 时间
    const now = new Date().toISOString();
    chatHistory.updatedAt = now;

    // 如果是新对话，设置创建时间
    if (!chatHistory.createdAt) {
      chatHistory.createdAt = now;
    }

    try {
      const command = new PutObjectCommand({
        Bucket: this.bucketName,
        Key: objectKey,
        Body: JSON.stringify(chatHistory, null, 2),
        ContentType: 'application/json',
      });

      await this.s3Client.send(command);
      console.log(`保存聊天历史: ${chatHistory.id} 用户: ${chatHistory.userId}`);

      // 更新索引
      await this.updateChatHistoryIndex(chatHistory);
    } catch (error) {
      console.error('保存聊天历史失败:', error);
      throw new Error('保存聊天历史失败');
    }
  }

  /**
   * 获取聊天历史列表
   */
  async getChatHistoryList(userId?: string): Promise<ChatHistory[]> {
    try {
      if (userId) {
        // 使用索引获取特定用户的聊天历史列表
        const indexItems = await this.getUserChatHistoryIndex(userId);

        if (indexItems.length > 0) {
          const chatHistories: ChatHistory[] = [];

          // 根据索引读取实际文件
          for (const item of indexItems) {
            try {
              const objectKey = this.getObjectKey(userId, item.id);

              const command = new GetObjectCommand({
                Bucket: this.bucketName,
                Key: objectKey,
              });

              const response = await this.s3Client.send(command);
              const rawData = await response.Body?.transformToString();

              if (rawData) {
                const chatHistory = JSON.parse(rawData) as ChatHistory;
                // 确保userId字段存在
                chatHistory.userId = userId;
                chatHistories.push(chatHistory);
              }
            } catch (e) {
              console.error(`读取聊天历史文件失败: ${item.id}`, e);
            }
          }

          return chatHistories;
        }

        // 如果索引为空，尝试列出用户目录中的所有对象
        const userPrefix = this.getUserPrefix(userId);
        const command = new ListObjectsV2Command({
          Bucket: this.bucketName,
          Prefix: userPrefix
        });

        const response = await this.s3Client.send(command);

        if (response.Contents && response.Contents.length > 0) {
          const chatHistories: ChatHistory[] = [];

          // 过滤出聊天历史文件（排除索引文件）
          const chatFiles = response.Contents.filter(item =>
            item.Key &&
            item.Key.endsWith('.json') &&
            !item.Key.endsWith('user_index.json')
          );

          for (const file of chatFiles) {
            if (file.Key) {
              try {
                const getCommand = new GetObjectCommand({
                  Bucket: this.bucketName,
                  Key: file.Key
                });

                const fileResponse = await this.s3Client.send(getCommand);
                const fileContent = await fileResponse.Body?.transformToString();

                if (fileContent) {
                  const chatHistory = JSON.parse(fileContent) as ChatHistory;

                  // 确保userId存在
                  chatHistory.userId = userId;

                  chatHistories.push(chatHistory);
                }
              } catch (fileError) {
                console.error(`[S3Storage] 处理文件 ${file.Key} 失败:`, fileError);
              }
            }
          }

          // 如果找到了聊天记录但没有用户索引文件，重建用户索引
          if (chatHistories.length > 0) {
            console.log(`[S3Storage] 用户 ${userId} 有 ${chatHistories.length} 条聊天记录但没有索引，重建索引`);
            await this.rebuildIndex();
          }

          // 按创建时间倒序排序
          return chatHistories.sort((a, b) =>
            new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
          );
        }

        return [];
      } else {
        // 获取所有用户的聊天历史
        const allIndexes = await this.getAllChatHistoryIndex();
        const allChatHistories: ChatHistory[] = [];

        for (const userId in allIndexes) {
          const userChats = await this.getChatHistoryList(userId);
          allChatHistories.push(...userChats);
        }

        // 按创建时间倒序排序
        return allChatHistories.sort((a, b) =>
          new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
        );
      }
    } catch (error) {
      console.error('获取聊天历史列表失败:', error);
      return [];
    }
  }

  /**
   * 获取单个聊天历史
   */
  async getChatHistory(id: string, userId?: string): Promise<ChatHistory | null> {
    try {
      if (userId) {
        // 直接获取特定用户的聊天历史
        const objectKey = this.getObjectKey(userId, id);

        try {
          const command = new GetObjectCommand({
            Bucket: this.bucketName,
            Key: objectKey,
          });

          const response = await this.s3Client.send(command);
          const rawData = await response.Body?.transformToString();

          if (rawData) {
            const chatHistory = JSON.parse(rawData) as ChatHistory;
            // 确保userId字段存在
            chatHistory.userId = userId;
            return chatHistory;
          }
        } catch (e) {
          console.error(`获取聊天历史失败: ${id}`, e);
          return null;
        }
      } else {
        // 获取所有用户的索引
        const allIndexes = await this.getAllChatHistoryIndex();

        // 在所有用户中查找匹配的聊天历史
        for (const userId in allIndexes) {
          const userChats = allIndexes[userId];
          const matchingChat = userChats.find(chat => chat.id === id);

          if (matchingChat) {
            // 找到匹配的聊天历史，获取完整内容
            return await this.getChatHistory(id, userId);
          }
        }
      }

      return null;
    } catch (error) {
      console.error(`获取聊天历史 ${id} 失败:`, error);
      return null;
    }
  }

  /**
   * 从索引中移除聊天历史（假删除）
   */
  async deleteChat(id: string, userId?: string): Promise<boolean> {
    try {
      if (userId) {
        // 直接从特定用户的索引中移除
        const objectKey = this.getObjectKey(userId, id);

        try {
          // 检查聊天历史是否存在
          const command = new GetObjectCommand({
            Bucket: this.bucketName,
            Key: objectKey,
          });

          await this.s3Client.send(command);

          // 聊天历史存在，从索引中移除
          const chatHistory: ChatHistory = {
            id,
            userId,
            title: '',
            model: 'unknown', // 添加必需的model字段
            createdAt: '',
            updatedAt: '',
            messages: []
          };

          await this.updateChatHistoryIndex(chatHistory, true);
          return true;
        } catch (e) {
          console.error(`聊天历史不存在: ${id}`, e);
          return false;
        }
      } else {
        // 在所有用户中查找匹配的聊天历史
        const allIndexes = await this.getAllChatHistoryIndex();

        for (const userId in allIndexes) {
          const userChats = allIndexes[userId];
          const matchingChat = userChats.find(chat => chat.id === id);

          if (matchingChat) {
            // 找到匹配的聊天历史，从索引中移除
            return await this.deleteChat(id, userId);
          }
        }
      }

      return false;
    } catch (error) {
      console.error(`从索引中移除聊天历史 ${id} 失败:`, error);
      return false;
    }
  }

  public async getAllUserIds(): Promise<string[]> {
    const userIds: string[] = [];
    try {
      const command = new ListObjectsV2Command({
        Bucket: this.bucketName,
        Prefix: this.prefix,
        Delimiter: '/'
      });
      const response = await this.s3Client.send(command);
      if (response.CommonPrefixes && response.CommonPrefixes.length > 0) {
        for (const prefix of response.CommonPrefixes) {
          if (prefix.Prefix) {
            const userPrefix = prefix.Prefix;
            const userId = userPrefix.substring(this.prefix.length).replace('/', '');
            if (userId) {
              userIds.push(userId);
            }
          }
        }
      }
    } catch (error) {
      console.error('[S3Storage] 获取所有用户目录失败:', error);
    }
    return userIds;
  }
}
