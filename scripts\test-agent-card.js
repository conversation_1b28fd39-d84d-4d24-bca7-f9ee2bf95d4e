#!/usr/bin/env node

/**
 * 测试Agent Card端点
 * 验证/.well-known/agent.json是否正常工作
 */

const http = require('http');

// 测试配置
const agents = [
  { name: 'Super Agent', url: 'http://localhost:3000' },
  { name: 'WMS Agent', url: 'http://localhost:3001' },
  { name: 'TMS Agent', url: 'http://localhost:3002' },
  { name: 'FMS Agent', url: 'http://localhost:3003' }
];

async function testAgentCard(agent) {
  return new Promise((resolve) => {
    const url = `${agent.url}/.well-known/agent.json`;
    console.log(`\n🔍 Testing ${agent.name} at ${url}`);
    
    const startTime = Date.now();
    
    const req = http.get(url, (res) => {
      const duration = Date.now() - startTime;
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          if (res.statusCode === 200) {
            const agentCard = JSON.parse(data);
            console.log(`✅ ${agent.name} - OK (${duration}ms)`);
            console.log(`   ID: ${agentCard.id}`);
            console.log(`   Name: ${agentCard.name}`);
            console.log(`   Version: ${agentCard.version}`);
            console.log(`   Capabilities: ${agentCard.capabilities?.length || 0}`);
            console.log(`   Skills: ${agentCard.skills?.length || 0}`);
            console.log(`   Protocol: ${agentCard.protocol || 'N/A'}`);
            
            resolve({
              success: true,
              agent: agent.name,
              duration,
              agentCard
            });
          } else {
            console.log(`❌ ${agent.name} - HTTP ${res.statusCode} (${duration}ms)`);
            console.log(`   Response: ${data.substring(0, 200)}...`);
            
            resolve({
              success: false,
              agent: agent.name,
              duration,
              error: `HTTP ${res.statusCode}`,
              response: data
            });
          }
        } catch (error) {
          console.log(`❌ ${agent.name} - Parse Error (${duration}ms)`);
          console.log(`   Error: ${error.message}`);
          console.log(`   Response: ${data.substring(0, 200)}...`);
          
          resolve({
            success: false,
            agent: agent.name,
            duration,
            error: error.message,
            response: data
          });
        }
      });
    });
    
    req.on('error', (error) => {
      const duration = Date.now() - startTime;
      console.log(`❌ ${agent.name} - Connection Error (${duration}ms)`);
      console.log(`   Error: ${error.message}`);
      
      resolve({
        success: false,
        agent: agent.name,
        duration,
        error: error.message
      });
    });
    
    req.setTimeout(10000, () => {
      req.destroy();
      const duration = Date.now() - startTime;
      console.log(`❌ ${agent.name} - Timeout (${duration}ms)`);
      
      resolve({
        success: false,
        agent: agent.name,
        duration,
        error: 'Timeout'
      });
    });
  });
}

async function testAllAgents() {
  console.log('🚀 Testing Agent Card endpoints...\n');
  console.log('=' .repeat(60));
  
  const results = [];
  
  for (const agent of agents) {
    const result = await testAgentCard(agent);
    results.push(result);
    
    // 等待一下再测试下一个
    await new Promise(resolve => setTimeout(resolve, 500));
  }
  
  // 汇总结果
  console.log('\n' + '=' .repeat(60));
  console.log('📊 Test Summary:');
  console.log('=' .repeat(60));
  
  const successful = results.filter(r => r.success);
  const failed = results.filter(r => !r.success);
  
  console.log(`✅ Successful: ${successful.length}/${results.length}`);
  console.log(`❌ Failed: ${failed.length}/${results.length}`);
  
  if (successful.length > 0) {
    console.log('\n✅ Successful Agents:');
    successful.forEach(result => {
      console.log(`   - ${result.agent} (${result.duration}ms)`);
    });
  }
  
  if (failed.length > 0) {
    console.log('\n❌ Failed Agents:');
    failed.forEach(result => {
      console.log(`   - ${result.agent}: ${result.error} (${result.duration}ms)`);
    });
  }
  
  console.log('\n' + '=' .repeat(60));
  
  // 如果有成功的，显示一个示例Agent Card
  if (successful.length > 0) {
    const example = successful[0];
    console.log(`\n📋 Example Agent Card (${example.agent}):`);
    console.log('=' .repeat(60));
    console.log(JSON.stringify(example.agentCard, null, 2));
  }
  
  process.exit(failed.length > 0 ? 1 : 0);
}

// 运行测试
testAllAgents().catch(error => {
  console.error('❌ Test failed:', error);
  process.exit(1);
}); 