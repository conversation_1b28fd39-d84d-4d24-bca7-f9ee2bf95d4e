const path = require('path');
const TerserPlugin = require('terser-webpack-plugin');
const fs = require('fs');
const webpack = require('webpack');

const site = process.env.SITE || 'unis';
const env = process.env.ENV || 'dev';
const configPath = path.resolve(__dirname, `config/${site}.${env}.json`);
let siteConfig = {};
if (fs.existsSync(configPath)) {
  siteConfig = JSON.parse(fs.readFileSync(configPath, 'utf-8'));
} else {
  console.warn(`配置文件未找到: ${configPath}`);
}

module.exports = {
  mode: 'production',
  entry: './src/embed/index.tsx',
  output: {
    path: path.resolve(__dirname, 'public'),
    filename: 'embed-bundle.js',
    library: {
      name: 'CyberAgentEmbed',
      type: 'umd',
      export: 'default',
    },
  },
  resolve: {
    extensions: ['.tsx', '.ts', '.js', '.jsx', '.css'],
    alias: {
      '@': path.resolve(__dirname, 'src'),
    },
  },
  module: {
    rules: [
      {
        test: /\.(ts|tsx)$/,
        exclude: /node_modules/,
        use: {
          loader: 'babel-loader',
          options: {
            presets: [
              '@babel/preset-env',
              '@babel/preset-react',
              '@babel/preset-typescript',
            ],
          },
        },
      },
      {
        test: /\.css$/,
        use: [
          {
            loader: 'css-loader',
            options: {
              modules: false,
              // exportType: 'string' // CSS 会转换为字符串
            }
          },
          {
            loader: 'postcss-loader',
            options: {
              postcssOptions: {
                plugins: [
                  require('tailwindcss'),
                  require('autoprefixer'),
                ],
              },
            },
          },
        ],
        // include: path.resolve(__dirname, 'src/embed'),
      },
    ],
  },
  // 不再将React和ReactDOM作为外部依赖，而是打包进bundle
  optimization: {
    minimize: true,
    minimizer: [
      new TerserPlugin({
        terserOptions: {
          format: {
            comments: false,
          },
        },
        extractComments: false,
      }),
    ],
  },
  plugins: [
    new webpack.DefinePlugin({
      __SITE_CONFIG__: JSON.stringify(siteConfig),
      'process.env.NEXT_PUBLIC_WMS_ENDPOINT': JSON.stringify(process.env.NEXT_PUBLIC_WMS_ENDPOINT),
      'process.env.NEXT_PUBLIC_PORTAL_ENDPOINT': JSON.stringify(process.env.NEXT_PUBLIC_PORTAL_ENDPOINT)
    })
  ]
};
