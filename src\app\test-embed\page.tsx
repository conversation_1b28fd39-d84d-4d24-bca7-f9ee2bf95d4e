'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { useRouter } from 'next/navigation';

const TestEmbedPage: React.FC = () => {
  const { isAuthenticated, isLoading, user, login } = useAuth();
  const router = useRouter();
  const [embedLoaded, setEmbedLoaded] = useState(false);
  const [embedError, setEmbedError] = useState<string | null>(null);
  const [agentInitialized, setAgentInitialized] = useState(false);

  // 如果未认证，重定向到登录
  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/unauthorized');
    }
  }, [isAuthenticated, isLoading, router]);

  // 加载嵌入式 agent 脚本
  useEffect(() => {
    if (!isAuthenticated) return;

    const loadEmbedScript = () => {
      // 检查是否已经加载
      if (window.CyberAgent) {
        setEmbedLoaded(true);
        console.log('CyberAgent already available');
        return;
      }

      console.log('Loading embed script...');
      const script = document.createElement('script');
      script.src = 'https://staticcontent.item.com/stage/cyberbot/agent/unisco/embed-bundle.js';
      script.async = true;
      
      script.onload = () => {
        console.log('Embed script loaded successfully');
        setEmbedLoaded(true);
        setEmbedError(null);
        
        // 等待一下确保脚本完全初始化
        setTimeout(() => {
          if (window.CyberAgent) {
            console.log('CyberAgent is now available');
            // 自动初始化
            initializeAgent();
          } else {
            console.error('CyberAgent not available after script load');
            setEmbedError('CyberAgent not available after script load');
          }
        }, 1000);
      };
      
      script.onerror = (error) => {
        console.error('Failed to load embed script:', error);
        setEmbedError('Failed to load embed script');
      };

      document.head.appendChild(script);
    };

    loadEmbedScript();
  }, [isAuthenticated]);

  // 初始化 agent
  const initializeAgent = () => {
    if (!window.CyberAgent) {
      console.error('CyberAgent not available');
      setEmbedError('CyberAgent not available');
      return;
    }

    try {
      console.log('Initializing CyberAgent...');
      
      // 获取用户token
      const token = localStorage.getItem('iam_access_token');
      console.log('Token available:', !!token);
      
      // 先调用 init 方法创建 DOM 结构
      console.log('Calling CyberAgent.init...');
      window.CyberAgent.init({
        theme: 'dark',
        position: 'bottom-right',
        minimized: false, // 初始状态不最小化，方便看到
        siteId: 'unis',
        welcomeSuggestions: [
          "Request get a freight quote", 
         "Tracking shipment order Status"
        ],
        apiUrl: window.location.origin
      });
      
      console.log('CyberAgent.init called successfully');
      setAgentInitialized(true);
      
      // init 方法会自动调用 render，所以我们不需要手动调用 render
      
    } catch (error) {
      console.error('Failed to initialize agent:', error);
      setEmbedError(`Failed to initialize agent: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-400 mx-auto"></div>
          <p className="mt-4 text-gray-300">Loading...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-white mb-4">Authentication Required</h1>
          <p className="text-gray-300 mb-6">You need to be logged in to access this page.</p>
          <button
            onClick={login}
            className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
          >
            Login
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-900 text-white flex items-center justify-center">
      <div className="max-w-md w-full mx-4">
        {/* 主卡片 */}
        <div className="bg-gray-800 rounded-lg p-6 text-center">
          <h1 className="text-2xl font-bold text-white mb-2">Embedded Agent Test</h1>
          <p className="text-gray-300 mb-6">Testing CyberBot agent</p>
          
          {/* 状态 */}
          <div className="mb-6">
            <div className="flex items-center justify-center space-x-2 mb-2">
              <div className={`w-3 h-3 rounded-full ${embedLoaded ? 'bg-green-500' : 'bg-yellow-500'}`}></div>
              <span className="text-sm">
                {embedLoaded ? 'Script Loaded' : 'Loading Script...'}
              </span>
            </div>
            
            <div className="flex items-center justify-center space-x-2 mb-2">
              <div className={`w-3 h-3 rounded-full ${agentInitialized ? 'bg-green-500' : 'bg-gray-500'}`}></div>
              <span className="text-sm">
                {agentInitialized ? 'Agent Initialized' : 'Not Initialized'}
              </span>
            </div>
          </div>

          {/* 错误显示 */}
          {embedError && (
            <div className="bg-red-900/50 border border-red-700 rounded-lg p-4 mb-6">
              <p className="text-red-200 text-sm">{embedError}</p>
            </div>
          )}

          {/* 用户信息 */}
          <div className="text-sm text-gray-400 mb-6">
            <div>User: {user?.email || 'N/A'}</div>
          </div>

          {/* 控制按钮 */}
          <div className="space-y-3">
            <button
              onClick={initializeAgent}
              disabled={!embedLoaded}
              className="w-full bg-green-600 hover:bg-green-700 disabled:bg-gray-600 text-white py-2 px-4 rounded"
            >
              Initialize Agent
            </button>
            
            <button
              onClick={() => {
                if (window.CyberAgent) {
                  window.CyberAgent.toggle();
                } else {
                  console.log('CyberAgent not available');
                }
              }}
              disabled={!agentInitialized}
              className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 text-white py-2 px-4 rounded"
            >
              Toggle Agent
            </button>
            
            <button
              onClick={() => {
                if (window.CyberAgent) {
                  window.CyberAgent.maximize();
                } else {
                  console.log('CyberAgent not available');
                }
              }}
              disabled={!agentInitialized}
              className="w-full bg-purple-600 hover:bg-purple-700 disabled:bg-gray-600 text-white py-2 px-4 rounded"
            >
              Show Agent
            </button>
          </div>

          {/* 说明 */}
          <div className="mt-6 text-xs text-gray-400">
            <p>The agent should appear in the bottom-right corner</p>
            <p>Check browser console for debug info</p>
          </div>
        </div>
      </div>
    </div>
  );
};

// 扩展 Window 接口以包含 CyberAgent
declare global {
  interface Window {
    CyberAgent?: {
      init: (options?: {
        theme?: 'light' | 'dark';
        position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';
        minimized?: boolean;
        siteId?: string;
        welcomeSuggestions?: string[];
        apiUrl?: string;
      }) => any;
      minimize: () => void;
      maximize: () => void;
      toggle: () => void;
      setTheme: (theme: string) => void;
      render: (token?: string, config?: any, siteId?: string, welcomeSuggestions?: string[], apiUrl?: string) => void;
    };
  }
}

export default TestEmbedPage; 