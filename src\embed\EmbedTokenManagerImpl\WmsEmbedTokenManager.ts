import { EmbedTokenManager } from '../EmbedTokenManager';

export class WmsEmbedTokenManager implements EmbedTokenManager {
  getRefreshToken(): string | null {
    return localStorage.getItem('refreshToken');
  }

  getCurrentToken(): string | null {
    return localStorage.getItem('token');
  }

  removeToken(): void {
    localStorage.removeItem('token');
    localStorage.removeItem('refreshToken');
  }

  async refreshToken(): Promise<string | null> {
    const accessToken = this.getCurrentToken();
    const refreshToken = this.getRefreshToken();
    if (!refreshToken) return null;
    try {
      const response = await fetch('/api/wms/auth/refresh-token', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          accessToken,
          refreshToken,
          grantType: 'AUTHORIZATION_CODE'
        })
      });
      if (!response.ok) return null;
      const data = await response.json();
      console.log('accessToken:', data.data.accessToken, ';refreshToken:', data.data.refreshToken);
      if (data.data.accessToken) {
        this.updateClientToken(data.data.accessToken);
        if (data.data.refreshToken) {
          localStorage.setItem('refreshToken', data.data.refreshToken);
        }
        return data.data.accessToken;
      }
      return null;
    } catch (e) {
      console.log('refreshToken error', e);
      return null;
    }
  }

  updateClientToken(token: string) {
    const value = token.startsWith('Bearer ') ? token : `Bearer ${token}`;
    localStorage.setItem('token', value);
  }
} 