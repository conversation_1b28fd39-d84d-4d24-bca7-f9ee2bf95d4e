import { tool } from 'ai';
import { z } from 'zod';

/**
 * Jira Server 配置
 */
const JIRA_CONFIG = {
  baseUrl: process.env.JIRA_BASE_URL || 'http://localhost:8080',
  username: process.env.JIRA_USERNAME || '',
  password: process.env.JIRA_PASSWORD || '',
};

/**
 * 创建 Basic Auth 头
 */
function createAuthHeader(): string {
  const credentials = Buffer.from(`${JIRA_CONFIG.username}:${JIRA_CONFIG.password}`).toString('base64');
  return `Basic ${credentials}`;
}

/**
 * 通用的 Jira API 请求函数
 */
async function jiraRequest(endpoint: string, options: RequestInit = {}): Promise<any> {
  const url = `${JIRA_CONFIG.baseUrl}/rest/api/2${endpoint}`;
  const method = options.method || 'GET';
  
  // 记录请求开始
  console.log(`[JiraAPI] ${method} ${url}`);
  if (options.body) {
    console.log(`[JiraAPI] Request Body:`, JSON.parse(options.body as string));
  }
  
  const defaultHeaders = {
    'Authorization': createAuthHeader(),
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  };

  // 输出请求头信息（隐藏敏感的Authorization内容）
  const headersForLog = {
    ...defaultHeaders,
    ...options.headers,
    'Authorization': defaultHeaders.Authorization ? `${defaultHeaders.Authorization.substring(0, 16)}...` : 'Not set'
  };
  console.log(`[JiraAPI] Request Headers:`, headersForLog);
  
  // 输出环境变量配置状态
  console.log(`[JiraAPI] Raw env JIRA_PASSWORD:`, process.env.JIRA_PASSWORD);
  console.log(`[JiraAPI] Config - BaseURL: ${JIRA_CONFIG.baseUrl}, Username: ${JIRA_CONFIG.username ? JIRA_CONFIG.username : 'Not set'}, Password: ${JIRA_CONFIG.password ? JIRA_CONFIG.password : 'Not set'}`);
  console.log(`[JiraAPI] Password length - Raw: ${process.env.JIRA_PASSWORD?.length || 0}, Config: ${JIRA_CONFIG.password?.length || 0}`);

  const startTime = Date.now();
  
  try {
    const response = await fetch(url, {
      ...options,
      headers: {
        ...defaultHeaders,
        ...options.headers,
      },
    });

    const responseTime = Date.now() - startTime;
    const responseText = await response.text();
    let responseData;
    
    try {
      responseData = responseText ? JSON.parse(responseText) : {};
    } catch (e) {
      responseData = { message: responseText };
    }

    // 记录响应信息
    console.log(`[JiraAPI] Response: ${response.status} ${response.statusText} (${responseTime}ms)`);
    
    if (!response.ok) {
      console.error(`[JiraAPI] Error Response:`, responseData);
      throw new Error(`Jira API Error (${response.status}): ${JSON.stringify(responseData)}`);
    }

    // 记录成功响应的关键信息
    if (responseData) {
      if (responseData.issues) {
        console.log(`[JiraAPI] Success: Found ${responseData.issues.length} issues (total: ${responseData.total || 'unknown'})`);
      } else if (responseData.key) {
        console.log(`[JiraAPI] Success: Issue ${responseData.key} (ID: ${responseData.id})`);
      } else if (responseData.transitions) {
        console.log(`[JiraAPI] Success: Found ${responseData.transitions.length} transitions`);
      } else if (responseData.projects) {
        console.log(`[JiraAPI] Success: Found ${responseData.projects.length} projects with metadata`);
      } else if (Array.isArray(responseData)) {
        console.log(`[JiraAPI] Success: Found ${responseData.length} items`);
      } else {
        console.log(`[JiraAPI] Success: Operation completed`);
      }
    }

    return responseData;
  } catch (error) {
    const responseTime = Date.now() - startTime;
    console.error(`[JiraAPI] Request failed after ${responseTime}ms:`, error instanceof Error ? error.message : error);
    throw error;
  }
}

/**
 * 获取 Jira Issue 创建元数据工具
 * 用于获取指定项目的字段定义，包括必填字段、可选字段、字段类型等信息
 */
export const getJiraCreateMeta = tool({
  description: 'Get Jira issue creation metadata for a specific project. This returns field definitions including required fields, field types, allowed values, etc. IMPORTANT: Pay attention to the field schema types and use the correct format when creating issues. The response includes fieldFormatGuide and commonMistakes to help construct proper field values.',
  parameters: z.object({
    projectKey: z.string().describe('The project key (e.g., "PROJ", "TEST")'),
    issueTypeId: z.string().optional().describe('Specific issue type ID to get metadata for. If not provided, returns metadata for all issue types in the project'),
    expand: z.string().optional().describe('Comma-separated list of fields to expand. Common values: "projects.issuetypes.fields"').default('projects.issuetypes.fields'),
    onlyGetRequiredFields: z.boolean().optional().describe('If true, filter the response to only include required fields. This filtering is done client-side after receiving the full response from Jira.').default(true)
  }),
  execute: async ({ projectKey, issueTypeId, expand, onlyGetRequiredFields }) => {
    console.log(`[getJiraCreateMeta] Starting - Project: ${projectKey}, IssueType: ${issueTypeId || 'all'}, OnlyRequired: ${onlyGetRequiredFields}`);
    
    try {
      let endpoint = `/issue/createmeta?projectKeys=${projectKey}&expand=${expand}`;
      
      if (issueTypeId) {
        endpoint += `&issuetypeIds=${issueTypeId}`;
      }

      const result = await jiraRequest(endpoint);
      
      // 如果需要只返回必填字段，进行客户端过滤
      let filteredResult = result;
      if (onlyGetRequiredFields && result.projects) {
        console.log('[getJiraCreateMeta] Filtering to show only required fields and optimizing response...');
        console.log(`[getJiraCreateMeta] Original projects count: ${result.projects.length}`);
        
        filteredResult = {
          ...result,
          projects: result.projects.map((project: any) => {
            // 只保留第一个 issue type，因为同一个项目的不同 issue type 字段定义基本相同
            const firstIssueType = project.issuetypes?.[0];
            if (!firstIssueType) {
              return { ...project, issuetypes: [] };
            }

            // 收集所有 issue type 的选项，合并到第一个 issue type 的 allowedValues 中
            const allIssueTypes: any[] = [];
            if (project.issuetypes && Array.isArray(project.issuetypes)) {
              project.issuetypes.forEach((issueType: any) => {
                if (issueType.id && issueType.name) {
                  allIssueTypes.push({
                    self: issueType.self,
                    id: issueType.id,
                    description: issueType.description,
                    iconUrl: issueType.iconUrl,
                    name: issueType.name,
                    subtask: issueType.subtask,
                    avatarId: issueType.avatarId
                  });
                }
              });
            }

            // 过滤字段：只保留必填字段和 description 字段，去掉 versions 字段
            const filteredFields = firstIssueType.fields ? Object.fromEntries(
              Object.entries(firstIssueType.fields).filter(([fieldKey, fieldValue]: [string, any]) => {
                // 跳过 versions 字段
                if (fieldKey === 'versions' || fieldKey === 'fixVersions') {
                  return false;
                }
                
                const isRequired = fieldValue?.required === true;
                // description 字段比较特殊，即使不是必填也要保留，并强制设为必填
                const isDescription = fieldKey === 'description';
                const shouldInclude = isRequired || isDescription;
                
                if (shouldInclude) {
                  console.log(`[getJiraCreateMeta] Including field: ${fieldKey} (required: ${isRequired}, isDescription: ${isDescription})`);
                }
                return shouldInclude;
              }).map(([fieldKey, fieldValue]: [string, any]) => {
                // 特殊处理 description 字段：强制设为必填
                if (fieldKey === 'description') {
                  return [fieldKey, {
                    ...fieldValue,
                    required: true
                  }];
                }
                
                // 特殊处理 issuetype 字段：合并所有 issue type 选项
                if (fieldKey === 'issuetype') {
                  return [fieldKey, {
                    ...fieldValue,
                    allowedValues: allIssueTypes
                  }];
                }
                
                return [fieldKey, fieldValue];
              })
            ) : {};

            return {
              ...project,
              issuetypes: [{
                ...firstIssueType,
                fields: filteredFields
              }]
            };
          })
        };
      }
      
      return {
        success: true,
        data: filteredResult,
        message: onlyGetRequiredFields 
          ? `Successfully retrieved required fields metadata for project ${projectKey} (optimized: showing only one issue type)`
          : `Successfully retrieved create metadata for project ${projectKey}`,
        filtered: onlyGetRequiredFields,
        formDataProcessingNote: "CRITICAL: When using requireUserInputTool, use dot notation field names (e.g., 'fields.summary', 'fields.project'). However, when calling createJiraIssue, you MUST transform the collected form data from dot notation to nested objects. Example: Form returns {\"fields.summary\": \"test\", \"fields.project\": {\"key\": \"PROJ\"}} → Pass to createJiraIssue as: {\"fields\": {\"summary\": \"test\", \"project\": {\"key\": \"PROJ\"}}}",
        fieldFormatGuide: {
          "option": "Use {\"id\": \"optionId\"} format for select fields",
          "option-with-child": "Use {\"id\": \"parentId\", \"child\": {\"id\": \"childId\"}} format for cascading select fields",
          "user": "Use {\"name\": \"username\"} or {\"key\": \"userkey\"} format for user fields",
          "project": "Use {\"key\": \"projectKey\"} or {\"id\": \"projectId\"} format for project fields",
          "issuetype": "Use {\"id\": \"issueTypeId\"} or {\"name\": \"issueTypeName\"} format for issue type fields",
          "priority": "Use {\"id\": \"priorityId\"} or {\"name\": \"priorityName\"} format for priority fields",
          "string": "Use direct string value for text fields",
          "number": "Use direct number value for number fields",
          "array": "Use array format for multi-value fields"
        },
        commonMistakes: [
          "❌ Don't use string values for option fields - use {\"id\": \"value\"} instead",
          "❌ Don't use {\"parent\": \"id\", \"child\": \"id\"} for cascading select - use {\"id\": \"parentId\", \"child\": {\"id\": \"childId\"}} instead",
          "❌ Don't pass form data with dot notation directly to createJiraIssue - transform to nested objects first",
          "✅ Always check the field schema type to determine the correct format",
          "✅ For option fields, look at allowedValues to find the correct id",
          "✅ For cascading select, the parent option contains children array with valid child options",
          "✅ Transform dot notation form data before calling createJiraIssue"
        ]
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        message: `Failed to retrieve create metadata for project ${projectKey}`
      };
    }
  }
});

/**
 * 创建 Jira Issue 工具
 * 使用动态字段对象创建 issue，AI 可以根据 createmeta 返回的字段定义构造合适的 fields 对象
 */
export const createJiraIssue = tool({
  description: 'Create a new Jira issue with dynamic fields. CRITICAL: Use correct field formats based on schema types from createmeta. Examples: option fields use {"id": "value"}, cascading select uses {"id": "parentId", "child": {"id": "childId"}}, user fields use {"name": "username"}, project uses {"key": "projectKey"}. Always refer to the fieldFormatGuide from getJiraCreateMeta response.',
  parameters: z.object({
    fields: z.record(z.any()).describe('Dynamic object containing all issue fields. MUST follow correct format based on field schema type. Example: {"project": {"key": "PROJ"}, "summary": "Issue title", "issuetype": {"id": "1"}, "description": "Issue description", "customfield_12345": {"id": "optionId"}, "assignee": {"name": "username"}}'),
    notifyUsers: z.boolean().optional().describe('Whether to send email notifications to users. Defaults to true').default(true)
  }),
  execute: async ({ fields, notifyUsers }) => {
    console.log(`[createJiraIssue] Starting - Project: ${fields.project?.key || 'unknown'}, IssueType: ${fields.issuetype?.id || fields.issuetype?.name || 'unknown'}, NotifyUsers: ${notifyUsers}`);
    console.log(`[createJiraIssue] Fields provided:`, Object.keys(fields));
    
    try {
      const requestBody = {
        fields,
        ...(notifyUsers !== undefined && { notifyUsers })
      };

      const result = await jiraRequest('/issue', {
        method: 'POST',
        body: JSON.stringify(requestBody)
      });

      return {
        success: true,
        data: result,
        message: `Successfully created issue ${result.key || result.id}`,
        issueKey: result.key,
        issueId: result.id,
        issueUrl: result.self
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        message: 'Failed to create Jira issue'
      };
    }
  }
});

/**
 * 获取 Jira 项目列表工具（辅助工具）
 * 用于帮助用户选择正确的项目
 */
export const getJiraProjects = tool({
  description: 'Get list of Jira projects that the user has access to. Useful for helping users select the correct project key when creating issues.',
  parameters: z.object({
    expand: z.string().optional().describe('Comma-separated list of fields to expand. Common values: "projectKeys"')
  }),
  execute: async ({ expand }) => {
    console.log(`[getJiraProjects] Starting - Expand: ${expand || 'none'}`);
    
    try {
      let endpoint = '/project';
      if (expand) {
        endpoint += `?expand=${expand}`;
      }

      const result = await jiraRequest(endpoint);

      // 只返回指定的项目键值
      const allowedProjectKeys = ['WISE2018', 'WSP',  'FMS', 'TMS','TMSS'];
      
      console.log(`[getJiraProjects] Total projects found: ${result.length}, Filtering for: ${allowedProjectKeys.join(', ')}`);
      
      // 只保留 id、name、key 字段，并过滤指定的项目
      const filteredResult = result
        .filter((project: any) => allowedProjectKeys.includes(project.key))
        .map((project: any) => ({
          id: project.id,
          name: project.name,
          key: project.key
        }));
      
      console.log(`[getJiraProjects] Filtered projects: ${filteredResult.map((p: any) => p.key).join(', ')}`);

      return {
        success: true,
        data: filteredResult,
        message: `Successfully retrieved ${filteredResult.length || 0} projects`
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        message: 'Failed to retrieve Jira projects'
      };
    }
  }
});

/**
 * 搜索 Jira Issues 工具
 * 使用 JQL 查询语句搜索 issues，适用于 Jira Server 7.3 版本
 */
export const searchJiraIssues = tool({
  description: 'Search for Jira issues using JQL (Jira Query Language). Supports complex queries, pagination, and field filtering. Perfect for finding issues based on project, assignee, status, dates, and other criteria. Use JQL syntax like "project = PROJ AND status = Open".',
  parameters: z.object({
    jql: z.string().describe('JQL query string. Examples: "project = PROJ", "assignee = currentUser()", "project = PROJ AND status != Closed ORDER BY created DESC"'),
    startAt: z.number().optional().describe('Starting index for pagination (0-based). Default is 0').default(0),
    maxResults: z.number().optional().describe('Maximum number of results to return (1-1000). Recommended: 50-100 for performance. Default is 50').default(50),
    fields: z.array(z.string()).optional().describe('Array of field names to return. Examples: ["key", "summary", "status", "assignee"]. If not specified, returns all fields'),
    expand: z.array(z.string()).optional().describe('Array of fields to expand. Common values: ["names", "schema", "transitions"]'),
    validateQuery: z.boolean().optional().describe('Whether to validate JQL query. Default is true').default(true)
  }),
  execute: async ({ jql, startAt, maxResults, fields, expand, validateQuery }) => {
    console.log(`[searchJiraIssues] Starting search - JQL: "${jql}"`);
    console.log(`[searchJiraIssues] Parameters - StartAt: ${startAt}, MaxResults: ${maxResults}, ValidateQuery: ${validateQuery}`);
    console.log(`[searchJiraIssues] Fields: ${fields ? fields.join(', ') : 'all'}, Expand: ${expand ? expand.join(', ') : 'none'}`);
    
    try {
      // 构建请求体
      const requestBody: any = {
        jql,
        startAt,
        maxResults,
        validateQuery: validateQuery
      };

      // 添加可选参数
      if (fields && fields.length > 0) {
        requestBody.fields = fields;
      }

      if (expand && expand.length > 0) {
        requestBody.expand = expand;
      }

      const result = await jiraRequest('/search', {
        method: 'POST',
        body: JSON.stringify(requestBody)
      });

      // 提取关键信息
      const issues = result.issues || [];
      const total = result.total || 0;
      const hasMore = (startAt + maxResults) < total;
      
      console.log(`[searchJiraIssues] Search completed - Found ${issues.length} issues out of ${total} total, HasMore: ${hasMore}`);

      // 简化 issues 数据，只保留关键字段
      const simplifiedIssues = issues.map((issue: any) => {
        const simplified: any = {
          key: issue.key,
          id: issue.id,
          self: issue.self
        };

        // 添加字段信息
        if (issue.fields) {
          simplified.fields = {};
          
          // 常用字段
          if (issue.fields.summary) simplified.fields.summary = issue.fields.summary;
          if (issue.fields.status) simplified.fields.status = {
            name: issue.fields.status.name,
            id: issue.fields.status.id
          };
          if (issue.fields.assignee) simplified.fields.assignee = {
            name: issue.fields.assignee.name,
            displayName: issue.fields.assignee.displayName
          };
          if (issue.fields.reporter) simplified.fields.reporter = {
            name: issue.fields.reporter.name,
            displayName: issue.fields.reporter.displayName
          };
          if (issue.fields.priority) simplified.fields.priority = {
            name: issue.fields.priority.name,
            id: issue.fields.priority.id
          };
          if (issue.fields.issuetype) simplified.fields.issuetype = {
            name: issue.fields.issuetype.name,
            id: issue.fields.issuetype.id
          };
          if (issue.fields.project) simplified.fields.project = {
            key: issue.fields.project.key,
            name: issue.fields.project.name,
            id: issue.fields.project.id
          };
          if (issue.fields.created) simplified.fields.created = issue.fields.created;
          if (issue.fields.updated) simplified.fields.updated = issue.fields.updated;
          if (issue.fields.description) simplified.fields.description = issue.fields.description;

          // 如果用户指定了特定字段，也包含这些字段
          if (fields && fields.length > 0) {
            fields.forEach(fieldName => {
              if (issue.fields[fieldName] && !simplified.fields[fieldName]) {
                simplified.fields[fieldName] = issue.fields[fieldName];
              }
            });
          }
        }

        return simplified;
      });

      return {
        success: true,
        data: {
          issues: simplifiedIssues,
          total,
          startAt,
          maxResults,
          hasMore,
          pagination: {
            currentPage: Math.floor(startAt / maxResults) + 1,
            totalPages: Math.ceil(total / maxResults),
            hasNextPage: hasMore,
            nextStartAt: hasMore ? startAt + maxResults : null
          }
        },
        message: `Found ${total} issues, showing ${issues.length} results (${startAt + 1}-${startAt + issues.length})`,
        jqlQuery: jql,
        performanceInfo: {
          returnedFields: fields || 'all',
          queryValidation: validateQuery,
          suggestion: maxResults > 100 ? 'Consider reducing maxResults to 50-100 for better performance' : null
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        message: `Failed to search issues with JQL: "${jql}"`,
        jqlQuery: jql,
        troubleshooting: {
          commonIssues: [
            'Check JQL syntax - use proper field names and operators',
            'Verify project key exists and you have access',
            'Ensure field names in JQL are correct (case sensitive)',
            'Try simpler query first, then add complexity'
          ],
          jqlExamples: [
            'project = PROJ',
            'assignee = currentUser()',
            'status = "In Progress"',
            'created >= -7d',
            'project = PROJ AND status != Closed ORDER BY created DESC'
          ]
        }
      };
    }
  }
});

/**
 * 获取 Jira Issue 可用的 Transition 动作工具
 * 用于获取指定 issue 当前状态下可以执行的所有 transition 动作
 */
export const getJiraIssueTransitions = tool({
  description: 'Get available transitions for a specific Jira issue. Returns all possible status transitions that can be performed on the issue from its current state. Use this to show users what actions they can take on an issue (e.g., "In Progress" -> "Done", "Open" -> "In Progress").',
  parameters: z.object({
    issueIdOrKey: z.string().describe('The issue ID or key (e.g., "PROJ-123" or "10001")'),
    expand: z.string().optional().describe('Comma-separated list of fields to expand. Common values: "transitions.fields" to get field requirements for each transition').default('transitions.fields')
  }),
  execute: async ({ issueIdOrKey, expand }) => {
    console.log(`[getJiraIssueTransitions] Starting - Issue: ${issueIdOrKey}, Expand: ${expand}`);
    
    try {
      let endpoint = `/issue/${issueIdOrKey}/transitions`;
      if (expand) {
        endpoint += `?expand=${expand}`;
      }

      const result = await jiraRequest(endpoint);

      // 简化 transitions 数据，保留关键信息
      const simplifiedTransitions = (result.transitions || []).map((transition: any) => {
        const simplified: any = {
          id: transition.id,
          name: transition.name,
          to: {
            id: transition.to?.id,
            name: transition.to?.name,
            description: transition.to?.description,
            statusCategory: transition.to?.statusCategory ? {
              id: transition.to.statusCategory.id,
              name: transition.to.statusCategory.name,
              key: transition.to.statusCategory.key,
              colorName: transition.to.statusCategory.colorName
            } : undefined
          }
        };

        // 如果有字段要求，也包含进来（用于需要用户输入的 transition）
        if (transition.fields && Object.keys(transition.fields).length > 0) {
          simplified.fields = {};
          
          // 只保留必填字段和常用字段
          Object.entries(transition.fields).forEach(([fieldKey, fieldValue]: [string, any]) => {
            if (fieldValue?.required || ['comment', 'resolution', 'assignee'].includes(fieldKey)) {
              simplified.fields[fieldKey] = {
                required: fieldValue.required || false,
                name: fieldValue.name,
                schema: fieldValue.schema,
                allowedValues: fieldValue.allowedValues,
                hasDefaultValue: fieldValue.hasDefaultValue,
                defaultValue: fieldValue.defaultValue
              };
            }
          });
        }

        return simplified;
      });

      console.log(`[getJiraIssueTransitions] Found ${simplifiedTransitions.length} transitions: ${simplifiedTransitions.map((t: any) => `${t.name}(${t.id})`).join(', ')}`);

      return {
        success: true,
        data: {
          transitions: simplifiedTransitions,
          issueKey: issueIdOrKey,
          totalTransitions: simplifiedTransitions.length
        },
        message: `Found ${simplifiedTransitions.length} available transitions for issue ${issueIdOrKey}`,
        usage: {
          description: 'Use the transition ID with doJiraIssueTransition to execute the transition',
          example: 'If user selects "Done" transition with ID "31", call doJiraIssueTransition with transitionId: "31"'
        },
        fieldFormatGuide: {
          "comment": "Use {\"body\": \"comment text\"} format for comment fields",
          "resolution": "Use {\"id\": \"resolutionId\"} or {\"name\": \"resolutionName\"} format",
          "assignee": "Use {\"name\": \"username\"} or {\"key\": \"userkey\"} format",
          "option": "Use {\"id\": \"optionId\"} format for select fields"
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        message: `Failed to get transitions for issue ${issueIdOrKey}`,
        troubleshooting: {
          commonIssues: [
            'Verify the issue key/ID exists and is accessible',
            'Check if you have permission to view the issue',
            'Ensure the issue is not in a final state (some issues may have no available transitions)'
          ]
        }
      };
    }
  }
});

/**
 * 执行 Jira Issue Transition 工具
 * 用于执行指定的 transition 动作，改变 issue 的状态
 */
export const doJiraIssueTransition = tool({
  description: 'Execute a transition on a Jira issue to change its status. Use this after getting available transitions with getJiraIssueTransitions. Can include additional fields like comments, resolution, assignee changes during the transition.',
  parameters: z.object({
    issueIdOrKey: z.string().describe('The issue ID or key (e.g., "PROJ-123" or "10001")'),
    transitionId: z.string().describe('The ID of the transition to execute (get this from getJiraIssueTransitions)'),
    fields: z.record(z.any()).optional().describe('Optional fields to update during transition. Common fields: comment ({"body": "text"}), resolution ({"id": "1"}), assignee ({"name": "username"}). Format must match field schema requirements.'),
    update: z.record(z.any()).optional().describe('Optional update operations. Used for adding comments, work logs, etc. Example: {"comment": [{"add": {"body": "Transition comment"}}]}'),
    historyMetadata: z.object({
      type: z.string().optional(),
      description: z.string().optional(),
      descriptionKey: z.string().optional(),
      activityDescription: z.string().optional(),
      activityDescriptionKey: z.string().optional(),
      emailDescription: z.string().optional(),
      emailDescriptionKey: z.string().optional(),
      actor: z.object({
        id: z.string().optional(),
        displayName: z.string().optional(),
        type: z.string().optional(),
        avatarUrl: z.string().optional(),
        url: z.string().optional()
      }).optional(),
      generator: z.object({
        id: z.string().optional(),
        type: z.string().optional()
      }).optional(),
      cause: z.object({
        id: z.string().optional(),
        type: z.string().optional()
      }).optional(),
      extraData: z.record(z.string()).optional()
    }).optional().describe('Optional history metadata for the transition')
  }),
  execute: async ({ issueIdOrKey, transitionId, fields, update, historyMetadata }) => {
    console.log(`[doJiraIssueTransition] Starting - Issue: ${issueIdOrKey}, TransitionId: ${transitionId}`);
    console.log(`[doJiraIssueTransition] Fields: ${fields ? Object.keys(fields).join(', ') : 'none'}, Update: ${update ? Object.keys(update).join(', ') : 'none'}`);
    
    try {
      const endpoint = `/issue/${issueIdOrKey}/transitions`;

      // 构建请求体
      const requestBody: any = {
        transition: {
          id: transitionId
        }
      };

      // 添加字段更新
      if (fields && Object.keys(fields).length > 0) {
        requestBody.fields = fields;
        console.log(`[doJiraIssueTransition] Adding fields:`, fields);
      }

      // 添加更新操作
      if (update && Object.keys(update).length > 0) {
        requestBody.update = update;
        console.log(`[doJiraIssueTransition] Adding update operations:`, update);
      }

      // 添加历史元数据
      if (historyMetadata) {
        requestBody.historyMetadata = historyMetadata;
        console.log(`[doJiraIssueTransition] Adding history metadata`);
      }

      const result = await jiraRequest(endpoint, {
        method: 'POST',
        body: JSON.stringify(requestBody)
      });

      // Jira transition API 通常返回 204 No Content，所以 result 可能为空
      return {
        success: true,
        data: result || { message: 'Transition completed successfully' },
        message: `Successfully executed transition ${transitionId} on issue ${issueIdOrKey}`,
        issueKey: issueIdOrKey,
        transitionId: transitionId,
        fieldsUpdated: fields ? Object.keys(fields) : [],
        updateOperations: update ? Object.keys(update) : []
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        message: `Failed to execute transition ${transitionId} on issue ${issueIdOrKey}`,
        issueKey: issueIdOrKey,
        transitionId: transitionId,
        troubleshooting: {
          commonIssues: [
            'Verify the transition ID is valid and available for this issue',
            'Check if required fields are provided with correct format',
            'Ensure you have permission to perform this transition',
            'Verify field values match the allowed values from transition metadata'
          ],
          fieldFormatExamples: {
            "comment": '{"body": "Transition comment text"}',
            "resolution": '{"id": "1"} or {"name": "Fixed"}',
            "assignee": '{"name": "username"} or {"key": "userkey"}',
            "priority": '{"id": "1"} or {"name": "High"}'
          }
        }
      };
    }
  }
});

/**
 * 导出所有 Jira 工具
 */
export const jiraTools = {
  getJiraCreateMeta,
  createJiraIssue,
  getJiraProjects,
  searchJiraIssues,
  getJiraIssueTransitions,
  doJiraIssueTransition
};

/**
 * 使用示例：结合 requireUserInputTool 实现 Issue Transition 工作流
 * 
 * 典型使用场景：
 * 1. 用户说："我想把 PROJ-123 这个 issue 的状态改一下"
 * 2. AI 调用 getJiraIssueTransitions 获取可用的 transition 动作
 * 3. AI 调用 requireUserInputTool 生成表单让用户选择 transition 和填写必要信息
 * 4. 用户提交表单后，AI 调用 doJiraIssueTransition 执行 transition
 * 
 * 示例代码：
 * 
 * // 步骤 1: 获取可用的 transitions
 * const transitions = await getJiraIssueTransitions({ issueIdOrKey: "PROJ-123" });
 * 
 * // 步骤 2: 生成用户选择表单
 * const transitionOptions = transitions.data.transitions.map(t => ({
 *   label: `${t.name} (${t.to.name})`,
 *   value: t.id
 * }));
 * 
 * await requireUserInputTool({
 *   title: "Select Issue Transition",
 *   description: `Please select the transition action to perform on issue ${issueIdOrKey}`,
 *   fields: [
 *     {
 *       name: "transitionId",
 *       label: "Transition Action",
 *       type: "select",
 *       required: true,
 *       options: transitionOptions,
 *       description: "Select the transition action to execute"
 *     },
 *     {
 *       name: "comment",
 *       label: "Comment",
 *       type: "textarea",
 *       required: false,
 *       description: "Optional: Add a comment about this status change"
 *     },
 *     // 如果 transition 需要 resolution，添加 resolution 字段
 *     {
 *       name: "resolution",
 *       label: "Resolution",
 *       type: "select",
 *       required: false,
 *       options: [
 *         { label: "Fixed", value: "1" },
 *         { label: "Won't Fix", value: "2" },
 *         { label: "Duplicate", value: "3" },
 *         { label: "Incomplete", value: "4" },
 *         { label: "Cannot Reproduce", value: "5" }
 *       ],
 *       description: "If closing the issue, please select a resolution"
 *     }
 *   ]
 * });
 * 
 * // 步骤 3: 用户提交表单后，执行 transition
 * // Assume user selected transitionId: "31", comment: "Development work completed", resolution: "1"
 * const result = await doJiraIssueTransition({
 *   issueIdOrKey: "PROJ-123",
 *   transitionId: "31",
 *   fields: {
 *     resolution: { id: "1" }  // Fixed
 *   },
 *   update: {
 *     comment: [{ add: { body: "Development work completed" } }]
 *   }
 * });
 * 
 * 高级用法：动态字段生成
 * 
 * // 根据 transition 的字段要求动态生成表单字段
 * const selectedTransition = transitions.data.transitions.find(t => t.id === selectedTransitionId);
 * const dynamicFields = [];
 * 
 * if (selectedTransition.fields) {
 *   Object.entries(selectedTransition.fields).forEach(([fieldKey, fieldMeta]) => {
 *     if (fieldKey === 'resolution' && fieldMeta.allowedValues) {
 *       dynamicFields.push({
 *         name: 'resolution',
 *         label: 'Resolution',
 *         type: 'select',
 *         required: fieldMeta.required,
 *         options: fieldMeta.allowedValues.map(v => ({
 *           label: v.name,
 *           value: v.id
 *         }))
 *       });
 *     }
 *     // 处理其他字段类型...
 *   });
 * }
 */ 