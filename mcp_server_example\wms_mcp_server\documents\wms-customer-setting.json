{"openapi": "3.0.1", "info": {"title": "WMS", "description": "", "version": "1.0.0"}, "tags": [], "paths": {"/mdm/customer/orgId/{orgId}": {"get": {"summary": "Get Customer by ORG ID", "deprecated": false, "description": "", "tags": [], "parameters": [{"name": "orgId", "in": "path", "description": "", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RCustomerDto", "description": ""}, "examples": {"1": {"summary": "成功示例", "value": {"code": 0, "msg": "", "success": false, "data": {"id": "", "orgId": "", "customerCode": "", "name": "", "fullName": "", "createdTime": "", "updatedTime": "", "createdBy": "", "updatedBy": "", "customerSetting": {"masterDataSetting": {"ssccSetting": {"companyPrefix": "", "maxCompanyPrefixLength": 0, "maxSequenceLength": 0, "retailerIds": [""], "uccType": ""}}, "inboundSetting": {"notAllowForceCloseForReceiving": false}, "outboundSetting": {"allowPartialLockInventory": false}}}}}}}}, "headers": {}}}, "security": [{"oauth2_1": []}]}}}, "components": {"schemas": {"MasterDataSetting": {"type": "object", "properties": {"ssccSettings": {"type": "array", "items": {"$ref": "#/components/schemas/SsccSetting", "description": "com.item.mdm.domain.customer.entity.customersetting.SsccSetting"}, "description": ""}, "facilityLPLabelSizeSettings": {"type": "array", "items": {"$ref": "#/components/schemas/FacilityLPLabelSizeSetting", "description": "com.item.mdm.domain.customer.entity.customersetting.FacilityLPLabelSizeSetting"}, "description": ""}, "companyPrefix": {"type": "string", "description": ""}, "holidays": {"type": "array", "items": {"$ref": "#/components/schemas/Holiday", "description": "com.item.mdm.domain.company.entity.Holiday"}, "description": ""}, "defaultSerialNoValidationRegex": {"type": "string", "description": ""}}}, "Holiday": {"type": "object", "properties": {"date": {"type": "string", "description": ""}, "description": {"type": "string", "description": ""}}}, "FacilityLPLabelSizeSetting": {"type": "object", "properties": {"facilityId": {"type": "string", "description": ""}, "lpLabelSize": {"type": "string", "description": "", "enum": ["TOW_ONE", "FOUR_SIX"]}}}, "SsccSetting": {"type": "object", "properties": {"facilityId": {"type": "string", "description": ""}, "companyPrefix": {"type": "string", "description": ""}, "extensionCode": {"type": "string", "description": ""}, "maxCompanyPrefixLength": {"type": "integer", "description": ""}, "maxSequenceLength": {"type": "integer", "description": ""}, "retailerIds": {"type": "array", "items": {"type": "string"}, "description": ""}, "uccType": {"type": "string", "description": ""}}}, "InboundSetting": {"type": "object", "properties": {"receiptUniqueKeys": {"type": "array", "items": {"type": "string"}, "description": "", "default": "new ArrayList<>(Arrays.asList(\"poNo\"))"}, "putAwayLocationTypes": {"type": "array", "items": {"type": "string", "enum": ["CUSTOMER_VLG", "ITEM_VLG", "ITEM_GROUP_VLG", "TITLE_VLG"]}, "description": ""}, "putAwayStrategies": {"type": "array", "items": {"type": "string", "enum": ["SAME_ITEM", "SAME_LOT_NO", "SAME_EXPIRATION_DATE", "SAME_RN_AND_EXPIRATION_DATE"]}, "description": ""}, "skipPutAwayOnReceive": {"type": "boolean", "description": ""}, "allowedReceivingGoodsTypes": {"type": "array", "items": {"type": "string"}, "description": ""}, "notSendReceiveConfirmationResources": {"type": "array", "items": {"type": "string", "enum": ["OMS", "PUBLIC_API", "MANUAL"]}, "description": ""}, "receiveQtyChecks": {"type": "array", "items": {"$ref": "#/components/schemas/ReceiveQtyCheck", "description": "com.item.mdm.domain.customer.entity.ReceiveQtyCheck"}, "description": ""}, "allowReceiveLocationTypes": {"type": "array", "items": {"type": "string"}, "description": ""}, "enableSnCheckByPreProvidedSnFile": {"type": "boolean", "description": ""}, "allowReceiveSNNotInPreProvidedSnFile": {"type": "boolean", "description": ""}, "enableSNCheckWithoutSupplierAndFacility": {"type": "boolean", "description": ""}, "enableSNCheckWithoutContainer": {"type": "boolean", "description": ""}, "preProvidedSnCheckSupplierFacilities": {"type": "array", "items": {"$ref": "#/components/schemas/SupplierFacilitySetting", "description": "com.item.mdm.domain.customer.entity.customersetting.SupplierFacilitySetting"}, "description": ""}, "allowAutoCloseReceipt": {"type": "boolean", "description": ""}, "putAwayMode": {"type": "string", "description": "", "enum": ["AFTER_RECEIVING", "DURING_RECEIVING"]}, "receiptCloseTiming": {"type": "string", "description": "", "enum": ["AFTER_RECEIVING", "AFTER_PUT_AWAY"]}, "rcCartonLevel": {"type": "string", "description": "", "enum": ["SN", "PALLET"]}, "inventoryStatusControl": {"type": "string", "description": "", "enum": ["AVAILABLE_AFTER_PUT_AWAY", "AVAILABLE_AFTER_RECEIPT_CLOSE"]}, "allowManualPutAwayByRN": {"type": "boolean", "description": ""}, "allowPutAwayWithExceptionReceipt": {"type": "boolean", "description": ""}, "displayStackHighAtPutAwayTask": {"type": "boolean", "description": ""}, "forbidCloseReceiveTaskBeforePutAway": {"type": "boolean", "description": ""}, "allowReceiveMethods": {"type": "array", "items": {"type": "string", "enum": ["RECEIVE_BY_SINGLE_ITEM", "RECEIVE_TO_PICK_LOCATION", "RECEIVE_TO_PUT_AWAY", "RECEIVE_BY_PALLET", "RECEIVE_BY_MIX_ITEM", "RECEIVE_BY_CARTON"]}, "description": ""}, "forceDefaultUOMReceiving": {"type": "boolean", "description": "", "default": false}}}, "SupplierFacilitySetting": {"type": "object", "properties": {"facilityId": {"type": "string", "description": ""}, "supplierId": {"type": "string", "description": ""}}}, "ReceiveQtyCheck": {"type": "object", "properties": {"receiptType": {"type": "string", "description": "", "enum": ["REGULAR_RECEIPT", "TITLE_TRANSFER_RECEIPT", "MIGO_TRANSFER_RECEIPT", "INTERNAL_TRANSFER_RECEIVING", "TRANSLOAD", "MATERIAL_PURCHASE"]}, "allowShortReceive": {"type": "boolean", "description": ""}, "notAllowOverReceive": {"type": "boolean", "description": ""}, "allowPartialReceive": {"type": "boolean", "description": ""}}}, "InventorySetting": {"type": "object", "properties": {"autoUpdateExpirationDate": {"type": "boolean", "description": ""}, "holdInventoryBeforeShipAllowDays": {"type": "boolean", "description": ""}}}, "RCustomerDto": {"type": "object", "properties": {"code": {"type": "integer", "description": ""}, "msg": {"type": "string", "description": ""}, "success": {"type": "boolean", "description": ""}, "data": {"$ref": "#/components/schemas/CustomerDto", "description": ""}}}, "CustomerDto": {"type": "object", "properties": {"id": {"type": "string", "description": ""}, "orgId": {"type": "string", "description": ""}, "customerCode": {"type": "string", "description": ""}, "name": {"type": "string", "description": ""}, "fullName": {"type": "string", "description": ""}, "createdTime": {"type": "string", "description": ""}, "updatedTime": {"type": "string", "description": ""}, "createdBy": {"type": "string", "description": ""}, "updatedBy": {"type": "string", "description": ""}, "masterDataSetting": {"$ref": "#/components/schemas/MasterDataSetting", "description": ""}, "inboundSetting": {"$ref": "#/components/schemas/InboundSetting", "description": ""}, "outboundSetting": {"description": "", "type": "object", "properties": {}}, "inventorySetting": {"$ref": "#/components/schemas/InventorySetting", "description": ""}, "billingPaySetting": {"$ref": "#/components/schemas/BillingPaySetting", "description": ""}, "dynamicFieldMapping": {"$ref": "#/components/schemas/DynamicFieldMapping", "description": ""}}}, "DynamicFieldMapping": {"type": "object", "properties": {"orderDynamicFieldMapping": {"type": "array", "items": {"$ref": "#/components/schemas/DynamicFieldMappingItem", "description": "com.item.mdm.domain.customer.entity.customersetting.DynamicFieldMappingItem"}, "description": ""}, "orderItemLineDynamicFieldMapping": {"type": "array", "items": {"$ref": "#/components/schemas/DynamicFieldMappingItem", "description": "com.item.mdm.domain.customer.entity.customersetting.DynamicFieldMappingItem"}, "description": ""}, "receiptDynamicFieldMapping": {"type": "array", "items": {"$ref": "#/components/schemas/DynamicFieldMappingItem", "description": "com.item.mdm.domain.customer.entity.customersetting.DynamicFieldMappingItem"}, "description": ""}, "receiptItemLineDynamicFieldMapping": {"type": "array", "items": {"$ref": "#/components/schemas/DynamicFieldMappingItem", "description": "com.item.mdm.domain.customer.entity.customersetting.DynamicFieldMappingItem"}, "description": ""}}}, "DynamicFieldMappingItem": {"type": "object", "properties": {"dynamicField": {"type": "string", "description": ""}, "dynamicName": {"type": "string", "description": ""}}}, "BillingPaySetting": {"type": "object", "properties": {"billingPay": {"type": "boolean", "description": ""}, "billingStack": {"type": "string", "description": "", "enum": ["ITEM", "LOCATION", "ITEM_LOCATION", "LOCATION_ITEM"]}}}}, "securitySchemes": {"oauth2_1": {"type": "oauth2", "flows": {"password": {"authorizationUrl": "{{authUrl}}", "tokenUrl": "{{accessTokenUrl}}", "refreshUrl": "", "scopes": {"profile": "", "email": "", "phone": "", "openid": ""}}}}}}, "servers": [], "security": [{"oauth2_1": []}]}