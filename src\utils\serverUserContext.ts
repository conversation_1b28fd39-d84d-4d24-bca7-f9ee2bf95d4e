/**
 * 服务端用户上下文管理
 * 
 * 这个模块实现了服务端用户上下文管理，包含:
 * - 短期会话数据 (内存存储)
 * - 长期用户偏好 (SQLite持久存储)
 */

import Database from 'better-sqlite3';
import path from 'path';
import fs from 'fs';

// 类型定义
export interface SessionContext {
  userId: string;
  username: string;
  email?: string;
  currentTenant?: string;
  currentFacility?: string;
  lastActivity: number;
}

export interface UserPreferences {
  defaultTenant?: string;
  defaultFacility?: string;
  preferredLanguage?: string;
  theme?: string;
}

export interface UserContext {
  session: SessionContext;
  preferences: UserPreferences;
}

/**
 * 服务端内存会话存储
 * 管理服务端内存中的会话数据 (登出时清除，服务重启时重置)
 */
class ServerSessionStore {
  private store: Map<string, SessionContext>;

  constructor() {
    this.store = new Map();
  }

  /**
   * 获取会话数据
   */
  get(userId: string): SessionContext | undefined {
    return this.store.get(userId);
  }

  /**
   * 设置会话数据
   */
  set(userId: string, session: SessionContext): void {
    this.store.set(userId, {
      ...session,
      lastActivity: Date.now()
    });
  }

  /**
   * 清除用户会话
   */
  clearUserSession(userId: string): void {
    this.store.delete(userId);
  }

  /**
   * 更新会话的特定字段
   */
  update(userId: string, updates: Partial<SessionContext>): SessionContext | undefined {
    const currentSession = this.store.get(userId);
    if (!currentSession) return undefined;

    const updatedSession = {
      ...currentSession,
      ...updates,
      lastActivity: Date.now()
    };

    this.store.set(userId, updatedSession);
    return updatedSession;
  }
}

/**
 * SQLite偏好存储
 * 管理服务端持久化的用户偏好
 */
class SQLitePreferenceStore {
  private db: any;
  private initialized: boolean = false;

  constructor(dbPath: string = './data/userPrefs.db') {
    try {
      // 确保目录存在
      const dir = path.dirname(dbPath);
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }
      
      // 初始化 SQLite 数据库
      this.db = new Database(dbPath);
      
      // 创建表结构
      this.db.exec(`
        CREATE TABLE IF NOT EXISTS user_preferences (
          key TEXT PRIMARY KEY,
          value TEXT NOT NULL
        )
      `);
      
      this.initialized = true;
      console.log('[Server] SQLite 偏好存储初始化成功');
    } catch (error) {
      console.error('[Server] 初始化 SQLite 失败:', error);
      this.initialized = false;
    }
  }

  async getUserPreferences(userId: string): Promise<UserPreferences> {
    if (!this.initialized) return {};
    
    try {
      // 准备查询语句
      const stmt = this.db.prepare('SELECT value FROM user_preferences WHERE key = ?');
      
      // 执行查询
      const row = stmt.get(`user:${userId}:preferences`);
      
      // 如果找到数据，解析并返回
      if (row) {
        return JSON.parse(row.value);
      }
      
      // 没有找到数据，返回空对象
      return {};
    } catch (error) {
      console.error(`[Server] 获取用户偏好失败 ${userId}:`, error);
      return {};
    }
  }

  async updateUserPreferences(userId: string, preferences: Partial<UserPreferences>): Promise<UserPreferences> {
    if (!this.initialized) return preferences;
    
    try {
      // 准备查询获取现有偏好
      const getStmt = this.db.prepare('SELECT value FROM user_preferences WHERE key = ?');
      const key = `user:${userId}:preferences`;
      
      // 获取现有偏好
      let existingPrefs: UserPreferences = {};
      const row = getStmt.get(key);
      
      if (row) {
        existingPrefs = JSON.parse(row.value);
      }

      // 合并新偏好
      const mergedPrefs = {
        ...existingPrefs,
        ...preferences,
      };
      
      // 准备插入/更新语句
      const setStmt = this.db.prepare('INSERT OR REPLACE INTO user_preferences (key, value) VALUES (?, ?)');
      
      // 保存到数据库
      setStmt.run(key, JSON.stringify(mergedPrefs));
      
      return mergedPrefs;
    } catch (error) {
      console.error(`[Server] 更新用户偏好失败 ${userId}:`, error);
      return preferences;
    }
  }
  
  close() {
    if (this.initialized && this.db) {
      try {
        this.db.close();
        console.log('[Server] SQLite 数据库连接已关闭');
      } catch (error) {
        console.error('[Server] 关闭 SQLite 连接失败:', error);
      }
    }
  }
}

/**
 * 服务端用户上下文管理器
 */
export class ServerUserContextManager {
  private static instance: ServerUserContextManager;
  private sessionStore: ServerSessionStore;
  private prefStore: SQLitePreferenceStore;
  
  private constructor() {
    this.sessionStore = new ServerSessionStore();
    this.prefStore = new SQLitePreferenceStore();
  }
  
  /**
   * 获取单例实例
   */
  public static getInstance(): ServerUserContextManager {
    if (!ServerUserContextManager.instance) {
      ServerUserContextManager.instance = new ServerUserContextManager();
    }
    return ServerUserContextManager.instance;
  }

  /**
   * 初始化用户会话
   * 在用户登录时调用
   */
  async initUserSession(
    userId: string, 
    username: string,
    email?: string,
    tenant?: string,
    facility?: string
  ): Promise<UserContext> {
    // 首先清除可能存在的旧会话
    this.clearUserSession(userId);
    
    // 创建会话上下文
    const sessionContext: SessionContext = {
      userId,
      username,
      email,
      currentTenant: tenant,
      currentFacility: facility,
      lastActivity: Date.now()
    };
    
    // 保存到会话存储
    this.sessionStore.set(userId, sessionContext);
    
    // 获取用户偏好
    const preferences = await this.prefStore.getUserPreferences(userId);
    
    // 如果会话中没有设置租户/设施，但偏好中有默认值，则使用默认值
    if (!sessionContext.currentTenant && preferences.defaultTenant) {
      sessionContext.currentTenant = preferences.defaultTenant;
      this.sessionStore.update(userId, { currentTenant: preferences.defaultTenant });
    }
    
    if (!sessionContext.currentFacility && preferences.defaultFacility) {
      sessionContext.currentFacility = preferences.defaultFacility;
      this.sessionStore.update(userId, { currentFacility: preferences.defaultFacility });
    }
    
    return {
      session: sessionContext,
      preferences
    };
  }

  /**
   * 获取用户上下文
   */
  async getUserContext(userId: string): Promise<UserContext | null> {
    // 获取会话数据
    const sessionData = this.sessionStore.get(userId);
    if (!sessionData) {
      return null; // 用户未登录或会话已过期
    }
    
    // 更新最后活动时间
    this.sessionStore.update(userId, { lastActivity: Date.now() });
    
    // 获取用户偏好
    const preferences = await this.prefStore.getUserPreferences(userId);
    
    return {
      session: sessionData,
      preferences
    };
  }

  /**
   * 更新会话上下文
   */
  updateSessionContext(userId: string, updates: Partial<SessionContext>): SessionContext | undefined {
    return this.sessionStore.update(userId, updates);
  }

  /**
   * 更新用户偏好
   */
  async updateUserPreferences(userId: string, preferences: Partial<UserPreferences>): Promise<UserPreferences> {
    return this.prefStore.updateUserPreferences(userId, preferences);
  }

  /**
   * 清除用户会话
   */
  clearUserSession(userId: string): void {
    this.sessionStore.clearUserSession(userId);
  }
}

// 导出单例实例
export const serverUserContextManager = ServerUserContextManager.getInstance(); 