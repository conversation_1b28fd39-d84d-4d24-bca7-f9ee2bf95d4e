import { WmsUserInfo } from './clientUserContext';
import { WmsInitStrategy } from './wmsStateManager';
import { fetchWmsUserInfo } from './wmsService';
import { wmsStateManager } from './wmsStateManager';
import { log } from './logger';

/**
 * 策略工厂
 * 根据当前上下文创建合适的初始化策略
 */
export function createWmsInitStrategy(): WmsInitStrategy {
  // 检查是否有保存的设施，用于判断是首次登录还是刷新页面
  const hasSavedFacility = wmsStateManager.hasSavedFacility();
  
  if (hasSavedFacility) {
    log.debug('检测到已保存的设施信息，使用刷新页面初始化策略', undefined, 'wmsInitStrategies');
    return new RefreshInitStrategy();
  } else {
    log.debug('未检测到已保存的设施信息，使用首次登录初始化策略', undefined, 'wmsInitStrategies');
    return new FirstLoginInitStrategy();
  }
}

/**
 * 首次登录初始化策略
 * 特点：加载所有设施，并使用系统默认设施
 */
export class FirstLoginInitStrategy implements WmsInitStrategy {
  async execute(userId: string): Promise<WmsUserInfo | null> {
    log.debug('执行首次登录初始化策略', undefined, 'wmsInitStrategies');
    
    try {
      // 完整获取WMS用户信息，包括系统默认设施
      const wmsUserInfo = await fetchWmsUserInfo(false);
      
      if (!wmsUserInfo) {
        log.error('首次登录初始化失败：无法获取WMS用户信息', undefined, 'wmsInitStrategies');
        return null;
      }
      
      // 确保用户ID正确
      wmsUserInfo.id = userId;
      
      log.debug('首次登录初始化成功，使用系统默认设施:', 
        wmsUserInfo.currentFacility?.name || '未设置默认设施', 'wmsInitStrategies');
      
      return wmsUserInfo;
    } catch (error) {
      log.error('首次登录初始化策略执行失败:', error, 'wmsInitStrategies');
      return null;
    }
  }
}

/**
 * 页面刷新初始化策略
 * 特点：优先使用localStorage中的设施，并保留用户之前的选择
 */
export class RefreshInitStrategy implements WmsInitStrategy {
  async execute(userId: string): Promise<WmsUserInfo | null> {
    log.debug('执行页面刷新初始化策略', undefined, 'wmsInitStrategies');
    
    try {
      // 获取保存的设施ID
      const savedFacilityId = wmsStateManager.getCurrentFacilityId();
      
      // 获取最新的WMS用户信息，但不设置默认设施
      const wmsUserInfo = await fetchWmsUserInfo(true);
      
      if (!wmsUserInfo) {
        log.error('页面刷新初始化失败：无法获取WMS用户信息', undefined, 'wmsInitStrategies');
        return null;
      }
      
      // 确保用户ID正确
      wmsUserInfo.id = userId;
      
      // 如果有保存的设施ID，尝试匹配
      if (savedFacilityId && wmsUserInfo.facilities && wmsUserInfo.facilities.length > 0) {
        const savedFacility = wmsUserInfo.facilities.find(f => f.id === savedFacilityId);
        
        if (savedFacility) {
          log.debug('恢复用户之前选择的设施:', savedFacility.name, 'wmsInitStrategies');
          wmsUserInfo.currentFacility = savedFacility;
        } else {
          log.warn(`无法找到之前选择的设施(${savedFacilityId})，使用默认设施`, undefined, 'wmsInitStrategies');
          // 如果没有已设置的当前设施，使用第一个
          if (!wmsUserInfo.currentFacility && wmsUserInfo.facilities.length > 0) {
            wmsUserInfo.currentFacility = wmsUserInfo.facilities[0];
            log.debug('使用第一个设施作为默认:', wmsUserInfo.currentFacility.name, 'wmsInitStrategies');
          }
        }
      } else if (!wmsUserInfo.currentFacility && wmsUserInfo.facilities && wmsUserInfo.facilities.length > 0) {
        // 如果没有保存的设施ID，也没有当前设施，使用第一个
        wmsUserInfo.currentFacility = wmsUserInfo.facilities[0];
        log.debug('没有保存的设施记录，使用第一个设施:', wmsUserInfo.currentFacility.name, 'wmsInitStrategies');
      }
      
      log.debug('页面刷新初始化成功，使用设施:', 
        wmsUserInfo.currentFacility?.name || '未设置设施', 'wmsInitStrategies');
      
      return wmsUserInfo;
    } catch (error) {
      log.error('页面刷新初始化策略执行失败:', error, 'wmsInitStrategies');
      return null;
    }
  }
} 