import { Facility, WmsUserInfo } from './clientUserContext';
import { log } from './logger';

// 本地存储键
const STORAGE_KEYS = {
  WMS_USER_INFO: 'wms_user_info',
  CURRENT_FACILITY: 'wms_current_facility'
};

// 初始化策略接口
export interface WmsInitStrategy {
  execute(userId: string): Promise<WmsUserInfo | null>;
}

/**
 * WMS状态管理器
 * 负责WMS状态的存储、恢复和管理
 */
class WmsStateManager {
  private static instance: WmsStateManager;

  private constructor() {}

  /**
   * 获取单例实例
   */
  public static getInstance(): WmsStateManager {
    if (!WmsStateManager.instance) {
      WmsStateManager.instance = new WmsStateManager();
    }
    return WmsStateManager.instance;
  }

  /**
   * 从localStorage保存WMS用户信息
   */
  saveWmsUserInfo(wmsUserInfo: WmsUserInfo): void {
    try {
      // 数据校验
      if (!wmsUserInfo || !wmsUserInfo.id) {
        console.error('尝试保存无效的WMS用户信息');
        return;
      }
      
      if (!Array.isArray(wmsUserInfo.facilities)) {
        console.error('WMS用户信息缺少设施数组');
        return;
      }
      
      // 创建简化的设施对象，避免循环引用
      const simplifiedFacilities = wmsUserInfo.facilities.map(facility => ({
        id: facility.id,
        name: facility.name,
        code: facility.code
      }));
      
      // 简化当前设施对象
      let simplifiedCurrentFacility = null;
      if (wmsUserInfo.currentFacility) {
        simplifiedCurrentFacility = {
          id: wmsUserInfo.currentFacility.id,
          name: wmsUserInfo.currentFacility.name,
          code: wmsUserInfo.currentFacility.code
        };
      }
      
      // 准备存储数据，确保ID为字符串
      const dataForStorage = {
        id: String(wmsUserInfo.id),
        username: wmsUserInfo.username,
        fullName: wmsUserInfo.fullName,
        email: wmsUserInfo.email,
        tenantId: wmsUserInfo.tenantId,
        facilities: simplifiedFacilities,
        currentFacility: simplifiedCurrentFacility,
        roles: wmsUserInfo.roles
      };
      
      // 序列化并保存
      localStorage.setItem(STORAGE_KEYS.WMS_USER_INFO, JSON.stringify(dataForStorage));
      console.log(`WMS用户信息已保存到localStorage，包含${simplifiedFacilities.length}个设施`);
      
      // 同时单独保存当前设施ID，便于快速访问
      if (wmsUserInfo.currentFacility) {
        localStorage.setItem(STORAGE_KEYS.CURRENT_FACILITY, wmsUserInfo.currentFacility.id);
        console.log('当前设施已保存到localStorage:', wmsUserInfo.currentFacility.name);
      }
    } catch (error) {
      console.error('保存WMS用户信息到localStorage失败:', error);
      this.handleStorageError(error, wmsUserInfo);
    }
  }

  /**
   * 处理存储错误，尝试备用存储方案
   */
  private handleStorageError(error: any, wmsUserInfo: WmsUserInfo): void {
    if (error instanceof TypeError && error.message.includes('circular')) {
      console.error('检测到循环引用。请确保对象没有循环引用');
    }
    
    try {
      // 保存最小必要信息
      if (wmsUserInfo && wmsUserInfo.facilities) {
        const minimalData = {
          id: String(wmsUserInfo.id),
          facilities: wmsUserInfo.facilities.map(f => ({ id: f.id, name: f.name, code: f.code })),
          currentFacility: wmsUserInfo.currentFacility ? 
            { id: wmsUserInfo.currentFacility.id, name: wmsUserInfo.currentFacility.name, code: wmsUserInfo.currentFacility.code } : 
            null
        };
        localStorage.setItem(STORAGE_KEYS.WMS_USER_INFO, JSON.stringify(minimalData));
        console.log('保存了简化版的WMS用户信息');
      }
    } catch (fallbackError) {
      console.error('保存简化的WMS用户数据也失败:', fallbackError);
    }
  }

  /**
   * 从localStorage获取WMS用户信息
   */
  getWmsUserInfo(): WmsUserInfo | null {
    try {
      const storedData = localStorage.getItem(STORAGE_KEYS.WMS_USER_INFO);
      if (!storedData) {
        console.log('localStorage中没有找到WMS用户信息');
        return null;
      }
      
      // 解析存储的数据
      const wmsUserData = JSON.parse(storedData);
      log.debug('从localStorage获取WMS数据成功', {
        hasFacilities: Array.isArray(wmsUserData.facilities) && wmsUserData.facilities.length > 0,
        facilitiesCount: Array.isArray(wmsUserData.facilities) ? wmsUserData.facilities.length : 0,
        hasCurrentFacility: !!wmsUserData.currentFacility,
        tenantId: wmsUserData.tenantId
      }, 'wmsStateManager');
      
      return wmsUserData;
    } catch (error) {
      console.error('从localStorage获取WMS用户信息失败:', error);
      return null;
    }
  }

  /**
   * 从localStorage获取当前设施ID
   */
  getCurrentFacilityId(): string | null {
    // 先从用户信息中获取
    const wmsUserInfo = this.getWmsUserInfo();
    if (wmsUserInfo?.currentFacility?.id) {
      return wmsUserInfo.currentFacility.id;
    }
    
    // 如果用户信息中没有，从单独存储中获取
    return localStorage.getItem(STORAGE_KEYS.CURRENT_FACILITY);
  }

  /**
   * 检查是否有保存的设施
   * 用于确定当前上下文（首次登录还是刷新）
   */
  hasSavedFacility(): boolean {
    return !!this.getCurrentFacilityId();
  }

  /**
   * 设置当前设施
   */
  setCurrentFacility(wmsUserInfo: WmsUserInfo, facilityId: string): WmsUserInfo | null {
    try {
      if (!wmsUserInfo || !Array.isArray(wmsUserInfo.facilities)) {
        console.error('无法设置当前设施：无效的WMS用户信息');
        return null;
      }
      
      // 查找设施
      const facility = wmsUserInfo.facilities.find(f => f.id === facilityId);
      if (!facility) {
        console.error('无法设置当前设施：设施不存在:', facilityId);
        return null;
      }
      
      // 更新当前设施
      const updatedUserInfo = {
        ...wmsUserInfo,
        currentFacility: facility
      };
      
      // 保存到localStorage
      this.saveWmsUserInfo(updatedUserInfo);
      console.log('成功设置当前设施:', facility.name);
      
      return updatedUserInfo;
    } catch (error) {
      console.error('设置当前设施失败:', error);
      return null;
    }
  }

  /**
   * 初始化WMS状态
   * 根据上下文选择合适的初始化策略
   */
  async initializeState(userId: string, strategy: WmsInitStrategy): Promise<WmsUserInfo | null> {
    try {
      const wmsUserInfo = await strategy.execute(userId);
      
      if (wmsUserInfo) {
        // 确保正确的用户ID
        wmsUserInfo.id = userId;
        
        // 保存状态
        this.saveWmsUserInfo(wmsUserInfo);
        return wmsUserInfo;
      }
      
      return null;
    } catch (error) {
      console.error('初始化WMS状态失败:', error);
      return null;
    }
  }

  /**
   * 清除存储的WMS数据
   */
  clearWmsData(): void {
    localStorage.removeItem(STORAGE_KEYS.WMS_USER_INFO);
    localStorage.removeItem(STORAGE_KEYS.CURRENT_FACILITY);
    console.log('已清除WMS数据');
  }
}

// 导出单例实例
export const wmsStateManager = WmsStateManager.getInstance(); 