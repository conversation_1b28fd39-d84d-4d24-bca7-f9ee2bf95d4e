'use client';

import * as React from 'react';
import { useState, useEffect, useRef } from 'react';
import { User, Search, Loader2 } from 'lucide-react';
// @ts-ignore 忽略类型检查
import { debounce } from 'lodash';
import { portalApi } from '@/utils/portalApiClient';

import { Input } from '@/components/ui/input';
import { cn } from '@/lib/utils';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
  getCyberAgentPortalContainer,
} from "@/components/ui/select";

interface CustomerOption {
  id: string;
  name: string;
  code?: string;
  status?: string;
}

interface CustomerSelectorProps {
  value?: string;
  onChange: (value: string, customerData?: CustomerOption) => void;
  placeholder?: string;
  disabled?: boolean;
  required?: boolean;
  apiHeaders?: Record<string, string>;
  defaultValue?: string;
}

// 获取 ssoUsername（email）
function getSsoUsernameFromLocalStorage(): string | undefined {
  // return "<EMAIL>";
  try {
    const subjectStr = localStorage.getItem('item-client-web/subject');
    if (!subjectStr) return undefined;
    const subjectObj = JSON.parse(subjectStr);
    if (subjectObj.value && typeof subjectObj.value === 'object') {
      return subjectObj.value.email;
    }
    if (typeof subjectObj.value === 'string') {
      const valueObj = JSON.parse(subjectObj.value);
      return valueObj.email;
    }
    return undefined;
  } catch {
    return undefined;
  }
}

export function CustomerSelector({
  value,
  onChange,
  placeholder = 'Select customer',
  disabled = false,
  required = false,
  apiHeaders = {},
  defaultValue
}: CustomerSelectorProps) {
  const API_PATH = 'api/v1/web/crm-hand-out/cpapi/tms/user/customer';
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [options, setOptions] = useState<CustomerOption[]>([]);
  const [selectedCustomer, setSelectedCustomer] = useState<CustomerOption | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const searchInputRef = useRef<HTMLInputElement>(null);
  const initialLoadRef = useRef(false);

  useEffect(() => {
    if (!initialLoadRef.current && defaultValue && !value) {
      initialLoadRef.current = true;
      fetchCustomerById(defaultValue).then(customer => {
        if (customer) {
          onChange(customer.id, customer);
        }
      });
    }
  }, [defaultValue, value]);

  const extractCustomerData = (customer: any): CustomerOption => {
    return {
      id: customer.location_id?.toString() || '',
      name: customer.location_name || 'Unknown Customer',
      code: customer.location_code || '',
      status: customer.status || ''
    };
  };

  const fetchCustomerById = async (customerId: string): Promise<CustomerOption | null> => {
    try {
      setLoading(true);
      const ssoUsername = getSsoUsernameFromLocalStorage() || '';
      const response = await portalApi.post(API_PATH, {
        id: customerId,
        ssoUsername
      });

      const data = response.data as { billing_list?: any[] };
      if (
        response.success &&
        data &&
        Array.isArray(data.billing_list)
      ) {
        const customer = data.billing_list.find((item: any) => item.location_id?.toString() === customerId);
        if (customer) {
          const customerOption = extractCustomerData(customer);
          setSelectedCustomer(customerOption);
          return customerOption;
        }
      }
    } catch (error) {
      console.error('Failed to fetch customer data:', error);
    } finally {
      setLoading(false);
    }
    return null;
  };

  const searchCustomers = async (query: string) => {
    try {
      setLoading(true);
      const ssoUsername = getSsoUsernameFromLocalStorage() || '';
      const response = await portalApi.post(API_PATH, {
        currentPage: 1,
        pageSize: 20,
        name: query.trim(),
        status: "ACTIVE",
        ssoUsername
      });

      const data = response.data as { billing_list?: any[] };
      if (response.success && data && Array.isArray(data.billing_list)) {
        const customerList = data.billing_list;
        const customers = customerList.map((customer: any) => extractCustomerData(customer));
        setOptions(customers);
      } else {
        setOptions([]);
      }
    } catch (error) {
      console.error('Failed to search customers:', error);
      setOptions([]);
    } finally {
      setLoading(false);
    }
  };

  const debouncedSearch = useRef(
    debounce((query: string) => {
      searchCustomers(query);
    }, 300)
  ).current;

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchQuery(value);
    debouncedSearch(value);
  };

  const handleCustomerSelect = (customerId: string) => {
    const customer = options.find(opt => opt.id === customerId);
    if (customer) {
      setSelectedCustomer(customer);
      onChange(customer.id, customer);
      setSearchQuery('');
      setOptions([]);
      setOpen(false);
    }
  };

  const handleOpenChange = (isOpen: boolean) => {
    setOpen(isOpen);
    if (!isOpen) {
      setSearchQuery('');
      setOptions([]);
    } else {
      // 打开下拉框时自动触发搜索，不依赖搜索关键字
      searchCustomers('');
    }
  };

  return (
    <div className="relative w-full">
      <Select
        value={value}
        onValueChange={handleCustomerSelect}
        open={open}
        onOpenChange={handleOpenChange}
        disabled={disabled}
      >
        <SelectTrigger
          className={cn(
            "w-full h-10 rounded-md bg-transparent",
            "border border-slate-700/50",
            "hover:border-slate-600 focus:border-slate-500",
            "flex items-center justify-between px-3 py-2 text-sm text-slate-200",
            "focus:outline-none focus:ring-0 focus:ring-offset-0",
            "data-[placeholder]:text-slate-400"
          )}
        >
          <SelectValue placeholder={placeholder}>
            {loading ? (
              <div className="flex items-center">
                <Loader2 className="mr-2 h-4 w-4 animate-spin text-slate-400" />
                <span>Loading...</span>
              </div>
            ) : selectedCustomer ? (
              <div className="flex items-center">
                <User className="mr-2 h-4 w-4 text-slate-400" />
                <span>{selectedCustomer.name}</span>
              </div>
            ) : null}
          </SelectValue>
        </SelectTrigger>
        <SelectContent
          className={cn(
            "border border-slate-700/70 bg-slate-800/90 text-slate-200",
            "rounded-md shadow-lg min-w-[300px] overflow-hidden",
            "animate-in fade-in-80 zoom-in-95"
          )}
          portalContainer={getCyberAgentPortalContainer()}
        >
          <div className="flex items-center px-3 py-2 border-b border-slate-700/50">
            <Search className="mr-2 h-4 w-4 shrink-0 text-slate-400" />
            <Input
              ref={searchInputRef}
              className="h-8 border-0 p-0 bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0 text-slate-200 placeholder:text-slate-400"
              placeholder="Search customers..."
              value={searchQuery}
              onChange={handleSearchChange}
            />
          </div>

          <div className="max-h-[320px] overflow-auto scrollbar-thin scrollbar-thumb-slate-600 scrollbar-track-transparent">
            {loading ? (
              <div className="flex items-center justify-center py-6">
                <Loader2 className="h-6 w-6 animate-spin text-slate-400" />
              </div>
            ) : options.length > 0 ? (
              <SelectGroup>
                <SelectLabel className="px-3 text-xs font-medium text-slate-400">Customer List</SelectLabel>
                {options.map((customer) => (
                  <SelectItem
                    key={customer.id}
                    value={customer.id}
                    className={cn(
                      "py-2 px-3 cursor-pointer text-slate-200",
                      "focus:bg-slate-700/70 focus:text-slate-200",
                      "data-[highlighted]:bg-slate-700/70 data-[highlighted]:text-slate-200"
                    )}
                  >
                    <div className="flex items-center">
                      <User className="mr-2 h-4 w-4 text-slate-400" />
                      <div>
                        <div>{customer.name}</div>
                        {customer.code && (
                          <div className="text-xs text-slate-400">{customer.code}</div>
                        )}
                      </div>
                    </div>
                  </SelectItem>
                ))}
              </SelectGroup>
            ) : searchQuery ? (
              <div className="py-6 text-center text-sm text-slate-400">
                No customers found
              </div>
            ) : (
              <div className="py-6 text-center text-sm text-slate-400">
                Type to search customers
              </div>
            )}
          </div>
        </SelectContent>
      </Select>
    </div>
  );
}