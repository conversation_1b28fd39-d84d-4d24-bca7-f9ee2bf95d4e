{"openapi": "3.0.1", "info": {"title": "FMS", "description": "", "version": "1.0.0"}, "paths": {"/fms-platform-dispatch-management/route-engine-dispatch/get-terminal-statistics": {"post": {"summary": "AI: According to Terminal statistics", "deprecated": false, "description": "", "tags": ["TripDispatch"], "parameters": [], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/TerminalStatisticsInputDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TerminalStatisticsOutputDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/route-engine-dispatch/get-tripstatus-list": {"get": {"summary": "AI: Get the TripStatus drop-down list", "deprecated": false, "description": "", "tags": ["TripDispatch"], "parameters": [], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Int32FmsSelectListItemListResultDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/route-engine-dispatch/get-doorderstatus-list": {"get": {"summary": "AI: Get DoOrderStatus drop-down list\r\nforeign", "deprecated": false, "description": "", "tags": ["TripDispatch"], "parameters": [], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Int32FmsSelectListItemListResultDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/route-engine-dispatch/get-woorderstatus-list": {"get": {"summary": "AI: <PERSON>rderStatus drop-down list\r\nforeign", "deprecated": false, "description": "", "tags": ["TripDispatch"], "parameters": [], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Int32FmsSelectListItemListResultDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/route-engine-dispatch/get-tasktype-list": {"get": {"summary": "AI: Get the TaskType drop-down list\r\nforeign", "deprecated": false, "description": "", "tags": ["TripDispatch"], "parameters": [], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Int32FmsSelectListItemListResultDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/TripDispatch/GetPackagePushInputByTaskNo": {"get": {"summary": "AI: Press TaskNo to get the structure required for pushing RouteEngine Push interface", "deprecated": false, "description": "", "tags": ["TripDispatch"], "parameters": [{"name": "taskNo", "in": "query", "description": "", "required": false, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/PackagePushInputV2Dto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/TripDispatch/GetRouteEngineHotZoneByTrips": {"post": {"summary": "Get the zone information of route engine in batches", "deprecated": false, "description": "", "tags": ["TripDispatch"], "parameters": [], "requestBody": {"content": {"application/json-patch+json": {"schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "boolean"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/trip-dispatch/get-pickup-delivery-task": {"post": {"summary": "/fms-platform-dispatch-management/trip-dispatch/get-pickup-delivery-task", "deprecated": false, "description": "", "tags": ["TripDispatch"], "parameters": [], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/GetPendingTaskInputDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RouteEngineDispatchLocalPickupDeliverTaskResponsePagedResultDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/Trips/GetORGTerminalList": {"get": {"summary": "Get ORGTerminal information", "deprecated": false, "description": "", "tags": ["Trips"], "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TerminalOutputDtoSuccessResponse"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/Trips/GetdocumentService": {"get": {"summary": "/fms-platform-dispatch-management/Trips/GetdocumentService", "deprecated": false, "description": "", "tags": ["Trips"], "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StringStringListDictionarySuccessResponse"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/Trips/GetDSTTerminalList": {"get": {"summary": "Get DSTTerminal information", "deprecated": false, "description": "", "tags": ["Trips"], "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TerminalOutputDtoSuccessResponse"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/Trips/GetCarrierList": {"get": {"summary": "/fms-platform-dispatch-management/Trips/GetCarrierList", "deprecated": false, "description": "", "tags": ["Trips"], "parameters": [{"name": "companyId", "in": "query", "description": "", "required": false, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CarrierDto"}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/Trips/GetDriverList": {"get": {"summary": "/fms-platform-dispatch-management/Trips/GetDriverList", "deprecated": false, "description": "", "tags": ["Trips"], "parameters": [{"name": "carrierCode", "in": "query", "description": "", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DriverDto"}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/management-trips/types": {"get": {"summary": "Trip Management list page, Type drop-down box data", "deprecated": false, "description": "", "tags": ["Trips"], "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Int32FmsSelectListItemListResultDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/trip/get-trip-info": {"get": {"summary": "Unnamed interface", "deprecated": false, "description": "", "tags": [], "parameters": [{"name": "orderNo", "in": "query", "description": "", "required": false, "example": "24370000002025", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/home/<USER>": {"get": {"summary": "/fms-platform-dispatch-management/home/<USER>", "deprecated": false, "description": "", "tags": ["Home"], "parameters": [{"name": "second", "in": "query", "description": "", "required": false, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "boolean"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/route-engine-dispatch/linehaul/tab-list": {"post": {"summary": "Get the total list of Linehaul Dispatch New", "deprecated": false, "description": "", "tags": ["LinehaulDispatch"], "parameters": [], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/LinehaulDispatchTotalInputDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/LinehaulDispatchTotalListOutputDtoPagedResultDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/shipment-orders/get-by-trips": {"post": {"summary": "Order matrix query initialization", "deprecated": false, "description": "", "tags": ["Orders"], "parameters": [], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/OrderGetLookupRequest"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"type": "string"}}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/search-all": {"get": {"summary": "Global Search", "deprecated": false, "description": "", "tags": ["Orders"], "parameters": [{"name": "Keyword", "in": "query", "description": "", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/SearchAllDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}, "/fms-platform-dispatch-management/dispatching-dashboard/get-trip-location": {"post": {"summary": "/fms-platform-dispatch-management/dispatching-dashboard/get-trip-location", "deprecated": false, "description": "", "tags": ["TripDispatchEnhance"], "parameters": [], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/GetTripLocationInputDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/GetTripLocationOutputDto"}}}, "headers": {}}}, "security": [{"apikey-header-Fms-Token": []}]}}}, "components": {"schemas": {"AccessorialServiceDto": {"type": "object", "properties": {"code": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CarrierTypeEnum": {"enum": [2, 6, 7, 8], "type": "integer", "format": "int32"}, "ApptObject": {"type": "object", "properties": {"pickupDate": {"type": "string", "nullable": true}, "pickupFromTime": {"type": "string", "nullable": true}, "pickupToTime": {"type": "string", "nullable": true}, "deliveryDate": {"type": "string", "nullable": true}, "deliveryFromTime": {"type": "string", "nullable": true}, "deliveryToTime": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Dispatch": {"type": "object", "properties": {"dispatchDate": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CarrierDto": {"type": "object", "properties": {"carrier_name": {"type": "string", "nullable": true}, "carrier_code": {"type": "string", "nullable": true}, "carrier_type": {"$ref": "#/components/schemas/CarrierTypeEnum"}, "company_name": {"type": "string", "nullable": true}, "id": {"type": "integer", "format": "int64"}}, "additionalProperties": false}, "DriverDto": {"type": "object", "properties": {"driver_code": {"type": "string", "nullable": true}, "driver_name": {"type": "string", "nullable": true}, "carrier_name": {"type": "string", "nullable": true}, "carrier_code": {"type": "string", "nullable": true}, "user_type": {"$ref": "#/components/schemas/UserTypeEnum"}}, "additionalProperties": false}, "GetPendingTaskInputDto": {"type": "object", "properties": {"order_nos": {"type": "array", "items": {"type": "string"}, "nullable": true}, "tracking_nos": {"type": "array", "items": {"type": "string"}, "nullable": true}, "terminal_code": {"type": "string", "nullable": true}, "pu_nos": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "LinehaulDispatchTotalInputDto": {"type": "object", "properties": {"current_terminal_code": {"type": "string", "nullable": true}, "est_departure_date_from": {"type": "string", "nullable": true}, "est_departure_date_to": {"type": "string", "nullable": true}, "est_arrival_date_from": {"type": "string", "nullable": true}, "est_arrival_date_to": {"type": "string", "nullable": true}, "trip_date_from": {"type": "string", "nullable": true}, "trip_date_to": {"type": "string", "nullable": true}, "lh_create_date_from": {"type": "string", "nullable": true}, "lh_create_date_to": {"type": "string", "nullable": true}, "org_terminal": {"type": "array", "items": {"type": "string"}, "nullable": true}, "dst_terminal": {"type": "array", "items": {"type": "string"}, "nullable": true}, "linehaul_status": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}, "load_status": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}, "actual_departure_date_from": {"type": "string", "nullable": true}, "actual_departure_date_to": {"type": "string", "nullable": true}, "actual_arrival_date_from": {"type": "string", "nullable": true}, "actual_arrival_date_to": {"type": "string", "nullable": true}, "lh_nos": {"type": "array", "items": {"type": "integer", "format": "int64"}, "nullable": true}, "tracking_nos": {"type": "array", "items": {"type": "string"}, "nullable": true}, "do_nos": {"type": "array", "items": {"type": "string"}, "nullable": true}, "trip_nos": {"type": "array", "items": {"type": "integer", "format": "int64"}, "nullable": true}, "route_ids": {"type": "array", "items": {"type": "string"}, "nullable": true}, "carrier_codes": {"type": "array", "items": {"type": "string"}, "nullable": true}, "driver_codes": {"type": "array", "items": {"type": "string"}, "nullable": true}, "is_inbound": {"type": "integer", "format": "int32", "nullable": true}, "is_outbound": {"type": "integer", "format": "int32", "nullable": true}, "tab": {"type": "string", "nullable": true}, "page_number": {"maximum": 2147483647, "minimum": 0, "type": "integer", "format": "int32"}, "page_size": {"maximum": 2147483647, "minimum": 1, "type": "integer", "format": "int32"}}, "additionalProperties": false}, "LinehaulDispatchTotalListOutputDto": {"type": "object", "properties": {"lh_no": {"type": "string", "nullable": true}, "lh_status": {"type": "integer", "format": "int32"}, "load_status": {"type": "integer", "format": "int32"}, "trip_no": {"type": "string", "nullable": true}, "route_name": {"type": "string", "nullable": true}, "org_terminal": {"type": "string", "nullable": true}, "dest_terminal": {"type": "string", "nullable": true}, "est_departure_date_from": {"type": "string", "nullable": true}, "est_departure_date_to": {"type": "string", "nullable": true}, "est_arrival_date_from": {"type": "string", "nullable": true}, "est_arrival_date_to": {"type": "string", "nullable": true}, "driver_code": {"type": "string", "nullable": true}, "driver_name": {"type": "string", "nullable": true}, "carrier_code": {"type": "string", "nullable": true}, "carrier_name": {"type": "string", "nullable": true}, "trip_date": {"type": "string", "nullable": true}, "pallet": {"type": "string", "nullable": true}, "weight": {"type": "string", "nullable": true}, "last_update_by": {"type": "string", "nullable": true}, "lh_create_date": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Manifest": {"type": "object", "properties": {"code": {"type": "string", "nullable": true}, "pallet": {"type": "integer", "format": "int64", "nullable": true}, "weight": {"type": "integer", "format": "int64", "nullable": true}}, "additionalProperties": false}, "Int32FmsSelectListItem": {"type": "object", "properties": {"text": {"type": "string", "nullable": true}, "value": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "Int32FmsSelectListItemListResultDto": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/Int32FmsSelectListItem"}, "nullable": true}}, "additionalProperties": false}, "LhSearchAllDto": {"type": "object", "properties": {"task_no": {"type": "integer", "format": "int64"}, "pro_no": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StringStringListDictionarySuccessResponse": {"type": "object", "properties": {"data": {"type": "object", "additionalProperties": {"type": "array", "items": {"type": "string"}}, "properties": {}, "nullable": true}, "is_success": {"type": "boolean", "readOnly": true}, "status": {"type": "string", "readOnly": true, "nullable": true}, "message": {"type": "string", "readOnly": true, "nullable": true}, "code": {"type": "integer", "format": "int32", "readOnly": true}}, "additionalProperties": false}, "LinehaulDispatchTotalListOutputDtoPagedResultDto": {"type": "object", "properties": {"total_count": {"type": "integer", "format": "int64"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/LinehaulDispatchTotalListOutputDto"}, "nullable": true}}, "additionalProperties": false}, "GetTripLocationInputDto": {"type": "object", "properties": {"trip_location_coditions": {"type": "array", "items": {"$ref": "#/components/schemas/TripLocationCodition"}, "nullable": true}}, "additionalProperties": false}, "GetTripLocationOutputDto": {"type": "object", "properties": {"trip_locations": {"type": "array", "items": {"$ref": "#/components/schemas/TripLocationInfo"}, "nullable": true}}, "additionalProperties": false}, "TerminalStatisticsInputDto": {"type": "object", "properties": {"terminal_code": {"type": "string", "nullable": true}}, "additionalProperties": false}, "TerminalStatisticsOutputDto": {"type": "object", "properties": {"trip_total": {"type": "integer", "format": "int32"}, "order_total": {"type": "integer", "format": "int32"}, "pallet_total": {"type": "string", "nullable": true}, "package_total": {"type": "string", "nullable": true}, "pallet_or_package_total": {"type": "string", "nullable": true}, "weight_total": {"type": "string", "nullable": true}, "mile_total": {"type": "string", "nullable": true}}, "additionalProperties": false}, "OrderGetLookupRequest": {"type": "object", "properties": {"trip_nos": {"type": "array", "items": {"type": "integer", "format": "int64"}, "nullable": true}}, "additionalProperties": false}, "LinehaulPreDispatchObject": {"type": "object", "properties": {"etd": {"type": "string", "nullable": true}, "route_id": {"type": "string", "nullable": true}}, "additionalProperties": false}, "OrderSearchAllDto": {"type": "object", "properties": {"order_no": {"type": "string", "nullable": true}, "pro_no": {"type": "string", "nullable": true}}, "additionalProperties": false}, "LocationV2Object": {"type": "object", "properties": {"address1": {"type": "string", "nullable": true}, "address2": {"type": "string", "nullable": true}, "address3": {"type": "string", "nullable": true}, "address4": {"type": "string", "nullable": true}, "city": {"type": "string", "nullable": true}, "code": {"type": "string", "nullable": true}, "country": {"type": "string", "nullable": true}, "group": {"type": "string", "nullable": true}, "lat": {"type": "number", "format": "double"}, "lng": {"type": "number", "format": "double"}, "name": {"type": "string", "nullable": true}, "state": {"type": "string", "nullable": true}, "street": {"type": "string", "nullable": true}, "zip": {"type": "string", "nullable": true}, "terminalCode": {"type": "string", "nullable": true}}, "additionalProperties": false}, "RouteEngineDispatchLocalAddressInfoResponse": {"type": "object", "properties": {"from_info": {"$ref": "#/components/schemas/RouteEngineDispatchLocalAddressItemInfoResponse"}, "to_info": {"$ref": "#/components/schemas/RouteEngineDispatchLocalAddressItemInfoResponse"}}, "additionalProperties": false}, "RouteEngineDispatchLocalAddressItemInfoResponse": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "address": {"type": "string", "nullable": true}, "state": {"type": "string", "nullable": true}, "city": {"type": "string", "nullable": true}, "zip_code": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UserTypeEnum": {"enum": [0, 1, 2, 3, 4, 5, 6, 7, 8], "type": "integer", "format": "int32"}, "RouteEngineDispatchLocalPickupDeliverTaskResponse": {"type": "object", "properties": {"quantity": {"type": "integer", "format": "int32"}, "package_count": {"type": "integer", "format": "int32", "nullable": true}, "weight": {"type": "number", "format": "double"}, "volume": {"type": "number", "format": "double"}, "order_no": {"type": "string", "nullable": true}, "tracking_no": {"type": "string", "nullable": true}, "task_no": {"type": "string", "nullable": true}, "task_type": {"type": "string", "nullable": true}, "task_status": {"type": "string", "nullable": true}, "address_info": {"$ref": "#/components/schemas/RouteEngineDispatchLocalAddressInfoResponse"}, "shipment_type": {"$ref": "#/components/schemas/ShipmentTypeEnum"}, "service_level": {"$ref": "#/components/schemas/ServiceLevelEnum"}, "terminal": {"type": "string", "nullable": true}, "polygon": {"type": "string", "nullable": true}, "customer": {"type": "string", "nullable": true}, "customer_id": {"type": "integer", "format": "int32", "nullable": true}, "desire_date": {"type": "string", "format": "date-time"}, "pickup_appointment_from": {"type": "string", "nullable": true}, "pickup_appointment_to": {"type": "string", "nullable": true}, "delivery_appointment_from": {"type": "string", "nullable": true}, "delivery_appointment_to": {"type": "string", "nullable": true}, "carrier_name": {"type": "string", "nullable": true}, "driver_name": {"type": "string", "nullable": true}, "carrier_code": {"type": "string", "nullable": true}, "driver_code": {"type": "string", "nullable": true}, "state": {"type": "string", "nullable": true}, "city": {"type": "string", "nullable": true}, "zip_code": {"type": "string", "nullable": true}, "pu_no": {"type": "string", "nullable": true}, "order_status": {"type": "integer", "format": "int32"}, "order_sub_status": {"type": "integer", "format": "int32"}, "pu_zone_code": {"type": "string", "nullable": true}}, "additionalProperties": false}, "RouteEngineDispatchLocalPickupDeliverTaskResponsePagedResultDto": {"type": "object", "properties": {"total_count": {"type": "integer", "format": "int64"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/RouteEngineDispatchLocalPickupDeliverTaskResponse"}, "nullable": true}}, "additionalProperties": false}, "ServiceLevelEnum": {"enum": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 30], "type": "integer", "format": "int32"}, "ShipmentTypeEnum": {"enum": [1, 2, 3, 4], "type": "integer", "format": "int32"}, "SearchAllDto": {"type": "object", "properties": {"lhs": {"uniqueItems": true, "type": "array", "items": {"$ref": "#/components/schemas/LhSearchAllDto"}, "nullable": true}, "orders": {"uniqueItems": true, "type": "array", "items": {"$ref": "#/components/schemas/OrderSearchAllDto"}, "nullable": true}, "trips": {"uniqueItems": true, "type": "array", "items": {"$ref": "#/components/schemas/TripSearchAllDto"}, "nullable": true}, "summary": {"uniqueItems": true, "type": "array", "items": {"$ref": "#/components/schemas/SummaryDto"}, "readOnly": true, "nullable": true}}, "additionalProperties": false}, "PackagePushInputV2Dto": {"type": "object", "properties": {"packageList": {"type": "array", "items": {"$ref": "#/components/schemas/PackagePushV2Object"}, "nullable": true}}, "additionalProperties": false}, "PackagePushV2Object": {"type": "object", "properties": {"packageNo": {"type": "string", "nullable": true}, "orderNo": {"type": "string", "nullable": true}, "taskNo": {"type": "string", "nullable": true}, "orderCreateTime": {"type": "string", "nullable": true}, "taskType": {"type": "integer", "format": "int32"}, "revenueType": {"type": "string", "nullable": true}, "shipmentType": {"type": "string", "nullable": true}, "serviceLevel": {"type": "string", "nullable": true}, "linear": {"type": "integer", "format": "int32"}, "polygonCode": {"type": "string", "nullable": true}, "toPolygonCode": {"type": "string", "nullable": true}, "appointment": {"$ref": "#/components/schemas/ApptObject"}, "shipper": {"$ref": "#/components/schemas/LocationV2Object"}, "consignee": {"$ref": "#/components/schemas/LocationV2Object"}, "orgTerminalCode": {"type": "string", "nullable": true}, "destTerminalCode": {"type": "string", "nullable": true}, "manifest": {"type": "array", "items": {"$ref": "#/components/schemas/Manifest"}, "nullable": true}, "totalWeight": {"type": "integer", "format": "int32"}, "totalPallet": {"type": "integer", "format": "int32"}, "billto": {"type": "string", "nullable": true}, "dispatch": {"$ref": "#/components/schemas/Dispatch"}, "accessorialServiceList": {"type": "array", "items": {"$ref": "#/components/schemas/AccessorialServiceDto"}, "nullable": true}, "hotZoneCode": {"type": "string", "nullable": true}, "ratedWeight": {"type": "number", "format": "double"}, "linehaulPreDispatch": {"$ref": "#/components/schemas/LinehaulPreDispatchObject"}}, "additionalProperties": false}, "SummaryDto": {"type": "object", "properties": {"number": {"type": "string", "nullable": true}, "pro_no": {"type": "string", "nullable": true}, "category": {"type": "string", "nullable": true}}, "additionalProperties": false}, "TerminalListInfo": {"type": "object", "properties": {"terminal_id": {"type": "integer", "format": "int32"}, "terminal_name": {"type": "string", "nullable": true}}, "additionalProperties": false}, "TerminalOutputDto": {"type": "object", "properties": {"terminal_lists": {"type": "array", "items": {"$ref": "#/components/schemas/TerminalListInfo"}, "nullable": true}}, "additionalProperties": false}, "TerminalOutputDtoSuccessResponse": {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/TerminalOutputDto"}, "status": {"type": "string", "readOnly": true, "nullable": true}, "message": {"type": "string", "readOnly": true, "nullable": true}, "code": {"type": "integer", "format": "int32", "readOnly": true}}, "additionalProperties": false}, "TripSearchAllDto": {"type": "object", "properties": {"trip_no": {"type": "integer", "format": "int64"}}, "additionalProperties": false}, "TripLocationCodition": {"type": "object", "properties": {"trip_no": {"type": "integer", "format": "int64"}, "tractor_code": {"type": "string", "nullable": true}, "trailer_code": {"type": "string", "nullable": true}}, "additionalProperties": false}, "TripLocationInfo": {"type": "object", "properties": {"trip_no": {"type": "integer", "format": "int64"}, "latitude": {"type": "string", "nullable": true}, "longitude": {"type": "string", "nullable": true}, "location_time": {"type": "string", "nullable": true}}, "additionalProperties": false}}}}