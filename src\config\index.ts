import { siteToolDevConfig } from './siteToolDevConfig';
import { siteToolStageConfig } from './siteToolStageConfig';
import { siteToolProdConfig } from './siteToolProdConfig';
import { SiteToolConfigType } from './types';

// 配置映射
const configMap: Record<string, SiteToolConfigType> = {
  dev: siteToolDevConfig,
  development: siteToolDevConfig,
  stage: siteToolStageConfig,
  staging: siteToolStageConfig,
  prod: siteToolProdConfig,
  production: siteToolProdConfig,
};

/**
 * 获取站点工具配置
 * 根据环境变量 siteToolConfigEnv 从对应的配置模块读取配置
 * 支持 dev、stage、prod 三种环境
 */
export function getSiteToolConfig(): SiteToolConfigType {
  try {
    // 获取环境配置
    const env = process.env.siteToolConfigEnv || 'stage';
    console.log(`[SiteToolConfig] 当前环境: ${env}`);
    
    // 从配置映射中获取对应环境的配置
    const config = configMap[env.toLowerCase()];
    
    if (!config) {
      console.error(`[SiteToolConfig] 未找到环境 ${env} 的配置，使用默认开发环境配置`);
      return siteToolDevConfig;
    }
    
    console.log(`[SiteToolConfig] 成功加载环境配置: ${env}`);
    return config;
  } catch (error) {
    console.error('[SiteToolConfig] 读取站点工具配置失败:', error);
    return siteToolDevConfig; // 返回开发环境配置作为默认值
  }
}

// 导出各个环境的配置，以便需要时直接使用
export { siteToolDevConfig, siteToolStageConfig, siteToolProdConfig };
export type { SiteToolConfigType }; 