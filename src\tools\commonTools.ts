import { tool } from 'ai';
import { z } from 'zod';

/**
 * 获取用户输入工具
 */
export const requireUserInputTool = tool({
  description: 'Use this tool to get user input when you need answers or decisions from users. Use text type for simple text input; use template type for structured input.',
  parameters: z.object({
    input_type: z.enum(['text', 'template']).describe('Input type: text for simple text input, template for structured input'),
    title: z.string().describe('Title of the input request'),
    description: z.string().optional().describe('Detailed description of the input request'),
    template_fields: z.array(
      z.object({
        name: z.string(),
        label: z.string(),
        type: z.string(),
        required: z.boolean().optional(),
        options: z.array(
          z.object({
            label: z.string(),
            value: z.string()
          })
        ).optional(),
        validation: z.object({
          pattern: z.string().optional(),
          min: z.number().optional(),
          max: z.number().optional(),
          message: z.string().optional()
        }).optional()
      })
    ).optional().describe('Field definitions when input_type is template')
  }),
  execute: async ({ input_type, title, description, template_fields }) => {
    console.log('requireUserInputTool.execute called with args:', 
      JSON.stringify({ input_type, title, description, template_fields }));
    
    try {
      // In a real application, this should integrate with WebSocket or other communication mechanisms
      // Simplified here to return a mock response
      console.log("Simulating user input retrieval");
      
      // Actual communication logic can be added here
      // For example, sending a request via WebSocket and waiting for a response
      
      return {
        success: true,
        response: "Simulated user response"
      };
    } catch (error) {
      console.error("Failed to get user input", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }
});

/**
 * 结束任务工具
 */
export const finishTaskTool = tool({
  description: 'Use this tool when you have completed all necessary task operations, do not do unnecessary things.',
  parameters: z.object({
    message: z.string().describe('Task summary, providing necessary information for users to understand the result')
  }),
  execute: async ({ message }) => {
    try {
      return {
        content: message || 'Task completed successfully',
        status: 'success'
      };
    } catch (error) {
      console.error("Task completion failed", error);
      return {
        content: 'Error: ' + (error instanceof Error ? error.message : String(error)),
        status: 'error'
      };
    }
  }
}); 