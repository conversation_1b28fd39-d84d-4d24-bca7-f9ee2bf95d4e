/**
 * 数据库配置文件
 * 从mcp.json文件中的mysql配置复制而来
 */
module.exports = {
  host: process.env.DB_HOST || 'ec2-54-189-142-24.us-west-2.compute.amazonaws.com',
  port: parseInt(process.env.DB_PORT || '3306', 10),
  user: process.env.DB_USER || 'ro_alluser',
  password: process.env.DB_PASSWORD || 'xj5TnAD2bCF+A1Va',
  database: process.env.DB_NAME || 'wms',
  // 连接设置
  connectTimeout: 10000,
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0
}; 