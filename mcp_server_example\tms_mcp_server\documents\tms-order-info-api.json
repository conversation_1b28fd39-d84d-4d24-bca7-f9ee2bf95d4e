{"openapi": "3.0.0", "info": {"title": "Freight App TMS Single Order Detail API", "version": "1.0.0", "description": "API for retrieving detailed information about a single TMS order based on order ID, PU number, or Pro number. This API is designed specifically for individual order lookup and detailed information retrieval."}, "servers": [{"url": "https://ship.unisco.com", "description": "production server"}], "paths": {"/apinew/cpv2/tracking/getOrders?search_text={search_text}": {"get": {"summary": "Get single TMS order details", "description": "Retrieves detailed information for a single TMS order by searching with order ID, PU number, or Pro number. This endpoint is primarily used for viewing complete order details, not for bulk listing.", "operationId": "getSingleOrderDetails", "parameters": [{"name": "search_text", "in": "path", "description": "The identifier to search for - can be an order number, PU number, or Pro number. The system will automatically determine the type of identifier and return matching orders.", "required": true, "example": "1001120298", "schema": {"type": "string"}}, {"name": "X-Tenant-id", "in": "header", "description": "Tenant identifier for the request", "example": "CO1", "schema": {"type": "string", "default": "CO1"}}], "responses": {"200": {"description": "Successful response with order details", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "description": "Indicates if the request was successful"}, "data": {"type": "array", "description": "Array of order objects matching the search criteria", "items": {"type": "object", "properties": {"order_id": {"type": "string", "description": "Unique identifier for the order"}, "pu_number": {"type": "string", "description": "Pickup number associated with the order"}, "pro_number": {"type": "string", "description": "Progressive number associated with the order"}, "status": {"type": "string", "description": "Current status of the order"}, "details": {"type": "object", "description": "Additional order details"}}}}}}}}}, "400": {"description": "Bad request - invalid parameters", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string"}}}}}}, "404": {"description": "No orders found matching the search criteria", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "No orders found"}}}}}}}, "tags": ["Order Details"]}}}, "components": {"securitySchemes": {"UserAuth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "in": "header", "name": "UserToken"}}}}