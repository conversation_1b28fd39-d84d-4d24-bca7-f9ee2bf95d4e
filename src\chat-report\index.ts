// Export all utilities for external use
export * from './types';
export * from './reportStorage';
export * from './reportAnalyzer';
export * from './scheduler';

// Initialize the scheduler if in Node.js environment (not browser)
import { startReportScheduler } from './scheduler';

// Only run in production server environment, not during development or builds
if (
  process.env.NODE_ENV === 'production' && 
  typeof window === 'undefined' && 
  !process.env.VERCEL_ENV // Skip on Vercel's serverless functions
) {
  // Delay startup to allow other server processes to initialize first
  setTimeout(() => {
    startReportScheduler();
  }, 5000);
} 