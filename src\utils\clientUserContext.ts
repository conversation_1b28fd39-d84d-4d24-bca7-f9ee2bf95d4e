import { getSite } from './SiteContext';
import { DefaultClientUserContextManager } from './clientUserContext/DefaultClientUserContextManager';
import { WmsClientUserContextManager } from './clientUserContext/WmsClientUserContextManager';
import type { IClientUserContextManager } from './clientUserContext/IClientUserContextManager';

// 类型定义
export interface Facility {
  id: string;
  name: string;
  code: string;
}

export interface WmsUserInfo {
  id: string;
  username: string;
  fullName?: string;
  email?: string;
  tenantId: string;
  facilities: Facility[];
  currentFacility?: Facility;
  roles?: string[];
}

export interface SessionContext {
  userId: string;
  username: string;
  email?: string;
  currentTenant?: string;
  currentFacility?: string;
  lastActivity: number;
  wmsUserInfo?: WmsUserInfo; // WMS用户信息
  iamUserInfo?: any; // IAM用户信息
}

export interface UserPreferences {
  defaultTenant?: string;
  defaultFacility?: string;
  preferredLanguage?: string;
  theme?: string;
}

export interface UserContext {
  session: SessionContext;
  preferences: UserPreferences;
}

let realInstance: IClientUserContextManager | null = null;

function createInstance() {
  const site = getSite();
  console.log('clientUserContextManager createInstance site', site,site === 'wms');
  if (site === 'wms') {
    return WmsClientUserContextManager.getInstance();
  } else {
    return DefaultClientUserContextManager.getInstance();
  }
}

export const clientUserContextManager = new Proxy({} as IClientUserContextManager, {
  get(_target, prop) {
    if (!realInstance) {
      realInstance = createInstance();
    }
    // @ts-ignore
    return realInstance[prop];
  }
});

export function initClientUserContextManager() {
  realInstance = createInstance();
} 