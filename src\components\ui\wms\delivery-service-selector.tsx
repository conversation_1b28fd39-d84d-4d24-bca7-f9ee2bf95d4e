'use client';

import * as React from 'react';
import { useState, useEffect, useRef } from 'react';
import { Package, Search, Loader2 } from 'lucide-react';
// @ts-ignore 忽略类型检查
import { debounce } from 'lodash';
import { wmsApi } from '@/utils/wmsApiClient';

import { Input } from '@/components/ui/input';
import { cn } from '@/lib/utils';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface DeliveryServiceOption {
  id: string;
  name: string;
  code?: string;
}

interface DeliveryServiceSelectorProps {
  value?: string;
  onChange: (value: string, deliveryServiceData?: DeliveryServiceOption) => void;
  placeholder?: string;
  disabled?: boolean;
  required?: boolean;
  carrierId?: string; // 依赖的承运商ID
  apiHeaders?: Record<string, string>;
  defaultValue?: string;
}

// 定义WMS API响应类型
interface WmsApiResponse {
  success: boolean;
  msg?: string;
  data?: any[] | {
    list?: any[];
    [key: string]: any;
  };
  [key: string]: any;
}

export function DeliveryServiceSelector({
  value,
  onChange,
  placeholder = 'Select delivery service',
  disabled = false,
  required = false,
  carrierId,
  apiHeaders = {},
  defaultValue
}: DeliveryServiceSelectorProps) {
  // Fixed API path for carriers (to get carrier details with delivery services)
  const API_PATH = 'mdm/carrier/search-by-paging';
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [options, setOptions] = useState<DeliveryServiceOption[]>([]);
  const [selectedDeliveryService, setSelectedDeliveryService] = useState<DeliveryServiceOption | null>(null);
  const initialLoadRef = useRef(false);
  const loadedCarrierIdRef = useRef<string | null>(null);

  // 当carrierId变化时，加载该承运商的送货服务
  useEffect(() => {
    if (carrierId && carrierId !== loadedCarrierIdRef.current) {
      loadedCarrierIdRef.current = carrierId;
      loadDeliveryServicesForCarrier(carrierId);
    } else if (!carrierId) {
      setOptions([]);
      if (value) {
        setSelectedDeliveryService(null);
        onChange('');
      }
    }
  }, [carrierId]);

  // 初始化时如果有defaultValue但没有value，且有carrierId，则设置默认值
  useEffect(() => {
    if (!initialLoadRef.current && defaultValue && !value && carrierId) {
      console.log('DeliveryServiceSelector: Attempting to set default value:', defaultValue);
      initialLoadRef.current = true;

      // 先加载承运商的送货服务
      loadDeliveryServicesForCarrier(carrierId);
    }
  }, [defaultValue, value, carrierId]);

  // 当value变化时，如果已有选中的送货服务ID与新value不同，清除已选送货服务数据
  useEffect(() => {
    if (value !== selectedDeliveryService?.id) {
      setSelectedDeliveryService(null);
    }
  }, [value]);

  // 辅助函数：从送货服务对象解析成选项格式
  const parseDeliveryService = (service: any): DeliveryServiceOption => {
    return {
      id: service,
      name: service,
      code: service
    };
  };

  // 加载承运商的送货服务
  const loadDeliveryServicesForCarrier = async (id: string): Promise<void> => {
    if (!id) return;

    try {
      setLoading(true);
      console.log('Loading delivery services for carrier:', id);

      const response = await wmsApi.post<WmsApiResponse>(API_PATH, {
        id: id
      });

      console.log('Carrier details response:', response);

      if (response.success) {
        // 解析API响应
        const carrierList = parseApiResponse(response);
        console.log('Parsed carrier data:', carrierList);

        // 找到匹配的承运商
        const carrier = carrierList.find((c: any) => c.id === id);

        if (carrier && carrier.deliveryServices && Array.isArray(carrier.deliveryServices)) {
          console.log('Found delivery services:', carrier.deliveryServices);

          // 将送货服务转换为下拉选项
          const serviceOptions = carrier.deliveryServices.map((service: any) =>
            parseDeliveryService(service)
          );

          // 如果有defaultValue但不在选项中，添加它
          if (defaultValue && !serviceOptions.some((option: DeliveryServiceOption) => option.id === defaultValue || option.code === defaultValue)) {
            console.log('Adding default value to options:', defaultValue);
            serviceOptions.push({
              id: defaultValue,
              name: defaultValue,
              code: defaultValue
            });
          }

          setOptions(serviceOptions);

          // 如果有defaultValue但没有value，设置默认值
          if (defaultValue && !value) {
            const defaultService = serviceOptions.find((svc: DeliveryServiceOption) => svc.id === defaultValue || svc.code === defaultValue);
            if (defaultService) {
              console.log('Setting default service from options:', defaultService);
              setSelectedDeliveryService(defaultService);
              onChange(defaultService.id, defaultService);
            }
          }
          // 如果当前选择的值在新的选项中不存在，清除它
          else if (value && !serviceOptions.some((option: DeliveryServiceOption) => option.id === value)) {
            onChange('');
          }
          // 如果有value但没有selectedDeliveryService，尝试从新选项中找到它
          else if (value && !selectedDeliveryService) {
            const matchingOption = serviceOptions.find((option: DeliveryServiceOption) => option.id === value);
            if (matchingOption) {
              setSelectedDeliveryService(matchingOption);
            }
          }
        } else {
          console.warn('No delivery services found for carrier:', id);
          setOptions([]);
          // 清除当前选择的值，因为没有可用的选项
          if (value) {
            onChange('');
          }
        }
      } else {
        console.error('Error fetching carrier data:', response.msg);
        setOptions([]);
      }
    } catch (error) {
      console.error('Error loading delivery services:', error);
      setOptions([]);
    } finally {
      setLoading(false);
    }
  };

  // 解析API响应的函数
  const parseApiResponse = (response: any): any[] => {
    // 检查response.data是数组的情况
    if (Array.isArray(response.data)) {
      return response.data;
    }

    // 检查response.data.list的情况
    if (response.data && Array.isArray(response.data.list)) {
      return response.data.list;
    }

    // 检查response.data是对象但包含data字段的情况
    if (response.data && response.data.data && Array.isArray(response.data.data)) {
      return response.data.data;
    }

    // 直接检查response是否包含data字段为数组的情况
    if (response.data && Array.isArray(response.data)) {
      return response.data;
    }

    console.warn('Could not extract data from API response:', response);
    return [];
  };

  // 处理送货服务选择
  const handleDeliveryServiceSelect = (serviceId: string) => {
    const service = options.find(opt => opt.id === serviceId);
    if (service) {
      setSelectedDeliveryService(service);
      onChange(service.id, service);
      setOpen(false);
    }
  };

  // 打开/关闭下拉框
  const handleOpenChange = (isOpen: boolean) => {
    setOpen(isOpen);
  };

  return (
    <div className="relative w-full">
      <Select
        value={value}
        onValueChange={handleDeliveryServiceSelect}
        open={open}
        onOpenChange={handleOpenChange}
        disabled={disabled || !carrierId || options.length === 0}
      >
        <SelectTrigger
          className={cn(
            "w-full h-10 rounded-md bg-transparent",
            "border border-item-gray-700/50",
            "hover:border-item-purple focus:border-item-purple",
            "flex items-center justify-between px-3 py-2 text-sm text-white",
            "focus:outline-none focus:ring-1 focus:ring-item-purple",
            "transition-all duration-200",
            "data-[placeholder]:text-item-gray-400"
          )}
        >
          <SelectValue placeholder={!carrierId ? "Select carrier first" : placeholder}>
            {loading ? (
              <div className="flex items-center">
                <Loader2 className="mr-2 h-4 w-4 animate-spin text-item-gray-400" />
                <span>Loading...</span>
              </div>
            ) : selectedDeliveryService ? (
              <div className="flex items-center">
                <Package className="mr-2 h-4 w-4 text-item-gray-400" />
                <span>{selectedDeliveryService.name}</span>
              </div>
            ) : null}
          </SelectValue>
        </SelectTrigger>
        <SelectContent
          className={cn(
            "border border-item-gray-700/70 bg-item-bg-card text-white",
            "rounded-md shadow-lg min-w-[300px] overflow-hidden",
            "animate-in fade-in-80 zoom-in-95"
          )}
        >
          {loading ? (
            <div className="flex items-center justify-center py-6">
              <Loader2 className="h-6 w-6 animate-spin text-item-gray-400" />
            </div>
          ) : options.length > 0 ? (
            <SelectGroup>
              <SelectLabel className="px-3 text-xs font-medium text-item-gray-400">Delivery Services</SelectLabel>
              {options.map((service) => (
                <SelectItem
                  key={service.id}
                  value={service.id}
                  className={cn(
                    "py-2 px-3 cursor-pointer text-white",
                    "focus:bg-item-purple focus:text-white",
                    "data-[highlighted]:bg-item-purple data-[highlighted]:text-white"
                  )}
                >
                  <div className="flex items-center">
                    <Package className="mr-2 h-4 w-4 text-item-gray-400" />
                    <div>{service.name}</div>
                  </div>
                </SelectItem>
              ))}
            </SelectGroup>
          ) : !carrierId ? (
            <div className="py-6 text-center text-sm text-item-gray-400">
              Please select a carrier first
            </div>
          ) : (
            <div className="py-6 text-center text-sm text-item-gray-400">
              No delivery services available for this carrier
            </div>
          )}
        </SelectContent>
      </Select>
    </div>
  );
}