// 简单测试 Mem0 API 的直接调用
const { Memory } = require('mem0ai/oss');
require('dotenv').config();

// 测试参数
const USER_ID = '1859121854943944706';
const CONTENT = "可用仓库设施列表：Valley View (LT_F1), <PERSON><PERSON><PERSON> (LT_F2), <PERSON><PERSON><PERSON> (LT_F3), <PERSON> (LT_F5), Morgan Lakes (LT_F6), AirRob (LT_F4), ItemShop (LT_ORG-7656), Savannah (LT_ORG-35184), Valley (LT_ORG-8125), Indiana (LT_ORG-8127), Texas (LT_ORG-8126), <PERSON> (LT_ORG-7990), <PERSON> (LT_ORG-7972), <PERSON><PERSON> (LT_ORG-7955), <PERSON><PERSON> (LT_ORG-7954), <PERSON> (LT_ORG-7941), Quality_4400 (LT_ORG-77709), Ontario (LT_ORG-7455), Innovation (LT_ORG-67669), <PERSON><PERSON><PERSON> (LT_ORG-61213), <PERSON> (LT_ORG-50100), Red Bluff (LT_ORG-45230), Houston (LT_ORG-34647), New Jersey (LT_ORG-34646), Walnut (LT_ORG-2)";

// 创建 Memory 实例
function createMemory() {
  return new Memory({
    embedder: {
      provider: 'openai',
      config: {
        apiKey: process.env.OPENAI_API_KEY || '',
        model: 'text-embedding-3-small',
      },
    },
    vectorStore: {
      provider: 'memory', // 使用内存向量存储进行测试
      config: {
        collectionName: 'test_memories',
        dimension: 1536,
      },
    },
    llm: {
      provider: 'openai',
      config: {
        apiKey: process.env.OPENAI_API_KEY || '',
        model: 'gpt-4o-mini',
      },
    },
  });
}

// 测试对话格式添加
async function runTest() {
  console.log('开始 Mem0 API 测试...');
  console.log('OpenAI API Key 长度:', process.env.OPENAI_API_KEY?.length || 0);
  
  const memory = createMemory();
  
  try {
    // 添加对话格式内容
    console.log('\n添加对话格式内容');
    const dialogContent = [
      {
        role: "user",
        content: CONTENT
      }
    ];
    
    console.log('添加内容:', JSON.stringify(dialogContent, null, 2));
    
    const result = await memory.add(dialogContent, {
      userId: USER_ID,
      metadata: {
        source: 'test',
        timestamp: new Date().toISOString()
      },
      version: "v2" // 使用 v2 版本
    });
    
    console.log('添加结果:', JSON.stringify(result, null, 2));
    
    // 搜索记忆
    console.log('\n搜索记忆');
    const searchResult = await memory.search("仓库", {
      userId: USER_ID,
      limit: 10,
      minScore: 0.5 // 降低相似度阈值以获取更多结果
    });
    
    console.log('搜索结果:', JSON.stringify(searchResult, null, 2));
    
    console.log('\n测试完成!');
  } catch (error) {
    console.error('测试过程中发生错误:', error);
  }
}

// 运行测试
runTest().catch(console.error);
