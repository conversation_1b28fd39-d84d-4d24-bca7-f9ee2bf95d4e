#!/usr/bin/env node

/**
 * cursor-sql.js
 * 一个简单的CLI工具，用于通过MySQL MCP服务器执行SQL查询
 * 用法: 
 * 1. 直接传递查询: node cursor-sql.js "SELECT * FROM table_name"
 * 2. 通过文件传递查询: node cursor-sql.js --file query.sql
 */

// 使用child_process执行MCP命令
const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

let sqlQuery = '';

// 检查是否通过文件传递查询
if (process.argv.includes('--file') && process.argv.length > 3) {
  const fileIndex = process.argv.indexOf('--file');
  const filePath = process.argv[fileIndex + 1];
  
  if (!fs.existsSync(filePath)) {
    console.error(`SQL文件不存在: ${filePath}`);
    process.exit(1);
  }
  
  // 从文件读取SQL查询
  sqlQuery = fs.readFileSync(filePath, 'utf8');
} else if (process.argv.length > 2) {
  // 将所有其他参数合并为SQL查询
  sqlQuery = process.argv.slice(2).join(' ');
}

if (!sqlQuery || sqlQuery.trim() === '') {
  console.error('请提供SQL查询. 例如: node cursor-sql.js "SELECT * FROM table_name"');
  console.error('或者通过文件: node cursor-sql.js --file query.sql');
  process.exit(1);
}

try {
  // 清理查询字符串中的多余空白
  sqlQuery = sqlQuery.trim();
  
  // 创建临时文件来存储SQL查询，以避免命令行参数长度限制和特殊字符问题
  const tempFile = path.join(__dirname, 'temp_query.json');
  const queryObj = { query: sqlQuery };
  fs.writeFileSync(tempFile, JSON.stringify(queryObj), 'utf8');
  
  // 构建MCP命令，使用mcp.json中配置的MySQL连接和临时文件
  const mcpCommand = `npx cursor mcp-call mysql "mcp_mysql_execute_sql" "${tempFile}"`;
  
  try {
    // 执行命令并获取输出
    const output = execSync(mcpCommand, { encoding: 'utf8' });
    
    // 解析输出为JSON
    try {
      const result = JSON.parse(output);
      console.log(JSON.stringify(result, null, 2));
    } catch (parseError) {
      // 如果无法解析为JSON，则输出原始结果
      console.log(output);
    }
  } finally {
    // 清理临时文件
    if (fs.existsSync(tempFile)) {
      fs.unlinkSync(tempFile);
    }
  }
} catch (error) {
  console.error('执行SQL查询时出错:');
  console.error(error.message);
  process.exit(1);
} 