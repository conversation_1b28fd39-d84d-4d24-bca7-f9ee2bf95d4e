'use client';

import React from 'react';
import { useArtifactsStore } from '@/stores/useArtifactsStore';

interface ArtifactToolCardProps {
  toolInvocation: any;
  title: string;
  icon: React.ReactNode;
  borderColor?: string;
  iconBgColor?: string;
  iconTextColor?: string;
}

export default function ArtifactToolCard({
  toolInvocation,
  title,
  icon,
  borderColor = 'border-item-gray-800',
  iconBgColor = 'bg-item-gray-800/20',
  iconTextColor = 'text-item-gray-400'
}: ArtifactToolCardProps) {
  const { activateArtifact } = useArtifactsStore();
  
  const result = toolInvocation.result;
  const hasArtifact = result?.artifact;
  
  const handleActivateArtifact = () => {
    console.log('ArtifactToolCard clicked', { hasArtifact, toolName: toolInvocation.toolName });
    if (hasArtifact) {
      const artifact = {
        ...result.artifact,
        messageId: toolInvocation.messageId,
        toolName: toolInvocation.toolName
      };
      console.log('Activating artifact:', artifact);
      activateArtifact(artifact);
    }
  };

  return (
    <div 
      className={`item-card bg-item-bg-card rounded-lg border ${borderColor} p-2 text-white shadow-lg my-2 transition-all duration-200 ${
        hasArtifact ? 'cursor-pointer hover:border-item-gray-700 hover:shadow-xl' : ''
      }`}
      onClick={hasArtifact ? handleActivateArtifact : undefined}
    >
      <div className="flex justify-between items-center">
        <div className="flex items-center flex-1">
          <div className={`${iconBgColor} p-1.5 rounded-lg mr-2.5`}>
            <div className={`w-4 h-4 ${iconTextColor}`}>
              {icon}
            </div>
          </div>
          <span className="text-sm text-item-gray-500 font-medium leading-snug">
            {title}
          </span>
        </div>
        
        {hasArtifact && (
          <div className="flex items-center">
            <div className="bg-item-purple/20 p-1.5 rounded-lg border border-item-purple/30">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-4 h-4 text-item-purple">
                <path strokeLinecap="round" strokeLinejoin="round" d="M3.75 6A2.25 2.25 0 016 3.75h2.25A2.25 2.25 0 0110.5 6v2.25a2.25 2.25 0 01-2.25 2.25H6a2.25 2.25 0 01-2.25-2.25V6zM3.75 15.75A2.25 2.25 0 016 13.5h2.25a2.25 2.25 0 012.25 2.25V18a2.25 2.25 0 01-2.25 2.25H6A2.25 2.25 0 013.75 18v-2.25zM13.5 6a2.25 2.25 0 012.25-2.25H18A2.25 2.25 0 0120.25 6v2.25A2.25 2.25 0 0118 10.5h-2.25a2.25 2.25 0 01-2.25-2.25V6zM13.5 15.75a2.25 2.25 0 012.25-2.25H18a2.25 2.25 0 012.25 2.25V18A2.25 2.25 0 0118 20.25h-2.25A2.25 2.25 0 0113.5 18v-2.25z" />
              </svg>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}